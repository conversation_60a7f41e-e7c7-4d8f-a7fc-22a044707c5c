package com.moego.server.grooming.service.ob;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

class OBGroomingServiceTest {

    @Test
    void testCanAutoAssign_AllConditionsMet_ReturnsTrue() {
        // 准备测试数据 - 所有条件都满足
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null, // 没有staffId
                true, // noStartTime = true
                createPetDataWithoutStaff() // petData 中没有 staffId
                );

        // 执行测试 - 调用静态方法
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testCanAutoAssign_NoAppointmentDate_ReturnsFalse() {
        // 准备测试数据 - 没有预约日期
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                null, // 没有日期
                null, // 没有staffId
                true, // noStartTime = true
                createPetDataWithoutStaff());

        // 执行测试
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testCanAutoAssign_EmptyAppointmentDate_ReturnsFalse() {
        // 准备测试数据 - 空字符串日期
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "", // 空字符串日期
                null, // 没有staffId
                true, // noStartTime = true
                createPetDataWithoutStaff());

        // 执行测试
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testCanAutoAssign_BlankAppointmentDate_ReturnsFalse() {
        // 准备测试数据 - 空白字符串日期
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "   ", // 空白字符串日期
                null, // 没有staffId
                true, // noStartTime = true
                createPetDataWithoutStaff());

        // 执行测试
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testCanAutoAssign_HasStaffIdAndHasStartTime_ReturnsFalse() {
        // 准备测试数据 - 有staffId且有开始时间
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                123, // 有staffId
                false, // noStartTime = false (有开始时间)
                createPetDataWithoutStaff());

        // 执行测试
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testCanAutoAssign_HasStaffIdButNoStartTime_ReturnsTrue() {
        // 准备测试数据 - 有staffId但没有开始时间
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                123, // 有staffId
                true, // noStartTime = true (没有开始时间)
                createPetDataWithoutStaff());

        // 执行测试
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testCanAutoAssign_PetDataHasStaffAndHasStartTime_ReturnsFalse() {
        // 准备测试数据 - petData中有staffId且有开始时间
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null, // 没有主staffId
                false, // noStartTime = false (有开始时间)
                createPetDataWithStaff(456) // petData中有staffId
                );

        // 执行测试
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testCanAutoAssign_PetDataHasStaffButNoStartTime_ReturnsTrue() {
        // 准备测试数据 - petData中有staffId但没有开始时间
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null, // 没有主staffId
                true, // noStartTime = true (没有开始时间)
                createPetDataWithStaff(456) // petData中有staffId
                );

        // 执行测试
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testCanAutoAssign_NotOB3Version_ReturnsFalse() {
        // 准备测试数据 - 不是OB 3.0版本
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_2_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null, // 没有staffId
                true, // noStartTime = true
                createPetDataWithoutStaff());

        // 执行测试
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testCanAutoAssign_NullUseVersion_ReturnsFalse() {
        // 准备测试数据 - useVersion为null
        MoeBusinessBookOnline bookOnline = createBookOnline(null);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null, // 没有staffId
                true, // noStartTime = true
                createPetDataWithoutStaff());

        // 执行测试
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testCanAutoAssign_HasNoStaffCondition_BothStaffIdAndPetDataEmpty_ReturnsTrue() {
        // 准备测试数据 - hasNoStaff为true的情况：obParams.getStaffId() == null && staffIdList.isEmpty()
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null, // staffId为null
                false, // noStartTime = false (有开始时间)
                createPetDataWithoutStaff() // petData中没有staffId，所以staffIdList.isEmpty()为true
                );

        // 执行测试 - hasNoStaff为true，所以 (hasNoStaff || obParams.isNoStartTime()) 为true
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testCanAutoAssign_HasNoStaffCondition_StaffIdNotNullButPetDataEmpty_ReturnsFalse() {
        // 准备测试数据 - hasNoStaff为false的情况：obParams.getStaffId() != null && staffIdList.isEmpty()
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                123, // staffId不为null
                false, // noStartTime = false (有开始时间)
                createPetDataWithoutStaff() // petData中没有staffId，但主staffId不为null，所以hasNoStaff为false
                );

        // 执行测试 - hasNoStaff为false，noStartTime为false，所以 (hasNoStaff || obParams.isNoStartTime()) 为false
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testCanAutoAssign_MultiplePetsWithMixedStaff_ReturnsTrue() {
        // 准备测试数据 - 多个宠物，部分有staffId，部分没有
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);

        List<BookOnlinePetParams> petData = new ArrayList<>();
        petData.add(createPetWithStaff(123)); // 有staffId
        petData.add(createPetWithoutStaff()); // 没有staffId
        petData.add(createPetWithStaff(456)); // 有staffId

        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null, // 没有主staffId
                true, // noStartTime = true
                petData);

        // 执行测试 - 因为petData中有staffId，所以hasNoStaff为false，但noStartTime为true，所以返回true
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testCanAutoAssign_EmptyPetData_ReturnsTrue() {
        // 准备测试数据 - 空的petData列表
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null, // 没有staffId
                false, // noStartTime = false (有开始时间)
                new ArrayList<>() // 空的petData，staffIdList.isEmpty()为true
                );

        // 执行测试 - hasNoStaff为true（因为staffId为null且staffIdList为空），所以返回true
        boolean result = OBGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    // ========== 辅助方法 ==========

    /**
     * 创建 MoeBusinessBookOnline 测试对象
     */
    private MoeBusinessBookOnline createBookOnline(Byte useVersion) {
        MoeBusinessBookOnline bookOnline = new MoeBusinessBookOnline();
        bookOnline.setUseVersion(useVersion);
        return bookOnline;
    }

    /**
     * 创建 BookOnlineSubmitParams 测试对象
     */
    private BookOnlineSubmitParams createSubmitParams(
            String appointmentDate, Integer staffId, boolean noStartTime, List<BookOnlinePetParams> petData) {
        BookOnlineSubmitParams params = new BookOnlineSubmitParams();
        params.setAppointmentDate(appointmentDate);
        params.setStaffId(staffId);
        params.setNoStartTime(noStartTime);
        params.setPetData(petData);
        return params;
    }

    /**
     * 创建没有staffId的petData列表
     */
    private List<BookOnlinePetParams> createPetDataWithoutStaff() {
        List<BookOnlinePetParams> petData = new ArrayList<>();
        petData.add(createPetWithoutStaff());
        return petData;
    }

    /**
     * 创建有staffId的petData列表
     */
    private List<BookOnlinePetParams> createPetDataWithStaff(Integer staffId) {
        List<BookOnlinePetParams> petData = new ArrayList<>();
        petData.add(createPetWithStaff(staffId));
        return petData;
    }

    /**
     * 创建没有staffId的宠物参数
     */
    private BookOnlinePetParams createPetWithoutStaff() {
        BookOnlinePetParams pet = new BookOnlinePetParams();
        pet.setPetName("TestPet");
        pet.setBreed("TestBreed");
        pet.setStaffId(null); // 没有staffId
        return pet;
    }

    /**
     * 创建有staffId的宠物参数
     */
    private BookOnlinePetParams createPetWithStaff(Integer staffId) {
        BookOnlinePetParams pet = new BookOnlinePetParams();
        pet.setPetName("TestPet");
        pet.setBreed("TestBreed");
        pet.setStaffId(staffId); // 有staffId
        return pet;
    }
}
