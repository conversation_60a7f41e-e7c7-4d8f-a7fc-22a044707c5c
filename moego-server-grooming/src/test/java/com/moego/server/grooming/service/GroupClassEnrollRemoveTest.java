package com.moego.server.grooming.service;

import com.moego.server.grooming.dto.GroupClassEnrollResultDTO;
import com.moego.server.grooming.dto.GroupClassRemoveResultDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.ServiceItemEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.params.GroupClassEnrollParams;
import com.moego.server.grooming.params.GroupClassRemoveParams;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * Test class for Group Class Enroll/Remove functionality
 * 团体课程报名/移除功能测试
 */
@ExtendWith(MockitoExtension.class)
class GroupClassEnrollRemoveTest {

    @Mock
    private MoeGroomingAppointmentService appointmentService;

    private GroupClassEnrollParams enrollParams;
    private GroupClassRemoveParams removeParams;
    private MoeGroomingAppointment mockAppointment;

    @BeforeEach
    void setUp() {
        // 设置报名参数
        enrollParams = new GroupClassEnrollParams();
        enrollParams.setAppointmentId(1001);
        enrollParams.setCustomerId(2001);
        enrollParams.setPetIds(Arrays.asList(3001, 3002));
        enrollParams.setBusinessId(4001);
        enrollParams.setCompanyId(5001L);
        enrollParams.setStaffId(6001);
        enrollParams.setPaymentAmount(new BigDecimal("100.00"));
        enrollParams.setPaymentMethod("CREDIT_CARD");
        enrollParams.setNotes("Test enrollment");

        // 设置移除参数
        removeParams = new GroupClassRemoveParams();
        removeParams.setAppointmentId(1001);
        removeParams.setCustomerId(2001);
        removeParams.setPetIds(Arrays.asList(3001));
        removeParams.setBusinessId(4001);
        removeParams.setCompanyId(5001L);
        removeParams.setStaffId(6001);
        removeParams.setRefundReason("Customer request");
        removeParams.setNotes("Test removal");

        // 设置模拟预约
        mockAppointment = new MoeGroomingAppointment();
        mockAppointment.setId(1001);
        mockAppointment.setStatus(AppointmentStatusEnum.CONFIRMED.getValue());
        mockAppointment.setServiceTypeInclude(ServiceItemEnum.GROUP_CLASS.getValue());
    }

    @Test
    void testEnrollInGroupClass_Success() {
        // Given
        GroupClassEnrollResultDTO expectedResult = new GroupClassEnrollResultDTO();
        expectedResult.setSuccess(true);
        expectedResult.setAppointmentId(1001);
        expectedResult.setCustomerId(2001);
        expectedResult.setEnrolledPetIds(Arrays.asList(3001, 3002));
        expectedResult.setChargedAmount(new BigDecimal("100.00"));
        expectedResult.setPaymentStatus("SUCCESS");

        when(appointmentService.enrollInGroupClass(any(GroupClassEnrollParams.class)))
                .thenReturn(expectedResult);

        // When
        GroupClassEnrollResultDTO result = appointmentService.enrollInGroupClass(enrollParams);

        // Then
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(1001, result.getAppointmentId());
        assertEquals(2001, result.getCustomerId());
        assertEquals(2, result.getEnrolledPetIds().size());
        assertEquals(new BigDecimal("100.00"), result.getChargedAmount());
        assertEquals("SUCCESS", result.getPaymentStatus());
    }

    @Test
    void testRemoveFromGroupClass_Success() {
        // Given
        GroupClassRemoveResultDTO expectedResult = new GroupClassRemoveResultDTO();
        expectedResult.setSuccess(true);
        expectedResult.setAppointmentId(1001);
        expectedResult.setCustomerId(2001);
        expectedResult.setRemovedPetIds(Arrays.asList(3001));
        expectedResult.setRefundAmount(new BigDecimal("50.00"));
        expectedResult.setRefundStatus("SUCCESS");

        when(appointmentService.removeFromGroupClass(any(GroupClassRemoveParams.class)))
                .thenReturn(expectedResult);

        // When
        GroupClassRemoveResultDTO result = appointmentService.removeFromGroupClass(removeParams);

        // Then
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(1001, result.getAppointmentId());
        assertEquals(2001, result.getCustomerId());
        assertEquals(1, result.getRemovedPetIds().size());
        assertEquals(new BigDecimal("50.00"), result.getRefundAmount());
        assertEquals("SUCCESS", result.getRefundStatus());
    }

    @Test
    void testEnrollParams_Validation() {
        // Test required fields
        assertNotNull(enrollParams.getAppointmentId());
        assertNotNull(enrollParams.getCustomerId());
        assertNotNull(enrollParams.getPetIds());
        assertFalse(enrollParams.getPetIds().isEmpty());
    }

    @Test
    void testRemoveParams_Validation() {
        // Test required fields
        assertNotNull(removeParams.getAppointmentId());
        assertNotNull(removeParams.getCustomerId());
        assertNotNull(removeParams.getPetIds());
        assertFalse(removeParams.getPetIds().isEmpty());
    }
}
