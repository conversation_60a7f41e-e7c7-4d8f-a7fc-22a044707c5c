package com.moego.server.grooming.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.moego.server.grooming.dto.GroupClassEnrollResultDTO;
import com.moego.server.grooming.dto.GroupClassRemoveResultDTO;
import com.moego.server.grooming.params.GroupClassEnrollParams;
import com.moego.server.grooming.params.GroupClassRemoveParams;
import java.math.BigDecimal;
import java.util.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Test class for Group Class Enroll/Remove functionality
 * 团体课程报名/移除功能测试
 */
class GroupClassEnrollRemoveTest {

    private GroupClassEnrollParams enrollParams;
    private GroupClassRemoveParams removeParams;

    @BeforeEach
    void setUp() {
        // 设置报名参数
        enrollParams = new GroupClassEnrollParams();
        enrollParams.setAppointmentId(1001);
        enrollParams.setCustomerId(2001);
        enrollParams.setPetIds(Arrays.asList(3001, 3002));
        enrollParams.setBusinessId(4001);
        enrollParams.setCompanyId(5001L);
        enrollParams.setStaffId(6001);
        enrollParams.setPaymentAmount(new BigDecimal("100.00"));
        enrollParams.setPaymentMethod("CREDIT_CARD");
        enrollParams.setNotes("Test enrollment");

        // 设置移除参数
        removeParams = new GroupClassRemoveParams();
        removeParams.setAppointmentId(1001);
        removeParams.setCustomerId(2001);
        removeParams.setPetIds(Arrays.asList(3001));
        removeParams.setBusinessId(4001);
        removeParams.setCompanyId(5001L);
        removeParams.setStaffId(6001);
        removeParams.setRefundReason("Customer request");
        removeParams.setNotes("Test removal");
    }

    @Test
    void testEnrollParams_Validation() {
        // Test required fields
        assertNotNull(enrollParams.getAppointmentId());
        assertNotNull(enrollParams.getCustomerId());
        assertNotNull(enrollParams.getPetIds());
        assertFalse(enrollParams.getPetIds().isEmpty());
        assertEquals(2, enrollParams.getPetIds().size());
        assertEquals(new BigDecimal("100.00"), enrollParams.getPaymentAmount());
    }

    @Test
    void testRemoveParams_Validation() {
        // Test required fields
        assertNotNull(removeParams.getAppointmentId());
        assertNotNull(removeParams.getCustomerId());
        assertNotNull(removeParams.getPetIds());
        assertFalse(removeParams.getPetIds().isEmpty());
        assertEquals(1, removeParams.getPetIds().size());
        assertEquals("Customer request", removeParams.getRefundReason());
    }

    @Test
    void testResultDTOs_Creation() {
        // Test GroupClassEnrollResultDTO
        GroupClassEnrollResultDTO enrollResult = new GroupClassEnrollResultDTO();
        enrollResult.setSuccess(true);
        enrollResult.setAppointmentId(1001);
        enrollResult.setCustomerId(2001);
        enrollResult.setEnrolledPetIds(Arrays.asList(3001, 3002));
        enrollResult.setChargedAmount(new BigDecimal("100.00"));
        enrollResult.setPaymentStatus("SUCCESS");

        assertTrue(enrollResult.getSuccess());
        assertEquals(1001, enrollResult.getAppointmentId());
        assertEquals(2, enrollResult.getEnrolledPetIds().size());

        // Test GroupClassRemoveResultDTO
        GroupClassRemoveResultDTO removeResult = new GroupClassRemoveResultDTO();
        removeResult.setSuccess(true);
        removeResult.setAppointmentId(1001);
        removeResult.setCustomerId(2001);
        removeResult.setRemovedPetIds(Arrays.asList(3001));
        removeResult.setRefundAmount(new BigDecimal("50.00"));
        removeResult.setRefundStatus("SUCCESS");

        assertTrue(removeResult.getSuccess());
        assertEquals(1001, removeResult.getAppointmentId());
        assertEquals(1, removeResult.getRemovedPetIds().size());
    }
}
