DROP table if exists `moe_grooming_invoice`;
CREATE TABLE `moe_grooming_invoice` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_id` int(11) NOT NULL COMMENT 'business id',
  `grooming_id` int(11) NOT NULL COMMENT '预约id',
  `customer_id` int(11) NOT NULL COMMENT 'customer id',
  `type` varchar(16) NOT NULL DEFAULT '' COMMENT 'type(appointment or noshow)',
  `sub_total_amount` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '服务总价',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.0000' COMMENT '折扣金额',
  `discount_rate` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '折扣百分比',
  `discount_type` varchar(16) NOT NULL DEFAULT '' COMMENT 'type(amount or percentage)',
  `discounted_sub_total_amount` decimal(10,4) NOT NULL DEFAULT '0' COMMENT 'sub_total_amount - discount_amount',
  `tips_amount` decimal(10,2) NOT NULL DEFAULT '0.0000' COMMENT '小费金额',
  `tips_rate` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '小费百分比',
  `tips_type` varchar(16) NOT NULL DEFAULT '' COMMENT 'type(amount or percentage)',
  `tax_amount` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT 'tax总价',
  `total_amount` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '总金额',
  `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实付金额',
  `paid_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'paid amount',
  `remain_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'remain amount',
  `refunded_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'refunded amount',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT 'invoice status(0:created, 1:processing, 2:completed)',
  `create_by` int(11) NOT NULL DEFAULT '0' COMMENT '创建员工',
  `update_by` int(11) NOT NULL DEFAULT '0' COMMENT '修改员工',
  `create_time` bigint(20) NOT NULL COMMENT '创建时间',
  `update_time` bigint(20) NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `UIX_invoice_grooming_id_type` (`grooming_id`, `type`),
  KEY `IDX_invoice_business_id` (`business_id`),
  KEY `IDX_invoice_customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP table if exists `moe_grooming_invoice_item`;
CREATE TABLE `moe_grooming_invoice_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `invoice_id` int(11) NOT NULL COMMENT 'invoice主键id',
  `service_id` int(11) NOT NULL COMMENT '宠物服务id',
  `pet_detail_id` int(11) NOT NULL COMMENT 'pet detail id',
  `service_name` varchar(50) COMMENT '服务名称',
  `service_description` varchar(500) COMMENT '服务说明',
  `service_unit_price` decimal(10,4) NOT NULL COMMENT '服务价格',
  `tax_id` int(11) NOT NULL DEFAULT '0' COMMENT '税率id',
  `tax_rate` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '税率',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `purchased_quantity` int(11) NOT NULL DEFAULT '0' COMMENT 'purchased quantity',
  `total_list_price` decimal(10,4) NOT NULL DEFAULT '0' COMMENT 'total list price',
  `total_sale_price` decimal(10,4) NOT NULL DEFAULT '0' COMMENT 'total sale price',
  `discount_amount` decimal(10,4) NOT NULL DEFAULT '0' COMMENT '折扣金额',
  `tax_amount` decimal(10,4) NOT NULL DEFAULT '0' COMMENT '剩余服务税额',
  `create_time` bigint(20) NOT NULL COMMENT '创建时间',
  `update_time` bigint(20) NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `IDX_invoice_item_invoice_id` (`invoice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP table if exists `moe_grooming_invoice_apply_package`;
CREATE TABLE `moe_grooming_invoice_apply_package` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `invoice_id` int(11) NOT NULL COMMENT 'invoice主键id',
  `invoice_item_id` int(11) NOT NULL COMMENT 'invoice item id',
  `package_id` int(11) NOT NULL COMMENT 'package id',
  `service_id` int(11) NOT NULL COMMENT 'service id',
  `package_service_id` int(11) NOT NULL COMMENT 'package service id',
  `package_name` varchar(50) COMMENT 'package name',
  `service_name` varchar(50) COMMENT 'service name',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `create_time` bigint(20) NOT NULL COMMENT '创建时间',
  `update_time` bigint(20) NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `IDX_invoice_item_invoice_id` (`invoice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP table if exists `moe_grooming_package_history`;
CREATE TABLE `moe_grooming_package_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `invoice_id` int(11) NOT NULL COMMENT 'invoice主键id',
  `package_id` int(11) DEFAULT NULL COMMENT 'packageid',
  `service_id` int(11) DEFAULT NULL COMMENT '服务id',
  `quantity` int(11) DEFAULT NULL COMMENT '使用数量',
  `appointment_date` date DEFAULT NULL COMMENT '预约日期',
  `use_time` bigint(20) DEFAULT '0' COMMENT '使用时间',
  `grooming_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_invoice_item_invoice_id` (`invoice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
