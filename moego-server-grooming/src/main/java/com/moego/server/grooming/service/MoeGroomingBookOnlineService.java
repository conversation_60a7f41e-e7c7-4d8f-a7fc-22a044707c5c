package com.moego.server.grooming.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.idl.models.agreement.v1.ServiceType.SERVICE_TYPE_GROOMING_VALUE;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_ONLINE_BOOKING_REQUEST_ACCEPTED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_ONLINE_BOOKING_REQUEST_AUTO_MOVED_TO_WAITLIST;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_ONLINE_BOOKING_REQUEST_DECLINED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_ONLINE_BOOKING_REQUEST_SUBMITTED;
import static com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc.BookingRequestServiceBlockingStub;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.server.grooming.service.DepositService.DEPOSIT_PREFIX;

import com.moego.common.constant.CommonConstant;
import com.moego.common.constant.Dictionary;
import com.moego.common.distributed.LockManager;
import com.moego.common.dto.SortDto;
import com.moego.common.dto.TimePeriodDto;
import com.moego.common.dto.notificationDto.NotificationExtraAgreementSignedDto;
import com.moego.common.enums.AgreementEnum;
import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.enums.OnlineBookingNotificationConst;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentSettingConst;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.enums.PropertyEnum;
import com.moego.common.enums.QuestionConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.StaffEnum;
import com.moego.common.enums.SubscriptionConst;
import com.moego.common.enums.filter.OperatorEnum;
import com.moego.common.enums.filter.TypeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.params.FilterParams;
import com.moego.common.params.SortIdListParams;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.common.utils.SortUtils;
import com.moego.common.utils.WeekUtil;
import com.moego.idl.models.agreement.v1.AgreementModel;
import com.moego.idl.models.agreement.v1.AgreementRecordSimpleView;
import com.moego.idl.models.agreement.v1.ServiceType;
import com.moego.idl.models.agreement.v1.SourceType;
import com.moego.idl.models.auto_message.v1.AppointmentAutoMsgConfigModel;
import com.moego.idl.models.auto_message.v1.ServiceTypeConfigDef;
import com.moego.idl.models.auto_message.v1.ServiceTypeConfigDefList;
import com.moego.idl.models.auto_message.v1.ServiceTypeConfigModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.message.v1.BatchCreateTemplateItemDef;
import com.moego.idl.models.message.v1.MessageTemplateUseCase;
import com.moego.idl.models.message.v1.MessageTemplateUseCaseEnumList;
import com.moego.idl.models.message.v1.TemplateModel;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceFilterByPet;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.AcceptClientType;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.PaymentType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.agreement.v1.AgreementRecordServiceGrpc.AgreementRecordServiceBlockingStub;
import com.moego.idl.service.agreement.v1.AgreementServiceGrpc;
import com.moego.idl.service.agreement.v1.ListUnsignedAgreementRequest;
import com.moego.idl.service.agreement.v1.SignAgreementRequest;
import com.moego.idl.service.auto_message.v1.AutoMessageConfigServiceGrpc;
import com.moego.idl.service.auto_message.v1.GetAppointmentAutoMsgConfigListRequest;
import com.moego.idl.service.auto_message.v1.UpdateAppointmentAutoMsgConfigRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeValidForCustomerInput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeValidForCustomerOutput;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.service.message.v1.BatchCreateTemplatesRequest;
import com.moego.idl.service.message.v1.MGetTemplatesRequest;
import com.moego.idl.service.message.v1.TemplateServiceGrpc;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.GetBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.GetGroomingServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityStatusRequest;
import com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.service.order.v2.DepositRuleServiceGrpc;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessClosedDateClient;
import com.moego.server.business.client.IBusinessPaymentMethodClient;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.MoeWorkingDailyDTO;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.StaffTimeslotPetCountDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerFilterResult;
import com.moego.server.customer.params.AddCustomerAgreementParams;
import com.moego.server.customer.params.ClientListParams;
import com.moego.server.customer.params.ClientListRequest;
import com.moego.server.grooming.api.IGroomingAppointmentService;
import com.moego.server.grooming.config.S3Client;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.ApplyServiceChargeDTO;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineQuestionSaveDto;
import com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.MoeBookOnlineNotificationDto;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.PreAuthAmountDTO;
import com.moego.server.grooming.dto.PrepayAmountDTO;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import com.moego.server.grooming.dto.StaffBlockInfoDTO;
import com.moego.server.grooming.dto.ob.BookOnlineConfigDTO;
import com.moego.server.grooming.dto.ob.BookOnlineGalleryDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentGroupSettingDTO;
import com.moego.server.grooming.dto.ob.BookOnlineProfileDTO;
import com.moego.server.grooming.dto.ob.CalculateServiceAmountDTO;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.helper.MetadataHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeBookOnlineGalleryMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineNotificationMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineProfileMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineQuestionMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineQuestionSaveMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineStaffTimeMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeBookOnlineGallery;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.MoeBookOnlineNotification;
import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionSave;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffTime;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnlineExample;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapstruct.BookOnlineQuestionConverter;
import com.moego.server.grooming.mapstruct.OBSettingMapper;
import com.moego.server.grooming.mapstruct.ObPaymentSettingMapper;
import com.moego.server.grooming.params.BookOnlineAgreementParams;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineQuestionListParams;
import com.moego.server.grooming.params.BookOnlineQuestionParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.params.BusinessBookOnlineParams;
import com.moego.server.grooming.params.CustomerIdWithPetIdsParams;
import com.moego.server.grooming.params.MoeBookOnlineGalleryBatchParams;
import com.moego.server.grooming.params.MoeBookOnlineGalleryParams;
import com.moego.server.grooming.params.MoeBookOnlineProfileParams;
import com.moego.server.grooming.params.MoeBookOnlineStaffTimeParams;
import com.moego.server.grooming.params.MoeBusinessBookOnlineDto;
import com.moego.server.grooming.params.PreAuthAmountParams;
import com.moego.server.grooming.params.PrepayAmountParams;
import com.moego.server.grooming.params.UpdateStaffTimeParam;
import com.moego.server.grooming.params.ob.BookOnlinePaymentGroupParams;
import com.moego.server.grooming.params.ob.BusinessBookOnlinePaymentParams;
import com.moego.server.grooming.params.ob.DiscountCodeParams;
import com.moego.server.grooming.params.ob.MobileGroomingParams;
import com.moego.server.grooming.params.ob.PreAuthDetailParams;
import com.moego.server.grooming.params.ob.ServiceParams;
import com.moego.server.grooming.service.dto.CapacityTimeslotDTO;
import com.moego.server.grooming.service.dto.OBAvailableTimeStaffAvailableTimeDto;
import com.moego.server.grooming.service.dto.OneDayTimeslotsDTO;
import com.moego.server.grooming.service.ob.OBBusinessProfileService;
import com.moego.server.grooming.service.ob.OBBusinessStaffService;
import com.moego.server.grooming.service.ob.OBClientService;
import com.moego.server.grooming.service.ob.OBClientTimeSlotService;
import com.moego.server.grooming.service.ob.OBCustomerService;
import com.moego.server.grooming.service.ob.OBGroomingService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.service.ob.OBLandingPageGalleryService;
import com.moego.server.grooming.service.ob.OBServiceService;
import com.moego.server.grooming.service.remote.SmartSchedulerService;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.service.utils.ServiceUtil;
import com.moego.server.grooming.web.dto.ob.BusinessInfoDto;
import com.moego.server.grooming.web.dto.ob.InfoDto;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.params.notification.NotificationAgreementSignedParams;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentRefundClient;
import com.moego.server.payment.client.IPaymentSettingClient;
import com.moego.server.payment.dto.PaymentSettingDTO;
import com.moego.server.payment.dto.PaymentSettingForClientDTO;
import com.moego.server.payment.dto.SmartTipConfigDTO;
import com.moego.server.payment.dto.SmartTipConfigForClientDTO;
import com.moego.server.payment.params.CreateRefundByPaymentIdParams;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.IdentityHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Builder;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020-11-13
 */
@Service
@Slf4j
public class MoeGroomingBookOnlineService {

    public static final String AM = "am";
    public static final String PM = "pm";
    private static final int AM_PM_PIVOT = 12 * 60;
    public static final String DEFAULT_STAFF_TIME;
    private static final Integer DEFAULT_START_TIME = 9 * 60;
    private static final Integer DEFAULT_STAFF_CAPACITY = 10;

    private static final String REFERRAL_SOURCE = "Referral source";
    private static final String PREFERRED_GROOMER = "Preferred groomer";
    public static final String EMPTY_JSON = "{}";

    static {
        Map<String, StaffTime> defaultTime = new HashMap<>();
        // use default constructor
        StaffTime staffTime = new StaffTime();
        staffTime.getTimeRange().add(new TimeRangeDto(540, 18 * 60));
        defaultTime.put(WeekUtil.KEY_OF_MONDAY, staffTime);
        defaultTime.put(WeekUtil.KEY_OF_TUESDAY, staffTime);
        defaultTime.put(WeekUtil.KEY_OF_WEDNESDAY, staffTime);
        defaultTime.put(WeekUtil.KEY_OF_THURSDAY, staffTime);
        defaultTime.put(WeekUtil.KEY_OF_FRIDAY, staffTime);
        defaultTime.put(WeekUtil.KEY_OF_SATURDAY, staffTime);
        defaultTime.put(WeekUtil.KEY_OF_SUNDAY, staffTime);
        DEFAULT_STAFF_TIME = JsonUtil.toJson(defaultTime);
    }

    @Autowired
    private MoePetDetailService moePetDetailService;

    @Autowired
    private MoeBookOnlineGalleryMapper moeBookOnlineGalleryMapper;

    @Autowired
    private MoeBookOnlineProfileMapper moeBookOnlineProfileMapper;

    @Setter
    @Autowired
    private MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;

    @Autowired
    private MoeBookOnlineStaffTimeMapper moeBookOnlineStaffTimeMapper;

    @Autowired
    private StaffTimeSyncService staffTimeSyncService;

    @Autowired
    private StaffAvailableSyncService staffAvailableSyncService;

    @Autowired
    private MoeBookOnlineNotificationMapper moeBookOnlineNotificationMapper;

    @Setter
    @Autowired
    private GroomingFeaturePricingCheckService pricingCheckService;

    @Autowired
    private MoeBookOnlineQuestionSaveMapper moeBookOnlineQuestionSaveMapper;

    @Autowired
    private MoeBookOnlineQuestionMapper moeBookOnlineQuestionMapper;

    @Autowired
    private OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;

    @Autowired
    private MoeGroomingQuestionService moeGroomingQuestionService;

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private INotificationClient iNotificationClient;

    @Autowired
    private IPaymentCreditCardClient iPaymentService;

    @Autowired
    private IPaymentPaymentClient iPaymentPaymentClient;

    @Autowired
    private IPaymentSettingClient iPaymentSettingClient;

    @Autowired
    private IPaymentRefundClient iPaymentRefundClient;

    @Autowired
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Setter
    @Autowired
    private SmartScheduleService smartScheduleService;

    @Autowired
    private OnlineBookingService onlineBookingService;

    @Autowired
    private MoeBookOnlineDepositService moeBookOnlineDepositService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Autowired
    private IBusinessClosedDateClient iBusinessClosedDateClient;

    @Autowired
    private IBusinessPaymentMethodClient iBusinessPaymentMethodClient;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private IBusinessServiceAreaClient iBusinessServiceAreaClient;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private AgreementServiceGrpc.AgreementServiceBlockingStub agreementClient;

    @Autowired
    private AgreementRecordServiceBlockingStub agreementRecordClient;

    @Autowired
    private S3Client s3Client;

    @Value("${s3.public.bucket}")
    private String s3PublicBucket;

    @Value("${s3.public.domain}")
    private String s3PublicDomain;

    @Value("${s3.public.prefix.signature}")
    private String s3PublicSignaturePrefix;

    @Autowired
    private OBBusinessStaffService businessStaffService;

    @Autowired
    private OBLandingPageConfigService landingPageConfigService;

    @Autowired
    private OBLandingPageGalleryService landingPageGalleryService;

    @Autowired
    private OBBusinessProfileService businessProfileService;

    @Resource
    private GroomingServiceOperationService groomingServiceOperationService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ICustomerCustomerClient customerClient;

    // svc-business-customer 服务
    @Autowired
    private BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerClient;

    @Autowired
    private OBCustomerService customerService;

    @Resource
    private DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub discountCodeClient;

    @Autowired
    private OBGroomingService obGroomingService;

    @Autowired
    private BookOnlineAcceptPetTypeService bookOnlineAcceptPetTypeService;

    @Autowired
    private OBClientTimeSlotService obClientTimeSlotService;

    @Autowired
    private IGroomingAppointmentService appointmentApi;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private AppointmentMapperProxy appointmentMapper;

    @Autowired
    private BookingRequestServiceBlockingStub bookingRequestStub;

    @Autowired
    private AutoMessageConfigServiceGrpc.AutoMessageConfigServiceBlockingStub autoMessageConfigClient;

    @Autowired
    private TemplateServiceGrpc.TemplateServiceBlockingStub templateClient;

    @Autowired
    private OBServiceService obServiceService;

    @Autowired
    private PermissionHelper permissionHelper;

    @Autowired
    private OBClientService clientService;

    @Autowired
    private OBAvailabilitySettingServiceGrpc.OBAvailabilitySettingServiceBlockingStub
            obAvailabilitySettingServiceBlockingStub;

    @Autowired
    private SmartSchedulerService smartSchedulerService;

    @Autowired
    private MetadataHelper metadataHelper;

    @Autowired
    private CompanyHelper companyHelper;

    @Autowired
    private FeatureFlagApi featureFlagApi;

    @Autowired
    private ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;

    @Autowired
    private DepositRuleServiceGrpc.DepositRuleServiceBlockingStub depositRuleClient;

    /**
     * only call this method once for each business id
     * <precondition> : table MoeBusinessBookOnline does not have record where
     * businessId = tokenBusinessid
     * 1、配置查询接口
     * a) 初始化moe_business_book_online
     * $obSetting = M("business_book_online")->where(['business_id' =>
     * $business_id])->find();
     * if(empty($obSetting)){
     * $saveData['business_id'] = $business_id;
     * $saveData['setting_location'] = $business['address'];
     * $saveData['setting_lat'] = $business['lat'];
     * $saveData['setting_lng'] = $business['lng'];
     * $saveData['create_time'] = time();
     * M("business_book_online")->add($saveData);
     * }
     * b) 初始化staff time
     * /Application/BusinessApi/Service/BookOnlineService.class.php
     * c) 初始化book online question
     * /Application/BusinessApi/Service/BookOnlineService.class.php
     * d) 初始化moe_book_online_profile
     * /Application/BusinessApi/Service/BookOnlineService.class.php
     * e) 初始化moe_book_online_notification (edited)
     * f) 初始化 accept pet type
     *
     * @param tokenBusinessId
     * @return
     */
    // @Transactional
    public MoeBusinessBookOnline initBookOnlineInfo(Integer tokenBusinessId, Long tokenCompanyId) {
        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(tokenBusinessId);
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfoWithOwnerEmail(infoIdParams);
        MoeBusinessBookOnline obInfo = new MoeBusinessBookOnline();
        obInfo.setBusinessId(tokenBusinessId);
        obInfo.setCompanyId(tokenCompanyId);
        obInfo.setCreateTime(DateUtil.get10Timestamp());
        obInfo.setUpdateTime(obInfo.getCreateTime());
        obInfo.setCancellationPolicy("");
        obInfo.setDescription("Welcome to ".concat(businessDto.getBusinessName()));
        // book online name init
        String bookOnlineName = generateBookOnlineName(businessDto);
        obInfo.setBookOnlineName(bookOnlineName);
        obInfo.setUseVersion(OnlineBookingConst.VERSION_TWO);
        moeBusinessBookOnlineMapper.insertSelective(obInfo);

        executeInitializationStep(
                "Staff availability",
                () -> obStaffAvailabilityServiceBlockingStub.getStaffAvailability(
                        GetStaffAvailabilityRequest.newBuilder()
                                .setCompanyId(tokenCompanyId)
                                .setBusinessId(tokenBusinessId)
                                .build()));

        executeInitializationStep(
                "Booking question",
                () -> moeGroomingQuestionService.initBookingQuestionForBusiness(tokenBusinessId, tokenCompanyId));

        executeInitializationStep(
                "Business profile", () -> initBusinessProfile(tokenBusinessId, tokenCompanyId, businessDto));

        executeInitializationStep(
                "Landing page config",
                () -> landingPageConfigService.initializeLandingPageConfig(businessDto, bookOnlineName));

        executeInitializationStep("Business notification", () -> initBusinessNotification(tokenBusinessId));

        executeInitializationStep(
                "Accept pet type",
                () -> bookOnlineAcceptPetTypeService.initAcceptPetTypeList(tokenCompanyId, tokenBusinessId));

        return obInfo;
    }

    private void executeInitializationStep(String stepName, Runnable initStep) {
        try {
            initStep.run();
        } catch (Exception e) {
            log.error("OB init [{}] failed", stepName, e);
        }
    }

    /**
     * bookOnlineName 生成规则
     * 1. 去除空格，下划线，特殊字符
     * 2. 如果为空，则使用 businessId
     * 3. 如果已存在，则在后面加上 businessId
     *
     * @param businessDto business info
     * @return bookOnlineName
     */
    public String generateBookOnlineName(MoeBusinessDto businessDto) {
        String bookOnlineName = businessDto.getBusinessName().replaceAll("[^\\w]|_", "");
        if (!StringUtils.hasText(bookOnlineName)) {
            bookOnlineName = businessDto.getId().toString();
        }
        BusinessCompanyPO businessCompanyPO = moeBusinessBookOnlineMapper.selectByBookOnlineName(bookOnlineName);
        if (Objects.nonNull(businessCompanyPO) && Objects.nonNull(businessCompanyPO.getBusinessId())) {
            bookOnlineName = bookOnlineName + businessDto.getId();
        }
        return bookOnlineName;
    }

    /**
     * 支持重复调用（重复初始化）
     *
     * @param businessId
     */
    public MoeBookOnlineNotification initBusinessNotification(Integer businessId) {
        MoeBookOnlineNotification notification = moeBookOnlineNotificationMapper.selectByBusinessId(businessId);
        if (notification == null) {
            notification = new MoeBookOnlineNotification();
            notification.setBusinessId(businessId);
            notification.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
            notification.setSubmitTemplate(OnlineBookingNotificationConst.SUBMIT_TEMPLATE);
            notification.setAutoMoveTemplate(OnlineBookingNotificationConst.AUTO_MOVE_TEMPLATE);
            notification.setDeclineTemplate(OnlineBookingNotificationConst.DECLINE_TEMPLATE);
            notification.setAcceptTemplate(OnlineBookingNotificationConst.ACCEPT_TEMPLATE);
            // config submit accept auto_move decline template for customer -> msg, subject
            // and content
            // business no need custom
            notification.setSubmitEmailSubjectTemplate(
                    OnlineBookingNotificationConst.SUBMIT_EMAIL_SUBJECT_CLIENT_TEMPLATE);
            notification.setSubmitEmailContentTemplate(
                    OnlineBookingNotificationConst.SUBMIT_EMAIL_CONTENT_CLIENT_TEMPLATE);
            notification.setAcceptEmailSubjectTemplate(
                    OnlineBookingNotificationConst.ACCEPT_EMAIL_SUBJECT_CLIENT_TEMPLATE);
            notification.setAcceptEmailContentTemplate(
                    OnlineBookingNotificationConst.ACCEPT_EMAIL_CONTENT_CLIENT_TEMPLATE);
            notification.setAutoMoveEmailSubjectTemplate(
                    OnlineBookingNotificationConst.AUTO_MOVE_EMAIL_SUBJECT_CLIENT_TEMPLATE);
            notification.setAutoMoveEmailContentTemplate(
                    OnlineBookingNotificationConst.AUTO_MOVE_EMAIL_CONTENT_CLIENT_TEMPLATE);
            notification.setDeclineEmailSubjectTemplate(
                    OnlineBookingNotificationConst.DECLINE_EMAIL_SUBJECT_CLIENT_TEMPLATE);
            notification.setDeclineEmailContentTemplate(
                    OnlineBookingNotificationConst.DECLINE_EMAIL_CONTENT_CLIENT_TEMPLATE);
            moeBookOnlineNotificationMapper.insertSelective(notification);
            notification = moeBookOnlineNotificationMapper.selectByBusinessId(businessId);
        }
        return notification;
    }

    /**
     * 支持重复调用（重复初始化）
     *
     * @param businessId
     */
    public void initBusinessProfile(Integer businessId, Long companyId, MoeBusinessDto businessDto) {
        MoeBookOnlineProfile profile = getProfileByBusinessId(businessId);
        if (profile == null) {
            log.info("init profile for {} ", businessId);
            profile = new MoeBookOnlineProfile();
            profile.setBusinessId(businessId);
            profile.setCompanyId(companyId);
            profile.setBusinessName(businessDto.getBusinessName());
            profile.setPhoneNumber(businessDto.getPhoneNumber());
            profile.setWebsite(businessDto.getWebsite());
            profile.setAddress(businessDto.getAddress());
            profile.setDescription("");
            profile.setBusinessHoursJson(DEFAULT_STAFF_TIME);
            profile.setBusinessEmail(businessDto.getOwnerEmail());
            profile.setAvatarPath(businessDto.getAvatarPath());
            profile.setCreateTime(DateUtil.get10TimestampInteger());
            profile.setUpdateTime(profile.getCreateTime());
            moeBookOnlineProfileMapper.insertSelective(profile);
        }
    }

    /**
     * 初始化单个 OB staff time
     */
    public void initOneStaffTime(MoeBookOnlineStaffTimeParams staffTimeParams) {
        // 加锁避免重复初始化
        String resourceKey = lockManager.getResourceKey(LockManager.OB_STAFF_TIME, staffTimeParams.getStaffId());
        String value = CommonUtil.getUuid();
        try {
            if (lockManager.lockWithRetry(resourceKey, value)) {
                MoeBookOnlineStaffTime currentStaffTime = new MoeBookOnlineStaffTime();
                // create new record
                currentStaffTime.setBusinessId(staffTimeParams.getBusinessId());
                currentStaffTime.setCompanyId(staffTimeParams.getCompanyId());
                currentStaffTime.setStaffId(staffTimeParams.getStaffId());
                String staffTimes = staffTimeParams.getStaffTimes();
                currentStaffTime.setStaffTimes(!StringUtils.hasText(staffTimes) ? DEFAULT_STAFF_TIME : staffTimes);
                currentStaffTime.setStaffSlots(staffTimeParams.getStaffSlots());
                currentStaffTime.setUpdateTime(DateUtil.get10Timestamp());
                currentStaffTime.setCreateTime(currentStaffTime.getUpdateTime());
                currentStaffTime.setStatus(DeleteStatusEnum.STATUS_NORMAL);
                staffTimeSyncService.insertSelective(currentStaffTime);
            } else {
                log.error("get lock failed for " + resourceKey);
            }
        } finally {
            lockManager.unlock(resourceKey, value);
        }
    }

    private String getStaffWorkingTime(Integer businessId, Integer staffId) {
        MoeWorkingDailyDTO workingDailyDTO = new MoeWorkingDailyDTO();
        workingDailyDTO.setStaffId(staffId);
        workingDailyDTO.setDate(DateUtil.getNowDate());
        // return [] when staffId is not available
        List<MoeWorkingDailyDTO> workingDailyDTOList =
                iBusinessStaffClient.query(businessId, workingDailyDTO).getData();
        if (CollectionUtils.isEmpty(workingDailyDTOList)) {
            return null;
        } else {
            Map<String, StaffTime> resultStaffTime = new HashMap<>();
            // 根据每天的配置时间，设置staffTime
            workingDailyDTOList.forEach(currDto -> {
                StaffTime tmpStaffTime = new StaffTime();
                List<TimeRangeDto> tmpRange = tmpStaffTime.getTimeRange();
                TimeRangeDto[] ranges = JsonUtil.toBean(currDto.getTimeRange(), TimeRangeDto[].class);
                Arrays.stream(ranges).forEach(range -> {
                    tmpRange.add(new TimeRangeDto(range.getStartTime(), range.getEndTime()));
                });
                resultStaffTime.put(WeekUtil.getKeyForWeekDay(currDto.getDayOfWeek()), tmpStaffTime);
            });
            return JsonUtil.toJson(resultStaffTime);
        }
    }

    public MoeBookOnlineNotification getNotificationByBusinessId(Integer businessId, boolean withClientNotification) {
        MoeBookOnlineNotification result = moeBookOnlineNotificationMapper.selectByBusinessId(businessId);
        if (result == null) {
            return null;
        }
        if (withClientNotification) {
            fillClientNotification(result);
        }
        return result;
    }

    /**
     * Get the online booking configuration information of the specified business
     * <br/>
     * There is idempotent control to ensure that data must be returned <br/>
     * There is pricing plan permission verification
     *
     * @param businessId business id
     * @return online booking configuration
     */
    public MoeBusinessBookOnline getSettingInfoByBusinessId(Integer businessId) {
        MoeBusinessBookOnline obSetting = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
        pricingCheckService.checkOnlineBooking(businessId, obSetting);
        if (Objects.isNull(obSetting)) {
            try {
                Long companyId = businessInfoHelper.getCompanyIdByBusinessId(businessId);
                obSetting = this.initBookOnlineInfo(businessId, companyId);
            } catch (DuplicateKeyException e) {
                log.warn("Initialization OB concurrency conflict, businessId: {}", businessId);
                obSetting = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
            }
        }
        // salon business reset mobile grooming setting
        OBBusinessInfoDTO businessInfo = iBusinessBusinessClient.getBusinessInfoForOB(
                InfoIdParams.builder().infoId(businessId).build());
        if (Objects.equals(businessInfo.getBusinessMode(), SubscriptionConst.BUSINESS_TYPE_SALON)) {
            resetMobileGroomingSetting(obSetting);
        }

        if (isDeletedBusiness(businessInfo.getCompanyId())) {
            return obSetting;
        }

        // compatible logic: get accept_client_type from new field
        final var acceptClientType = obAvailabilitySettingServiceBlockingStub
                .getGroomingServiceAvailability(GetGroomingServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(businessInfo.getCompanyId())
                                .setBusinessId(businessId)
                                .build())
                        .build())
                .getAvailability()
                .getAcceptCustomerType();
        obSetting.setAcceptClient((byte) acceptClientType.getNumber());

        return obSetting;
    }

    private static boolean isDeletedBusiness(Integer companyId) {
        return companyId == null || companyId < 0;
    }

    /**
     * Reset mobile grooming setting
     *
     * @param obSetting ob setting
     */
    public void resetMobileGroomingSetting(MoeBusinessBookOnline obSetting) {
        obSetting.setIsNeedAddress(CommonConstant.DISABLE);
        obSetting.setIsByZipcode(CommonConstant.DISABLE);
        obSetting.setZipCodes("");
        obSetting.setIsByRadius(CommonConstant.DISABLE);
        obSetting.setSettingLat("");
        obSetting.setSettingLng("");
        obSetting.setSettingLocation("");
        obSetting.setMaxAvailableTime(null);
        obSetting.setMaxAvailableDist(null);
        obSetting.setAllowedSimplifySubmit(CommonConstant.DISABLE);
        obSetting.setIsCheckExistingClient(CommonConstant.DISABLE);
        obSetting.setSmartScheduleEnable(CommonConstant.DISABLE);
        obSetting.setServiceAreaEnable(CommonConstant.DISABLE);
    }

    @Transactional
    public int updateInfoByPrimaryIdOrBusinessId(BusinessBookOnlineParams obParams) {
        // 参数校验 主键和businessId不能同时为空
        if (obParams.getId() == null && obParams.getBusinessId() == null) {
            throw new CommonException(ResponseCodeEnum.BUSINESS_IS_EMPTY, "OB primary id and business id is null");
        }
        obParams.setUpdateTime(DateUtil.get10Timestamp());
        // 兼容老字段
        if (obParams.getAppointmentInterval() != null && obParams.getTimeslotMins() == null) {
            obParams.setTimeslotMins(obParams.getAppointmentInterval() * 10);
        }

        MoeBusinessBookOnline moeBusinessBookOnline;
        if (obParams.getBusinessId() == null) {
            moeBusinessBookOnline = moeBusinessBookOnlineMapper.selectByPrimaryKey(obParams.getId());
            obParams.setBusinessId(moeBusinessBookOnline.getBusinessId());
        } else {
            moeBusinessBookOnline = moeBusinessBookOnlineMapper.selectByBusinessId(obParams.getBusinessId());
        }
        // check turn on/off online booking switch permission
        if (obParams.getIsEnable() != null && !obParams.getIsEnable().equals(moeBusinessBookOnline.getIsEnable())) {
            permissionHelper.checkPermission(
                    moeBusinessBookOnline.getCompanyId(),
                    new HashSet<>(),
                    AuthContext.get().staffId(),
                    PermissionEnums.ONLINE_BOOKING_SWITCH);
        }

        if (ServiceEnum.ENABLE_NO_SHOW_FEE_TRUE.equals(obParams.getEnableNoShowFee())) {
            if (!iPaymentService.businessHasVerifiedBank(obParams.getBusinessId())) {
                throw new CommonException(ResponseCodeEnum.STRIPE_ACCOUNT_ERROR);
            }
            if (!iBusinessPaymentMethodClient.checkCreditCardIsActive(obParams.getBusinessId())) {
                throw new CommonException(ResponseCodeEnum.CREDIT_CARD_NEED_OPEN);
            }
        }
        if (BooleanEnum.VALUE_FALSE.equals(obParams.getIsNeedAddress())) {
            // 关闭need address 标志位， 通知对其他相关标志位也置空
            obParams.setServiceAreaEnable(BooleanEnum.VALUE_FALSE);
            obParams.setSmartScheduleEnable(BooleanEnum.VALUE_FALSE);
            obParams.setIsCheckExistingClient(BooleanEnum.VALUE_FALSE);
        }

        renewNotificationIfNecessary(obParams);

        log.info(
                "syncStatusChanged in updateInfoByPrimaryIdOrBusinessId, businessId:{}, syncStatus:{}",
                obParams.getBusinessId(),
                obParams.getAvailableTimeSync());

        return moeBusinessBookOnlineMapper.updateInfoByPrimaryIdOrBusinessId(obParams);
    }

    public void deleteServiceArea(Integer businessId, Integer serviceAreaId) {
        if (serviceAreaId == null) {
            return;
        }
        MoeBusinessBookOnline moeBusinessBookOnline = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
        if (moeBusinessBookOnline == null || CollectionUtils.isEmpty(moeBusinessBookOnline.getServiceAreas())) {
            return;
        }
        // 过滤出不包含serviceAreaId的serviceAreas
        List<Integer> serviceAreas = moeBusinessBookOnline.getServiceAreas().stream()
                .filter(k -> !k.equals(serviceAreaId))
                .collect(Collectors.toList());
        if (serviceAreas.size() == moeBusinessBookOnline.getServiceAreas().size()) {
            return;
        }

        BusinessBookOnlineParams obParams = new BusinessBookOnlineParams();
        obParams.setId(moeBusinessBookOnline.getId());
        obParams.setServiceAreas(serviceAreas);
        moeBusinessBookOnlineMapper.updateInfoByPrimaryIdOrBusinessId(obParams);
    }

    private void renewNotificationIfNecessary(BusinessBookOnlineParams obParams) {
        if (Objects.equals(obParams.getBookingRangeEndType(), BookOnlineDTO.BookingRangeEndType.USING_DATE)) {
            MoeBusinessBookOnline obSetting = moeBusinessBookOnlineMapper.selectByBusinessId(obParams.getBusinessId());
            LocalDate newDate = LocalDate.parse(obParams.getBookingRangeEndDate());
            if (StringUtils.hasText(obSetting.getBookingRangeEndDate())) {
                LocalDate oldDate = LocalDate.parse(obSetting.getBookingRangeEndDate());
                if (newDate.isEqual(oldDate)) {
                    return;
                }
            }

            LocalDate notifyDate = LocalDate.now().plusDays(7);
            boolean isNeedSendNotification = newDate.isBefore(notifyDate) || newDate.isEqual(notifyDate);

            MoeBusinessBookOnlineExample example = new MoeBusinessBookOnlineExample();
            example.createCriteria().andBusinessIdEqualTo(obParams.getBusinessId());
            MoeBusinessBookOnline bookOnline = new MoeBusinessBookOnline();
            bookOnline.setIsNeedSendRenewNotification(isNeedSendNotification);
            moeBusinessBookOnlineMapper.updateByExampleSelective(bookOnline, example);
        }
    }

    public int updateMobileGrooming(MobileGroomingParams mobileGroomingParams) {
        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        BeanUtils.copyProperties(mobileGroomingParams, businessBookOnline);

        log.info(
                "syncStatusChanged in updateMobileGrooming, businessId:{}, syncStatus:{}",
                businessBookOnline.getBusinessId(),
                businessBookOnline.getAvailableTimeSync());

        return moeBusinessBookOnlineMapper.updateInfoByBusinessId(businessBookOnline);
    }

    public int updateService(ServiceParams serviceParams) {
        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setBusinessId(serviceParams.getBusinessId());
        businessBookOnline.setServiceFilter(serviceParams.getServiceFilter());
        int i = moeBusinessBookOnlineMapper.updateInfoByBusinessId(businessBookOnline);

        if (CollectionUtils.isEmpty(serviceParams.getServiceList())) {
            return i;
        }

        return groomingServiceService.updateServiceList(
                serviceParams.getBusinessId(), serviceParams.getCompanyId(), serviceParams.getServiceList());
    }

    public int updateNotification(Long companyId, MoeBookOnlineNotification currentNotification) {
        if (currentNotification.getSubmitClientType() != null
                || currentNotification.getSubmitTemplate() != null
                || currentNotification.getSubmitEmailSubjectTemplate() != null
                || currentNotification.getSubmitEmailContentTemplate() != null
                || currentNotification.getAcceptClientType() != null
                || currentNotification.getAcceptTemplate() != null
                || currentNotification.getAcceptEmailSubjectTemplate() != null
                || currentNotification.getAcceptEmailContentTemplate() != null
                || currentNotification.getDeclineClientType() != null
                || currentNotification.getDeclineTemplate() != null
                || currentNotification.getDeclineEmailSubjectTemplate() != null
                || currentNotification.getDeclineEmailContentTemplate() != null
                || currentNotification.getAutoMoveClientType() != null
                || currentNotification.getAutoMoveTemplate() != null
                || currentNotification.getAutoMoveEmailSubjectTemplate() != null
                || currentNotification.getAutoMoveEmailContentTemplate() != null) {
            updateClientNotification(companyId, currentNotification);
        }
        // 前置约定： 每个商户下保证有且只有一条notification记录
        return moeBookOnlineNotificationMapper.updateByBusinessIdWithBLOBs(currentNotification);
    }

    public List<MoeBookOnlineGallery> getGalleryByBusinessId(Integer id) {
        return moeBookOnlineGalleryMapper.selectByBusinessId(id);
    }

    public List<MoeBookOnlineGallery> getGalleryByBookOnlineName(String name) {
        Integer id = getBusinessIdByBookOnlineName(name);
        return moeBookOnlineGalleryMapper.selectByBusinessId(id);
    }

    public Integer getBusinessIdByBookOnlineName(String name) {
        BusinessCompanyPO businessCompanyPO = moeBusinessBookOnlineMapper.selectByBookOnlineName(name);
        if (Objects.isNull(businessCompanyPO)
                || Objects.isNull(businessCompanyPO.getBusinessId())
                || Objects.equals(businessCompanyPO.getBusinessId(), 0)) {
            throw bizException(Code.CODE_BOOK_ONLINE_NAME_INVALID);
        }
        return businessCompanyPO.getBusinessId();
    }

    public BusinessCompanyPO getBusinessCompanyByBookOnlineName(String name) {
        return moeBusinessBookOnlineMapper.selectByBookOnlineName(name);
    }

    /**
     * C端获取OB相关设置
     *
     * @param obName
     * @return
     */
    public InfoDto getOBSettingByBookOnlineName(String obName) {
        Integer businessId = getBusinessIdByBookOnlineName(obName);
        // 结果放到business_info下面
        BusinessInfoDto infoObject = new BusinessInfoDto();
        MoeBusinessBookOnline currentBusiness = getSettingInfoByBusinessId(businessId);
        // 当商家降级后，需要在C端接口上返回only show applicable service开关的默认值 关闭，否则C端页面显示数据会有问题
        if (!groomingServiceService.isServiceFilterAllowed(businessId)) {
            currentBusiness.setServiceFilter(OnlineBookingConst.SERVICE_FILTER_DISABLE);
        }
        infoObject.setIsEnable(currentBusiness.getIsEnable());
        infoObject.setSetting(currentBusiness);
        // 商家会员是否到期 todo check level
        infoObject.setIsAvailable(true);

        // 查询payment setting
        PaymentSettingDTO paymentSetting = iPaymentSettingClient.getPaymentSetting(businessId);
        PaymentSettingForClientDTO paymentSettingForClient = new PaymentSettingForClientDTO();
        BeanUtils.copyProperties(paymentSetting, paymentSettingForClient);

        // 查询tip配置
        SmartTipConfigDTO tipConfig = iPaymentSettingClient.getSmartTipConfig(businessId);
        SmartTipConfigForClientDTO tipConfigForClient = new SmartTipConfigForClientDTO();
        BeanUtils.copyProperties(tipConfig, tipConfigForClient);

        // 构造最终返回结果
        return new InfoDto()
                .setBusinessInfo(infoObject)
                .setSquareInfo(iPaymentService.getToken(businessId))
                .setPaymentSetting(paymentSettingForClient)
                .setTipConfig(tipConfigForClient);
    }

    public int addGalleryImage(MoeBookOnlineGalleryParams galleryParam) {
        MoeBookOnlineGallery galleryRecord = new MoeBookOnlineGallery();
        BeanUtils.copyProperties(galleryParam, galleryRecord);
        galleryRecord.setCreateTime(DateUtil.get10TimestampInteger());
        galleryRecord.setUpdateTime(DateUtil.get10TimestampInteger());
        boolean result = moeBookOnlineGalleryMapper.insertSelective(galleryRecord) > 0;
        int galleryRecordId = galleryRecord.getId();
        if (result) {
            MoeBookOnlineGallery tmpRecord = new MoeBookOnlineGallery();
            tmpRecord.setId(galleryRecordId);
            tmpRecord.setSort(galleryRecordId);
            moeBookOnlineGalleryMapper.updateByPrimaryKeySelective(tmpRecord);
        }
        return galleryRecordId;
    }

    public int batchDeleteGalleryImage(MoeBookOnlineGalleryBatchParams batchDeleteParams) {
        return moeBookOnlineGalleryMapper.batchDeleteGallery(batchDeleteParams);
    }

    /**
     * 被标记的图片放在最前面
     * 新上传的图片放在前面
     *
     * @param batchDeleteParams
     * @return
     */
    public int batchStarGalleryImage(MoeBookOnlineGalleryBatchParams batchDeleteParams) {
        List<MoeBookOnlineGallery> galleries =
                moeBookOnlineGalleryMapper.selectByBusinessId(batchDeleteParams.getBusinessId());
        // erase old star flag
        for (MoeBookOnlineGallery pic : galleries) {
            pic.setIsStar(BooleanEnum.IS_STAR_FALSE);
        }
        // update isStar and sort column
        int sortStart = galleries.size();
        for (int starId : batchDeleteParams.getImageIds()) {
            MoeBookOnlineGallery current = galleries.stream()
                    .filter(pic -> pic.getId().equals(starId))
                    .findFirst()
                    .get();
            current.setIsStar(BooleanEnum.IS_STAR_TRUE);
            current.setSort(sortStart--);
        }
        // remain pictures should keep the old order.
        for (MoeBookOnlineGallery pic : galleries) {
            if (BooleanEnum.IS_AVAILABLE_FALSE.equals(pic.getIsStar())) {
                pic.setSort(sortStart--);
            }
        }
        return moeBookOnlineGalleryMapper.batchStarGallery(galleries);
    }

    /**
     * 查询business的staff online booking availability
     * 如无记录则进行初始化
     *
     * @param businessId
     * @return
     */
    public List<BookOnlineStaffAvailabilityDTO> getStaffOBAvailability(Integer businessId, Boolean onlyShowOnCalendar) {
        // 查询商家所有的staff
        StaffIdListParams params = new StaffIdListParams();
        params.setBusinessId(businessId);
        List<MoeStaffDto> staffs = iBusinessStaffClient.getStaffList(params);
        // 过滤删除状态的staff、 show on calendar开关的判断
        staffs.removeIf(s -> !Objects.equals(s.getStatus(), StaffEnum.STATUS_NORMAL)
                || (onlyShowOnCalendar && Objects.equals(s.getShowOnCalendar(), StaffEnum.SHOW_ON_CALENDAR_FALSE)));

        Set<Integer> staffIds = staffs.stream().map(MoeStaffDto::getId).collect(Collectors.toSet());

        var response = obStaffAvailabilityServiceBlockingStub.getStaffAvailabilityStatus(
                GetStaffAvailabilityStatusRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addAllStaffIdList(
                                staffIds.stream().map(Integer::longValue).toList())
                        .build());

        // 构造对象返回
        List<BookOnlineStaffAvailabilityDTO> result = new ArrayList<>();
        var syncWithWorkingHour = Boolean.TRUE.equals(staffAvailableSyncService.queryAvailableTimeSync(businessId))
                ? CommonConstant.ENABLE
                : CommonConstant.DISABLE;
        for (Map.Entry<Long, Boolean> staffAvailableEntry :
                response.getStaffAvailabilityMap().entrySet()) {
            byte staffAvailable = Boolean.TRUE.equals(staffAvailableEntry.getValue())
                    ? CommonConstant.ENABLE
                    : CommonConstant.DISABLE;
            result.add(BookOnlineStaffAvailabilityDTO.builder()
                    .staffId(staffAvailableEntry.getKey().intValue())
                    .byWorkingHourEnable(staffAvailable)
                    .bySlotEnable(staffAvailable)
                    .syncWithWorkingHour(syncWithWorkingHour)
                    .build());
        }

        return result;
    }

    /**
     * 修改staff online booking availability
     * (外部调用，有同步旧表操作，如不需同步，可以调用这个方法modifyStaffOBAvailabilityInternal)
     *
     * @param businessId
     * @param params
     */
    public void modifyStaffOBAvailability(Integer businessId, BookOnlineStaffAvailabilityDTO params) {
        // 参数检查
        if (params.getBySlotEnable() == null && params.getByWorkingHourEnable() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        // staff检查
        StaffIdParams staffIdParams = new StaffIdParams();
        staffIdParams.setBusinessId(businessId);
        staffIdParams.setStaffId(params.getStaffId());
        MoeStaffDto staffDto = iBusinessStaffClient.getStaff(staffIdParams);
        if (staffDto == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Staff not found");
        }
        // 兼容旧字段：如果byWorkingHourEnable有改动，同步更新到staff表
        if (params.getByWorkingHourEnable() != null) {
            MoeStaffDto updateParam = new MoeStaffDto();
            updateParam.setId(params.getStaffId());
            updateParam.setBookOnlineAvailable(params.getByWorkingHourEnable());
            iBusinessStaffClient.updateStaff(updateParam);
        }

        // 更新
        MoeBookOnlineAvailableStaff update = new MoeBookOnlineAvailableStaff();
        update.setBusinessId(businessId);
        update.setStaffId(params.getStaffId());
        update.setByWorkingHourEnable(params.getByWorkingHourEnable());
        update.setBySlotEnable(params.getBySlotEnable());
        update.setUpdateTime(DateUtil.get10Timestamp());
        staffAvailableSyncService.updateByStaffId(update);
    }

    public Boolean getSyncWithWorkingHourStatus(Integer businessId) {
        return staffAvailableSyncService.queryAvailableTimeSync(businessId);
    }

    public void modifySyncWithWorkingHourStatus(Integer businessId) {
        Byte syncWithWorkingHour = Boolean.TRUE.equals(getSyncWithWorkingHourStatus(businessId))
                ? OnlineBookingConst.SYNC_WITH_WORKING_HOUR_DISABLE
                : OnlineBookingConst.SYNC_WITH_WORKING_HOUR_ENABLE;
        staffAvailableSyncService.updateSyncByBusinessId(businessId, syncWithWorkingHour);
    }

    public void modifyStaffOBAvailabilityInternalBatch(
            Integer businessId, List<BookOnlineStaffAvailabilityDTO> paramList) {
        paramList.forEach(param -> modifyStaffOBAvailabilityInternal(businessId, param));
    }

    public void modifyStaffOBAvailabilityInternal(Integer businessId, BookOnlineStaffAvailabilityDTO params) {
        if (params.getStaffId() == null) {
            return;
        }
        MoeBookOnlineAvailableStaff update = new MoeBookOnlineAvailableStaff();
        BeanUtils.copyProperties(params, update);
        update.setBusinessId(businessId);
        update.setUpdateTime(DateUtil.get10Timestamp());
        staffAvailableSyncService.updateByStaffId(update);
    }

    public MoeBookOnlineProfile getProfileByBusinessId(Integer businessId) {
        return moeBookOnlineProfileMapper.selectByBusinessId(businessId);
    }

    public int updateProfileByPrimaryIdOrBusinessId(MoeBookOnlineProfileParams profileParams) {
        // 参数校验 主键和businessId不能同时为空
        if (profileParams.getId() == null && profileParams.getBusinessId() == null) {
            throw new CommonException(
                    ResponseCodeEnum.BUSINESS_IS_EMPTY, "OB profile primary id and business id is null");
        }
        profileParams.setUpdateTime(DateUtil.get10TimestampInteger());
        profileParams.setCreateTime(null);
        return moeBookOnlineProfileMapper.updateProfileByPrimaryIdOrBusinessId(profileParams);
    }

    public List<MoeBookOnlineStaffTime> getStaffTimeByBusinessId(Integer businessId) {
        List<MoeBookOnlineStaffTime> staffTimeList = staffTimeSyncService.selectByBusinessId(businessId);

        List<Integer> emptyTimeslotStaffIds = new ArrayList<>();

        // staffSlots初始化并保存
        staffTimeList.forEach(staffTime -> {
            if (ObjectUtils.isEmpty(staffTime.getStaffSlots())) {
                emptyTimeslotStaffIds.add(staffTime.getStaffId());
            }
        });
        if (!emptyTimeslotStaffIds.isEmpty()) {
            generateDefaultTimeSlots(staffTimeList, businessId, emptyTimeslotStaffIds);
        }
        return staffTimeList;
    }

    private void generateDefaultTimeSlots(
            List<MoeBookOnlineStaffTime> staffTimeList, Integer businessId, List<Integer> staffIds) {
        Map<Integer, Map<Integer, TimeRangeDto>> workingDailyMap =
                iBusinessStaffClient.getBusinessStaffWorkingHour(businessId, staffIds);

        Map<Integer, Map<Integer, TimeRangeDto>> staffWorkingDailyMap = new HashMap<>();
        workingDailyMap.forEach((weekday, timeRangeMap) -> {
            timeRangeMap.forEach((staffId, timeRange) -> {
                Map<Integer, TimeRangeDto> staffWorkingDaily =
                        staffWorkingDailyMap.computeIfAbsent(staffId, k -> new HashMap<>());
                staffWorkingDaily.put(weekday, timeRange);
            });
        });

        for (MoeBookOnlineStaffTime staffTime : staffTimeList) {
            if (!StringUtils.hasText(staffTime.getStaffSlots())) {
                Map<Integer, TimeRangeDto> weekWorkingDaily = staffWorkingDailyMap.get(staffTime.getStaffId());

                Map<String, OneDayTimeslotsDTO> dayCapacityMap = new HashMap<>();
                for (int i = 0; i < 7; i++) {
                    OneDayTimeslotsDTO staffCapacity = new OneDayTimeslotsDTO();
                    // 默认使用早上9点作为第一个slot，并设置为不选中
                    staffCapacity.setIsSelected(false);
                    CapacityTimeslotDTO timeslot = new CapacityTimeslotDTO();
                    timeslot.setCapacity(DEFAULT_STAFF_CAPACITY);
                    timeslot.setStartTime(DEFAULT_START_TIME);
                    // working hour有设置时则替换为working hour的开始时间
                    if (!CollectionUtils.isEmpty(weekWorkingDaily)) {
                        TimeRangeDto dayWorkingRange = weekWorkingDaily.get(i);
                        if (dayWorkingRange != null && dayWorkingRange.getStartTime() != null) {
                            staffCapacity.setIsSelected(true);
                            timeslot.setStartTime(dayWorkingRange.getStartTime());
                        }
                    }
                    staffCapacity.setTimeSlot(Collections.singletonList(timeslot));
                    dayCapacityMap.put(WeekUtil.getKeyForWeekDay(i), staffCapacity);
                }
                staffTime.setStaffSlots(JsonUtil.toJson(dayCapacityMap));

                MoeBookOnlineStaffTime updateStaffTime = new MoeBookOnlineStaffTime();
                updateStaffTime.setStaffSlots(staffTime.getStaffSlots());
                updateStaffTime.setUpdateTime(DateUtil.get10Timestamp());
                staffTimeSyncService.updateByPrimaryKeySelective(businessId, staffTime.getStaffId(), updateStaffTime);
            }
        }
    }

    public void updateBusinessBookOnlineSetting(Integer businessId) {
        MoeBusinessBookOnline obInfo = getSettingInfoByBusinessId(businessId);
        if (obInfo != null) {
            MoeBusinessBookOnline saveBookOnline = new MoeBusinessBookOnline();
            saveBookOnline.setId(obInfo.getId());
            saveBookOnline.setEnableNoShowFee(ServiceEnum.ENABLE_NO_SHOW_FEE_FALSE);
            moeBusinessBookOnlineMapper.updateByPrimaryKeySelective(saveBookOnline);
        }
    }

    public MoeBusinessBookOnlineDto getBusinessBookOnlineSetting(Integer businessId) {
        MoeBusinessBookOnline obInfo = getSettingInfoByBusinessId(businessId);
        MoeBusinessBookOnlineDto dto = new MoeBusinessBookOnlineDto();
        BeanUtils.copyProperties(obInfo, dto);
        return dto;
    }

    public void saveStaffTime(MoeBookOnlineStaffTimeParams staffTimeParams) {
        if (staffTimeParams.getStaffId() == null) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_IS_EMPTY, "staff id is null");
        }
        if (staffTimeParams.getBusinessId() == null) {
            throw ExceptionUtil.bizException(Code.CODE_BUSINESS_IS_EMPTY, "business id is null");
        }
        obStaffAvailabilityServiceBlockingStub.getStaffAvailabilityStatus(GetStaffAvailabilityStatusRequest.newBuilder()
                .setBusinessId(staffTimeParams.getBusinessId().longValue())
                .addStaffIdList(staffTimeParams.getStaffId())
                .build());
        // update current record
        MoeBookOnlineStaffTime updateRecord = new MoeBookOnlineStaffTime();
        updateRecord.setStaffTimes(staffTimeParams.getStaffTimes());
        updateRecord.setStaffSlots(staffTimeParams.getStaffSlots());
        updateRecord.setUpdateTime(DateUtil.get10Timestamp());
        staffTimeSyncService.updateByPrimaryKeySelective(
                staffTimeParams.getBusinessId(), staffTimeParams.getStaffId(), updateRecord);
    }

    public List<GroomingQuestionDTO> getQuestionsByBusinessId(Integer businessId, Integer type) {
        List<MoeBookOnlineQuestion> originQuestion = moeBookOnlineQuestionMapper.getListByBusinessId(businessId, type);
        // 为每个问题添加key： 生成规则
        // //优化前端数据
        // if ($v['is_allow_delete'] == 0) {
        // //无法删除，是默认字段
        // $v['key'] = str_replace(' ', '_', $v['question']);
        // } else {
        // $v['key'] = "custom_{$v['form_detail_id']}";
        // }

        return originQuestion.stream()
                .map(question -> {
                    var dto = BookOnlineQuestionConverter.entityToDTO(question);
                    if (QuestionConst.IS_ALLOW_DELETE_FALSE.equals(dto.getIsAllowDelete())) {
                        dto.setKey(question.getQuestion().replaceAll(" ", "_"));
                    } else {
                        dto.setKey(QuestionConst.KEY_PREFIX + question.getId());
                    }

                    if (REFERRAL_SOURCE.equals(question.getQuestion())
                            || PREFERRED_GROOMER.equals(question.getQuestion())) {
                        dto.setQuestionType(QuestionConst.QUESTION_TYPE_SHORT);
                    }
                    return dto;
                })
                .toList();
    }

    public List<GroomingQuestionDTO> getQuestionListByBusinessId(Integer businessId, Long companyId, Integer type) {
        // 为存量用户添加新增的 question
        if (Objects.equals(QuestionConst.TYPE_PET_OWNER_QUESTION.intValue(), type)) {
            List<String> questionNameList = moeBookOnlineQuestionMapper.getListByBusinessId(businessId, type).stream()
                    .map(MoeBookOnlineQuestion::getQuestion)
                    .collect(Collectors.toList());
            moeGroomingQuestionService.createAdditionalPetOwnerQuestion(businessId, companyId, questionNameList);
        } else if (Objects.equals(QuestionConst.TYPE_BOARDING_SERVICE_QUESTION.intValue(), type)) {
            List<String> questionNameList = moeBookOnlineQuestionMapper.getListByBusinessId(businessId, type).stream()
                    .map(MoeBookOnlineQuestion::getQuestion)
                    .toList();
            if (CollectionUtils.isEmpty(questionNameList)) {
                moeGroomingQuestionService.createBoardingServiceQuestion(businessId, companyId);
            }
        } else if (Objects.equals(QuestionConst.TYPE_DAYCARE_SERVICE_QUESTION.intValue(), type)) {
            List<String> questionNameList = moeBookOnlineQuestionMapper.getListByBusinessId(businessId, type).stream()
                    .map(MoeBookOnlineQuestion::getQuestion)
                    .toList();
            if (CollectionUtils.isEmpty(questionNameList)) {
                moeGroomingQuestionService.createDaycareServiceQuestion(businessId, companyId);
            }
        }

        return getQuestionsByBusinessId(businessId, type);
    }

    public int createQuestion(BookOnlineQuestionParams param) {
        // name check for business
        if (moeBookOnlineQuestionMapper.getCountWithName(
                        param.getBusinessId(), param.getQuestion(), param.getId(), param.getType())
                > 0) {
            throw bizException(
                    Code.CODE_INVOICE_SET_TIPS_ERROR,
                    MessageFormat.format("Name is already in use: {0}", param.getQuestion()));
        }
        param.setCreateTime(DateUtil.get10Timestamp());
        param.setUpdateTime(param.getCreateTime());

        moeGroomingQuestionService.insert(BookOnlineQuestionConverter.paramToEntity(param));

        return 1;
    }

    public int updateQuestion(BookOnlineQuestionParams param) {
        // name check for business
        if (moeBookOnlineQuestionMapper.getCountWithName(
                        param.getBusinessId(), param.getQuestion(), param.getId(), param.getType())
                > 0) {
            throw bizException(
                    Code.CODE_INVOICE_SET_TIPS_ERROR,
                    MessageFormat.format("Name is already in use: {0}", param.getQuestion()));
        }
        param.setUpdateTime(DateUtil.get10Timestamp());

        var updateBean = BookOnlineQuestionConverter.paramToEntity(param);
        return moeGroomingQuestionService.update(updateBean);
    }

    public void updateQuestionList(BookOnlineQuestionListParams params) {
        for (var question : params.getQuestions()) {
            question.setBusinessId(params.getBusinessId());
            question.setCompanyId(params.getCompanyId());
            if (question.getId() == null) {
                createQuestion(question);
            } else {
                updateQuestion(question);
            }
        }
    }

    public Integer sortQuestion(SortIdListParams questionsIdList) {
        List<SortDto> dtos = SortUtils.sort(questionsIdList.getIdList());
        return moeBookOnlineQuestionMapper.sortMoeQuestion(dtos);
    }

    /**
     * 前两个为可用时间，最后一个为不可用时间，三个取交集
     *
     * @param availableTimeRanges1
     * @param availableTimeRanges2
     * @param noAvailableTimeRanges
     * @return
     */
    public List<TimeRangeDto> filterOutNotAvailableTimeForDay(
            List<TimeRangeDto> availableTimeRanges1,
            List<TimeRangeDto> availableTimeRanges2,
            List<TimeRangeDto> noAvailableTimeRanges) {
        if (availableTimeRanges1 == null) {
            availableTimeRanges1 = new ArrayList<>();
        }
        if (availableTimeRanges2 == null) {
            availableTimeRanges2 = new ArrayList<>();
        }
        List<TimeRangeDto> returnAvailableTimes = new ArrayList<>();
        List<TimeRangeDto> tempRanges = new ArrayList<>();
        tempRanges.addAll(availableTimeRanges1);
        tempRanges.addAll(availableTimeRanges2);
        if (CollectionUtils.isEmpty(tempRanges)) {
            return returnAvailableTimes;
        }
        int minStartTime = tempRanges.get(0).getStartTime();
        int maxEndTime = tempRanges.get(0).getEndTime();
        for (TimeRangeDto timeRange : tempRanges) {
            if (minStartTime > timeRange.getStartTime()) {
                minStartTime = timeRange.getStartTime();
            }
            if (maxEndTime < timeRange.getEndTime()) {
                maxEndTime = timeRange.getEndTime();
            }
        }
        tempRanges = null;
        LinkedList<Integer> allAvailableTimes = new LinkedList<>();
        for (int i = minStartTime; i <= maxEndTime; i++) {
            boolean isFind = false;
            for (TimeRangeDto availableTime1 : availableTimeRanges1) {
                if (availableTime1.getStartTime() <= i && i <= availableTime1.getEndTime()) {
                    // 在第一个可用段内找到
                    isFind = true;
                    break;
                }
            }
            if (!isFind) {
                // 上个是判断可用时间，没找到就跳过当前的时间不用找了
                continue;
            }
            // 重置为faile
            isFind = false;
            for (TimeRangeDto availableTime2 : availableTimeRanges2) {
                if (availableTime2.getStartTime() <= i && i <= availableTime2.getEndTime()) {
                    // 在第二个可用段内找到
                    isFind = true;
                    break;
                }
            }
            if (!isFind) {
                // 上个是判断可用时间，没找到就跳过当前的时间不用找了
                continue;
            }
            isFind = false;
            for (TimeRangeDto noAvailableTime : noAvailableTimeRanges) {
                if (noAvailableTime.getStartTime() < i && i < noAvailableTime.getEndTime()) {
                    isFind = true;
                    break;
                }
            }
            // 上个是判断不可用时间，应该是不能找到才对，没找到，把time保存
            if (!isFind) {
                allAvailableTimes.add(i);
            }
        }
        Collections.sort(allAvailableTimes);
        if (allAvailableTimes.size() > 0) {
            int endTime = allAvailableTimes.getLast();
            int startTime = allAvailableTimes.removeFirst();
            int lastTime = startTime;
            while (allAvailableTimes.size() > 0) {
                int timeI = allAvailableTimes.removeFirst();
                if (timeI == (lastTime + 1)) {
                    lastTime = timeI;
                } else {
                    returnAvailableTimes.add(new TimeRangeDto(startTime, lastTime));
                    startTime = timeI;
                    lastTime = timeI;
                }
                if (timeI == endTime) {
                    returnAvailableTimes.add(new TimeRangeDto(startTime, endTime));
                    break;
                }
            }
        }
        return returnAvailableTimes;
    }

    public String getObNameByBusinessId(Integer businessId) {
        MoeBusinessBookOnline bookOnline = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
        if (bookOnline == null) {
            // 兼容测试服上 商家没有bookonline数据的情况
            return null;
        }
        return bookOnline.getBookOnlineName();
    }

    void generateAvailableTimePoint(
            List<TimeRangeDto> origin,
            int appointInterval,
            int totalDuration,
            OBAvailableTimeStaffAvailableTimeDto timeList) {
        origin.stream()
                .filter(timeRange -> (timeRange.getEndTime() - timeRange.getStartTime()) >= totalDuration)
                .forEach(timeRangeDto -> {
                    Integer start = timeRangeDto.getStartTime();
                    Integer end = timeRangeDto.getEndTime() - totalDuration;
                    for (int point = start; point <= end; point += appointInterval) {
                        if (point >= AM_PM_PIVOT) {
                            timeList.getPm().add(point);
                        } else {
                            timeList.getAm().add(point);
                        }
                    }
                });
    }

    /**
     * 计算OB Prepay金额
     * 1.full pay：订单总金额，并查询对应的tax，计算booking fee
     * 2.deposit by fixed amount：返回固定定金金额，计算booking fee
     * 3.deposit by percentage：返回服务总额乘以定金百分比，计算booking fee
     * <p>
     * 最后判断Processing fee by client开关是否打开，如打开则计算Processing fee
     * 以及生成一个guid，用于标识支付和订单，deposit的支付以de_开头，和B端保持一致
     *
     * @param businessId 商家id
     * @param params     请求服务参数
     * @return
     */
    public PrepayAmountDTO calculateOBPrepayAmount(Integer businessId, PrepayAmountParams params) {
        PrepayAmountDTO result = new PrepayAmountDTO();

        // Stripe Only检查
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(businessId));
        if (!Objects.equals(businessDto.getPrimaryPayType(), PaymentMethodEnum.CARD_PROCESSOR_TYPE_STRIPE)) {
            return result;
        }
        var obSetting = mustGetBookOnline(businessId, AuthContext.get().getCustomerId());
        Byte prepayType = params.getPrepayType();
        Byte depositType = params.getDepositType();
        BigDecimal serviceAmount;
        BigDecimal serviceAndServiceChargeAmount = BigDecimal.ZERO;
        if (Objects.equals(prepayType, OnlineBookingConst.PREPAY_TYPE_DEPOSIT)
                && Objects.equals(depositType, OnlineBookingConst.DEPOSIT_TYPE_BY_FIXED_AMOUNT)) {
            // deposit by fixed amount，直接返回设置的金额
            serviceAmount = obSetting.getDepositAmount();
            serviceAndServiceChargeAmount = serviceAmount;
        } else {
            // full pay或deposit by percent，都需要先计算service price
            Integer customerId = Optional.ofNullable(params.getCustomerData())
                    .map(BookOnlineCustomerParams::getCustomerId)
                    .orElse(null);
            CalculateServiceAmountDTO serviceAmountDTO = getServiceSubTotalAmount(
                    businessId, params.getPetData(), params.getDiscountCode(), params.getStaffId(), customerId);
            serviceAmount = serviceAmountDTO.getServiceAmount();
            BigDecimal serviceChargeAmount = serviceAmountDTO.getServiceChargeAmount();
            if (Objects.equals(prepayType, OnlineBookingConst.PREPAY_TYPE_FULL_PAY)) {
                // full pay: service 总金额、tax 总金额
                // full pay 才返回 service-charge
                result.setTaxAmount(serviceAmountDTO.getTaxAmount());
                result.setServiceChargeAmount(serviceAmountDTO.getServiceChargeAmount());
                result.setServiceChargeList(serviceAmountDTO.getServiceChargeList());
                result.setApplyServiceChargeList(serviceAmountDTO.getApplyServiceChargeList());
                result.setDiscountAmount(serviceAmountDTO.getDiscountAmount());
                result.setUsedMembershipIds(serviceAmountDTO.getUsedMembershipIds());
                serviceAndServiceChargeAmount =
                        serviceAmount.add(serviceChargeAmount).subtract(serviceAmountDTO.getDiscountAmount());
            } else if (Objects.equals(depositType, OnlineBookingConst.DEPOSIT_TYPE_BY_PERCENTAGE)) {
                // deposit by percentage: service + serviceCharge 总金额乘以百分比
                serviceAmount = serviceAmount
                        .add(serviceChargeAmount)
                        .multiply(BigDecimal.valueOf(obSetting.getDepositPercentage())
                                .scaleByPowerOfTen(-2));
                serviceAndServiceChargeAmount = serviceAmount;
            }
        }

        result.setSubTotal(serviceAmount.setScale(2, RoundingMode.HALF_UP));

        // 计算Booking fee
        if (serviceAndServiceChargeAmount.compareTo(BigDecimal.ZERO) >= 0) {
            // 计算 booking fee 基于 subTotal + serviceCharge - discountAmount
            result.setFee(BigDecimal.ZERO); // 下掉booking fee
            // 初始化processing fee，pay by client开关检查，如果没有开启，则设置为0
            BigDecimal initProcessingFee = BigDecimal.valueOf(0);
            PaymentSettingDTO paymentSettingDTO = iPaymentSettingClient.getPaymentSetting(businessId);
            if (Objects.equals(
                    paymentSettingDTO.getProcessingFeePayBy(), PaymentSettingConst.PROCESSING_FEE_PAY_BY_CLIENT)) {
                // 计算 processing fee 基于 subTotal + serviceCharge - discountAmount + tax
                BigDecimal convenienceFeeBased = serviceAndServiceChargeAmount;
                if (result.getTaxAmount() != null) {
                    convenienceFeeBased = convenienceFeeBased.add(result.getTaxAmount());
                }
                initProcessingFee = iPaymentPaymentClient.getConvenienceFee(
                        businessId, convenienceFeeBased, PaymentStripeStatus.CARD_PAY);
            }
            result.setInitProcessingFee(initProcessingFee);

            String prepayGuid = CommonUtil.getUuid();
            if (Objects.equals(prepayType, OnlineBookingConst.PREPAY_TYPE_DEPOSIT)) {
                prepayGuid = DEPOSIT_PREFIX + prepayGuid;
            }
            result.setGuid(prepayGuid);
        }
        stringRedisTemplate
                .opsForValue()
                .set(buildPrepayKey(businessId, result.getGuid()), JsonUtil.toJson(result), Duration.ofHours(1));
        return result;
    }

    /**
     * 计算 prepay 价格，支持 dynamic deposit。
     *
     * <p> 这是为了支持 Red Dog 商家的定制需求做的一个非常临时的方案。
     */
    public PrepayAmountDTO calculateOBPrepayAmountForDynamicDeposit(Integer businessId, PrepayAmountParams params) {
        // Stripe Only检查
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(businessId));
        if (!Objects.equals(businessDto.getPrimaryPayType(), PaymentMethodEnum.CARD_PROCESSOR_TYPE_STRIPE)) {
            return new PrepayAmountDTO();
        }

        var services =
                listCustomizedService(businessDto.getCompanyId(), businessId, params.getStaffId(), params.getPetData());
        if (services.isEmpty()) {
            return new PrepayAmountDTO();
        }

        var amount =
                switch (getMainServiceItemType(services)) {
                    case BOARDING -> calculateBoarding(businessDto, params, services);
                    case DAYCARE -> calculateDaycare(businessDto, params, services);
                    case GROOMING -> calculateGrooming(businessDto, params, services);
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "Unsupported service type: " + getMainServiceItemType(services));
                };

        var result = new PrepayAmountDTO();
        result.setSubTotal(amount.serviceAmount().setScale(2, RoundingMode.HALF_UP));
        result.setTaxAmount(amount.taxAmount());
        result.setServiceChargeAmount(amount.serviceChargeAmount());
        result.setServiceChargeList(amount.serviceChargeList());
        result.setApplyServiceChargeList(amount.applyServiceChargeList());
        result.setDiscountAmount(amount.discountAmount());
        result.setUsedMembershipIds(amount.usedMembershipIds());

        // 计算Booking fee
        if (amount.serviceAndServiceChargeAmount().compareTo(BigDecimal.ZERO) >= 0) {
            // 计算 booking fee 基于 subTotal + serviceCharge - discountAmount
            result.setFee(BigDecimal.ZERO); // 下掉booking fee
            // 初始化processing fee，pay by client开关检查，如果没有开启，则设置为0
            BigDecimal initProcessingFee = BigDecimal.valueOf(0);
            PaymentSettingDTO paymentSettingDTO = iPaymentSettingClient.getPaymentSetting(businessId);
            if (Objects.equals(
                    paymentSettingDTO.getProcessingFeePayBy(), PaymentSettingConst.PROCESSING_FEE_PAY_BY_CLIENT)) {
                // 计算 processing fee 基于 subTotal + serviceCharge - discountAmount + tax
                BigDecimal convenienceFeeBased = amount.serviceAndServiceChargeAmount();
                if (result.getTaxAmount() != null) {
                    convenienceFeeBased = convenienceFeeBased.add(result.getTaxAmount());
                }
                initProcessingFee = iPaymentPaymentClient.getConvenienceFee(
                        businessId, convenienceFeeBased, PaymentStripeStatus.CARD_PAY);
            }
            result.setInitProcessingFee(initProcessingFee);
            result.setGuid(DEPOSIT_PREFIX + CommonUtil.getUuid());
        }
        stringRedisTemplate
                .opsForValue()
                .set(buildPrepayKey(businessId, result.getGuid()), JsonUtil.toJson(result), Duration.ofHours(1));
        return result;
    }

    private CalculatedPrepayAmount calculateGrooming(
            MoeBusinessDto businessDto,
            PrepayAmountParams params,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> services) {
        // 获取基本信息
        var customerId = Optional.ofNullable(params.getCustomerData())
                .map(BookOnlineCustomerParams::getCustomerId)
                .orElse(null);
        var bookOnline = mustGetBookOnline(businessDto.getId(), customerId);

        // 获取服务ID到服务的映射
        var serviceIdToService = services.stream()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .collect(Collectors.toMap(CustomizedServiceView::getId, Function.identity(), (o, n) -> o));

        // 计算美容服务金额
        return calculatePrepayAmount(
                bookOnline,
                ServiceItemType.GROOMING,
                params.getPetData(),
                serviceIdToService,
                params.getDiscountCode(),
                params.getStaffId(),
                customerId);
    }

    private CalculatedPrepayAmount calculateDaycare(
            MoeBusinessDto businessDto,
            PrepayAmountParams params,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> services) {
        // 获取基本信息
        var customerId = Optional.ofNullable(params.getCustomerData())
                .map(BookOnlineCustomerParams::getCustomerId)
                .orElse(null);
        var bookOnline = mustGetBookOnline(businessDto.getId(), customerId);

        // 获取服务ID到服务的映射
        var serviceIdToService = services.stream()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .collect(Collectors.toMap(CustomizedServiceView::getId, Function.identity(), (o, n) -> o));

        var totalAmount = calculatePrepayAmount(
                bookOnline,
                ServiceItemType.DAYCARE,
                params.getPetData(),
                serviceIdToService,
                params.getDiscountCode(),
                params.getStaffId(),
                customerId);

        var hasGroomingServices = services.stream()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .anyMatch(service -> service.getServiceItemType() == ServiceItemType.GROOMING);
        if (hasGroomingServices) {
            var groomingAmount = calculatePrepayAmount(
                    bookOnline,
                    ServiceItemType.GROOMING,
                    params.getPetData(),
                    serviceIdToService,
                    params.getDiscountCode(),
                    params.getStaffId(),
                    customerId);

            totalAmount = CalculatedPrepayAmount.add(totalAmount, groomingAmount);
        }

        return totalAmount;
    }

    private CalculatedPrepayAmount calculateBoarding(
            MoeBusinessDto businessDto,
            PrepayAmountParams params,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> services) {
        // 获取基本信息
        var customerId = Optional.ofNullable(params.getCustomerData())
                .map(BookOnlineCustomerParams::getCustomerId)
                .orElse(null);
        var bookOnline = mustGetBookOnline(businessDto.getId(), customerId);

        // 获取服务ID到服务的映射
        var serviceIdToService = services.stream()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .collect(Collectors.toMap(CustomizedServiceView::getId, Function.identity(), (o, n) -> o));

        // 同时 boarding 和 grooming 服务的情况
        var totalAmount = calculatePrepayAmount(
                bookOnline,
                ServiceItemType.BOARDING,
                params.getPetData(),
                serviceIdToService,
                params.getDiscountCode(),
                params.getStaffId(),
                customerId);

        // 检查是否同时有美容服务
        var hasGroomingServices = services.stream()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .anyMatch(service -> service.getServiceItemType() == ServiceItemType.GROOMING);
        if (hasGroomingServices) {
            var groomingAmount = calculatePrepayAmount(
                    bookOnline,
                    ServiceItemType.GROOMING,
                    params.getPetData(),
                    serviceIdToService,
                    params.getDiscountCode(),
                    params.getStaffId(),
                    customerId);

            totalAmount = CalculatedPrepayAmount.add(totalAmount, groomingAmount);
        }

        return totalAmount;
    }

    private CalculatedPrepayAmount calculatePrepayAmount(
            BookOnlineDTO bookOnline,
            ServiceItemType serviceType,
            List<BookOnlinePetParams> petData,
            Map<Long, CustomizedServiceView> serviceIdToService,
            String discountCode,
            Integer staffId,
            Integer customerId) {

        var paymentOption = getPaymentOption(bookOnline, serviceType);
        var builder = CalculatedPrepayAmount.builder();

        // 如果不是 prepay 类型，返回零金额
        if (!Objects.equals(paymentOption.getPaymentType(), PaymentType.PAYMENT_TYPE_PREPAY_VALUE)) {
            return builder.serviceAmount(BigDecimal.ZERO)
                    .serviceAndServiceChargeAmount(BigDecimal.ZERO)
                    .build();
        }

        var prepayOption = paymentOption.getPrePay();
        Byte prepayType = prepayOption.getPrepayType().byteValue();
        Byte depositType = prepayOption.getDepositType().byteValue();

        // 固定金额定金的情况
        if (Objects.equals(prepayType, OnlineBookingConst.PREPAY_TYPE_DEPOSIT)
                && Objects.equals(depositType, OnlineBookingConst.DEPOSIT_TYPE_BY_FIXED_AMOUNT)) {
            BigDecimal depositAmount = prepayOption.getDepositAmount();
            return builder.serviceAmount(depositAmount)
                    .serviceAndServiceChargeAmount(depositAmount)
                    .build();
        }

        // 需要计算服务价格的情况（全额付款或百分比定金）
        var filteredPetData = petData.stream()
                .filter(e -> {
                    var service = serviceIdToService.get(e.getServiceId().longValue());
                    return service != null && service.getServiceItemType() == serviceType;
                })
                .toList();

        // 如果没有该类型的服务，返回零金额
        if (filteredPetData.isEmpty()) {
            return builder.serviceAmount(BigDecimal.ZERO)
                    .serviceAndServiceChargeAmount(BigDecimal.ZERO)
                    .build();
        }

        // 计算服务金额
        CalculateServiceAmountDTO serviceAmountDTO = getServiceSubTotalAmount(
                bookOnline.getBusinessId(), filteredPetData, discountCode, staffId, customerId);

        BigDecimal serviceAmount = serviceAmountDTO.getServiceAmount();
        BigDecimal serviceChargeAmount = serviceAmountDTO.getServiceChargeAmount();
        BigDecimal serviceAndServiceChargeAmount;

        // 全额付款的情况
        if (Objects.equals(prepayType, OnlineBookingConst.PREPAY_TYPE_FULL_PAY)) {
            builder.taxAmount(serviceAmountDTO.getTaxAmount())
                    .serviceChargeAmount(serviceAmountDTO.getServiceChargeAmount())
                    .serviceChargeList(serviceAmountDTO.getServiceChargeList())
                    .applyServiceChargeList(serviceAmountDTO.getApplyServiceChargeList())
                    .discountAmount(serviceAmountDTO.getDiscountAmount())
                    .usedMembershipIds(serviceAmountDTO.getUsedMembershipIds());

            serviceAndServiceChargeAmount =
                    serviceAmount.add(serviceChargeAmount).subtract(serviceAmountDTO.getDiscountAmount());
        }
        // 百分比定金的情况
        else if (Objects.equals(depositType, OnlineBookingConst.DEPOSIT_TYPE_BY_PERCENTAGE)) {
            BigDecimal baseAmount = serviceAmount.add(serviceChargeAmount);
            BigDecimal percentage =
                    BigDecimal.valueOf(prepayOption.getDepositPercentage()).scaleByPowerOfTen(-2);
            serviceAmount = baseAmount.multiply(percentage);
            serviceAndServiceChargeAmount = serviceAmount;
        }
        // 其他情况
        else {
            serviceAndServiceChargeAmount = serviceAmount;
        }

        return builder.serviceAmount(serviceAmount)
                .serviceAndServiceChargeAmount(serviceAndServiceChargeAmount)
                .build();
    }

    @Builder(toBuilder = true)
    record CalculatedPrepayAmount(
            BigDecimal serviceAmount,
            BigDecimal serviceAndServiceChargeAmount,
            BigDecimal taxAmount,
            BigDecimal serviceChargeAmount,
            @Deprecated List<ServiceChargeDTO> serviceChargeList,
            List<ApplyServiceChargeDTO> applyServiceChargeList,
            BigDecimal discountAmount,
            List<Long> usedMembershipIds) {

        public static CalculatedPrepayAmount add(CalculatedPrepayAmount a, CalculatedPrepayAmount b) {
            return new CalculatedPrepayAmount(
                    addBigDecimals(a.serviceAmount, b.serviceAmount),
                    addBigDecimals(a.serviceAndServiceChargeAmount, b.serviceAndServiceChargeAmount),
                    addBigDecimals(a.taxAmount, b.taxAmount),
                    addBigDecimals(a.serviceChargeAmount, b.serviceChargeAmount),
                    mergeLists(a.serviceChargeList, b.serviceChargeList),
                    mergeLists(a.applyServiceChargeList, b.applyServiceChargeList),
                    addBigDecimals(a.discountAmount, b.discountAmount),
                    mergeLists(a.usedMembershipIds, b.usedMembershipIds));
        }

        private static BigDecimal addBigDecimals(@Nullable BigDecimal a, @Nullable BigDecimal b) {
            if (a == null && b == null) {
                return BigDecimal.ZERO;
            }
            if (a == null) {
                return b;
            }
            if (b == null) {
                return a;
            }
            return a.add(b);
        }

        private static <T> List<T> mergeLists(@Nullable List<T> a, @Nullable List<T> b) {
            if (a == null && b == null) {
                return List.of();
            }
            if (a == null) {
                return b;
            }
            if (b == null) {
                return a;
            }
            var result = new ArrayList<>(a);
            result.addAll(b);
            return result;
        }
    }

    private static BookOnlineDTO.PaymentOption getPaymentOption(
            BookOnlineDTO obSetting, ServiceItemType serviceItemType) {
        var paymentOptionMap = obSetting.getPaymentOptionMap();
        if (ObjectUtils.isEmpty(paymentOptionMap) || !paymentOptionMap.containsKey(serviceItemType.getNumber())) {
            return buildDefaultPaymentOption(obSetting);
        }
        return paymentOptionMap.get(serviceItemType.getNumber());
    }

    private static BookOnlineDTO.PaymentOption buildDefaultPaymentOption(BookOnlineDTO obSetting) {
        var result = new BookOnlineDTO.PaymentOption();
        result.setPaymentType(obSetting.getEnableNoShowFee().intValue());

        var prePay = new BookOnlineDTO.PaymentOption.PrePay();
        prePay.setPrepayType(obSetting.getPrepayType().intValue());
        prePay.setDepositType(obSetting.getDepositType().intValue());
        prePay.setDepositPercentage(obSetting.getDepositPercentage());
        prePay.setDepositAmount(obSetting.getDepositAmount());

        result.setPrePay(prePay);

        return result;
    }

    private static ServiceItemType getMainServiceItemType(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> services) {
        if (services.stream()
                .anyMatch(e -> e.getCustomizedService().getServiceItemType() == ServiceItemType.BOARDING)) {
            return ServiceItemType.BOARDING;
        }
        if (services.stream().anyMatch(e -> e.getCustomizedService().getServiceItemType() == ServiceItemType.DAYCARE)) {
            return ServiceItemType.DAYCARE;
        }
        if (services.stream()
                .anyMatch(e -> e.getCustomizedService().getServiceItemType() == ServiceItemType.GROOMING)) {
            return ServiceItemType.GROOMING;
        }
        if (services.stream()
                .anyMatch(e -> e.getCustomizedService().getServiceItemType() == ServiceItemType.DOG_WALKING)) {
            return ServiceItemType.DOG_WALKING;
        }
        if (services.stream()
                .anyMatch(e -> e.getCustomizedService().getServiceItemType() == ServiceItemType.EVALUATION)) {
            return ServiceItemType.EVALUATION;
        }
        throw bizException(
                Code.CODE_PARAMS_ERROR,
                "Unknown service type: "
                        + services.stream()
                                .map(e -> e.getCustomizedService().getServiceItemType())
                                .toList());
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            long companyId, int businessId, @Nullable Integer staffId, List<BookOnlinePetParams> petData) {

        var builder = BatchGetCustomizedServiceRequest.newBuilder();

        builder.setCompanyId(companyId);

        for (var petAndServiceList : buildPetToServicesMap(petData).entrySet()) {
            var pet = petAndServiceList.getKey();
            for (var serviceId : petAndServiceList.getValue()) {
                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(serviceId)
                        .setBusinessId(businessId);
                if (isNormal(pet.getPetId())) {
                    condBuilder.setPetId(pet.getPetId());
                }
                if (isNormal(staffId)) {
                    condBuilder.setStaffId(staffId);
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        for (var petAndAddonList : buildPetToAddonsMap(petData).entrySet()) {
            var pet = petAndAddonList.getKey();
            for (var addon : petAndAddonList.getValue()) {
                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(addon.getId())
                        .setBusinessId(businessId);
                if (isNormal(pet.getPetId())) {
                    condBuilder.setPetId(pet.getPetId());
                }
                if (isNormal(staffId)) {
                    condBuilder.setStaffId(staffId);
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        if (builder.getQueryConditionListList().isEmpty()) {
            return List.of();
        }

        return serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();
    }

    private static IdentityHashMap<BookOnlinePetParams, List<Integer>> buildPetToServicesMap(
            List<BookOnlinePetParams> petData) {
        var petToServices = new IdentityHashMap<BookOnlinePetParams, List<Integer>>();
        for (var pet : petData) {
            var services = petToServices.computeIfAbsent(pet, k -> new ArrayList<>());
            if (isNormal(pet.getServiceId())) {
                services.add(pet.getServiceId());
            }
        }
        return petToServices;
    }

    private static IdentityHashMap<BookOnlinePetParams, List<BookOnlinePetParams.Addon>> buildPetToAddonsMap(
            List<BookOnlinePetParams> petData) {

        var petToAddons = new IdentityHashMap<BookOnlinePetParams, List<BookOnlinePetParams.Addon>>();

        for (var pet : petData) {
            var addons = petToAddons.computeIfAbsent(pet, k -> new ArrayList<>());
            if (pet.getAddons() != null) { // 优先使用新字段，如果新字段为空，向后兼容使用旧字段
                addons.addAll(pet.getAddons());
            } else if (pet.getAddOnIds() != null) {
                for (var addOnId : pet.getAddOnIds()) {
                    var addon = new BookOnlinePetParams.Addon();
                    addon.setId(addOnId);
                    addons.add(addon);
                }
            }
        }

        return petToAddons;
    }

    private BookOnlineDTO mustGetBookOnline(Integer businessId, Integer customerId) {
        MoeBusinessBookOnline obSetting = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
        if (obSetting == null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "OB setting not found for business: " + businessId);
        }

        if (obSetting.getGroupPaymentType() != null
                && obSetting.getAcceptClient() != null
                && PaymentType.PAYMENT_TYPE_PREPAY.getNumber() == obSetting.getGroupPaymentType()) {
            BookOnlinePaymentGroupSettingDTO groupSettingDTO = checkGroupPaymentTypeForClient(obSetting, customerId);
            if (groupSettingDTO != null) {
                // cover prepay param
                obSetting.setPrepayType(groupSettingDTO.getPrepayType());
                obSetting.setPrepayPolicy(groupSettingDTO.getPrepayPolicy());
                obSetting.setPrepayTipEnable(groupSettingDTO.getPrepayTipEnable());
                obSetting.setDepositType(groupSettingDTO.getDepositType());
                obSetting.setDepositAmount(groupSettingDTO.getDepositAmount());
                obSetting.setDepositPercentage(groupSettingDTO.getDepositPercentage());
            }
        }
        return OBSettingMapper.INSTANCE.entity2DTO(obSetting);
    }

    private static String buildPrepayKey(Integer businessId, String guid) {
        return OnlineBookingConst.PREPAY_GUID_PREFIX + businessId + ":" + guid;
    }

    public PrepayAmountDTO getOBPrepayAmount(Integer businessId, String guid) {
        String value = stringRedisTemplate.opsForValue().get(buildPrepayKey(businessId, guid));
        if (StringUtils.hasText(value)) {
            return JsonUtil.toBean(value, PrepayAmountDTO.class);
        }
        return null;
    }

    public PreAuthAmountDTO calculateOBPreAuthAmount(Integer businessId, PreAuthAmountParams params) {
        PreAuthAmountDTO result = new PreAuthAmountDTO();
        // Stripe Only检查
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(businessId));
        if (!Objects.equals(businessDto.getPrimaryPayType(), PaymentMethodEnum.CARD_PROCESSOR_TYPE_STRIPE)) {
            return result;
        }
        Integer customerId = Optional.ofNullable(params.getCustomerData())
                .map(BookOnlineCustomerParams::getCustomerId)
                .orElse(null);
        CalculateServiceAmountDTO serviceAmountDTO = getServiceSubTotalAmount(
                businessId, params.getPetData(), params.getDiscountCode(), params.getStaffId(), customerId);
        BigDecimal serviceAmount = serviceAmountDTO.getServiceAmount();
        BigDecimal serviceChargeAmount = serviceAmountDTO.getServiceChargeAmount();
        BigDecimal taxAmount = serviceAmountDTO.getTaxAmount();
        result.setDiscountAmount(serviceAmountDTO.getDiscountAmount());
        result.setTaxAmount(taxAmount.setScale(2, RoundingMode.HALF_UP));
        result.setUsedMembershipIds(serviceAmountDTO.getUsedMembershipIds());
        BigDecimal serviceAndServiceChargeAmount =
                serviceAmount.add(serviceChargeAmount).subtract(serviceAmountDTO.getDiscountAmount());

        result.setServiceTotal(serviceAmount.setScale(2, RoundingMode.HALF_UP));
        result.setServiceChargeAmount(serviceChargeAmount.setScale(2, RoundingMode.HALF_UP));
        result.setServiceChargeList(serviceAmountDTO.getServiceChargeList());
        result.setApplyServiceChargeList(serviceAmountDTO.getApplyServiceChargeList());

        // 计算Booking fee
        if (serviceAndServiceChargeAmount.compareTo(BigDecimal.ZERO) >= 0) {
            // 计算 booking fee 基于 subTotal + serviceCharge - discountAmount
            result.setBookingFee(BigDecimal.ZERO); // 下掉booking fee
            // 初始化processing fee，pay by client开关检查，如果没有开启，则设置为0
            BigDecimal initProcessingFee = BigDecimal.valueOf(0);
            PaymentSettingDTO paymentSettingDTO = iPaymentSettingClient.getPaymentSetting(businessId);
            if (Objects.equals(
                    paymentSettingDTO.getProcessingFeePayBy(), PaymentSettingConst.PROCESSING_FEE_PAY_BY_CLIENT)) {
                // 计算 processing fee 基于 subTotal + serviceCharge - discountAmount + tax
                BigDecimal convenienceFeeBased = serviceAndServiceChargeAmount;
                if (result.getTaxAmount() != null) {
                    convenienceFeeBased = convenienceFeeBased.add(result.getTaxAmount());
                }
                initProcessingFee = iPaymentPaymentClient.getConvenienceFee(
                        businessId, convenienceFeeBased, PaymentStripeStatus.CARD_PAY);
            }
            result.setInitProcessingFee(initProcessingFee);
        }
        return result;
    }

    /**
     * 查询服务总价、tax总价，如有saved price，使用saved price计算
     *
     * @param businessId
     * @param
     * @return
     */
    public CalculateServiceAmountDTO getServiceSubTotalAmount(
            Integer businessId,
            List<BookOnlinePetParams> petData,
            String discountCode,
            @Nullable Integer staffId,
            @Nullable Integer customerId) {
        return getServiceSubTotalAmountByNew(businessId, petData, discountCode, staffId, customerId);
    }

    private CalculateServiceAmountDTO getServiceSubTotalAmountByNew(
            Integer businessId,
            List<BookOnlinePetParams> petData,
            String discountCode,
            @Nullable Integer staffId,
            @Nullable Integer customerId) {
        return groomingServiceService.getCustomizedServicePriceAndTax(
                businessId, petData, discountCode, staffId, customerId);
    }

    /**
     * OB提交参数检查、预约时间是否冲突检查
     * 不满足直接抛出异常
     *
     * @param obSettings   商家OB设置
     * @param submitParams 提交参数
     */
    public void submitParamsCheck(MoeBusinessBookOnline obSettings, BookOnlineSubmitParams submitParams) {
        checkApplicableService(obSettings, submitParams);

        // check config
        checkObConfig(obSettings, submitParams);
        checkAgreementNumber(obSettings, submitParams);
        checkCustomerType(submitParams);
        checkCancelPolicyAndPayType(submitParams, obSettings);

        if (submitParams.getAppointmentStartTime() == null) {
            submitParams.setNoStartTime(true);
        }
        // check auto assign
        if (OBGroomingService.canAutoAssign(obSettings, submitParams)) {
            obGroomingService.doAssign(
                    submitParams, submitParams.getCustomerData().getCustomerId(), null);
        }

        // 校验预约时间
        verifyAppointmentTimeIsAvailable(submitParams, obSettings);
        // preauth card check
        checkPreAuthCardInfo(submitParams);
        // check discount code
        checkDiscountCode(submitParams);
    }

    private void checkApplicableService(MoeBusinessBookOnline obSettings, BookOnlineSubmitParams submitParams) {
        // true - exist pet
        // false - new pet
        Map<Boolean, List<BookOnlinePetParams>> petDataMap = submitParams.getPetData().stream()
                .collect(Collectors.partitioningBy(param -> Objects.nonNull(param.getPetId())));

        Long companyId = obSettings.getCompanyId();
        Integer businessId = obSettings.getBusinessId();

        checkExistPets(
                petDataMap.get(Boolean.TRUE),
                businessId,
                companyId,
                submitParams.getCustomerData().getCustomerId());
        checkNewPets(petDataMap.get(Boolean.FALSE), businessId, companyId);
    }

    private void checkNewPets(List<BookOnlinePetParams> petDataMap, Integer businessId, Long companyId) {
        if (CollectionUtils.isEmpty(petDataMap)) {
            return;
        }
        var petSizeList = obServiceService.getPetSizeList(companyId);
        var petCoatList = obServiceService.getPetCoatTypeList(companyId);
        // new pet -> Pair<serviceIdList, addOnIdList>
        Map<ServiceFilterByPet, Pair<List<Integer>, List<Integer>>> petServiceMap = new HashMap<>();
        // new pet, then get pet filter
        petDataMap.forEach(param -> {
            List<Integer> serviceIds = new ArrayList<>();
            serviceIds.add(param.getServiceId());
            List<Integer> addOnIds =
                    new ArrayList<>(Optional.ofNullable(param.getAddOnIds()).orElse(Collections.emptyList()));

            BusinessCustomerPetModel.Builder builder = BusinessCustomerPetModel.newBuilder()
                    .setPetType(PetType.forNumber(param.getPetTypeId()))
                    .setBreed(param.getBreed())
                    .setWeight(StringUtils.hasText(param.getWeight()) ? param.getWeight() : "")
                    .setCoatType(StringUtils.hasText(param.getHairLength()) ? param.getHairLength() : "");
            ServiceFilterByPet petFilter = obServiceService.getPetFilter(builder.build(), petSizeList, petCoatList);

            petServiceMap
                    .computeIfAbsent(petFilter, k -> Pair.of(new ArrayList<>(), new ArrayList<>()))
                    .getFirst()
                    .addAll(serviceIds);
            petServiceMap.get(petFilter).getSecond().addAll(addOnIds);
        });
        // check new pet
        obServiceService.checkApplicableServiceByPet(petServiceMap, businessId, companyId);
    }

    private void checkExistPets(
            List<BookOnlinePetParams> petDataMap, Integer businessId, Long companyId, Integer customerId) {
        if (CollectionUtils.isEmpty(petDataMap)) {
            return;
        }

        List<Integer> petIds = petDataMap.stream()
                .map(BookOnlinePetParams::getPetId)
                .distinct()
                .toList();

        // petId -> pet
        Map<Integer, ServiceFilterByPet> petFilterMap =
                obServiceService.getPetFilters(companyId, businessId, petIds, customerId);

        // exist pet -> Pair<serviceIdList, addOnIdList>
        Map<ServiceFilterByPet, Pair<List<Integer>, List<Integer>>> petServiceMap = new HashMap<>();
        // exist pet
        petDataMap.forEach(param -> {
            List<Integer> serviceIds = new ArrayList<>();
            serviceIds.add(param.getServiceId());
            List<Integer> addOnIds =
                    new ArrayList<>(Optional.ofNullable(param.getAddOnIds()).orElse(Collections.emptyList()));

            ServiceFilterByPet petFilter = petFilterMap.get(param.getPetId());
            if (Objects.isNull(petFilter)) {
                throw bizException(Code.CODE_PARAMS_ERROR, "Pet is not exist.");
            }

            petServiceMap
                    .computeIfAbsent(petFilter, k -> Pair.of(new ArrayList<>(), new ArrayList<>()))
                    .getFirst()
                    .addAll(serviceIds);
            petServiceMap.get(petFilter).getSecond().addAll(addOnIds);
        });

        // check exist pet
        obServiceService.checkApplicableServiceByPet(petServiceMap, businessId, companyId);
    }

    private void checkDiscountCode(BookOnlineSubmitParams submitParams) {
        DiscountCodeParams discountCodeParams = submitParams.getDiscountCodeParams();
        if (Objects.isNull(discountCodeParams)) {
            return;
        }

        String discountCode = discountCodeParams.getDiscountCode();
        if (!StringUtils.hasText(discountCode)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Discount code is empty.");
        }

        int customerId = 0;
        if (Objects.nonNull(submitParams.getCustomerData().getCustomerId())) {
            customerId = submitParams.getCustomerData().getCustomerId();
        }

        List<Long> serviceAndAddonIdList = submitParams.getPetData().stream()
                .flatMap(petData -> Stream.concat(
                        Stream.of(petData.getServiceId()),
                        Optional.ofNullable(petData.getAddOnIds()).stream().flatMap(Collection::stream)))
                .distinct()
                .map(Long::valueOf)
                .toList();

        CheckDiscountCodeValidForCustomerInput.Builder builder = CheckDiscountCodeValidForCustomerInput.newBuilder()
                .setCodeName(discountCode)
                .addAllServiceIds(serviceAndAddonIdList)
                .setCustomerId(customerId)
                .setBusinessId(submitParams.getBusinessId());
        if (StringUtils.hasText(submitParams.getAppointmentDate())) {
            builder.setAppointmentDate(submitParams.getAppointmentDate());
        }

        CheckDiscountCodeValidForCustomerOutput result =
                discountCodeClient.checkDiscountCodeValidForCustomer(builder.build());
        if (Objects.isNull(result)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Discount code is invalid.");
        }
    }

    private void checkPreAuthCardInfo(BookOnlineSubmitParams submitParams) {
        PreAuthDetailParams preAuthDetail = submitParams.getPreAuthDetail();
        if (preAuthDetail != null
                && !StringUtils.hasText(preAuthDetail.getPaymentMethodId())
                && !StringUtils.hasText(preAuthDetail.getChargeToken())) {
            throw new CommonException(ResponseCodeEnum.STRIPE_CARD_TOKEN_IS_EMPTY);
        }
    }

    /**
     * 对定金发起退款
     * 由原来的退款改为取消paymentIntent
     *
     * @param deGuid 定金guid
     */
    public void createRefundForOBDeposit(String deGuid) {
        MoeBookOnlineDeposit obDeposit = moeBookOnlineDepositService.getOBDepositByDeGuid(deGuid);

        if (obDeposit == null) {
            return;
        }
        CreateRefundByPaymentIdParams refundParams = new CreateRefundByPaymentIdParams();
        refundParams.setPaymentId(obDeposit.getPaymentId());
        refundParams.setReason("Online Booking request failed to submit, deposit cancel.");
        refundParams.setBookingFee(obDeposit.getBookingFee());
        iPaymentRefundClient.createRefundByPaymentId(obDeposit.getBusinessId(), refundParams);

        // 更新deposit状态
        obDeposit.setStatus(BookOnlineDepositConst.CANCEL);
        moeBookOnlineDepositService.update(obDeposit);
    }

    /**
     * 校验预约时间是否可用
     * 有两种情况不需要检验时间
     * 1. 商家选择了服务范围，客户预约的地址不再服务范围内
     * 2. 商家选择了disable select time option
     *
     * @param bookOnlineSubmitParams
     */
    private void verifyAppointmentTimeIsAvailable(
            BookOnlineSubmitParams bookOnlineSubmitParams, MoeBusinessBookOnline businessBookOnline) {
        String date = bookOnlineSubmitParams.getAppointmentDate();
        String timeZone = companyHelper.getCompanyTimeZoneName(bookOnlineSubmitParams.getCompanyId());

        // see https://moego.atlassian.net/jira/software/c/projects/ERP/issues/ERP-8596
        if (StringUtils.hasText(date) && LocalDate.parse(date).isBefore(LocalDate.now(ZoneId.of(timeZone)))) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Appointment date is before today.");
        }

        if (Objects.isNull(date) && Objects.isNull(bookOnlineSubmitParams.getAppointmentStartTime())) {
            return;
        }
        // not select time，不需要校验
        if (bookOnlineSubmitParams.getAppointmentStartTime() == null) {
            return;
        }
        if (bookOnlineSubmitParams.getStaffId() != null) {
            List<MoeStaffDto> staffList = businessStaffService.getOBAvailableStaffList(
                    businessBookOnline.getBusinessId(), businessBookOnline.getAvailableTimeType());
            if (staffList.stream()
                    .noneMatch(staff -> Objects.equals(staff.getId(), bookOnlineSubmitParams.getStaffId()))) {
                log.error("verifyAppointmentTimeIsAvailable, staff is not available.");
                throw bizException(
                        Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                        ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
            }
        }
        // 根据available time type决定校验方式：0. working hour 1. slot 2. disable
        switch (businessBookOnline.getAvailableTimeType()) {
            case OnlineBookingConst.AVAILABLE_TIME_TYPE_WORKING_HOUR -> verifyAppointmentTimeByWorkingHourIsAvailable(
                    bookOnlineSubmitParams);
            case OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT -> verifyAppointmentTimeBySlotIsAvailable(
                    bookOnlineSubmitParams);
            case OnlineBookingConst.AVAILABLE_TIME_TYPE_DISABLE -> {
                // do nothing
            }
        }
    }

    /**
     * 校验预约时间是否可用 (type: working hour)
     *
     * @param obParams
     */
    private void verifyAppointmentTimeByWorkingHourIsAvailable(BookOnlineSubmitParams obParams) {
        Integer businessId = obParams.getBusinessId();
        if (Objects.isNull(obParams.getAppointmentDate())) {
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }
        // 没有选择 staff，不需要校验
        if (obParams.getStaffId() == null) {
            return;
        }
        // 统计 service id list，并查询出 service list
        List<Integer> serviceIdList = new ArrayList<>();
        for (BookOnlinePetParams petParam : obParams.getPetData()) {
            // 只关心已选择的宠物（前端会将当前用户下所有宠物信息一起提交，后期会优化）
            if (petParam.getIsSelected()) {
                if (!PrimitiveTypeUtil.isNullOrZero(petParam.getServiceId())) {
                    serviceIdList.add(petParam.getServiceId());
                }
                if (!CollectionUtils.isEmpty(petParam.getAddOnIds())) {
                    serviceIdList.addAll(petParam.getAddOnIds());
                }
            }
        }
        // 根据用户选择的 service id 列表，查询服务
        List<MoeGroomingServiceDTO> serviceList =
                groomingServiceService.getServicesByServiceIds(businessId, serviceIdList);

        // 获取商家buffer time配置
        Integer bufferTime = smartSchedulerService.getSmartScheduleBufferTime(
                obParams.getCompanyId().intValue(), businessId);

        // 统计预约服务总耗时
        int totalDuration = 0;
        Integer customerId = obParams.getCustomerData().getCustomerId();
        // 如果不存在 customerId 说明是新用户，查询服务列表计算耗时即可
        if (PrimitiveTypeUtil.isNullOrZero(customerId)) {
            for (MoeGroomingServiceDTO moeGroomingServiceDTO : serviceList) {
                totalDuration += moeGroomingServiceDTO.getDuration();
            }
        } else {
            // 如果存在老用户，需要查询客户定制的服务（支持为每一个客户定制特殊的服务），来计算耗时
            totalDuration = calculateTotalDurationForExistingClient(obParams, serviceList);
        }

        // 计算预约时间周期，date only 允许不传 startTime
        Integer apptStartTime = obParams.getAppointmentStartTime();
        int finalTotalDuration = totalDuration;
        Integer apptEndTime = Optional.ofNullable(apptStartTime)
                .map(startTime -> startTime + finalTotalDuration)
                .orElse(null);

        // 根据日期和员工id查询员工当天的预约
        List<MoeGroomingPetDetail> moeGroomingPetDetails =
                moePetDetailService.queryPetDetailByAppointmentDateAndStaffId(
                        businessId, obParams.getAppointmentDate(), obParams.getStaffId());

        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingServiceIdList(
                        businessId,
                        moeGroomingPetDetails.stream()
                                .map(MoeGroomingPetDetail::getId)
                                .toList());

        // 判断预约时间和已存在预约是否冲突
        for (MoeGroomingPetDetail moeGroomingPetDetail : moeGroomingPetDetails) {
            int start = 0, end = 0;

            if (!CollectionUtils.isEmpty(operationMap) && operationMap.containsKey(moeGroomingPetDetail.getId())) {
                // 如果存在 operation, 则使用 operation 的时间
                for (GroomingServiceOperationDTO operation : operationMap.get(moeGroomingPetDetail.getId())) {
                    if (Objects.equals(obParams.getStaffId(), operation.getStaffId())) {
                        start = operation.getStartTime();
                        end = start + operation.getDuration();
                        break;
                    }
                }
            } else {
                start = moeGroomingPetDetail.getStartTime().intValue();
                end = start + moeGroomingPetDetail.getServiceTime();
            }

            // 待预约时间和已存在预约出现交集，则冲突，预约时间不可用
            if (!isNotConflict(apptStartTime, apptEndTime, start, end, bufferTime)) {
                throw new CommonException(ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE);
            }
        }

        // 判断预约时间是否和 block 冲突 fix ERP-1437
        Set<String> targetDateSet =
                Stream.of(obParams.getAppointmentDate()).collect(Collectors.toCollection(HashSet::new));
        Set<Integer> targetStaffIdSet = Stream.of(obParams.getStaffId()).collect(Collectors.toCollection(HashSet::new));

        List<StaffBlockInfoDTO> staffBlockList = moeGroomingAppointmentService.queryBlockListByDatesAndStaffIds(
                businessId, targetDateSet, targetStaffIdSet);
        if (!CollectionUtils.isEmpty(staffBlockList)) {
            for (StaffBlockInfoDTO staffBlock : staffBlockList) {
                // 待预约时间和 block 出现交集，则冲突，预约时间不可用
                if (!isNotConflict(
                        apptStartTime, apptEndTime, staffBlock.getStartTime(), staffBlock.getEndTime(), bufferTime)) {
                    throw new CommonException(ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE);
                }
            }
        }
    }

    private int calculateTotalDurationForExistingClient(
            BookOnlineSubmitParams obParams, List<MoeGroomingServiceDTO> serviceList) {
        var serviceIdToInfoMap = serviceList.stream()
                .collect(Collectors.toMap(MoeGroomingServiceDTO::getId, Function.identity(), (o, n) -> o));
        var customizedServiceList = obGroomingService.listCustomizedService(obParams.getCompanyId(), obParams);

        return obParams.getPetData().stream()
                .mapToInt(petParam -> {
                    var customizedService = ServiceUtil.getCustomizedService(
                            customizedServiceList, petParam.getPetId(), petParam.getServiceId(), obParams.getStaffId());

                    return customizedService != null
                            ? customizedService.getDuration()
                            : serviceIdToInfoMap.get(petParam.getServiceId()).getDuration();
                })
                .sum();
    }

    /**
     * compareStart 到 compareEnd 与 start 到 end 不发生重合，说明不发生冲突
     *
     * @param compareStart
     * @param compareEnd
     * @param start
     * @param end
     */
    public static boolean isNotConflict(Integer compareStart, Integer compareEnd, int start, int end, int bufferTime) {
        if (compareStart == null || compareEnd == null) {
            return true;
        }
        // before start
        // bufferTime > 0, 当前compareEnd（当前预约的结束时间）至少和start(下一预约的开始时间）间隔一个bufferTime
        if (compareStart <= compareEnd && (bufferTime > 0 ? compareEnd + bufferTime : compareEnd) <= start) {
            return true;
        }
        // after end
        // 如果 bufferTime < 0，则compareStart（当前预约的开始时间）允许和end(上一预约的结束时间)部分重合
        return compareStart <= compareEnd && (bufferTime < 0 ? compareStart - bufferTime : compareStart) >= end;
    }

    /**
     * 校验预约时间是否可用 (type: slot)
     *
     * @param obParams 待提交信息
     */
    private void verifyAppointmentTimeBySlotIsAvailable(BookOnlineSubmitParams obParams) {
        if (Objects.isNull(obParams.getAppointmentDate())) {
            log.error("Appointment date is empty.");
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }
        // 判断预约时间是否和 block 冲突 fix ERP-1437
        isConflictWithStaffBlock(obParams);

        // 没有选择 staff，不需要校验
        if (obParams.getStaffId() == null) {
            return;
        }

        // 获取business的slot配置
        Map<Integer, BookOnlineStaffTimeDTO> staffTimeDTOMap = staffTimeSyncService.queryStaffTime(
                obParams.getBusinessId(), Collections.singletonList(obParams.getStaffId()));
        // 校验时，如果查不到相关配置，直接抛出异常
        if (staffTimeDTOMap == null || staffTimeDTOMap.get(obParams.getStaffId()) == null) {
            log.error("verifyAppointmentTimeBySlotIsAvailable, book online staff time could not be found.");
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }

        // 解析slot 配置，获取 capacity
        Map<String, BookOnlineStaffTimeDTO.StaffSlot> slotsMap =
                staffTimeDTOMap.get(obParams.getStaffId()).getStaffSlots();
        String dayOfWeek = WeekUtil.getKeyForWeekDay(obParams.getAppointmentDate());
        var timeslotDTO = slotsMap.get(dayOfWeek);

        if (!timeslotDTO.getIsSelected()) {
            log.error("verifyAppointmentTimeBySlotIsAvailable, staff time is not selected.");
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }

        int capacity = 0;
        for (var capacityTimeslotDTO : timeslotDTO.getTimeSlot()) {
            if (capacityTimeslotDTO.getStartTime().equals(obParams.getAppointmentStartTime())) {
                capacity = capacityTimeslotDTO.getCapacity();
                break;
            }
        }

        // 查询所选时间的appt pet数
        Set<String> dateSet = new HashSet<>();
        dateSet.add(obParams.getAppointmentDate());
        Collection<StaffTimeslotPetCountDTO> petCounts = moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                obParams.getBusinessId(), dateSet, null, Set.of(obParams.getStaffId()), null);

        // 校验宠物数量是否超出上限
        boolean isSlotCapacityByReservation = featureFlagApi.isOn(
                FeatureFlags.BOOK_BY_SLOT_WITH_RESERVATION,
                FeatureFlagContext.builder().company(obParams.getCompanyId()).build());
        int slotOccupiedCount = 0;
        if (!CollectionUtils.isEmpty(petCounts)) {
            for (StaffTimeslotPetCountDTO petCount : petCounts) {
                // if (petCount.getStaffId().equals(obParams.getStaffId())) {
                if (Objects.equals(petCount.getStaffId(), obParams.getStaffId())
                        && Objects.equals(petCount.getSlotTime(), obParams.getAppointmentStartTime())) {
                    slotOccupiedCount += obClientTimeSlotService.getSlotCapacityToOccupy(
                            isSlotCapacityByReservation, petCount.getPetCount(), petCount.getAppointmentCount());
                }
            }
        }

        int selectedPetNum = 0;
        for (BookOnlinePetParams pet : obParams.getPetData()) {
            if (pet.getIsSelected()) {
                selectedPetNum += 1;
            }
        }
        int slotsNeedToOccupy =
                obClientTimeSlotService.getSlotCapacityToOccupy(isSlotCapacityByReservation, selectedPetNum);

        if (slotOccupiedCount + slotsNeedToOccupy > capacity) {
            log.error(
                    "verifyAppointmentTimeBySlotIsAvailable, slot capacity is not enough. slotOccupiedCount: {}, slotsNeedToOccupy: {}, capacity: {}",
                    slotOccupiedCount,
                    slotsNeedToOccupy,
                    capacity);
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }
    }

    /**
     * 判断预约时间是否与 staff block 冲突
     *
     * @param obParams
     */
    public void isConflictWithStaffBlock(BookOnlineSubmitParams obParams) {
        if (Objects.isNull(obParams.getAppointmentDate())
                || PrimitiveTypeUtil.isNullOrZero(obParams.getStaffId())
                || PrimitiveTypeUtil.isNullOrZero(obParams.getAppointmentStartTime())) {
            return;
        }

        Set<String> targetDateSet =
                Stream.of(obParams.getAppointmentDate()).collect(Collectors.toCollection(HashSet::new));
        Set<Integer> targetStaffIdSet = Stream.of(obParams.getStaffId()).collect(Collectors.toCollection(HashSet::new));

        List<StaffBlockInfoDTO> staffBlockList = moeGroomingAppointmentService.queryBlockListByDatesAndStaffIds(
                obParams.getBusinessId(), targetDateSet, targetStaffIdSet);
        if (!CollectionUtils.isEmpty(staffBlockList)) {
            for (StaffBlockInfoDTO staffBlock : staffBlockList) {
                // 待预约时间和 block 出现交集，则冲突，预约时间不可用
                if (obParams.getAppointmentStartTime() >= staffBlock.getStartTime()
                        && obParams.getAppointmentStartTime() < staffBlock.getEndTime()) {
                    throw bizException(
                            Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                            ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
                }
            }
        }
    }

    public void saveAgreementForObSubmit(List<BookOnlineAgreementParams> agreements, Integer targetId) {
        MoeGroomingAppointment appointment = Optional.ofNullable(appointmentMapper.selectByPrimaryKey(targetId))
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Appointment not found: " + targetId));
        if (!ObjectUtils.isEmpty(agreements)) {
            agreements.forEach(agree -> {
                AddCustomerAgreementParams customerAgreementVo = new AddCustomerAgreementParams();
                customerAgreementVo.setAgreementId(agree.getAgreementId());
                customerAgreementVo.setSignature(agree.getSignature());
                customerAgreementVo.setBusinessId(appointment.getBusinessId());
                customerAgreementVo.setCustomerId(appointment.getCustomerId());
                customerAgreementVo.setIsFormBookOnline(true);
                addCustomerAgreement(customerAgreementVo, targetId);
            });
        }
    }

    public void saveAgreementForBookingRequest(List<BookOnlineAgreementParams> agreements, long bookingRequestId) {
        GetBookingRequestResponse resp = bookingRequestStub.getBookingRequest(
                GetBookingRequestRequest.newBuilder().setId(bookingRequestId).build());
        if (!resp.hasBookingRequest()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + bookingRequestId);
        }
        BookingRequestModel bookingRequest = resp.getBookingRequest();
        if (!ObjectUtils.isEmpty(agreements)) {
            agreements.forEach(agree -> {
                AddCustomerAgreementParams customerAgreementVo = new AddCustomerAgreementParams();
                customerAgreementVo.setAgreementId(agree.getAgreementId());
                customerAgreementVo.setSignature(agree.getSignature());
                customerAgreementVo.setBusinessId((int) bookingRequest.getBusinessId());
                customerAgreementVo.setCustomerId((int) bookingRequest.getCustomerId());
                customerAgreementVo.setIsFormBookOnline(true);
                addCustomerAgreement(customerAgreementVo, (int) bookingRequestId);
            });
        }
    }

    public ResponseResult<AddResultDTO> addCustomerAgreement(AddCustomerAgreementParams params, Integer targetId) {
        String url = uploadSignature(params.getBusinessId(), params.getSignature());
        SignAgreementRequest.Builder builder = SignAgreementRequest.newBuilder();
        builder.setBusinessId(params.getBusinessId());
        builder.setCustomerId(params.getCustomerId());
        builder.setAgreementId(params.getAgreementId());
        builder.setSignature(url);
        if (targetId != null) {
            builder.setTargetId(targetId);
        }
        if (params.getIsFormBookOnline()) {
            builder.setServiceTypes(
                    ServiceType.SERVICE_TYPE_GROOMING_VALUE | ServiceType.SERVICE_TYPE_ONLINE_BOOKING_VALUE);
            builder.setSourceType(SourceType.SOURCE_TYPE_ONLINE_BOOKING);
        } else if (params.getIsFormIntakeForm()) {
            builder.setServiceTypes(
                    ServiceType.SERVICE_TYPE_GROOMING_VALUE | ServiceType.SERVICE_TYPE_INTAKE_FORM_VALUE);
            builder.setSourceType(SourceType.SOURCE_TYPE_INTAKE_FORM);
        }

        AgreementRecordSimpleView agreementRecord = agreementRecordClient.signAgreement(builder.build());

        if (!params.getIsFormBookOnline() && !params.getIsFormIntakeForm()) {
            ThreadPool.execute(() -> notifyForAgreement(
                    (int) agreementRecord.getBusinessId(),
                    (int) agreementRecord.getCustomerId(),
                    (int) agreementRecord.getId(),
                    agreementRecord.getAgreementTitle(),
                    agreementRecord.getServiceTypes(),
                    targetId));
        }

        AddResultDTO addResultDTO = new AddResultDTO();
        addResultDTO.setResult(true);
        addResultDTO.setId((int) agreementRecord.getId());
        return ResponseResult.success(addResultDTO);
    }

    private String uploadSignature(Integer businessId, String signature) {
        try {
            String sig;
            String mimeType;
            String imageSuffix;
            if (signature.startsWith("data:image/png;base64,")) {
                sig = signature.substring("data:image/png;base64,".length());
                mimeType = MediaType.IMAGE_PNG_VALUE;
                imageSuffix = ".png";
            } else if (signature.startsWith("data:image/jpg;base64,")) {
                sig = signature.substring("data:image/jpg;base64,".length());
                mimeType = MediaType.IMAGE_JPEG_VALUE;
                imageSuffix = ".jpg";
            } else if (signature.startsWith("http")) {
                return signature;
            } else {
                log.error("signature data format invalid: {}", signature);
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "signature format invalid");
            }
            byte[] data = Base64.getDecoder().decode(sig);
            String name = md5(businessId.toString().getBytes(StandardCharsets.UTF_8)) + "/" + md5(data) + imageSuffix;
            String key = s3PublicSignaturePrefix + name;

            if (s3Client.isExisting(s3PublicBucket, key)) {
                log.info("signature already uploaded, businessId: {}, name: {}", businessId, name);
            } else {
                return s3Client.upload(data, s3PublicBucket, key, mimeType, null);
            }

            return s3PublicDomain + key;
        } catch (Exception e) {
            log.error("upload signature failed, businessId: " + businessId + ", signature: " + signature, e);
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR, "upload signature failed", e);
        }
    }

    private static String md5(byte[] bytes) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(bytes);
        return Hex.encodeHexString(md.digest());
    }

    private void notifyForAgreement(
            Integer businessId,
            Integer customerId,
            Integer agreementRecordId,
            String agreementTitle,
            Integer serviceType,
            Integer targetId) {

        // 调用通知发送
        NotificationAgreementSignedParams agreementSignedParams = new NotificationAgreementSignedParams();
        agreementSignedParams.setBusinessId(businessId);

        // 给前端的数据体
        NotificationExtraAgreementSignedDto agreementSignedDto = new NotificationExtraAgreementSignedDto();
        agreementSignedDto.setCustomerId(customerId);
        agreementSignedDto.setAgreementHeader(agreementTitle);
        agreementSignedDto.setSignId(agreementRecordId);
        // customer firstName 和 lastName 在notification模块组装
        agreementSignedParams.setWebPushDto(agreementSignedDto);
        if (SERVICE_TYPE_GROOMING_VALUE == (SERVICE_TYPE_GROOMING_VALUE & serviceType)) {
            // get appointment related staffs
            List<Integer> staffIds = appointmentApi.getAppointmentRelatedStaffIds(targetId);
            agreementSignedParams.setStaffIdList(new HashSet<>(staffIds));
        }
        iNotificationClient.sendNotificationAgreementSigned(agreementSignedParams);
    }

    /**
     * 根据 经纬度 / 邮政编码 校验距离
     * <p>
     * 先对已配置的 business 下 service area 进行校验
     * 再校验遗留逻辑，ob 下的 by zipcode、by radius
     * 1. check by zipcode:
     * a. check zipcode
     * b. 如果 zipcode = null，根据 lat，lng 获取 zipcode
     * 2. check by radius
     * a. check <lat, lng>
     * 3. check by business service area
     *
     * @param businessId
     * @param lat
     * @param lng
     * @param zipcode
     * @return
     */
    public Boolean checkAvailableDist(Integer businessId, String lat, String lng, String zipcode) {
        if (!StringUtils.hasText(zipcode) && !(StringUtils.hasText(lat) && StringUtils.hasText(lng))) {
            log.debug(
                    "checkAvailableDist, but no param is passed in. zipcode<{}>, lat<{}>, lng<{}>.", zipcode, lat, lng);
            return false;
        }
        MoeBusinessBookOnline obSettings = getSettingInfoByBusinessId(businessId);

        // by business service area
        if (!CollectionUtils.isEmpty(obSettings.getServiceAreas())) {
            Map<Long, List<CertainAreaDTO>> certainAreaMap =
                    iBusinessServiceAreaClient.getAreasByLocation(new BatchGetAreasByLocationParams(
                            businessId.longValue(),
                            obSettings.getServiceAreas(),
                            List.of(new GetAreasByLocationParams(businessId.longValue(), lat, lng, zipcode))));

            if (certainAreaMap != null
                    && !certainAreaMap.get(businessId.longValue()).isEmpty()) {
                return true;
            }
        }

        // 0. 判断是否需要校验
        if (PrimitiveTypeUtil.isNullOrZero(obSettings.getIsByZipcode())
                && PrimitiveTypeUtil.isNullOrZero(obSettings.getIsByRadius())) {
            log.info("do not need check(IsByZipcode = false, IsByRadius = false)");
            return true;
        }

        boolean result = false;
        // 1. 根据 zip code 进行校验
        if (CommonConstant.ENABLE.equals(obSettings.getIsByZipcode())) {
            if (StringUtils.isEmpty(obSettings.getZipCodes())) {
                // 商家未设置 zip code
                log.info("check by zip code, business not set zip code.");
                return true;
            }
            if (!StringUtils.isEmpty(zipcode)) {
                // zipcode校验
                result = checkZipcodeIsHave(zipcode, obSettings);
                if (result) { // zipcode满足
                    log.info("check by zip code, zipcode <{}> is available.", zipcode);
                    return true;
                }
            } else {
                if (!StringUtils.isEmpty(lat) && !StringUtils.isEmpty(lng)) {
                    // 通过 lat，lng 获取 zipcode 来进行校验
                    result = checkByZipcode(lat, lng, obSettings);
                    if (result) {
                        log.info("check by zip code, lat<{}> lng<{}> is available.", lat, lng);
                        return true;
                    }
                } else {
                    log.debug(
                            "check by zip code, but no param is passed in. zipcode<{}>, lat<{}>, lng<{}>.",
                            zipcode,
                            lat,
                            lng);
                }
            }
        }

        // 2. 根据 经纬度 进行判断
        if (CommonConstant.ENABLE.equals(obSettings.getIsByRadius())) {
            if (StringUtils.isEmpty(obSettings.getSettingLocation())) {
                // 商家未设置地址
                log.info("check by radius, setting location not set location.");
                return true;
            }
            if (!StringUtils.isEmpty(lat) && !StringUtils.isEmpty(lng)) {
                result = checkZipcodeByLatLng(lat, lng, obSettings);
                if (result) {
                    log.info("check by radius, lat<{}> lng<{}> is available.", lat, lng);
                    return true;
                }
            } else {
                log.debug(
                        "check by radius, but no param is passed in. zipcode<{}>, lat<{}>, lng<{}>.",
                        zipcode,
                        lat,
                        lng);
            }
        }

        // zipcode 和 radius都不满足
        return false;
    }

    public Boolean checkZipcodeIsHave(String zipcode, MoeBusinessBookOnline obSettings) {
        if (obSettings.getZipCodes() != null) {
            String[] businessAllowZipcodes = obSettings.getZipCodes().split(",");
            List<String> businessAllowZipcodeList = Arrays.asList(businessAllowZipcodes);
            boolean result = businessAllowZipcodeList.contains(zipcode);
            if (result) {
                log.info("checkZipcodeIsHave businessAllowZipcodeList.contains true");
            }
            return result;
        }
        return false;
    }

    public Boolean checkByZipcode(String lat, String lng, MoeBusinessBookOnline obInfo) {
        String zipcode = getZipcodeFromLatlng(lat, lng);
        log.info("getZipcodeFromLatlng:" + zipcode);
        return checkZipcodeIsHave(zipcode, obInfo);
    }

    public Boolean checkZipcodeByLatLng(String lat, String lng, MoeBusinessBookOnline obInfo) {
        // is_in_service_area
        // 起点 lat,lng
        // 终点 obInfo.getSettingLat(),obInfo.getSettingLng()
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(obInfo.getBusinessId()).build());
        boolean result = false;
        try {
            var distanceMatrixElement =
                    smartScheduleService.callGoogleAPI(lat, lng, obInfo.getSettingLat(), obInfo.getSettingLng());
            log.info("google api result:" + JsonUtil.toJson(distanceMatrixElement));
            log.info("setting dist" + obInfo.getMaxAvailableDist());
            if (GoogleMapService.isValidMatrixElement(distanceMatrixElement)) {
                if (obInfo.getMaxAvailableDist()
                        >=
                        // meters to mile / kilometer
                        metersToMiOrKm(businessInfo.getUnitOfDistanceType(), distanceMatrixElement.getDistance())) {
                    result = true;
                }
                log.info("setting time" + obInfo.getMaxAvailableTime());
                if (!result
                        && obInfo.getMaxAvailableTime()
                                >= (distanceMatrixElement.getDuration().getSeconds() / 60)) {
                    result = true;
                }
            }
        } catch (CommonException commonException) {
            if (commonException.getCode().equals(ResponseCodeEnum.GOOGLE_INVALID_ADDRESS.getCode())) {
                return false;
            }
            throw commonException;
        }
        // entry.getKey().setDriveInMinutes(Math.toIntExact(before.duration.inSeconds /
        // 60));
        // entry.getKey().setDriveInMiles(Math.toIntExact(before.distance.inMeters /
        // 1609));
        // 根据api获取 dist和time
        // 最大距离obInfo.getMaxAvailableDist();
        // 最大时间obInfo.getMaxAvailableTime();
        // 距离和时间满足至少一个即可
        return result;
    }

    public Integer metersToMiOrKm(Byte unitOfDistanceType, long meters) {
        if (Objects.equals(unitOfDistanceType, Dictionary.UNITED_DISTANCE_TYPE_2.byteValue())) {
            return Math.toIntExact(meters / CommonConstant.METERS_PER_KILOMETER);
        }
        return Double.valueOf(meters / CommonConstant.METERS_PER_MILE).intValue();
    }

    public String getZipcodeFromLatlng(String lat, String lng) {
        // 通过经纬度lat,lng解析zipcode
        // $latlng = $lat . ',' . $lng;
        // $url =
        // "https://maps.googleapis.com/maps/api/geocode/json?key=$google_api_key&latlng=$latlng";
        return smartScheduleService.getZipcodeByLatLng(lat, lng);
        /**
         * $zip_code = false;
         * foreach ($address_components as $key => $value) {
         * if (in_array("postal_code", $value['types']) || $value['types'] ==
         * "postal_code") {
         * $this->logger->info("$value:", $value);
         * $zip_code = $value['long_name'];
         * break;
         * }
         * }
         * return $zip_code;
         */
    }

    public List<BookOnlineQuestionSaveDto> getQuestionSaveByCustomerInfo(CustomerIdWithPetIdsParams petIdsParams) {
        List<MoeBookOnlineQuestionSave> questionSaveList = moeBookOnlineQuestionSaveMapper.selectByCustomerIdWithPetIds(
                petIdsParams.getBusinessId(), petIdsParams.getCustomerId());
        List<BookOnlineQuestionSaveDto> returnSaveDtoList = new ArrayList<>();
        for (MoeBookOnlineQuestionSave questionSave : questionSaveList) {
            BookOnlineQuestionSaveDto saveDto = new BookOnlineQuestionSaveDto();
            BeanUtils.copyProperties(questionSave, saveDto);
            returnSaveDtoList.add(saveDto);
        }
        return returnSaveDtoList;
    }

    public MoeBusinessDto getBusinessInfo(Integer businessId) {
        // get business info
        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(businessId);
        return iBusinessBusinessClient.getBusinessInfo(infoIdParams);
    }

    public MoeBookOnlineNotificationDto getOnlineBookingSendTemplate(Integer businessId) {
        MoeBookOnlineNotificationDto dto = new MoeBookOnlineNotificationDto();
        BeanUtils.copyProperties(initBusinessNotification(businessId), dto);
        return dto;
    }

    public Boolean updateStaffAvailableTimeByWorkingTime(UpdateStaffTimeParam updateStaffTimeParam) {
        log.info(
                "staff working hours modified then update ob available time, updateParam:{}",
                JsonUtil.toJson(updateStaffTimeParam));
        var response = staffTimeSyncService.queryStaffTime(
                updateStaffTimeParam.getBusinessId(), Collections.singletonList(updateStaffTimeParam.getStaffId()));
        var staffTime = response.get(updateStaffTimeParam.getStaffId());
        if (staffTime == null) {
            log.info("no need update");
            return true;
        } else {
            Map<String, StaffTime> staffTimeObject = staffTime.getStaffTimes();
            MoeBookOnlineStaffTime saveStaffTime = new MoeBookOnlineStaffTime();

            // 计算timeRange交集
            List<TimePeriodDto> timeRange = updateStaffTimeParam.getTimeRange();
            List<TimeRangeDto> workTime = new ArrayList<>();
            for (TimePeriodDto timeDto : timeRange) {
                TimeRangeDto timeRangeDto = new TimeRangeDto();
                timeRangeDto.setStartTime(timeDto.getStartTime());
                timeRangeDto.setEndTime(timeDto.getEndTime());
                workTime.add(timeRangeDto);
            }
            List<TimeRangeDto> staffAvailableTime = staffTimeObject
                    .get(WeekUtil.getKeyForWeekDay(updateStaffTimeParam.getDayOfWeek()))
                    .getTimeRange();
            List<TimeRangeDto> newStaffAvailableTime =
                    filterOutNotAvailableTimeForDay(workTime, staffAvailableTime, new ArrayList<>());
            if (newStaffAvailableTime.size() == 0) {
                staffTimeObject
                        .get(WeekUtil.getKeyForWeekDay(updateStaffTimeParam.getDayOfWeek()))
                        .setIsSelected(false);
            }
            staffTimeObject
                    .get(WeekUtil.getKeyForWeekDay(updateStaffTimeParam.getDayOfWeek()))
                    .setTimeRange(newStaffAvailableTime);
            saveStaffTime.setStaffTimes(JsonUtil.toJson(staffTimeObject));
            // 是否关闭
            if (updateStaffTimeParam.getIsClose()) {
                staffTimeObject
                        .get(WeekUtil.getKeyForWeekDay(updateStaffTimeParam.getDayOfWeek()))
                        .setIsSelected(false);
            }
            staffTimeSyncService.updateByPrimaryKeySelective(
                    updateStaffTimeParam.getBusinessId(), updateStaffTimeParam.getStaffId(), saveStaffTime);
            log.info(
                    "ob available time update succeeded, updated staffId:{}, ob time:{}",
                    updateStaffTimeParam.getStaffId(),
                    saveStaffTime.getStaffTimes());
        }
        return true;
    }

    /**
     * 校验 ob 配置
     * 1. 校验是否允许启用 ob
     * 2. 校验地址
     * <p>
     * 从MoeGroomingBookOnlineController迁移过来
     *
     * @param obConfig
     * @param obParams
     */
    private void checkObConfig(MoeBusinessBookOnline obConfig, BookOnlineSubmitParams obParams) {
        if (!CustomerContactEnum.BUSINESS_IS_ENABLE.equals(obConfig.getIsEnable())) {
            throw new CommonException(ResponseCodeEnum.BOOK_ONLINE_NOT_ENABLE);
        }
        if (BooleanEnum.VALUE_TRUE.equals(obConfig.getIsNeedAddress())
                && BooleanEnum.VALUE_TRUE.equals(obConfig.getIsCheckExistingClient())) {
            if (StringUtils.isEmpty(obParams.getCustomerData().getAddress1())
                    && StringUtils.isEmpty(obParams.getCustomerData().getAddressId())) {
                throw new CommonException(ResponseCodeEnum.NO_CUSTOMER_ADDRESS);
            }
        }
    }

    /**
     * 校验协议
     * 1. 数量
     * 2. 协议编号
     *
     * @param obParams
     */
    private void checkAgreementNumber(MoeBusinessBookOnline bookOnline, BookOnlineSubmitParams obParams) {
        var needSignAgreements =
                listUnsignedAgreement(bookOnline.getCompanyId(), bookOnline.getBusinessId(), getCustomerId(obParams));
        if (ObjectUtils.isEmpty(needSignAgreements)) {
            return;
        }

        var agreements = Optional.ofNullable(obParams.getAgreements()).orElseGet(List::of);
        if (needSignAgreements.size() > agreements.size()) {
            throw bizException(Code.CODE_AGREEMENT_NOT_CONFIRM);
        }

        for (var agreement : agreements) {
            if (!AgreementEnum.AGREEMENT_CONFIRM.equals(agreement.getAgreementConfirmed())) {
                throw bizException(Code.CODE_AGREEMENT_NOT_CONFIRM);
            }
            if (!StringUtils.hasLength(agreement.getSignature())) {
                throw bizException(Code.CODE_SIGNATURE_IS_EMPTY);
            }
        }
    }

    @Nullable
    private static Integer getCustomerId(BookOnlineSubmitParams obParams) {
        return Optional.ofNullable(obParams.getCustomerData())
                .map(BookOnlineCustomerParams::getCustomerId)
                .orElse(null);
    }

    /**
     * 校验客户类型
     * 1. 是否仅允许新用户
     * 2. 是否仅允许老用户
     * <p>
     * 从MoeGroomingBookOnlineController迁移过来
     *
     * @param obParams
     */
    private void checkCustomerType(BookOnlineSubmitParams obParams) {
        boolean isNewClient = obParams.getCustomerData().getCustomerId() == null;

        final var acceptClientType = obAvailabilitySettingServiceBlockingStub
                .getGroomingServiceAvailability(GetGroomingServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(obParams.getCompanyId())
                                .setBusinessId(obParams.getBusinessId())
                                .build())
                        .build())
                .getAvailability()
                .getAcceptCustomerType();

        // 满足一个则失败
        // 只允许新顾客却出现旧顾客
        boolean cond1 = AcceptCustomerType.NEW_CUSTOMER.equals(acceptClientType) && !isNewClient;
        // 只允许旧顾客却出现新顾客
        boolean cond2 = AcceptCustomerType.EXISTING_CUSTOMER.equals(acceptClientType) && isNewClient;
        if (cond1 || cond2) {
            throw new CommonException(ResponseCodeEnum.ACCEPT_TYPE_NOW_ALLOWED);
        }
    }

    /**
     * 1. 校验是否确认 cancel policy
     * 2. 校验付款方式是否合法
     * <p>
     * 从MoeGroomingBookOnlineController迁移过来
     *
     * @param obParams
     * @param obConfig
     */
    private void checkCancelPolicyAndPayType(BookOnlineSubmitParams obParams, MoeBusinessBookOnline obConfig) {
        // cancellation policy checking and check pay type
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(obParams.getBusinessId()).build());
        // https://moego.atlassian.net/browse/ERP-8620
        if (BooleanUtils.isTrue(obParams.getFromPetParentPortal())
                && Objects.equals(businessDto.getPrimaryPayType(), PaymentMethodEnum.CARD_PROCESSOR_TYPE_SQUARE)) {
            return;
        }
        if (!PaymentMethodEnum.CARD_PROCESSOR_TYPE_NONE.equals(businessDto.getPrimaryPayType())
                && CustomerContactEnum.ENABLE_NO_SHOW_FEE.equals(obConfig.getEnableNoShowFee())) {
            if (!CustomerContactEnum.AGREE_CANCEL_POLICY.equals(obParams.getIsAgreePolicy())) {
                throw new CommonException(ResponseCodeEnum.CANCEL_POLICY_NOT_CONFIRMED);
            }
            // check card
            // Application/BusinessApi/Service/BookOnlineClientService.class.php:3470 DONE
            // in payment by
            // steve
            // charge token will be save in ob service then.
            if (!Boolean.TRUE.equals(obParams.getCustomerData().getHasStripeCard())
                    && StringUtils.isEmpty(obParams.getCustomerData().getChargeToken())) {
                throw new CommonException(ResponseCodeEnum.STRIPE_CARD_TOKEN_IS_EMPTY);
            }
        }
    }

    /**
     * Depending on whether migrate decides to return ob profile or biz info
     *
     * @param businessIdList biz id
     * @return biz ob profile
     */
    public List<BookOnlineProfileDTO> listBookOnlineProfile(List<Integer> businessIdList) {
        List<MoeBookOnlineProfile> obProfileList = businessProfileService.listProfileByBusinessId(businessIdList);
        return obProfileList.stream()
                .map(bookOnlineProfile -> new BookOnlineProfileDTO()
                        .setBusinessId(bookOnlineProfile.getBusinessId())
                        .setBusinessName(bookOnlineProfile.getBusinessName())
                        .setAddress(bookOnlineProfile.getAddress())
                        .setAvatarPath(bookOnlineProfile.getAvatarPath())
                        .setPhoneNumber(bookOnlineProfile.getPhoneNumber()))
                .toList();
    }

    /**
     * Depending on whether migrate decides to return old gallery or new gallery
     *
     * @param businessIdList biz id
     * @return ob gallery
     */
    public List<BookOnlineGalleryDTO> listGalleryMaxSortImageByBusinessId(List<Integer> businessIdList) {
        if (CollectionUtils.isEmpty(businessIdList)) {
            return Collections.emptyList();
        }
        // Get migrated biz ob gallery
        List<MoeBookOnlineLandingPageConfig> landingPageConfigList =
                landingPageConfigService.listByBusinessId(businessIdList);
        Map<Integer, BookOnlineGalleryDTO> bizMaxSortGalleryMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(landingPageConfigList)) {
            List<Integer> migratedBizIdList = landingPageConfigList.stream()
                    .map(MoeBookOnlineLandingPageConfig::getBusinessId)
                    .toList();
            bizMaxSortGalleryMap.putAll(
                    landingPageGalleryService.listGalleryMinSortImageByBusinessId(migratedBizIdList).stream()
                            .collect(Collectors.toMap(BookOnlineGalleryDTO::getBusinessId, Function.identity())));
        }
        // No migrate
        Map<Integer, List<BookOnlineGalleryDTO>> galleryDTOMap =
                moeBookOnlineGalleryMapper.listGalleryMaxSortImageByBusinessId(businessIdList).stream()
                        .map(gallery -> new BookOnlineGalleryDTO()
                                .setId(gallery.getId())
                                .setBusinessId(gallery.getBusinessId())
                                .setImagePath(gallery.getImagePath())
                                .setSort(gallery.getSort()))
                        .collect(Collectors.groupingBy(BookOnlineGalleryDTO::getBusinessId));
        return businessIdList.stream()
                .map(businessId -> {
                    BookOnlineGalleryDTO galleryDTO = bizMaxSortGalleryMap.get(businessId);
                    if (Objects.nonNull(galleryDTO)) {
                        return galleryDTO;
                    }
                    List<BookOnlineGalleryDTO> galleryDTOList = galleryDTOMap.get(businessId);
                    if (!CollectionUtils.isEmpty(galleryDTOList)) {
                        return galleryDTOList.get(0);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .toList();
    }

    public List<BookOnlineConfigDTO> listBookOnlineConfig(List<Integer> businessIdList) {
        return moeBusinessBookOnlineMapper.getBusinessBookOnlineList(businessIdList).stream()
                .map(businessBookOnline -> new BookOnlineConfigDTO()
                        .setBusinessId(businessBookOnline.getBusinessId())
                        .setIsEnable(businessBookOnline.getIsEnable())
                        .setBookOnlineName(businessBookOnline.getBookOnlineName())
                        .setUseVersion(businessBookOnline.getUseVersion()))
                .collect(Collectors.toList());
    }

    public void deletePaymentGroupConfig(Integer businessId) {
        moeBusinessBookOnlineMapper.deletePaymentGroupSetting(businessId);
    }

    public void updatePaymentSetting(Long companyId, Integer businessId, BusinessBookOnlinePaymentParams params) {
        BookOnlinePaymentGroupParams groupSetting = params.getPaymentGroupSetting();
        MoeBusinessBookOnline setting = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
        if (setting == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "ob setting not exists");
        }
        final var acceptClientType = obAvailabilitySettingServiceBlockingStub
                .getGroomingServiceAvailability(GetGroomingServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder().setCompanyId(companyId).setBusinessId(businessId))
                        .build())
                .getAvailability()
                .getAcceptCustomerType();
        if (groupSetting != null && groupSetting.getAcceptClient() != null) {
            if (acceptClientType != AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER
                    && !Objects.equals(
                            acceptClientType, AcceptCustomerType.forNumber(groupSetting.getAcceptClient()))) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Client accept client type setting conflict with Payment");
            }
        }
        if (params.getPaymentGroupSetting() != null
                && "".equals(params.getPaymentGroupSetting().getAcceptRule())) {
            params.getPaymentGroupSetting().setAcceptRule(EMPTY_JSON);
        }
        MoeBusinessBookOnline bookOnline = ObPaymentSettingMapper.INSTANCE.paramsToDO(params, groupSetting);
        bookOnline.setId(setting.getId());
        moeBusinessBookOnlineMapper.updateByPrimaryKeySelective(bookOnline);
    }

    // c port check user payment method
    public BookOnlinePaymentGroupSettingDTO checkGroupPaymentTypeForClient(
            MoeBusinessBookOnline info, Integer customerId) {
        if (info == null || info.getGroupPaymentType() == null || info.getGroupAcceptClient() == null) {
            return null;
        }
        int clientType = customerId == null
                ? AcceptClientType.ACCEPT_CLIENT_TYPE_NEW_VALUE
                : AcceptClientType.ACCEPT_CLIENT_TYPE_EXISTING_VALUE;
        // 先判断accept_client
        switch (clientType) {
            case AcceptClientType.ACCEPT_CLIENT_TYPE_NEW_VALUE:
                if (AcceptClientType.ACCEPT_CLIENT_TYPE_NEW_VALUE == info.getGroupAcceptClient()
                        || AcceptClientType.ACCEPT_CLIENT_TYPE_BOTH_VALUE == info.getGroupAcceptClient()) {
                    return ObPaymentSettingMapper.INSTANCE.toCertainGroupSetting(info);
                }
                // 没有命中 不需要继续判断
                return null;
            case AcceptClientType.ACCEPT_CLIENT_TYPE_EXISTING_VALUE:
                if (AcceptClientType.ACCEPT_CLIENT_TYPE_NEW_VALUE == info.getGroupAcceptClient()) {
                    return null;
                }
                // 后续优化可以在这里加缓存
                return checkPaymentTypeWithRule(info, customerId);
            default:
                return null;
        }
    }

    private BookOnlinePaymentGroupSettingDTO checkPaymentTypeWithRule(MoeBusinessBookOnline info, Integer customerId) {
        if (!StringUtils.hasText(info.getGroupFilterRule()) || EMPTY_JSON.equals(info.getGroupFilterRule())) {
            // 没有配置规则 直接命中
            log.info("aim checkPaymentTypeWithRule, info: {}, customerId: {}", info, customerId);
            return ObPaymentSettingMapper.INSTANCE.toCertainGroupSetting(info);
        }
        // existing 需要判断rule, TODO 需要优化下存储方案 或者 逻辑下沉到 customer
        FilterParams params = JsonUtil.toBean(info.getGroupFilterRule(), FilterParams.class);
        FilterParams clientIdRule = FilterParams.builder()
                .type(TypeEnum.TYPE_AND)
                .property(PropertyEnum.client_id)
                .value(String.valueOf(customerId))
                .operator(OperatorEnum.OPERATOR_EQUAL)
                .build();
        params.filters().add(clientIdRule);
        CustomerFilterResult smartClientList = customerClient.getSmartClientListV2(new ClientListRequest(
                info.getCompanyId(),
                info.getBusinessId(),
                null,
                ClientListParams.builder().filters(params).build()));
        if (smartClientList != null && smartClientList.getCustomerTotal() == 1) {
            log.info("aim checkPaymentTypeWithRule, info: {}, customerId: {}", info, customerId);
            return ObPaymentSettingMapper.INSTANCE.toCertainGroupSetting(info);
        }
        return null;
    }

    private List<AgreementModel> listUnsignedAgreement(long companyId, long businessId, @Nullable Integer customerId) {
        var builder = ListUnsignedAgreementRequest.newBuilder();
        builder.setCompanyId(companyId);
        builder.addBusinessIds(businessId);
        builder.setServiceTypes(ServiceType.SERVICE_TYPE_ONLINE_BOOKING_VALUE);
        Optional.ofNullable(customerId).filter(CommonUtil::isNormal).ifPresent(builder::setCustomerId);
        return agreementClient.listUnsignedAgreement(builder.build()).getAgreementsList();
    }

    public List<MessageTemplateUseCase> getUserCaseForAPPTAutoMsg() {
        return List.of(
                USE_CASE_ONLINE_BOOKING_REQUEST_SUBMITTED,
                USE_CASE_ONLINE_BOOKING_REQUEST_ACCEPTED,
                USE_CASE_ONLINE_BOOKING_REQUEST_DECLINED,
                USE_CASE_ONLINE_BOOKING_REQUEST_AUTO_MOVED_TO_WAITLIST);
    }

    // 新版 auto message 与 旧版 ob notification 数据格式无法完全兼容。
    // 这里兼容逻辑是，包含多个 service type template 的场景，取 main service 的 template
    MoeBookOnlineNotification fillClientNotification(MoeBookOnlineNotification notification) {
        var request = GetAppointmentAutoMsgConfigListRequest.newBuilder()
                .setCompanyId(notification.getCompanyId())
                .setBusinessId(notification.getBusinessId())
                .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
                        .addAllValues(getUserCaseForAPPTAutoMsg())
                        .build())
                .build();
        List<AppointmentAutoMsgConfigModel> configList =
                autoMessageConfigClient.getAppointmentAutoMsgConfigList(request).getAutoMessagesList();
        Map<Long, TemplateModel> templates = getTemplatesByAppointmentConfig(configList);

        for (AppointmentAutoMsgConfigModel config : configList) {
            ServiceTypeConfigModel template = getMainServiceTypeConfig(config.getTemplatesList());

            String smsBody = templates.get(template.getSmsTemplateId()).getBody();
            String emailSubject = templates.get(template.getEmailTemplateId()).getSubject();
            String emailBody = templates.get(template.getEmailTemplateId()).getBody();

            switch (config.getUseCase()) {
                case USE_CASE_ONLINE_BOOKING_REQUEST_SUBMITTED -> {
                    notification.setSubmitClientType(
                            getNotificationClientType(config.getIsSmsEnabled(), config.getIsEmailEnabled()));
                    notification.setSubmitTemplate(smsBody);
                    notification.setSubmitEmailSubjectTemplate(emailSubject);
                    notification.setSubmitEmailContentTemplate(emailBody);
                }
                case USE_CASE_ONLINE_BOOKING_REQUEST_ACCEPTED -> {
                    notification.setAcceptClientType(
                            getNotificationClientType(config.getIsSmsEnabled(), config.getIsEmailEnabled()));
                    notification.setAcceptTemplate(smsBody);
                    notification.setAcceptEmailSubjectTemplate(emailSubject);
                    notification.setAcceptEmailContentTemplate(emailBody);
                }
                case USE_CASE_ONLINE_BOOKING_REQUEST_DECLINED -> {
                    notification.setDeclineClientType(
                            getNotificationClientType(config.getIsSmsEnabled(), config.getIsEmailEnabled()));
                    notification.setDeclineTemplate(smsBody);
                    notification.setDeclineEmailSubjectTemplate(emailSubject);
                    notification.setDeclineEmailContentTemplate(emailBody);
                }
                case USE_CASE_ONLINE_BOOKING_REQUEST_AUTO_MOVED_TO_WAITLIST -> {
                    notification.setAutoMoveClientType(
                            getNotificationClientType(config.getIsSmsEnabled(), config.getIsEmailEnabled()));
                    notification.setAutoMoveTemplate(smsBody);
                    notification.setAutoMoveEmailSubjectTemplate(emailSubject);
                    notification.setAutoMoveEmailContentTemplate(emailBody);
                }
                default -> {}
            }
        }
        return notification;
    }

    // 主 service 选择逻辑：boarding > daycare > grooming, evaluation 不与其他服务一起
    public ServiceTypeConfigModel getMainServiceTypeConfig(List<ServiceTypeConfigModel> serviceTypeConfigs) {
        ServiceItemType mainServiceType = ServiceItemType.EVALUATION;
        for (ServiceTypeConfigModel serviceTypeConfig : serviceTypeConfigs) {
            if (serviceTypeConfig.getServiceItemTypesList().contains(ServiceItemType.BOARDING)) {
                mainServiceType = ServiceItemType.BOARDING;
                break;
            }
            if (serviceTypeConfig.getServiceItemTypesList().contains(ServiceItemType.DAYCARE)) {
                mainServiceType = ServiceItemType.DAYCARE;
                continue;
            }
            if (serviceTypeConfig.getServiceItemTypesList().contains(ServiceItemType.GROOMING)
                    && mainServiceType == ServiceItemType.EVALUATION) {
                mainServiceType = ServiceItemType.GROOMING;
            }
        }

        ServiceItemType finalMainServiceType = mainServiceType;
        return serviceTypeConfigs.stream()
                .filter(k -> k.getServiceItemTypesList().contains(finalMainServiceType))
                .findFirst()
                .orElse(null);
    }

    void updateClientNotification(Long companyId, MoeBookOnlineNotification currentNotification) {
        var request = GetAppointmentAutoMsgConfigListRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(currentNotification.getBusinessId())
                .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
                        .addAllValues(getUserCaseForAPPTAutoMsg())
                        .build())
                .build();
        List<AppointmentAutoMsgConfigModel> configList =
                autoMessageConfigClient.getAppointmentAutoMsgConfigList(request).getAutoMessagesList();
        Map<Long, TemplateModel> templates = getTemplatesByAppointmentConfig(configList);

        for (AppointmentAutoMsgConfigModel config : configList) {
            switch (config.getUseCase()) {
                case USE_CASE_ONLINE_BOOKING_REQUEST_SUBMITTED -> {
                    updateNewAutoMsg(
                            config,
                            templates,
                            currentNotification.getSubmitClientType(),
                            currentNotification.getSubmitEmailSubjectTemplate(),
                            currentNotification.getSubmitEmailContentTemplate(),
                            currentNotification.getSubmitTemplate());
                }
                case USE_CASE_ONLINE_BOOKING_REQUEST_ACCEPTED -> {
                    updateNewAutoMsg(
                            config,
                            templates,
                            currentNotification.getAcceptClientType(),
                            currentNotification.getAcceptEmailSubjectTemplate(),
                            currentNotification.getAcceptEmailContentTemplate(),
                            currentNotification.getAcceptTemplate());
                }
                case USE_CASE_ONLINE_BOOKING_REQUEST_DECLINED -> {
                    updateNewAutoMsg(
                            config,
                            templates,
                            currentNotification.getDeclineClientType(),
                            currentNotification.getDeclineEmailSubjectTemplate(),
                            currentNotification.getDeclineEmailContentTemplate(),
                            currentNotification.getDeclineTemplate());
                }
                case USE_CASE_ONLINE_BOOKING_REQUEST_AUTO_MOVED_TO_WAITLIST -> {
                    updateNewAutoMsg(
                            config,
                            templates,
                            currentNotification.getAutoMoveClientType(),
                            currentNotification.getAutoMoveEmailSubjectTemplate(),
                            currentNotification.getAutoMoveEmailContentTemplate(),
                            currentNotification.getAutoMoveTemplate());
                }
                default -> {}
            }
        }
    }

    void updateNewAutoMsg(
            AppointmentAutoMsgConfigModel existConfig,
            Map<Long, TemplateModel> templates,
            Byte newClientType,
            String newEmailSubject,
            String newEmailBody,
            String newSmsBody) {
        if (existConfig.getTemplatesCount() != 1) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FORBIDDEN, "Unsupported. Please upgrade your app to the latest version and try again.");
        }
        ServiceTypeConfigModel template = existConfig.getTemplates(0);

        UpdateAppointmentAutoMsgConfigRequest.Builder autoMsgConfigUpdate =
                UpdateAppointmentAutoMsgConfigRequest.newBuilder()
                        .setId(existConfig.getId())
                        .setCompanyId(existConfig.getCompanyId());
        if (newClientType != null && (isEmailEnabled(newClientType) != existConfig.getIsEmailEnabled())) {
            boolean isEmailEnable = isEmailEnabled(newClientType);
            if (isEmailEnable != existConfig.getIsEmailEnabled()) {
                autoMsgConfigUpdate.setIsEmailEnabled(isEmailEnable);
            }
            boolean isSMSEnable = isSMSEnabled(newClientType);
            if (isSMSEnable != existConfig.getIsSmsEnabled()) {
                autoMsgConfigUpdate.setIsSmsEnabled(isSMSEnable);
            }
        }

        String existEmailSubject = templates.get(template.getEmailTemplateId()).getSubject();
        String existEmailBody = templates.get(template.getEmailTemplateId()).getBody();
        String existSmsBody = templates.get(template.getSmsTemplateId()).getBody();
        if (newEmailSubject == null) {
            newEmailSubject = existEmailSubject;
        }
        if (newEmailBody == null) {
            newEmailBody = existEmailBody;
        }
        if (newSmsBody == null) {
            newSmsBody = existSmsBody;
        }
        if (!newEmailSubject.equals(existEmailSubject)
                || !newEmailBody.equals(existEmailBody)
                || !newSmsBody.equals(existSmsBody)) {
            Pair<Long, Long> newTemplates = buildSMSAndEmailTemplate(newSmsBody, newEmailSubject, newEmailBody);
            autoMsgConfigUpdate.setTemplates(ServiceTypeConfigDefList.newBuilder()
                    .addAllValues(List.of(ServiceTypeConfigDef.newBuilder()
                            .addAllServiceItemTypes(template.getServiceItemTypesList())
                            .setSmsTemplateId(newTemplates.getFirst())
                            .setEmailTemplateId(newTemplates.getSecond())
                            .build()))
                    .build());
        }
        if (autoMsgConfigUpdate.hasIsEmailEnabled()
                || autoMsgConfigUpdate.hasIsSmsEnabled()
                || autoMsgConfigUpdate.hasTemplates()) {
            autoMessageConfigClient.updateAppointmentAutoMsgConfig(autoMsgConfigUpdate.build());
        }
    }

    private Map<Long, TemplateModel> getTemplatesByAppointmentConfig(
            List<AppointmentAutoMsgConfigModel> autoMsgConfigs) {
        if (CollectionUtils.isEmpty(autoMsgConfigs)) {
            return new HashMap<>();
        }
        List<Long> templateIds = new ArrayList<>();
        for (AppointmentAutoMsgConfigModel config : autoMsgConfigs) {
            for (ServiceTypeConfigModel template : config.getTemplatesList()) {
                templateIds.add(template.getSmsTemplateId());
                templateIds.add(template.getEmailTemplateId());
            }
        }
        var request = MGetTemplatesRequest.newBuilder().addAllIds(templateIds).build();
        var resp = templateClient.mGetTemplates(request);
        return resp.getTemplatesList().stream()
                .collect(Collectors.toMap(TemplateModel::getId, smsTemplateModel -> smsTemplateModel, (k1, k2) -> k1));
    }

    private Byte getNotificationClientType(Boolean isSMSEnabled, Boolean isEmailEnabled) {
        int result = 4;
        if (Boolean.TRUE.equals(isSMSEnabled) && Boolean.TRUE.equals(isEmailEnabled)) {
            result = 3;
        } else if (Boolean.TRUE.equals(isSMSEnabled)) {
            result = 2;
        } else if (Boolean.TRUE.equals(isEmailEnabled)) {
            result = 1;
        }
        return (byte) result;
    }

    private boolean isSMSEnabled(Byte newClientType) {
        return newClientType == 2 || newClientType == 3;
    }

    private boolean isEmailEnabled(Byte newClientType) {
        return newClientType == 1 || newClientType == 3;
    }

    private Pair<Long, Long> buildSMSAndEmailTemplate(String smsBody, String emailSubject, String emailBody) {
        List<BatchCreateTemplateItemDef> templatesToCreate = new ArrayList<>();
        templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                .setSeqNum(0)
                .setBody(smsBody)
                .build());
        templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                .setSeqNum(1)
                .setSubject(emailSubject)
                .setBody(emailBody)
                .build());
        Map<Long, TemplateModel> templatesCreated = templateClient
                .batchCreateTemplates(BatchCreateTemplatesRequest.newBuilder()
                        .addAllTemplates(templatesToCreate)
                        .build())
                .getTemplatesMap();
        return Pair.of(
                templatesCreated.get(0L).getId(), templatesCreated.get(1L).getId());
    }
}
