<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingServiceMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="tax_id" jdbcType="INTEGER" property="taxId" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="inactive" jdbcType="TINYINT" property="inactive" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="color_code" jdbcType="VARCHAR" property="colorCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="show_base_price" jdbcType="TINYINT" property="showBasePrice" />
    <result column="book_online_available" jdbcType="TINYINT" property="bookOnlineAvailable" />
    <result column="is_all_staff" jdbcType="TINYINT" property="isAllStaff" />
    <result column="breed_filter" jdbcType="TINYINT" property="breedFilter" />
    <result column="weight_filter" jdbcType="TINYINT" property="weightFilter" />
    <result column="weight_down_limit" jdbcType="DECIMAL" property="weightDownLimit" />
    <result column="weight_up_limit" jdbcType="DECIMAL" property="weightUpLimit" />
    <result column="coat_filter" jdbcType="TINYINT" property="coatFilter" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="is_all_location" jdbcType="TINYINT" property="isAllLocation" />
    <result column="images" jdbcType="CHAR" property="images" />
    <result column="service_item_type" jdbcType="INTEGER" property="serviceItemType" />
    <result column="price_unit" jdbcType="INTEGER" property="priceUnit" />
    <result column="add_to_commission" jdbcType="BIT" property="addToCommission" />
    <result column="can_tip" jdbcType="BIT" property="canTip" />
    <result column="require_dedicated_staff" jdbcType="BIT" property="requireDedicatedStaff" />
    <result column="require_dedicated_lodging" jdbcType="BIT" property="requireDedicatedLodging" />
    <result column="lodging_filter" jdbcType="BIT" property="lodgingFilter" />
    <result column="allowed_lodging_list" jdbcType="CHAR" property="allowedLodgingList" />
    <result column="service_filter" jdbcType="BIT" property="serviceFilter" />
    <result column="allowed_pet_size_list" jdbcType="CHAR" property="allowedPetSizeList" />
    <result column="pet_size_filter" jdbcType="BIT" property="petSizeFilter" />
    <result column="max_duration" jdbcType="INTEGER" property="maxDuration" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, category_id, name, description, type, tax_id, price, duration, inactive,
    sort, color_code, status, create_time, update_time, show_base_price, book_online_available,
    is_all_staff, breed_filter, weight_filter, weight_down_limit, weight_up_limit, coat_filter,
    company_id, is_all_location, images, service_item_type, price_unit, add_to_commission,
    can_tip, require_dedicated_staff, require_dedicated_lodging, lodging_filter, allowed_lodging_list,
    service_filter, allowed_pet_size_list, pet_size_filter, max_duration
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_service
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service (business_id, category_id, name,
      description, type, tax_id,
      price, duration, inactive,
      sort, color_code, status,
      create_time, update_time, show_base_price,
      book_online_available, is_all_staff, breed_filter,
      weight_filter, weight_down_limit, weight_up_limit,
      coat_filter, company_id, is_all_location,
      images, service_item_type, price_unit,
      add_to_commission, can_tip, require_dedicated_staff,
      require_dedicated_lodging, lodging_filter, allowed_lodging_list,
      service_filter, allowed_pet_size_list, pet_size_filter,
      max_duration)
    values (#{businessId,jdbcType=INTEGER}, #{categoryId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR},
      #{description,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}, #{taxId,jdbcType=INTEGER},
      #{price,jdbcType=DECIMAL}, #{duration,jdbcType=INTEGER}, #{inactive,jdbcType=TINYINT},
      #{sort,jdbcType=INTEGER}, #{colorCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{showBasePrice,jdbcType=TINYINT},
      #{bookOnlineAvailable,jdbcType=TINYINT}, #{isAllStaff,jdbcType=TINYINT}, #{breedFilter,jdbcType=TINYINT},
      #{weightFilter,jdbcType=TINYINT}, #{weightDownLimit,jdbcType=DECIMAL}, #{weightUpLimit,jdbcType=DECIMAL},
      #{coatFilter,jdbcType=TINYINT}, #{companyId,jdbcType=BIGINT}, #{isAllLocation,jdbcType=TINYINT},
      #{images,jdbcType=CHAR}, #{serviceItemType,jdbcType=INTEGER}, #{priceUnit,jdbcType=INTEGER},
      #{addToCommission,jdbcType=BIT}, #{canTip,jdbcType=BIT}, #{requireDedicatedStaff,jdbcType=BIT},
      #{requireDedicatedLodging,jdbcType=BIT}, #{lodgingFilter,jdbcType=BIT}, #{allowedLodgingList,jdbcType=CHAR},
      #{serviceFilter,jdbcType=BIT}, #{allowedPetSizeList,jdbcType=CHAR}, #{petSizeFilter,jdbcType=BIT},
      #{maxDuration,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="taxId != null">
        tax_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="inactive != null">
        inactive,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="colorCode != null">
        color_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="showBasePrice != null">
        show_base_price,
      </if>
      <if test="bookOnlineAvailable != null">
        book_online_available,
      </if>
      <if test="isAllStaff != null">
        is_all_staff,
      </if>
      <if test="breedFilter != null">
        breed_filter,
      </if>
      <if test="weightFilter != null">
        weight_filter,
      </if>
      <if test="weightDownLimit != null">
        weight_down_limit,
      </if>
      <if test="weightUpLimit != null">
        weight_up_limit,
      </if>
      <if test="coatFilter != null">
        coat_filter,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="isAllLocation != null">
        is_all_location,
      </if>
      <if test="images != null">
        images,
      </if>
      <if test="serviceItemType != null">
        service_item_type,
      </if>
      <if test="priceUnit != null">
        price_unit,
      </if>
      <if test="addToCommission != null">
        add_to_commission,
      </if>
      <if test="canTip != null">
        can_tip,
      </if>
      <if test="requireDedicatedStaff != null">
        require_dedicated_staff,
      </if>
      <if test="requireDedicatedLodging != null">
        require_dedicated_lodging,
      </if>
      <if test="lodgingFilter != null">
        lodging_filter,
      </if>
      <if test="allowedLodgingList != null">
        allowed_lodging_list,
      </if>
      <if test="serviceFilter != null">
        service_filter,
      </if>
      <if test="allowedPetSizeList != null">
        allowed_pet_size_list,
      </if>
      <if test="petSizeFilter != null">
        pet_size_filter,
      </if>
      <if test="maxDuration != null">
        max_duration,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="taxId != null">
        #{taxId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="inactive != null">
        #{inactive,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="colorCode != null">
        #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="showBasePrice != null">
        #{showBasePrice,jdbcType=TINYINT},
      </if>
      <if test="bookOnlineAvailable != null">
        #{bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="isAllStaff != null">
        #{isAllStaff,jdbcType=TINYINT},
      </if>
      <if test="breedFilter != null">
        #{breedFilter,jdbcType=TINYINT},
      </if>
      <if test="weightFilter != null">
        #{weightFilter,jdbcType=TINYINT},
      </if>
      <if test="weightDownLimit != null">
        #{weightDownLimit,jdbcType=DECIMAL},
      </if>
      <if test="weightUpLimit != null">
        #{weightUpLimit,jdbcType=DECIMAL},
      </if>
      <if test="coatFilter != null">
        #{coatFilter,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="isAllLocation != null">
        #{isAllLocation,jdbcType=TINYINT},
      </if>
      <if test="images != null">
        #{images,jdbcType=CHAR},
      </if>
      <if test="serviceItemType != null">
        #{serviceItemType,jdbcType=INTEGER},
      </if>
      <if test="priceUnit != null">
        #{priceUnit,jdbcType=INTEGER},
      </if>
      <if test="addToCommission != null">
        #{addToCommission,jdbcType=BIT},
      </if>
      <if test="canTip != null">
        #{canTip,jdbcType=BIT},
      </if>
      <if test="requireDedicatedStaff != null">
        #{requireDedicatedStaff,jdbcType=BIT},
      </if>
      <if test="requireDedicatedLodging != null">
        #{requireDedicatedLodging,jdbcType=BIT},
      </if>
      <if test="lodgingFilter != null">
        #{lodgingFilter,jdbcType=BIT},
      </if>
      <if test="allowedLodgingList != null">
        #{allowedLodgingList,jdbcType=CHAR},
      </if>
      <if test="serviceFilter != null">
        #{serviceFilter,jdbcType=BIT},
      </if>
      <if test="allowedPetSizeList != null">
        #{allowedPetSizeList,jdbcType=CHAR},
      </if>
      <if test="petSizeFilter != null">
        #{petSizeFilter,jdbcType=BIT},
      </if>
      <if test="maxDuration != null">
        #{maxDuration,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.taxId != null">
        tax_id = #{record.taxId,jdbcType=INTEGER},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.inactive != null">
        inactive = #{record.inactive,jdbcType=TINYINT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.colorCode != null">
        color_code = #{record.colorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.showBasePrice != null">
        show_base_price = #{record.showBasePrice,jdbcType=TINYINT},
      </if>
      <if test="record.bookOnlineAvailable != null">
        book_online_available = #{record.bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="record.isAllStaff != null">
        is_all_staff = #{record.isAllStaff,jdbcType=TINYINT},
      </if>
      <if test="record.breedFilter != null">
        breed_filter = #{record.breedFilter,jdbcType=TINYINT},
      </if>
      <if test="record.weightFilter != null">
        weight_filter = #{record.weightFilter,jdbcType=TINYINT},
      </if>
      <if test="record.weightDownLimit != null">
        weight_down_limit = #{record.weightDownLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.weightUpLimit != null">
        weight_up_limit = #{record.weightUpLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.coatFilter != null">
        coat_filter = #{record.coatFilter,jdbcType=TINYINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.isAllLocation != null">
        is_all_location = #{record.isAllLocation,jdbcType=TINYINT},
      </if>
      <if test="record.images != null">
        images = #{record.images,jdbcType=CHAR},
      </if>
      <if test="record.serviceItemType != null">
        service_item_type = #{record.serviceItemType,jdbcType=INTEGER},
      </if>
      <if test="record.priceUnit != null">
        price_unit = #{record.priceUnit,jdbcType=INTEGER},
      </if>
      <if test="record.addToCommission != null">
        add_to_commission = #{record.addToCommission,jdbcType=BIT},
      </if>
      <if test="record.canTip != null">
        can_tip = #{record.canTip,jdbcType=BIT},
      </if>
      <if test="record.requireDedicatedStaff != null">
        require_dedicated_staff = #{record.requireDedicatedStaff,jdbcType=BIT},
      </if>
      <if test="record.requireDedicatedLodging != null">
        require_dedicated_lodging = #{record.requireDedicatedLodging,jdbcType=BIT},
      </if>
      <if test="record.lodgingFilter != null">
        lodging_filter = #{record.lodgingFilter,jdbcType=BIT},
      </if>
      <if test="record.allowedLodgingList != null">
        allowed_lodging_list = #{record.allowedLodgingList,jdbcType=CHAR},
      </if>
      <if test="record.serviceFilter != null">
        service_filter = #{record.serviceFilter,jdbcType=BIT},
      </if>
      <if test="record.allowedPetSizeList != null">
        allowed_pet_size_list = #{record.allowedPetSizeList,jdbcType=CHAR},
      </if>
      <if test="record.petSizeFilter != null">
        pet_size_filter = #{record.petSizeFilter,jdbcType=BIT},
      </if>
      <if test="record.maxDuration != null">
        max_duration = #{record.maxDuration,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      category_id = #{record.categoryId,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=TINYINT},
      tax_id = #{record.taxId,jdbcType=INTEGER},
      price = #{record.price,jdbcType=DECIMAL},
      duration = #{record.duration,jdbcType=INTEGER},
      inactive = #{record.inactive,jdbcType=TINYINT},
      sort = #{record.sort,jdbcType=INTEGER},
      color_code = #{record.colorCode,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      show_base_price = #{record.showBasePrice,jdbcType=TINYINT},
      book_online_available = #{record.bookOnlineAvailable,jdbcType=TINYINT},
      is_all_staff = #{record.isAllStaff,jdbcType=TINYINT},
      breed_filter = #{record.breedFilter,jdbcType=TINYINT},
      weight_filter = #{record.weightFilter,jdbcType=TINYINT},
      weight_down_limit = #{record.weightDownLimit,jdbcType=DECIMAL},
      weight_up_limit = #{record.weightUpLimit,jdbcType=DECIMAL},
      coat_filter = #{record.coatFilter,jdbcType=TINYINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      is_all_location = #{record.isAllLocation,jdbcType=TINYINT},
      images = #{record.images,jdbcType=CHAR},
      service_item_type = #{record.serviceItemType,jdbcType=INTEGER},
      price_unit = #{record.priceUnit,jdbcType=INTEGER},
      add_to_commission = #{record.addToCommission,jdbcType=BIT},
      can_tip = #{record.canTip,jdbcType=BIT},
      require_dedicated_staff = #{record.requireDedicatedStaff,jdbcType=BIT},
      require_dedicated_lodging = #{record.requireDedicatedLodging,jdbcType=BIT},
      lodging_filter = #{record.lodgingFilter,jdbcType=BIT},
      allowed_lodging_list = #{record.allowedLodgingList,jdbcType=CHAR},
      service_filter = #{record.serviceFilter,jdbcType=BIT},
      allowed_pet_size_list = #{record.allowedPetSizeList,jdbcType=CHAR},
      pet_size_filter = #{record.petSizeFilter,jdbcType=BIT},
      max_duration = #{record.maxDuration,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="inactive != null">
        inactive = #{inactive,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="colorCode != null">
        color_code = #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="showBasePrice != null">
        show_base_price = #{showBasePrice,jdbcType=TINYINT},
      </if>
      <if test="bookOnlineAvailable != null">
        book_online_available = #{bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="isAllStaff != null">
        is_all_staff = #{isAllStaff,jdbcType=TINYINT},
      </if>
      <if test="breedFilter != null">
        breed_filter = #{breedFilter,jdbcType=TINYINT},
      </if>
      <if test="weightFilter != null">
        weight_filter = #{weightFilter,jdbcType=TINYINT},
      </if>
      <if test="weightDownLimit != null">
        weight_down_limit = #{weightDownLimit,jdbcType=DECIMAL},
      </if>
      <if test="weightUpLimit != null">
        weight_up_limit = #{weightUpLimit,jdbcType=DECIMAL},
      </if>
      <if test="coatFilter != null">
        coat_filter = #{coatFilter,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="isAllLocation != null">
        is_all_location = #{isAllLocation,jdbcType=TINYINT},
      </if>
      <if test="images != null">
        images = #{images,jdbcType=CHAR},
      </if>
      <if test="serviceItemType != null">
        service_item_type = #{serviceItemType,jdbcType=INTEGER},
      </if>
      <if test="priceUnit != null">
        price_unit = #{priceUnit,jdbcType=INTEGER},
      </if>
      <if test="addToCommission != null">
        add_to_commission = #{addToCommission,jdbcType=BIT},
      </if>
      <if test="canTip != null">
        can_tip = #{canTip,jdbcType=BIT},
      </if>
      <if test="requireDedicatedStaff != null">
        require_dedicated_staff = #{requireDedicatedStaff,jdbcType=BIT},
      </if>
      <if test="requireDedicatedLodging != null">
        require_dedicated_lodging = #{requireDedicatedLodging,jdbcType=BIT},
      </if>
      <if test="lodgingFilter != null">
        lodging_filter = #{lodgingFilter,jdbcType=BIT},
      </if>
      <if test="allowedLodgingList != null">
        allowed_lodging_list = #{allowedLodgingList,jdbcType=CHAR},
      </if>
      <if test="serviceFilter != null">
        service_filter = #{serviceFilter,jdbcType=BIT},
      </if>
      <if test="allowedPetSizeList != null">
        allowed_pet_size_list = #{allowedPetSizeList,jdbcType=CHAR},
      </if>
      <if test="petSizeFilter != null">
        pet_size_filter = #{petSizeFilter,jdbcType=BIT},
      </if>
      <if test="maxDuration != null">
        max_duration = #{maxDuration,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service
    set business_id = #{businessId,jdbcType=INTEGER},
      category_id = #{categoryId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      tax_id = #{taxId,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      duration = #{duration,jdbcType=INTEGER},
      inactive = #{inactive,jdbcType=TINYINT},
      sort = #{sort,jdbcType=INTEGER},
      color_code = #{colorCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      show_base_price = #{showBasePrice,jdbcType=TINYINT},
      book_online_available = #{bookOnlineAvailable,jdbcType=TINYINT},
      is_all_staff = #{isAllStaff,jdbcType=TINYINT},
      breed_filter = #{breedFilter,jdbcType=TINYINT},
      weight_filter = #{weightFilter,jdbcType=TINYINT},
      weight_down_limit = #{weightDownLimit,jdbcType=DECIMAL},
      weight_up_limit = #{weightUpLimit,jdbcType=DECIMAL},
      coat_filter = #{coatFilter,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=BIGINT},
      is_all_location = #{isAllLocation,jdbcType=TINYINT},
      images = #{images,jdbcType=CHAR},
      service_item_type = #{serviceItemType,jdbcType=INTEGER},
      price_unit = #{priceUnit,jdbcType=INTEGER},
      add_to_commission = #{addToCommission,jdbcType=BIT},
      can_tip = #{canTip,jdbcType=BIT},
      require_dedicated_staff = #{requireDedicatedStaff,jdbcType=BIT},
      require_dedicated_lodging = #{requireDedicatedLodging,jdbcType=BIT},
      lodging_filter = #{lodgingFilter,jdbcType=BIT},
      allowed_lodging_list = #{allowedLodgingList,jdbcType=CHAR},
      service_filter = #{serviceFilter,jdbcType=BIT},
      allowed_pet_size_list = #{allowedPetSizeList,jdbcType=CHAR},
      pet_size_filter = #{petSizeFilter,jdbcType=BIT},
      max_duration = #{maxDuration,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <sql id="Base_Column_List_With_Override">
    <!--
        from moe_grooming_service gs
            LEFT JOIN moe_grooming_service_location gsl
                ON gsl.service_id = gs.id AND gsl.business_id = #{businessId} and gsl.is_deleted = 0
    -->
    gs.id, gs.business_id, gs.category_id, gs.name, gs.description, gs.type, gs.inactive,
    gs.sort, gs.color_code, gs.status, gs.create_time, gs.update_time, gs.show_base_price, gs.book_online_available,
    gs.is_all_staff, gs.breed_filter, gs.weight_filter, gs.weight_down_limit, gs.weight_up_limit, gs.coat_filter,
    gs.company_id, gs.is_all_location, gs.pet_size_filter, gs.allowed_pet_size_list, gs.service_filter, gs.service_item_type, gs.price_unit,
	CASE WHEN gsl.tax_id is not NULL THEN gsl.tax_id ELSE gs.tax_id END as tax_id,
	CASE WHEN gsl.price is not NULL THEN gsl.price ELSE gs.price END as price,
	CASE WHEN gsl.duration is not NULL THEN gsl.duration ELSE gs.duration END as duration
  </sql>


  <resultMap id="PetServiceDTOResultMap" type="com.moego.server.grooming.dto.PetServiceDTO">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="inactive" jdbcType="TINYINT" property="inactive" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="color_code" jdbcType="VARCHAR" property="colorCode" />
    <result column="show_base_price" jdbcType="TINYINT" property="showBasePrice" />
    <result column="book_online_available" jdbcType="TINYINT" property="bookOnlineAvailable" />
    <result column="weight_filter" jdbcType="TINYINT" property="weightFilter" />
    <result column="breed_filter" jdbcType="TINYINT" property="breedFilter" />
    <result column="weight_down_limit" jdbcType="DECIMAL" property="weightDownLimit" />
    <result column="weight_up_limit" jdbcType="DECIMAL" property="weightUpLimit" />
    <result column="coat_filter" jdbcType="TINYINT" property="coatFilter" />
    <result column="pet_size_filter" jdbcType="TINYINT" property="petSizeFilter" />
    <result column="allowed_pet_size_list" jdbcType="VARCHAR" property="allowedPetSizeList" />
  </resultMap>

  <resultMap id="OBPetServiceDTOResultMap" type="com.moego.server.grooming.web.dto.ob.OBPetServiceDTO">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="inactive" jdbcType="TINYINT" property="inactive" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="color_code" jdbcType="VARCHAR" property="colorCode" />
    <result column="show_base_price" jdbcType="TINYINT" property="showBasePrice" />
    <result column="book_online_available" jdbcType="TINYINT" property="bookOnlineAvailable" />
    <result column="is_all_staff" jdbcType="TINYINT" property="isAllStaff" />
    <result column="weight_filter" jdbcType="TINYINT" property="weightFilter" />
    <result column="breed_filter" jdbcType="TINYINT" property="breedFilter" />
    <result column="weight_down_limit" jdbcType="DECIMAL" property="weightDownLimit" />
    <result column="weight_up_limit" jdbcType="DECIMAL" property="weightUpLimit" />
    <result column="coat_filter" jdbcType="TINYINT" property="coatFilter" />
    <result column="pet_size_filter" jdbcType="TINYINT" property="petSizeFilter" />
    <result column="allowed_pet_size_list" jdbcType="VARCHAR" property="allowedPetSizeList" />
    <result column="service_filter" jdbcType="TINYINT" property="addOnServiceFilter"/>
  </resultMap>

  <resultMap id="ReportServiceMap" type="com.moego.server.grooming.service.dto.ReportServiceDto">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="tax_id" jdbcType="INTEGER" property="taxId" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="inactive" jdbcType="TINYINT" property="inactive" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="color_code" jdbcType="VARCHAR" property="colorCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="category" jdbcType="VARCHAR" property="category" />

    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="total_list_price" jdbcType="DECIMAL" property="totalListPrice" />
    <result column="total_sale_price" jdbcType="DECIMAL" property="totalSalePrice" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
  </resultMap>

  <update id="batchUpdateSort">
    <foreach collection="serviceList" item="service" separator=";">
      UPDATE moe_grooming_service SET sort = #{service.sort}, update_time = unix_timestamp(now())
      WHERE id = #{service.id}
    </foreach>
  </update>

  <select id="getMaxSortByCategoryId" resultType="java.lang.Integer">
      SELECT MAX(sort) FROM moe_grooming_service
      WHERE business_id = #{businessId}
      AND category_id = #{categoryId}
      AND inactive = #{inactive}
  </select>
  <select id="getMaxSortByCategoryIdForCid" resultType="java.lang.Integer">
      SELECT MAX(sort) FROM moe_grooming_service
      WHERE company_id = #{companyId}
      AND category_id = #{categoryId}
      AND inactive = #{inactive}
  </select>

  <update id="setServiceCategoryDefaultForCid">
    UPDATE moe_grooming_service
    SET category_id = 0, update_time = unix_timestamp(now())
    WHERE category_id = #{oldCategoryId}
      and company_id = #{companyId}
  </update>

  <update id="batchUpdateCategoryId">
    UPDATE moe_grooming_service
    SET category_id = #{toCategoryId}, update_time = unix_timestamp(now())
    WHERE category_id = #{fromCategoryId}
      and company_id = #{companyId}
  </update>

  <update id="updateByPrimaryKeySelectiveWithBidCid" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingService">
    update moe_grooming_service
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="inactive != null">
        inactive = #{inactive,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="colorCode != null">
        color_code = #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="showBasePrice != null">
        show_base_price = #{showBasePrice,jdbcType=TINYINT},
      </if>
      <if test="bookOnlineAvailable != null">
        book_online_available = #{bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="isAllStaff != null">
        is_all_staff = #{isAllStaff,jdbcType=TINYINT},
      </if>
      <if test="breedFilter != null">
        breed_filter = #{breedFilter,jdbcType=TINYINT},
      </if>
      <if test="weightFilter != null">
        weight_filter = #{weightFilter,jdbcType=TINYINT},
      </if>
      <if test="weightDownLimit != null">
        weight_down_limit = #{weightDownLimit,jdbcType=DECIMAL},
      </if>
      <if test="weightUpLimit != null">
        weight_up_limit = #{weightUpLimit,jdbcType=DECIMAL},
      </if>
      <if test="coatFilter != null">
        coat_filter = #{coatFilter,jdbcType=TINYINT},
      </if>
      <if test="isAllLocation != null">
        is_all_location = #{isAllLocation,jdbcType=TINYINT},
      </if>
      <if test="petSizeFilter != null">
        pet_size_filter = #{petSizeFilter,jdbcType=BIT},
      </if>
      <if test="allowedPetSizeList != null">
        allowed_pet_size_list = #{allowedPetSizeList,jdbcType=CHAR},
      </if>
    </set>
    <where>
        id = #{id,jdbcType=INTEGER}
    <if test="businessId != null">
      AND business_id = #{businessId,jdbcType=INTEGER}
    </if>
    <if test="companyId != null">
      AND company_id = #{companyId,jdbcType=BIGINT}
    </if>
    </where>
  </update>

  <select id="selectCompanyServiceWithBids" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service
    where
    company_id = #{companyId,jdbcType=BIGINT}
    AND type = #{type}
    AND status = 1
    AND service_item_type = 1
    <if test="businessIds != null">
    AND (
      is_all_location = 1
        OR (
          is_all_location = 0
        AND id in (select service_id from moe_grooming_service_location
                  where
                  company_id = #{companyId,jdbcType=BIGINT}
                  AND
                  `business_id` IN
                  <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
                      #{item}
                    </foreach>
                  AND is_deleted = 0
                )
            )
    )
    </if>
    <if test="inactive != null">
      AND inactive = #{inactive}
    </if>
    order by category_id asc,inactive,sort desc,id desc
  </select>

  <select id="selectByBusinessIdType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_With_Override" />
    from moe_grooming_service gs
      LEFT JOIN moe_grooming_service_location gsl
      ON gsl.service_id = gs.id AND gsl.business_id = #{businessId} and gsl.is_deleted = 0
    where
    gs.company_id = #{companyId,jdbcType=BIGINT}
    <if test="type != null">
      AND gs.type = #{type}
    </if>
    AND gs.status = 1
    AND (
      gs.is_all_location = 1
        OR (
        gs.is_all_location = 0
        AND gs.id in (select service_id from moe_grooming_service_location
                  where
                  company_id = #{companyId,jdbcType=BIGINT}
                  AND
                  business_id = #{businessId}
                  AND is_deleted = 0
                )
            )
    )
    <if test="inactive != null">
      AND gs.inactive = #{inactive}
    </if>
    <if test="serviceItemType != null">
      AND gs.service_item_type = #{serviceItemType}
    </if>
    order by gs.category_id asc,gs.inactive,gs.sort desc,gs.id desc
  </select>

  <select id="getServiceCountWithName" resultType="java.lang.Integer">
    SELECT count(*) as count FROM moe_grooming_service
    <where>
    <if test="companyId != null">
      and company_id = #{companyId}
    </if>
    <if test="businessId != null">
      and business_id = #{businessId}
    </if>
    AND status = 1
      AND service_item_type = 1
    AND type = #{type}
    AND name=#{name}
    <if test="updateServiceId != null">
      AND id != #{updateServiceId}
    </if>
    </where>
  </select>

  <select id="selectByBusinessIdTypeKeyword" resultMap="BaseResultMap">
    select
    gs.id, gs.business_id, gs.category_id, gs.name, gs.description,
    gs.type, gs.tax_id, gs.price, gs.duration, gs.inactive,
    gs.sort, gs.color_code, gs.status, gs.show_base_price, gs.service_item_type, gs.price_unit
    from moe_grooming_service gs left join moe_grooming_service_category gsc on gs.category_id = gsc.id
    where gs.business_id = #{businessId}
    and gs.type = #{type}
    and gs.status = 1
    and gs.service_item_type = 1
    and gs.inactive = #{inactive}
    <if test="keyword != null and keyword != ''">
      and gs.name like CONCAT('%',#{keyword},'%')
    </if>
    order by case when gsc.sort is null then 0 else 1 end, gsc.sort desc, gs.sort desc, gs.id desc
  </select>

  <select id="selectByCidBidTypeKeyword" resultMap="BaseResultMap">
    select
    gs.id, gs.business_id, gs.category_id, gs.name, gs.description,
    gs.type, gs.tax_id, gs.price, gs.duration, gs.inactive,
    gs.sort, gs.color_code, gs.status, gs.show_base_price, gs.service_item_type, gs.price_unit
    from moe_grooming_service gs left join moe_grooming_service_category gsc on gs.category_id = gsc.id
    where
    gs.company_id = #{companyId,jdbcType=BIGINT}
    and gs.type = #{type}
    and gs.status = 1
    and gs.service_item_type = 1
    <if test="businessId != null">
      and (
        gs.is_all_location = 1 OR (
          gs.is_all_location = 0 and gs.id in (
            select service_id from moe_grooming_service_location
                              where company_id = #{companyId,jdbcType=BIGINT} and business_id = #{businessId} AND is_deleted = 0
            )
        )
      )
    </if>
    and gs.inactive = #{inactive}
    <if test="keyword != null and keyword != ''">
      and gs.name like CONCAT('%',#{keyword},'%')
    </if>
    order by case when gsc.sort is null then 0 else 1 end, gsc.sort desc, gs.sort desc, gs.id desc
  </select>

  <select id="getServicesByServiceIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM moe_grooming_service
    <where>
      <if test="serviceIds != null">
        AND `id` IN
        <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
  </select>


  <select id="getServicesByBusinessIdServiceIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM moe_grooming_service
    where
    business_id = #{businessId}
    AND `id` IN
    <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

    <select id="getServicesByBusinessIdServiceIdsNew" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List_With_Override" />
      from moe_grooming_service gs
          LEFT JOIN moe_grooming_service_location gsl
              ON gsl.service_id = gs.id AND gsl.business_id = #{businessId} and gsl.is_deleted = 0
      <where>
        gs.company_id = #{companyId}
        and gs.`id` IN
        <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
      </where>
    </select>
    <select id="getServicesByCompanyIdServiceIds" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List" />
      from moe_grooming_service
      <where>
        `id` IN
        <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
      and company_id = #{companyId,jdbcType=BIGINT}
      </where>
    </select>

  <select id="selectServiceByBusinessId" resultMap="PetServiceDTOResultMap">
  select
  <include refid="Base_Column_List" />
  from moe_grooming_service
  where status = 1
  and business_id = #{businessId}
  <if test="type != null">
    and type = #{type}
  </if>
    and service_item_type = 1
</select>

  <select id="selectServiceByBusinessIdNew" resultMap="PetServiceDTOResultMap">
    select
    gs.id,gs.business_id,gs.category_id,gs.name,gs.description,gs.type,gs.inactive,
    gs.sort,gs.color_code,gs.show_base_price,gs.book_online_available,gs.weight_filter,
    gs.breed_filter,gs.weight_down_limit,gs.weight_up_limit,gs.coat_filter,gs.pet_size_filter,gs.allowed_pet_size_list,
    CASE WHEN gsl.price is not NULL THEN gsl.price ELSE gs.price END as price,
    CASE WHEN gsl.duration is not NULL THEN gsl.duration ELSE gs.duration END as duration
    from moe_grooming_service gs
        LEFT JOIN moe_grooming_service_location gsl
            ON gsl.service_id = gs.id AND gsl.business_id = #{businessId} and gsl.is_deleted = 0
    <where>
      gs.status = 1
      <if test="type != null">
        and gs.type = #{type}
      </if>
      AND (
      gs.is_all_location = 1
        OR (
        gs.is_all_location = 0
          AND gs.id in (
            select service_id from moe_grooming_service_location
                where
                company_id = #{companyId,jdbcType=BIGINT}
                AND
                `business_id` = #{businessId}
                AND is_deleted = 0
          )
        )
      )
    and gs.company_id = #{companyId,jdbcType=BIGINT}
      and gs.service_item_type = 1
    </where>

  </select>

  <select id="selectActiveServiceByBusinessId" resultMap="PetServiceDTOResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service
    where status = 1
    and business_id = #{businessId}
    and inactive = 0
    <if test="type != null">
      and type = #{type}
    </if>
    and service_item_type = 1
    order by sort desc, id desc
  </select>

  <select id="selectActiveServiceByBusinessIdNew" resultMap="PetServiceDTOResultMap">
    select
    <include refid="Base_Column_List_With_Override" />
    from moe_grooming_service gs
    LEFT JOIN moe_grooming_service_location gsl
    ON gsl.service_id = gs.id AND gsl.business_id = #{businessId} and gsl.is_deleted = 0
    where gs.status = 1
      and gs.company_id = #{companyId}
      and gs.inactive = 0
    and gs.service_item_type = 1
    <if test="type != null">
      and gs.type = #{type}
    </if>
    AND (
      gs.is_all_location = 1
      OR (
        gs.is_all_location = 0
        AND gs.id in (
          select service_id from moe_grooming_service_location
          where
          company_id = #{companyId,jdbcType=BIGINT}
          AND
          `business_id` = #{businessId}
          AND is_deleted = 0
        )
      )
    )
    order by gs.sort desc, gs.id desc
  </select>

  <select id="selectOBServiceByBusinessId" resultMap="OBPetServiceDTOResultMap">
    select
    id, business_id, category_id, name, description, type,  price, duration, inactive, sort,
    color_code, show_base_price, book_online_available, is_all_staff, weight_filter, breed_filter,
    weight_down_limit, weight_up_limit, coat_filter, pet_size_filter, allowed_pet_size_list, service_filter
    FROM moe_grooming_service
    where  status = 1
    and business_id = #{businessId}
    and book_online_available = 1
    and inactive = 0
    and service_item_type = 1
    <if test="type!=null">and type = #{type} </if>
  </select>

  <select id="selectOBServiceByBusinessIdNew" resultMap="OBPetServiceDTOResultMap">
    select
    gs.id, gs.business_id, gs.category_id, gs.name, gs.description, gs.type, gs.inactive, gs.sort,
    gs.color_code, gs.show_base_price, gs.book_online_available, gs.is_all_staff, gs.weight_filter, gs.breed_filter,
    gs.weight_down_limit, gs.weight_up_limit, gs.coat_filter, gs.pet_size_filter, gs.allowed_pet_size_list, gs.service_filter,
    CASE WHEN gsl.price is not NULL THEN gsl.price ELSE gs.price END as price,
    CASE WHEN gsl.duration is not NULL THEN gsl.duration ELSE gs.duration END as duration
    from moe_grooming_service gs
        LEFT JOIN moe_grooming_service_location gsl
            ON gsl.service_id = gs.id AND gsl.business_id = #{businessId} and gsl.is_deleted = 0
    <where>
      gs.status = 1
      <if test="type != null">
        and gs.type = #{type}
      </if>
      AND (
      gs.is_all_location = 1
        OR (
        gs.is_all_location = 0
          AND gs.id in (
            select service_id from moe_grooming_service_location
                where
                company_id = #{companyId,jdbcType=BIGINT}
                AND
                `business_id` = #{businessId}
                AND is_deleted = 0
          )
        )
      )
    and gs.company_id = #{companyId,jdbcType=BIGINT}
    and gs.inactive = 0
      and gs.service_item_type = 1
    </where>
  </select>

  <select id="selectServiceCountByTagId" resultType="int">
    select
        count(*)
    FROM moe_grooming_service
    where status = 1
      and company_id = #{companyId}
      and tax_id = #{taxId}
      or id in (
        select service_id from moe_grooming_service_location
          where company_id = #{companyId,jdbcType=BIGINT} AND is_deleted = 0 and tax_id = #{taxId}
      )
  </select>

  <select id="selectAllByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    moe_grooming_service
    where
    company_id = #{companyId}
    <if test="categoryId != null">
      and category_id = #{categoryId}
    </if>
    and status = 1
    and service_item_type = 1
  </select>

  <select id="selectByCompanyIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    moe_grooming_service
    where
    company_id in
    <foreach close=")" collection="companyIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and inactive = 0
    and status = 1
    and service_item_type = 1
  </select>

    <select id="queryServiceWithCategoryByServiceIds" resultMap="ReportServiceMap">
        SELECT s.id, s.name, sc.name AS category
        FROM moe_grooming.moe_grooming_service s
            LEFT JOIN moe_grooming.moe_grooming_service_category sc ON s.category_id = sc.id
        WHERE s.business_id = #{businessId}
        AND s.id IN
        <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
      and s.service_item_type = 1
    </select>

    <select id="queryServiceWithCategoryByServiceIdsNew" resultMap="ReportServiceMap">
        SELECT s.id, s.name, sc.name AS category
        FROM moe_grooming.moe_grooming_service s
            LEFT JOIN moe_grooming.moe_grooming_service_category sc ON s.category_id = sc.id
        WHERE s.company_id = #{companyId}
        AND s.id IN
        <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="batchUpdateService">
      <foreach collection="serviceList" item="service" separator=";">
        UPDATE moe_grooming_service
        SET book_online_available = #{service.bookOnlineAvailable},
        show_base_price = #{service.showBasePrice},
        is_all_staff = #{service.isAllStaff},
        update_time = #{service.updateTime}
        WHERE id = #{service.id}
        and business_id = #{service.businessId}
        and service_item_type = 1
      </foreach>
    </update>

  <select id="selectByBusinessIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    moe_grooming_service
    where
    business_id in
    <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and inactive = 0
    and status = 1
  </select>
</mapper>
