package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_invoice_apply_package
 */
public class MoeGroomingInvoiceApplyPackage {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   invoice主键id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.invoice_id
     *
     * @mbg.generated
     */
    private Integer invoiceId;

    /**
     * Database Column Remarks:
     *   invoice item id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.invoice_item_id
     *
     * @mbg.generated
     */
    private Integer invoiceItemId;

    /**
     * Database Column Remarks:
     *   package id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.package_id
     *
     * @mbg.generated
     */
    private Integer packageId;

    /**
     * Database Column Remarks:
     *   service id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   package service id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.package_service_id
     *
     * @mbg.generated
     */
    private Integer packageServiceId;

    /**
     * Database Column Remarks:
     *   package name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.package_name
     *
     * @mbg.generated
     */
    private String packageName;

    /**
     * Database Column Remarks:
     *   service name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.service_name
     *
     * @mbg.generated
     */
    private String serviceName;

    /**
     * Database Column Remarks:
     *   数量
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.quantity
     *
     * @mbg.generated
     */
    private Integer quantity;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   状态：1-正常，2-失效
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_apply_package.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.id
     *
     * @return the value of moe_grooming_invoice_apply_package.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.id
     *
     * @param id the value for moe_grooming_invoice_apply_package.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.invoice_id
     *
     * @return the value of moe_grooming_invoice_apply_package.invoice_id
     *
     * @mbg.generated
     */
    public Integer getInvoiceId() {
        return invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.invoice_id
     *
     * @param invoiceId the value for moe_grooming_invoice_apply_package.invoice_id
     *
     * @mbg.generated
     */
    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.invoice_item_id
     *
     * @return the value of moe_grooming_invoice_apply_package.invoice_item_id
     *
     * @mbg.generated
     */
    public Integer getInvoiceItemId() {
        return invoiceItemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.invoice_item_id
     *
     * @param invoiceItemId the value for moe_grooming_invoice_apply_package.invoice_item_id
     *
     * @mbg.generated
     */
    public void setInvoiceItemId(Integer invoiceItemId) {
        this.invoiceItemId = invoiceItemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.package_id
     *
     * @return the value of moe_grooming_invoice_apply_package.package_id
     *
     * @mbg.generated
     */
    public Integer getPackageId() {
        return packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.package_id
     *
     * @param packageId the value for moe_grooming_invoice_apply_package.package_id
     *
     * @mbg.generated
     */
    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.service_id
     *
     * @return the value of moe_grooming_invoice_apply_package.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.service_id
     *
     * @param serviceId the value for moe_grooming_invoice_apply_package.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.package_service_id
     *
     * @return the value of moe_grooming_invoice_apply_package.package_service_id
     *
     * @mbg.generated
     */
    public Integer getPackageServiceId() {
        return packageServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.package_service_id
     *
     * @param packageServiceId the value for moe_grooming_invoice_apply_package.package_service_id
     *
     * @mbg.generated
     */
    public void setPackageServiceId(Integer packageServiceId) {
        this.packageServiceId = packageServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.package_name
     *
     * @return the value of moe_grooming_invoice_apply_package.package_name
     *
     * @mbg.generated
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.package_name
     *
     * @param packageName the value for moe_grooming_invoice_apply_package.package_name
     *
     * @mbg.generated
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.service_name
     *
     * @return the value of moe_grooming_invoice_apply_package.service_name
     *
     * @mbg.generated
     */
    public String getServiceName() {
        return serviceName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.service_name
     *
     * @param serviceName the value for moe_grooming_invoice_apply_package.service_name
     *
     * @mbg.generated
     */
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName == null ? null : serviceName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.quantity
     *
     * @return the value of moe_grooming_invoice_apply_package.quantity
     *
     * @mbg.generated
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.quantity
     *
     * @param quantity the value for moe_grooming_invoice_apply_package.quantity
     *
     * @mbg.generated
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.create_time
     *
     * @return the value of moe_grooming_invoice_apply_package.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.create_time
     *
     * @param createTime the value for moe_grooming_invoice_apply_package.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.update_time
     *
     * @return the value of moe_grooming_invoice_apply_package.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.update_time
     *
     * @param updateTime the value for moe_grooming_invoice_apply_package.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_invoice_apply_package.status
     *
     * @return the value of moe_grooming_invoice_apply_package.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_invoice_apply_package.status
     *
     * @param status the value for moe_grooming_invoice_apply_package.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }
}
