package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_repeat
 */
public class MoeGroomingRepeat {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   客户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   商户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   商户员工id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   重复类型 1 day 2 week 3 month
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.repeat_type
     *
     * @mbg.generated
     */
    private Byte repeatType;

    /**
     * Database Column Remarks:
     *   重复周期间隔type为1或2有此值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.repeat_every
     *
     * @mbg.generated
     */
    private Integer repeatEvery;

    /**
     * Database Column Remarks:
     *   重复条件当type为2有此值，每周几
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.repeat_by
     *
     * @mbg.generated
     */
    private String repeatBy;

    /**
     * Database Column Remarks:
     *   repeat开始时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.starts_on
     *
     * @mbg.generated
     */
    private Date startsOn;

    /**
     * Database Column Remarks:
     *   重复次数（周重复使用）
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.times
     *
     * @mbg.generated
     */
    private Integer times;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   状态 1-正常 2-关闭
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   repeat 结束时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.end_on
     *
     * @mbg.generated
     */
    private String endOn;

    /**
     * Database Column Remarks:
     *   是否开启准备过期提醒 1是 2否
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.is_notice
     *
     * @mbg.generated
     */
    private Byte isNotice;

    /**
     * Database Column Remarks:
     *   商家设置的结束时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.set_end_on
     *
     * @mbg.generated
     */
    private Date setEndOn;

    /**
     * Database Column Remarks:
     *   type为3有此值，每月的第几天为2每月第几个星期几为1
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.repeat_every_type
     *
     * @mbg.generated
     */
    private Byte repeatEveryType;

    /**
     * Database Column Remarks:
     *   当repeat_every_type为2需指定每月的几号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.month_day
     *
     * @mbg.generated
     */
    private Byte monthDay;

    /**
     * Database Column Remarks:
     *   当repeat_every_type为1需指定每月的第几个星期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.month_week_times
     *
     * @mbg.generated
     */
    private Byte monthWeekTimes;

    /**
     * Database Column Remarks:
     *   当repeat_every_type为1需指定每月的周几
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.month_week_day
     *
     * @mbg.generated
     */
    private Byte monthWeekDay;

    /**
     * Database Column Remarks:
     *   1次数2设置结束时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.type
     *
     * @mbg.generated
     */
    private String type;

    /**
     * Database Column Remarks:
     *   ss for repeat flag: 0-normal repeat, 1-ss repeat
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.ss_flag
     *
     * @mbg.generated
     */
    private Byte ssFlag;

    /**
     * Database Column Remarks:
     *   ss settings: before available days
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.ss_before_days
     *
     * @mbg.generated
     */
    private Integer ssBeforeDays;

    /**
     * Database Column Remarks:
     *   ss settings: after available days
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.ss_after_days
     *
     * @mbg.generated
     */
    private Integer ssAfterDays;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   每周哪几天
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_repeat.repeat_by_days
     *
     * @mbg.generated
     */
    private String repeatByDays;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.id
     *
     * @return the value of moe_grooming_repeat.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.id
     *
     * @param id the value for moe_grooming_repeat.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.customer_id
     *
     * @return the value of moe_grooming_repeat.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.customer_id
     *
     * @param customerId the value for moe_grooming_repeat.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.business_id
     *
     * @return the value of moe_grooming_repeat.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.business_id
     *
     * @param businessId the value for moe_grooming_repeat.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.staff_id
     *
     * @return the value of moe_grooming_repeat.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.staff_id
     *
     * @param staffId the value for moe_grooming_repeat.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.repeat_type
     *
     * @return the value of moe_grooming_repeat.repeat_type
     *
     * @mbg.generated
     */
    public Byte getRepeatType() {
        return repeatType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.repeat_type
     *
     * @param repeatType the value for moe_grooming_repeat.repeat_type
     *
     * @mbg.generated
     */
    public void setRepeatType(Byte repeatType) {
        this.repeatType = repeatType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.repeat_every
     *
     * @return the value of moe_grooming_repeat.repeat_every
     *
     * @mbg.generated
     */
    public Integer getRepeatEvery() {
        return repeatEvery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.repeat_every
     *
     * @param repeatEvery the value for moe_grooming_repeat.repeat_every
     *
     * @mbg.generated
     */
    public void setRepeatEvery(Integer repeatEvery) {
        this.repeatEvery = repeatEvery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.repeat_by
     *
     * @return the value of moe_grooming_repeat.repeat_by
     *
     * @mbg.generated
     */
    public String getRepeatBy() {
        return repeatBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.repeat_by
     *
     * @param repeatBy the value for moe_grooming_repeat.repeat_by
     *
     * @mbg.generated
     */
    public void setRepeatBy(String repeatBy) {
        this.repeatBy = repeatBy == null ? null : repeatBy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.starts_on
     *
     * @return the value of moe_grooming_repeat.starts_on
     *
     * @mbg.generated
     */
    public Date getStartsOn() {
        return startsOn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.starts_on
     *
     * @param startsOn the value for moe_grooming_repeat.starts_on
     *
     * @mbg.generated
     */
    public void setStartsOn(Date startsOn) {
        this.startsOn = startsOn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.times
     *
     * @return the value of moe_grooming_repeat.times
     *
     * @mbg.generated
     */
    public Integer getTimes() {
        return times;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.times
     *
     * @param times the value for moe_grooming_repeat.times
     *
     * @mbg.generated
     */
    public void setTimes(Integer times) {
        this.times = times;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.create_time
     *
     * @return the value of moe_grooming_repeat.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.create_time
     *
     * @param createTime the value for moe_grooming_repeat.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.update_time
     *
     * @return the value of moe_grooming_repeat.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.update_time
     *
     * @param updateTime the value for moe_grooming_repeat.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.status
     *
     * @return the value of moe_grooming_repeat.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.status
     *
     * @param status the value for moe_grooming_repeat.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.end_on
     *
     * @return the value of moe_grooming_repeat.end_on
     *
     * @mbg.generated
     */
    public String getEndOn() {
        return endOn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.end_on
     *
     * @param endOn the value for moe_grooming_repeat.end_on
     *
     * @mbg.generated
     */
    public void setEndOn(String endOn) {
        this.endOn = endOn == null ? null : endOn.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.is_notice
     *
     * @return the value of moe_grooming_repeat.is_notice
     *
     * @mbg.generated
     */
    public Byte getIsNotice() {
        return isNotice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.is_notice
     *
     * @param isNotice the value for moe_grooming_repeat.is_notice
     *
     * @mbg.generated
     */
    public void setIsNotice(Byte isNotice) {
        this.isNotice = isNotice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.set_end_on
     *
     * @return the value of moe_grooming_repeat.set_end_on
     *
     * @mbg.generated
     */
    public Date getSetEndOn() {
        return setEndOn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.set_end_on
     *
     * @param setEndOn the value for moe_grooming_repeat.set_end_on
     *
     * @mbg.generated
     */
    public void setSetEndOn(Date setEndOn) {
        this.setEndOn = setEndOn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.repeat_every_type
     *
     * @return the value of moe_grooming_repeat.repeat_every_type
     *
     * @mbg.generated
     */
    public Byte getRepeatEveryType() {
        return repeatEveryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.repeat_every_type
     *
     * @param repeatEveryType the value for moe_grooming_repeat.repeat_every_type
     *
     * @mbg.generated
     */
    public void setRepeatEveryType(Byte repeatEveryType) {
        this.repeatEveryType = repeatEveryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.month_day
     *
     * @return the value of moe_grooming_repeat.month_day
     *
     * @mbg.generated
     */
    public Byte getMonthDay() {
        return monthDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.month_day
     *
     * @param monthDay the value for moe_grooming_repeat.month_day
     *
     * @mbg.generated
     */
    public void setMonthDay(Byte monthDay) {
        this.monthDay = monthDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.month_week_times
     *
     * @return the value of moe_grooming_repeat.month_week_times
     *
     * @mbg.generated
     */
    public Byte getMonthWeekTimes() {
        return monthWeekTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.month_week_times
     *
     * @param monthWeekTimes the value for moe_grooming_repeat.month_week_times
     *
     * @mbg.generated
     */
    public void setMonthWeekTimes(Byte monthWeekTimes) {
        this.monthWeekTimes = monthWeekTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.month_week_day
     *
     * @return the value of moe_grooming_repeat.month_week_day
     *
     * @mbg.generated
     */
    public Byte getMonthWeekDay() {
        return monthWeekDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.month_week_day
     *
     * @param monthWeekDay the value for moe_grooming_repeat.month_week_day
     *
     * @mbg.generated
     */
    public void setMonthWeekDay(Byte monthWeekDay) {
        this.monthWeekDay = monthWeekDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.type
     *
     * @return the value of moe_grooming_repeat.type
     *
     * @mbg.generated
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.type
     *
     * @param type the value for moe_grooming_repeat.type
     *
     * @mbg.generated
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.ss_flag
     *
     * @return the value of moe_grooming_repeat.ss_flag
     *
     * @mbg.generated
     */
    public Byte getSsFlag() {
        return ssFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.ss_flag
     *
     * @param ssFlag the value for moe_grooming_repeat.ss_flag
     *
     * @mbg.generated
     */
    public void setSsFlag(Byte ssFlag) {
        this.ssFlag = ssFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.ss_before_days
     *
     * @return the value of moe_grooming_repeat.ss_before_days
     *
     * @mbg.generated
     */
    public Integer getSsBeforeDays() {
        return ssBeforeDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.ss_before_days
     *
     * @param ssBeforeDays the value for moe_grooming_repeat.ss_before_days
     *
     * @mbg.generated
     */
    public void setSsBeforeDays(Integer ssBeforeDays) {
        this.ssBeforeDays = ssBeforeDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.ss_after_days
     *
     * @return the value of moe_grooming_repeat.ss_after_days
     *
     * @mbg.generated
     */
    public Integer getSsAfterDays() {
        return ssAfterDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.ss_after_days
     *
     * @param ssAfterDays the value for moe_grooming_repeat.ss_after_days
     *
     * @mbg.generated
     */
    public void setSsAfterDays(Integer ssAfterDays) {
        this.ssAfterDays = ssAfterDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.company_id
     *
     * @return the value of moe_grooming_repeat.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.company_id
     *
     * @param companyId the value for moe_grooming_repeat.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_repeat.repeat_by_days
     *
     * @return the value of moe_grooming_repeat.repeat_by_days
     *
     * @mbg.generated
     */
    public String getRepeatByDays() {
        return repeatByDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_repeat.repeat_by_days
     *
     * @param repeatByDays the value for moe_grooming_repeat.repeat_by_days
     *
     * @mbg.generated
     */
    public void setRepeatByDays(String repeatByDays) {
        this.repeatByDays = repeatByDays == null ? null : repeatByDays.trim();
    }
}
