package com.moego.server.grooming.service.client;

import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineDepositMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.web.vo.client.PaymentDetailVO;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.dto.PaymentDTO;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
@Service
@AllArgsConstructor
public class ClientPaymentService {

    private final MoeBookOnlineDepositMapper bookOnlineDepositMapper;

    private final IPaymentPaymentClient paymentClient;

    public PaymentDetailVO getPrepayPayment(MoeGroomingAppointment appointment) {
        MoeBookOnlineDeposit bookOnlineDeposit =
                bookOnlineDepositMapper.selectByGroomingId(appointment.getBusinessId(), appointment.getId());
        // pre-auth 未产生实际支付 payment id 为 0
        if (Objects.isNull(bookOnlineDeposit)
                || DepositPaymentTypeEnum.PreAuth.equals(bookOnlineDeposit.getDepositType())) {
            return null;
        }
        PaymentDTO paymentDTO = paymentClient.getPaymentMethodById(bookOnlineDeposit.getPaymentId());
        return new PaymentDetailVO()
                .setDetail(ClientApptUtils.convert(bookOnlineDeposit))
                .setStripePaymentMethod(paymentDTO.getStripePaymentMethod())
                .setCreditCard(ClientApptUtils.convert(paymentDTO));
    }
}
