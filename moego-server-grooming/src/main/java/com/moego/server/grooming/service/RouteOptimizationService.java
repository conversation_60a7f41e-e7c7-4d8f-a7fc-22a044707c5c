package com.moego.server.grooming.service;

import com.google.type.LatLng;
import com.moego.common.dto.FeatureQuotaDto;
import com.moego.common.enums.BusinessCalendarEnum;
import com.moego.common.enums.FeatureConst;
import com.moego.common.enums.RouteOptimizationConst;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.service.dto.AppointmentWithAddressDTO;
import com.moego.server.grooming.service.dto.AppointmentWithCoordinateDTO;
import com.moego.server.grooming.service.dto.RouteOptimizationDTO;
import com.moego.server.grooming.service.dto.RouteOptimizationDetail;
import com.moego.server.grooming.service.dto.RouteOptimizationRequest;
import com.moego.server.grooming.service.dto.RouteOptimizationResponse;
import com.moego.server.grooming.service.dto.RouteResult;
import com.moego.server.grooming.service.ob.OBAddressService;
import com.moego.server.payment.api.IPaymentPlanService;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class RouteOptimizationService {

    @Resource
    private PetDetailMapperProxy petDetailMapper;

    @Resource
    private GoogleMapService googleMapService;

    @Resource
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Resource
    private OBAddressService obAddressService;

    @Resource
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private IPaymentPlanService paymentPlanService;

    @Autowired
    private RedisUtil redisUtil;

    public static final int MAX_WAYPOINTS = 25;

    public static final int GROUP_COUNT = 2;

    public RouteOptimizationResponse getRouteOptimizationResult(
            Long companyId, Integer businessId, RouteOptimizationRequest request) {
        if (!checkRouteOptimization(companyId)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_TOO_MANY_REQUESTS, "No result. Route optimization is not available.");
        }
        updateRouteOptimizationUsedTimesFor(companyId);
        log.info("getRouteOptimizationResult businessId: {}, request: {}", businessId, request);
        List<SmartScheduleGroomingDetailsDTO> serviceList = petDetailMapper
                .queryInProgressByBusinessIdBetweenDates(
                        businessId,
                        request.getDate().toString(),
                        request.getDate().plusDays(1).toString(),
                        List.of(request.getStaffId()))
                .stream()
                .collect(Collectors.toMap(
                        SmartScheduleGroomingDetailsDTO::getGroomingId, Function.identity(), (a, b) -> {
                            // 合并时间
                            long minStartTime = Math.min(a.getStartTime(), b.getStartTime());
                            long maxEndTime = Math.max(a.getEndTime(), b.getEndTime());
                            a.setStartTime(minStartTime);
                            a.setEndTime(maxEndTime);
                            return a;
                        }))
                .values()
                .stream()
                .sorted(Comparator.comparing(SmartScheduleGroomingDetailsDTO::getStartTime))
                .toList();

        if (CollectionUtils.isEmpty(serviceList)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No result. Please check the parameters.");
        }

        var origin = GoogleMapService.toGoogleLatLng(request.getStartLocationLat(), request.getStartLocationLnt());
        var destination = GoogleMapService.toGoogleLatLng(request.getEndLocationLat(), request.getEndLocationLnt());

        RouteOptimizationDTO routeOptimizationDTO = new RouteOptimizationDTO(
                origin, destination, serviceList, businessId, request.getStaffId(), request.getDate());
        RouteOptimizationResponse response = getRouteOptimizationResult(routeOptimizationDTO);

        // 获取 business 的距离单位
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(routeOptimizationDTO.businessId()).build());
        response.setUnitOfDistanceType(businessInfo.getUnitOfDistanceType());

        return response;
    }

    private RouteOptimizationResponse getRouteOptimizationResult(RouteOptimizationDTO routeOptimizationDTO) {
        // 获取所有预约的地址
        List<AppointmentWithAddressDTO> appointmentWithAddressList =
                getAppointmentWithAddressList(routeOptimizationDTO);

        RouteOptimizationResponse response = new RouteOptimizationResponse();
        if (CollectionUtils.isEmpty(appointmentWithAddressList)
                || appointmentWithAddressList.size() < 2
                || appointmentWithAddressList.size() > MAX_WAYPOINTS * GROUP_COUNT) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No result. Appointment count is not valid.");
        }

        // 构造每个预约的坐标数据
        var origin = routeOptimizationDTO.origin();
        var destination = routeOptimizationDTO.destination();
        List<AppointmentWithCoordinateDTO> waypointList = appointmentWithAddressList.stream()
                .map(appointment -> new AppointmentWithCoordinateDTO(
                        appointment.groomingId(),
                        GoogleMapService.toGoogleLatLng(appointment.lat(), appointment.lnt())))
                .toList();

        // 获取优化前的驾驶数据
        CompletableFuture<TempResult> unoptimizedResultFuture = CompletableFuture.supplyAsync(
                () -> optimizeRoute(origin, destination, waypointList, false), ThreadPool.getExecutor());

        // 获取优化后的驾驶数据
        CompletableFuture<TempResult> optimizedResultFuture = CompletableFuture.supplyAsync(
                () -> optimizeRoute(origin, destination, waypointList, true), ThreadPool.getExecutor());

        // 获取异步执行的结果
        CompletableFuture.allOf(unoptimizedResultFuture, optimizedResultFuture).join();
        TempResult unoptimizedResult, optimizedResult;
        try {
            unoptimizedResult = unoptimizedResultFuture.get();
            optimizedResult = optimizedResultFuture.get();
            log.info("getRouteOptimizationResult unoptimizedResult: {}", unoptimizedResult);
            log.info("getRouteOptimizationResult optimizedResult: {}", optimizedResult);
        } catch (Exception e) {
            log.error("getRouteOptimizationResult error", e);
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "No result. The distance between appointments is too far.");
        }
        if (Objects.isNull(unoptimizedResult) || Objects.isNull(optimizedResult)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "No result. The distance between appointments is too far.");
        }

        // 计算每个预约的开始时间
        List<RouteOptimizationDetail> detailList =
                arrangeAppointmentStartTime(routeOptimizationDTO, optimizedResult.detailList());

        response.setWayPointCount(appointmentWithAddressList.size());
        response.setOptimizedDrivingMinutes(optimizedResult.drivingMinutes);
        response.setOptimizedDrivingMiles(optimizedResult.drivingMiles);
        response.setSavedDrivingMinutes(Math.abs(unoptimizedResult.drivingMinutes - optimizedResult.drivingMinutes));
        response.setSavedDrivingMiles(unoptimizedResult
                .drivingMiles
                .subtract(optimizedResult.drivingMiles)
                .abs());
        response.setDetailList(detailList);
        response.setUnoptimizedPolyline(unoptimizedResult.polyline);
        response.setOptimizedPolyline(optimizedResult.polyline);

        return response;
    }

    private List<AppointmentWithAddressDTO> getAppointmentWithAddressList(RouteOptimizationDTO routeOptimizationDTO) {
        List<SmartScheduleGroomingDetailsDTO> serviceList = routeOptimizationDTO.serviceList();

        List<Integer> customerIds = serviceList.stream()
                .map(SmartScheduleGroomingDetailsDTO::getCustomerId)
                .filter(id -> id > 0)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, CustomerAddressDto> addressMap = new HashMap<>(16);
        // 获取所有 customer 的地址
        if (!CollectionUtils.isEmpty(customerIds)) {
            addressMap.putAll(obAddressService.batchGetPrimaryAddress(customerIds));
        }

        return serviceList.stream()
                .filter(curService -> curService.getCustomerId() > 0)
                .map(service -> {
                    CustomerAddressDto address = addressMap.get(service.getCustomerId());
                    if (Objects.isNull(address)
                            || !StringUtils.hasText(address.getLat())
                            || !StringUtils.hasText(address.getLng())) {
                        return null;
                    }
                    return new AppointmentWithAddressDTO(service.getGroomingId(), address.getLat(), address.getLng());
                })
                .filter(Objects::nonNull)
                .toList();
    }

    private List<RouteOptimizationDetail> arrangeAppointmentStartTime(
            RouteOptimizationDTO routeOptimizationDTO, List<RouteOptimizationDetail> detailList) {
        List<SmartScheduleGroomingDetailsDTO> serviceList = routeOptimizationDTO.serviceList();

        // 获取空闲时间间隔
        int staffWorkingStartTime = getStaffWorkingStartTime(
                routeOptimizationDTO.businessId(), routeOptimizationDTO.staffId(), routeOptimizationDTO.date());
        int staffWorkingEndTime = BusinessCalendarEnum.DEFAULT_CALENDAR_MAX_TIME;
        List<TimeSlot> timeSlotList = getTimeSlotList(serviceList, staffWorkingStartTime, staffWorkingEndTime);

        detailList.removeIf(detail -> detail.getGroomingId() == -1 || detail.getGroomingId() == -2);
        Map<Integer, SmartScheduleGroomingDetailsDTO> serviceMap = serviceList.stream()
                .collect(Collectors.toMap(
                        SmartScheduleGroomingDetailsDTO::getGroomingId, Function.identity(), (a, b) -> a));
        // detailList 中的预约索引
        int index = 0;
        // 最后一个预约的开始时间
        int lastStartTime = 0;
        // 遍历每个空闲时间间隔
        for (TimeSlot timeSlot : timeSlotList) {
            // 当前空闲时间间隔中的开始时间
            int start = timeSlot.getStart();
            int availableTime = timeSlot.getAvailableTime();

            // 遍历每个预约，将预约排进去
            for (int i = index; i < detailList.size(); i++) {
                RouteOptimizationDetail detail = detailList.get(i);
                int drivingInMinutes = roundUpToNextMultipleOfFive(detail.getDrivingInMinutes());
                int groomingTotalTime = getGroomingTotalTime(detail, serviceMap);
                // 如果空闲时间充裕，则将当前预约排进来
                if (availableTime >= (drivingInMinutes + groomingTotalTime)) {
                    // 第一个预约，不需要考虑 drivingInMinutes
                    if (index != 0) {
                        start += drivingInMinutes;
                    }
                    detail.setStartTime(start);

                    index = i + 1;
                    lastStartTime = start;
                    start += groomingTotalTime;
                    availableTime -= (drivingInMinutes + groomingTotalTime);
                } else {
                    break;
                }
            }
        }

        // 如果有预约没有排进去，或者没有空闲时间间隔，或者有预约的开始时间超过 24 点，则忽略 block，重新计算开始时间
        if (index < detailList.size()
                || lastStartTime <= 0
                || lastStartTime > BusinessCalendarEnum.DEFAULT_CALENDAR_MAX_TIME) {
            int nextStartTime = staffWorkingStartTime;
            for (int i = 0; i < detailList.size(); i++) {
                RouteOptimizationDetail detail = detailList.get(i);
                int drivingInMinutes = roundUpToNextMultipleOfFive(detail.getDrivingInMinutes());
                // 第一个预约，不需要考虑 drivingInMinutes
                detail.setStartTime(i == 0 ? nextStartTime : nextStartTime + drivingInMinutes);

                int groomingTotalTime = getGroomingTotalTime(detail, serviceMap);
                nextStartTime += (drivingInMinutes + groomingTotalTime);
            }
        }
        return detailList;
    }

    /**
     * 获取当前预约的总时间
     */
    private static int getGroomingTotalTime(
            RouteOptimizationDetail detail, Map<Integer, SmartScheduleGroomingDetailsDTO> serviceMap) {
        SmartScheduleGroomingDetailsDTO groomingService = serviceMap.get(detail.getGroomingId());
        long totalTime = groomingService.getEndTime() - groomingService.getStartTime();
        return Math.toIntExact(totalTime);
    }

    /**
     * 向上取整至 5 的倍数
     */
    private static int roundUpToNextMultipleOfFive(long number) {
        return (int) (Math.ceil(number / 5.0) * 5);
    }

    /**
     * 获取 block 之间的空闲时间间隔
     */
    private static List<TimeSlot> getTimeSlotList(
            List<SmartScheduleGroomingDetailsDTO> serviceList, int startTime, int endTime) {
        List<SmartScheduleGroomingDetailsDTO> blockList = serviceList.stream()
                .filter(curService -> curService.getCustomerId() == 0)
                .toList();
        List<TimeSlot> timeSlotList = new ArrayList<>();
        int prevEndTime = startTime;
        int prevApptId = -1;
        int lastEndTime = startTime;
        for (SmartScheduleGroomingDetailsDTO block : blockList) {
            TimeSlot timeSlot = new TimeSlot();
            timeSlot.setBeforeApptId(prevApptId);
            timeSlot.setAfterApptId(block.getGroomingId());
            timeSlot.setStart(prevEndTime);
            timeSlot.setEnd(Math.toIntExact(block.getStartTime()));
            timeSlot.setAvailableTime(timeSlot.getEnd() - timeSlot.getStart());
            if (timeSlot.getAvailableTime() > 0) {
                timeSlotList.add(timeSlot);
            }

            prevEndTime = Math.toIntExact(Math.max(block.getEndTime(), prevEndTime));
            prevApptId = block.getGroomingId();
            lastEndTime = prevEndTime;
        }
        if (lastEndTime < endTime) {
            TimeSlot timeSlot = new TimeSlot();
            timeSlot.setBeforeApptId(prevApptId);
            timeSlot.setStart(lastEndTime);
            timeSlot.setEnd(endTime);
            timeSlot.setAvailableTime(timeSlot.getEnd() - timeSlot.getStart());
            timeSlotList.add(timeSlot);
        }

        log.info("RouteOptimizationService getTimeSlotList: {}", timeSlotList);
        return timeSlotList;
    }

    private TempResult optimizeRoute(
            LatLng origin, LatLng destination, List<AppointmentWithCoordinateDTO> waypointList, boolean needOptimize) {
        boolean hasMoreWaypoint = waypointList.size() > MAX_WAYPOINTS;
        if (hasMoreWaypoint) {
            return optimizeRouteForMoreWaypoint(waypointList, origin, destination, needOptimize);
        }

        var waypoints = waypointList.stream()
                .map(AppointmentWithCoordinateDTO::coordinate)
                .toList();
        RouteResult routeResult = googleMapService.queryAndCacheRoutes(origin, destination, waypoints, needOptimize);
        if (Objects.isNull(routeResult)) {
            return null;
        }

        if (needOptimize) {
            List<Integer> drivingMinuteList = routeResult.drivingMinuteList();
            List<BigDecimal> drivingMileList = routeResult.drivingMileList();
            List<AppointmentWithCoordinateDTO> list = routeResult.waypointIndexList().stream()
                    .map(waypointList::get)
                    .toList();
            var detailList = getDetailList(origin, destination, list, drivingMinuteList, drivingMileList);
            return new TempResult(
                    routeResult.drivingMinutes(),
                    routeResult.drivingMiles(),
                    List.of(routeResult.polyline()),
                    detailList);
        }

        return new TempResult(
                routeResult.drivingMinutes(), routeResult.drivingMiles(), List.of(routeResult.polyline()), List.of());
    }

    private TempResult optimizeRouteForMoreWaypoint(
            List<AppointmentWithCoordinateDTO> waypointList, LatLng origin, LatLng destination, boolean needOptimize) {
        // 根据离出发地点距离进行排序
        List<AppointmentWithCoordinateDTO> sortedWaypointList = waypointList.stream()
                .sorted(Comparator.comparingDouble(
                        waypoint -> GoogleMapService.straightDistanceByAngle(origin, waypoint.coordinate())))
                .collect(Collectors.toList());

        // 分成两组，对每组进行路线规划
        List<AppointmentWithCoordinateDTO> firstWaypointList = sortedWaypointList.subList(0, MAX_WAYPOINTS);
        var firstCoordinateList = firstWaypointList.stream()
                .map(AppointmentWithCoordinateDTO::coordinate)
                .toList();
        RouteResult firstRouteResult =
                googleMapService.queryAndCacheRoutes(origin, destination, firstCoordinateList, needOptimize);
        if (Objects.isNull(firstRouteResult)) {
            return null;
        }

        // 第二段的起点是第一段的终点的前一个地点
        // 示例：
        // 获取 A - B - C - D - E - Z 的路线规划结果：
        // 第一段：A - B - C -Z
        // 第二段：C - D - E - Z
        var secondOrigin = origin;
        if (needOptimize) {
            List<Integer> waypointIndexList = firstRouteResult.waypointIndexList();
            secondOrigin = firstWaypointList
                    .get(waypointIndexList.get(waypointIndexList.size() - 1))
                    .coordinate();
        }
        List<AppointmentWithCoordinateDTO> secondWaypointList =
                sortedWaypointList.subList(MAX_WAYPOINTS, sortedWaypointList.size());
        var secondCoordinateList = secondWaypointList.stream()
                .map(AppointmentWithCoordinateDTO::coordinate)
                .toList();
        RouteResult secondRouteResult =
                googleMapService.queryAndCacheRoutes(secondOrigin, destination, secondCoordinateList, needOptimize);
        if (Objects.isNull(secondRouteResult)) {
            return null;
        }

        List<Integer> drivingMinuteList = new ArrayList<>(firstRouteResult.drivingMinuteList());
        List<BigDecimal> drivingMileList = new ArrayList<>(firstRouteResult.drivingMileList());
        int drivingMinutes = firstRouteResult.drivingMinutes()
                + secondRouteResult.drivingMinutes()
                - drivingMinuteList.get(drivingMinuteList.size() - 1);
        BigDecimal drivingMiles = firstRouteResult
                .drivingMiles()
                .add(secondRouteResult.drivingMiles())
                .subtract(drivingMileList.get(drivingMileList.size() - 1));
        List<String> polyline = List.of(firstRouteResult.polyline(), secondRouteResult.polyline());

        if (needOptimize) {
            // 移除第一段的最后一个，并拼接第二段
            drivingMinuteList.remove(drivingMinuteList.size() - 1);
            drivingMinuteList.addAll(secondRouteResult.drivingMinuteList());
            drivingMileList.remove(drivingMileList.size() - 1);
            drivingMileList.addAll(secondRouteResult.drivingMileList());

            List<AppointmentWithCoordinateDTO> list = firstRouteResult.waypointIndexList().stream()
                    .map(firstWaypointList::get)
                    .collect(Collectors.toList());
            list.addAll(secondRouteResult.waypointIndexList().stream()
                    .map(secondWaypointList::get)
                    .toList());

            var detailList = getDetailList(origin, destination, list, drivingMinuteList, drivingMileList);
            return new TempResult(drivingMinutes, drivingMiles, polyline, detailList);
        }

        return new TempResult(drivingMinutes, drivingMiles, polyline, List.of());
    }

    /**
     * 对每一个预约构造驾驶数据
     */
    private static List<RouteOptimizationDetail> getDetailList(
            LatLng origin,
            LatLng destination,
            List<AppointmentWithCoordinateDTO> appointmentWithCoordinateList,
            List<Integer> drivingMinuteList,
            List<BigDecimal> drivingMileList) {
        // 填充出发地点和目的地点
        List<AppointmentWithCoordinateDTO> list = new ArrayList<>(appointmentWithCoordinateList);
        list.add(0, new AppointmentWithCoordinateDTO(-1, origin));
        list.add(new AppointmentWithCoordinateDTO(-2, destination));

        Map<Integer, RouteOptimizationDetail> detailMap = new LinkedHashMap<>(16);
        IntStream.range(0, list.size() - 1).forEach(index -> {
            int currentDrivingMinutes = drivingMinuteList.get(index);
            BigDecimal currentDrivingMiles = drivingMileList.get(index);

            Integer prev = list.get(index).groomingId();
            RouteOptimizationDetail detail = detailMap.getOrDefault(prev, new RouteOptimizationDetail());
            detail.setGroomingId(prev);
            detail.setDrivingOutMinutes(currentDrivingMinutes);
            detail.setDrivingOutMiles(currentDrivingMiles);
            detailMap.put(prev, detail);

            Integer next = list.get(index + 1).groomingId();
            detail = detailMap.getOrDefault(next, new RouteOptimizationDetail());
            detail.setGroomingId(next);
            detail.setDrivingInMinutes(currentDrivingMinutes);
            detail.setDrivingInMiles(currentDrivingMiles);
            detailMap.put(next, detail);
        });
        return new ArrayList<>(detailMap.values());
    }

    private Integer getStaffWorkingStartTime(Integer businessId, Integer staffId, LocalDate date) {
        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> workingHour =
                iBusinessStaffClient.getStaffWorkingHourWithOverrideDate(
                        businessId, List.of(staffId), date.toString(), date.toString());
        if (CollectionUtils.isEmpty(workingHour)
                || CollectionUtils.isEmpty(workingHour.get(staffId))
                || CollectionUtils.isEmpty(workingHour.get(staffId).get(date))) {
            return 0;
        }
        return workingHour.get(staffId).get(date).get(0).getStartTime();
    }

    record TempResult(
            Integer drivingMinutes,
            BigDecimal drivingMiles,
            List<String> polyline,
            List<RouteOptimizationDetail> detailList) {}

    /**
     * 查询 route optimization 是否可用
     *
     * @return
     */
    public boolean checkRouteOptimization(Long companyId) {
        var remain = getRouteOptimizationAvailableTimesForSoloPlan(companyId);
        return remain > 0 || remain.equals(FeatureConst.MAX_QUOTA);
    }

    /**
     * 查询 route 剩余数量
     *
     * @param companyId
     * @return
     */
    public Integer getRouteOptimizationAvailableTimesForSoloPlan(Long companyId) {
        var featureCode = paymentPlanService.queryCompanyPlanFeatureByCidCode(
                companyId.intValue(), FeatureConst.FC_ROUTE_OPTIMIZATION);
        if (!featureCode.checkEnable()) {
            return 0;
        }
        if (FeatureConst.MAX_QUOTA.equals(featureCode.getQuota())) {
            return FeatureConst.MAX_QUOTA;
        }
        if (FeatureConst.MIN_QUOTA.equals(featureCode.getQuota())) {
            return FeatureConst.MIN_QUOTA;
        }
        //  solo plan 每个月限量 20 次，检查使用次数
        String usedTimesforRO =
                redisUtil.get(String.format(RouteOptimizationConst.REDIS_USED_FOR_ROUTE_OPTIMIZATION, companyId));
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(usedTimesforRO)) {
            var remain = featureCode.getQuota() - Integer.parseInt(usedTimesforRO);
            return Math.min(Math.max(remain, 0), featureCode.getQuota());
        } else {
            initRouteOptimizationTimes(companyId, featureCode);
            return featureCode.getQuota();
        }
    }

    public void updateRouteOptimizationUsedTimesFor(Long companyId) {
        if (getRouteOptimizationAvailableTimesForSoloPlan(companyId) > 0) {
            redisUtil.incrBy(getReidsCacheKey(companyId), 1);
        }
    }

    public void initRouteOptimizationTimes(Long companyId, FeatureQuotaDto featureCode) {
        if (Objects.equals(featureCode.getQuota(), FeatureConst.MAX_QUOTA)) {
            return;
        }
        if (Objects.equals(featureCode.getQuota(), FeatureConst.MIN_QUOTA)) {
            return;
        }
        redisUtil.set(getReidsCacheKey(companyId), "0");
    }

    private String getReidsCacheKey(Long companyId) {
        return String.format(RouteOptimizationConst.REDIS_USED_FOR_ROUTE_OPTIMIZATION, companyId);
    }
}
