package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table abandoned_schedule_message_setting
 */
public class AbandonedScheduleMessageSetting {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   client types to send messages, e.g. ["new_visitors", "existing_clients"]
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.client_types
     *
     * @mbg.generated
     */
    private String clientTypes;

    /**
     * Database Column Remarks:
     *   Abandoned steps to send messages, e.g. ["select_groomer", "select_time"]
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.abandoned_steps
     *
     * @mbg.generated
     */
    private String abandonedSteps;

    /**
     * Database Column Remarks:
     *   send out type
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.send_out_type
     *
     * @mbg.generated
     */
    private String sendOutType;

    /**
     * Database Column Remarks:
     *   需要发送的 day of week，当 send out type 使用 on 时使用的配置
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.on_type_days
     *
     * @mbg.generated
     */
    private String onTypeDays;

    /**
     * Database Column Remarks:
     *   发送的时间，当 send out type 使用 on 时使用的配置
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.on_type_minute
     *
     * @mbg.generated
     */
    private Integer onTypeMinute;

    /**
     * Database Column Remarks:
     *   delay 时间，当 send out type 使用 wait for 时使用的配置
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.wait_for_type_minute
     *
     * @mbg.generated
     */
    private Integer waitForTypeMinute;

    /**
     * Database Column Remarks:
     *   消息内容
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.message
     *
     * @mbg.generated
     */
    private String message;

    /**
     * Database Column Remarks:
     *   是否启用
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.is_enabled
     *
     * @mbg.generated
     */
    private Boolean isEnabled;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     * Database Column Remarks:
     *   更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     * Database Column Remarks:
     *   最近一次将 is_enabled 设置为 true 的时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abandoned_schedule_message_setting.enabled_at
     *
     * @mbg.generated
     */
    private Date enabledAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.id
     *
     * @return the value of abandoned_schedule_message_setting.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.id
     *
     * @param id the value for abandoned_schedule_message_setting.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.business_id
     *
     * @return the value of abandoned_schedule_message_setting.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.business_id
     *
     * @param businessId the value for abandoned_schedule_message_setting.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.client_types
     *
     * @return the value of abandoned_schedule_message_setting.client_types
     *
     * @mbg.generated
     */
    public String getClientTypes() {
        return clientTypes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.client_types
     *
     * @param clientTypes the value for abandoned_schedule_message_setting.client_types
     *
     * @mbg.generated
     */
    public void setClientTypes(String clientTypes) {
        this.clientTypes = clientTypes == null ? null : clientTypes.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.abandoned_steps
     *
     * @return the value of abandoned_schedule_message_setting.abandoned_steps
     *
     * @mbg.generated
     */
    public String getAbandonedSteps() {
        return abandonedSteps;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.abandoned_steps
     *
     * @param abandonedSteps the value for abandoned_schedule_message_setting.abandoned_steps
     *
     * @mbg.generated
     */
    public void setAbandonedSteps(String abandonedSteps) {
        this.abandonedSteps = abandonedSteps == null ? null : abandonedSteps.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.send_out_type
     *
     * @return the value of abandoned_schedule_message_setting.send_out_type
     *
     * @mbg.generated
     */
    public String getSendOutType() {
        return sendOutType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.send_out_type
     *
     * @param sendOutType the value for abandoned_schedule_message_setting.send_out_type
     *
     * @mbg.generated
     */
    public void setSendOutType(String sendOutType) {
        this.sendOutType = sendOutType == null ? null : sendOutType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.on_type_days
     *
     * @return the value of abandoned_schedule_message_setting.on_type_days
     *
     * @mbg.generated
     */
    public String getOnTypeDays() {
        return onTypeDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.on_type_days
     *
     * @param onTypeDays the value for abandoned_schedule_message_setting.on_type_days
     *
     * @mbg.generated
     */
    public void setOnTypeDays(String onTypeDays) {
        this.onTypeDays = onTypeDays == null ? null : onTypeDays.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.on_type_minute
     *
     * @return the value of abandoned_schedule_message_setting.on_type_minute
     *
     * @mbg.generated
     */
    public Integer getOnTypeMinute() {
        return onTypeMinute;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.on_type_minute
     *
     * @param onTypeMinute the value for abandoned_schedule_message_setting.on_type_minute
     *
     * @mbg.generated
     */
    public void setOnTypeMinute(Integer onTypeMinute) {
        this.onTypeMinute = onTypeMinute;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.wait_for_type_minute
     *
     * @return the value of abandoned_schedule_message_setting.wait_for_type_minute
     *
     * @mbg.generated
     */
    public Integer getWaitForTypeMinute() {
        return waitForTypeMinute;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.wait_for_type_minute
     *
     * @param waitForTypeMinute the value for abandoned_schedule_message_setting.wait_for_type_minute
     *
     * @mbg.generated
     */
    public void setWaitForTypeMinute(Integer waitForTypeMinute) {
        this.waitForTypeMinute = waitForTypeMinute;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.message
     *
     * @return the value of abandoned_schedule_message_setting.message
     *
     * @mbg.generated
     */
    public String getMessage() {
        return message;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.message
     *
     * @param message the value for abandoned_schedule_message_setting.message
     *
     * @mbg.generated
     */
    public void setMessage(String message) {
        this.message = message == null ? null : message.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.is_enabled
     *
     * @return the value of abandoned_schedule_message_setting.is_enabled
     *
     * @mbg.generated
     */
    public Boolean getIsEnabled() {
        return isEnabled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.is_enabled
     *
     * @param isEnabled the value for abandoned_schedule_message_setting.is_enabled
     *
     * @mbg.generated
     */
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.created_at
     *
     * @return the value of abandoned_schedule_message_setting.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.created_at
     *
     * @param createdAt the value for abandoned_schedule_message_setting.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.updated_at
     *
     * @return the value of abandoned_schedule_message_setting.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.updated_at
     *
     * @param updatedAt the value for abandoned_schedule_message_setting.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column abandoned_schedule_message_setting.enabled_at
     *
     * @return the value of abandoned_schedule_message_setting.enabled_at
     *
     * @mbg.generated
     */
    public Date getEnabledAt() {
        return enabledAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column abandoned_schedule_message_setting.enabled_at
     *
     * @param enabledAt the value for abandoned_schedule_message_setting.enabled_at
     *
     * @mbg.generated
     */
    public void setEnabledAt(Date enabledAt) {
        this.enabledAt = enabledAt;
    }
}
