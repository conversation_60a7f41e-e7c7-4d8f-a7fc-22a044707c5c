package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_questionnaire
 */
public class MoeQuestionnaire {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_questionnaire.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_questionnaire.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_questionnaire.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   questionnaire detail
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_questionnaire.form_detail
     *
     * @mbg.generated
     */
    private String formDetail;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_questionnaire.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_questionnaire.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_questionnaire.id
     *
     * @return the value of moe_questionnaire.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_questionnaire.id
     *
     * @param id the value for moe_questionnaire.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_questionnaire.business_id
     *
     * @return the value of moe_questionnaire.business_id
     *
     * @mbg.generated
     */
    public Long getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_questionnaire.business_id
     *
     * @param businessId the value for moe_questionnaire.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_questionnaire.company_id
     *
     * @return the value of moe_questionnaire.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_questionnaire.company_id
     *
     * @param companyId the value for moe_questionnaire.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_questionnaire.form_detail
     *
     * @return the value of moe_questionnaire.form_detail
     *
     * @mbg.generated
     */
    public String getFormDetail() {
        return formDetail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_questionnaire.form_detail
     *
     * @param formDetail the value for moe_questionnaire.form_detail
     *
     * @mbg.generated
     */
    public void setFormDetail(String formDetail) {
        this.formDetail = formDetail == null ? null : formDetail.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_questionnaire.created_at
     *
     * @return the value of moe_questionnaire.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_questionnaire.created_at
     *
     * @param createdAt the value for moe_questionnaire.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_questionnaire.created_by
     *
     * @return the value of moe_questionnaire.created_by
     *
     * @mbg.generated
     */
    public Long getCreatedBy() {
        return createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_questionnaire.created_by
     *
     * @param createdBy the value for moe_questionnaire.created_by
     *
     * @mbg.generated
     */
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }
}
