package com.moego.server.grooming.service.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QueryCustomerPackageInfoParam {
    @Schema(description = "package id")
    @NotNull
    private Integer id;

    @Schema(description = "history分页大小，默认20")
    @Min(1)
    private Integer historyPageSize;

    @Schema(description = "history分页页数，默认1")
    @Min(1)
    private Integer historyPageNum;
}
