package com.moego.server.grooming.service;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.server.grooming.service.GroomingServiceService.calculateServiceTax;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;

import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.order.DiscountType;
import com.moego.common.enums.order.LineApplyType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipUsageView;
import com.moego.idl.models.membership.v1.RedeemContext;
import com.moego.idl.models.membership.v1.RedeemScenario;
import com.moego.idl.models.membership.v1.RedeemScenarioItem;
import com.moego.idl.models.membership.v1.TargetType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.membership.v1.ListRecommendMembershipsRequest;
import com.moego.idl.service.membership.v1.ListRecommendMembershipsResponse;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.service.membership.v1.UpsertRecommendBenefitUsageRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.service.dto.CalculateResult;
import com.moego.server.grooming.service.dto.CalculateServiceAmount;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class MembershipService {

    @Autowired
    private MoeGroomingServiceMapper groomingServiceMapper;

    @Autowired
    private MembershipServiceGrpc.MembershipServiceBlockingStub membershipService;

    @Autowired
    private OrderService orderService;

    public CalculateResult useMembership(
            Integer businessId,
            Long companyId,
            @Nullable Integer customerId,
            List<CalculateServiceAmount> serviceAmountDTOList) {
        if (Objects.isNull(customerId)) {
            return new CalculateResult(BigDecimal.ZERO, BigDecimal.ZERO, List.of(), List.of());
        }
        List<Integer> serviceIds = serviceAmountDTOList.stream()
                .map(CalculateServiceAmount::serviceId)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        Map<Integer, MoeGroomingService> serviceMap =
                groomingServiceMapper.getServicesByCompanyIdServiceIds(companyId, serviceIds).stream()
                        .collect(Collectors.toMap(MoeGroomingService::getId, Function.identity(), (a, b) -> a));

        AtomicLong orderItemId = new AtomicLong(1);
        Map<Long, CalculateServiceAmount> calculateServiceMap = serviceAmountDTOList.stream()
                .filter(service -> CommonUtil.isNormal(service.serviceId()))
                .collect(Collectors.toMap(v -> orderItemId.getAndIncrement(), identity()));
        List<RedeemScenarioItem> list = calculateServiceMap.entrySet().stream()
                .map(entry -> {
                    CalculateServiceAmount item = entry.getValue();
                    RedeemScenarioItem.Builder builder = RedeemScenarioItem.newBuilder()
                            .setTargetId(item.serviceId())
                            .setTargetType(getTargetType(item.serviceId(), serviceMap))
                            .setAmount(1)
                            .setPrice(item.serviceAmount().doubleValue())
                            .setOrderItemId(entry.getKey());
                    return builder.build();
                })
                .filter(item -> !Objects.equals(item.getTargetType(), TargetType.TARGET_TYPE_UNSPECIFIED))
                .toList();

        // aggregate items
        list = list.stream()
                .collect(
                        Collectors.groupingBy(item -> "" + item.getTargetType() + item.getTargetId() + item.getPrice()))
                .values()
                .stream()
                .map(values -> {
                    if (CollectionUtils.isEmpty(values)) {
                        return null;
                    }
                    RedeemScenarioItem first = values.get(0);
                    return RedeemScenarioItem.newBuilder()
                            .setTargetId(first.getTargetId())
                            .setTargetType(first.getTargetType())
                            .setAmount(values.size())
                            .setPrice(first.getPrice())
                            .setOrderItemId(first.getOrderItemId())
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();

        RedeemContext.Builder builder = RedeemContext.newBuilder()
                .setScenario(RedeemScenario.REDEEM_BY_ONLINE_BOOKING_SUBMIT)
                .addAllItems(list);

        // membership info for obc
        // get membership
        ListRecommendMembershipsResponse membershipsResponse =
                membershipService.listRecommendedMemberships(ListRecommendMembershipsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setCustomerId(customerId)
                        .setContext(builder.build())
                        .build());

        List<MembershipUsageView> usageViews = membershipsResponse.getUsageViewsList();
        if (CollectionUtils.isEmpty(usageViews)) {
            return new CalculateResult(BigDecimal.ZERO, BigDecimal.ZERO, List.of(), List.of());
        }

        BigDecimal totalDiscount = BigDecimal.ZERO;
        BigDecimal totalTax = BigDecimal.ZERO;
        List<CalculateServiceAmount> usedMembership = new ArrayList<>();
        for (Map.Entry<Long /* order itme id */, List<MembershipUsageView>> entry : usageViews.stream()
                .collect(groupingBy(MembershipUsageView::getOrderItemId))
                .entrySet()) {
            Long key = entry.getKey();
            List<MembershipUsageView> values = entry.getValue();
            if (CollectionUtils.isEmpty(values)) {
                continue;
            }

            double discountAmount = values.stream()
                    .map(MembershipUsageView::getPriceReduction)
                    .reduce(Double::sum)
                    .orElseThrow(() -> bizException(Code.CODE_SERVER_ERROR, "price reduction not found"));

            CalculateServiceAmount serviceAmountDTO = calculateServiceMap.get(key);
            usedMembership.add(serviceAmountDTO);
            BigDecimal currentDiscountAmount = BigDecimal.valueOf(discountAmount);
            totalDiscount = totalDiscount.add(currentDiscountAmount);
            BigDecimal currentSubTotal = serviceAmountDTO.serviceAmount().subtract(currentDiscountAmount);
            totalTax = totalTax.add(calculateServiceTax(currentSubTotal, serviceAmountDTO.taxRate()));
        }

        List<Long> usedMembershipIds = usageViews.stream()
                .map(MembershipUsageView::getMembershipId)
                .distinct()
                .toList();

        return new CalculateResult(totalDiscount, totalTax, usedMembership, usedMembershipIds);
    }

    private TargetType getTargetType(Integer serviceId, Map<Integer, MoeGroomingService> serviceMap) {
        if (!serviceMap.containsKey(serviceId)) {
            return TargetType.TARGET_TYPE_UNSPECIFIED;
        }
        MoeGroomingService service = serviceMap.get(serviceId);
        if (ServiceEnum.TYPE_SERVICE.equals(service.getType())) {
            return TargetType.SERVICE;
        } else if (ServiceEnum.TYPE_ADD_ONS.equals(service.getType())) {
            return TargetType.ADDON;
        }
        return TargetType.TARGET_TYPE_UNSPECIFIED;
    }

    public boolean applyMemberships(
            Long businessId, Long companyId, Long orderId, Long staffId, Collection<Long> needAppliedMembershipIds) {
        if (CollectionUtils.isEmpty(needAppliedMembershipIds)) {
            return false;
        }

        OrderDetailModel orderDetail = orderService.getOrderDetailByOrderId(businessId.intValue(), orderId.intValue());
        long customerId = orderDetail.getOrder().getCustomerId();

        // membership info for apply benefits
        ListRecommendMembershipsResponse membershipsResponse = getListRecommendMembershipsResponse(
                businessId, companyId, needAppliedMembershipIds, orderDetail, customerId);
        log.debug("apply memberships: {}", membershipsResponse);
        List<MembershipUsageView> usageViews = membershipsResponse.getUsageViewsList();
        if (CollectionUtils.isEmpty(usageViews)) {
            // clear applied records
            membershipService.upsertRecommendBenefitUsage(UpsertRecommendBenefitUsageRequest.newBuilder()
                    .setOrderId(orderId)
                    .setCustomerId(orderDetail.getOrder().getCustomerId())
                    .build());
            return false;
        }

        // apply discounts
        Map<Long, OrderLineDiscountModel> discountMap =
                usageViews.stream()
                        .collect(Collectors.groupingBy(MembershipUsageView::getOrderItemId))
                        .entrySet()
                        .stream()
                        .map(entry -> convertToLineDiscountModel(
                                entry.getKey(), entry.getValue(), orderId, businessId, staffId))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(OrderLineDiscountModel::getOrderItemId, Function.identity()));

        if (CollectionUtils.isEmpty(discountMap)) {
            return false;
        }

        // update order
        OrderModel order = OrderModel.newBuilder()
                .setId(orderId)
                .setUpdateBy(staffId)
                .setBusinessId(businessId)
                .build();
        orderService.updateOrderIncremental(
                orderId.intValue(),
                order,
                List.of(),
                List.of(),
                discountMap.values().stream().toList(),
                List.of(),
                false);

        // upsert applied records
        membershipService.upsertRecommendBenefitUsage(UpsertRecommendBenefitUsageRequest.newBuilder()
                .setOrderId(orderId)
                .setCustomerId(customerId)
                .addAllAllMemberships(membershipsResponse.getAllList())
                .addAllBenefitCombination(membershipsResponse.getBenefitCombinationList())
                .addAllUsageViews(usageViews)
                .build());
        return true;
    }

    private ListRecommendMembershipsResponse getListRecommendMembershipsResponse(
            Long businessId,
            Long companyId,
            Collection<Long> needAppliedMembershipIds,
            OrderDetailModel orderDetail,
            long customerId) {
        List<Integer> serviceIds = orderDetail.getLineItemsList().stream()
                .filter(item -> Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), item.getType()))
                .map(OrderLineItemModel::getObjectId)
                .distinct()
                .map(Long::intValue)
                .toList();
        Map<Integer, MoeGroomingService> serviceMap =
                groomingServiceMapper.getServicesByCompanyIdServiceIds(companyId, serviceIds).stream()
                        .collect(Collectors.toMap(MoeGroomingService::getId, Function.identity(), (a, b) -> a));

        List<RedeemScenarioItem> list = orderDetail.getLineItemsList().stream()
                .map(item -> {
                    RedeemScenarioItem.Builder builder = RedeemScenarioItem.newBuilder()
                            .setTargetId(item.getObjectId())
                            .setTargetType(getTargetType(item.getType(), item.getObjectId(), serviceMap))
                            .setAmount(item.getQuantity())
                            .setPrice(item.getUnitPrice())
                            .setOrderItemId(item.getId());
                    return builder.build();
                })
                .filter(item -> !Objects.equals(item.getTargetType(), TargetType.TARGET_TYPE_UNSPECIFIED))
                .toList();

        RedeemContext.Builder builder = RedeemContext.newBuilder()
                .setScenario(RedeemScenario.REDEEM_BY_CHECKOUT)
                .addAllItems(list);

        // membership info for apply benefits
        // get membership
        ListRecommendMembershipsRequest.Builder requestBuilder = ListRecommendMembershipsRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setCustomerId(customerId)
                .setContext(builder.build());
        if (!CollectionUtils.isEmpty(needAppliedMembershipIds)) {
            requestBuilder.setFilter(ListRecommendMembershipsRequest.Filter.newBuilder()
                    .addAllTargetMembershipIds(needAppliedMembershipIds)
                    .build());
        }
        return membershipService.listRecommendedMemberships(requestBuilder.build());
    }

    private TargetType getTargetType(String type, Long objectId, Map<Integer, MoeGroomingService> serviceMap) {
        if (Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), type)) {
            if (!serviceMap.containsKey(objectId.intValue())) {
                return TargetType.TARGET_TYPE_UNSPECIFIED;
            }
            MoeGroomingService service = serviceMap.get(objectId.intValue());
            if (ServiceEnum.TYPE_SERVICE.equals(service.getType())) {
                return TargetType.SERVICE;
            } else if (ServiceEnum.TYPE_ADD_ONS.equals(service.getType())) {
                return TargetType.ADDON;
            }
        } else if (Objects.equals(OrderItemType.ITEM_TYPE_PRODUCT.getType(), type)) {
            return TargetType.PRODUCT;
        }
        return TargetType.TARGET_TYPE_UNSPECIFIED;
    }

    private static OrderLineDiscountModel convertToLineDiscountModel(
            Long orderItemId, List<MembershipUsageView> values, Long orderId, Long businessId, Long staffId) {
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }

        double discountAmount = values.stream()
                .map(MembershipUsageView::getPriceReduction)
                .reduce(Double::sum)
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "price reduction not found"));

        OrderLineDiscountModel.Builder discountBuilder = OrderLineDiscountModel.newBuilder();
        discountBuilder.setDiscountType(DiscountType.AMOUNT.getType());
        discountBuilder.setDiscountAmount(discountAmount);
        discountBuilder.setOrderId(orderId);
        discountBuilder.setOrderItemId(orderItemId);
        discountBuilder.setApplyBy(staffId);
        discountBuilder.setBusinessId(businessId);
        discountBuilder.setApplyType(LineApplyType.TYPE_ITEM.getType());
        discountBuilder.setIsDeleted(false);
        return discountBuilder.build();
    }
}
