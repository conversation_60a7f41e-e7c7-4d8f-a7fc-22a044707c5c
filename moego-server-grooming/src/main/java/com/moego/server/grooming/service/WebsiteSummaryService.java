package com.moego.server.grooming.service;

import static com.moego.server.grooming.service.WebsiteSummaryService.WebsiteSummaryCacheModel.Appointment;
import static com.moego.server.grooming.service.WebsiteSummaryService.WebsiteSummaryCacheModel.Customer;
import static com.moego.server.grooming.service.WebsiteSummaryService.WebsiteSummaryCacheModel.Pet;

import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.WebsiteCustomerSummaryDTO;
import com.moego.server.customer.dto.WebsitePetSummaryDTO;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.po.WebsiteAppointmentSummaryPO;
import com.moego.server.grooming.service.dto.WebsiteSummaryDTO;
import java.time.Duration;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class WebsiteSummaryService {

    private static final Logger log = LoggerFactory.getLogger(WebsiteSummaryService.class);

    /**
     * Cache key for website data summary.
     */
    public static final String KEY = "report:website_data_summary";

    private static final Duration TTL = Duration.ofDays(180);

    @Autowired
    private StringRedisTemplate redis;

    @Autowired
    private ICustomerCustomerService customerService;

    @Autowired
    private IPetService petService;

    @Autowired
    private AppointmentMapperProxy appointmentMapper;

    /**
     * Get website data summary.
     *
     * @return {@link WebsiteSummaryDTO}
     */
    public WebsiteSummaryDTO getSummary() {
        String v = redis.opsForValue().get(KEY);
        WebsiteSummaryCacheModel model = Optional.ofNullable(v)
                .map(val -> JsonUtil.toBean(val, WebsiteSummaryCacheModel.class))
                .orElseGet(this::queryAllAndAddCache);
        return new WebsiteSummaryDTO(
                model.appointment().count(),
                model.pet().count(),
                model.customer().count());
    }

    /**
     * Refresh website data summary cache.
     *
     * @return {@link WebsiteSummaryCacheModel}
     */
    public WebsiteSummaryCacheModel refreshCache() {
        String v = redis.opsForValue().get(KEY);
        if (v != null) {
            return incrementalQueryAndUpdateCache(v);
        }
        return queryAllAndAddCache();
    }

    private WebsiteSummaryCacheModel incrementalQueryAndUpdateCache(String cacheV) {
        WebsiteSummaryCacheModel lastSummary = JsonUtil.toBean(cacheV, WebsiteSummaryCacheModel.class);
        log.info("Found website summary data cache: {}", lastSummary);

        WebsiteAppointmentSummaryPO apptSummary =
                appointmentMapper.countBetween(lastSummary.appointment().maxId() + 1, null);
        WebsitePetSummaryDTO petSummary =
                petService.countBetween(lastSummary.pet().maxId() + 1, null);
        WebsiteCustomerSummaryDTO customerSummary =
                customerService.countBetween(lastSummary.customer().maxId() + 1, null);

        WebsiteSummaryCacheModel cacheValue = new WebsiteSummaryCacheModel(
                new Appointment(
                        Optional.ofNullable(apptSummary.getMaxId())
                                .orElse(lastSummary.appointment().maxId()),
                        apptSummary.getCount() + lastSummary.appointment().count()),
                new Pet(
                        Optional.ofNullable(petSummary.getMaxId())
                                .orElse(lastSummary.pet().maxId()),
                        petSummary.getCount() + lastSummary.pet().count()),
                new Customer(
                        Optional.ofNullable(customerSummary.getMaxId())
                                .orElse(lastSummary.customer().maxId()),
                        customerSummary.getCount() + lastSummary.customer().count()));
        if (Objects.equals(lastSummary, cacheValue)) {
            log.info("No new data, skip update website summary data cache.");
            return cacheValue;
        }
        redis.opsForValue().set(KEY, JsonUtil.toJson(cacheValue), TTL);
        log.info("Update website summary data cache: {}", cacheValue);
        return cacheValue;
    }

    private WebsiteSummaryCacheModel queryAllAndAddCache() {
        WebsiteAppointmentSummaryPO apptSummary = appointmentMapper.countBetween(null, null);
        WebsitePetSummaryDTO petSummary = petService.countBetween(null, null);
        WebsiteCustomerSummaryDTO customerSummary = customerService.countBetween(null, null);
        WebsiteSummaryCacheModel cacheValue = new WebsiteSummaryCacheModel(
                new Appointment(apptSummary.getMaxId(), apptSummary.getCount()),
                new Pet(petSummary.getMaxId(), petSummary.getCount()),
                new Customer(customerSummary.getMaxId(), customerSummary.getCount()));
        redis.opsForValue().set(KEY, JsonUtil.toJson(cacheValue), TTL);
        log.info("Add website data summary cache: {}", cacheValue);
        return cacheValue;
    }

    record WebsiteSummaryCacheModel(Appointment appointment, Pet pet, Customer customer) {
        record Appointment(int maxId, int count) {}

        record Pet(int maxId, int count) {}

        record Customer(int maxId, int count) {}
    }
}
