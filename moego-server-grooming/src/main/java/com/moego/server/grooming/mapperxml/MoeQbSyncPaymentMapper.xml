<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeQbSyncPaymentMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeQbSyncPayment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="connect_id" jdbcType="INTEGER" property="connectId" />
    <result column="realm_id" jdbcType="VARCHAR" property="realmId" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="payment_id" jdbcType="INTEGER" property="paymentId" />
    <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
    <result column="qb_invoice_id" jdbcType="VARCHAR" property="qbInvoiceId" />
    <result column="qb_payment_id" jdbcType="VARCHAR" property="qbPaymentId" />
    <result column="qb_payment_status" jdbcType="TINYINT" property="qbPaymentStatus" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, connect_id, realm_id, invoice_id, payment_id, paid_amount, qb_invoice_id,
    qb_payment_id, qb_payment_status, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_qb_sync_payment
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_sync_payment
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncPayment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_payment (business_id, connect_id, realm_id,
      invoice_id, payment_id, paid_amount,
      qb_invoice_id, qb_payment_id, qb_payment_status,
      create_time, update_time, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{connectId,jdbcType=INTEGER}, #{realmId,jdbcType=VARCHAR},
      #{invoiceId,jdbcType=INTEGER}, #{paymentId,jdbcType=INTEGER}, #{paidAmount,jdbcType=DECIMAL},
      #{qbInvoiceId,jdbcType=VARCHAR}, #{qbPaymentId,jdbcType=VARCHAR}, #{qbPaymentStatus,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncPayment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_payment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="connectId != null">
        connect_id,
      </if>
      <if test="realmId != null">
        realm_id,
      </if>
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="qbInvoiceId != null">
        qb_invoice_id,
      </if>
      <if test="qbPaymentId != null">
        qb_payment_id,
      </if>
      <if test="qbPaymentStatus != null">
        qb_payment_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="paymentId != null">
        #{paymentId,jdbcType=INTEGER},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="qbInvoiceId != null">
        #{qbInvoiceId,jdbcType=VARCHAR},
      </if>
      <if test="qbPaymentId != null">
        #{qbPaymentId,jdbcType=VARCHAR},
      </if>
      <if test="qbPaymentStatus != null">
        #{qbPaymentStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncPayment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_payment
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        connect_id = #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        realm_id = #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=INTEGER},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="qbInvoiceId != null">
        qb_invoice_id = #{qbInvoiceId,jdbcType=VARCHAR},
      </if>
      <if test="qbPaymentId != null">
        qb_payment_id = #{qbPaymentId,jdbcType=VARCHAR},
      </if>
      <if test="qbPaymentStatus != null">
        qb_payment_status = #{qbPaymentStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncPayment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_payment
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      invoice_id = #{invoiceId,jdbcType=INTEGER},
      payment_id = #{paymentId,jdbcType=INTEGER},
      paid_amount = #{paidAmount,jdbcType=DECIMAL},
      qb_invoice_id = #{qbInvoiceId,jdbcType=VARCHAR},
      qb_payment_id = #{qbPaymentId,jdbcType=VARCHAR},
      qb_payment_status = #{qbPaymentStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByInvoiceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_qb_sync_payment
        where business_id = #{businessId} and
        realm_id = #{realmId} and
        invoice_id = #{invoiceId}
    </select>
    <select id="selectByBusinessIdAndPaymentId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_qb_sync_payment
        where business_id = #{businessId} and
        realm_id = #{realmId} and
        payment_id = #{paymentId}
        order by update_time desc limit 1;
    </select>
  <select id="listByBusinessAndInvoiceIds"
          resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
      from moe_qb_sync_payment
        where business_id = #{businessId}
    and invoice_id in
    <foreach collection="invoiceIds" item="item" open="(" close=")" separator=",">
        ${item}
    </foreach>
  </select>

</mapper>
