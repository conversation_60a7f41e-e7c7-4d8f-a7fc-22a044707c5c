package com.moego.server.grooming.mapperbean;

import com.moego.server.grooming.dto.BookOnlineDTO.PaymentOption;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MoeBusinessBookOnlineExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public MoeBusinessBookOnlineExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> serviceAreasCriteria;

        protected List<Criterion> paymentOptionMapCriteria;

        protected List<Criterion> allCriteria;

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
            serviceAreasCriteria = new ArrayList<>();
            paymentOptionMapCriteria = new ArrayList<>();
        }

        public List<Criterion> getServiceAreasCriteria() {
            return serviceAreasCriteria;
        }

        protected void addServiceAreasCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            serviceAreasCriteria.add(
                    new Criterion(condition, value, "com.moego.server.grooming.mapper.typehandler.IntListTypeHandler"));
            allCriteria = null;
        }

        protected void addServiceAreasCriterion(
                String condition, List<Integer> value1, List<Integer> value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            serviceAreasCriteria.add(new Criterion(
                    condition, value1, value2, "com.moego.server.grooming.mapper.typehandler.IntListTypeHandler"));
            allCriteria = null;
        }

        public List<Criterion> getPaymentOptionMapCriteria() {
            return paymentOptionMapCriteria;
        }

        protected void addPaymentOptionMapCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            paymentOptionMapCriteria.add(new Criterion(
                    condition,
                    value,
                    "com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler"));
            allCriteria = null;
        }

        protected void addPaymentOptionMapCriterion(
                String condition,
                Map<Integer, PaymentOption> value1,
                Map<Integer, PaymentOption> value2,
                String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            paymentOptionMapCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler"));
            allCriteria = null;
        }

        public boolean isValid() {
            return criteria.size() > 0 || serviceAreasCriteria.size() > 0 || paymentOptionMapCriteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            if (allCriteria == null) {
                allCriteria = new ArrayList<>();
                allCriteria.addAll(criteria);
                allCriteria.addAll(serviceAreasCriteria);
                allCriteria.addAll(paymentOptionMapCriteria);
            }
            return allCriteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
            allCriteria = null;
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andIsEnableIsNull() {
            addCriterion("is_enable is null");
            return (Criteria) this;
        }

        public Criteria andIsEnableIsNotNull() {
            addCriterion("is_enable is not null");
            return (Criteria) this;
        }

        public Criteria andIsEnableEqualTo(Byte value) {
            addCriterion("is_enable =", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableNotEqualTo(Byte value) {
            addCriterion("is_enable <>", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableGreaterThan(Byte value) {
            addCriterion("is_enable >", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_enable >=", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableLessThan(Byte value) {
            addCriterion("is_enable <", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableLessThanOrEqualTo(Byte value) {
            addCriterion("is_enable <=", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableIn(List<Byte> values) {
            addCriterion("is_enable in", values, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableNotIn(List<Byte> values) {
            addCriterion("is_enable not in", values, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableBetween(Byte value1, Byte value2) {
            addCriterion("is_enable between", value1, value2, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("is_enable not between", value1, value2, "isEnable");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistIsNull() {
            addCriterion("max_available_dist is null");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistIsNotNull() {
            addCriterion("max_available_dist is not null");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistEqualTo(Integer value) {
            addCriterion("max_available_dist =", value, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistNotEqualTo(Integer value) {
            addCriterion("max_available_dist <>", value, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistGreaterThan(Integer value) {
            addCriterion("max_available_dist >", value, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_available_dist >=", value, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistLessThan(Integer value) {
            addCriterion("max_available_dist <", value, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistLessThanOrEqualTo(Integer value) {
            addCriterion("max_available_dist <=", value, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistIn(List<Integer> values) {
            addCriterion("max_available_dist in", values, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistNotIn(List<Integer> values) {
            addCriterion("max_available_dist not in", values, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistBetween(Integer value1, Integer value2) {
            addCriterion("max_available_dist between", value1, value2, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableDistNotBetween(Integer value1, Integer value2) {
            addCriterion("max_available_dist not between", value1, value2, "maxAvailableDist");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeIsNull() {
            addCriterion("max_available_time is null");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeIsNotNull() {
            addCriterion("max_available_time is not null");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeEqualTo(Integer value) {
            addCriterion("max_available_time =", value, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeNotEqualTo(Integer value) {
            addCriterion("max_available_time <>", value, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeGreaterThan(Integer value) {
            addCriterion("max_available_time >", value, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_available_time >=", value, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeLessThan(Integer value) {
            addCriterion("max_available_time <", value, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeLessThanOrEqualTo(Integer value) {
            addCriterion("max_available_time <=", value, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeIn(List<Integer> values) {
            addCriterion("max_available_time in", values, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeNotIn(List<Integer> values) {
            addCriterion("max_available_time not in", values, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeBetween(Integer value1, Integer value2) {
            addCriterion("max_available_time between", value1, value2, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andMaxAvailableTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("max_available_time not between", value1, value2, "maxAvailableTime");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableIsNull() {
            addCriterion("soonest_available is null");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableIsNotNull() {
            addCriterion("soonest_available is not null");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableEqualTo(Integer value) {
            addCriterion("soonest_available =", value, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableNotEqualTo(Integer value) {
            addCriterion("soonest_available <>", value, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableGreaterThan(Integer value) {
            addCriterion("soonest_available >", value, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableGreaterThanOrEqualTo(Integer value) {
            addCriterion("soonest_available >=", value, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableLessThan(Integer value) {
            addCriterion("soonest_available <", value, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableLessThanOrEqualTo(Integer value) {
            addCriterion("soonest_available <=", value, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableIn(List<Integer> values) {
            addCriterion("soonest_available in", values, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableNotIn(List<Integer> values) {
            addCriterion("soonest_available not in", values, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableBetween(Integer value1, Integer value2) {
            addCriterion("soonest_available between", value1, value2, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andSoonestAvailableNotBetween(Integer value1, Integer value2) {
            addCriterion("soonest_available not between", value1, value2, "soonestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableIsNull() {
            addCriterion("farest_available is null");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableIsNotNull() {
            addCriterion("farest_available is not null");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableEqualTo(Integer value) {
            addCriterion("farest_available =", value, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableNotEqualTo(Integer value) {
            addCriterion("farest_available <>", value, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableGreaterThan(Integer value) {
            addCriterion("farest_available >", value, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableGreaterThanOrEqualTo(Integer value) {
            addCriterion("farest_available >=", value, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableLessThan(Integer value) {
            addCriterion("farest_available <", value, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableLessThanOrEqualTo(Integer value) {
            addCriterion("farest_available <=", value, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableIn(List<Integer> values) {
            addCriterion("farest_available in", values, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableNotIn(List<Integer> values) {
            addCriterion("farest_available not in", values, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableBetween(Integer value1, Integer value2) {
            addCriterion("farest_available between", value1, value2, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andFarestAvailableNotBetween(Integer value1, Integer value2) {
            addCriterion("farest_available not between", value1, value2, "farestAvailable");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementIsNull() {
            addCriterion("is_require_agreement is null");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementIsNotNull() {
            addCriterion("is_require_agreement is not null");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementEqualTo(Byte value) {
            addCriterion("is_require_agreement =", value, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementNotEqualTo(Byte value) {
            addCriterion("is_require_agreement <>", value, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementGreaterThan(Byte value) {
            addCriterion("is_require_agreement >", value, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_require_agreement >=", value, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementLessThan(Byte value) {
            addCriterion("is_require_agreement <", value, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementLessThanOrEqualTo(Byte value) {
            addCriterion("is_require_agreement <=", value, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementIn(List<Byte> values) {
            addCriterion("is_require_agreement in", values, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementNotIn(List<Byte> values) {
            addCriterion("is_require_agreement not in", values, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementBetween(Byte value1, Byte value2) {
            addCriterion("is_require_agreement between", value1, value2, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andIsRequireAgreementNotBetween(Byte value1, Byte value2) {
            addCriterion("is_require_agreement not between", value1, value2, "isRequireAgreement");
            return (Criteria) this;
        }

        public Criteria andZipCodeIsNull() {
            addCriterion("zip_code is null");
            return (Criteria) this;
        }

        public Criteria andZipCodeIsNotNull() {
            addCriterion("zip_code is not null");
            return (Criteria) this;
        }

        public Criteria andZipCodeEqualTo(String value) {
            addCriterion("zip_code =", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeNotEqualTo(String value) {
            addCriterion("zip_code <>", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeGreaterThan(String value) {
            addCriterion("zip_code >", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeGreaterThanOrEqualTo(String value) {
            addCriterion("zip_code >=", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeLessThan(String value) {
            addCriterion("zip_code <", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeLessThanOrEqualTo(String value) {
            addCriterion("zip_code <=", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeLike(String value) {
            addCriterion("zip_code like", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeNotLike(String value) {
            addCriterion("zip_code not like", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeIn(List<String> values) {
            addCriterion("zip_code in", values, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeNotIn(List<String> values) {
            addCriterion("zip_code not in", values, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeBetween(String value1, String value2) {
            addCriterion("zip_code between", value1, value2, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeNotBetween(String value1, String value2) {
            addCriterion("zip_code not between", value1, value2, "zipCode");
            return (Criteria) this;
        }

        public Criteria andPlaceNameIsNull() {
            addCriterion("place_name is null");
            return (Criteria) this;
        }

        public Criteria andPlaceNameIsNotNull() {
            addCriterion("place_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlaceNameEqualTo(String value) {
            addCriterion("place_name =", value, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameNotEqualTo(String value) {
            addCriterion("place_name <>", value, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameGreaterThan(String value) {
            addCriterion("place_name >", value, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameGreaterThanOrEqualTo(String value) {
            addCriterion("place_name >=", value, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameLessThan(String value) {
            addCriterion("place_name <", value, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameLessThanOrEqualTo(String value) {
            addCriterion("place_name <=", value, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameLike(String value) {
            addCriterion("place_name like", value, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameNotLike(String value) {
            addCriterion("place_name not like", value, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameIn(List<String> values) {
            addCriterion("place_name in", values, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameNotIn(List<String> values) {
            addCriterion("place_name not in", values, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameBetween(String value1, String value2) {
            addCriterion("place_name between", value1, value2, "placeName");
            return (Criteria) this;
        }

        public Criteria andPlaceNameNotBetween(String value1, String value2) {
            addCriterion("place_name not between", value1, value2, "placeName");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("state is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("state is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("state =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("state <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("state >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("state >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("state <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("state <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("state like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("state not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("state in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("state not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("state between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("state not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationIsNull() {
            addCriterion("state_abbreviation is null");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationIsNotNull() {
            addCriterion("state_abbreviation is not null");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationEqualTo(String value) {
            addCriterion("state_abbreviation =", value, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationNotEqualTo(String value) {
            addCriterion("state_abbreviation <>", value, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationGreaterThan(String value) {
            addCriterion("state_abbreviation >", value, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationGreaterThanOrEqualTo(String value) {
            addCriterion("state_abbreviation >=", value, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationLessThan(String value) {
            addCriterion("state_abbreviation <", value, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationLessThanOrEqualTo(String value) {
            addCriterion("state_abbreviation <=", value, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationLike(String value) {
            addCriterion("state_abbreviation like", value, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationNotLike(String value) {
            addCriterion("state_abbreviation not like", value, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationIn(List<String> values) {
            addCriterion("state_abbreviation in", values, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationNotIn(List<String> values) {
            addCriterion("state_abbreviation not in", values, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationBetween(String value1, String value2) {
            addCriterion("state_abbreviation between", value1, value2, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andStateAbbreviationNotBetween(String value1, String value2) {
            addCriterion("state_abbreviation not between", value1, value2, "stateAbbreviation");
            return (Criteria) this;
        }

        public Criteria andCountyIsNull() {
            addCriterion("county is null");
            return (Criteria) this;
        }

        public Criteria andCountyIsNotNull() {
            addCriterion("county is not null");
            return (Criteria) this;
        }

        public Criteria andCountyEqualTo(String value) {
            addCriterion("county =", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyNotEqualTo(String value) {
            addCriterion("county <>", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyGreaterThan(String value) {
            addCriterion("county >", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyGreaterThanOrEqualTo(String value) {
            addCriterion("county >=", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyLessThan(String value) {
            addCriterion("county <", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyLessThanOrEqualTo(String value) {
            addCriterion("county <=", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyLike(String value) {
            addCriterion("county like", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyNotLike(String value) {
            addCriterion("county not like", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyIn(List<String> values) {
            addCriterion("county in", values, "county");
            return (Criteria) this;
        }

        public Criteria andCountyNotIn(List<String> values) {
            addCriterion("county not in", values, "county");
            return (Criteria) this;
        }

        public Criteria andCountyBetween(String value1, String value2) {
            addCriterion("county between", value1, value2, "county");
            return (Criteria) this;
        }

        public Criteria andCountyNotBetween(String value1, String value2) {
            addCriterion("county not between", value1, value2, "county");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressIsNull() {
            addCriterion("is_need_address is null");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressIsNotNull() {
            addCriterion("is_need_address is not null");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressEqualTo(Byte value) {
            addCriterion("is_need_address =", value, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressNotEqualTo(Byte value) {
            addCriterion("is_need_address <>", value, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressGreaterThan(Byte value) {
            addCriterion("is_need_address >", value, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_need_address >=", value, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressLessThan(Byte value) {
            addCriterion("is_need_address <", value, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressLessThanOrEqualTo(Byte value) {
            addCriterion("is_need_address <=", value, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressIn(List<Byte> values) {
            addCriterion("is_need_address in", values, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressNotIn(List<Byte> values) {
            addCriterion("is_need_address not in", values, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressBetween(Byte value1, Byte value2) {
            addCriterion("is_need_address between", value1, value2, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedAddressNotBetween(Byte value1, Byte value2) {
            addCriterion("is_need_address not between", value1, value2, "isNeedAddress");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeIsNull() {
            addCriterion("is_need_select_time is null");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeIsNotNull() {
            addCriterion("is_need_select_time is not null");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeEqualTo(Byte value) {
            addCriterion("is_need_select_time =", value, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeNotEqualTo(Byte value) {
            addCriterion("is_need_select_time <>", value, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeGreaterThan(Byte value) {
            addCriterion("is_need_select_time >", value, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_need_select_time >=", value, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeLessThan(Byte value) {
            addCriterion("is_need_select_time <", value, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeLessThanOrEqualTo(Byte value) {
            addCriterion("is_need_select_time <=", value, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeIn(List<Byte> values) {
            addCriterion("is_need_select_time in", values, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeNotIn(List<Byte> values) {
            addCriterion("is_need_select_time not in", values, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeBetween(Byte value1, Byte value2) {
            addCriterion("is_need_select_time between", value1, value2, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andIsNeedSelectTimeNotBetween(Byte value1, Byte value2) {
            addCriterion("is_need_select_time not between", value1, value2, "isNeedSelectTime");
            return (Criteria) this;
        }

        public Criteria andFakeItIsNull() {
            addCriterion("fake_it is null");
            return (Criteria) this;
        }

        public Criteria andFakeItIsNotNull() {
            addCriterion("fake_it is not null");
            return (Criteria) this;
        }

        public Criteria andFakeItEqualTo(Byte value) {
            addCriterion("fake_it =", value, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andFakeItNotEqualTo(Byte value) {
            addCriterion("fake_it <>", value, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andFakeItGreaterThan(Byte value) {
            addCriterion("fake_it >", value, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andFakeItGreaterThanOrEqualTo(Byte value) {
            addCriterion("fake_it >=", value, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andFakeItLessThan(Byte value) {
            addCriterion("fake_it <", value, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andFakeItLessThanOrEqualTo(Byte value) {
            addCriterion("fake_it <=", value, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andFakeItIn(List<Byte> values) {
            addCriterion("fake_it in", values, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andFakeItNotIn(List<Byte> values) {
            addCriterion("fake_it not in", values, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andFakeItBetween(Byte value1, Byte value2) {
            addCriterion("fake_it between", value1, value2, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andFakeItNotBetween(Byte value1, Byte value2) {
            addCriterion("fake_it not between", value1, value2, "fakeIt");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeIsNull() {
            addCriterion("enable_no_show_fee is null");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeIsNotNull() {
            addCriterion("enable_no_show_fee is not null");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeEqualTo(Byte value) {
            addCriterion("enable_no_show_fee =", value, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeNotEqualTo(Byte value) {
            addCriterion("enable_no_show_fee <>", value, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeGreaterThan(Byte value) {
            addCriterion("enable_no_show_fee >", value, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeGreaterThanOrEqualTo(Byte value) {
            addCriterion("enable_no_show_fee >=", value, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeLessThan(Byte value) {
            addCriterion("enable_no_show_fee <", value, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeLessThanOrEqualTo(Byte value) {
            addCriterion("enable_no_show_fee <=", value, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeIn(List<Byte> values) {
            addCriterion("enable_no_show_fee in", values, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeNotIn(List<Byte> values) {
            addCriterion("enable_no_show_fee not in", values, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeBetween(Byte value1, Byte value2) {
            addCriterion("enable_no_show_fee between", value1, value2, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andEnableNoShowFeeNotBetween(Byte value1, Byte value2) {
            addCriterion("enable_no_show_fee not between", value1, value2, "enableNoShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeIsNull() {
            addCriterion("no_show_fee is null");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeIsNotNull() {
            addCriterion("no_show_fee is not null");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeEqualTo(BigDecimal value) {
            addCriterion("no_show_fee =", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeNotEqualTo(BigDecimal value) {
            addCriterion("no_show_fee <>", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeGreaterThan(BigDecimal value) {
            addCriterion("no_show_fee >", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_show_fee >=", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeLessThan(BigDecimal value) {
            addCriterion("no_show_fee <", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_show_fee <=", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeIn(List<BigDecimal> values) {
            addCriterion("no_show_fee in", values, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeNotIn(List<BigDecimal> values) {
            addCriterion("no_show_fee not in", values, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_show_fee between", value1, value2, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_show_fee not between", value1, value2, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalIsNull() {
            addCriterion("appointment_interval is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalIsNotNull() {
            addCriterion("appointment_interval is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalEqualTo(Integer value) {
            addCriterion("appointment_interval =", value, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalNotEqualTo(Integer value) {
            addCriterion("appointment_interval <>", value, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalGreaterThan(Integer value) {
            addCriterion("appointment_interval >", value, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalGreaterThanOrEqualTo(Integer value) {
            addCriterion("appointment_interval >=", value, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalLessThan(Integer value) {
            addCriterion("appointment_interval <", value, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalLessThanOrEqualTo(Integer value) {
            addCriterion("appointment_interval <=", value, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalIn(List<Integer> values) {
            addCriterion("appointment_interval in", values, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalNotIn(List<Integer> values) {
            addCriterion("appointment_interval not in", values, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalBetween(Integer value1, Integer value2) {
            addCriterion("appointment_interval between", value1, value2, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andAppointmentIntervalNotBetween(Integer value1, Integer value2) {
            addCriterion("appointment_interval not between", value1, value2, "appointmentInterval");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsIsNull() {
            addCriterion("timeslot_mins is null");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsIsNotNull() {
            addCriterion("timeslot_mins is not null");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsEqualTo(Integer value) {
            addCriterion("timeslot_mins =", value, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsNotEqualTo(Integer value) {
            addCriterion("timeslot_mins <>", value, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsGreaterThan(Integer value) {
            addCriterion("timeslot_mins >", value, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsGreaterThanOrEqualTo(Integer value) {
            addCriterion("timeslot_mins >=", value, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsLessThan(Integer value) {
            addCriterion("timeslot_mins <", value, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsLessThanOrEqualTo(Integer value) {
            addCriterion("timeslot_mins <=", value, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsIn(List<Integer> values) {
            addCriterion("timeslot_mins in", values, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsNotIn(List<Integer> values) {
            addCriterion("timeslot_mins not in", values, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsBetween(Integer value1, Integer value2) {
            addCriterion("timeslot_mins between", value1, value2, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotMinsNotBetween(Integer value1, Integer value2) {
            addCriterion("timeslot_mins not between", value1, value2, "timeslotMins");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatIsNull() {
            addCriterion("timeslot_format is null");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatIsNotNull() {
            addCriterion("timeslot_format is not null");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatEqualTo(Byte value) {
            addCriterion("timeslot_format =", value, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatNotEqualTo(Byte value) {
            addCriterion("timeslot_format <>", value, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatGreaterThan(Byte value) {
            addCriterion("timeslot_format >", value, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatGreaterThanOrEqualTo(Byte value) {
            addCriterion("timeslot_format >=", value, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatLessThan(Byte value) {
            addCriterion("timeslot_format <", value, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatLessThanOrEqualTo(Byte value) {
            addCriterion("timeslot_format <=", value, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatIn(List<Byte> values) {
            addCriterion("timeslot_format in", values, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatNotIn(List<Byte> values) {
            addCriterion("timeslot_format not in", values, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatBetween(Byte value1, Byte value2) {
            addCriterion("timeslot_format between", value1, value2, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andTimeslotFormatNotBetween(Byte value1, Byte value2) {
            addCriterion("timeslot_format not between", value1, value2, "timeslotFormat");
            return (Criteria) this;
        }

        public Criteria andAcceptClientIsNull() {
            addCriterion("accept_client is null");
            return (Criteria) this;
        }

        public Criteria andAcceptClientIsNotNull() {
            addCriterion("accept_client is not null");
            return (Criteria) this;
        }

        public Criteria andAcceptClientEqualTo(Byte value) {
            addCriterion("accept_client =", value, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAcceptClientNotEqualTo(Byte value) {
            addCriterion("accept_client <>", value, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAcceptClientGreaterThan(Byte value) {
            addCriterion("accept_client >", value, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAcceptClientGreaterThanOrEqualTo(Byte value) {
            addCriterion("accept_client >=", value, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAcceptClientLessThan(Byte value) {
            addCriterion("accept_client <", value, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAcceptClientLessThanOrEqualTo(Byte value) {
            addCriterion("accept_client <=", value, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAcceptClientIn(List<Byte> values) {
            addCriterion("accept_client in", values, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAcceptClientNotIn(List<Byte> values) {
            addCriterion("accept_client not in", values, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAcceptClientBetween(Byte value1, Byte value2) {
            addCriterion("accept_client between", value1, value2, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAcceptClientNotBetween(Byte value1, Byte value2) {
            addCriterion("accept_client not between", value1, value2, "acceptClient");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitIsNull() {
            addCriterion("auto_move_wait is null");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitIsNotNull() {
            addCriterion("auto_move_wait is not null");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitEqualTo(Byte value) {
            addCriterion("auto_move_wait =", value, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitNotEqualTo(Byte value) {
            addCriterion("auto_move_wait <>", value, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitGreaterThan(Byte value) {
            addCriterion("auto_move_wait >", value, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitGreaterThanOrEqualTo(Byte value) {
            addCriterion("auto_move_wait >=", value, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitLessThan(Byte value) {
            addCriterion("auto_move_wait <", value, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitLessThanOrEqualTo(Byte value) {
            addCriterion("auto_move_wait <=", value, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitIn(List<Byte> values) {
            addCriterion("auto_move_wait in", values, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitNotIn(List<Byte> values) {
            addCriterion("auto_move_wait not in", values, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitBetween(Byte value1, Byte value2) {
            addCriterion("auto_move_wait between", value1, value2, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andAutoMoveWaitNotBetween(Byte value1, Byte value2) {
            addCriterion("auto_move_wait not between", value1, value2, "autoMoveWait");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableIsNull() {
            addCriterion("service_area_enable is null");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableIsNotNull() {
            addCriterion("service_area_enable is not null");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableEqualTo(Byte value) {
            addCriterion("service_area_enable =", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableNotEqualTo(Byte value) {
            addCriterion("service_area_enable <>", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableGreaterThan(Byte value) {
            addCriterion("service_area_enable >", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("service_area_enable >=", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableLessThan(Byte value) {
            addCriterion("service_area_enable <", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableLessThanOrEqualTo(Byte value) {
            addCriterion("service_area_enable <=", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableIn(List<Byte> values) {
            addCriterion("service_area_enable in", values, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableNotIn(List<Byte> values) {
            addCriterion("service_area_enable not in", values, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableBetween(Byte value1, Byte value2) {
            addCriterion("service_area_enable between", value1, value2, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("service_area_enable not between", value1, value2, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyIsNull() {
            addCriterion("weight_limit_notify is null");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyIsNotNull() {
            addCriterion("weight_limit_notify is not null");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyEqualTo(Byte value) {
            addCriterion("weight_limit_notify =", value, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyNotEqualTo(Byte value) {
            addCriterion("weight_limit_notify <>", value, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyGreaterThan(Byte value) {
            addCriterion("weight_limit_notify >", value, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyGreaterThanOrEqualTo(Byte value) {
            addCriterion("weight_limit_notify >=", value, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyLessThan(Byte value) {
            addCriterion("weight_limit_notify <", value, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyLessThanOrEqualTo(Byte value) {
            addCriterion("weight_limit_notify <=", value, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyIn(List<Byte> values) {
            addCriterion("weight_limit_notify in", values, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyNotIn(List<Byte> values) {
            addCriterion("weight_limit_notify not in", values, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyBetween(Byte value1, Byte value2) {
            addCriterion("weight_limit_notify between", value1, value2, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotifyNotBetween(Byte value1, Byte value2) {
            addCriterion("weight_limit_notify not between", value1, value2, "weightLimitNotify");
            return (Criteria) this;
        }

        public Criteria andWeightLimitIsNull() {
            addCriterion("weight_limit is null");
            return (Criteria) this;
        }

        public Criteria andWeightLimitIsNotNull() {
            addCriterion("weight_limit is not null");
            return (Criteria) this;
        }

        public Criteria andWeightLimitEqualTo(Integer value) {
            addCriterion("weight_limit =", value, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotEqualTo(Integer value) {
            addCriterion("weight_limit <>", value, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andWeightLimitGreaterThan(Integer value) {
            addCriterion("weight_limit >", value, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andWeightLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("weight_limit >=", value, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andWeightLimitLessThan(Integer value) {
            addCriterion("weight_limit <", value, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andWeightLimitLessThanOrEqualTo(Integer value) {
            addCriterion("weight_limit <=", value, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andWeightLimitIn(List<Integer> values) {
            addCriterion("weight_limit in", values, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotIn(List<Integer> values) {
            addCriterion("weight_limit not in", values, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andWeightLimitBetween(Integer value1, Integer value2) {
            addCriterion("weight_limit between", value1, value2, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andWeightLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("weight_limit not between", value1, value2, "weightLimit");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsIsNull() {
            addCriterion("over_limit_tips is null");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsIsNotNull() {
            addCriterion("over_limit_tips is not null");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsEqualTo(String value) {
            addCriterion("over_limit_tips =", value, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsNotEqualTo(String value) {
            addCriterion("over_limit_tips <>", value, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsGreaterThan(String value) {
            addCriterion("over_limit_tips >", value, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsGreaterThanOrEqualTo(String value) {
            addCriterion("over_limit_tips >=", value, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsLessThan(String value) {
            addCriterion("over_limit_tips <", value, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsLessThanOrEqualTo(String value) {
            addCriterion("over_limit_tips <=", value, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsLike(String value) {
            addCriterion("over_limit_tips like", value, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsNotLike(String value) {
            addCriterion("over_limit_tips not like", value, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsIn(List<String> values) {
            addCriterion("over_limit_tips in", values, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsNotIn(List<String> values) {
            addCriterion("over_limit_tips not in", values, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsBetween(String value1, String value2) {
            addCriterion("over_limit_tips between", value1, value2, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andOverLimitTipsNotBetween(String value1, String value2) {
            addCriterion("over_limit_tips not between", value1, value2, "overLimitTips");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaIsNull() {
            addCriterion("need_within_area is null");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaIsNotNull() {
            addCriterion("need_within_area is not null");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaEqualTo(Byte value) {
            addCriterion("need_within_area =", value, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaNotEqualTo(Byte value) {
            addCriterion("need_within_area <>", value, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaGreaterThan(Byte value) {
            addCriterion("need_within_area >", value, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaGreaterThanOrEqualTo(Byte value) {
            addCriterion("need_within_area >=", value, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaLessThan(Byte value) {
            addCriterion("need_within_area <", value, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaLessThanOrEqualTo(Byte value) {
            addCriterion("need_within_area <=", value, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaIn(List<Byte> values) {
            addCriterion("need_within_area in", values, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaNotIn(List<Byte> values) {
            addCriterion("need_within_area not in", values, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaBetween(Byte value1, Byte value2) {
            addCriterion("need_within_area between", value1, value2, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andNeedWithinAreaNotBetween(Byte value1, Byte value2) {
            addCriterion("need_within_area not between", value1, value2, "needWithinArea");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeIsNull() {
            addCriterion("is_by_zipcode is null");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeIsNotNull() {
            addCriterion("is_by_zipcode is not null");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeEqualTo(Byte value) {
            addCriterion("is_by_zipcode =", value, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeNotEqualTo(Byte value) {
            addCriterion("is_by_zipcode <>", value, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeGreaterThan(Byte value) {
            addCriterion("is_by_zipcode >", value, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_by_zipcode >=", value, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeLessThan(Byte value) {
            addCriterion("is_by_zipcode <", value, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeLessThanOrEqualTo(Byte value) {
            addCriterion("is_by_zipcode <=", value, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeIn(List<Byte> values) {
            addCriterion("is_by_zipcode in", values, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeNotIn(List<Byte> values) {
            addCriterion("is_by_zipcode not in", values, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeBetween(Byte value1, Byte value2) {
            addCriterion("is_by_zipcode between", value1, value2, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByZipcodeNotBetween(Byte value1, Byte value2) {
            addCriterion("is_by_zipcode not between", value1, value2, "isByZipcode");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusIsNull() {
            addCriterion("is_by_radius is null");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusIsNotNull() {
            addCriterion("is_by_radius is not null");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusEqualTo(Byte value) {
            addCriterion("is_by_radius =", value, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusNotEqualTo(Byte value) {
            addCriterion("is_by_radius <>", value, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusGreaterThan(Byte value) {
            addCriterion("is_by_radius >", value, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_by_radius >=", value, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusLessThan(Byte value) {
            addCriterion("is_by_radius <", value, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusLessThanOrEqualTo(Byte value) {
            addCriterion("is_by_radius <=", value, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusIn(List<Byte> values) {
            addCriterion("is_by_radius in", values, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusNotIn(List<Byte> values) {
            addCriterion("is_by_radius not in", values, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusBetween(Byte value1, Byte value2) {
            addCriterion("is_by_radius between", value1, value2, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andIsByRadiusNotBetween(Byte value1, Byte value2) {
            addCriterion("is_by_radius not between", value1, value2, "isByRadius");
            return (Criteria) this;
        }

        public Criteria andSettingLocationIsNull() {
            addCriterion("setting_location is null");
            return (Criteria) this;
        }

        public Criteria andSettingLocationIsNotNull() {
            addCriterion("setting_location is not null");
            return (Criteria) this;
        }

        public Criteria andSettingLocationEqualTo(String value) {
            addCriterion("setting_location =", value, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationNotEqualTo(String value) {
            addCriterion("setting_location <>", value, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationGreaterThan(String value) {
            addCriterion("setting_location >", value, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationGreaterThanOrEqualTo(String value) {
            addCriterion("setting_location >=", value, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationLessThan(String value) {
            addCriterion("setting_location <", value, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationLessThanOrEqualTo(String value) {
            addCriterion("setting_location <=", value, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationLike(String value) {
            addCriterion("setting_location like", value, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationNotLike(String value) {
            addCriterion("setting_location not like", value, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationIn(List<String> values) {
            addCriterion("setting_location in", values, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationNotIn(List<String> values) {
            addCriterion("setting_location not in", values, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationBetween(String value1, String value2) {
            addCriterion("setting_location between", value1, value2, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLocationNotBetween(String value1, String value2) {
            addCriterion("setting_location not between", value1, value2, "settingLocation");
            return (Criteria) this;
        }

        public Criteria andSettingLatIsNull() {
            addCriterion("setting_lat is null");
            return (Criteria) this;
        }

        public Criteria andSettingLatIsNotNull() {
            addCriterion("setting_lat is not null");
            return (Criteria) this;
        }

        public Criteria andSettingLatEqualTo(String value) {
            addCriterion("setting_lat =", value, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatNotEqualTo(String value) {
            addCriterion("setting_lat <>", value, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatGreaterThan(String value) {
            addCriterion("setting_lat >", value, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatGreaterThanOrEqualTo(String value) {
            addCriterion("setting_lat >=", value, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatLessThan(String value) {
            addCriterion("setting_lat <", value, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatLessThanOrEqualTo(String value) {
            addCriterion("setting_lat <=", value, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatLike(String value) {
            addCriterion("setting_lat like", value, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatNotLike(String value) {
            addCriterion("setting_lat not like", value, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatIn(List<String> values) {
            addCriterion("setting_lat in", values, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatNotIn(List<String> values) {
            addCriterion("setting_lat not in", values, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatBetween(String value1, String value2) {
            addCriterion("setting_lat between", value1, value2, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLatNotBetween(String value1, String value2) {
            addCriterion("setting_lat not between", value1, value2, "settingLat");
            return (Criteria) this;
        }

        public Criteria andSettingLngIsNull() {
            addCriterion("setting_lng is null");
            return (Criteria) this;
        }

        public Criteria andSettingLngIsNotNull() {
            addCriterion("setting_lng is not null");
            return (Criteria) this;
        }

        public Criteria andSettingLngEqualTo(String value) {
            addCriterion("setting_lng =", value, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngNotEqualTo(String value) {
            addCriterion("setting_lng <>", value, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngGreaterThan(String value) {
            addCriterion("setting_lng >", value, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngGreaterThanOrEqualTo(String value) {
            addCriterion("setting_lng >=", value, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngLessThan(String value) {
            addCriterion("setting_lng <", value, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngLessThanOrEqualTo(String value) {
            addCriterion("setting_lng <=", value, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngLike(String value) {
            addCriterion("setting_lng like", value, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngNotLike(String value) {
            addCriterion("setting_lng not like", value, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngIn(List<String> values) {
            addCriterion("setting_lng in", values, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngNotIn(List<String> values) {
            addCriterion("setting_lng not in", values, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngBetween(String value1, String value2) {
            addCriterion("setting_lng between", value1, value2, "settingLng");
            return (Criteria) this;
        }

        public Criteria andSettingLngNotBetween(String value1, String value2) {
            addCriterion("setting_lng not between", value1, value2, "settingLng");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientIsNull() {
            addCriterion("is_check_existing_client is null");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientIsNotNull() {
            addCriterion("is_check_existing_client is not null");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientEqualTo(Byte value) {
            addCriterion("is_check_existing_client =", value, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientNotEqualTo(Byte value) {
            addCriterion("is_check_existing_client <>", value, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientGreaterThan(Byte value) {
            addCriterion("is_check_existing_client >", value, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_check_existing_client >=", value, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientLessThan(Byte value) {
            addCriterion("is_check_existing_client <", value, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientLessThanOrEqualTo(Byte value) {
            addCriterion("is_check_existing_client <=", value, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientIn(List<Byte> values) {
            addCriterion("is_check_existing_client in", values, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientNotIn(List<Byte> values) {
            addCriterion("is_check_existing_client not in", values, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientBetween(Byte value1, Byte value2) {
            addCriterion("is_check_existing_client between", value1, value2, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsCheckExistingClientNotBetween(Byte value1, Byte value2) {
            addCriterion("is_check_existing_client not between", value1, value2, "isCheckExistingClient");
            return (Criteria) this;
        }

        public Criteria andIsRedirectIsNull() {
            addCriterion("is_redirect is null");
            return (Criteria) this;
        }

        public Criteria andIsRedirectIsNotNull() {
            addCriterion("is_redirect is not null");
            return (Criteria) this;
        }

        public Criteria andIsRedirectEqualTo(Byte value) {
            addCriterion("is_redirect =", value, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andIsRedirectNotEqualTo(Byte value) {
            addCriterion("is_redirect <>", value, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andIsRedirectGreaterThan(Byte value) {
            addCriterion("is_redirect >", value, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andIsRedirectGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_redirect >=", value, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andIsRedirectLessThan(Byte value) {
            addCriterion("is_redirect <", value, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andIsRedirectLessThanOrEqualTo(Byte value) {
            addCriterion("is_redirect <=", value, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andIsRedirectIn(List<Byte> values) {
            addCriterion("is_redirect in", values, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andIsRedirectNotIn(List<Byte> values) {
            addCriterion("is_redirect not in", values, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andIsRedirectBetween(Byte value1, Byte value2) {
            addCriterion("is_redirect between", value1, value2, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andIsRedirectNotBetween(Byte value1, Byte value2) {
            addCriterion("is_redirect not between", value1, value2, "isRedirect");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptIsNull() {
            addCriterion("auto_accept is null");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptIsNotNull() {
            addCriterion("auto_accept is not null");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptEqualTo(Byte value) {
            addCriterion("auto_accept =", value, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptNotEqualTo(Byte value) {
            addCriterion("auto_accept <>", value, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptGreaterThan(Byte value) {
            addCriterion("auto_accept >", value, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptGreaterThanOrEqualTo(Byte value) {
            addCriterion("auto_accept >=", value, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptLessThan(Byte value) {
            addCriterion("auto_accept <", value, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptLessThanOrEqualTo(Byte value) {
            addCriterion("auto_accept <=", value, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptIn(List<Byte> values) {
            addCriterion("auto_accept in", values, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptNotIn(List<Byte> values) {
            addCriterion("auto_accept not in", values, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptBetween(Byte value1, Byte value2) {
            addCriterion("auto_accept between", value1, value2, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andAutoAcceptNotBetween(Byte value1, Byte value2) {
            addCriterion("auto_accept not between", value1, value2, "autoAccept");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeIsNull() {
            addCriterion("show_one_available_time is null");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeIsNotNull() {
            addCriterion("show_one_available_time is not null");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeEqualTo(Byte value) {
            addCriterion("show_one_available_time =", value, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeNotEqualTo(Byte value) {
            addCriterion("show_one_available_time <>", value, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeGreaterThan(Byte value) {
            addCriterion("show_one_available_time >", value, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeGreaterThanOrEqualTo(Byte value) {
            addCriterion("show_one_available_time >=", value, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeLessThan(Byte value) {
            addCriterion("show_one_available_time <", value, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeLessThanOrEqualTo(Byte value) {
            addCriterion("show_one_available_time <=", value, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeIn(List<Byte> values) {
            addCriterion("show_one_available_time in", values, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeNotIn(List<Byte> values) {
            addCriterion("show_one_available_time not in", values, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeBetween(Byte value1, Byte value2) {
            addCriterion("show_one_available_time between", value1, value2, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andShowOneAvailableTimeNotBetween(Byte value1, Byte value2) {
            addCriterion("show_one_available_time not between", value1, value2, "showOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableIsNull() {
            addCriterion("smart_schedule_enable is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableIsNotNull() {
            addCriterion("smart_schedule_enable is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableEqualTo(Byte value) {
            addCriterion("smart_schedule_enable =", value, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableNotEqualTo(Byte value) {
            addCriterion("smart_schedule_enable <>", value, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableGreaterThan(Byte value) {
            addCriterion("smart_schedule_enable >", value, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("smart_schedule_enable >=", value, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableLessThan(Byte value) {
            addCriterion("smart_schedule_enable <", value, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableLessThanOrEqualTo(Byte value) {
            addCriterion("smart_schedule_enable <=", value, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableIn(List<Byte> values) {
            addCriterion("smart_schedule_enable in", values, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableNotIn(List<Byte> values) {
            addCriterion("smart_schedule_enable not in", values, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableBetween(Byte value1, Byte value2) {
            addCriterion("smart_schedule_enable between", value1, value2, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("smart_schedule_enable not between", value1, value2, "smartScheduleEnable");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistIsNull() {
            addCriterion("smart_schedule_max_dist is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistIsNotNull() {
            addCriterion("smart_schedule_max_dist is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistEqualTo(Integer value) {
            addCriterion("smart_schedule_max_dist =", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistNotEqualTo(Integer value) {
            addCriterion("smart_schedule_max_dist <>", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistGreaterThan(Integer value) {
            addCriterion("smart_schedule_max_dist >", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistGreaterThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_max_dist >=", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistLessThan(Integer value) {
            addCriterion("smart_schedule_max_dist <", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistLessThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_max_dist <=", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistIn(List<Integer> values) {
            addCriterion("smart_schedule_max_dist in", values, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistNotIn(List<Integer> values) {
            addCriterion("smart_schedule_max_dist not in", values, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_max_dist between", value1, value2, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistNotBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_max_dist not between", value1, value2, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeIsNull() {
            addCriterion("smart_schedule_max_time is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeIsNotNull() {
            addCriterion("smart_schedule_max_time is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeEqualTo(Integer value) {
            addCriterion("smart_schedule_max_time =", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeNotEqualTo(Integer value) {
            addCriterion("smart_schedule_max_time <>", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeGreaterThan(Integer value) {
            addCriterion("smart_schedule_max_time >", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_max_time >=", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeLessThan(Integer value) {
            addCriterion("smart_schedule_max_time <", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeLessThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_max_time <=", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeIn(List<Integer> values) {
            addCriterion("smart_schedule_max_time in", values, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeNotIn(List<Integer> values) {
            addCriterion("smart_schedule_max_time not in", values, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_max_time between", value1, value2, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_max_time not between", value1, value2, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andZipCodesIsNull() {
            addCriterion("zip_codes is null");
            return (Criteria) this;
        }

        public Criteria andZipCodesIsNotNull() {
            addCriterion("zip_codes is not null");
            return (Criteria) this;
        }

        public Criteria andZipCodesEqualTo(String value) {
            addCriterion("zip_codes =", value, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesNotEqualTo(String value) {
            addCriterion("zip_codes <>", value, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesGreaterThan(String value) {
            addCriterion("zip_codes >", value, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesGreaterThanOrEqualTo(String value) {
            addCriterion("zip_codes >=", value, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesLessThan(String value) {
            addCriterion("zip_codes <", value, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesLessThanOrEqualTo(String value) {
            addCriterion("zip_codes <=", value, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesLike(String value) {
            addCriterion("zip_codes like", value, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesNotLike(String value) {
            addCriterion("zip_codes not like", value, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesIn(List<String> values) {
            addCriterion("zip_codes in", values, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesNotIn(List<String> values) {
            addCriterion("zip_codes not in", values, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesBetween(String value1, String value2) {
            addCriterion("zip_codes between", value1, value2, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andZipCodesNotBetween(String value1, String value2) {
            addCriterion("zip_codes not between", value1, value2, "zipCodes");
            return (Criteria) this;
        }

        public Criteria andServiceAreasIsNull() {
            addCriterion("service_areas is null");
            return (Criteria) this;
        }

        public Criteria andServiceAreasIsNotNull() {
            addCriterion("service_areas is not null");
            return (Criteria) this;
        }

        public Criteria andServiceAreasEqualTo(List<Integer> value) {
            addServiceAreasCriterion("service_areas =", value, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasNotEqualTo(List<Integer> value) {
            addServiceAreasCriterion("service_areas <>", value, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasGreaterThan(List<Integer> value) {
            addServiceAreasCriterion("service_areas >", value, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasGreaterThanOrEqualTo(List<Integer> value) {
            addServiceAreasCriterion("service_areas >=", value, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasLessThan(List<Integer> value) {
            addServiceAreasCriterion("service_areas <", value, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasLessThanOrEqualTo(List<Integer> value) {
            addServiceAreasCriterion("service_areas <=", value, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasLike(List<Integer> value) {
            addServiceAreasCriterion("service_areas like", value, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasNotLike(List<Integer> value) {
            addServiceAreasCriterion("service_areas not like", value, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasIn(List<List<Integer>> values) {
            addServiceAreasCriterion("service_areas in", values, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasNotIn(List<List<Integer>> values) {
            addServiceAreasCriterion("service_areas not in", values, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasBetween(List<Integer> value1, List<Integer> value2) {
            addServiceAreasCriterion("service_areas between", value1, value2, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andServiceAreasNotBetween(List<Integer> value1, List<Integer> value2) {
            addServiceAreasCriterion("service_areas not between", value1, value2, "serviceAreas");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameIsNull() {
            addCriterion("book_online_name is null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameIsNotNull() {
            addCriterion("book_online_name is not null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameEqualTo(String value) {
            addCriterion("book_online_name =", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameNotEqualTo(String value) {
            addCriterion("book_online_name <>", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameGreaterThan(String value) {
            addCriterion("book_online_name >", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameGreaterThanOrEqualTo(String value) {
            addCriterion("book_online_name >=", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameLessThan(String value) {
            addCriterion("book_online_name <", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameLessThanOrEqualTo(String value) {
            addCriterion("book_online_name <=", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameLike(String value) {
            addCriterion("book_online_name like", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameNotLike(String value) {
            addCriterion("book_online_name not like", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameIn(List<String> values) {
            addCriterion("book_online_name in", values, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameNotIn(List<String> values) {
            addCriterion("book_online_name not in", values, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameBetween(String value1, String value2) {
            addCriterion("book_online_name between", value1, value2, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameNotBetween(String value1, String value2) {
            addCriterion("book_online_name not between", value1, value2, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitIsNull() {
            addCriterion("allowed_simplify_submit is null");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitIsNotNull() {
            addCriterion("allowed_simplify_submit is not null");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitEqualTo(Byte value) {
            addCriterion("allowed_simplify_submit =", value, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitNotEqualTo(Byte value) {
            addCriterion("allowed_simplify_submit <>", value, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitGreaterThan(Byte value) {
            addCriterion("allowed_simplify_submit >", value, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitGreaterThanOrEqualTo(Byte value) {
            addCriterion("allowed_simplify_submit >=", value, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitLessThan(Byte value) {
            addCriterion("allowed_simplify_submit <", value, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitLessThanOrEqualTo(Byte value) {
            addCriterion("allowed_simplify_submit <=", value, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitIn(List<Byte> values) {
            addCriterion("allowed_simplify_submit in", values, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitNotIn(List<Byte> values) {
            addCriterion("allowed_simplify_submit not in", values, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitBetween(Byte value1, Byte value2) {
            addCriterion("allowed_simplify_submit between", value1, value2, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAllowedSimplifySubmitNotBetween(Byte value1, Byte value2) {
            addCriterion("allowed_simplify_submit not between", value1, value2, "allowedSimplifySubmit");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeIsNull() {
            addCriterion("available_time_type is null");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeIsNotNull() {
            addCriterion("available_time_type is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeEqualTo(Byte value) {
            addCriterion("available_time_type =", value, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeNotEqualTo(Byte value) {
            addCriterion("available_time_type <>", value, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeGreaterThan(Byte value) {
            addCriterion("available_time_type >", value, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("available_time_type >=", value, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeLessThan(Byte value) {
            addCriterion("available_time_type <", value, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeLessThanOrEqualTo(Byte value) {
            addCriterion("available_time_type <=", value, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeIn(List<Byte> values) {
            addCriterion("available_time_type in", values, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeNotIn(List<Byte> values) {
            addCriterion("available_time_type not in", values, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeBetween(Byte value1, Byte value2) {
            addCriterion("available_time_type between", value1, value2, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("available_time_type not between", value1, value2, "availableTimeType");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatIsNull() {
            addCriterion("by_slot_timeslot_format is null");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatIsNotNull() {
            addCriterion("by_slot_timeslot_format is not null");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatEqualTo(Byte value) {
            addCriterion("by_slot_timeslot_format =", value, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatNotEqualTo(Byte value) {
            addCriterion("by_slot_timeslot_format <>", value, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatGreaterThan(Byte value) {
            addCriterion("by_slot_timeslot_format >", value, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatGreaterThanOrEqualTo(Byte value) {
            addCriterion("by_slot_timeslot_format >=", value, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatLessThan(Byte value) {
            addCriterion("by_slot_timeslot_format <", value, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatLessThanOrEqualTo(Byte value) {
            addCriterion("by_slot_timeslot_format <=", value, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatIn(List<Byte> values) {
            addCriterion("by_slot_timeslot_format in", values, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatNotIn(List<Byte> values) {
            addCriterion("by_slot_timeslot_format not in", values, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatBetween(Byte value1, Byte value2) {
            addCriterion("by_slot_timeslot_format between", value1, value2, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotFormatNotBetween(Byte value1, Byte value2) {
            addCriterion("by_slot_timeslot_format not between", value1, value2, "bySlotTimeslotFormat");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsIsNull() {
            addCriterion("by_slot_timeslot_mins is null");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsIsNotNull() {
            addCriterion("by_slot_timeslot_mins is not null");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsEqualTo(Integer value) {
            addCriterion("by_slot_timeslot_mins =", value, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsNotEqualTo(Integer value) {
            addCriterion("by_slot_timeslot_mins <>", value, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsGreaterThan(Integer value) {
            addCriterion("by_slot_timeslot_mins >", value, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsGreaterThanOrEqualTo(Integer value) {
            addCriterion("by_slot_timeslot_mins >=", value, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsLessThan(Integer value) {
            addCriterion("by_slot_timeslot_mins <", value, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsLessThanOrEqualTo(Integer value) {
            addCriterion("by_slot_timeslot_mins <=", value, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsIn(List<Integer> values) {
            addCriterion("by_slot_timeslot_mins in", values, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsNotIn(List<Integer> values) {
            addCriterion("by_slot_timeslot_mins not in", values, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsBetween(Integer value1, Integer value2) {
            addCriterion("by_slot_timeslot_mins between", value1, value2, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotTimeslotMinsNotBetween(Integer value1, Integer value2) {
            addCriterion("by_slot_timeslot_mins not between", value1, value2, "bySlotTimeslotMins");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableIsNull() {
            addCriterion("by_slot_soonest_available is null");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableIsNotNull() {
            addCriterion("by_slot_soonest_available is not null");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableEqualTo(Integer value) {
            addCriterion("by_slot_soonest_available =", value, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableNotEqualTo(Integer value) {
            addCriterion("by_slot_soonest_available <>", value, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableGreaterThan(Integer value) {
            addCriterion("by_slot_soonest_available >", value, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableGreaterThanOrEqualTo(Integer value) {
            addCriterion("by_slot_soonest_available >=", value, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableLessThan(Integer value) {
            addCriterion("by_slot_soonest_available <", value, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableLessThanOrEqualTo(Integer value) {
            addCriterion("by_slot_soonest_available <=", value, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableIn(List<Integer> values) {
            addCriterion("by_slot_soonest_available in", values, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableNotIn(List<Integer> values) {
            addCriterion("by_slot_soonest_available not in", values, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableBetween(Integer value1, Integer value2) {
            addCriterion("by_slot_soonest_available between", value1, value2, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotSoonestAvailableNotBetween(Integer value1, Integer value2) {
            addCriterion("by_slot_soonest_available not between", value1, value2, "bySlotSoonestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableIsNull() {
            addCriterion("by_slot_farthest_available is null");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableIsNotNull() {
            addCriterion("by_slot_farthest_available is not null");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableEqualTo(Integer value) {
            addCriterion("by_slot_farthest_available =", value, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableNotEqualTo(Integer value) {
            addCriterion("by_slot_farthest_available <>", value, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableGreaterThan(Integer value) {
            addCriterion("by_slot_farthest_available >", value, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableGreaterThanOrEqualTo(Integer value) {
            addCriterion("by_slot_farthest_available >=", value, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableLessThan(Integer value) {
            addCriterion("by_slot_farthest_available <", value, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableLessThanOrEqualTo(Integer value) {
            addCriterion("by_slot_farthest_available <=", value, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableIn(List<Integer> values) {
            addCriterion("by_slot_farthest_available in", values, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableNotIn(List<Integer> values) {
            addCriterion("by_slot_farthest_available not in", values, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableBetween(Integer value1, Integer value2) {
            addCriterion("by_slot_farthest_available between", value1, value2, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andBySlotFarthestAvailableNotBetween(Integer value1, Integer value2) {
            addCriterion("by_slot_farthest_available not between", value1, value2, "bySlotFarthestAvailable");
            return (Criteria) this;
        }

        public Criteria andServiceFilterIsNull() {
            addCriterion("service_filter is null");
            return (Criteria) this;
        }

        public Criteria andServiceFilterIsNotNull() {
            addCriterion("service_filter is not null");
            return (Criteria) this;
        }

        public Criteria andServiceFilterEqualTo(Byte value) {
            addCriterion("service_filter =", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterNotEqualTo(Byte value) {
            addCriterion("service_filter <>", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterGreaterThan(Byte value) {
            addCriterion("service_filter >", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterGreaterThanOrEqualTo(Byte value) {
            addCriterion("service_filter >=", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterLessThan(Byte value) {
            addCriterion("service_filter <", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterLessThanOrEqualTo(Byte value) {
            addCriterion("service_filter <=", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterIn(List<Byte> values) {
            addCriterion("service_filter in", values, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterNotIn(List<Byte> values) {
            addCriterion("service_filter not in", values, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterBetween(Byte value1, Byte value2) {
            addCriterion("service_filter between", value1, value2, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterNotBetween(Byte value1, Byte value2) {
            addCriterion("service_filter not between", value1, value2, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesIsNull() {
            addCriterion("is_show_categories is null");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesIsNotNull() {
            addCriterion("is_show_categories is not null");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesEqualTo(Boolean value) {
            addCriterion("is_show_categories =", value, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesNotEqualTo(Boolean value) {
            addCriterion("is_show_categories <>", value, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesGreaterThan(Boolean value) {
            addCriterion("is_show_categories >", value, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_show_categories >=", value, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesLessThan(Boolean value) {
            addCriterion("is_show_categories <", value, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesLessThanOrEqualTo(Boolean value) {
            addCriterion("is_show_categories <=", value, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesIn(List<Boolean> values) {
            addCriterion("is_show_categories in", values, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesNotIn(List<Boolean> values) {
            addCriterion("is_show_categories not in", values, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesBetween(Boolean value1, Boolean value2) {
            addCriterion("is_show_categories between", value1, value2, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andIsShowCategoriesNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_show_categories not between", value1, value2, "isShowCategories");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeIsNull() {
            addCriterion("prepay_type is null");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeIsNotNull() {
            addCriterion("prepay_type is not null");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeEqualTo(Byte value) {
            addCriterion("prepay_type =", value, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeNotEqualTo(Byte value) {
            addCriterion("prepay_type <>", value, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeGreaterThan(Byte value) {
            addCriterion("prepay_type >", value, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("prepay_type >=", value, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeLessThan(Byte value) {
            addCriterion("prepay_type <", value, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeLessThanOrEqualTo(Byte value) {
            addCriterion("prepay_type <=", value, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeIn(List<Byte> values) {
            addCriterion("prepay_type in", values, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeNotIn(List<Byte> values) {
            addCriterion("prepay_type not in", values, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeBetween(Byte value1, Byte value2) {
            addCriterion("prepay_type between", value1, value2, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("prepay_type not between", value1, value2, "prepayType");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableIsNull() {
            addCriterion("prepay_tip_enable is null");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableIsNotNull() {
            addCriterion("prepay_tip_enable is not null");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableEqualTo(Byte value) {
            addCriterion("prepay_tip_enable =", value, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableNotEqualTo(Byte value) {
            addCriterion("prepay_tip_enable <>", value, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableGreaterThan(Byte value) {
            addCriterion("prepay_tip_enable >", value, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("prepay_tip_enable >=", value, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableLessThan(Byte value) {
            addCriterion("prepay_tip_enable <", value, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableLessThanOrEqualTo(Byte value) {
            addCriterion("prepay_tip_enable <=", value, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableIn(List<Byte> values) {
            addCriterion("prepay_tip_enable in", values, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableNotIn(List<Byte> values) {
            addCriterion("prepay_tip_enable not in", values, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableBetween(Byte value1, Byte value2) {
            addCriterion("prepay_tip_enable between", value1, value2, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andPrepayTipEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("prepay_tip_enable not between", value1, value2, "prepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andDepositTypeIsNull() {
            addCriterion("deposit_type is null");
            return (Criteria) this;
        }

        public Criteria andDepositTypeIsNotNull() {
            addCriterion("deposit_type is not null");
            return (Criteria) this;
        }

        public Criteria andDepositTypeEqualTo(Byte value) {
            addCriterion("deposit_type =", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeNotEqualTo(Byte value) {
            addCriterion("deposit_type <>", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeGreaterThan(Byte value) {
            addCriterion("deposit_type >", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("deposit_type >=", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeLessThan(Byte value) {
            addCriterion("deposit_type <", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeLessThanOrEqualTo(Byte value) {
            addCriterion("deposit_type <=", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeIn(List<Byte> values) {
            addCriterion("deposit_type in", values, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeNotIn(List<Byte> values) {
            addCriterion("deposit_type not in", values, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeBetween(Byte value1, Byte value2) {
            addCriterion("deposit_type between", value1, value2, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("deposit_type not between", value1, value2, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageIsNull() {
            addCriterion("deposit_percentage is null");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageIsNotNull() {
            addCriterion("deposit_percentage is not null");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageEqualTo(Integer value) {
            addCriterion("deposit_percentage =", value, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageNotEqualTo(Integer value) {
            addCriterion("deposit_percentage <>", value, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageGreaterThan(Integer value) {
            addCriterion("deposit_percentage >", value, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageGreaterThanOrEqualTo(Integer value) {
            addCriterion("deposit_percentage >=", value, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageLessThan(Integer value) {
            addCriterion("deposit_percentage <", value, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageLessThanOrEqualTo(Integer value) {
            addCriterion("deposit_percentage <=", value, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageIn(List<Integer> values) {
            addCriterion("deposit_percentage in", values, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageNotIn(List<Integer> values) {
            addCriterion("deposit_percentage not in", values, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageBetween(Integer value1, Integer value2) {
            addCriterion("deposit_percentage between", value1, value2, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositPercentageNotBetween(Integer value1, Integer value2) {
            addCriterion("deposit_percentage not between", value1, value2, "depositPercentage");
            return (Criteria) this;
        }

        public Criteria andDepositAmountIsNull() {
            addCriterion("deposit_amount is null");
            return (Criteria) this;
        }

        public Criteria andDepositAmountIsNotNull() {
            addCriterion("deposit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andDepositAmountEqualTo(BigDecimal value) {
            addCriterion("deposit_amount =", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountNotEqualTo(BigDecimal value) {
            addCriterion("deposit_amount <>", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountGreaterThan(BigDecimal value) {
            addCriterion("deposit_amount >", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("deposit_amount >=", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountLessThan(BigDecimal value) {
            addCriterion("deposit_amount <", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("deposit_amount <=", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountIn(List<BigDecimal> values) {
            addCriterion("deposit_amount in", values, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountNotIn(List<BigDecimal> values) {
            addCriterion("deposit_amount not in", values, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("deposit_amount between", value1, value2, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("deposit_amount not between", value1, value2, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andUseVersionIsNull() {
            addCriterion("use_version is null");
            return (Criteria) this;
        }

        public Criteria andUseVersionIsNotNull() {
            addCriterion("use_version is not null");
            return (Criteria) this;
        }

        public Criteria andUseVersionEqualTo(Byte value) {
            addCriterion("use_version =", value, "useVersion");
            return (Criteria) this;
        }

        public Criteria andUseVersionNotEqualTo(Byte value) {
            addCriterion("use_version <>", value, "useVersion");
            return (Criteria) this;
        }

        public Criteria andUseVersionGreaterThan(Byte value) {
            addCriterion("use_version >", value, "useVersion");
            return (Criteria) this;
        }

        public Criteria andUseVersionGreaterThanOrEqualTo(Byte value) {
            addCriterion("use_version >=", value, "useVersion");
            return (Criteria) this;
        }

        public Criteria andUseVersionLessThan(Byte value) {
            addCriterion("use_version <", value, "useVersion");
            return (Criteria) this;
        }

        public Criteria andUseVersionLessThanOrEqualTo(Byte value) {
            addCriterion("use_version <=", value, "useVersion");
            return (Criteria) this;
        }

        public Criteria andUseVersionIn(List<Byte> values) {
            addCriterion("use_version in", values, "useVersion");
            return (Criteria) this;
        }

        public Criteria andUseVersionNotIn(List<Byte> values) {
            addCriterion("use_version not in", values, "useVersion");
            return (Criteria) this;
        }

        public Criteria andUseVersionBetween(Byte value1, Byte value2) {
            addCriterion("use_version between", value1, value2, "useVersion");
            return (Criteria) this;
        }

        public Criteria andUseVersionNotBetween(Byte value1, Byte value2) {
            addCriterion("use_version not between", value1, value2, "useVersion");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableIsNull() {
            addCriterion("pre_auth_tip_enable is null");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableIsNotNull() {
            addCriterion("pre_auth_tip_enable is not null");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableEqualTo(Byte value) {
            addCriterion("pre_auth_tip_enable =", value, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableNotEqualTo(Byte value) {
            addCriterion("pre_auth_tip_enable <>", value, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableGreaterThan(Byte value) {
            addCriterion("pre_auth_tip_enable >", value, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("pre_auth_tip_enable >=", value, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableLessThan(Byte value) {
            addCriterion("pre_auth_tip_enable <", value, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableLessThanOrEqualTo(Byte value) {
            addCriterion("pre_auth_tip_enable <=", value, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableIn(List<Byte> values) {
            addCriterion("pre_auth_tip_enable in", values, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableNotIn(List<Byte> values) {
            addCriterion("pre_auth_tip_enable not in", values, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableBetween(Byte value1, Byte value2) {
            addCriterion("pre_auth_tip_enable between", value1, value2, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andPreAuthTipEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("pre_auth_tip_enable not between", value1, value2, "preAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositIsNull() {
            addCriterion("auto_refund_deposit is null");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositIsNotNull() {
            addCriterion("auto_refund_deposit is not null");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositEqualTo(Byte value) {
            addCriterion("auto_refund_deposit =", value, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositNotEqualTo(Byte value) {
            addCriterion("auto_refund_deposit <>", value, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositGreaterThan(Byte value) {
            addCriterion("auto_refund_deposit >", value, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositGreaterThanOrEqualTo(Byte value) {
            addCriterion("auto_refund_deposit >=", value, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositLessThan(Byte value) {
            addCriterion("auto_refund_deposit <", value, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositLessThanOrEqualTo(Byte value) {
            addCriterion("auto_refund_deposit <=", value, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositIn(List<Byte> values) {
            addCriterion("auto_refund_deposit in", values, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositNotIn(List<Byte> values) {
            addCriterion("auto_refund_deposit not in", values, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositBetween(Byte value1, Byte value2) {
            addCriterion("auto_refund_deposit between", value1, value2, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andAutoRefundDepositNotBetween(Byte value1, Byte value2) {
            addCriterion("auto_refund_deposit not between", value1, value2, "autoRefundDeposit");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeIsNull() {
            addCriterion("request_submitted_auto_type is null");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeIsNotNull() {
            addCriterion("request_submitted_auto_type is not null");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeEqualTo(String value) {
            addCriterion("request_submitted_auto_type =", value, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeNotEqualTo(String value) {
            addCriterion("request_submitted_auto_type <>", value, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeGreaterThan(String value) {
            addCriterion("request_submitted_auto_type >", value, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeGreaterThanOrEqualTo(String value) {
            addCriterion("request_submitted_auto_type >=", value, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeLessThan(String value) {
            addCriterion("request_submitted_auto_type <", value, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeLessThanOrEqualTo(String value) {
            addCriterion("request_submitted_auto_type <=", value, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeLike(String value) {
            addCriterion("request_submitted_auto_type like", value, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeNotLike(String value) {
            addCriterion("request_submitted_auto_type not like", value, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeIn(List<String> values) {
            addCriterion("request_submitted_auto_type in", values, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeNotIn(List<String> values) {
            addCriterion("request_submitted_auto_type not in", values, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeBetween(String value1, String value2) {
            addCriterion("request_submitted_auto_type between", value1, value2, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andRequestSubmittedAutoTypeNotBetween(String value1, String value2) {
            addCriterion("request_submitted_auto_type not between", value1, value2, "requestSubmittedAutoType");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageIsNull() {
            addCriterion("display_staff_selection_page is null");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageIsNotNull() {
            addCriterion("display_staff_selection_page is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageEqualTo(Boolean value) {
            addCriterion("display_staff_selection_page =", value, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageNotEqualTo(Boolean value) {
            addCriterion("display_staff_selection_page <>", value, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageGreaterThan(Boolean value) {
            addCriterion("display_staff_selection_page >", value, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageGreaterThanOrEqualTo(Boolean value) {
            addCriterion("display_staff_selection_page >=", value, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageLessThan(Boolean value) {
            addCriterion("display_staff_selection_page <", value, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageLessThanOrEqualTo(Boolean value) {
            addCriterion("display_staff_selection_page <=", value, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageIn(List<Boolean> values) {
            addCriterion("display_staff_selection_page in", values, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageNotIn(List<Boolean> values) {
            addCriterion("display_staff_selection_page not in", values, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageBetween(Boolean value1, Boolean value2) {
            addCriterion("display_staff_selection_page between", value1, value2, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andDisplayStaffSelectionPageNotBetween(Boolean value1, Boolean value2) {
            addCriterion("display_staff_selection_page not between", value1, value2, "displayStaffSelectionPage");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinIsNull() {
            addCriterion("arrival_window_before_min is null");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinIsNotNull() {
            addCriterion("arrival_window_before_min is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinEqualTo(Integer value) {
            addCriterion("arrival_window_before_min =", value, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinNotEqualTo(Integer value) {
            addCriterion("arrival_window_before_min <>", value, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinGreaterThan(Integer value) {
            addCriterion("arrival_window_before_min >", value, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinGreaterThanOrEqualTo(Integer value) {
            addCriterion("arrival_window_before_min >=", value, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinLessThan(Integer value) {
            addCriterion("arrival_window_before_min <", value, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinLessThanOrEqualTo(Integer value) {
            addCriterion("arrival_window_before_min <=", value, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinIn(List<Integer> values) {
            addCriterion("arrival_window_before_min in", values, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinNotIn(List<Integer> values) {
            addCriterion("arrival_window_before_min not in", values, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinBetween(Integer value1, Integer value2) {
            addCriterion("arrival_window_before_min between", value1, value2, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowBeforeMinNotBetween(Integer value1, Integer value2) {
            addCriterion("arrival_window_before_min not between", value1, value2, "arrivalWindowBeforeMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinIsNull() {
            addCriterion("arrival_window_after_min is null");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinIsNotNull() {
            addCriterion("arrival_window_after_min is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinEqualTo(Integer value) {
            addCriterion("arrival_window_after_min =", value, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinNotEqualTo(Integer value) {
            addCriterion("arrival_window_after_min <>", value, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinGreaterThan(Integer value) {
            addCriterion("arrival_window_after_min >", value, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinGreaterThanOrEqualTo(Integer value) {
            addCriterion("arrival_window_after_min >=", value, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinLessThan(Integer value) {
            addCriterion("arrival_window_after_min <", value, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinLessThanOrEqualTo(Integer value) {
            addCriterion("arrival_window_after_min <=", value, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinIn(List<Integer> values) {
            addCriterion("arrival_window_after_min in", values, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinNotIn(List<Integer> values) {
            addCriterion("arrival_window_after_min not in", values, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinBetween(Integer value1, Integer value2) {
            addCriterion("arrival_window_after_min between", value1, value2, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andArrivalWindowAfterMinNotBetween(Integer value1, Integer value2) {
            addCriterion("arrival_window_after_min not between", value1, value2, "arrivalWindowAfterMin");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetIsNull() {
            addCriterion("booking_range_start_offset is null");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetIsNotNull() {
            addCriterion("booking_range_start_offset is not null");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetEqualTo(Integer value) {
            addCriterion("booking_range_start_offset =", value, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetNotEqualTo(Integer value) {
            addCriterion("booking_range_start_offset <>", value, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetGreaterThan(Integer value) {
            addCriterion("booking_range_start_offset >", value, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetGreaterThanOrEqualTo(Integer value) {
            addCriterion("booking_range_start_offset >=", value, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetLessThan(Integer value) {
            addCriterion("booking_range_start_offset <", value, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetLessThanOrEqualTo(Integer value) {
            addCriterion("booking_range_start_offset <=", value, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetIn(List<Integer> values) {
            addCriterion("booking_range_start_offset in", values, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetNotIn(List<Integer> values) {
            addCriterion("booking_range_start_offset not in", values, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetBetween(Integer value1, Integer value2) {
            addCriterion("booking_range_start_offset between", value1, value2, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeStartOffsetNotBetween(Integer value1, Integer value2) {
            addCriterion("booking_range_start_offset not between", value1, value2, "bookingRangeStartOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeIsNull() {
            addCriterion("booking_range_end_type is null");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeIsNotNull() {
            addCriterion("booking_range_end_type is not null");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeEqualTo(Byte value) {
            addCriterion("booking_range_end_type =", value, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeNotEqualTo(Byte value) {
            addCriterion("booking_range_end_type <>", value, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeGreaterThan(Byte value) {
            addCriterion("booking_range_end_type >", value, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("booking_range_end_type >=", value, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeLessThan(Byte value) {
            addCriterion("booking_range_end_type <", value, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeLessThanOrEqualTo(Byte value) {
            addCriterion("booking_range_end_type <=", value, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeIn(List<Byte> values) {
            addCriterion("booking_range_end_type in", values, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeNotIn(List<Byte> values) {
            addCriterion("booking_range_end_type not in", values, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeBetween(Byte value1, Byte value2) {
            addCriterion("booking_range_end_type between", value1, value2, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("booking_range_end_type not between", value1, value2, "bookingRangeEndType");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetIsNull() {
            addCriterion("booking_range_end_offset is null");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetIsNotNull() {
            addCriterion("booking_range_end_offset is not null");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetEqualTo(Integer value) {
            addCriterion("booking_range_end_offset =", value, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetNotEqualTo(Integer value) {
            addCriterion("booking_range_end_offset <>", value, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetGreaterThan(Integer value) {
            addCriterion("booking_range_end_offset >", value, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetGreaterThanOrEqualTo(Integer value) {
            addCriterion("booking_range_end_offset >=", value, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetLessThan(Integer value) {
            addCriterion("booking_range_end_offset <", value, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetLessThanOrEqualTo(Integer value) {
            addCriterion("booking_range_end_offset <=", value, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetIn(List<Integer> values) {
            addCriterion("booking_range_end_offset in", values, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetNotIn(List<Integer> values) {
            addCriterion("booking_range_end_offset not in", values, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetBetween(Integer value1, Integer value2) {
            addCriterion("booking_range_end_offset between", value1, value2, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndOffsetNotBetween(Integer value1, Integer value2) {
            addCriterion("booking_range_end_offset not between", value1, value2, "bookingRangeEndOffset");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateIsNull() {
            addCriterion("booking_range_end_date is null");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateIsNotNull() {
            addCriterion("booking_range_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateEqualTo(String value) {
            addCriterion("booking_range_end_date =", value, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateNotEqualTo(String value) {
            addCriterion("booking_range_end_date <>", value, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateGreaterThan(String value) {
            addCriterion("booking_range_end_date >", value, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("booking_range_end_date >=", value, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateLessThan(String value) {
            addCriterion("booking_range_end_date <", value, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateLessThanOrEqualTo(String value) {
            addCriterion("booking_range_end_date <=", value, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateLike(String value) {
            addCriterion("booking_range_end_date like", value, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateNotLike(String value) {
            addCriterion("booking_range_end_date not like", value, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateIn(List<String> values) {
            addCriterion("booking_range_end_date in", values, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateNotIn(List<String> values) {
            addCriterion("booking_range_end_date not in", values, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateBetween(String value1, String value2) {
            addCriterion("booking_range_end_date between", value1, value2, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andBookingRangeEndDateNotBetween(String value1, String value2) {
            addCriterion("booking_range_end_date not between", value1, value2, "bookingRangeEndDate");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationIsNull() {
            addCriterion("is_need_send_renew_notification is null");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationIsNotNull() {
            addCriterion("is_need_send_renew_notification is not null");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationEqualTo(Boolean value) {
            addCriterion("is_need_send_renew_notification =", value, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationNotEqualTo(Boolean value) {
            addCriterion("is_need_send_renew_notification <>", value, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationGreaterThan(Boolean value) {
            addCriterion("is_need_send_renew_notification >", value, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_need_send_renew_notification >=", value, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationLessThan(Boolean value) {
            addCriterion("is_need_send_renew_notification <", value, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationLessThanOrEqualTo(Boolean value) {
            addCriterion("is_need_send_renew_notification <=", value, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationIn(List<Boolean> values) {
            addCriterion("is_need_send_renew_notification in", values, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationNotIn(List<Boolean> values) {
            addCriterion("is_need_send_renew_notification not in", values, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationBetween(Boolean value1, Boolean value2) {
            addCriterion("is_need_send_renew_notification between", value1, value2, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andIsNeedSendRenewNotificationNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_need_send_renew_notification not between", value1, value2, "isNeedSendRenewNotification");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeIsNull() {
            addCriterion("group_payment_type is null");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeIsNotNull() {
            addCriterion("group_payment_type is not null");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeEqualTo(Byte value) {
            addCriterion("group_payment_type =", value, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeNotEqualTo(Byte value) {
            addCriterion("group_payment_type <>", value, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeGreaterThan(Byte value) {
            addCriterion("group_payment_type >", value, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("group_payment_type >=", value, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeLessThan(Byte value) {
            addCriterion("group_payment_type <", value, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeLessThanOrEqualTo(Byte value) {
            addCriterion("group_payment_type <=", value, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeIn(List<Byte> values) {
            addCriterion("group_payment_type in", values, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeNotIn(List<Byte> values) {
            addCriterion("group_payment_type not in", values, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeBetween(Byte value1, Byte value2) {
            addCriterion("group_payment_type between", value1, value2, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupPaymentTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("group_payment_type not between", value1, value2, "groupPaymentType");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleIsNull() {
            addCriterion("group_filter_rule is null");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleIsNotNull() {
            addCriterion("group_filter_rule is not null");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleEqualTo(String value) {
            addCriterion("group_filter_rule =", value, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleNotEqualTo(String value) {
            addCriterion("group_filter_rule <>", value, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleGreaterThan(String value) {
            addCriterion("group_filter_rule >", value, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleGreaterThanOrEqualTo(String value) {
            addCriterion("group_filter_rule >=", value, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleLessThan(String value) {
            addCriterion("group_filter_rule <", value, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleLessThanOrEqualTo(String value) {
            addCriterion("group_filter_rule <=", value, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleLike(String value) {
            addCriterion("group_filter_rule like", value, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleNotLike(String value) {
            addCriterion("group_filter_rule not like", value, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleIn(List<String> values) {
            addCriterion("group_filter_rule in", values, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleNotIn(List<String> values) {
            addCriterion("group_filter_rule not in", values, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleBetween(String value1, String value2) {
            addCriterion("group_filter_rule between", value1, value2, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupFilterRuleNotBetween(String value1, String value2) {
            addCriterion("group_filter_rule not between", value1, value2, "groupFilterRule");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeIsNull() {
            addCriterion("group_prepay_type is null");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeIsNotNull() {
            addCriterion("group_prepay_type is not null");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeEqualTo(Byte value) {
            addCriterion("group_prepay_type =", value, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeNotEqualTo(Byte value) {
            addCriterion("group_prepay_type <>", value, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeGreaterThan(Byte value) {
            addCriterion("group_prepay_type >", value, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("group_prepay_type >=", value, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeLessThan(Byte value) {
            addCriterion("group_prepay_type <", value, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeLessThanOrEqualTo(Byte value) {
            addCriterion("group_prepay_type <=", value, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeIn(List<Byte> values) {
            addCriterion("group_prepay_type in", values, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeNotIn(List<Byte> values) {
            addCriterion("group_prepay_type not in", values, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeBetween(Byte value1, Byte value2) {
            addCriterion("group_prepay_type between", value1, value2, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("group_prepay_type not between", value1, value2, "groupPrepayType");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableIsNull() {
            addCriterion("group_prepay_tip_enable is null");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableIsNotNull() {
            addCriterion("group_prepay_tip_enable is not null");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableEqualTo(Byte value) {
            addCriterion("group_prepay_tip_enable =", value, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableNotEqualTo(Byte value) {
            addCriterion("group_prepay_tip_enable <>", value, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableGreaterThan(Byte value) {
            addCriterion("group_prepay_tip_enable >", value, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("group_prepay_tip_enable >=", value, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableLessThan(Byte value) {
            addCriterion("group_prepay_tip_enable <", value, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableLessThanOrEqualTo(Byte value) {
            addCriterion("group_prepay_tip_enable <=", value, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableIn(List<Byte> values) {
            addCriterion("group_prepay_tip_enable in", values, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableNotIn(List<Byte> values) {
            addCriterion("group_prepay_tip_enable not in", values, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableBetween(Byte value1, Byte value2) {
            addCriterion("group_prepay_tip_enable between", value1, value2, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPrepayTipEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("group_prepay_tip_enable not between", value1, value2, "groupPrepayTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeIsNull() {
            addCriterion("group_deposit_type is null");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeIsNotNull() {
            addCriterion("group_deposit_type is not null");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeEqualTo(Byte value) {
            addCriterion("group_deposit_type =", value, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeNotEqualTo(Byte value) {
            addCriterion("group_deposit_type <>", value, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeGreaterThan(Byte value) {
            addCriterion("group_deposit_type >", value, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("group_deposit_type >=", value, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeLessThan(Byte value) {
            addCriterion("group_deposit_type <", value, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeLessThanOrEqualTo(Byte value) {
            addCriterion("group_deposit_type <=", value, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeIn(List<Byte> values) {
            addCriterion("group_deposit_type in", values, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeNotIn(List<Byte> values) {
            addCriterion("group_deposit_type not in", values, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeBetween(Byte value1, Byte value2) {
            addCriterion("group_deposit_type between", value1, value2, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("group_deposit_type not between", value1, value2, "groupDepositType");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageIsNull() {
            addCriterion("group_deposit_percentage is null");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageIsNotNull() {
            addCriterion("group_deposit_percentage is not null");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageEqualTo(Integer value) {
            addCriterion("group_deposit_percentage =", value, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageNotEqualTo(Integer value) {
            addCriterion("group_deposit_percentage <>", value, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageGreaterThan(Integer value) {
            addCriterion("group_deposit_percentage >", value, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageGreaterThanOrEqualTo(Integer value) {
            addCriterion("group_deposit_percentage >=", value, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageLessThan(Integer value) {
            addCriterion("group_deposit_percentage <", value, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageLessThanOrEqualTo(Integer value) {
            addCriterion("group_deposit_percentage <=", value, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageIn(List<Integer> values) {
            addCriterion("group_deposit_percentage in", values, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageNotIn(List<Integer> values) {
            addCriterion("group_deposit_percentage not in", values, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageBetween(Integer value1, Integer value2) {
            addCriterion("group_deposit_percentage between", value1, value2, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositPercentageNotBetween(Integer value1, Integer value2) {
            addCriterion("group_deposit_percentage not between", value1, value2, "groupDepositPercentage");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountIsNull() {
            addCriterion("group_deposit_amount is null");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountIsNotNull() {
            addCriterion("group_deposit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountEqualTo(BigDecimal value) {
            addCriterion("group_deposit_amount =", value, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountNotEqualTo(BigDecimal value) {
            addCriterion("group_deposit_amount <>", value, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountGreaterThan(BigDecimal value) {
            addCriterion("group_deposit_amount >", value, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("group_deposit_amount >=", value, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountLessThan(BigDecimal value) {
            addCriterion("group_deposit_amount <", value, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("group_deposit_amount <=", value, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountIn(List<BigDecimal> values) {
            addCriterion("group_deposit_amount in", values, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountNotIn(List<BigDecimal> values) {
            addCriterion("group_deposit_amount not in", values, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("group_deposit_amount between", value1, value2, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupDepositAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("group_deposit_amount not between", value1, value2, "groupDepositAmount");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableIsNull() {
            addCriterion("group_pre_auth_tip_enable is null");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableIsNotNull() {
            addCriterion("group_pre_auth_tip_enable is not null");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableEqualTo(Byte value) {
            addCriterion("group_pre_auth_tip_enable =", value, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableNotEqualTo(Byte value) {
            addCriterion("group_pre_auth_tip_enable <>", value, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableGreaterThan(Byte value) {
            addCriterion("group_pre_auth_tip_enable >", value, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("group_pre_auth_tip_enable >=", value, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableLessThan(Byte value) {
            addCriterion("group_pre_auth_tip_enable <", value, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableLessThanOrEqualTo(Byte value) {
            addCriterion("group_pre_auth_tip_enable <=", value, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableIn(List<Byte> values) {
            addCriterion("group_pre_auth_tip_enable in", values, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableNotIn(List<Byte> values) {
            addCriterion("group_pre_auth_tip_enable not in", values, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableBetween(Byte value1, Byte value2) {
            addCriterion("group_pre_auth_tip_enable between", value1, value2, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupPreAuthTipEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("group_pre_auth_tip_enable not between", value1, value2, "groupPreAuthTipEnable");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientIsNull() {
            addCriterion("group_accept_client is null");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientIsNotNull() {
            addCriterion("group_accept_client is not null");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientEqualTo(Byte value) {
            addCriterion("group_accept_client =", value, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientNotEqualTo(Byte value) {
            addCriterion("group_accept_client <>", value, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientGreaterThan(Byte value) {
            addCriterion("group_accept_client >", value, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientGreaterThanOrEqualTo(Byte value) {
            addCriterion("group_accept_client >=", value, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientLessThan(Byte value) {
            addCriterion("group_accept_client <", value, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientLessThanOrEqualTo(Byte value) {
            addCriterion("group_accept_client <=", value, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientIn(List<Byte> values) {
            addCriterion("group_accept_client in", values, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientNotIn(List<Byte> values) {
            addCriterion("group_accept_client not in", values, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientBetween(Byte value1, Byte value2) {
            addCriterion("group_accept_client between", value1, value2, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andGroupAcceptClientNotBetween(Byte value1, Byte value2) {
            addCriterion("group_accept_client not between", value1, value2, "groupAcceptClient");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeIsNull() {
            addCriterion("new_client_flow_type is null");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeIsNotNull() {
            addCriterion("new_client_flow_type is not null");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeEqualTo(Integer value) {
            addCriterion("new_client_flow_type =", value, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeNotEqualTo(Integer value) {
            addCriterion("new_client_flow_type <>", value, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeGreaterThan(Integer value) {
            addCriterion("new_client_flow_type >", value, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_client_flow_type >=", value, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeLessThan(Integer value) {
            addCriterion("new_client_flow_type <", value, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeLessThanOrEqualTo(Integer value) {
            addCriterion("new_client_flow_type <=", value, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeIn(List<Integer> values) {
            addCriterion("new_client_flow_type in", values, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeNotIn(List<Integer> values) {
            addCriterion("new_client_flow_type not in", values, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeBetween(Integer value1, Integer value2) {
            addCriterion("new_client_flow_type between", value1, value2, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andNewClientFlowTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("new_client_flow_type not between", value1, value2, "newClientFlowType");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncIsNull() {
            addCriterion("available_time_sync is null");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncIsNotNull() {
            addCriterion("available_time_sync is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncEqualTo(Byte value) {
            addCriterion("available_time_sync =", value, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncNotEqualTo(Byte value) {
            addCriterion("available_time_sync <>", value, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncGreaterThan(Byte value) {
            addCriterion("available_time_sync >", value, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncGreaterThanOrEqualTo(Byte value) {
            addCriterion("available_time_sync >=", value, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncLessThan(Byte value) {
            addCriterion("available_time_sync <", value, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncLessThanOrEqualTo(Byte value) {
            addCriterion("available_time_sync <=", value, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncIn(List<Byte> values) {
            addCriterion("available_time_sync in", values, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncNotIn(List<Byte> values) {
            addCriterion("available_time_sync not in", values, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncBetween(Byte value1, Byte value2) {
            addCriterion("available_time_sync between", value1, value2, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andAvailableTimeSyncNotBetween(Byte value1, Byte value2) {
            addCriterion("available_time_sync not between", value1, value2, "availableTimeSync");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapIsNull() {
            addCriterion("payment_option_map is null");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapIsNotNull() {
            addCriterion("payment_option_map is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapEqualTo(Map<Integer, PaymentOption> value) {
            addPaymentOptionMapCriterion("payment_option_map =", value, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapNotEqualTo(Map<Integer, PaymentOption> value) {
            addPaymentOptionMapCriterion("payment_option_map <>", value, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapGreaterThan(Map<Integer, PaymentOption> value) {
            addPaymentOptionMapCriterion("payment_option_map >", value, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapGreaterThanOrEqualTo(Map<Integer, PaymentOption> value) {
            addPaymentOptionMapCriterion("payment_option_map >=", value, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapLessThan(Map<Integer, PaymentOption> value) {
            addPaymentOptionMapCriterion("payment_option_map <", value, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapLessThanOrEqualTo(Map<Integer, PaymentOption> value) {
            addPaymentOptionMapCriterion("payment_option_map <=", value, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapLike(Map<Integer, PaymentOption> value) {
            addPaymentOptionMapCriterion("payment_option_map like", value, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapNotLike(Map<Integer, PaymentOption> value) {
            addPaymentOptionMapCriterion("payment_option_map not like", value, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapIn(List<Map<Integer, PaymentOption>> values) {
            addPaymentOptionMapCriterion("payment_option_map in", values, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapNotIn(List<Map<Integer, PaymentOption>> values) {
            addPaymentOptionMapCriterion("payment_option_map not in", values, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapBetween(
                Map<Integer, PaymentOption> value1, Map<Integer, PaymentOption> value2) {
            addPaymentOptionMapCriterion("payment_option_map between", value1, value2, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andPaymentOptionMapNotBetween(
                Map<Integer, PaymentOption> value1, Map<Integer, PaymentOption> value2) {
            addPaymentOptionMapCriterion("payment_option_map not between", value1, value2, "paymentOptionMap");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeIsNull() {
            addCriterion("by_slot_show_one_available_time is null");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeIsNotNull() {
            addCriterion("by_slot_show_one_available_time is not null");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeEqualTo(Boolean value) {
            addCriterion("by_slot_show_one_available_time =", value, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeNotEqualTo(Boolean value) {
            addCriterion("by_slot_show_one_available_time <>", value, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeGreaterThan(Boolean value) {
            addCriterion("by_slot_show_one_available_time >", value, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("by_slot_show_one_available_time >=", value, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeLessThan(Boolean value) {
            addCriterion("by_slot_show_one_available_time <", value, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeLessThanOrEqualTo(Boolean value) {
            addCriterion("by_slot_show_one_available_time <=", value, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeIn(List<Boolean> values) {
            addCriterion("by_slot_show_one_available_time in", values, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeNotIn(List<Boolean> values) {
            addCriterion("by_slot_show_one_available_time not in", values, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeBetween(Boolean value1, Boolean value2) {
            addCriterion("by_slot_show_one_available_time between", value1, value2, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }

        public Criteria andBySlotShowOneAvailableTimeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("by_slot_show_one_available_time not between", value1, value2, "bySlotShowOneAvailableTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_business_book_online
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
