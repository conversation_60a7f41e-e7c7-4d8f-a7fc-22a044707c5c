package com.moego.server.grooming.service;

import com.moego.idl.models.order.v1.GroomingDetailRelationModel;
import com.moego.idl.service.order.v1.GetGroomingDetailRelationRequest;
import com.moego.idl.service.order.v1.GetGroomingDetailRelationResponse;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapper.po.GroomingStaffIdListPO;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class GroomingServiceOperationService {

    @Resource
    private MoeGroomingServiceOperationMapper moeGroomingServiceOperationMapper;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    public Map<Integer, List<GroomingServiceOperationDTO>> getOperationMapByGroomingServiceIdList(
            Integer businessId, List<Integer> groomingServiceIdList) {
        if (Objects.isNull(businessId) || CollectionUtils.isEmpty(groomingServiceIdList)) {
            return Map.of();
        }
        return moeGroomingServiceOperationMapper
                .selectByBusinessIdAndGroomingServiceIdList(businessId, groomingServiceIdList)
                .stream()
                .map(this::recordToDTO)
                .collect(Collectors.groupingBy(GroomingServiceOperationDTO::getGroomingServiceId));
    }

    public Map<Integer, List<GroomingServiceOperationDTO>> getOperationMapByGroomingId(
            Integer businessId, Integer groomingId, Integer orderId, boolean isOriginOrder) {
        if (Objects.isNull(businessId) || Objects.isNull(groomingId)) {
            return Map.of();
        }
        GetGroomingDetailRelationResponse groomingDetailRelResponse =
                orderClient.getGroomingDetailRelation(GetGroomingDetailRelationRequest.newBuilder()
                        .setIsOriginOrder(isOriginOrder)
                        .setOrderId(orderId)
                        .build());
        List<GroomingDetailRelationModel> groomingDetailRelModelsList =
                groomingDetailRelResponse.getGroomingDetailRelationModelsList();
        Set<Long> petDetailIdSet = groomingDetailRelModelsList.stream()
                .map(GroomingDetailRelationModel::getPetDetailId)
                .collect(Collectors.toSet());

        return moeGroomingServiceOperationMapper.selectByBusinessIdAndGroomingId(businessId, groomingId).stream()
                .filter(operation -> {
                    // 区分origin order, origin order 的需要排除，extra order的直接筛选
                    if (isOriginOrder) {
                        return !petDetailIdSet.contains(Long.valueOf(operation.getGroomingServiceId()));
                    }
                    return petDetailIdSet.contains(Long.valueOf(operation.getGroomingServiceId()));
                })
                .map(this::recordToDTO)
                .collect(Collectors.groupingBy(GroomingServiceOperationDTO::getGroomingServiceId));
    }

    public Map<Integer, List<GroomingServiceOperationDTO>> getOperationMapByGroomingId(
            Integer businessId, Integer groomingId) {
        if (Objects.isNull(businessId) || Objects.isNull(groomingId)) {
            return Map.of();
        }
        return moeGroomingServiceOperationMapper.selectByBusinessIdAndGroomingId(businessId, groomingId).stream()
                .map(this::recordToDTO)
                .collect(Collectors.groupingBy(GroomingServiceOperationDTO::getGroomingServiceId));
    }

    public Map<Integer, List<GroomingServiceOperationDTO>> getOperationMapByPetDetailId(
            Integer businessId, Integer groomingId) {
        if (Objects.isNull(businessId) || Objects.isNull(groomingId)) {
            return Map.of();
        }
        return moeGroomingServiceOperationMapper.selectByBusinessIdAndGroomingId(businessId, groomingId).stream()
                .map(this::recordToDTO)
                .collect(Collectors.groupingBy(GroomingServiceOperationDTO::getGroomingServiceId));
    }

    public Map<Integer, List<GroomingServiceOperationDTO>> getOperationMapByGroomingIdList(
            Integer businessId, List<Integer> groomingIdList) {
        if (Objects.isNull(businessId) || CollectionUtils.isEmpty(groomingIdList)) {
            return Map.of();
        }
        return moeGroomingServiceOperationMapper
                .selectByBusinessIdAndGroomingIdList(businessId, groomingIdList)
                .stream()
                .map(this::recordToDTO)
                .collect(Collectors.groupingBy(GroomingServiceOperationDTO::getGroomingServiceId));
    }

    public void deleteOperationByGroomingId(Integer groomingId) {
        moeGroomingServiceOperationMapper.deleteByGroomingId(groomingId);
    }

    public void deleteOperationByGroomingIds(List<Integer> groomingIdList) {
        moeGroomingServiceOperationMapper.deleteByGroomingIdList(groomingIdList);
    }

    public List<Integer> queryStaffIdByGroomingId(Integer groomingId) {
        return moeGroomingServiceOperationMapper.queryStaffIdByGroomingId(groomingId);
    }

    public List<GroomingStaffIdListPO> queryStaffIdByGroomingIds(List<Integer> groomingIds) {
        return moeGroomingServiceOperationMapper.queryStaffIdByGroomingIds(groomingIds);
    }

    public void addOperation(OperationBasic basic, List<GroomingServiceOperationDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            log.error("addOperation dtoList is empty, dtoList: {}", dtoList);
            return;
        }
        moeGroomingServiceOperationMapper.batchInsert(
                dtoList.stream().map(dto -> dtoToRecord(dto, basic)).toList());
    }

    public void updateOperation(List<GroomingServiceOperationDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        moeGroomingServiceOperationMapper.batchUpdate(
                dtoList.stream().map(this::dtoToRecord).toList());
    }

    public Integer queryStaffUpComingOperationCount(
            Integer targetStaffId, Integer sourceStaffId, List<Integer> groomingIdList) {
        if (Objects.isNull(targetStaffId) || Objects.isNull(sourceStaffId) || CollectionUtils.isEmpty(groomingIdList)) {
            return 0;
        }
        return moeGroomingServiceOperationMapper.countOperationWithSameStartTimeByGroomingIdList(
                targetStaffId, sourceStaffId, groomingIdList);
    }

    public void transferOperation(Integer sourceStaffId, Integer targetStaffId, List<Integer> groomingIdList) {
        if (Objects.isNull(targetStaffId) || Objects.isNull(sourceStaffId) || CollectionUtils.isEmpty(groomingIdList)) {
            return;
        }
        moeGroomingServiceOperationMapper.transferOperation(sourceStaffId, targetStaffId, groomingIdList);
    }

    public void deleteByPetDetailId(Long petDetailId) {
        moeGroomingServiceOperationMapper.deleteByPetDetailId(petDetailId);
    }

    public void batchInsertOperation(List<MoeGroomingServiceOperation> needAddOperationList) {
        if (CollectionUtils.isEmpty(needAddOperationList)) {
            return;
        }
        moeGroomingServiceOperationMapper.batchInsert(needAddOperationList);
    }

    private GroomingServiceOperationDTO recordToDTO(MoeGroomingServiceOperation operation) {
        GroomingServiceOperationDTO dto = new GroomingServiceOperationDTO();
        dto.setId(operation.getId());
        dto.setBusinessId(operation.getBusinessId());
        dto.setGroomingId(operation.getGroomingId());
        dto.setGroomingServiceId(operation.getGroomingServiceId());
        dto.setPetId(operation.getPetId());
        dto.setStaffId(operation.getStaffId());
        dto.setOperationName(operation.getOperationName());
        dto.setStartTime(operation.getStartTime());
        dto.setDuration(operation.getDuration());
        dto.setComment(operation.getComment());
        dto.setPrice(operation.getPrice());
        dto.setPriceRatio(operation.getPriceRatio());
        return dto;
    }

    private MoeGroomingServiceOperation dtoToRecord(GroomingServiceOperationDTO dto, OperationBasic basic) {
        MoeGroomingServiceOperation operation = new MoeGroomingServiceOperation();
        operation.setId(dto.getId());
        operation.setBusinessId(basic.businessId());
        operation.setCompanyId(basic.companyId());
        operation.setGroomingId(basic.groomingId());
        operation.setGroomingServiceId(basic.groomingServiceId());
        operation.setPetId(basic.petId());
        operation.setStaffId(dto.getStaffId());
        operation.setOperationName(dto.getOperationName());
        operation.setStartTime(dto.getStartTime());
        operation.setDuration(dto.getDuration());
        operation.setComment(Objects.isNull(dto.getComment()) ? "" : dto.getComment());
        operation.setPrice(dto.getPrice());
        operation.setPriceRatio(Objects.isNull(dto.getPriceRatio()) ? BigDecimal.ZERO : dto.getPriceRatio());
        return operation;
    }

    private MoeGroomingServiceOperation dtoToRecord(GroomingServiceOperationDTO dto) {
        MoeGroomingServiceOperation operation = new MoeGroomingServiceOperation();
        operation.setId(dto.getId());
        operation.setBusinessId(dto.getBusinessId());
        operation.setGroomingId(dto.getGroomingId());
        operation.setGroomingServiceId(dto.getGroomingServiceId());
        operation.setPetId(dto.getPetId());
        operation.setStaffId(dto.getStaffId());
        operation.setOperationName(dto.getOperationName());
        operation.setStartTime(dto.getStartTime());
        operation.setDuration(dto.getDuration());
        operation.setComment(dto.getComment());
        operation.setPrice(dto.getPrice());
        operation.setPriceRatio(dto.getPriceRatio());
        return operation;
    }

    public record OperationBasic(
            Integer businessId, Long companyId, Integer groomingId, Integer groomingServiceId, Integer petId) {}
}
