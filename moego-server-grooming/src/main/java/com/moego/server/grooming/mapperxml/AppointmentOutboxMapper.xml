<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.AppointmentOutboxMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.AppointmentOutbox">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="topic" jdbcType="VARCHAR" property="topic" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="event_time" jdbcType="TIMESTAMP" property="eventTime" />
    <result column="event_key" jdbcType="VARCHAR" property="eventKey" />
    <result column="event_type" jdbcType="INTEGER" property="eventType" typeHandler="com.moego.server.grooming.mapper.typehandler.EventTypeHandler" />
    <result column="status" jdbcType="TINYINT" property="status" typeHandler="com.moego.server.grooming.mapper.typehandler.OutboxSendStatusHandler" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.AppointmentOutbox">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="event_detail" jdbcType="LONGVARCHAR" property="eventDetail" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, topic, event_id, event_time, event_key, event_type, status, created_at
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    event_detail
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from appointment_outbox
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from appointment_outbox
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.AppointmentOutbox">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_outbox (topic, event_id, event_time, 
      event_key, event_type, 
      status, 
      created_at, event_detail)
    values (#{topic,jdbcType=VARCHAR}, #{eventId,jdbcType=VARCHAR}, #{eventTime,jdbcType=TIMESTAMP}, 
      #{eventKey,jdbcType=VARCHAR}, #{eventType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.EventTypeHandler}, 
      #{status,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.OutboxSendStatusHandler}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{eventDetail,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.AppointmentOutbox">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_outbox
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="topic != null">
        topic,
      </if>
      <if test="eventId != null">
        event_id,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
      <if test="eventKey != null">
        event_key,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="eventDetail != null">
        event_detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="topic != null">
        #{topic,jdbcType=VARCHAR},
      </if>
      <if test="eventId != null">
        #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eventKey != null">
        #{eventKey,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.EventTypeHandler},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.OutboxSendStatusHandler},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="eventDetail != null">
        #{eventDetail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.AppointmentOutbox">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_outbox
    <set>
      <if test="topic != null">
        topic = #{topic,jdbcType=VARCHAR},
      </if>
      <if test="eventId != null">
        event_id = #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eventKey != null">
        event_key = #{eventKey,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.EventTypeHandler},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.OutboxSendStatusHandler},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="eventDetail != null">
        event_detail = #{eventDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.AppointmentOutbox">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_outbox
    set topic = #{topic,jdbcType=VARCHAR},
      event_id = #{eventId,jdbcType=VARCHAR},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      event_key = #{eventKey,jdbcType=VARCHAR},
      event_type = #{eventType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.EventTypeHandler},
      status = #{status,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.OutboxSendStatusHandler},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      event_detail = #{eventDetail,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.AppointmentOutbox">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_outbox
    set topic = #{topic,jdbcType=VARCHAR},
      event_id = #{eventId,jdbcType=VARCHAR},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      event_key = #{eventKey,jdbcType=VARCHAR},
      event_type = #{eventType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.EventTypeHandler},
      status = #{status,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.OutboxSendStatusHandler},
      created_at = #{createdAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>