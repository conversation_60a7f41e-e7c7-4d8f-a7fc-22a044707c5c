package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeGroomingReportTemplateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public MoeGroomingReportTemplateExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageIsNull() {
            addCriterion("thank_you_message is null");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageIsNotNull() {
            addCriterion("thank_you_message is not null");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageEqualTo(String value) {
            addCriterion("thank_you_message =", value, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageNotEqualTo(String value) {
            addCriterion("thank_you_message <>", value, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageGreaterThan(String value) {
            addCriterion("thank_you_message >", value, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageGreaterThanOrEqualTo(String value) {
            addCriterion("thank_you_message >=", value, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageLessThan(String value) {
            addCriterion("thank_you_message <", value, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageLessThanOrEqualTo(String value) {
            addCriterion("thank_you_message <=", value, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageLike(String value) {
            addCriterion("thank_you_message like", value, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageNotLike(String value) {
            addCriterion("thank_you_message not like", value, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageIn(List<String> values) {
            addCriterion("thank_you_message in", values, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageNotIn(List<String> values) {
            addCriterion("thank_you_message not in", values, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageBetween(String value1, String value2) {
            addCriterion("thank_you_message between", value1, value2, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThankYouMessageNotBetween(String value1, String value2) {
            addCriterion("thank_you_message not between", value1, value2, "thankYouMessage");
            return (Criteria) this;
        }

        public Criteria andThemeColorIsNull() {
            addCriterion("theme_color is null");
            return (Criteria) this;
        }

        public Criteria andThemeColorIsNotNull() {
            addCriterion("theme_color is not null");
            return (Criteria) this;
        }

        public Criteria andThemeColorEqualTo(String value) {
            addCriterion("theme_color =", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotEqualTo(String value) {
            addCriterion("theme_color <>", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorGreaterThan(String value) {
            addCriterion("theme_color >", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorGreaterThanOrEqualTo(String value) {
            addCriterion("theme_color >=", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorLessThan(String value) {
            addCriterion("theme_color <", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorLessThanOrEqualTo(String value) {
            addCriterion("theme_color <=", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorLike(String value) {
            addCriterion("theme_color like", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotLike(String value) {
            addCriterion("theme_color not like", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorIn(List<String> values) {
            addCriterion("theme_color in", values, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotIn(List<String> values) {
            addCriterion("theme_color not in", values, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorBetween(String value1, String value2) {
            addCriterion("theme_color between", value1, value2, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotBetween(String value1, String value2) {
            addCriterion("theme_color not between", value1, value2, "themeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorIsNull() {
            addCriterion("light_theme_color is null");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorIsNotNull() {
            addCriterion("light_theme_color is not null");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorEqualTo(String value) {
            addCriterion("light_theme_color =", value, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorNotEqualTo(String value) {
            addCriterion("light_theme_color <>", value, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorGreaterThan(String value) {
            addCriterion("light_theme_color >", value, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorGreaterThanOrEqualTo(String value) {
            addCriterion("light_theme_color >=", value, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorLessThan(String value) {
            addCriterion("light_theme_color <", value, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorLessThanOrEqualTo(String value) {
            addCriterion("light_theme_color <=", value, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorLike(String value) {
            addCriterion("light_theme_color like", value, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorNotLike(String value) {
            addCriterion("light_theme_color not like", value, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorIn(List<String> values) {
            addCriterion("light_theme_color in", values, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorNotIn(List<String> values) {
            addCriterion("light_theme_color not in", values, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorBetween(String value1, String value2) {
            addCriterion("light_theme_color between", value1, value2, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andLightThemeColorNotBetween(String value1, String value2) {
            addCriterion("light_theme_color not between", value1, value2, "lightThemeColor");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseIsNull() {
            addCriterion("show_showcase is null");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseIsNotNull() {
            addCriterion("show_showcase is not null");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseEqualTo(Boolean value) {
            addCriterion("show_showcase =", value, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseNotEqualTo(Boolean value) {
            addCriterion("show_showcase <>", value, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseGreaterThan(Boolean value) {
            addCriterion("show_showcase >", value, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_showcase >=", value, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseLessThan(Boolean value) {
            addCriterion("show_showcase <", value, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseLessThanOrEqualTo(Boolean value) {
            addCriterion("show_showcase <=", value, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseIn(List<Boolean> values) {
            addCriterion("show_showcase in", values, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseNotIn(List<Boolean> values) {
            addCriterion("show_showcase not in", values, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseBetween(Boolean value1, Boolean value2) {
            addCriterion("show_showcase between", value1, value2, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowShowcaseNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_showcase not between", value1, value2, "showShowcase");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackIsNull() {
            addCriterion("show_overall_feedback is null");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackIsNotNull() {
            addCriterion("show_overall_feedback is not null");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackEqualTo(Boolean value) {
            addCriterion("show_overall_feedback =", value, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackNotEqualTo(Boolean value) {
            addCriterion("show_overall_feedback <>", value, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackGreaterThan(Boolean value) {
            addCriterion("show_overall_feedback >", value, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_overall_feedback >=", value, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackLessThan(Boolean value) {
            addCriterion("show_overall_feedback <", value, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackLessThanOrEqualTo(Boolean value) {
            addCriterion("show_overall_feedback <=", value, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackIn(List<Boolean> values) {
            addCriterion("show_overall_feedback in", values, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackNotIn(List<Boolean> values) {
            addCriterion("show_overall_feedback not in", values, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackBetween(Boolean value1, Boolean value2) {
            addCriterion("show_overall_feedback between", value1, value2, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andShowOverallFeedbackNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_overall_feedback not between", value1, value2, "showOverallFeedback");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoIsNull() {
            addCriterion("require_before_photo is null");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoIsNotNull() {
            addCriterion("require_before_photo is not null");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoEqualTo(Boolean value) {
            addCriterion("require_before_photo =", value, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoNotEqualTo(Boolean value) {
            addCriterion("require_before_photo <>", value, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoGreaterThan(Boolean value) {
            addCriterion("require_before_photo >", value, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoGreaterThanOrEqualTo(Boolean value) {
            addCriterion("require_before_photo >=", value, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoLessThan(Boolean value) {
            addCriterion("require_before_photo <", value, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoLessThanOrEqualTo(Boolean value) {
            addCriterion("require_before_photo <=", value, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoIn(List<Boolean> values) {
            addCriterion("require_before_photo in", values, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoNotIn(List<Boolean> values) {
            addCriterion("require_before_photo not in", values, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoBetween(Boolean value1, Boolean value2) {
            addCriterion("require_before_photo between", value1, value2, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireBeforePhotoNotBetween(Boolean value1, Boolean value2) {
            addCriterion("require_before_photo not between", value1, value2, "requireBeforePhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoIsNull() {
            addCriterion("require_after_photo is null");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoIsNotNull() {
            addCriterion("require_after_photo is not null");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoEqualTo(Boolean value) {
            addCriterion("require_after_photo =", value, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoNotEqualTo(Boolean value) {
            addCriterion("require_after_photo <>", value, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoGreaterThan(Boolean value) {
            addCriterion("require_after_photo >", value, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoGreaterThanOrEqualTo(Boolean value) {
            addCriterion("require_after_photo >=", value, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoLessThan(Boolean value) {
            addCriterion("require_after_photo <", value, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoLessThanOrEqualTo(Boolean value) {
            addCriterion("require_after_photo <=", value, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoIn(List<Boolean> values) {
            addCriterion("require_after_photo in", values, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoNotIn(List<Boolean> values) {
            addCriterion("require_after_photo not in", values, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoBetween(Boolean value1, Boolean value2) {
            addCriterion("require_after_photo between", value1, value2, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andRequireAfterPhotoNotBetween(Boolean value1, Boolean value2) {
            addCriterion("require_after_photo not between", value1, value2, "requireAfterPhoto");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionIsNull() {
            addCriterion("show_pet_condition is null");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionIsNotNull() {
            addCriterion("show_pet_condition is not null");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionEqualTo(Boolean value) {
            addCriterion("show_pet_condition =", value, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionNotEqualTo(Boolean value) {
            addCriterion("show_pet_condition <>", value, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionGreaterThan(Boolean value) {
            addCriterion("show_pet_condition >", value, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_pet_condition >=", value, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionLessThan(Boolean value) {
            addCriterion("show_pet_condition <", value, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionLessThanOrEqualTo(Boolean value) {
            addCriterion("show_pet_condition <=", value, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionIn(List<Boolean> values) {
            addCriterion("show_pet_condition in", values, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionNotIn(List<Boolean> values) {
            addCriterion("show_pet_condition not in", values, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionBetween(Boolean value1, Boolean value2) {
            addCriterion("show_pet_condition between", value1, value2, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowPetConditionNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_pet_condition not between", value1, value2, "showPetCondition");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameIsNull() {
            addCriterion("show_service_staff_name is null");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameIsNotNull() {
            addCriterion("show_service_staff_name is not null");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameEqualTo(Boolean value) {
            addCriterion("show_service_staff_name =", value, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameNotEqualTo(Boolean value) {
            addCriterion("show_service_staff_name <>", value, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameGreaterThan(Boolean value) {
            addCriterion("show_service_staff_name >", value, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_service_staff_name >=", value, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameLessThan(Boolean value) {
            addCriterion("show_service_staff_name <", value, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameLessThanOrEqualTo(Boolean value) {
            addCriterion("show_service_staff_name <=", value, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameIn(List<Boolean> values) {
            addCriterion("show_service_staff_name in", values, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameNotIn(List<Boolean> values) {
            addCriterion("show_service_staff_name not in", values, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameBetween(Boolean value1, Boolean value2) {
            addCriterion("show_service_staff_name between", value1, value2, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowServiceStaffNameNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_service_staff_name not between", value1, value2, "showServiceStaffName");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentIsNull() {
            addCriterion("show_next_appointment is null");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentIsNotNull() {
            addCriterion("show_next_appointment is not null");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentEqualTo(Boolean value) {
            addCriterion("show_next_appointment =", value, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentNotEqualTo(Boolean value) {
            addCriterion("show_next_appointment <>", value, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentGreaterThan(Boolean value) {
            addCriterion("show_next_appointment >", value, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_next_appointment >=", value, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentLessThan(Boolean value) {
            addCriterion("show_next_appointment <", value, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentLessThanOrEqualTo(Boolean value) {
            addCriterion("show_next_appointment <=", value, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentIn(List<Boolean> values) {
            addCriterion("show_next_appointment in", values, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentNotIn(List<Boolean> values) {
            addCriterion("show_next_appointment not in", values, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentBetween(Boolean value1, Boolean value2) {
            addCriterion("show_next_appointment between", value1, value2, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andShowNextAppointmentNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_next_appointment not between", value1, value2, "showNextAppointment");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeIsNull() {
            addCriterion("next_appointment_date_format_type is null");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeIsNotNull() {
            addCriterion("next_appointment_date_format_type is not null");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeEqualTo(Byte value) {
            addCriterion("next_appointment_date_format_type =", value, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeNotEqualTo(Byte value) {
            addCriterion("next_appointment_date_format_type <>", value, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeGreaterThan(Byte value) {
            addCriterion("next_appointment_date_format_type >", value, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("next_appointment_date_format_type >=", value, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeLessThan(Byte value) {
            addCriterion("next_appointment_date_format_type <", value, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeLessThanOrEqualTo(Byte value) {
            addCriterion("next_appointment_date_format_type <=", value, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeIn(List<Byte> values) {
            addCriterion("next_appointment_date_format_type in", values, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeNotIn(List<Byte> values) {
            addCriterion("next_appointment_date_format_type not in", values, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeBetween(Byte value1, Byte value2) {
            addCriterion("next_appointment_date_format_type between", value1, value2, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andNextAppointmentDateFormatTypeNotBetween(Byte value1, Byte value2) {
            addCriterion(
                    "next_appointment_date_format_type not between", value1, value2, "nextAppointmentDateFormatType");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterIsNull() {
            addCriterion("show_review_booster is null");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterIsNotNull() {
            addCriterion("show_review_booster is not null");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterEqualTo(Boolean value) {
            addCriterion("show_review_booster =", value, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterNotEqualTo(Boolean value) {
            addCriterion("show_review_booster <>", value, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterGreaterThan(Boolean value) {
            addCriterion("show_review_booster >", value, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_review_booster >=", value, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterLessThan(Boolean value) {
            addCriterion("show_review_booster <", value, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterLessThanOrEqualTo(Boolean value) {
            addCriterion("show_review_booster <=", value, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterIn(List<Boolean> values) {
            addCriterion("show_review_booster in", values, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterNotIn(List<Boolean> values) {
            addCriterion("show_review_booster not in", values, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterBetween(Boolean value1, Boolean value2) {
            addCriterion("show_review_booster between", value1, value2, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowReviewBoosterNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_review_booster not between", value1, value2, "showReviewBooster");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewIsNull() {
            addCriterion("show_yelp_review is null");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewIsNotNull() {
            addCriterion("show_yelp_review is not null");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewEqualTo(Boolean value) {
            addCriterion("show_yelp_review =", value, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewNotEqualTo(Boolean value) {
            addCriterion("show_yelp_review <>", value, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewGreaterThan(Boolean value) {
            addCriterion("show_yelp_review >", value, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_yelp_review >=", value, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewLessThan(Boolean value) {
            addCriterion("show_yelp_review <", value, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewLessThanOrEqualTo(Boolean value) {
            addCriterion("show_yelp_review <=", value, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewIn(List<Boolean> values) {
            addCriterion("show_yelp_review in", values, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewNotIn(List<Boolean> values) {
            addCriterion("show_yelp_review not in", values, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewBetween(Boolean value1, Boolean value2) {
            addCriterion("show_yelp_review between", value1, value2, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowYelpReviewNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_yelp_review not between", value1, value2, "showYelpReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewIsNull() {
            addCriterion("show_google_review is null");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewIsNotNull() {
            addCriterion("show_google_review is not null");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewEqualTo(Boolean value) {
            addCriterion("show_google_review =", value, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewNotEqualTo(Boolean value) {
            addCriterion("show_google_review <>", value, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewGreaterThan(Boolean value) {
            addCriterion("show_google_review >", value, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_google_review >=", value, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewLessThan(Boolean value) {
            addCriterion("show_google_review <", value, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewLessThanOrEqualTo(Boolean value) {
            addCriterion("show_google_review <=", value, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewIn(List<Boolean> values) {
            addCriterion("show_google_review in", values, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewNotIn(List<Boolean> values) {
            addCriterion("show_google_review not in", values, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewBetween(Boolean value1, Boolean value2) {
            addCriterion("show_google_review between", value1, value2, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowGoogleReviewNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_google_review not between", value1, value2, "showGoogleReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewIsNull() {
            addCriterion("show_facebook_review is null");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewIsNotNull() {
            addCriterion("show_facebook_review is not null");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewEqualTo(Boolean value) {
            addCriterion("show_facebook_review =", value, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewNotEqualTo(Boolean value) {
            addCriterion("show_facebook_review <>", value, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewGreaterThan(Boolean value) {
            addCriterion("show_facebook_review >", value, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_facebook_review >=", value, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewLessThan(Boolean value) {
            addCriterion("show_facebook_review <", value, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewLessThanOrEqualTo(Boolean value) {
            addCriterion("show_facebook_review <=", value, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewIn(List<Boolean> values) {
            addCriterion("show_facebook_review in", values, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewNotIn(List<Boolean> values) {
            addCriterion("show_facebook_review not in", values, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewBetween(Boolean value1, Boolean value2) {
            addCriterion("show_facebook_review between", value1, value2, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andShowFacebookReviewNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_facebook_review not between", value1, value2, "showFacebookReview");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeIsNull() {
            addCriterion("last_publish_time is null");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeIsNotNull() {
            addCriterion("last_publish_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeEqualTo(Date value) {
            addCriterion("last_publish_time =", value, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeNotEqualTo(Date value) {
            addCriterion("last_publish_time <>", value, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeGreaterThan(Date value) {
            addCriterion("last_publish_time >", value, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("last_publish_time >=", value, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeLessThan(Date value) {
            addCriterion("last_publish_time <", value, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeLessThanOrEqualTo(Date value) {
            addCriterion("last_publish_time <=", value, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeIn(List<Date> values) {
            addCriterion("last_publish_time in", values, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeNotIn(List<Date> values) {
            addCriterion("last_publish_time not in", values, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeBetween(Date value1, Date value2) {
            addCriterion("last_publish_time between", value1, value2, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andLastPublishTimeNotBetween(Date value1, Date value2) {
            addCriterion("last_publish_time not between", value1, value2, "lastPublishTime");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Integer value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Integer value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Integer value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Integer value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Integer value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Integer> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Integer> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Integer value1, Integer value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Integer value1, Integer value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andThemeCodeIsNull() {
            addCriterion("theme_code is null");
            return (Criteria) this;
        }

        public Criteria andThemeCodeIsNotNull() {
            addCriterion("theme_code is not null");
            return (Criteria) this;
        }

        public Criteria andThemeCodeEqualTo(String value) {
            addCriterion("theme_code =", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeNotEqualTo(String value) {
            addCriterion("theme_code <>", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeGreaterThan(String value) {
            addCriterion("theme_code >", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("theme_code >=", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeLessThan(String value) {
            addCriterion("theme_code <", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeLessThanOrEqualTo(String value) {
            addCriterion("theme_code <=", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeLike(String value) {
            addCriterion("theme_code like", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeNotLike(String value) {
            addCriterion("theme_code not like", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeIn(List<String> values) {
            addCriterion("theme_code in", values, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeNotIn(List<String> values) {
            addCriterion("theme_code not in", values, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeBetween(String value1, String value2) {
            addCriterion("theme_code between", value1, value2, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeNotBetween(String value1, String value2) {
            addCriterion("theme_code not between", value1, value2, "themeCode");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
