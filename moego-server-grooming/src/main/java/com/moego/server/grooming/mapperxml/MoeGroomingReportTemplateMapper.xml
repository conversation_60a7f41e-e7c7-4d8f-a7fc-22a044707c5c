<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingReportTemplateMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="thank_you_message" jdbcType="VARCHAR" property="thankYouMessage" />
    <result column="theme_color" jdbcType="VARCHAR" property="themeColor" />
    <result column="light_theme_color" jdbcType="VARCHAR" property="lightThemeColor" />
    <result column="show_showcase" jdbcType="BIT" property="showShowcase" />
    <result column="show_overall_feedback" jdbcType="BIT" property="showOverallFeedback" />
    <result column="require_before_photo" jdbcType="BIT" property="requireBeforePhoto" />
    <result column="require_after_photo" jdbcType="BIT" property="requireAfterPhoto" />
    <result column="show_pet_condition" jdbcType="BIT" property="showPetCondition" />
    <result column="show_service_staff_name" jdbcType="BIT" property="showServiceStaffName" />
    <result column="show_next_appointment" jdbcType="BIT" property="showNextAppointment" />
    <result column="next_appointment_date_format_type" jdbcType="TINYINT" property="nextAppointmentDateFormatType" />
    <result column="show_review_booster" jdbcType="BIT" property="showReviewBooster" />
    <result column="show_yelp_review" jdbcType="BIT" property="showYelpReview" />
    <result column="show_google_review" jdbcType="BIT" property="showGoogleReview" />
    <result column="show_facebook_review" jdbcType="BIT" property="showFacebookReview" />
    <result column="last_publish_time" jdbcType="TIMESTAMP" property="lastPublishTime" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="theme_code" jdbcType="VARCHAR" property="themeCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, thank_you_message, theme_color, light_theme_color, show_showcase, 
    show_overall_feedback, require_before_photo, require_after_photo, show_pet_condition, 
    show_service_staff_name, show_next_appointment, next_appointment_date_format_type, 
    show_review_booster, show_yelp_review, show_google_review, show_facebook_review, 
    last_publish_time, update_by, create_time, update_time, title, company_id, theme_code
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportTemplateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_report_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_grooming_report_template
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_report_template
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportTemplateExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_report_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_report_template (business_id, thank_you_message, theme_color, 
      light_theme_color, show_showcase, show_overall_feedback, 
      require_before_photo, require_after_photo, show_pet_condition, 
      show_service_staff_name, show_next_appointment, next_appointment_date_format_type, 
      show_review_booster, show_yelp_review, show_google_review, 
      show_facebook_review, last_publish_time, update_by, 
      create_time, update_time, title, 
      company_id, theme_code)
    values (#{businessId,jdbcType=INTEGER}, #{thankYouMessage,jdbcType=VARCHAR}, #{themeColor,jdbcType=VARCHAR}, 
      #{lightThemeColor,jdbcType=VARCHAR}, #{showShowcase,jdbcType=BIT}, #{showOverallFeedback,jdbcType=BIT}, 
      #{requireBeforePhoto,jdbcType=BIT}, #{requireAfterPhoto,jdbcType=BIT}, #{showPetCondition,jdbcType=BIT}, 
      #{showServiceStaffName,jdbcType=BIT}, #{showNextAppointment,jdbcType=BIT}, #{nextAppointmentDateFormatType,jdbcType=TINYINT}, 
      #{showReviewBooster,jdbcType=BIT}, #{showYelpReview,jdbcType=BIT}, #{showGoogleReview,jdbcType=BIT}, 
      #{showFacebookReview,jdbcType=BIT}, #{lastPublishTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{title,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=BIGINT}, #{themeCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_report_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="thankYouMessage != null">
        thank_you_message,
      </if>
      <if test="themeColor != null">
        theme_color,
      </if>
      <if test="lightThemeColor != null">
        light_theme_color,
      </if>
      <if test="showShowcase != null">
        show_showcase,
      </if>
      <if test="showOverallFeedback != null">
        show_overall_feedback,
      </if>
      <if test="requireBeforePhoto != null">
        require_before_photo,
      </if>
      <if test="requireAfterPhoto != null">
        require_after_photo,
      </if>
      <if test="showPetCondition != null">
        show_pet_condition,
      </if>
      <if test="showServiceStaffName != null">
        show_service_staff_name,
      </if>
      <if test="showNextAppointment != null">
        show_next_appointment,
      </if>
      <if test="nextAppointmentDateFormatType != null">
        next_appointment_date_format_type,
      </if>
      <if test="showReviewBooster != null">
        show_review_booster,
      </if>
      <if test="showYelpReview != null">
        show_yelp_review,
      </if>
      <if test="showGoogleReview != null">
        show_google_review,
      </if>
      <if test="showFacebookReview != null">
        show_facebook_review,
      </if>
      <if test="lastPublishTime != null">
        last_publish_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="themeCode != null">
        theme_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="thankYouMessage != null">
        #{thankYouMessage,jdbcType=VARCHAR},
      </if>
      <if test="themeColor != null">
        #{themeColor,jdbcType=VARCHAR},
      </if>
      <if test="lightThemeColor != null">
        #{lightThemeColor,jdbcType=VARCHAR},
      </if>
      <if test="showShowcase != null">
        #{showShowcase,jdbcType=BIT},
      </if>
      <if test="showOverallFeedback != null">
        #{showOverallFeedback,jdbcType=BIT},
      </if>
      <if test="requireBeforePhoto != null">
        #{requireBeforePhoto,jdbcType=BIT},
      </if>
      <if test="requireAfterPhoto != null">
        #{requireAfterPhoto,jdbcType=BIT},
      </if>
      <if test="showPetCondition != null">
        #{showPetCondition,jdbcType=BIT},
      </if>
      <if test="showServiceStaffName != null">
        #{showServiceStaffName,jdbcType=BIT},
      </if>
      <if test="showNextAppointment != null">
        #{showNextAppointment,jdbcType=BIT},
      </if>
      <if test="nextAppointmentDateFormatType != null">
        #{nextAppointmentDateFormatType,jdbcType=TINYINT},
      </if>
      <if test="showReviewBooster != null">
        #{showReviewBooster,jdbcType=BIT},
      </if>
      <if test="showYelpReview != null">
        #{showYelpReview,jdbcType=BIT},
      </if>
      <if test="showGoogleReview != null">
        #{showGoogleReview,jdbcType=BIT},
      </if>
      <if test="showFacebookReview != null">
        #{showFacebookReview,jdbcType=BIT},
      </if>
      <if test="lastPublishTime != null">
        #{lastPublishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="themeCode != null">
        #{themeCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportTemplateExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_report_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report_template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.thankYouMessage != null">
        thank_you_message = #{record.thankYouMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.themeColor != null">
        theme_color = #{record.themeColor,jdbcType=VARCHAR},
      </if>
      <if test="record.lightThemeColor != null">
        light_theme_color = #{record.lightThemeColor,jdbcType=VARCHAR},
      </if>
      <if test="record.showShowcase != null">
        show_showcase = #{record.showShowcase,jdbcType=BIT},
      </if>
      <if test="record.showOverallFeedback != null">
        show_overall_feedback = #{record.showOverallFeedback,jdbcType=BIT},
      </if>
      <if test="record.requireBeforePhoto != null">
        require_before_photo = #{record.requireBeforePhoto,jdbcType=BIT},
      </if>
      <if test="record.requireAfterPhoto != null">
        require_after_photo = #{record.requireAfterPhoto,jdbcType=BIT},
      </if>
      <if test="record.showPetCondition != null">
        show_pet_condition = #{record.showPetCondition,jdbcType=BIT},
      </if>
      <if test="record.showServiceStaffName != null">
        show_service_staff_name = #{record.showServiceStaffName,jdbcType=BIT},
      </if>
      <if test="record.showNextAppointment != null">
        show_next_appointment = #{record.showNextAppointment,jdbcType=BIT},
      </if>
      <if test="record.nextAppointmentDateFormatType != null">
        next_appointment_date_format_type = #{record.nextAppointmentDateFormatType,jdbcType=TINYINT},
      </if>
      <if test="record.showReviewBooster != null">
        show_review_booster = #{record.showReviewBooster,jdbcType=BIT},
      </if>
      <if test="record.showYelpReview != null">
        show_yelp_review = #{record.showYelpReview,jdbcType=BIT},
      </if>
      <if test="record.showGoogleReview != null">
        show_google_review = #{record.showGoogleReview,jdbcType=BIT},
      </if>
      <if test="record.showFacebookReview != null">
        show_facebook_review = #{record.showFacebookReview,jdbcType=BIT},
      </if>
      <if test="record.lastPublishTime != null">
        last_publish_time = #{record.lastPublishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.themeCode != null">
        theme_code = #{record.themeCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report_template
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      thank_you_message = #{record.thankYouMessage,jdbcType=VARCHAR},
      theme_color = #{record.themeColor,jdbcType=VARCHAR},
      light_theme_color = #{record.lightThemeColor,jdbcType=VARCHAR},
      show_showcase = #{record.showShowcase,jdbcType=BIT},
      show_overall_feedback = #{record.showOverallFeedback,jdbcType=BIT},
      require_before_photo = #{record.requireBeforePhoto,jdbcType=BIT},
      require_after_photo = #{record.requireAfterPhoto,jdbcType=BIT},
      show_pet_condition = #{record.showPetCondition,jdbcType=BIT},
      show_service_staff_name = #{record.showServiceStaffName,jdbcType=BIT},
      show_next_appointment = #{record.showNextAppointment,jdbcType=BIT},
      next_appointment_date_format_type = #{record.nextAppointmentDateFormatType,jdbcType=TINYINT},
      show_review_booster = #{record.showReviewBooster,jdbcType=BIT},
      show_yelp_review = #{record.showYelpReview,jdbcType=BIT},
      show_google_review = #{record.showGoogleReview,jdbcType=BIT},
      show_facebook_review = #{record.showFacebookReview,jdbcType=BIT},
      last_publish_time = #{record.lastPublishTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      title = #{record.title,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=BIGINT},
      theme_code = #{record.themeCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report_template
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="thankYouMessage != null">
        thank_you_message = #{thankYouMessage,jdbcType=VARCHAR},
      </if>
      <if test="themeColor != null">
        theme_color = #{themeColor,jdbcType=VARCHAR},
      </if>
      <if test="lightThemeColor != null">
        light_theme_color = #{lightThemeColor,jdbcType=VARCHAR},
      </if>
      <if test="showShowcase != null">
        show_showcase = #{showShowcase,jdbcType=BIT},
      </if>
      <if test="showOverallFeedback != null">
        show_overall_feedback = #{showOverallFeedback,jdbcType=BIT},
      </if>
      <if test="requireBeforePhoto != null">
        require_before_photo = #{requireBeforePhoto,jdbcType=BIT},
      </if>
      <if test="requireAfterPhoto != null">
        require_after_photo = #{requireAfterPhoto,jdbcType=BIT},
      </if>
      <if test="showPetCondition != null">
        show_pet_condition = #{showPetCondition,jdbcType=BIT},
      </if>
      <if test="showServiceStaffName != null">
        show_service_staff_name = #{showServiceStaffName,jdbcType=BIT},
      </if>
      <if test="showNextAppointment != null">
        show_next_appointment = #{showNextAppointment,jdbcType=BIT},
      </if>
      <if test="nextAppointmentDateFormatType != null">
        next_appointment_date_format_type = #{nextAppointmentDateFormatType,jdbcType=TINYINT},
      </if>
      <if test="showReviewBooster != null">
        show_review_booster = #{showReviewBooster,jdbcType=BIT},
      </if>
      <if test="showYelpReview != null">
        show_yelp_review = #{showYelpReview,jdbcType=BIT},
      </if>
      <if test="showGoogleReview != null">
        show_google_review = #{showGoogleReview,jdbcType=BIT},
      </if>
      <if test="showFacebookReview != null">
        show_facebook_review = #{showFacebookReview,jdbcType=BIT},
      </if>
      <if test="lastPublishTime != null">
        last_publish_time = #{lastPublishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="themeCode != null">
        theme_code = #{themeCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report_template
    set business_id = #{businessId,jdbcType=INTEGER},
      thank_you_message = #{thankYouMessage,jdbcType=VARCHAR},
      theme_color = #{themeColor,jdbcType=VARCHAR},
      light_theme_color = #{lightThemeColor,jdbcType=VARCHAR},
      show_showcase = #{showShowcase,jdbcType=BIT},
      show_overall_feedback = #{showOverallFeedback,jdbcType=BIT},
      require_before_photo = #{requireBeforePhoto,jdbcType=BIT},
      require_after_photo = #{requireAfterPhoto,jdbcType=BIT},
      show_pet_condition = #{showPetCondition,jdbcType=BIT},
      show_service_staff_name = #{showServiceStaffName,jdbcType=BIT},
      show_next_appointment = #{showNextAppointment,jdbcType=BIT},
      next_appointment_date_format_type = #{nextAppointmentDateFormatType,jdbcType=TINYINT},
      show_review_booster = #{showReviewBooster,jdbcType=BIT},
      show_yelp_review = #{showYelpReview,jdbcType=BIT},
      show_google_review = #{showGoogleReview,jdbcType=BIT},
      show_facebook_review = #{showFacebookReview,jdbcType=BIT},
      last_publish_time = #{lastPublishTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      title = #{title,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      theme_code = #{themeCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="initGroomingReportTemplate">
    insert into moe_grooming_report_template (business_id, company_id) values (#{businessId}, #{companyId})
    on duplicate key update update_time = now()
  </insert>

  <select id="selectByBusinessId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_report_template
    where business_id = #{businessId,jdbcType=INTEGER}
  </select>

</mapper>