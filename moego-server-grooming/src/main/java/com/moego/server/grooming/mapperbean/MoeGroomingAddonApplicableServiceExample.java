package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeGroomingAddonApplicableServiceExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public MoeGroomingAddonApplicableServiceExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andAddonIdIsNull() {
            addCriterion("addon_id is null");
            return (Criteria) this;
        }

        public Criteria andAddonIdIsNotNull() {
            addCriterion("addon_id is not null");
            return (Criteria) this;
        }

        public Criteria andAddonIdEqualTo(Long value) {
            addCriterion("addon_id =", value, "addonId");
            return (Criteria) this;
        }

        public Criteria andAddonIdNotEqualTo(Long value) {
            addCriterion("addon_id <>", value, "addonId");
            return (Criteria) this;
        }

        public Criteria andAddonIdGreaterThan(Long value) {
            addCriterion("addon_id >", value, "addonId");
            return (Criteria) this;
        }

        public Criteria andAddonIdGreaterThanOrEqualTo(Long value) {
            addCriterion("addon_id >=", value, "addonId");
            return (Criteria) this;
        }

        public Criteria andAddonIdLessThan(Long value) {
            addCriterion("addon_id <", value, "addonId");
            return (Criteria) this;
        }

        public Criteria andAddonIdLessThanOrEqualTo(Long value) {
            addCriterion("addon_id <=", value, "addonId");
            return (Criteria) this;
        }

        public Criteria andAddonIdIn(List<Long> values) {
            addCriterion("addon_id in", values, "addonId");
            return (Criteria) this;
        }

        public Criteria andAddonIdNotIn(List<Long> values) {
            addCriterion("addon_id not in", values, "addonId");
            return (Criteria) this;
        }

        public Criteria andAddonIdBetween(Long value1, Long value2) {
            addCriterion("addon_id between", value1, value2, "addonId");
            return (Criteria) this;
        }

        public Criteria andAddonIdNotBetween(Long value1, Long value2) {
            addCriterion("addon_id not between", value1, value2, "addonId");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIsNull() {
            addCriterion("service_item_type is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIsNotNull() {
            addCriterion("service_item_type is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeEqualTo(Integer value) {
            addCriterion("service_item_type =", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotEqualTo(Integer value) {
            addCriterion("service_item_type <>", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeGreaterThan(Integer value) {
            addCriterion("service_item_type >", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_item_type >=", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeLessThan(Integer value) {
            addCriterion("service_item_type <", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeLessThanOrEqualTo(Integer value) {
            addCriterion("service_item_type <=", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIn(List<Integer> values) {
            addCriterion("service_item_type in", values, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotIn(List<Integer> values) {
            addCriterion("service_item_type not in", values, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeBetween(Integer value1, Integer value2) {
            addCriterion("service_item_type between", value1, value2, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("service_item_type not between", value1, value2, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesIsNull() {
            addCriterion("available_for_all_services is null");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesIsNotNull() {
            addCriterion("available_for_all_services is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesEqualTo(Boolean value) {
            addCriterion("available_for_all_services =", value, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesNotEqualTo(Boolean value) {
            addCriterion("available_for_all_services <>", value, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesGreaterThan(Boolean value) {
            addCriterion("available_for_all_services >", value, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesGreaterThanOrEqualTo(Boolean value) {
            addCriterion("available_for_all_services >=", value, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesLessThan(Boolean value) {
            addCriterion("available_for_all_services <", value, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesLessThanOrEqualTo(Boolean value) {
            addCriterion("available_for_all_services <=", value, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesIn(List<Boolean> values) {
            addCriterion("available_for_all_services in", values, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesNotIn(List<Boolean> values) {
            addCriterion("available_for_all_services not in", values, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesBetween(Boolean value1, Boolean value2) {
            addCriterion("available_for_all_services between", value1, value2, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableForAllServicesNotBetween(Boolean value1, Boolean value2) {
            addCriterion("available_for_all_services not between", value1, value2, "availableForAllServices");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListIsNull() {
            addCriterion("available_service_id_list is null");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListIsNotNull() {
            addCriterion("available_service_id_list is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListEqualTo(String value) {
            addCriterion("available_service_id_list =", value, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListNotEqualTo(String value) {
            addCriterion("available_service_id_list <>", value, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListGreaterThan(String value) {
            addCriterion("available_service_id_list >", value, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListGreaterThanOrEqualTo(String value) {
            addCriterion("available_service_id_list >=", value, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListLessThan(String value) {
            addCriterion("available_service_id_list <", value, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListLessThanOrEqualTo(String value) {
            addCriterion("available_service_id_list <=", value, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListLike(String value) {
            addCriterion("available_service_id_list like", value, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListNotLike(String value) {
            addCriterion("available_service_id_list not like", value, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListIn(List<String> values) {
            addCriterion("available_service_id_list in", values, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListNotIn(List<String> values) {
            addCriterion("available_service_id_list not in", values, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListBetween(String value1, String value2) {
            addCriterion("available_service_id_list between", value1, value2, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andAvailableServiceIdListNotBetween(String value1, String value2) {
            addCriterion("available_service_id_list not between", value1, value2, "availableServiceIdList");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
