package com.moego.server.grooming.mapperbean;

public class MoeGc<PERSON>ask {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_task.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_task.gc_calendar_id
     *
     * @mbg.generated
     */
    private Integer gcCalendarId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_task.task_status
     *
     * @mbg.generated
     */
    private Byte taskStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_task.complete_time
     *
     * @mbg.generated
     */
    private Long completeTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_task.complete_count
     *
     * @mbg.generated
     */
    private Integer completeCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_task.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_task.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_task.id
     *
     * @return the value of moe_gc_task.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_task.id
     *
     * @param id the value for moe_gc_task.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_task.gc_calendar_id
     *
     * @return the value of moe_gc_task.gc_calendar_id
     *
     * @mbg.generated
     */
    public Integer getGcCalendarId() {
        return gcCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_task.gc_calendar_id
     *
     * @param gcCalendarId the value for moe_gc_task.gc_calendar_id
     *
     * @mbg.generated
     */
    public void setGcCalendarId(Integer gcCalendarId) {
        this.gcCalendarId = gcCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_task.task_status
     *
     * @return the value of moe_gc_task.task_status
     *
     * @mbg.generated
     */
    public Byte getTaskStatus() {
        return taskStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_task.task_status
     *
     * @param taskStatus the value for moe_gc_task.task_status
     *
     * @mbg.generated
     */
    public void setTaskStatus(Byte taskStatus) {
        this.taskStatus = taskStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_task.complete_time
     *
     * @return the value of moe_gc_task.complete_time
     *
     * @mbg.generated
     */
    public Long getCompleteTime() {
        return completeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_task.complete_time
     *
     * @param completeTime the value for moe_gc_task.complete_time
     *
     * @mbg.generated
     */
    public void setCompleteTime(Long completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_task.complete_count
     *
     * @return the value of moe_gc_task.complete_count
     *
     * @mbg.generated
     */
    public Integer getCompleteCount() {
        return completeCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_task.complete_count
     *
     * @param completeCount the value for moe_gc_task.complete_count
     *
     * @mbg.generated
     */
    public void setCompleteCount(Integer completeCount) {
        this.completeCount = completeCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_task.create_time
     *
     * @return the value of moe_gc_task.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_task.create_time
     *
     * @param createTime the value for moe_gc_task.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_task.update_time
     *
     * @return the value of moe_gc_task.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_task.update_time
     *
     * @param updateTime the value for moe_gc_task.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
