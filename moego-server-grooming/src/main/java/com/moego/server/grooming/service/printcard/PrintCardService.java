package com.moego.server.grooming.service.printcard;

import static java.util.Comparator.comparing;

import com.google.protobuf.util.Timestamps;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.appointment.v1.BoardingSplitLodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.ListBoardingSplitLodgingsRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.PetBelongingDTO;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.convert.PrintCardConverter;
import com.moego.server.grooming.convert.TimestampConverter;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.LodgingInfo;
import com.moego.server.grooming.dto.LodgingTypeDTO;
import com.moego.server.grooming.dto.LodgingUnitDTO;
import com.moego.server.grooming.dto.printcard.ActivityCardAddOnColumn;
import com.moego.server.grooming.dto.printcard.ActivityCardDetail;
import com.moego.server.grooming.dto.printcard.ActivityCardFeedingColumn;
import com.moego.server.grooming.dto.printcard.ActivityCardMedicationColumn;
import com.moego.server.grooming.dto.printcard.ExtraServiceDetail;
import com.moego.server.grooming.dto.printcard.FeedingInstruction;
import com.moego.server.grooming.dto.printcard.MedicationInstruction;
import com.moego.server.grooming.dto.printcard.PrintCardAppointment;
import com.moego.server.grooming.dto.printcard.PrintCardCustomer;
import com.moego.server.grooming.dto.printcard.PrintCardDetail;
import com.moego.server.grooming.dto.printcard.PrintCardHistory;
import com.moego.server.grooming.dto.printcard.PrintPetCodeBinding;
import com.moego.server.grooming.dto.printcard.StayCardAppointment;
import com.moego.server.grooming.dto.printcard.StayCardDetail;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.helper.AgreementHelper;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.helper.CustomerHelper;
import com.moego.server.grooming.helper.OfferingHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.AppointmentPetFeedingMapper;
import com.moego.server.grooming.mapper.AppointmentPetMedicationMapper;
import com.moego.server.grooming.mapper.AppointmentPetScheduleSettingMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.AppointmentPetFeeding;
import com.moego.server.grooming.mapperbean.AppointmentPetFeedingExample;
import com.moego.server.grooming.mapperbean.AppointmentPetMedication;
import com.moego.server.grooming.mapperbean.AppointmentPetMedicationExample;
import com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting;
import com.moego.server.grooming.mapperbean.AppointmentPetScheduleSettingExample;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapstruct.LodgingTypeConverter;
import com.moego.server.grooming.mapstruct.LodgingUnitConverter;
import com.moego.server.grooming.service.AppointmentServiceDetailService;
import com.moego.server.grooming.service.GroomingServiceOperationService;
import com.moego.server.grooming.service.MoeGroomingNoteService;
import com.moego.server.grooming.service.MoePetDetailService;
import com.moego.server.grooming.utils.PetDetailUtil;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class PrintCardService {

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private MoeGroomingNoteService moeGroomingNoteService;

    @Autowired
    private MoePetDetailService moePetDetailService;

    @Autowired
    private PetDetailMapperProxy moeGroomingPetDetailMapper;

    @Autowired
    private AppointmentServiceDetailService appointmentServiceDetailService;

    @Resource
    private GroomingServiceOperationService groomingServiceOperationService;

    @Autowired
    private AppointmentPetFeedingMapper appointmentPetFeedingMapper;

    @Autowired
    private AppointmentPetMedicationMapper appointmentPetMedicationMapper;

    @Autowired
    private AppointmentPetScheduleSettingMapper appointmentPetScheduleSettingMapper;

    @Autowired
    private CustomerHelper customerHelper;

    @Autowired
    private CompanyHelper companyHelper;

    @Autowired
    private AgreementHelper agreementHelper;

    @Autowired
    private OfferingHelper offeringHelper;

    @Resource
    private BoardingSplitLodgingServiceGrpc.BoardingSplitLodgingServiceBlockingStub boardingSplitLodgingService;

    public List<PrintCardDetail> getPrintCardInfoByDate(String date, Long businessId, Long companyId) {
        List<MoeGroomingAppointment> appointmentList = moeGroomingAppointmentMapper.getAllGroomingByDateRange(
                Math.toIntExact(businessId), date, date, null, AppointmentStatusSet.ACTIVE_STATUS_SET);
        if (CollectionUtils.isEmpty(appointmentList)) {
            return List.of();
        }
        return getPrintCardInfo(businessId, companyId, appointmentList, date);
    }

    public List<PrintCardDetail> getPrintCardInfo(
            Long businessId,
            Long companyId,
            List<MoeGroomingAppointment> appointmentList,
            String dateForHistoryAppointment) {
        if (CollectionUtils.isEmpty(appointmentList)) {
            return List.of();
        }
        Map<Integer, MoeGroomingAppointment> appointmentMap = appointmentList.stream()
                .collect(Collectors.toMap(MoeGroomingAppointment::getId, k -> k, (k1, k2) -> k1));
        List<Integer> appointmentIdList =
                appointmentList.stream().map(MoeGroomingAppointment::getId).toList();
        List<Long> customerIds = appointmentList.stream()
                .filter(k -> k.getCustomerId() != null && k.getCustomerId() > 0)
                .map(k -> k.getCustomerId().longValue())
                .toList();

        // staff
        Map<Integer, MoeStaffDto> staffMap = companyHelper.getBusinessStaff(businessId.intValue());

        // timezone name
        var timeZoneFuture = CompletableFuture.supplyAsync(
                () -> companyHelper.getCompanyTimeZoneName(companyId), ThreadPool.getSubmitExecutor());

        // customer tag
        var customerTagMapFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getCustomerTagName(companyId), ThreadPool.getSubmitExecutor());

        // service
        var petDetailListFuture = CompletableFuture.supplyAsync(
                () -> moePetDetailService.queryAllPetDetailByGroomingIds(appointmentIdList),
                ThreadPool.getSubmitExecutor());

        // customer
        var customerMapFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getCustomerInfos(customerIds), ThreadPool.getSubmitExecutor());

        // customer notes
        var customerNotesFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getCustomerNotes(customerIds), ThreadPool.getSubmitExecutor());

        // agreement
        var customerAgreementsFuture = CompletableFuture.supplyAsync(
                () -> agreementHelper.batchGetCustomerRecentSignedAgreements(customerIds, businessId),
                ThreadPool.getSubmitExecutor());

        // appointment notes
        var appointmentNotesFuture = CompletableFuture.supplyAsync(
                () -> moeGroomingNoteService.getNoteListByGroomingIdList(appointmentIdList).stream()
                        .collect(Collectors.groupingBy(MoeGroomingNote::getGroomingId)),
                ThreadPool.getSubmitExecutor());

        // operation
        var serviceOperationMapFuture = petDetailListFuture.thenApplyAsync(
                result -> {
                    return groomingServiceOperationService.getOperationMapByGroomingServiceIdList(
                            Math.toIntExact(businessId),
                            result.stream()
                                    .filter(GroomingPetDetailDTO::getEnableOperation)
                                    .map(GroomingPetDetailDTO::getId)
                                    .toList());
                },
                ThreadPool.getSubmitExecutor());

        // pet
        var petMapFuture = petDetailListFuture.thenApplyAsync(
                result -> customerHelper.getPetInfoMap(appointmentServiceDetailService.getPetIds(result, null)),
                ThreadPool.getSubmitExecutor());

        // pet notes
        var petNotesFuture = petDetailListFuture.thenApplyAsync(
                result -> customerHelper.getPetNotes(appointmentServiceDetailService.getPetIds(result, null).stream()
                        .map(Integer::longValue)
                        .toList()),
                ThreadPool.getSubmitExecutor());

        // boarding split lodgings
        var boardingSplitLodgingsFuture = CompletableFuture.supplyAsync(() -> boardingSplitLodgingService
                .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                        .addAllAppointmentIds(
                                appointmentIdList.stream().map(Long::valueOf).toList())
                        .build())
                .getBoardingSplitLodgingsList());

        // lodging
        CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
                lodgingMapFuture = boardingSplitLodgingsFuture.thenCombineAsync(
                        petDetailListFuture,
                        (boardingSplitLodgings, allPetDetails) -> {
                            var lodgingIds = getLodgingIds(allPetDetails);
                            var splitLodgingIds = boardingSplitLodgings.stream()
                                    .map(BoardingSplitLodgingModel::getLodgingId)
                                    .toList();
                            return offeringHelper.getLodgingMap(
                                    Stream.concat(lodgingIds.stream(), splitLodgingIds.stream())
                                            .distinct()
                                            .toList());
                        },
                        ThreadPool.getSubmitExecutor());

        // pet history print card
        var petHistoryAppointmentFuture = petDetailListFuture.thenApplyAsync(
                result -> batchGetPetPrintCardHistory(
                        businessId.intValue(),
                        customerIds.stream().map(Long::intValue).toList(),
                        appointmentServiceDetailService.getPetIds(result, null),
                        dateForHistoryAppointment,
                        staffMap),
                ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(
                        timeZoneFuture,
                        customerTagMapFuture,
                        petDetailListFuture,
                        customerMapFuture,
                        customerNotesFuture,
                        customerAgreementsFuture,
                        appointmentNotesFuture,
                        serviceOperationMapFuture,
                        petMapFuture,
                        petNotesFuture,
                        boardingSplitLodgingsFuture,
                        lodgingMapFuture,
                        petHistoryAppointmentFuture)
                .join();

        var boardingSplitLodgingModels = boardingSplitLodgingsFuture.join();
        Map<Integer, List<GroomingPetDetailDTO>> appointmentPetDetailMap =
                petDetailListFuture.join().stream().collect(Collectors.groupingBy(GroomingPetDetailDTO::getGroomingId));

        List<PrintCardDetail> result = new ArrayList<>();
        appointmentMap.forEach((appointmentId, appointment) -> {
            BusinessCustomerInfoModel customer =
                    customerMapFuture.join().get(appointment.getCustomerId().longValue());
            if (customer == null) {
                return;
            }
            PrintCardCustomer printCardCustomer = PrintCardConverter.INSTANCE.toPrintCardCustomer(
                    customer,
                    customerTagMapFuture.join(),
                    customerNotesFuture.join(),
                    customerAgreementsFuture.join(),
                    timeZoneFuture.join());
            PrintCardAppointment printCardAppointment =
                    PrintCardConverter.INSTANCE.toPrintCardAppointment(appointment, appointmentNotesFuture.join());

            Map<Integer, List<GroomingPetDetailDTO>> petDetailMap =
                    appointmentPetDetailMap.getOrDefault(appointmentId, List.of()).stream()
                            .collect(Collectors.groupingBy(GroomingPetDetailDTO::getPetId));
            petDetailMap.forEach((petId, petDetailList) -> {
                CustomerPetPetCodeDTO pet = petMapFuture.join().get(petId);
                if (pet == null) {
                    return;
                }

                var splitLodgingModels = boardingSplitLodgingModels.stream()
                        .filter(boardingSplitLodgingModel ->
                                Objects.equals(boardingSplitLodgingModel.getAppointmentId(), appointmentId.longValue())
                                        && Objects.equals(boardingSplitLodgingModel.getPetId(), petId.longValue()))
                        .sorted(comparing(BoardingSplitLodgingModel::getStartDateTime, Timestamps.comparator()))
                        .toList();

                PrintCardDetail printCardDetail = new PrintCardDetail();
                printCardDetail.setGroomingId(appointmentId);
                printCardDetail.setPetId(petId);
                printCardDetail.setPet(
                        PrintCardConverter.INSTANCE.toPrintCardPet(pet, petNotesFuture.join(), List.of()));
                printCardDetail.setCustomer(printCardCustomer);
                printCardDetail.setAppointment(printCardAppointment);
                printCardDetail.setServiceList(PrintCardConverter.INSTANCE.toPrintCardServiceInfo(
                        petDetailList,
                        staffMap,
                        serviceOperationMapFuture.join(),
                        lodgingMapFuture.join(),
                        splitLodgingModels));
                printCardDetail.setHistoryList(
                        petHistoryAppointmentFuture.join().getOrDefault(petId, List.of()));

                result.add(printCardDetail);
            });
        });

        return result;
    }

    /**
     * @return <petId, List<PrintCardHistory>>
     */
    Map<Integer, List<PrintCardHistory>> batchGetPetPrintCardHistory(
            Integer businessId,
            List<Integer> customerIds,
            List<Integer> petIds,
            String dateForHistoryAppointment,
            Map<Integer, MoeStaffDto> staffMap) {

        if (CollectionUtils.isEmpty(customerIds) || CollectionUtils.isEmpty(petIds)) {
            return Map.of();
        }

        // history appointment
        List<Integer> appointmentIds = moeGroomingAppointmentMapper.getPetHistoryAppointment(
                businessId, customerIds.stream().toList(), petIds.stream().toList(), dateForHistoryAppointment);
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Map.of();
        }
        var appointmentMap = moeGroomingAppointmentMapper.selectByIdList(appointmentIds).stream()
                .collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity(), (k1, k2) -> (k1)));

        // appointment comments
        var appointmentComments = moeGroomingNoteService
                .getNoteListByGroomingIdListAndType(appointmentIds, GroomingAppointmentEnum.NOTE_COMMENT.intValue())
                .stream()
                .collect(Collectors.groupingBy(MoeGroomingNote::getGroomingId));

        // pet service detail & service operation
        var allPetDetails = moePetDetailService.queryAllPetDetailByGroomingIds(appointmentIds);
        var appontmentPetDetails =
                allPetDetails.stream().collect(Collectors.groupingBy(GroomingPetDetailDTO::getGroomingId));
        var petAppointments = allPetDetails.stream()
                .collect(Collectors.groupingBy(
                        GroomingPetDetailDTO::getPetId,
                        Collectors.mapping(GroomingPetDetailDTO::getGroomingId, Collectors.toList())));

        var serviceOperations = groomingServiceOperationService.getOperationMapByGroomingServiceIdList(
                Math.toIntExact(businessId),
                allPetDetails.stream()
                        .filter(GroomingPetDetailDTO::getEnableOperation)
                        .map(GroomingPetDetailDTO::getId)
                        .toList());

        Map<Integer, List<PrintCardHistory>> result = new HashMap<>();
        petAppointments.forEach((petId, groomingIds) -> {
            List<PrintCardHistory> printCardHistories = new ArrayList<>();
            groomingIds.stream()
                    .filter(appointmentMap::containsKey)
                    .distinct()
                    .sorted(compareByAppointmentDate(appointmentMap))
                    .limit(5)
                    .forEach(groomingId -> {
                        PrintCardHistory printCardHistory = PrintCardConverter.INSTANCE.toPrintCardHistory(
                                petId,
                                appointmentMap.get(groomingId),
                                appointmentComments,
                                appontmentPetDetails,
                                serviceOperations,
                                staffMap);
                        if (printCardHistory != null) {
                            printCardHistories.add(printCardHistory);
                        }
                    });
            result.put(petId, printCardHistories);
        });
        return result;
    }

    public List<StayCardDetail> getStayCardInfoByDate(
            String date,
            List<Integer> serviceItemList,
            Long businessId,
            Long companyId,
            List<AppointmentStatusEnum> statusList) {

        List<Integer> serviceTypeIncludeList = null;
        if (serviceItemList != null) {
            serviceTypeIncludeList = ServiceItemEnum.convertServiceItemListToBitValueList(serviceItemList);
        }

        List<MoeGroomingAppointment> appointmentList = moeGroomingAppointmentMapper.getAllGroomingByStartDate(
                Math.toIntExact(businessId), date, serviceTypeIncludeList, statusList);
        if (CollectionUtils.isEmpty(appointmentList)) {
            return Collections.emptyList();
        }

        return getStayCardInfo(businessId, companyId, appointmentList);
    }

    public List<StayCardDetail> getStayCardInfo(
            Long businessId, Long companyId, List<MoeGroomingAppointment> appointmentList) {
        if (CollectionUtils.isEmpty(appointmentList)) {
            return List.of();
        }
        Map<Integer, MoeGroomingAppointment> appointmentMap = appointmentList.stream()
                .collect(Collectors.toMap(MoeGroomingAppointment::getId, k -> k, (k1, k2) -> k1));
        List<Integer> appointmentIdList =
                appointmentList.stream().map(MoeGroomingAppointment::getId).toList();
        List<Long> appointmentIdLongList =
                appointmentIdList.stream().map(Integer::longValue).toList();
        List<Long> customerIds = appointmentList.stream()
                .filter(k -> k.getCustomerId() != null && k.getCustomerId() > 0)
                .map(k -> k.getCustomerId().longValue())
                .toList();

        var allPetDetails = moeGroomingPetDetailMapper.selectByAppointmentIdList(appointmentIdList);
        var petIds = PetDetailUtil.getPetIds(allPetDetails, null);

        // timezone name
        var timeZoneFuture = CompletableFuture.supplyAsync(
                () -> companyHelper.getCompanyTimeZoneName(companyId), ThreadPool.getSubmitExecutor());

        // staff
        var staffMapFuture = CompletableFuture.supplyAsync(
                () -> companyHelper.getBusinessStaff(businessId.intValue()), ThreadPool.getSubmitExecutor());

        // customer tag
        var customerTagMapFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getCustomerTagName(companyId), ThreadPool.getSubmitExecutor());

        // customer
        var customerMapFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getCustomerInfos(customerIds), ThreadPool.getSubmitExecutor());

        // customer notes
        var customerNotesFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getCustomerNotes(customerIds), ThreadPool.getSubmitExecutor());

        // agreement
        var customerAgreementListFuture = CompletableFuture.supplyAsync(
                () -> agreementHelper.batchGetCustomerRecentSignedAgreements(customerIds, businessId),
                ThreadPool.getSubmitExecutor());

        // appointment notes
        var appointmentNotesFuture = CompletableFuture.supplyAsync(
                () -> moeGroomingNoteService.getNoteListByGroomingIdList(appointmentIdList).stream()
                        .collect(Collectors.groupingBy(MoeGroomingNote::getGroomingId)),
                ThreadPool.getSubmitExecutor());

        // service name
        CompletableFuture<Map<Integer, String>> serviceNameFuture = CompletableFuture.supplyAsync(
                () -> offeringHelper.getServiceNameMap(PetDetailUtil.getServiceIds(allPetDetails)),
                ThreadPool.getSubmitExecutor());

        // pet
        var petMapFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getPetInfoMap(petIds), ThreadPool.getSubmitExecutor());

        // pet notes
        var petNotesFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getPetNotes(
                        petIds.stream().map(Integer::longValue).toList()),
                ThreadPool.getSubmitExecutor());

        // pet code
        var petCodeMapFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.listPetCodeBindings(
                        companyId, petIds.stream().map(Integer::longValue).toList()),
                ThreadPool.getSubmitExecutor());

        // belonging
        var appointmentBelongingsFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getAppointmentPetBelongs(appointmentIdList), ThreadPool.getSubmitExecutor());

        // boarding split lodgings
        var boardingSplitLodgingsFuture = CompletableFuture.supplyAsync(() -> boardingSplitLodgingService
                .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                        .addAllAppointmentIds(
                                appointmentIdList.stream().map(Long::valueOf).toList())
                        .build())
                .getBoardingSplitLodgingsList());

        // lodging
        CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
                lodgingMapFuture = boardingSplitLodgingsFuture.thenApplyAsync(
                        boardingSplitLodgings -> {
                            var lodgingIds = PetDetailUtil.getLodgingIds(allPetDetails);
                            var splitLodgingIds = boardingSplitLodgings.stream()
                                    .map(BoardingSplitLodgingModel::getLodgingId)
                                    .toList();
                            return offeringHelper.getLodgingMap(
                                    Stream.concat(lodgingIds.stream(), splitLodgingIds.stream())
                                            .distinct()
                                            .toList());
                        },
                        ThreadPool.getSubmitExecutor());

        // feeding
        var appointmentFeedingMapFuture = CompletableFuture.supplyAsync(
                () -> getAppointmentFeedings(appointmentIdLongList), ThreadPool.getSubmitExecutor());

        // medication
        var appointmentMedicationMapFuture = CompletableFuture.supplyAsync(
                () -> getAppointmentMedications(appointmentIdLongList), ThreadPool.getSubmitExecutor());

        // schedule setting
        var scheduleSettingMapFuture = CompletableFuture.supplyAsync(
                () -> getScheduleSettingMap(appointmentIdLongList), ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(
                        timeZoneFuture,
                        staffMapFuture,
                        customerTagMapFuture,
                        customerMapFuture,
                        customerNotesFuture,
                        customerAgreementListFuture,
                        appointmentNotesFuture,
                        serviceNameFuture,
                        petMapFuture,
                        petNotesFuture,
                        petCodeMapFuture,
                        appointmentBelongingsFuture,
                        boardingSplitLodgingsFuture,
                        lodgingMapFuture,
                        appointmentFeedingMapFuture,
                        appointmentMedicationMapFuture,
                        scheduleSettingMapFuture)
                .join();

        Map<Integer, List<MoeGroomingPetDetail>> appointmentPetDetailMap =
                allPetDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));

        var boardingSplitLodgingModels = boardingSplitLodgingsFuture.join();

        List<StayCardDetail> result = new ArrayList<>();
        appointmentMap.forEach((appointmentId, appointment) -> {
            BusinessCustomerInfoModel customer =
                    customerMapFuture.join().get(appointment.getCustomerId().longValue());
            if (customer == null) {
                return;
            }

            PrintCardCustomer printCardCustomer = PrintCardConverter.INSTANCE.toPrintCardCustomer(
                    customer,
                    customerTagMapFuture.join(),
                    customerNotesFuture.join(),
                    customerAgreementListFuture.join(),
                    timeZoneFuture.join());

            var petDetailMap = appointmentPetDetailMap.getOrDefault(appointmentId, List.of()).stream()
                    .collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId));
            var petFeedingInstructions =
                    appointmentFeedingMapFuture.join().getOrDefault(appointmentId.longValue(), List.of()).stream()
                            .collect(Collectors.groupingBy(AppointmentPetFeeding::getPetId));
            var petMedicationInstructions =
                    appointmentMedicationMapFuture.join().getOrDefault(appointmentId.longValue(), List.of()).stream()
                            .collect(Collectors.groupingBy(AppointmentPetMedication::getPetId));

            var petBelongings = appointmentBelongingsFuture.join().getOrDefault(appointmentId, List.of()).stream()
                    .collect(Collectors.groupingBy(PetBelongingDTO::getPetId));

            petDetailMap.forEach((petId, petDetailList) -> {
                CustomerPetPetCodeDTO pet = petMapFuture.join().get(petId);
                if (pet == null) {
                    return;
                }

                var splitLodgingModels = boardingSplitLodgingModels.stream()
                        .filter(boardingSplitLodgingModel ->
                                Objects.equals(boardingSplitLodgingModel.getAppointmentId(), appointmentId.longValue())
                                        && Objects.equals(boardingSplitLodgingModel.getPetId(), petId.longValue()))
                        .sorted(comparing(BoardingSplitLodgingModel::getStartDateTime, Timestamps.comparator()))
                        .toList();

                StayCardAppointment stayCardAppointment = PrintCardConverter.INSTANCE.toStayCardAppointment(
                        appointment,
                        petDetailList,
                        lodgingMapFuture.join(),
                        appointmentNotesFuture.join(),
                        splitLodgingModels,
                        serviceNameFuture.join());
                List<FeedingInstruction> feedings = PrintCardConverter.INSTANCE.toFeedingInstruction(
                        petId.longValue(),
                        petFeedingInstructions,
                        scheduleSettingMapFuture.join().getKey());
                List<MedicationInstruction> medications = PrintCardConverter.INSTANCE.toMedicationInstruction(
                        petId.longValue(),
                        petMedicationInstructions,
                        scheduleSettingMapFuture.join().getValue());

                Map<Long, List<PrintPetCodeBinding>> printCardPetCodeBindingsViews =
                        PrintCardConverter.INSTANCE.toPrintCardPetCodeBindingsViews(petCodeMapFuture.join());

                StayCardDetail stayCardDetail = new StayCardDetail();
                stayCardDetail.setGroomingId(appointmentId);
                stayCardDetail.setPetId(petId);
                stayCardDetail.setPet(PrintCardConverter.INSTANCE.toPrintCardPet(
                        pet,
                        petNotesFuture.join(),
                        printCardPetCodeBindingsViews.getOrDefault(petId.longValue(), List.of())));
                stayCardDetail.setCustomer(printCardCustomer);
                stayCardDetail.setAppointment(stayCardAppointment);
                stayCardDetail.setFeedingInstructions(feedings);
                stayCardDetail.setMedicationInstructions(medications);
                stayCardDetail.setPetBelongings(PrintCardConverter.INSTANCE.toPetBelonging(petId, petBelongings));
                stayCardDetail.setAddOns(getAddOnNameList(petDetailList, serviceNameFuture.join()));
                stayCardDetail.setExtraServiceDetails(getExtraServiceDetailList(
                        appointment, petDetailList, serviceNameFuture.join(), staffMapFuture.join()));

                result.add(stayCardDetail);
            });
        });
        return result;
    }

    /**
     * @param serviceItemList 过滤预约的主 service
     */
    public ActivityCardDetail getActivityCardInfoByDate(
            String date,
            List<Integer> serviceItemList,
            Long businessId,
            Long companyId,
            List<AppointmentStatusEnum> statusList) {
        var localDate = LocalDate.parse(date);

        ActivityCardDetail activityCardDetail = new ActivityCardDetail();

        List<Integer> serviceTypeIncludeList = null;
        if (!CollectionUtils.isEmpty(serviceItemList)) {
            serviceTypeIncludeList = ServiceItemEnum.convertServiceItemListToBitValueList(serviceItemList);
        }

        var appointments = moeGroomingAppointmentMapper.getAllGroomingByDateRange(
                Math.toIntExact(businessId), date, date, serviceTypeIncludeList, statusList);
        appointments = appointments.stream()
                .filter(k -> {
                    if (CollectionUtils.isEmpty(serviceItemList)) {
                        return true;
                    }
                    return serviceItemList.contains(ServiceItemEnum.getMainServiceItemType(k.getServiceTypeInclude())
                            .getServiceItem());
                })
                .toList();

        List<Integer> appointmentIdList =
                appointments.stream().map(MoeGroomingAppointment::getId).toList();
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return activityCardDetail;
        }
        var appointmentMap = appointments.stream()
                .collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity(), (k1, k2) -> (k1)));

        // customer info
        var customerIdList = appointments.stream()
                .map(MoeGroomingAppointment::getCustomerId)
                .map(Integer::longValue)
                .distinct()
                .toList();
        CompletableFuture<Map<Long, BusinessCustomerInfoModel>> customerInfoFuture = CompletableFuture.supplyAsync(
                () -> customerHelper.getCustomerInfos(customerIdList), ThreadPool.getSubmitExecutor());

        // service
        CompletableFuture<List<MoeGroomingPetDetail>> petDetailFuture = CompletableFuture.supplyAsync(
                () -> moeGroomingPetDetailMapper.selectByAppointmentIdList(appointmentIdList),
                ThreadPool.getSubmitExecutor());

        // staff
        CompletableFuture<Map<Integer, MoeStaffDto>> staffMapFuture = petDetailFuture.thenApplyAsync(
                petDetails -> companyHelper.getStaffInfoMap(
                        businessId.intValue(), PetDetailUtil.getStaffIds(petDetails, null)),
                ThreadPool.getSubmitExecutor());

        // pet
        CompletableFuture<Map<Integer, CustomerPetPetCodeDTO>> petMapFuture = petDetailFuture.thenApplyAsync(
                petDetails -> customerHelper.getPetInfoMap(PetDetailUtil.getPetIds(petDetails, null)),
                ThreadPool.getSubmitExecutor());

        // service name
        CompletableFuture<Map<Integer, String>> serviceNameFuture = petDetailFuture.thenApplyAsync(
                petDetails -> offeringHelper.getServiceNameMap(PetDetailUtil.getServiceIds(petDetails)),
                ThreadPool.getSubmitExecutor());

        // boarding split lodgings
        var boardingSplitLodgingsFuture = CompletableFuture.supplyAsync(() -> boardingSplitLodgingService
                .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                        .addAllAppointmentIds(
                                appointmentIdList.stream().map(Long::valueOf).toList())
                        .build())
                .getBoardingSplitLodgingsList());

        // lodging
        CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
                lodgingMapFuture = petDetailFuture.thenCombineAsync(
                        boardingSplitLodgingsFuture,
                        (petDetails, boardingSplitLodgings) -> {
                            var lodgingIds = PetDetailUtil.getLodgingIds(petDetails);
                            var splitLodgingIds = boardingSplitLodgings.stream()
                                    .map(BoardingSplitLodgingModel::getLodgingId)
                                    .toList();
                            return offeringHelper.getLodgingMap(
                                    Stream.concat(lodgingIds.stream(), splitLodgingIds.stream())
                                            .distinct()
                                            .toList());
                        },
                        ThreadPool.getSubmitExecutor());

        // feeding
        CompletableFuture<List<AppointmentPetFeeding>> petFeedingListFuture = CompletableFuture.supplyAsync(
                () -> {
                    AppointmentPetFeedingExample appointmentPetFeedingExample = new AppointmentPetFeedingExample();
                    appointmentPetFeedingExample
                            .createCriteria()
                            .andAppointmentIdIn(appointmentIdList.stream()
                                    .map(Long::valueOf)
                                    .toList());
                    return appointmentPetFeedingMapper.selectByExample(appointmentPetFeedingExample);
                },
                ThreadPool.getSubmitExecutor());

        // medication
        CompletableFuture<List<AppointmentPetMedication>> petMedicationListFuture = CompletableFuture.supplyAsync(
                () -> {
                    AppointmentPetMedicationExample appointmentPetMedicationExample =
                            new AppointmentPetMedicationExample();
                    appointmentPetMedicationExample
                            .createCriteria()
                            .andAppointmentIdIn(appointmentIdList.stream()
                                    .map(Long::valueOf)
                                    .toList());
                    return appointmentPetMedicationMapper.selectByExample(appointmentPetMedicationExample);
                },
                ThreadPool.getSubmitExecutor());

        // schedule setting
        CompletableFuture<
                        AbstractMap.SimpleEntry<
                                Map<Long, List<AppointmentPetScheduleSetting>>,
                                Map<Long, List<AppointmentPetScheduleSetting>>>>
                scheduleSettingMapFuture = CompletableFuture.supplyAsync(
                        () -> getScheduleSettingMap(
                                appointmentIdList.stream().map(Long::valueOf).toList()),
                        ThreadPool.getSubmitExecutor());

        // pet code
        var petCodeMapFuture = petDetailFuture.thenApplyAsync(
                petDetails -> customerHelper.listPetCodeBindings(
                        companyId,
                        PetDetailUtil.getPetIds(petDetails, null).stream()
                                .map(Integer::longValue)
                                .toList()),
                ThreadPool.getSubmitExecutor());

        // appointment notes
        var appointmentNotesFuture = CompletableFuture.supplyAsync(
                () -> moeGroomingNoteService.getNoteListByGroomingIdList(appointmentIdList).stream()
                        .collect(Collectors.groupingBy(MoeGroomingNote::getGroomingId)),
                ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(
                        staffMapFuture,
                        petDetailFuture,
                        petMapFuture,
                        lodgingMapFuture,
                        petFeedingListFuture,
                        petMedicationListFuture,
                        scheduleSettingMapFuture,
                        boardingSplitLodgingsFuture,
                        petCodeMapFuture,
                        appointmentNotesFuture)
                .join();

        List<MoeGroomingPetDetail> petDetails = petDetailFuture.join();
        appointmentServiceDetailService.fixServiceType(petDetails);
        Map<Integer, MoeGroomingPetDetail> petDetailMap = petDetails.stream()
                .collect(Collectors.toMap(MoeGroomingPetDetail::getId, Function.identity(), (a, b) -> b));
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap =
                PetDetailUtil.getPetServiceMap(petDetails.stream().toList());

        Map<Integer, MoeStaffDto> staffMap = staffMapFuture.join();
        Map<Integer, CustomerPetPetCodeDTO> petMap = petMapFuture.join();
        Map<Integer, String> serviceNameMap = serviceNameFuture.join();
        Map<Long, BusinessCustomerInfoModel> customerInfoModelMap = customerInfoFuture.join();
        Map<Long, LodgingUnitModel> longLodgingUnitMap = lodgingMapFuture.join().getKey();
        Map<Long, LodgingTypeModel> lodgingTypeMap = lodgingMapFuture.join().getValue();
        Map<Long, List<PrintPetCodeBinding>> printCardPetCodeBindingsViews =
                PrintCardConverter.INSTANCE.toPrintCardPetCodeBindingsViews(petCodeMapFuture.join());
        Map<Integer, List<MoeGroomingNote>> appointmentNotes = appointmentNotesFuture.join();
        var boardingSplitLodgings = boardingSplitLodgingsFuture.join();

        // feeding
        // 先按照 petId + petDetailId 分组，再按照 scheduleTime 分组
        CompletableFuture<List<ActivityCardFeedingColumn>> feedingColumnsFuture = CompletableFuture.supplyAsync(
                () -> petFeedingListFuture.join().stream()
                        .collect(Collectors.groupingBy(
                                feeding -> new AbstractMap.SimpleEntry<>(feeding.getPetId(), feeding.getPetDetailId())))
                        .entrySet()
                        .stream()
                        .map(entry -> {
                            Long petId = entry.getKey().getKey();
                            Long petDetailId = entry.getKey().getValue();
                            CustomerPetDetailDTO pet = petMap.get(petId.intValue());
                            var customerInfo = customerInfoModelMap.get(
                                    pet != null ? pet.getCustomerId().longValue() : null);
                            if (pet == null || customerInfo == null) {
                                return new ArrayList<ActivityCardFeedingColumn>();
                            }
                            MoeGroomingPetDetail petDetail = getPetDetail(petDetailMap, petDetailId, petId);

                            var splitLodgingModels =
                                    getBoardingSplitLodgingModels(petDetail, localDate, boardingSplitLodgings);

                            // map<scheduleTime, column>
                            Map<Integer, ActivityCardFeedingColumn> map = new HashMap<>();
                            entry.getValue().forEach(feeding -> scheduleSettingMapFuture
                                    .join()
                                    .getKey()
                                    .get(feeding.getId())
                                    .forEach(setting -> {
                                        Integer scheduleTime = setting.getScheduleTime();
                                        if (map.containsKey(scheduleTime)) {
                                            map.get(scheduleTime)
                                                    .instruction()
                                                    .add(PrintCardConverter.INSTANCE.toFeedingInstruction(
                                                            feeding, List.of()));
                                        } else {
                                            List<FeedingInstruction> list = new ArrayList<>();
                                            list.add(PrintCardConverter.INSTANCE.toFeedingInstruction(
                                                    feeding, List.of()));

                                            var lodgingId = splitLodgingModels.stream()
                                                    .filter(splitLodging -> isTimeInRange(
                                                            localDate,
                                                            scheduleTime,
                                                            TimestampConverter.INSTANCE.toLocalDateTime(
                                                                    splitLodging.getStartDateTime()),
                                                            TimestampConverter.INSTANCE.toLocalDateTime(
                                                                    splitLodging.getEndDateTime())))
                                                    .map(BoardingSplitLodgingModel::getLodgingId)
                                                    .findFirst()
                                                    .orElseGet(petDetail::getLodgingId);

                                            var lodgingInfo = getLodgingInfo(
                                                    lodgingMapFuture.join().getKey(),
                                                    lodgingMapFuture.join().getValue(),
                                                    lodgingId);
                                            ActivityCardFeedingColumn column = new ActivityCardFeedingColumn(
                                                    pet.getPetId(),
                                                    pet.getPetName(),
                                                    pet.getPetTypeId(),
                                                    pet.getBreed(),
                                                    PrintCardConverter.INSTANCE.toGenderName(pet.getGender()),
                                                    pet.getAvatarPath(),
                                                    pet.getWeight(),
                                                    printCardPetCodeBindingsViews.getOrDefault(petId, List.of()),
                                                    getServiceName(petDetail, serviceNameMap),
                                                    list,
                                                    scheduleTime,
                                                    customerInfo.getFirstName(),
                                                    customerInfo.getLastName(),
                                                    lodgingInfo.getLodgingTypeId(),
                                                    lodgingInfo.getLodgingUnitId(),
                                                    lodgingInfo.getLodgingUnitName(),
                                                    lodgingInfo);
                                            map.put(scheduleTime, column);
                                        }
                                    }));
                            return map.values();
                        })
                        .flatMap(Collection::stream)
                        .toList(),
                ThreadPool.getSubmitExecutor());

        // medication
        // 先按照 petId + petDetailId 分组，再按照 scheduleTime 分组
        CompletableFuture<List<ActivityCardMedicationColumn>> medicationColumnsFuture = CompletableFuture.supplyAsync(
                () -> petMedicationListFuture.join().stream()
                        .filter(medication -> isMedicationDateSelected(
                                date,
                                getPetDetail(petDetailMap, medication.getPetDetailId(), medication.getPetId()),
                                medication))
                        .collect(Collectors.groupingBy(medication ->
                                new AbstractMap.SimpleEntry<>(medication.getPetId(), medication.getPetDetailId())))
                        .entrySet()
                        .stream()
                        .map(entry -> {
                            Long petId = entry.getKey().getKey();
                            Long petDetailId = entry.getKey().getValue();
                            CustomerPetDetailDTO pet = petMap.get(petId.intValue());
                            var customerInfo = customerInfoModelMap.get(
                                    pet != null ? pet.getCustomerId().longValue() : null);
                            if (pet == null || customerInfo == null) {
                                return new ArrayList<ActivityCardMedicationColumn>();
                            }
                            MoeGroomingPetDetail petDetail = getPetDetail(petDetailMap, petDetailId, petId);

                            var splitLodgingModels =
                                    getBoardingSplitLodgingModels(petDetail, localDate, boardingSplitLodgings);

                            // map<scheduleTime, column>
                            Map<Integer, ActivityCardMedicationColumn> map = new HashMap<>();
                            entry.getValue().forEach(medication -> scheduleSettingMapFuture
                                    .join()
                                    .getValue()
                                    .get(medication.getId())
                                    .forEach(setting -> {
                                        Integer scheduleTime = setting.getScheduleTime();
                                        if (map.containsKey(scheduleTime)) {
                                            map.get(scheduleTime)
                                                    .instruction()
                                                    .add(PrintCardConverter.INSTANCE.toMedicationInstruction(
                                                            medication, List.of()));
                                        } else {
                                            List<MedicationInstruction> list = new ArrayList<>();
                                            list.add(PrintCardConverter.INSTANCE.toMedicationInstruction(
                                                    medication, List.of()));

                                            var lodgingId = splitLodgingModels.stream()
                                                    .filter(splitLodging -> isTimeInRange(
                                                            localDate,
                                                            scheduleTime,
                                                            TimestampConverter.INSTANCE.toLocalDateTime(
                                                                    splitLodging.getStartDateTime()),
                                                            TimestampConverter.INSTANCE.toLocalDateTime(
                                                                    splitLodging.getEndDateTime())))
                                                    .map(BoardingSplitLodgingModel::getLodgingId)
                                                    .findFirst()
                                                    .orElseGet(petDetail::getLodgingId);

                                            var lodgingInfo = getLodgingInfo(
                                                    lodgingMapFuture.join().getKey(),
                                                    lodgingMapFuture.join().getValue(),
                                                    lodgingId);
                                            ActivityCardMedicationColumn column = new ActivityCardMedicationColumn(
                                                    pet.getPetId(),
                                                    pet.getPetName(),
                                                    pet.getPetTypeId(),
                                                    pet.getBreed(),
                                                    PrintCardConverter.INSTANCE.toGenderName(pet.getGender()),
                                                    pet.getAvatarPath(),
                                                    pet.getWeight(),
                                                    printCardPetCodeBindingsViews.getOrDefault(petId, List.of()),
                                                    getServiceName(petDetail, serviceNameMap),
                                                    list,
                                                    scheduleTime,
                                                    customerInfo.getFirstName(),
                                                    customerInfo.getLastName(),
                                                    lodgingInfo.getLodgingTypeId(),
                                                    lodgingInfo.getLodgingUnitId(),
                                                    lodgingInfo.getLodgingUnitName(),
                                                    lodgingInfo);
                                            map.put(scheduleTime, column);
                                        }
                                    }));
                            return map.values();
                        })
                        .flatMap(Collection::stream)
                        .toList(),
                ThreadPool.getSubmitExecutor());

        List<ActivityCardAddOnColumn> addOns = petDetails.stream()
                .filter(k -> (Objects.equals(ServiceType.ADDON_VALUE, k.getServiceType())))
                .filter(k -> petMap.containsKey(k.getPetId()))
                .filter(k ->
                        PetDetailUtil.getServiceActualDates(k, petServiceMap).contains(date))
                .sorted(sortByCheckInTimeAndServiceName(appointmentMap, serviceNameMap))
                .map(k -> {
                    CustomerPetDetailDTO pet = petMap.get(k.getPetId());
                    var customerInfo = customerInfoModelMap.get(
                            pet != null ? pet.getCustomerId().longValue() : null);
                    if (pet == null || customerInfo == null) {
                        return null;
                    }
                    var petDetail = petDetails.stream()
                            .filter(detail -> detail.getServiceId()
                                            .equals(k.getAssociatedServiceId().intValue())
                                    && detail.getGroomingId().equals(k.getGroomingId())
                                    && detail.getPetId().equals(k.getPetId()))
                            .findFirst()
                            .orElse(null);

                    String associatedServiceName = getServiceName(petDetail, serviceNameMap);

                    var splitLodgingModels = getBoardingSplitLodgingModels(petDetail, localDate, boardingSplitLodgings);

                    var lodgingInfos = getLodgingInfos(
                            lodgingMapFuture.join().getKey(),
                            lodgingMapFuture.join().getValue(),
                            splitLodgingModels.stream()
                                    .map(BoardingSplitLodgingModel::getLodgingId)
                                    .toList());
                    var lodgingInfo = CollectionUtils.lastElement(lodgingInfos);
                    if (CollectionUtils.isEmpty(splitLodgingModels)) {
                        lodgingInfo = getLodgingInfo(
                                lodgingMapFuture.join().getKey(),
                                lodgingMapFuture.join().getValue(),
                                petDetail != null ? petDetail.getLodgingId() : 0);
                        lodgingInfos = List.of(lodgingInfo);
                    }

                    return new ActivityCardAddOnColumn(
                            pet.getPetId(),
                            pet.getPetName(),
                            pet.getPetTypeId(),
                            pet.getBreed(),
                            PrintCardConverter.INSTANCE.toGenderName(pet.getGender()),
                            pet.getAvatarPath(),
                            pet.getWeight(),
                            printCardPetCodeBindingsViews.getOrDefault(
                                    pet.getPetId().longValue(), List.of()),
                            serviceNameMap.getOrDefault(k.getServiceId(), ""),
                            k.getServiceId().longValue(),
                            associatedServiceName,
                            Math.toIntExact(k.getStartTime()),
                            PrintCardConverter.INSTANCE.toStaffName(staffMap.get(k.getStaffId())),
                            k.getQuantityPerDay(),
                            customerInfo.getFirstName(),
                            customerInfo.getLastName(),
                            Objects.nonNull(lodgingInfo) ? lodgingInfo.getLodgingTypeId() : null,
                            Objects.nonNull(lodgingInfo) ? lodgingInfo.getLodgingUnitId() : null,
                            Objects.nonNull(lodgingInfo) ? lodgingInfo.getLodgingUnitName() : "",
                            lodgingInfos,
                            PrintCardConverter.INSTANCE.toCommentNote(appointmentNotes.get(k.getGroomingId())));
                })
                .filter(Objects::nonNull)
                .toList();

        List<LodgingTypeDTO> lodgingTypeDTOs = lodgingTypeMap.values().stream()
                .sorted(comparing(LodgingTypeModel::getSort))
                .map(LodgingTypeConverter.INSTANCE::toLodgingTypeDTO)
                .toList();
        List<LodgingUnitDTO> lodgingUnitDTOs = longLodgingUnitMap.values().stream()
                .sorted(comparing(LodgingUnitModel::getSort))
                .map(LodgingUnitConverter.INSTANCE::toLodgingUnitDTO)
                .toList();

        activityCardDetail.setFeedingInstructions(feedingColumnsFuture.join());
        activityCardDetail.setMedicationInstructions(medicationColumnsFuture.join());
        activityCardDetail.setAddOns(addOns);
        activityCardDetail.setLodgingTypes(lodgingTypeDTOs);
        activityCardDetail.setLodgingUnits(lodgingUnitDTOs);
        return activityCardDetail;
    }

    @NotNull
    private static List<BoardingSplitLodgingModel> getBoardingSplitLodgingModels(
            final MoeGroomingPetDetail petDetail,
            final LocalDate localDate,
            final List<BoardingSplitLodgingModel> boardingSplitLodgings) {
        if (Objects.isNull(petDetail)) {
            return List.of();
        }
        return boardingSplitLodgings.stream()
                .filter(boardingSplitLodging -> Objects.equals(
                                boardingSplitLodging.getPetDetailId(),
                                petDetail.getId().longValue())
                        && isDateInRange(
                                localDate,
                                TimestampConverter.INSTANCE.toLocalDateTime(boardingSplitLodging.getStartDateTime()),
                                TimestampConverter.INSTANCE.toLocalDateTime(boardingSplitLodging.getEndDateTime())))
                .toList();
    }

    private static MoeGroomingPetDetail getPetDetail(
            Map<Integer, MoeGroomingPetDetail> petDetailMap, Long petDetailId, Long petId) {
        MoeGroomingPetDetail petDetail = petDetailMap.get(Math.toIntExact(petDetailId));
        if (Objects.isNull(petDetail)) {
            petDetail = PetDetailUtil.getMainService(petDetailMap.values().stream()
                    .filter(detail -> Objects.equals(detail.getPetId(), petId.intValue()))
                    .toList());
        }
        return petDetail;
    }

    private static String getServiceName(MoeGroomingPetDetail petDetail, Map<Integer, String> serviceNameMap) {
        if (Objects.isNull(petDetail)) {
            return "";
        }
        return serviceNameMap.getOrDefault(petDetail.getServiceId(), "");
    }

    private static LodgingInfo getLodgingInfo(
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            Long lodgingUnitId) {
        LodgingInfo lodgingInfo = new LodgingInfo();
        if (lodgingUnitMap.containsKey(lodgingUnitId)) {
            var lodgingUnitModel = lodgingUnitMap.get(lodgingUnitId);
            lodgingInfo.setLodgingUnitId(lodgingUnitId);
            lodgingInfo.setLodgingUnitName(lodgingUnitModel.getName());
            lodgingInfo.setSort(lodgingUnitModel.getSort());
            if (lodgingTypeMap.containsKey(lodgingUnitModel.getLodgingTypeId())) {
                var lodgingTypeModel = lodgingTypeMap.get(lodgingUnitModel.getLodgingTypeId());
                lodgingInfo.setLodgingTypeId(lodgingTypeModel.getId());
                lodgingInfo.setLodgingTypeName(lodgingTypeModel.getName());
            }
        }
        return lodgingInfo;
    }

    private static List<LodgingInfo> getLodgingInfos(
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            List<Long> lodgingUnitIds) {
        return lodgingUnitIds.stream()
                .map(lodgingUnitId -> getLodgingInfo(lodgingUnitMap, lodgingTypeMap, lodgingUnitId))
                .toList();
    }

    Map<Long, List<AppointmentPetFeeding>> getAppointmentFeedings(List<Long> appointmentIds) {
        appointmentIds = appointmentIds.stream()
                .filter(k -> k != null && k > 0)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Map.of();
        }
        AppointmentPetFeedingExample appointmentPetFeedingExample = new AppointmentPetFeedingExample();
        appointmentPetFeedingExample.createCriteria().andAppointmentIdIn(appointmentIds);
        List<AppointmentPetFeeding> feedings =
                appointmentPetFeedingMapper.selectByExample(appointmentPetFeedingExample);
        return feedings.stream().collect(Collectors.groupingBy(AppointmentPetFeeding::getAppointmentId));
    }

    Map<Long, List<AppointmentPetMedication>> getAppointmentMedications(List<Long> appointmentIds) {
        appointmentIds = appointmentIds.stream()
                .filter(k -> k != null && k > 0)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Map.of();
        }
        AppointmentPetMedicationExample appointmentPetMedicationExample = new AppointmentPetMedicationExample();
        appointmentPetMedicationExample.createCriteria().andAppointmentIdIn(appointmentIds);
        List<AppointmentPetMedication> medications =
                appointmentPetMedicationMapper.selectByExample(appointmentPetMedicationExample);
        return medications.stream().collect(Collectors.groupingBy(AppointmentPetMedication::getAppointmentId));
    }

    AbstractMap.SimpleEntry<
                    Map<Long, List<AppointmentPetScheduleSetting>>, Map<Long, List<AppointmentPetScheduleSetting>>>
            getScheduleSettingMap(List<Long> appointmentIds) {
        appointmentIds = appointmentIds.stream()
                .filter(k -> k != null && k > 0)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return new AbstractMap.SimpleEntry<>(Map.of(), Map.of());
        }
        AppointmentPetScheduleSettingExample appointmentPetScheduleSettingExample =
                new AppointmentPetScheduleSettingExample();
        appointmentPetScheduleSettingExample.createCriteria().andAppointmentIdIn(appointmentIds);
        List<AppointmentPetScheduleSetting> appointmentPetScheduleSettings =
                appointmentPetScheduleSettingMapper.selectByExample(appointmentPetScheduleSettingExample);
        Map<Long, List<AppointmentPetScheduleSetting>> feedingMap = appointmentPetScheduleSettings.stream()
                .filter(setting -> setting.getScheduleType() == 1)
                .collect(Collectors.groupingBy(AppointmentPetScheduleSetting::getScheduleId));
        Map<Long, List<AppointmentPetScheduleSetting>> medicationMap = appointmentPetScheduleSettings.stream()
                .filter(setting -> setting.getScheduleType() == 2)
                .collect(Collectors.groupingBy(AppointmentPetScheduleSetting::getScheduleId));
        return new AbstractMap.SimpleEntry<>(feedingMap, medicationMap);
    }

    private static List<String> getAddOnNameList(
            List<MoeGroomingPetDetail> moeGroomingPetDetails, Map<Integer, String> serviceNameMap) {
        return moeGroomingPetDetails.stream()
                .filter(petDetail -> Objects.equals(petDetail.getServiceType(), 2))
                .map(k -> serviceNameMap.getOrDefault(k.getServiceId(), ""))
                .toList();
    }

    private static List<ExtraServiceDetail> getExtraServiceDetailList(
            MoeGroomingAppointment appointment,
            List<MoeGroomingPetDetail> moeGroomingPetDetails,
            Map<Integer, String> serviceNameMap,
            Map<Integer, MoeStaffDto> staffMap) {
        // get main service
        ServiceItemEnum mainServiceItemType =
                ServiceItemEnum.getMainServiceItemType(appointment.getServiceTypeInclude());
        var petServiceMap = PetDetailUtil.getPetServiceMap(moeGroomingPetDetails);

        return moeGroomingPetDetails.stream()
                .filter(petDetail -> {
                    boolean isMainService = Objects.equals(petDetail.getServiceType(), ServiceType.SERVICE_VALUE)
                            && Objects.equals(petDetail.getServiceItemType(), mainServiceItemType.getServiceItem());
                    return !isMainService;
                })
                .map(petDetail -> ExtraServiceDetail.builder()
                        .serviceName(serviceNameMap.getOrDefault(petDetail.getServiceId(), ""))
                        .serviceType(petDetail.getServiceType())
                        .quantityPerDay(petDetail.getQuantityPerDay())
                        .requireDedicatedStaff(petDetail.getStaffId() != null && petDetail.getStaffId() > 0)
                        .staffName(PrintCardConverter.INSTANCE.toStaffName(staffMap.get(petDetail.getStaffId())))
                        .specificDates(PetDetailUtil.getServiceDates(
                                petDetail, petServiceMap)) // TODO：Specific Date 应该直接返回数据库中字段，不应该返回计算值
                        .startTime(petDetail.getStartTime())
                        .endTime(petDetail.getEndTime())
                        .dateType(petDetail.getDateType())
                        .actualDates(PetDetailUtil.getServiceActualDates(petDetail, petServiceMap))
                        .build())
                .toList();
    }

    /**
     * 先按 pet check in 时间排序；后按 service name 排序
     */
    static Comparator<MoeGroomingPetDetail> sortByCheckInTimeAndServiceName(
            Map<Integer, MoeGroomingAppointment> appointmentMap, Map<Integer, String> serviceNameMap) {
        return comparing((MoeGroomingPetDetail k) -> getCheckInTime(appointmentMap, k))
                .thenComparing(petDetail -> serviceNameMap.getOrDefault(petDetail.getServiceId(), ""));
    }

    static Long getCheckInTime(Map<Integer, MoeGroomingAppointment> appointmentMap, MoeGroomingPetDetail petDetail) {
        return Optional.ofNullable(appointmentMap.get(petDetail.getGroomingId()))
                .map(MoeGroomingAppointment::getCheckInTime)
                .filter(checkInTime -> checkInTime > 0L)
                .orElse(Long.MAX_VALUE);
    }

    static Comparator<Integer> compareByAppointmentDate(Map<Integer, MoeGroomingAppointment> appointmentMap) {
        return comparing((Integer appointmentId) ->
                        appointmentMap.get(appointmentId).getAppointmentDate())
                .thenComparing(
                        appointmentId -> appointmentMap.get(appointmentId).getAppointmentStartTime())
                .reversed();
    }

    private static boolean isDateInRange(
            LocalDate serviceDate, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return !serviceDate.isBefore(startDateTime.toLocalDate()) && !serviceDate.isAfter(endDateTime.toLocalDate());
    }

    private static boolean isTimeInRange(
            LocalDate serviceDate, Number minutes, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        LocalDateTime serviceDateTime =
                serviceDate.atTime(Math.floorDiv(minutes.intValue(), 60), Math.floorMod(minutes.intValue(), 60), 0);
        return !serviceDateTime.isBefore(startDateTime) && !serviceDateTime.isAfter(endDateTime);
    }

    static List<Long> getLodgingIds(List<GroomingPetDetailDTO> petDetails) {
        List<Long> lodgingIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(petDetails)) {
            return lodgingIds;
        }
        lodgingIds.addAll(petDetails.stream()
                .map(GroomingPetDetailDTO::getLodgingId)
                .filter(Objects::nonNull)
                .toList());
        return lodgingIds.stream().filter(k -> k > 0).distinct().toList();
    }

    private static boolean isMedicationDateSelected(
            String currentDate, MoeGroomingPetDetail petDetail, AppointmentPetMedication appointmentPetMedication) {
        if (Objects.isNull(petDetail) || Objects.isNull(appointmentPetMedication)) return false;

        List<String> petDetailDateRange =
                DateUtil.generateAllDatesBetween(petDetail.getStartDate(), petDetail.getEndDate());

        List<String> specificDates =
                switch (appointmentPetMedication.getDateType()) {
                    case EVERYDAY_EXCEPT_CHECKOUT_DATE -> petDetailDateRange.subList(0, petDetailDateRange.size() - 1);
                    case SPECIFIC_DATE -> JsonUtil.toList(appointmentPetMedication.getSpecificDates(), String.class);
                    default -> petDetailDateRange;
                };

        return specificDates.contains(currentDate);
    }
}
