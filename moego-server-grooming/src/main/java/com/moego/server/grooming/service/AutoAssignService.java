package com.moego.server.grooming.service;

import com.moego.server.grooming.mapper.AutoAssignMapper;
import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.AutoAssignExample;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AutoAssignService {

    private final AutoAssignMapper autoAssignMapper;

    /**
     * Get auto assign by appointment id.
     *
     * @param appointmentId appointment id
     * @return {@link AutoAssign}, null if not found
     */
    @Nullable
    public AutoAssign getAutoAssign(Integer appointmentId) {
        AutoAssignExample example = new AutoAssignExample();
        example.createCriteria().andAppointmentIdEqualTo(appointmentId);
        List<AutoAssign> autoAssigns = autoAssignMapper.selectByExample(example);
        return !autoAssigns.isEmpty() ? autoAssigns.get(0) : null;
    }

    /**
     * List auto assign by appointment ids.
     *
     * @param appointmentIds appointment ids
     * @return {@link AutoAssign}s, empty list if not found
     */
    public List<AutoAssign> listAutoAssign(Collection<Integer> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        AutoAssignExample example = new AutoAssignExample();
        example.createCriteria().andAppointmentIdIn(List.copyOf(appointmentIds));
        return autoAssignMapper.selectByExample(example);
    }
}
