package com.moego.server.grooming.service.utils;

import static com.moego.common.enums.groomingreport.GroomingReportCategoryEnum.CATEGORY_CUSTOMIZED_FEEDBACK;
import static com.moego.common.enums.groomingreport.GroomingReportConst.CAT_DEFAULT_BODY_VIEW_LEFT;
import static com.moego.common.enums.groomingreport.GroomingReportConst.CAT_DEFAULT_BODY_VIEW_RIGHT;
import static com.moego.common.enums.groomingreport.GroomingReportConst.DOG_DEFAULT_BODY_VIEW_LEFT;
import static com.moego.common.enums.groomingreport.GroomingReportConst.DOG_DEFAULT_BODY_VIEW_RIGHT;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_ADDON_DURATION;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_ADDON_NAME;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_APPOINTMENT_END_TIME;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_APPOINTMENT_START_TIME;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_BODY_VIEW_LEFT;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_BODY_VIEW_RIGHT;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_COMMENT;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_CUSTOMER_FREQUENCY;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_PET_AVATAR;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_PET_BREED;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_PET_NAME;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_PET_WEIGHT;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_SERVICE_DURATION;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_SERVICE_NAME;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_SHOWCASE_AFTER;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_SHOWCASE_BEFORE;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_STAFF_FIRST_NAME;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_STAFF_LAST_NAME;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.getDaysAfter;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.getFrequencyText;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.getGenderText;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.getSendingMethodListFromDB;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.getWeightText;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.toDate;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.toTimestamp;

import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.BusinessCustomerConst;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.PetTypeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.groomingreport.GroomingReportCategoryEnum;
import com.moego.common.enums.groomingreport.GroomingReportConst;
import com.moego.common.enums.groomingreport.GroomingReportQuestionTypeEnum;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.common.utils.BusinessUtil;
import com.moego.common.utils.DateUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.AppointmentServiceInfo;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.groomingreport.BodyViewUrl;
import com.moego.server.grooming.dto.groomingreport.GroomingRecommendation;
import com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportQuestionDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportTemplateDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion;
import com.moego.server.grooming.mapperbean.MoeGroomingReportSetting;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import com.moego.server.grooming.params.MoeBusinessBookOnlineDto;
import com.moego.server.grooming.params.groomingreport.GroomingReportContentParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportInfoParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams;
import com.moego.server.grooming.service.dto.groomingreport.GroomingReportExtraJson;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import com.moego.server.message.dto.ReviewBoosterDTO;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
public class GroomingReportConverter {

    /* Convert 相关方法 */
    public static GroomingReportSettingDTO convertSettingDTO(MoeGroomingReportSetting setting) {
        GroomingReportSettingDTO dto = new GroomingReportSettingDTO();
        BeanUtils.copyProperties(setting, dto);
        dto.setSendingMethodList(getSendingMethodListFromDB(setting.getSendingMethod()));
        dto.setTemplatePublishTime(toTimestamp(setting.getTemplatePublishTime()));
        dto.setCreateTime(toTimestamp(setting.getCreateTime()));
        dto.setUpdateTime(toTimestamp(setting.getUpdateTime()));
        return dto;
    }

    /**
     * 合并 template 和 question 列表成一个对象
     *
     * @param template            template
     * @param questions           question 列表
     * @param reviewBoosterConfig review booster 配置
     * @return GroomingReportTemplateDTO
     */
    public static GroomingReportTemplateDTO convertTemplateAndQuestionsDTO(
            MoeGroomingReportTemplate template,
            List<MoeGroomingReportQuestion> questions,
            ReviewBoosterDTO reviewBoosterConfig) {
        if (template == null) {
            return null;
        }
        GroomingReportTemplateDTO dto = new GroomingReportTemplateDTO();
        BeanUtils.copyProperties(template, dto);
        dto.setCreateTime(toTimestamp(template.getCreateTime()));
        dto.setUpdateTime(toTimestamp(template.getUpdateTime()));
        dto.setLastPublishTime(toTimestamp(template.getLastPublishTime()));
        if (reviewBoosterConfig != null) {
            dto.setYelpReviewLink(reviewBoosterConfig.getPositiveYelp());
            dto.setGoogleReviewLink(reviewBoosterConfig.getPositiveGoogle());
            dto.setFacebookReviewLink(reviewBoosterConfig.getPositiveFacebook());
        }

        List<GroomingReportQuestionDTO> feedbacks = new ArrayList<>();
        List<GroomingReportQuestionDTO> petConditions = new ArrayList<>();
        for (MoeGroomingReportQuestion question : questions) {
            if (question.getCategory() == GroomingReportCategoryEnum.CATEGORY_FEEDBACK.getType()
                    || question.getCategory() == CATEGORY_CUSTOMIZED_FEEDBACK.getType()) {
                feedbacks.add(convertQuestionDTO(question));
            } else if (question.getCategory() == GroomingReportCategoryEnum.CATEGORY_PET_CONDITION.getType()) {
                petConditions.add(convertQuestionDTO(question));
            }
        }
        feedbacks.sort(Comparator.comparing(
                        GroomingReportQuestionDTO::getCategory, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(
                        Comparator.comparing(GroomingReportQuestionDTO::getSort).reversed()));
        petConditions.sort(
                Comparator.comparing(GroomingReportQuestionDTO::getSort).reversed());
        dto.setQuestions(new GroomingReportTemplateDTO.TemplateQuestions(feedbacks, petConditions));
        return dto;
    }

    public static MoeGroomingReportQuestion convertParamsToQuestion(
            GroomingReportQuestionParams questionParams, Byte category) {
        MoeGroomingReportQuestion question = new MoeGroomingReportQuestion();
        BeanUtils.copyProperties(questionParams, question);
        question.setCategory(category);
        question.setIsDefault(null); // 不更新 isDefault 字段
        GroomingReportExtraJson extraJson = new GroomingReportExtraJson();
        BeanUtils.copyProperties(questionParams, extraJson);
        question.setExtraJson(JsonUtil.toJson(extraJson));
        return question;
    }

    public static GroomingReportQuestionDTO convertQuestionDTO(MoeGroomingReportQuestion question) {
        GroomingReportQuestionDTO questionDTO = new GroomingReportQuestionDTO();
        BeanUtils.copyProperties(question, questionDTO);
        questionDTO.setCreateTime(toTimestamp(question.getCreateTime()));
        questionDTO.setUpdateTime(toTimestamp(question.getUpdateTime()));

        if (Objects.nonNull(question.getExtraJson())) {
            // copy extraJson 的字段到 dto
            GroomingReportExtraJson extraJson = JsonUtil.toBean(question.getExtraJson(), GroomingReportExtraJson.class);
            BeanUtils.copyProperties(extraJson, questionDTO);
        }
        return questionDTO;
    }

    public static GroomingReportRecordDTO convertGroomingReportRecordDTO(
            MoeGroomingReport report,
            CustomerPetDetailDTO pet,
            GroomingReportSettingDTO setting,
            List<GroomingReportSendLogDTO> sendLogs) {
        GroomingReportRecordDTO recordDTO = new GroomingReportRecordDTO();
        recordDTO.setPetId(pet.getPetId());
        recordDTO.setPetName(pet.getPetName());
        recordDTO.setPetTypeId(pet.getPetTypeId());
        recordDTO.setReportId(0);
        recordDTO.setTemplatePublishTime(0L);
        recordDTO.setReportStatus(GroomingReportStatusEnum.created.name());
        recordDTO.setSendingType(setting.getSendingType());
        recordDTO.setSendingMethodList(setting.getSendingMethodList());
        recordDTO.setLastSendingMethodList(List.of());
        if (report == null) {
            return recordDTO;
        }
        recordDTO.setReportId(report.getId());
        recordDTO.setTemplatePublishTime(toTimestamp(report.getTemplatePublishTime()));
        recordDTO.setReportStatus(report.getStatus());

        if (!CollectionUtils.isEmpty(sendLogs)) {
            GroomingReportRecordDTO.GroomingReportSendLog smsSendLog = null;
            GroomingReportRecordDTO.GroomingReportSendLog emailSendLog = null;
            List<Byte> lastSendingMethodList = new ArrayList<>();
            for (GroomingReportSendLogDTO log : sendLogs) {
                GroomingReportRecordDTO.GroomingReportSendLog sendLog =
                        new GroomingReportRecordDTO.GroomingReportSendLog(
                                log.getStatus(), log.getErrorCode(), log.getErrorMsg());
                if (Objects.equals(log.getSendingMethod(), GroomingReportConst.SEND_BY_SMS)) {
                    smsSendLog = sendLog;
                } else if (Objects.equals(log.getSendingMethod(), GroomingReportConst.SEND_BY_EMAIL)) {
                    emailSendLog = sendLog;
                }
                lastSendingMethodList.add(log.getSendingMethod());
            }

            // 设置上次的发送方式、发送记录
            recordDTO.setLastSendingMethodList(lastSendingMethodList);
            recordDTO.setSentRecords(new GroomingReportRecordDTO.GroomingReportSendRecord(smsSendLog, emailSendLog));
        }
        return recordDTO;
    }

    public static GroomingReportInfoDTO convertGroomingReportInfoDTO(
            MoeGroomingReport groomingReport, String appointmentDate, MoeBusinessDto business) {
        GroomingReportInfoDTO reportInfo = new GroomingReportInfoDTO();
        BeanUtils.copyProperties(groomingReport, reportInfo);
        reportInfo.setSubmittedTime(toTimestamp(groomingReport.getSubmittedTime()));
        reportInfo.setCreateTime(toTimestamp(groomingReport.getCreateTime()));
        reportInfo.setUpdateTime(toTimestamp(groomingReport.getUpdateTime()));
        reportInfo.setTemplatePublishTime(toTimestamp(groomingReport.getTemplatePublishTime()));
        reportInfo.setTemplate(
                JsonUtil.toBean(groomingReport.getTemplateJson(), GroomingReportInfoDTO.GroomingReportTemplate.class));
        reportInfo.setContent(
                JsonUtil.toBean(groomingReport.getContentJson(), GroomingReportInfoDTO.GroomingReportContent.class));
        // 初始化
        initGroomingReportInfo(reportInfo, appointmentDate, business);
        return reportInfo;
    }

    private static List<GroomingReportInfoDTO.GroomingReportQuestion> convertQuestionListForPreview(
            List<GroomingReportQuestionParams> questions) {
        return questions.stream()
                .filter(Objects::nonNull)
                .map(params -> {
                    GroomingReportInfoDTO.GroomingReportQuestion question =
                            new GroomingReportInfoDTO.GroomingReportQuestion();
                    BeanUtils.copyProperties(params, question);
                    if (question.getShow() == null) {
                        question.setShow(question.getRequired());
                    }
                    return question;
                })
                .toList();
    }

    public static List<GroomingReportInfoDTO.GroomingReportQuestion> convertGroomingReportQuestionList(
            List<GroomingReportQuestionDTO> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return List.of();
        }

        return convertQuestionList(questions);
    }

    private static List<GroomingReportInfoDTO.GroomingReportQuestion> convertQuestionList(
            List<GroomingReportQuestionDTO> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return List.of();
        }

        return questions.stream()
                .map(questionDTO -> {
                    GroomingReportInfoDTO.GroomingReportQuestion question =
                            new GroomingReportInfoDTO.GroomingReportQuestion();
                    BeanUtils.copyProperties(questionDTO, question);
                    return question;
                })
                .toList();
    }

    public static GroomingReportInfoDTO.GroomingReportContent convertParamsToBeanForPreview(
            Integer petTypeId, GroomingReportContentParams contentParams) {
        if (contentParams == null) {
            return null;
        }
        GroomingReportInfoDTO.GroomingReportContent content = new GroomingReportInfoDTO.GroomingReportContent();
        BeanUtils.copyProperties(contentParams, content);
        if (!CollectionUtils.isEmpty(contentParams.getFeedbacks())) {
            content.setFeedbacks(convertQuestionListForPreview(contentParams.getFeedbacks()));
            initQuestionList(content.getFeedbacks(), petTypeId);
            // additional note question 初始化
            initAdditionalNoteQuestion(content.getFeedbacks());
        }
        if (!CollectionUtils.isEmpty(contentParams.getPetConditions())) {
            content.setPetConditions(convertQuestionListForPreview(contentParams.getPetConditions()));
            initQuestionList(content.getPetConditions(), petTypeId);
        }
        return content;
    }

    /* 构建对象 */
    public static GroomingReportSummaryInfoDTO.BusinessInfo buildBusinessInfo(
            MoeBusinessDto business, MoeBusinessBookOnlineDto obSettings) {
        GroomingReportSummaryInfoDTO.BusinessInfo businessInfo = new GroomingReportSummaryInfoDTO.BusinessInfo();
        BeanUtils.copyProperties(business, businessInfo);
        businessInfo.setBusinessMode(BusinessUtil.getBusinessMode(business.getAppType()));
        if (obSettings != null) {
            businessInfo.setBookOnlineName(obSettings.getBookOnlineName());
            businessInfo.setBookOnlineEnable(BooleanEnum.VALUE_TRUE.equals(obSettings.getIsEnable()));
        }
        return businessInfo;
    }

    public static GroomingReportSummaryInfoDTO.PetInfo buildPetInfo(MoeBusinessDto business, CustomerPetDetailDTO pet) {
        if (pet == null) {
            return null;
        }
        // pet info
        GroomingReportSummaryInfoDTO.PetInfo petInfo = new GroomingReportSummaryInfoDTO.PetInfo();
        BeanUtils.copyProperties(pet, petInfo);
        petInfo.setGenderText(getGenderText(pet.getGender()));
        petInfo.setWeightWithUnit(getWeightText(pet.getWeight(), business.getUnitOfWeight()));
        return petInfo;
    }

    public static GroomingReportInfoDTO buildReportInfo(
            MoeGroomingReport groomingReport,
            AppointmentWithPetDetailsDto appointment,
            MoeBusinessDto business,
            ReviewBoosterDTO reviewBoosterConfig) {
        GroomingReportInfoDTO groomingReportInfo =
                convertGroomingReportInfoDTO(groomingReport, appointment.getAppointmentDate(), business);
        // 额外的初始化
        initAdditionalNoteQuestion(groomingReportInfo.getContent().getFeedbacks());
        initReviewBoosterLink(groomingReportInfo.getTemplate(), reviewBoosterConfig);
        return groomingReportInfo;
    }

    public static GroomingReportSummaryInfoDTO.ReviewBoosterConfig buildReviewBoosterConfig(
            GroomingReportInfoDTO.GroomingReportTemplate template, ReviewBoosterDTO reviewBoosterConfig) {
        GroomingReportSummaryInfoDTO.ReviewBoosterConfig reviewConfig =
                new GroomingReportSummaryInfoDTO.ReviewBoosterConfig();
        if (template.getShowReviewBooster() && reviewBoosterConfig != null) {
            BeanUtils.copyProperties(reviewBoosterConfig, reviewConfig);
        }
        return reviewConfig;
    }

    public static GroomingReportSummaryInfoDTO.ReviewBoosterRecord buildReviewBoosterRecord(
            MoeGroomingReport groomingReport,
            AppointmentWithPetDetailsDto appointment,
            List<ReviewBoosterRecordDTO> reviewRecords) {
        if (groomingReport == null || appointment == null || CollectionUtils.isEmpty(reviewRecords)) {
            return null;
        }
        Integer groomingReportPetId = groomingReport.getPetId();
        List<Integer> groomingReportStaffIds = appointment.getServices().stream()
                .filter(service -> Objects.equals(service.getPetId(), groomingReportPetId))
                .map(AppointmentServiceInfo::getStaffId)
                .distinct()
                .toList();
        ReviewBoosterRecordDTO reviewRecordDTO = reviewRecords.stream()
                .filter(record -> record.getStaffIds().equals(groomingReportStaffIds))
                .filter(record -> record.getPetIds().equals(List.of(groomingReportPetId)))
                .findFirst()
                .orElse(null);

        GroomingReportSummaryInfoDTO.ReviewBoosterRecord reviewRecord = null;
        if (reviewRecordDTO != null) {
            reviewRecord = new GroomingReportSummaryInfoDTO.ReviewBoosterRecord();
            BeanUtils.copyProperties(reviewRecordDTO, reviewRecord);
        }
        return reviewRecord;
    }

    public static GroomingReportSummaryInfoDTO.GroomingInfo buildGroomingInfo(
            MoeBusinessDto business,
            Map<Integer, CustomerPetDetailDTO> petMap,
            AppointmentWithPetDetailsDto appointment,
            Map<Integer, MoeStaffDto> staffMap,
            boolean showDateOnly) {
        if (appointment == null) {
            return null;
        }
        List<GroomingReportSummaryInfoDTO.PetServiceDetailInfo> petServiceDetails = new ArrayList<>();
        appointment.getServices().stream()
                .filter(service -> petMap.containsKey(service.getPetId()))
                .collect(Collectors.groupingBy(AppointmentServiceInfo::getPetId))
                .forEach((petId, serviceInfoList) -> {
                    CustomerPetDetailDTO pet = petMap.get(petId);
                    GroomingReportSummaryInfoDTO.PetServiceDetailInfo petServiceDetail =
                            new GroomingReportSummaryInfoDTO.PetServiceDetailInfo();
                    petServiceDetail.setPetId(pet.getPetId());
                    petServiceDetail.setPetTypeId(pet.getPetTypeId());
                    petServiceDetail.setPetName(pet.getPetName());
                    petServiceDetail.setPetAvatarPath(pet.getAvatarPath());
                    petServiceDetail.setServiceDetails(serviceInfoList.stream()
                            .map(serviceInfo -> {
                                MoeStaffDto staff = staffMap.get(serviceInfo.getStaffId());
                                GroomingReportSummaryInfoDTO.ServiceDetailInfo serviceDetail =
                                        new GroomingReportSummaryInfoDTO.ServiceDetailInfo();
                                BeanUtils.copyProperties(serviceInfo, serviceDetail);
                                serviceDetail.setServiceDuration(serviceInfo.getServiceTime());
                                if (Objects.nonNull(staff)) {
                                    serviceDetail.setStaffFirstName(staff.getFirstName());
                                    serviceDetail.setStaffLastName(staff.getLastName());
                                    serviceDetail.setStaffAvatarPath(staff.getAvatarPath());
                                }
                                return serviceDetail;
                            })
                            .toList());
                    petServiceDetails.add(petServiceDetail);
                });

        GroomingReportSummaryInfoDTO.GroomingInfo groomingInfo = new GroomingReportSummaryInfoDTO.GroomingInfo();
        BeanUtils.copyProperties(appointment, groomingInfo);
        groomingInfo.setAppointmentDateTimeText(buildAppointmentDateTimeText(business, appointment, showDateOnly));
        groomingInfo.setPetServiceDetails(petServiceDetails);
        return groomingInfo;
    }

    // appointmentDateTimeText 字段已废弃，由调用方自行格式化，等前端上线后，这里再去掉
    public static String buildAppointmentDateTimeText(
            MoeBusinessDto business, AppointmentWithPetDetailsDto appointment, boolean showOnlyDate) {
        String appointmentDateText =
                DateUtil.dateToBusinessFormat(appointment.getAppointmentDate(), business.getDateFormat());
        String appointmentDateTimeText;
        if (showOnlyDate) {
            // 只展示 date
            appointmentDateTimeText = appointmentDateText;
        } else {
            // Arrival window
            if (Objects.nonNull(appointment.getArrivalBeforeStartTime())
                    && Objects.nonNull(appointment.getArrivalAfterStartTime())
                    && !appointment.getArrivalBeforeStartTime().equals(appointment.getArrivalAfterStartTime())) {
                appointmentDateTimeText = appointmentDateText + ", arrive between: "
                        + DateUtil.formatArrivalWindowTime(
                                appointment.getArrivalBeforeStartTime(),
                                appointment.getArrivalAfterStartTime(),
                                business.getTimeFormatType());
            } else {
                // date + time
                appointmentDateTimeText = DateUtil.getApptDateAndTimeStr(
                        appointment.getAppointmentDate(),
                        appointment.getAppointmentStartTime(),
                        business.getDateFormat(),
                        business.getTimeFormatType());
            }
        }
        return appointmentDateTimeText;
    }

    public static MoeGroomingReport getSampleGroomingReport(Integer businessId, GroomingReportTemplateDTO template) {
        GroomingReportInfoDTO.GroomingReportTemplate templateInfo = new GroomingReportInfoDTO.GroomingReportTemplate();
        BeanUtils.copyProperties(template, templateInfo);
        GroomingReportInfoDTO.GroomingReportContent contentInfo = buildSampleGroomingReportContent(template);

        MoeGroomingReport groomingReport = new MoeGroomingReport();
        groomingReport.setBusinessId(businessId);
        groomingReport.setGroomingId(0);
        groomingReport.setCustomerId(0);
        groomingReport.setPetId(0);
        groomingReport.setPetTypeId(PetTypeEnum.DOG.getType());
        groomingReport.setStatus(GroomingReportStatusEnum.submitted.name());
        Optional.ofNullable(template.getLastPublishTime())
                .ifPresent(time -> groomingReport.setTemplatePublishTime(toDate(time)));
        groomingReport.setTemplateJson(JsonUtil.toJson(templateInfo));
        groomingReport.setContentJson(JsonUtil.toJson(contentInfo));
        groomingReport.setThemeCode(template.getThemeCode());
        return groomingReport;
    }

    public static CustomerPetDetailDTO getSamplePet() {
        return new CustomerPetDetailDTO()
                .setPetId(0)
                .setPetName(SAMPLE_PET_NAME)
                .setPetTypeId(PetTypeEnum.DOG.getType())
                .setPetBreed(SAMPLE_PET_BREED)
                .setAvatarPath(SAMPLE_PET_AVATAR)
                .setGender(CustomerPetEnum.GENDER_MALE.intValue())
                .setWeight(SAMPLE_PET_WEIGHT);
    }

    public static MoeStaffDto getSampleStaff() {
        MoeStaffDto staff = new MoeStaffDto();
        staff.setId(0);
        staff.setFirstName(SAMPLE_STAFF_FIRST_NAME);
        staff.setLastName(SAMPLE_STAFF_LAST_NAME);
        return staff;
    }

    public static MoeBusinessCustomerDTO getSampleCustomer() {
        MoeBusinessCustomerDTO customer = new MoeBusinessCustomerDTO();
        customer.setCustomerId(0);
        customer.setPreferredFrequencyDay(SAMPLE_CUSTOMER_FREQUENCY);
        customer.setPreferredFrequencyType(BusinessCustomerConst.FREQUENCY_TYPE_BY_WEEK);
        return customer;
    }

    public static AppointmentWithPetDetailsDto getSampleAppointment(Integer frequencyDay) {
        AppointmentWithPetDetailsDto appointment = new AppointmentWithPetDetailsDto();
        appointment.setAppointmentId(0);
        String date = DateUtil.getNowDateString();
        if (frequencyDay != null) {
            date = GroomingReportUtil.getDaysAfter(date, frequencyDay);
        }
        appointment.setAppointmentDate(date);
        appointment.setAppointmentStartTime(SAMPLE_APPOINTMENT_START_TIME); // 9:30
        appointment.setAppointmentEndTime(SAMPLE_APPOINTMENT_END_TIME); // 10:30
        appointment.setServices(List.of(
                new AppointmentServiceInfo()
                        .setServiceId(1)
                        .setServiceName(SAMPLE_SERVICE_NAME)
                        .setPetId(0)
                        .setServiceType(ServiceEnum.TYPE_SERVICE.intValue())
                        .setServiceTime(SAMPLE_SERVICE_DURATION) // 1 hour
                        .setStaffId(0),
                new AppointmentServiceInfo()
                        .setServiceId(2)
                        .setServiceName(SAMPLE_ADDON_NAME)
                        .setPetId(0)
                        .setServiceType(ServiceEnum.TYPE_ADD_ONS.intValue())
                        .setServiceTime(SAMPLE_ADDON_DURATION) // 0 min
                        .setStaffId(0)));
        return appointment;
    }

    public static GroomingReportInfoDTO.GroomingReportContent buildSampleGroomingReportContent(
            GroomingReportTemplateDTO template) {
        MoeBusinessCustomerDTO customer = getSampleCustomer();
        GroomingReportInfoDTO.GroomingReportContent content = buildGroomingReportContent(template, customer);
        // 填充默认值
        if (!CollectionUtils.isEmpty(content.getFeedbacks())) {
            content.getFeedbacks().forEach(question -> {
                if (Objects.equals(question.getKey(), GroomingReportConst.QUESTION_KEY_COMMENT)) {
                    question.setText(SAMPLE_COMMENT);
                }
                question.setShow(true); // preview 时默认全都展示
                // preview 时 mood 默认选择第一个
                if (Objects.equals(question.getKey(), GroomingReportConst.QUESTION_KEY_MOOD)
                        || !CollectionUtils.isEmpty(question.getOptions())) {
                    question.setChoices(List.of(question.getOptions().get(0)));
                }
            });
        }
        if (!CollectionUtils.isEmpty(content.getPetConditions())) {
            content.getPetConditions().forEach(question -> {
                // body view 填充默认数据
                if (Objects.equals(question.getType(), GroomingReportQuestionTypeEnum.body_view.name())) {
                    question.setUrls(new BodyViewUrl(SAMPLE_BODY_VIEW_LEFT, SAMPLE_BODY_VIEW_RIGHT));
                }
                question.setShow(true); // preview 时默认全都展示
            });
        }
        // showcase
        content.setShowcase(List.of(SAMPLE_SHOWCASE_BEFORE, SAMPLE_SHOWCASE_AFTER));
        return content;
    }

    public static GroomingReportPreviewDataDTO.GroomingReportSampleValue getGroomingReportSampleValue() {
        return new GroomingReportPreviewDataDTO.GroomingReportSampleValue(
                SAMPLE_COMMENT,
                SAMPLE_PET_AVATAR,
                List.of(SAMPLE_SHOWCASE_BEFORE, SAMPLE_SHOWCASE_AFTER),
                new BodyViewUrl(SAMPLE_BODY_VIEW_LEFT, SAMPLE_BODY_VIEW_RIGHT));
    }

    public static GroomingReportInfoDTO.GroomingReportContent buildGroomingReportContent(
            GroomingReportTemplateDTO template, MoeBusinessCustomerDTO customer) {
        GroomingReportInfoDTO.GroomingReportContent content = new GroomingReportInfoDTO.GroomingReportContent();
        if (Boolean.TRUE.equals(template.getShowOverallFeedback())) {
            content.setFeedbacks(convertQuestionList(template.getQuestions().feedbacks()));
        }
        if (Boolean.TRUE.equals(template.getShowPetCondition())) {
            content.setPetConditions(convertQuestionList(template.getQuestions().petConditions()));
        }
        if (Boolean.TRUE.equals(template.getShowShowcase())) {
            content.setShowcase(List.of("", ""));
        }
        if (Boolean.TRUE.equals(template.getShowNextAppointment())) {
            GroomingRecommendation recommendation = new GroomingRecommendation();
            recommendation.setFrequencyDay(customer.getPreferredFrequencyDay());
            recommendation.setFrequencyType(customer.getPreferredFrequencyType());
            content.setRecommendation(recommendation);
        }
        return content;
    }

    /**
     * 合并 template 和 save params，把 params 中已输入项的值保留
     *
     * @param reportInfo       reportInfo
     * @param reportInfoParams 保存参数
     * @param template         template
     * @param business         business 信息
     * @param customer         customer 信息
     * @param appointmentDate  appointmentDate
     */
    public static void mergeGroomingReportContent(
            GroomingReportInfoDTO reportInfo,
            GroomingReportInfoParams reportInfoParams,
            GroomingReportTemplateDTO template,
            MoeBusinessDto business,
            MoeBusinessCustomerDTO customer,
            String appointmentDate) {
        GroomingReportInfoDTO.GroomingReportTemplate newTemplate = new GroomingReportInfoDTO.GroomingReportTemplate();
        BeanUtils.copyProperties(template, newTemplate);
        reportInfo.setTemplate(newTemplate);
        reportInfo.setTemplatePublishTime(template.getLastPublishTime());

        GroomingReportInfoDTO.GroomingReportContent content = new GroomingReportInfoDTO.GroomingReportContent();
        if (Boolean.TRUE.equals(template.getShowOverallFeedback())) {
            content.setFeedbacks(mergeQuestionList(
                    reportInfoParams.getContent().getFeedbacks(),
                    template.getQuestions().feedbacks()));
        }
        if (Boolean.TRUE.equals(template.getShowPetCondition())) {
            content.setPetConditions(mergeQuestionList(
                    reportInfoParams.getContent().getPetConditions(),
                    template.getQuestions().petConditions()));
        }
        if (Boolean.TRUE.equals(template.getShowShowcase())) {
            content.setShowcase(reportInfoParams.getContent().getShowcase());
        }

        if (Boolean.TRUE.equals(template.getShowNextAppointment())) {
            GroomingRecommendation recommendation =
                    reportInfoParams.getContent().getRecommendation();
            if (recommendation == null) {
                recommendation = new GroomingRecommendation();
            }
            if (recommendation.getFrequencyDay() == null
                    && // merge 之前没设置则重新设置一次
                    customer != null) {
                recommendation.setFrequencyDay(customer.getPreferredFrequencyDay());
                recommendation.setFrequencyType(customer.getPreferredFrequencyType());
            }
            content.setRecommendation(recommendation);
        }
        reportInfo.setContent(content);
        initGroomingReportInfo(reportInfo, appointmentDate, business);
    }

    private static List<GroomingReportInfoDTO.GroomingReportQuestion> mergeQuestionList(
            List<GroomingReportQuestionParams> questionList, List<GroomingReportQuestionDTO> templateQuestionList) {
        Map<Integer, GroomingReportQuestionParams> questionMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(questionList)) {
            questionMap.putAll(questionList.stream()
                    .collect(Collectors.toMap(GroomingReportQuestionParams::getId, Function.identity(), (o, n) -> n)));
        }

        return templateQuestionList.stream()
                .map(templateQuestion -> {
                    GroomingReportInfoDTO.GroomingReportQuestion question =
                            new GroomingReportInfoDTO.GroomingReportQuestion();
                    BeanUtils.copyProperties(templateQuestion, question);
                    if (!questionMap.containsKey(templateQuestion.getId())) {
                        return question;
                    }
                    GroomingReportQuestionParams params = questionMap.get(templateQuestion.getId());
                    if (GroomingReportQuestionTypeEnum.isChoiceQuestion(params.getType())
                            && !CollectionUtils.isEmpty(params.getChoices())) {
                        Set<String> allOptions = new HashSet<>(question.getOptions());
                        if (!CollectionUtils.isEmpty(params.getCustomOptions())) {
                            allOptions.addAll(params.getCustomOptions());
                            question.setCustomOptions(params.getCustomOptions());
                        }
                        // 移除不在 options 和 customOptions 中的 choice
                        params.getChoices().removeIf(choice -> !allOptions.contains(choice));
                        question.setChoices(params.getChoices());
                    } else if (GroomingReportQuestionTypeEnum.isBodyViewQuestion(params.getType())) {
                        // body view question 选择的内容保留
                        if (params.getChoices() != null) {
                            question.setChoices(params.getChoices());
                        }
                    }
                    // question.required = true 时，忽略 params.show，直接设置为 true，否则使用 params.show
                    question.setShow(question.getRequired() || params.getShow());
                    question.setText(params.getText());
                    question.setPlaceholder(params.getPlaceholder());
                    question.setUrls(params.getUrls());
                    return question;
                })
                .toList();
    }

    /* 初始化相关 */
    /**
     * 设置最新 review booster link 到模板
     *
     * @param template template
     * @param reviewBoosterConfig review booster 配置
     */
    private static void initReviewBoosterLink(
            GroomingReportInfoDTO.GroomingReportTemplate template, ReviewBoosterDTO reviewBoosterConfig) {
        if (!Boolean.TRUE.equals(template.getShowReviewBooster()) || reviewBoosterConfig == null) {
            return;
        }
        if (Boolean.TRUE.equals(template.getShowYelpReview())) {
            template.setYelpReviewLink(reviewBoosterConfig.getPositiveYelp());
        }
        if (Boolean.TRUE.equals(template.getShowGoogleReview())) {
            template.setGoogleReviewLink(reviewBoosterConfig.getPositiveGoogle());
        }
        if (Boolean.TRUE.equals(template.getShowFacebookReview())) {
            template.setFacebookReviewLink(reviewBoosterConfig.getPositiveFacebook());
        }
    }

    private static void initGroomingReportInfo(
            GroomingReportInfoDTO reportInfo, String appointmentDate, MoeBusinessDto business) {
        initQuestionList(reportInfo.getContent().getFeedbacks(), reportInfo.getPetTypeId());
        initPetConditionShow(reportInfo);
        initQuestionList(reportInfo.getContent().getPetConditions(), reportInfo.getPetTypeId());
        initGroomingRecommendation(reportInfo.getContent().getRecommendation(), appointmentDate, business);
    }

    private static void initPetConditionShow(GroomingReportInfoDTO reportInfo) {
        List<GroomingReportInfoDTO.GroomingReportQuestion> petConditions =
                reportInfo.getContent().getPetConditions();
        // 非第一次初始化时，不需要再次初始化 question show
        if (CollectionUtils.isEmpty(petConditions)) {
            return;
        }
        // 如果 question 的 show 都为 null，则设置前面4个 show = true
        if (petConditions.stream().allMatch(petCondition -> petCondition.getShow() == null)) {
            int showSize = 4; // 默认 show 前面四个
            for (GroomingReportInfoDTO.GroomingReportQuestion question : petConditions) {
                // 非 Dog/Cat type 时，Body view 默认不展示
                if (!GroomingReportUtil.isDogOrCatType(reportInfo.getPetTypeId())
                        && GroomingReportQuestionTypeEnum.isBodyViewQuestion(question.getType())) {
                    question.setShow(false);
                    continue;
                }

                if (showSize > 0) {
                    question.setShow(true);
                    showSize--;
                }
            }
        }
    }

    private static void initQuestionList(
            List<GroomingReportInfoDTO.GroomingReportQuestion> questions, Integer petTypeId) {
        if (CollectionUtils.isEmpty(questions)) {
            return;
        }
        for (GroomingReportInfoDTO.GroomingReportQuestion question : questions) {
            // single choice question 初始化
            initChoiceQuestion(question);
            // body view question 初始化
            initBodyViewQuestion(petTypeId, question);
            // 通用字段赋值
            if (question.getShow() == null) {
                question.setShow(question.getRequired());
            }
        }
    }

    public static void initChoiceQuestionChoice(GroomingReportInfoDTO.GroomingReportQuestion question) {
        initChoiceQuestion(question);
    }

    private static void initChoiceQuestion(GroomingReportInfoDTO.GroomingReportQuestion question) {

        // 统一初始化空数组
        if (Objects.isNull(question.getChoices())) {
            question.setChoices(new ArrayList<>());
        }

        // 非 choice question 或者 customized feedback question 无需预填
        if (!GroomingReportQuestionTypeEnum.isChoiceQuestion(question.getType())
                || Objects.equals(question.getCategory(), CATEGORY_CUSTOMIZED_FEEDBACK.getType())) {
            return;
        }

        // 单选题如果 choices, customOptions 为空且 options 不为空，则选中第一个，多选题可以为空
        if (question.getChoices().isEmpty()
                && CollectionUtils.isEmpty(question.getCustomOptions())
                && !CollectionUtils.isEmpty(question.getOptions())) {
            question.getChoices().add(question.getOptions().get(0));
        }

        Set<String> allOptions = new HashSet<>();
        if (!CollectionUtils.isEmpty(question.getOptions())) {
            allOptions.addAll(question.getOptions());
        }

        if (!CollectionUtils.isEmpty(question.getCustomOptions())) {
            allOptions.addAll(question.getCustomOptions());
        }
        // 移除不存在的 choice
        question.getChoices().removeIf(choice -> !allOptions.contains(choice));
    }

    private static void initBodyViewQuestion(Integer petTypeId, GroomingReportInfoDTO.GroomingReportQuestion question) {
        if (!GroomingReportQuestionTypeEnum.isBodyViewQuestion(question.getType())) {
            return;
        }
        if (!GroomingReportUtil.isDogOrCatType(petTypeId)) {
            question.setShow(false);
            return;
        }

        if (question.getChoices() == null) {
            question.setChoices(List.of());
        }
        boolean isDogType = Objects.equals(petTypeId, PetTypeEnum.DOG.getType());
        String defaultLeftUrl = isDogType ? DOG_DEFAULT_BODY_VIEW_LEFT : CAT_DEFAULT_BODY_VIEW_LEFT;
        String defaultRightUrl = isDogType ? DOG_DEFAULT_BODY_VIEW_RIGHT : CAT_DEFAULT_BODY_VIEW_RIGHT;
        if (question.getUrls() == null) {
            question.setUrls(new BodyViewUrl(defaultLeftUrl, defaultRightUrl));
        } else {
            question.setUrls(new BodyViewUrl(
                    StringUtils.hasText(question.getUrls().getLeft())
                            ? question.getUrls().getLeft()
                            : defaultLeftUrl,
                    StringUtils.hasText(question.getUrls().getRight())
                            ? question.getUrls().getRight()
                            : defaultRightUrl));
        }
        // 默认值，防止前端报错
        question.setOptions(List.of());
        question.setCustomOptions(List.of());
    }

    private static void initAdditionalNoteQuestion(List<GroomingReportInfoDTO.GroomingReportQuestion> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return;
        }
        questions.stream()
                .filter(question -> Objects.equals(question.getKey(), GroomingReportConst.QUESTION_KEY_COMMENT))
                .findFirst()
                .ifPresent(question -> {
                    // additional note question 没有填内容时，填充默认 comment
                    if (!StringUtils.hasText(question.getText())) {
                        question.setText(SAMPLE_COMMENT);
                    }
                });
    }

    private static void initGroomingRecommendation(
            GroomingRecommendation recommendation, String appointmentDate, MoeBusinessDto business) {
        if (appointmentDate == null || recommendation == null) {
            return;
        }
        if (recommendation.getFrequencyDay() != null && recommendation.getFrequencyType() != null) {
            recommendation.setFrequencyText(
                    getFrequencyText(recommendation.getFrequencyDay(), recommendation.getFrequencyType()));

            String recommendDate = getDaysAfter(appointmentDate, recommendation.getFrequencyDay());
            recommendation.setNextAppointmentDate(recommendDate);
            recommendation.setNextAppointmentDateText(
                    DateUtil.dateToBusinessFormat(recommendDate, business.getDateFormat()));
        }
    }
}
