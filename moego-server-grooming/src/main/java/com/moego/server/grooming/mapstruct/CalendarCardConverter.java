package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.calendarcard.CalendarCardDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/4/5
 */
@Mapper
public interface CalendarCardConverter {

    CalendarCardConverter INSTANCE = Mappers.getMapper(CalendarCardConverter.class);

    CalendarCardDTO copy(CalendarCardDTO calendarCardDTO);
}
