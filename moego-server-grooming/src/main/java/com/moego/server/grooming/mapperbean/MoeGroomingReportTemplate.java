package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_report_template
 */
public class MoeGroomingReportTemplate {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   thank you message
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.thank_you_message
     *
     * @mbg.generated
     */
    private String thankYouMessage;

    /**
     * Database Column Remarks:
     *   theme color
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.theme_color
     *
     * @mbg.generated
     */
    private String themeColor;

    /**
     * Database Column Remarks:
     *   light theme color
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.light_theme_color
     *
     * @mbg.generated
     */
    private String lightThemeColor;

    /**
     * Database Column Remarks:
     *   show showcase section: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.show_showcase
     *
     * @mbg.generated
     */
    private Boolean showShowcase;

    /**
     * Database Column Remarks:
     *   show overall feedback section: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.show_overall_feedback
     *
     * @mbg.generated
     */
    private Boolean showOverallFeedback;

    /**
     * Database Column Remarks:
     *   before photo required: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.require_before_photo
     *
     * @mbg.generated
     */
    private Boolean requireBeforePhoto;

    /**
     * Database Column Remarks:
     *   after photo required: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.require_after_photo
     *
     * @mbg.generated
     */
    private Boolean requireAfterPhoto;

    /**
     * Database Column Remarks:
     *   show pet condition: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.show_pet_condition
     *
     * @mbg.generated
     */
    private Boolean showPetCondition;

    /**
     * Database Column Remarks:
     *   service info should staff name: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.show_service_staff_name
     *
     * @mbg.generated
     */
    private Boolean showServiceStaffName;

    /**
     * Database Column Remarks:
     *   show recommended next appointment: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.show_next_appointment
     *
     * @mbg.generated
     */
    private Boolean showNextAppointment;

    /**
     * Database Column Remarks:
     *   next appointment date format type: 1-only date, 2-date and time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.next_appointment_date_format_type
     *
     * @mbg.generated
     */
    private Byte nextAppointmentDateFormatType;

    /**
     * Database Column Remarks:
     *   show review booster section: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.show_review_booster
     *
     * @mbg.generated
     */
    private Boolean showReviewBooster;

    /**
     * Database Column Remarks:
     *   show yelp review icon: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.show_yelp_review
     *
     * @mbg.generated
     */
    private Boolean showYelpReview;

    /**
     * Database Column Remarks:
     *   show google review icon: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.show_google_review
     *
     * @mbg.generated
     */
    private Boolean showGoogleReview;

    /**
     * Database Column Remarks:
     *   show facebook review icon: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.show_facebook_review
     *
     * @mbg.generated
     */
    private Boolean showFacebookReview;

    /**
     * Database Column Remarks:
     *   last published time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.last_publish_time
     *
     * @mbg.generated
     */
    private Date lastPublishTime;

    /**
     * Database Column Remarks:
     *   last update staff id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.update_by
     *
     * @mbg.generated
     */
    private Integer updateBy;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   title
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.title
     *
     * @mbg.generated
     */
    private String title;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   theme code
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_template.theme_code
     *
     * @mbg.generated
     */
    private String themeCode;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.id
     *
     * @return the value of moe_grooming_report_template.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.id
     *
     * @param id the value for moe_grooming_report_template.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.business_id
     *
     * @return the value of moe_grooming_report_template.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.business_id
     *
     * @param businessId the value for moe_grooming_report_template.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.thank_you_message
     *
     * @return the value of moe_grooming_report_template.thank_you_message
     *
     * @mbg.generated
     */
    public String getThankYouMessage() {
        return thankYouMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.thank_you_message
     *
     * @param thankYouMessage the value for moe_grooming_report_template.thank_you_message
     *
     * @mbg.generated
     */
    public void setThankYouMessage(String thankYouMessage) {
        this.thankYouMessage = thankYouMessage == null ? null : thankYouMessage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.theme_color
     *
     * @return the value of moe_grooming_report_template.theme_color
     *
     * @mbg.generated
     */
    public String getThemeColor() {
        return themeColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.theme_color
     *
     * @param themeColor the value for moe_grooming_report_template.theme_color
     *
     * @mbg.generated
     */
    public void setThemeColor(String themeColor) {
        this.themeColor = themeColor == null ? null : themeColor.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.light_theme_color
     *
     * @return the value of moe_grooming_report_template.light_theme_color
     *
     * @mbg.generated
     */
    public String getLightThemeColor() {
        return lightThemeColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.light_theme_color
     *
     * @param lightThemeColor the value for moe_grooming_report_template.light_theme_color
     *
     * @mbg.generated
     */
    public void setLightThemeColor(String lightThemeColor) {
        this.lightThemeColor = lightThemeColor == null ? null : lightThemeColor.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.show_showcase
     *
     * @return the value of moe_grooming_report_template.show_showcase
     *
     * @mbg.generated
     */
    public Boolean getShowShowcase() {
        return showShowcase;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.show_showcase
     *
     * @param showShowcase the value for moe_grooming_report_template.show_showcase
     *
     * @mbg.generated
     */
    public void setShowShowcase(Boolean showShowcase) {
        this.showShowcase = showShowcase;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.show_overall_feedback
     *
     * @return the value of moe_grooming_report_template.show_overall_feedback
     *
     * @mbg.generated
     */
    public Boolean getShowOverallFeedback() {
        return showOverallFeedback;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.show_overall_feedback
     *
     * @param showOverallFeedback the value for moe_grooming_report_template.show_overall_feedback
     *
     * @mbg.generated
     */
    public void setShowOverallFeedback(Boolean showOverallFeedback) {
        this.showOverallFeedback = showOverallFeedback;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.require_before_photo
     *
     * @return the value of moe_grooming_report_template.require_before_photo
     *
     * @mbg.generated
     */
    public Boolean getRequireBeforePhoto() {
        return requireBeforePhoto;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.require_before_photo
     *
     * @param requireBeforePhoto the value for moe_grooming_report_template.require_before_photo
     *
     * @mbg.generated
     */
    public void setRequireBeforePhoto(Boolean requireBeforePhoto) {
        this.requireBeforePhoto = requireBeforePhoto;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.require_after_photo
     *
     * @return the value of moe_grooming_report_template.require_after_photo
     *
     * @mbg.generated
     */
    public Boolean getRequireAfterPhoto() {
        return requireAfterPhoto;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.require_after_photo
     *
     * @param requireAfterPhoto the value for moe_grooming_report_template.require_after_photo
     *
     * @mbg.generated
     */
    public void setRequireAfterPhoto(Boolean requireAfterPhoto) {
        this.requireAfterPhoto = requireAfterPhoto;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.show_pet_condition
     *
     * @return the value of moe_grooming_report_template.show_pet_condition
     *
     * @mbg.generated
     */
    public Boolean getShowPetCondition() {
        return showPetCondition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.show_pet_condition
     *
     * @param showPetCondition the value for moe_grooming_report_template.show_pet_condition
     *
     * @mbg.generated
     */
    public void setShowPetCondition(Boolean showPetCondition) {
        this.showPetCondition = showPetCondition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.show_service_staff_name
     *
     * @return the value of moe_grooming_report_template.show_service_staff_name
     *
     * @mbg.generated
     */
    public Boolean getShowServiceStaffName() {
        return showServiceStaffName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.show_service_staff_name
     *
     * @param showServiceStaffName the value for moe_grooming_report_template.show_service_staff_name
     *
     * @mbg.generated
     */
    public void setShowServiceStaffName(Boolean showServiceStaffName) {
        this.showServiceStaffName = showServiceStaffName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.show_next_appointment
     *
     * @return the value of moe_grooming_report_template.show_next_appointment
     *
     * @mbg.generated
     */
    public Boolean getShowNextAppointment() {
        return showNextAppointment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.show_next_appointment
     *
     * @param showNextAppointment the value for moe_grooming_report_template.show_next_appointment
     *
     * @mbg.generated
     */
    public void setShowNextAppointment(Boolean showNextAppointment) {
        this.showNextAppointment = showNextAppointment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.next_appointment_date_format_type
     *
     * @return the value of moe_grooming_report_template.next_appointment_date_format_type
     *
     * @mbg.generated
     */
    public Byte getNextAppointmentDateFormatType() {
        return nextAppointmentDateFormatType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.next_appointment_date_format_type
     *
     * @param nextAppointmentDateFormatType the value for moe_grooming_report_template.next_appointment_date_format_type
     *
     * @mbg.generated
     */
    public void setNextAppointmentDateFormatType(Byte nextAppointmentDateFormatType) {
        this.nextAppointmentDateFormatType = nextAppointmentDateFormatType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.show_review_booster
     *
     * @return the value of moe_grooming_report_template.show_review_booster
     *
     * @mbg.generated
     */
    public Boolean getShowReviewBooster() {
        return showReviewBooster;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.show_review_booster
     *
     * @param showReviewBooster the value for moe_grooming_report_template.show_review_booster
     *
     * @mbg.generated
     */
    public void setShowReviewBooster(Boolean showReviewBooster) {
        this.showReviewBooster = showReviewBooster;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.show_yelp_review
     *
     * @return the value of moe_grooming_report_template.show_yelp_review
     *
     * @mbg.generated
     */
    public Boolean getShowYelpReview() {
        return showYelpReview;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.show_yelp_review
     *
     * @param showYelpReview the value for moe_grooming_report_template.show_yelp_review
     *
     * @mbg.generated
     */
    public void setShowYelpReview(Boolean showYelpReview) {
        this.showYelpReview = showYelpReview;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.show_google_review
     *
     * @return the value of moe_grooming_report_template.show_google_review
     *
     * @mbg.generated
     */
    public Boolean getShowGoogleReview() {
        return showGoogleReview;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.show_google_review
     *
     * @param showGoogleReview the value for moe_grooming_report_template.show_google_review
     *
     * @mbg.generated
     */
    public void setShowGoogleReview(Boolean showGoogleReview) {
        this.showGoogleReview = showGoogleReview;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.show_facebook_review
     *
     * @return the value of moe_grooming_report_template.show_facebook_review
     *
     * @mbg.generated
     */
    public Boolean getShowFacebookReview() {
        return showFacebookReview;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.show_facebook_review
     *
     * @param showFacebookReview the value for moe_grooming_report_template.show_facebook_review
     *
     * @mbg.generated
     */
    public void setShowFacebookReview(Boolean showFacebookReview) {
        this.showFacebookReview = showFacebookReview;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.last_publish_time
     *
     * @return the value of moe_grooming_report_template.last_publish_time
     *
     * @mbg.generated
     */
    public Date getLastPublishTime() {
        return lastPublishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.last_publish_time
     *
     * @param lastPublishTime the value for moe_grooming_report_template.last_publish_time
     *
     * @mbg.generated
     */
    public void setLastPublishTime(Date lastPublishTime) {
        this.lastPublishTime = lastPublishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.update_by
     *
     * @return the value of moe_grooming_report_template.update_by
     *
     * @mbg.generated
     */
    public Integer getUpdateBy() {
        return updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.update_by
     *
     * @param updateBy the value for moe_grooming_report_template.update_by
     *
     * @mbg.generated
     */
    public void setUpdateBy(Integer updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.create_time
     *
     * @return the value of moe_grooming_report_template.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.create_time
     *
     * @param createTime the value for moe_grooming_report_template.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.update_time
     *
     * @return the value of moe_grooming_report_template.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.update_time
     *
     * @param updateTime the value for moe_grooming_report_template.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.title
     *
     * @return the value of moe_grooming_report_template.title
     *
     * @mbg.generated
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.title
     *
     * @param title the value for moe_grooming_report_template.title
     *
     * @mbg.generated
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.company_id
     *
     * @return the value of moe_grooming_report_template.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.company_id
     *
     * @param companyId the value for moe_grooming_report_template.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_template.theme_code
     *
     * @return the value of moe_grooming_report_template.theme_code
     *
     * @mbg.generated
     */
    public String getThemeCode() {
        return themeCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_template.theme_code
     *
     * @param themeCode the value for moe_grooming_report_template.theme_code
     *
     * @mbg.generated
     */
    public void setThemeCode(String themeCode) {
        this.themeCode = themeCode == null ? null : themeCode.trim();
    }
}
