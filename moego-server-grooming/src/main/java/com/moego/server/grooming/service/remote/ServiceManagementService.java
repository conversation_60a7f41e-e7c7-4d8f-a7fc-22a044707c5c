package com.moego.server.grooming.service.remote;

import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
@Service
@RequiredArgsConstructor
public class ServiceManagementService {

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceBlockingStub;

    public Map<Long, ServiceBriefView> list(List<Integer> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Map.of();
        }
        GetServiceListByIdsResponse response =
                serviceBlockingStub.getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(
                                serviceIds.stream().map(Integer::longValue).toList())
                        .build());
        return response.getServicesList().stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity()));
    }

    public List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> batchGetCustomizedService(
            Long companyId, List<CustomizedServiceQueryCondition> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return List.of();
        }

        BatchGetCustomizedServiceRequest request = BatchGetCustomizedServiceRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllQueryConditionList(conditions)
                .build();

        BatchGetCustomizedServiceResponse batchGetCustomizedServiceResponse =
                serviceBlockingStub.batchGetCustomizedService(request);

        return batchGetCustomizedServiceResponse.getCustomizedServiceListList();
    }
}
