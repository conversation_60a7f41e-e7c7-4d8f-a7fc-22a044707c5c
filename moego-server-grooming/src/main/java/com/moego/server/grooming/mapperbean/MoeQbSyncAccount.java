package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_sync_account
 */
public class MoeQbSyncAccount {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.connect_id
     *
     * @mbg.generated
     */
    private Integer connectId;

    /**
     * Database Column Remarks:
     *   realmId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.realm_id
     *
     * @mbg.generated
     */
    private String realmId;

    /**
     * Database Column Remarks:
     *   quickbookds服务ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.qb_account_id
     *
     * @mbg.generated
     */
    private String qbAccountId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     * Database Column Remarks:
     *   moego在qb上创建的帐号类型, e.g. 1-moego invoice
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.type
     *
     * @mbg.generated
     */
    private Integer type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_account.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.id
     *
     * @return the value of moe_qb_sync_account.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.id
     *
     * @param id the value for moe_qb_sync_account.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.business_id
     *
     * @return the value of moe_qb_sync_account.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.business_id
     *
     * @param businessId the value for moe_qb_sync_account.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.connect_id
     *
     * @return the value of moe_qb_sync_account.connect_id
     *
     * @mbg.generated
     */
    public Integer getConnectId() {
        return connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.connect_id
     *
     * @param connectId the value for moe_qb_sync_account.connect_id
     *
     * @mbg.generated
     */
    public void setConnectId(Integer connectId) {
        this.connectId = connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.realm_id
     *
     * @return the value of moe_qb_sync_account.realm_id
     *
     * @mbg.generated
     */
    public String getRealmId() {
        return realmId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.realm_id
     *
     * @param realmId the value for moe_qb_sync_account.realm_id
     *
     * @mbg.generated
     */
    public void setRealmId(String realmId) {
        this.realmId = realmId == null ? null : realmId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.qb_account_id
     *
     * @return the value of moe_qb_sync_account.qb_account_id
     *
     * @mbg.generated
     */
    public String getQbAccountId() {
        return qbAccountId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.qb_account_id
     *
     * @param qbAccountId the value for moe_qb_sync_account.qb_account_id
     *
     * @mbg.generated
     */
    public void setQbAccountId(String qbAccountId) {
        this.qbAccountId = qbAccountId == null ? null : qbAccountId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.name
     *
     * @return the value of moe_qb_sync_account.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.name
     *
     * @param name the value for moe_qb_sync_account.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.type
     *
     * @return the value of moe_qb_sync_account.type
     *
     * @mbg.generated
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.type
     *
     * @param type the value for moe_qb_sync_account.type
     *
     * @mbg.generated
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.update_time
     *
     * @return the value of moe_qb_sync_account.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.update_time
     *
     * @param updateTime the value for moe_qb_sync_account.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.create_time
     *
     * @return the value of moe_qb_sync_account.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.create_time
     *
     * @param createTime the value for moe_qb_sync_account.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_account.company_id
     *
     * @return the value of moe_qb_sync_account.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_account.company_id
     *
     * @param companyId the value for moe_qb_sync_account.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
