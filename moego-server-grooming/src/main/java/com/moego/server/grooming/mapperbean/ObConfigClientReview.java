package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table ob_config_client_review
 */
public class ObConfigClientReview {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_client_review.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_client_review.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   review booster record id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_client_review.review_id
     *
     * @mbg.generated
     */
    private Integer reviewId;

    /**
     * Database Column Remarks:
     *   值越小排在越前面
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_client_review.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     * Database Column Remarks:
     *   created time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_client_review.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_client_review.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     * Database Column Remarks:
     *   pet id（冗余字段）
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_client_review.pet_id
     *
     * @mbg.generated
     */
    private Integer petId;

    /**
     * Database Column Remarks:
     *   customer id（冗余字段）
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_client_review.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_client_review.id
     *
     * @return the value of ob_config_client_review.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_client_review.id
     *
     * @param id the value for ob_config_client_review.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_client_review.business_id
     *
     * @return the value of ob_config_client_review.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_client_review.business_id
     *
     * @param businessId the value for ob_config_client_review.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_client_review.review_id
     *
     * @return the value of ob_config_client_review.review_id
     *
     * @mbg.generated
     */
    public Integer getReviewId() {
        return reviewId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_client_review.review_id
     *
     * @param reviewId the value for ob_config_client_review.review_id
     *
     * @mbg.generated
     */
    public void setReviewId(Integer reviewId) {
        this.reviewId = reviewId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_client_review.sort
     *
     * @return the value of ob_config_client_review.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_client_review.sort
     *
     * @param sort the value for ob_config_client_review.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_client_review.created_at
     *
     * @return the value of ob_config_client_review.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_client_review.created_at
     *
     * @param createdAt the value for ob_config_client_review.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_client_review.updated_at
     *
     * @return the value of ob_config_client_review.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_client_review.updated_at
     *
     * @param updatedAt the value for ob_config_client_review.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_client_review.pet_id
     *
     * @return the value of ob_config_client_review.pet_id
     *
     * @mbg.generated
     */
    public Integer getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_client_review.pet_id
     *
     * @param petId the value for ob_config_client_review.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Integer petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_client_review.customer_id
     *
     * @return the value of ob_config_client_review.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_client_review.customer_id
     *
     * @param customerId the value for ob_config_client_review.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }
}
