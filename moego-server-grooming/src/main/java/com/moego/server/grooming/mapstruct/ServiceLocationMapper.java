package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation;
import com.moego.server.grooming.service.dto.ServiceLocationOverrideDto;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ServiceLocationMapper {

    ServiceLocationMapper INSTANCE = Mappers.getMapper(ServiceLocationMapper.class);

    List<ServiceLocationOverrideDto> beanToDto(List<MoeGroomingServiceLocation> location);
}
