package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_sync_refund
 */
public class MoeQbSyncRefund {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.connect_id
     *
     * @mbg.generated
     */
    private Integer connectId;

    /**
     * Database Column Remarks:
     *   realmId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.realm_id
     *
     * @mbg.generated
     */
    private String realmId;

    /**
     * Database Column Remarks:
     *   payment id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.payment_id
     *
     * @mbg.generated
     */
    private Integer paymentId;

    /**
     * Database Column Remarks:
     *   refund id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.refund_id
     *
     * @mbg.generated
     */
    private Integer refundId;

    /**
     * Database Column Remarks:
     *   qb 中 item 的 id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.qb_refund_id
     *
     * @mbg.generated
     */
    private String qbRefundId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.refund_name
     *
     * @mbg.generated
     */
    private String refundName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_refund.refund_description
     *
     * @mbg.generated
     */
    private String refundDescription;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.id
     *
     * @return the value of moe_qb_sync_refund.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.id
     *
     * @param id the value for moe_qb_sync_refund.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.business_id
     *
     * @return the value of moe_qb_sync_refund.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.business_id
     *
     * @param businessId the value for moe_qb_sync_refund.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.connect_id
     *
     * @return the value of moe_qb_sync_refund.connect_id
     *
     * @mbg.generated
     */
    public Integer getConnectId() {
        return connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.connect_id
     *
     * @param connectId the value for moe_qb_sync_refund.connect_id
     *
     * @mbg.generated
     */
    public void setConnectId(Integer connectId) {
        this.connectId = connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.realm_id
     *
     * @return the value of moe_qb_sync_refund.realm_id
     *
     * @mbg.generated
     */
    public String getRealmId() {
        return realmId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.realm_id
     *
     * @param realmId the value for moe_qb_sync_refund.realm_id
     *
     * @mbg.generated
     */
    public void setRealmId(String realmId) {
        this.realmId = realmId == null ? null : realmId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.payment_id
     *
     * @return the value of moe_qb_sync_refund.payment_id
     *
     * @mbg.generated
     */
    public Integer getPaymentId() {
        return paymentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.payment_id
     *
     * @param paymentId the value for moe_qb_sync_refund.payment_id
     *
     * @mbg.generated
     */
    public void setPaymentId(Integer paymentId) {
        this.paymentId = paymentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.refund_id
     *
     * @return the value of moe_qb_sync_refund.refund_id
     *
     * @mbg.generated
     */
    public Integer getRefundId() {
        return refundId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.refund_id
     *
     * @param refundId the value for moe_qb_sync_refund.refund_id
     *
     * @mbg.generated
     */
    public void setRefundId(Integer refundId) {
        this.refundId = refundId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.qb_refund_id
     *
     * @return the value of moe_qb_sync_refund.qb_refund_id
     *
     * @mbg.generated
     */
    public String getQbRefundId() {
        return qbRefundId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.qb_refund_id
     *
     * @param qbRefundId the value for moe_qb_sync_refund.qb_refund_id
     *
     * @mbg.generated
     */
    public void setQbRefundId(String qbRefundId) {
        this.qbRefundId = qbRefundId == null ? null : qbRefundId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.refund_name
     *
     * @return the value of moe_qb_sync_refund.refund_name
     *
     * @mbg.generated
     */
    public String getRefundName() {
        return refundName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.refund_name
     *
     * @param refundName the value for moe_qb_sync_refund.refund_name
     *
     * @mbg.generated
     */
    public void setRefundName(String refundName) {
        this.refundName = refundName == null ? null : refundName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.update_time
     *
     * @return the value of moe_qb_sync_refund.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.update_time
     *
     * @param updateTime the value for moe_qb_sync_refund.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.create_time
     *
     * @return the value of moe_qb_sync_refund.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.create_time
     *
     * @param createTime the value for moe_qb_sync_refund.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.company_id
     *
     * @return the value of moe_qb_sync_refund.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.company_id
     *
     * @param companyId the value for moe_qb_sync_refund.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_refund.refund_description
     *
     * @return the value of moe_qb_sync_refund.refund_description
     *
     * @mbg.generated
     */
    public String getRefundDescription() {
        return refundDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_refund.refund_description
     *
     * @param refundDescription the value for moe_qb_sync_refund.refund_description
     *
     * @mbg.generated
     */
    public void setRefundDescription(String refundDescription) {
        this.refundDescription = refundDescription == null ? null : refundDescription.trim();
    }
}
