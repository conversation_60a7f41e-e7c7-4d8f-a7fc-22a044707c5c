<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingInvoiceApplyPackageMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="invoice_item_id" jdbcType="INTEGER" property="invoiceItemId" />
    <result column="package_id" jdbcType="INTEGER" property="packageId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="package_service_id" jdbcType="INTEGER" property="packageServiceId" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, invoice_id, invoice_item_id, package_id, service_id, package_service_id, package_name,
    service_name, quantity, create_time, update_time, status
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackageExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_invoice_apply_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_invoice_apply_package
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_invoice_apply_package
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackageExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_invoice_apply_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_invoice_apply_package (invoice_id, invoice_item_id, package_id,
      service_id, package_service_id, package_name,
      service_name, quantity, create_time,
      update_time, status)
    values (#{invoiceId,jdbcType=INTEGER}, #{invoiceItemId,jdbcType=INTEGER}, #{packageId,jdbcType=INTEGER},
      #{serviceId,jdbcType=INTEGER}, #{packageServiceId,jdbcType=INTEGER}, #{packageName,jdbcType=VARCHAR},
      #{serviceName,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT}, #{status,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_invoice_apply_package
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="invoiceItemId != null">
        invoice_item_id,
      </if>
      <if test="packageId != null">
        package_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="packageServiceId != null">
        package_service_id,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="serviceName != null">
        service_name,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="invoiceItemId != null">
        #{invoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="packageId != null">
        #{packageId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="packageServiceId != null">
        #{packageServiceId,jdbcType=INTEGER},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackageExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_invoice_apply_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_invoice_apply_package
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceId != null">
        invoice_id = #{record.invoiceId,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceItemId != null">
        invoice_item_id = #{record.invoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="record.packageId != null">
        package_id = #{record.packageId,jdbcType=INTEGER},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=INTEGER},
      </if>
      <if test="record.packageServiceId != null">
        package_service_id = #{record.packageServiceId,jdbcType=INTEGER},
      </if>
      <if test="record.packageName != null">
        package_name = #{record.packageName,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceName != null">
        service_name = #{record.serviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_invoice_apply_package
    set id = #{record.id,jdbcType=INTEGER},
      invoice_id = #{record.invoiceId,jdbcType=INTEGER},
      invoice_item_id = #{record.invoiceItemId,jdbcType=INTEGER},
      package_id = #{record.packageId,jdbcType=INTEGER},
      service_id = #{record.serviceId,jdbcType=INTEGER},
      package_service_id = #{record.packageServiceId,jdbcType=INTEGER},
      package_name = #{record.packageName,jdbcType=VARCHAR},
      service_name = #{record.serviceName,jdbcType=VARCHAR},
      quantity = #{record.quantity,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_invoice_apply_package
    <set>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="invoiceItemId != null">
        invoice_item_id = #{invoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="packageId != null">
        package_id = #{packageId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="packageServiceId != null">
        package_service_id = #{packageServiceId,jdbcType=INTEGER},
      </if>
      <if test="packageName != null">
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        service_name = #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_invoice_apply_package
    set invoice_id = #{invoiceId,jdbcType=INTEGER},
      invoice_item_id = #{invoiceItemId,jdbcType=INTEGER},
      package_id = #{packageId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      package_service_id = #{packageServiceId,jdbcType=INTEGER},
      package_name = #{packageName,jdbcType=VARCHAR},
      service_name = #{serviceName,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <resultMap id="UsedPackageWithPriceMap" type="com.moego.server.grooming.service.dto.UsedPackageWithPrice">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="invoice_item_id" jdbcType="INTEGER" property="invoiceItemId" />
    <result column="package_id" jdbcType="INTEGER" property="packageId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
  </resultMap>

  <select id="selectByInvoiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_invoice_apply_package
    where invoice_id = #{invoiceId,jdbcType=INTEGER}
    and status = 1
  </select>

  <select id="selectByInvoiceIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_invoice_apply_package
    where invoice_id IN
    <foreach close=")" collection="invoiceIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and status = 1
  </select>

  <select id="selectByInvoiceIdWithPrice" resultMap="UsedPackageWithPriceMap">
    select a.id, a.quantity, a.invoice_id, a.service_name, b.price
    from moe_grooming.moe_grooming_invoice_apply_package a
    join moe_grooming.moe_grooming_service b on b.id = a.service_id
    where a.invoice_id in
    <foreach close=")" collection="invoiceIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and a.status = 1
  </select>

  <update id="batchUpdateAppliedRecord">
    <foreach collection="records" index="index" item="item" separator=";">
      update moe_grooming.moe_grooming_invoice_apply_package
      <set>
        <if test="item.invoiceId != null">
          invoice_id = #{item.invoiceId,jdbcType=INTEGER},
        </if>
        <if test="item.invoiceItemId != null">
          invoice_item_id = #{item.invoiceItemId,jdbcType=INTEGER},
        </if>
        <if test="item.packageId != null">
          package_id = #{item.packageId,jdbcType=INTEGER},
        </if>
        <if test="item.serviceId != null">
          service_id = #{item.serviceId,jdbcType=INTEGER},
        </if>
        <if test="item.packageServiceId != null">
          package_service_id = #{item.packageServiceId,jdbcType=INTEGER},
        </if>
        <if test="item.packageName != null">
          package_name = #{item.packageName,jdbcType=VARCHAR},
        </if>
        <if test="item.serviceName != null">
          service_name = #{item.serviceName,jdbcType=VARCHAR},
        </if>
        <if test="item.quantity != null">
          quantity = #{item.quantity,jdbcType=INTEGER},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=BIGINT},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime,jdbcType=BIGINT},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=TINYINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

</mapper>
