package com.moego.server.grooming.service;

import com.moego.server.grooming.mapperbean.MoeGroomingDataRule;
import org.springframework.stereotype.Service;

@Service
public class MoeDataRuleService {

    /**
     * 过时方法，后续会清理
     *
     * @return
     */
    @Deprecated
    public MoeGroomingDataRule getDefaultDataRule() {
        MoeGroomingDataRule moeGroomingDataRule = new MoeGroomingDataRule();
        moeGroomingDataRule.setClientFullName(Byte.parseByte("1"));
        moeGroomingDataRule.setPetNameBreed(Byte.parseByte("1"));
        moeGroomingDataRule.setServiceName(Byte.parseByte("1"));
        moeGroomingDataRule.setServicePrice(Byte.parseByte("1"));
        moeGroomingDataRule.setClientFullAddress(Byte.parseByte("1"));
        moeGroomingDataRule.setCity(Byte.parseByte("1"));
        moeGroomingDataRule.setZipcode(Byte.parseByte("1"));
        moeGroomingDataRule.setTicketComments(Byte.parseByte("1"));
        moeGroomingDataRule.setAlertNotes(Byte.parseByte("1"));
        moeGroomingDataRule.setClientPhoneNumber(Byte.parseByte("1"));
        moeGroomingDataRule.setMonthlyViewCustomization("clientFullName");
        return moeGroomingDataRule;
    }
}
