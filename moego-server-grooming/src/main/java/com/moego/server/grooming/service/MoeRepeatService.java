package com.moego.server.grooming.service;

import static com.moego.server.grooming.service.utils.RepeatUtil.checkAndComputeDate;
import static com.moego.server.grooming.service.utils.RepeatUtil.checkParams;
import static com.moego.server.grooming.service.utils.RepeatUtil.getDates;

import com.moego.common.enums.RepeatConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.AccountUtil;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.ConflictCheckDTO;
import com.moego.server.grooming.dto.ConflictDayInfoDTO;
import com.moego.server.grooming.dto.RepeatAppointmentDto;
import com.moego.server.grooming.mapper.MoeGroomingRepeatMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingRepeat;
import com.moego.server.grooming.mapstruct.RepeatMapper;
import com.moego.server.grooming.params.AddRepeatParams;
import com.moego.server.grooming.params.AppointmentCheckParams;
import com.moego.server.grooming.params.EditRepeatParams;
import com.moego.server.grooming.params.PreviewRepeatParams;
import com.moego.server.grooming.params.RepeatParams;
import com.moego.server.grooming.params.RepeatStaffInfoParams;
import com.moego.server.grooming.params.RepeatWithSSParams;
import com.moego.server.grooming.service.dto.DateAndStartTimeDTO;
import com.moego.server.grooming.web.params.MoeGroomingRepeatParam;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class MoeRepeatService {

    public static final String KEY_PREFIX_SS_FOR_REPEAT_TIMES = "grooming:repeatWithSsUsedTimes:";

    @Value("${smart-scheduling.repeat.free-user-available-times}")
    public int freeUsersAvailableTimes;

    @Value("${smart-scheduling.repeat.times-limit}")
    public int repeatWithSSTimesLimit;

    private final MoeGroomingRepeatMapper moeGroomingRepeatMapper;
    private final MoeGroomingAppointmentService appointmentService;
    private final MoeAppointmentQueryService appointmentQueryService;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final SmartScheduleService smartScheduleService;
    private final RedisUtil redisUtil;

    @Deprecated
    public List<ConflictDayInfoDTO> previewRepeatDay(PreviewRepeatParams repeatParams) {
        // 老 repeat 参数兼容
        if (Boolean.TRUE.equals(repeatParams.getIsExistAppt())) {
            // 基于已有预约，需要把 startsOn 往后移一个周期，times + 1
            LocalDate startsOn = LocalDate.parse(repeatParams.getStartsOn(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            switch (repeatParams.getRepeatType()) {
                case RepeatConst.REPEAT_TYPE_DAY -> startsOn = startsOn.plusDays(repeatParams.getRepeatEvery());
                case RepeatConst.REPEAT_TYPE_WEEK -> startsOn = startsOn.plusWeeks(repeatParams.getRepeatEvery());
                case RepeatConst.REPEAT_TYPE_MONTH -> startsOn = startsOn.plusDays(1);
                default -> {}
            }
            repeatParams.setStartsOn(startsOn.toString());
        }
        List<LocalDate> dates = checkAndComputeDate(repeatParams);

        if (CollectionUtils.isEmpty(dates)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "No matching date");
        }

        // 如果是编辑repeat 需要过滤已存在的date
        if (repeatParams.getRepeatId() != null) {
            List<RepeatAppointmentDto> appointments = appointmentQueryService.getRepeatAppointmentList(
                    repeatParams.getBusinessId(),
                    repeatParams.getRepeatId(),
                    RepeatConst.REPEAT_APPOINTMENT_TYPE_ALL,
                    true);
            if (appointments != null && !appointments.isEmpty()) {
                RepeatAppointmentDto appointment = appointments.stream()
                        .max(Comparator.comparing(RepeatAppointmentDto::getAppointmentDate))
                        .get();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate startDate = LocalDate.parse(repeatParams.getStartsOn(), formatter);
                LocalDate lastDate = LocalDate.parse(appointment.getAppointmentDate(), formatter);
                if (!lastDate.isBefore(startDate)) {
                    dates = dates.stream()
                            .filter(date -> date.isAfter(lastDate))
                            .collect(Collectors.toList());
                }
            }
            if (RepeatConst.REPEAT_END_TYPE_TIMES.equals(repeatParams.getType()) && repeatParams.getTimes() != null) {
                int existing = (appointments == null ? 0 : appointments.size());
                int newTimes = repeatParams.getTimes() - existing;
                if (0 < newTimes && newTimes < dates.size()) {
                    dates = dates.subList(0, newTimes);
                }
            }
        }

        return appointmentService.checkConflictForRepeat(dates, repeatParams);
    }

    /**
     * ss for repeat 查询 repeat 日期
     *
     * @param params 查询参数
     * @return 返回 repeat 日期信息
     */
    @Deprecated
    public List<ConflictDayInfoDTO> previewRepeatDaySmartScheduling(RepeatWithSSParams params) {
        Integer businessId = params.getBusinessId();
        // 使用普通 repeat 方法获取 repeat date
        List<ConflictDayInfoDTO> repeatDays = previewRepeatDay(params);
        // 不打开 ss for repeat 时，直接返回
        if (Boolean.FALSE.equals(params.getSmartSchedule())) {
            return repeatDays;
        }

        // 参数,套餐,可用次数校验
        checkSsParamsAndAvailable(businessId, params);

        // 2.判断是否ss for repeat，对所有时间进行smart schedule，Mobile会计算驾驶时间，Salon不会
        Map<String, List<LocalDate>> needSsDateMap = new HashMap<>();
        for (ConflictDayInfoDTO repeatDay : repeatDays) {
            List<LocalDate> needSsDateList = new LinkedList<>();
            LocalDate curDate = LocalDate.parse(repeatDay.getDate());
            needSsDateList.add(curDate);
            Integer before = params.getSsBeforeDays();
            Integer after = params.getSsAfterDays();

            // 2.1 将当前日期、前beforeDays和后afterDays，按先后顺序加入列表，比如repeatDate是4月5号，before=3，after=2，=> 4.5，4.4，4.6，4.3，4.7，4.2
            LocalDate beforeDate = curDate;
            LocalDate afterDate = curDate;
            while (before > 0 || after > 0) {
                if (before > 0) {
                    beforeDate = beforeDate.minusDays(1);
                    needSsDateList.add(beforeDate);
                    before--;
                }
                if (after > 0) {
                    afterDate = afterDate.plusDays(1);
                    needSsDateList.add(afterDate);
                    after--;
                }
            }
            needSsDateMap.put(repeatDay.getDate(), needSsDateList);
        }

        // 2.2 smart schedule，返回优化过的时间，如果无返回，说明没有找到优化的时间或不需要优化
        if (!CollectionUtils.isEmpty(needSsDateMap)) {
            // 计算总时长
            int serviceDuration = params.getRepeatStaffInfoParams().stream()
                    .mapToInt(RepeatStaffInfoParams::getServiceTime)
                    .sum();
            // 获取ss重新查询计算后的date和start time
            Map<String, DateAndStartTimeDTO> rescheduleTimeSlot =
                    smartScheduleService.smartScheduleForRepeat(params, serviceDuration, needSsDateMap);

            for (ConflictDayInfoDTO repeatDay : repeatDays) {
                // 2.3 把ss查询的date和start time更新到冲突的时间里
                if (rescheduleTimeSlot.containsKey(repeatDay.getDate())) {
                    DateAndStartTimeDTO dto = rescheduleTimeSlot.get(repeatDay.getDate());
                    repeatDay.setDate(dto.getDate());
                    repeatDay.setStartTime(dto.getStartTime());
                    repeatDay.setScheduleType(RepeatConst.REPEAT_SCHEDULE_TYPE_SS);
                    repeatDay.setIsNotConflict(true);
                }
            }
        }
        // 3.更新使用次数
        incrRepeatUsedTimesCache(businessId);

        return repeatDays;
    }

    /**
     * ss for repeat参数校验
     *
     * @param params
     */
    private void checkSsParamsAndAvailable(Integer businessId, RepeatWithSSParams params) {
        // 非 ss for repeat 不校验
        if (Boolean.FALSE.equals(params.getSmartSchedule())) {
            return;
        }
        // staffInfoParams 不能为空
        if (CollectionUtils.isEmpty(params.getRepeatStaffInfoParams())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }
        // ss for repeat 不支持多个 staffId
        Set<Integer> staffIdSet = new HashSet<>();
        for (RepeatStaffInfoParams staffInfo : params.getRepeatStaffInfoParams()) {
            staffIdSet.add(staffInfo.getStaffId());
            if (staffIdSet.size() > 1) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
            }
        }
        // customerId 检查
        if (params.getCustomerId() == null) {
            // smart schedule时必须有customerId
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }

        // before 和 after 的检查
        Integer before = params.getSsBeforeDays();
        Integer after = params.getSsAfterDays();
        if (before == null || after == null || before + after >= Math.min(params.getRepeatEvery(), 7)) {
            throw new CommonException(ResponseCodeEnum.SS_FOR_REPEAT_FREQUENCY_PARAMS_ERROR);
        }

        // 套餐是否可用
        boolean isAvailable = getRepeatWithSSAvailableTimes(businessId) > 0;
        if (!isAvailable) {
            throw new CommonException(ResponseCodeEnum.FREE_SS_FOR_REPEAT_TIMES_USED_OUT);
        }

        // repeat times限制：24次，非smart schedule限制次数和普通repeat的一致
        Integer times = params.getTimes();
        if (Objects.equals(params.getType(), RepeatConst.REPEAT_END_TYPE_END_DATE)) {
            List<LocalDate> dates = getDates(params);
            times = dates.size();
        }
        if (times > repeatWithSSTimesLimit) {
            throw new CommonException(ResponseCodeEnum.SS_FOR_REPEAT_TIMES_OVER_LIMIT);
        }
    }

    /**
     * 查询ss for repeat是否可用
     *
     * @param businessId
     * @return
     */
    public Integer getRepeatWithSSAvailableTimes(Integer businessId) {
        CompanyDto companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        Boolean isAllowSSRepeat = AccountUtil.getCompanyControlDto(
                        companyDto.getLevel(), companyDto.getIsNewPricing(), companyDto.getEnableStripeReader())
                .getAllowSmartScheduleForRepeat();
        if (!isAllowSSRepeat) {
            // 账号不支持ss for repeat，检查免费试用次数
            String cacheUsedTimesStr = redisUtil.get(KEY_PREFIX_SS_FOR_REPEAT_TIMES + businessId);
            if (StringUtils.isNotEmpty(cacheUsedTimesStr)) {
                return freeUsersAvailableTimes - Integer.parseInt(cacheUsedTimesStr);
            } else {
                return freeUsersAvailableTimes;
            }
        }
        return 999; // 69以上的用户允许直接使用，不考虑试用次数
    }

    /**
     * 记录商家试用次数
     *
     * @param businessId
     */
    public void incrRepeatUsedTimesCache(Integer businessId) {
        redisUtil.incrBy(KEY_PREFIX_SS_FOR_REPEAT_TIMES + businessId, 1);
    }

    // 0203 @Transactional
    public List<ConflictDayInfoDTO> checkConflictDay(List<AppointmentCheckParams> paramsList) {
        List<ConflictDayInfoDTO> conflictDayInfoDTOS = new LinkedList<>();

        for (AppointmentCheckParams appointmentCheckParams : paramsList) {
            appointmentCheckParams.setAppointmentDate(appointmentCheckParams.getAppointmentTime());

            ConflictDayInfoDTO conflictDayInfoDTO = new ConflictDayInfoDTO();

            conflictDayInfoDTO.setDate(appointmentCheckParams.getAppointmentTime());
            conflictDayInfoDTO.setDuration(appointmentCheckParams.getDuration());
            conflictDayInfoDTO.setStartTime(appointmentCheckParams.getStartTime());
            conflictDayInfoDTO.setStaffId(appointmentCheckParams.getStaffId());

            ConflictCheckDTO responseResult = appointmentService.checkConflict(appointmentCheckParams);

            conflictDayInfoDTO.setIsNotConflict(responseResult.getIsNotConflict());
            conflictDayInfoDTO.setConflictType(responseResult.getType());

            conflictDayInfoDTOS.add(conflictDayInfoDTO);
        }
        // 清理重复数据 确保每一天只有一条数据
        Map<String, ConflictDayInfoDTO> map = new HashMap<>();

        for (ConflictDayInfoDTO conflictDayInfoDTO : conflictDayInfoDTOS) {
            ConflictDayInfoDTO temp = map.get(conflictDayInfoDTO.getDate());
            if (temp == null) {
                map.put(conflictDayInfoDTO.getDate(), conflictDayInfoDTO);
                continue;
            }

            if (temp.getIsNotConflict()) {
                if (!conflictDayInfoDTO.getIsNotConflict()) {
                    temp.setIsNotConflict(conflictDayInfoDTO.getIsNotConflict());
                }
            }
        }

        Collection<ConflictDayInfoDTO> values = map.values();
        List<ConflictDayInfoDTO> res = new ArrayList<>(values.size() * 2);

        res.addAll(values);

        res.sort(Comparator.comparing(ConflictDayInfoDTO::getDate));

        return res;
    }

    // 0203 @Transactional
    public AddResultDTO addRepeatRule(AddRepeatParams addRepeatParams) {
        // 老 repeat 参数兼容
        if (Boolean.TRUE.equals(addRepeatParams.getIsExistAppt())) {
            // 基于已有预约，需要把 startsOn 往后移一个周期，times + 1
            LocalDate startsOn =
                    LocalDate.parse(addRepeatParams.getStartsOn(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            switch (addRepeatParams.getRepeatType()) {
                case RepeatConst.REPEAT_TYPE_DAY -> startsOn = startsOn.plusDays(addRepeatParams.getRepeatEvery());
                case RepeatConst.REPEAT_TYPE_WEEK -> startsOn = startsOn.plusWeeks(addRepeatParams.getRepeatEvery());
                case RepeatConst.REPEAT_TYPE_MONTH -> startsOn = startsOn.plusDays(1);
                default -> {}
            }
            addRepeatParams.setStartsOn(startsOn.toString());
        }
        List<LocalDate> dates = checkAndComputeDate(RepeatMapper.INSTANCE.toRepeatParams(addRepeatParams));

        // 添加repeat规则
        MoeGroomingRepeat newRecord = RepeatMapper.INSTANCE.repeatParamsToBean(addRepeatParams);
        if (addRepeatParams.getSmartSchedule() != null && addRepeatParams.getSmartSchedule()) {
            newRecord.setSsFlag(RepeatConst.REPEAT_SS_FLAG_TRUE);
        }

        // 根据 type 区分补齐 times 和 endOn
        // 次数获取最后一个 date
        if (CollectionUtils.isEmpty(dates)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "No matching date");
        }

        LocalDate date = dates.get(dates.size() - 1);
        Date endOn = DateUtil.convertLocalDateToDate(date);
        if (RepeatConst.REPEAT_END_TYPE_TIMES.equals(addRepeatParams.getType())) {
            // 如果是次数
            newRecord.setSetEndOn(endOn);
            // 如果isExistAppt为true则为基于已创建预约创建repeat，times需要+1
            if (addRepeatParams.getIsExistAppt() != null && addRepeatParams.getIsExistAppt()) {
                Integer times = newRecord.getTimes();
                newRecord.setTimes(times + 1);
            }
        } else {
            // 如果是截止日期
            newRecord.setTimes(dates.size());
            // 如果isExistAppt为true则为基于已创建预约创建repeat，times需要+1
            if (addRepeatParams.getIsExistAppt() != null && addRepeatParams.getIsExistAppt()) {
                Integer times = newRecord.getTimes();
                newRecord.setTimes(times + 1);
            }
            // 如果dates接近极限值去最后一个日期替代endsOn
            if (dates.size() == RepeatConst.MAX_REPEAT_TIMES) {
                newRecord.setSetEndOn(endOn);
            }
        }
        // 兼容：老版本创建时对这个字段赋值 1
        if (newRecord.getRepeatEvery() == null || newRecord.getRepeatEvery() == 0) {
            newRecord.setRepeatEvery(1);
        }

        newRecord.setCreateTime(CommonUtil.get10Timestamp());
        newRecord.setUpdateTime(CommonUtil.get10Timestamp());
        moeGroomingRepeatMapper.insertSelective(newRecord);
        AddResultDTO addResultDTO = new AddResultDTO();
        addResultDTO.setResult(true);
        addResultDTO.setId(newRecord.getId());
        return addResultDTO;
    }

    // 0203 @Transactional
    public Integer modifyRepeatRule(EditRepeatParams editRepeatParams) {
        if (!checkParams(editRepeatParams)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "params error");
        }
        MoeGroomingRepeat moeGroomingRepeat =
                moeGroomingRepeatMapper.selectByPrimaryKey(editRepeatParams.getRepeatId());
        if (moeGroomingRepeat == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        if (moeGroomingRepeat.getTimes() == RepeatConst.MAX_REPEAT_TIMES) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "repeat can not edit");
        }
        if (!moeGroomingRepeat.getBusinessId().equals(editRepeatParams.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found repeat");
        }

        RepeatParams repeatParams = RepeatMapper.INSTANCE.beanToRepeatParams(moeGroomingRepeat);
        // 更新字段
        repeatParams.setType(editRepeatParams.getType());
        repeatParams.setTimes(editRepeatParams.getTimes());
        repeatParams.setSetEndOn(editRepeatParams.getEndsOn());
        repeatParams.setStartsOn(DateUtil.dateToStr(moeGroomingRepeat.getStartsOn()));

        List<LocalDate> dates = getDates(repeatParams);

        if (CollectionUtils.isEmpty(dates)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "no match date");
        }

        moeGroomingRepeat = new MoeGroomingRepeat();
        moeGroomingRepeat.setType(editRepeatParams.getType());
        moeGroomingRepeat.setId(editRepeatParams.getRepeatId());
        moeGroomingRepeat.setUpdateTime(CommonUtil.get10Timestamp());

        LocalDate date = dates.get(dates.size() - 1);
        Date endOn = DateUtil.convertLocalDateToDate(date);
        if (RepeatConst.REPEAT_END_TYPE_TIMES.equals(editRepeatParams.getType())) {
            // 如果是次数
            moeGroomingRepeat.setSetEndOn(endOn);
            moeGroomingRepeat.setTimes(dates.size());
        } else {
            // 如果是截止日期
            moeGroomingRepeat.setTimes(dates.size());
            // 如果dates接近极限值去最后一个日期替代endsOn
            if (dates.size() == RepeatConst.MAX_REPEAT_TIMES) {
                moeGroomingRepeat.setSetEndOn(endOn);
            } else {
                moeGroomingRepeat.setSetEndOn(DateUtil.parseDate(editRepeatParams.getEndsOn(), "yyyy-MM-dd"));
            }
        }

        return moeGroomingRepeatMapper.updateByPrimaryKeySelective(moeGroomingRepeat);
    }

    public MoeGroomingRepeat queryRepeatRule(Integer tokenBusinessId, Integer repeatId) {
        MoeGroomingRepeat moeGroomingRepeat = moeGroomingRepeatMapper.selectByPrimaryKey(repeatId);
        if (moeGroomingRepeat == null || !tokenBusinessId.equals(moeGroomingRepeat.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found repeat info");
        }
        return moeGroomingRepeat;
    }

    // DM 相关接口，非业务逻辑
    public List<MoeGroomingRepeat> queryAllRepeatRuleByBusinessId(Integer businessId) {
        return moeGroomingRepeatMapper.queryByBusinessId(businessId);
    }

    // DM 相关接口，非业务逻辑
    public void modifyFullRepeatRule(Integer businessId, MoeGroomingRepeatParam param) {
        var groomingRepeat = moeGroomingRepeatMapper.selectByPrimaryKey(param.getId());
        if (groomingRepeat == null || !businessId.equals(groomingRepeat.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found repeat info");
        }
        moeGroomingRepeatMapper.updateByPrimaryKey(RepeatMapper.INSTANCE.paramToBean(param));
    }
}
