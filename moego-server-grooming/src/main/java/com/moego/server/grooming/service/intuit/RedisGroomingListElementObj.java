package com.moego.server.grooming.service.intuit;

import lombok.Data;

@Data
public class RedisGroomingListElementObj {

    private Integer businessId;
    private Integer groomingId;
    // 发起sync时预约的日期
    private String appointmentDate;
    /**
     * 错误次数，大于3次的errorCount不执行
     */
    private Integer errorCount;

    private Long syncTime;

    public RedisGroomingListElementObj() {}

    public RedisGroomingListElementObj(Integer businessId, Integer groomingId, String appointmentDate, Long syncTime) {
        this.businessId = businessId;
        this.groomingId = groomingId;
        this.appointmentDate = appointmentDate;
        this.errorCount = 0;
        this.syncTime = syncTime;
    }

    public RedisGroomingListElementObj(
            Integer businessId, Integer groomingId, String appointmentDate, Integer errorCount, Long syncTime) {
        this.businessId = businessId;
        this.groomingId = groomingId;
        this.appointmentDate = appointmentDate;
        this.errorCount = errorCount;
        this.syncTime = syncTime;
    }

    public void errorCountPlus() {
        this.errorCount++;
    }
}
