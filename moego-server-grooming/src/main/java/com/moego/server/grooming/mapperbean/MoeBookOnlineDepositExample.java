package com.moego.server.grooming.mapperbean;

import com.moego.server.grooming.dto.BookOnlineDepositDTO.PreAuth;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeBookOnlineDepositExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public MoeBookOnlineDepositExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> preauthInfoCriteria;

        protected List<Criterion> allCriteria;

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
            preauthInfoCriteria = new ArrayList<>();
        }

        public List<Criterion> getPreauthInfoCriteria() {
            return preauthInfoCriteria;
        }

        protected void addPreauthInfoCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            preauthInfoCriteria.add(new Criterion(
                    condition,
                    value,
                    "com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler"));
            allCriteria = null;
        }

        protected void addPreauthInfoCriterion(String condition, PreAuth value1, PreAuth value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            preauthInfoCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler"));
            allCriteria = null;
        }

        public boolean isValid() {
            return criteria.size() > 0 || preauthInfoCriteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            if (allCriteria == null) {
                allCriteria = new ArrayList<>();
                allCriteria.addAll(criteria);
                allCriteria.addAll(preauthInfoCriteria);
            }
            return allCriteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
            allCriteria = null;
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdIsNull() {
            addCriterion("grooming_id is null");
            return (Criteria) this;
        }

        public Criteria andGroomingIdIsNotNull() {
            addCriterion("grooming_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroomingIdEqualTo(Integer value) {
            addCriterion("grooming_id =", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdNotEqualTo(Integer value) {
            addCriterion("grooming_id <>", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdGreaterThan(Integer value) {
            addCriterion("grooming_id >", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("grooming_id >=", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdLessThan(Integer value) {
            addCriterion("grooming_id <", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdLessThanOrEqualTo(Integer value) {
            addCriterion("grooming_id <=", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdIn(List<Integer> values) {
            addCriterion("grooming_id in", values, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdNotIn(List<Integer> values) {
            addCriterion("grooming_id not in", values, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdBetween(Integer value1, Integer value2) {
            addCriterion("grooming_id between", value1, value2, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdNotBetween(Integer value1, Integer value2) {
            addCriterion("grooming_id not between", value1, value2, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGuidIsNull() {
            addCriterion("guid is null");
            return (Criteria) this;
        }

        public Criteria andGuidIsNotNull() {
            addCriterion("guid is not null");
            return (Criteria) this;
        }

        public Criteria andGuidEqualTo(String value) {
            addCriterion("guid =", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotEqualTo(String value) {
            addCriterion("guid <>", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidGreaterThan(String value) {
            addCriterion("guid >", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidGreaterThanOrEqualTo(String value) {
            addCriterion("guid >=", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidLessThan(String value) {
            addCriterion("guid <", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidLessThanOrEqualTo(String value) {
            addCriterion("guid <=", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidLike(String value) {
            addCriterion("guid like", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotLike(String value) {
            addCriterion("guid not like", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidIn(List<String> values) {
            addCriterion("guid in", values, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotIn(List<String> values) {
            addCriterion("guid not in", values, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidBetween(String value1, String value2) {
            addCriterion("guid between", value1, value2, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotBetween(String value1, String value2) {
            addCriterion("guid not between", value1, value2, "guid");
            return (Criteria) this;
        }

        public Criteria andPaymentIdIsNull() {
            addCriterion("payment_id is null");
            return (Criteria) this;
        }

        public Criteria andPaymentIdIsNotNull() {
            addCriterion("payment_id is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentIdEqualTo(Integer value) {
            addCriterion("payment_id =", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotEqualTo(Integer value) {
            addCriterion("payment_id <>", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdGreaterThan(Integer value) {
            addCriterion("payment_id >", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_id >=", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdLessThan(Integer value) {
            addCriterion("payment_id <", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdLessThanOrEqualTo(Integer value) {
            addCriterion("payment_id <=", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdIn(List<Integer> values) {
            addCriterion("payment_id in", values, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotIn(List<Integer> values) {
            addCriterion("payment_id not in", values, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdBetween(Integer value1, Integer value2) {
            addCriterion("payment_id between", value1, value2, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_id not between", value1, value2, "paymentId");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andBookingFeeIsNull() {
            addCriterion("booking_fee is null");
            return (Criteria) this;
        }

        public Criteria andBookingFeeIsNotNull() {
            addCriterion("booking_fee is not null");
            return (Criteria) this;
        }

        public Criteria andBookingFeeEqualTo(BigDecimal value) {
            addCriterion("booking_fee =", value, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andBookingFeeNotEqualTo(BigDecimal value) {
            addCriterion("booking_fee <>", value, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andBookingFeeGreaterThan(BigDecimal value) {
            addCriterion("booking_fee >", value, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andBookingFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_fee >=", value, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andBookingFeeLessThan(BigDecimal value) {
            addCriterion("booking_fee <", value, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andBookingFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_fee <=", value, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andBookingFeeIn(List<BigDecimal> values) {
            addCriterion("booking_fee in", values, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andBookingFeeNotIn(List<BigDecimal> values) {
            addCriterion("booking_fee not in", values, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andBookingFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_fee between", value1, value2, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andBookingFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_fee not between", value1, value2, "bookingFee");
            return (Criteria) this;
        }

        public Criteria andTipsAmountIsNull() {
            addCriterion("tips_amount is null");
            return (Criteria) this;
        }

        public Criteria andTipsAmountIsNotNull() {
            addCriterion("tips_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTipsAmountEqualTo(BigDecimal value) {
            addCriterion("tips_amount =", value, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andTipsAmountNotEqualTo(BigDecimal value) {
            addCriterion("tips_amount <>", value, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andTipsAmountGreaterThan(BigDecimal value) {
            addCriterion("tips_amount >", value, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andTipsAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tips_amount >=", value, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andTipsAmountLessThan(BigDecimal value) {
            addCriterion("tips_amount <", value, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andTipsAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tips_amount <=", value, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andTipsAmountIn(List<BigDecimal> values) {
            addCriterion("tips_amount in", values, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andTipsAmountNotIn(List<BigDecimal> values) {
            addCriterion("tips_amount not in", values, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andTipsAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tips_amount between", value1, value2, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andTipsAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tips_amount not between", value1, value2, "tipsAmount");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeIsNull() {
            addCriterion("convenience_fee is null");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeIsNotNull() {
            addCriterion("convenience_fee is not null");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeEqualTo(BigDecimal value) {
            addCriterion("convenience_fee =", value, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeNotEqualTo(BigDecimal value) {
            addCriterion("convenience_fee <>", value, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeGreaterThan(BigDecimal value) {
            addCriterion("convenience_fee >", value, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("convenience_fee >=", value, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeLessThan(BigDecimal value) {
            addCriterion("convenience_fee <", value, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("convenience_fee <=", value, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeIn(List<BigDecimal> values) {
            addCriterion("convenience_fee in", values, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeNotIn(List<BigDecimal> values) {
            addCriterion("convenience_fee not in", values, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("convenience_fee between", value1, value2, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andConvenienceFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("convenience_fee not between", value1, value2, "convenienceFee");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andServiceTotalIsNull() {
            addCriterion("service_total is null");
            return (Criteria) this;
        }

        public Criteria andServiceTotalIsNotNull() {
            addCriterion("service_total is not null");
            return (Criteria) this;
        }

        public Criteria andServiceTotalEqualTo(BigDecimal value) {
            addCriterion("service_total =", value, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andServiceTotalNotEqualTo(BigDecimal value) {
            addCriterion("service_total <>", value, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andServiceTotalGreaterThan(BigDecimal value) {
            addCriterion("service_total >", value, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andServiceTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_total >=", value, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andServiceTotalLessThan(BigDecimal value) {
            addCriterion("service_total <", value, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andServiceTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_total <=", value, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andServiceTotalIn(List<BigDecimal> values) {
            addCriterion("service_total in", values, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andServiceTotalNotIn(List<BigDecimal> values) {
            addCriterion("service_total not in", values, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andServiceTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_total between", value1, value2, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andServiceTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_total not between", value1, value2, "serviceTotal");
            return (Criteria) this;
        }

        public Criteria andTaxAmountIsNull() {
            addCriterion("tax_amount is null");
            return (Criteria) this;
        }

        public Criteria andTaxAmountIsNotNull() {
            addCriterion("tax_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTaxAmountEqualTo(BigDecimal value) {
            addCriterion("tax_amount =", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountNotEqualTo(BigDecimal value) {
            addCriterion("tax_amount <>", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountGreaterThan(BigDecimal value) {
            addCriterion("tax_amount >", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_amount >=", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountLessThan(BigDecimal value) {
            addCriterion("tax_amount <", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_amount <=", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountIn(List<BigDecimal> values) {
            addCriterion("tax_amount in", values, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountNotIn(List<BigDecimal> values) {
            addCriterion("tax_amount not in", values, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_amount between", value1, value2, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_amount not between", value1, value2, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountIsNull() {
            addCriterion("service_charge_amount is null");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountIsNotNull() {
            addCriterion("service_charge_amount is not null");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountEqualTo(BigDecimal value) {
            addCriterion("service_charge_amount =", value, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountNotEqualTo(BigDecimal value) {
            addCriterion("service_charge_amount <>", value, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountGreaterThan(BigDecimal value) {
            addCriterion("service_charge_amount >", value, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_charge_amount >=", value, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountLessThan(BigDecimal value) {
            addCriterion("service_charge_amount <", value, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_charge_amount <=", value, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountIn(List<BigDecimal> values) {
            addCriterion("service_charge_amount in", values, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountNotIn(List<BigDecimal> values) {
            addCriterion("service_charge_amount not in", values, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_charge_amount between", value1, value2, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceChargeAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_charge_amount not between", value1, value2, "serviceChargeAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountIsNull() {
            addCriterion("discount_amount is null");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountIsNotNull() {
            addCriterion("discount_amount is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountEqualTo(BigDecimal value) {
            addCriterion("discount_amount =", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountNotEqualTo(BigDecimal value) {
            addCriterion("discount_amount <>", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountGreaterThan(BigDecimal value) {
            addCriterion("discount_amount >", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_amount >=", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountLessThan(BigDecimal value) {
            addCriterion("discount_amount <", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_amount <=", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountIn(List<BigDecimal> values) {
            addCriterion("discount_amount in", values, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountNotIn(List<BigDecimal> values) {
            addCriterion("discount_amount not in", values, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_amount between", value1, value2, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_amount not between", value1, value2, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdIsNull() {
            addCriterion("discount_code_id is null");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdIsNotNull() {
            addCriterion("discount_code_id is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdEqualTo(Long value) {
            addCriterion("discount_code_id =", value, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdNotEqualTo(Long value) {
            addCriterion("discount_code_id <>", value, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdGreaterThan(Long value) {
            addCriterion("discount_code_id >", value, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("discount_code_id >=", value, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdLessThan(Long value) {
            addCriterion("discount_code_id <", value, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdLessThanOrEqualTo(Long value) {
            addCriterion("discount_code_id <=", value, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdIn(List<Long> values) {
            addCriterion("discount_code_id in", values, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdNotIn(List<Long> values) {
            addCriterion("discount_code_id not in", values, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdBetween(Long value1, Long value2) {
            addCriterion("discount_code_id between", value1, value2, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIdNotBetween(Long value1, Long value2) {
            addCriterion("discount_code_id not between", value1, value2, "discountCodeId");
            return (Criteria) this;
        }

        public Criteria andDepositTypeIsNull() {
            addCriterion("deposit_type is null");
            return (Criteria) this;
        }

        public Criteria andDepositTypeIsNotNull() {
            addCriterion("deposit_type is not null");
            return (Criteria) this;
        }

        public Criteria andDepositTypeEqualTo(Byte value) {
            addCriterion("deposit_type =", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeNotEqualTo(Byte value) {
            addCriterion("deposit_type <>", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeGreaterThan(Byte value) {
            addCriterion("deposit_type >", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("deposit_type >=", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeLessThan(Byte value) {
            addCriterion("deposit_type <", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeLessThanOrEqualTo(Byte value) {
            addCriterion("deposit_type <=", value, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeIn(List<Byte> values) {
            addCriterion("deposit_type in", values, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeNotIn(List<Byte> values) {
            addCriterion("deposit_type not in", values, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeBetween(Byte value1, Byte value2) {
            addCriterion("deposit_type between", value1, value2, "depositType");
            return (Criteria) this;
        }

        public Criteria andDepositTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("deposit_type not between", value1, value2, "depositType");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdIsNull() {
            addCriterion("booking_request_id is null");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdIsNotNull() {
            addCriterion("booking_request_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdEqualTo(Long value) {
            addCriterion("booking_request_id =", value, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdNotEqualTo(Long value) {
            addCriterion("booking_request_id <>", value, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdGreaterThan(Long value) {
            addCriterion("booking_request_id >", value, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdGreaterThanOrEqualTo(Long value) {
            addCriterion("booking_request_id >=", value, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdLessThan(Long value) {
            addCriterion("booking_request_id <", value, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdLessThanOrEqualTo(Long value) {
            addCriterion("booking_request_id <=", value, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdIn(List<Long> values) {
            addCriterion("booking_request_id in", values, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdNotIn(List<Long> values) {
            addCriterion("booking_request_id not in", values, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdBetween(Long value1, Long value2) {
            addCriterion("booking_request_id between", value1, value2, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andBookingRequestIdNotBetween(Long value1, Long value2) {
            addCriterion("booking_request_id not between", value1, value2, "bookingRequestId");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoIsNull() {
            addCriterion("preauth_info is null");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoIsNotNull() {
            addCriterion("preauth_info is not null");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoEqualTo(PreAuth value) {
            addPreauthInfoCriterion("preauth_info =", value, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoNotEqualTo(PreAuth value) {
            addPreauthInfoCriterion("preauth_info <>", value, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoGreaterThan(PreAuth value) {
            addPreauthInfoCriterion("preauth_info >", value, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoGreaterThanOrEqualTo(PreAuth value) {
            addPreauthInfoCriterion("preauth_info >=", value, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoLessThan(PreAuth value) {
            addPreauthInfoCriterion("preauth_info <", value, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoLessThanOrEqualTo(PreAuth value) {
            addPreauthInfoCriterion("preauth_info <=", value, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoLike(PreAuth value) {
            addPreauthInfoCriterion("preauth_info like", value, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoNotLike(PreAuth value) {
            addPreauthInfoCriterion("preauth_info not like", value, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoIn(List<PreAuth> values) {
            addPreauthInfoCriterion("preauth_info in", values, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoNotIn(List<PreAuth> values) {
            addPreauthInfoCriterion("preauth_info not in", values, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoBetween(PreAuth value1, PreAuth value2) {
            addPreauthInfoCriterion("preauth_info between", value1, value2, "preauthInfo");
            return (Criteria) this;
        }

        public Criteria andPreauthInfoNotBetween(PreAuth value1, PreAuth value2) {
            addPreauthInfoCriterion("preauth_info not between", value1, value2, "preauthInfo");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
