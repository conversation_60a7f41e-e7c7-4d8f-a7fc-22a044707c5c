package com.moego.server.grooming.mapperbean;

import com.moego.server.grooming.dto.BookOnlineDepositDTO.PreAuth;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_deposit
 */
public class MoeBookOnlineDeposit {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     * Database Column Remarks:
     *   payment guid for prepay, deposit is start with de_, and other is full pay
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.guid
     *
     * @mbg.generated
     */
    private String guid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.payment_id
     *
     * @mbg.generated
     */
    private Integer paymentId;

    /**
     * Database Column Remarks:
     *   prepay amount, total exclude booking fee
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.amount
     *
     * @mbg.generated
     */
    private BigDecimal amount;

    /**
     * Database Column Remarks:
     *   extra fee when client prepay
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.booking_fee
     *
     * @mbg.generated
     */
    private BigDecimal bookingFee;

    /**
     * Database Column Remarks:
     *   tips amount, only in full pay
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.tips_amount
     *
     * @mbg.generated
     */
    private BigDecimal tipsAmount;

    /**
     * Database Column Remarks:
     *   convenience fee of current deposit record
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.convenience_fee
     *
     * @mbg.generated
     */
    private BigDecimal convenienceFee;

    /**
     * Database Column Remarks:
     *   PROCESSING = 1; REQUIRE_PAYMENT_METHOD = 2; REQUIRE_CONFIRM = 3; REQUIRE_CAPTURE = 4; PROCESSING_CAPTURE = 5; PAID = 6; REFUNDED = 7; CANCEL = 8; FAILED = -1;
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   prepay service total amount
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.service_total
     *
     * @mbg.generated
     */
    private BigDecimal serviceTotal;

    /**
     * Database Column Remarks:
     *   prepay tax amount
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.tax_amount
     *
     * @mbg.generated
     */
    private BigDecimal taxAmount;

    /**
     * Database Column Remarks:
     *   service charge amount
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.service_charge_amount
     *
     * @mbg.generated
     */
    private BigDecimal serviceChargeAmount;

    /**
     * Database Column Remarks:
     *   discount amount
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.discount_amount
     *
     * @mbg.generated
     */
    private BigDecimal discountAmount;

    /**
     * Database Column Remarks:
     *   discount code id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.discount_code_id
     *
     * @mbg.generated
     */
    private Long discountCodeId;

    /**
     * Database Column Remarks:
     *   #DepositPaymentTypeEnum
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.deposit_type
     *
     * @mbg.generated
     */
    private Byte depositType;

    /**
     * Database Column Remarks:
     *   booking request id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.booking_request_id
     *
     * @mbg.generated
     */
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   preauth info, see com.moego.server.grooming.dto.BookOnlineDepositDTO.PreAuth
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_deposit.preauth_info
     *
     * @mbg.generated
     */
    private PreAuth preauthInfo;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.id
     *
     * @return the value of moe_book_online_deposit.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.id
     *
     * @param id the value for moe_book_online_deposit.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.business_id
     *
     * @return the value of moe_book_online_deposit.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.business_id
     *
     * @param businessId the value for moe_book_online_deposit.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.company_id
     *
     * @return the value of moe_book_online_deposit.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.company_id
     *
     * @param companyId the value for moe_book_online_deposit.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.grooming_id
     *
     * @return the value of moe_book_online_deposit.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.grooming_id
     *
     * @param groomingId the value for moe_book_online_deposit.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.guid
     *
     * @return the value of moe_book_online_deposit.guid
     *
     * @mbg.generated
     */
    public String getGuid() {
        return guid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.guid
     *
     * @param guid the value for moe_book_online_deposit.guid
     *
     * @mbg.generated
     */
    public void setGuid(String guid) {
        this.guid = guid == null ? null : guid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.payment_id
     *
     * @return the value of moe_book_online_deposit.payment_id
     *
     * @mbg.generated
     */
    public Integer getPaymentId() {
        return paymentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.payment_id
     *
     * @param paymentId the value for moe_book_online_deposit.payment_id
     *
     * @mbg.generated
     */
    public void setPaymentId(Integer paymentId) {
        this.paymentId = paymentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.amount
     *
     * @return the value of moe_book_online_deposit.amount
     *
     * @mbg.generated
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.amount
     *
     * @param amount the value for moe_book_online_deposit.amount
     *
     * @mbg.generated
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.booking_fee
     *
     * @return the value of moe_book_online_deposit.booking_fee
     *
     * @mbg.generated
     */
    public BigDecimal getBookingFee() {
        return bookingFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.booking_fee
     *
     * @param bookingFee the value for moe_book_online_deposit.booking_fee
     *
     * @mbg.generated
     */
    public void setBookingFee(BigDecimal bookingFee) {
        this.bookingFee = bookingFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.tips_amount
     *
     * @return the value of moe_book_online_deposit.tips_amount
     *
     * @mbg.generated
     */
    public BigDecimal getTipsAmount() {
        return tipsAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.tips_amount
     *
     * @param tipsAmount the value for moe_book_online_deposit.tips_amount
     *
     * @mbg.generated
     */
    public void setTipsAmount(BigDecimal tipsAmount) {
        this.tipsAmount = tipsAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.convenience_fee
     *
     * @return the value of moe_book_online_deposit.convenience_fee
     *
     * @mbg.generated
     */
    public BigDecimal getConvenienceFee() {
        return convenienceFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.convenience_fee
     *
     * @param convenienceFee the value for moe_book_online_deposit.convenience_fee
     *
     * @mbg.generated
     */
    public void setConvenienceFee(BigDecimal convenienceFee) {
        this.convenienceFee = convenienceFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.status
     *
     * @return the value of moe_book_online_deposit.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.status
     *
     * @param status the value for moe_book_online_deposit.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.create_time
     *
     * @return the value of moe_book_online_deposit.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.create_time
     *
     * @param createTime the value for moe_book_online_deposit.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.update_time
     *
     * @return the value of moe_book_online_deposit.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.update_time
     *
     * @param updateTime the value for moe_book_online_deposit.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.service_total
     *
     * @return the value of moe_book_online_deposit.service_total
     *
     * @mbg.generated
     */
    public BigDecimal getServiceTotal() {
        return serviceTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.service_total
     *
     * @param serviceTotal the value for moe_book_online_deposit.service_total
     *
     * @mbg.generated
     */
    public void setServiceTotal(BigDecimal serviceTotal) {
        this.serviceTotal = serviceTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.tax_amount
     *
     * @return the value of moe_book_online_deposit.tax_amount
     *
     * @mbg.generated
     */
    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.tax_amount
     *
     * @param taxAmount the value for moe_book_online_deposit.tax_amount
     *
     * @mbg.generated
     */
    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.service_charge_amount
     *
     * @return the value of moe_book_online_deposit.service_charge_amount
     *
     * @mbg.generated
     */
    public BigDecimal getServiceChargeAmount() {
        return serviceChargeAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.service_charge_amount
     *
     * @param serviceChargeAmount the value for moe_book_online_deposit.service_charge_amount
     *
     * @mbg.generated
     */
    public void setServiceChargeAmount(BigDecimal serviceChargeAmount) {
        this.serviceChargeAmount = serviceChargeAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.discount_amount
     *
     * @return the value of moe_book_online_deposit.discount_amount
     *
     * @mbg.generated
     */
    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.discount_amount
     *
     * @param discountAmount the value for moe_book_online_deposit.discount_amount
     *
     * @mbg.generated
     */
    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.discount_code_id
     *
     * @return the value of moe_book_online_deposit.discount_code_id
     *
     * @mbg.generated
     */
    public Long getDiscountCodeId() {
        return discountCodeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.discount_code_id
     *
     * @param discountCodeId the value for moe_book_online_deposit.discount_code_id
     *
     * @mbg.generated
     */
    public void setDiscountCodeId(Long discountCodeId) {
        this.discountCodeId = discountCodeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.deposit_type
     *
     * @return the value of moe_book_online_deposit.deposit_type
     *
     * @mbg.generated
     */
    public Byte getDepositType() {
        return depositType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.deposit_type
     *
     * @param depositType the value for moe_book_online_deposit.deposit_type
     *
     * @mbg.generated
     */
    public void setDepositType(Byte depositType) {
        this.depositType = depositType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.booking_request_id
     *
     * @return the value of moe_book_online_deposit.booking_request_id
     *
     * @mbg.generated
     */
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.booking_request_id
     *
     * @param bookingRequestId the value for moe_book_online_deposit.booking_request_id
     *
     * @mbg.generated
     */
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_deposit.preauth_info
     *
     * @return the value of moe_book_online_deposit.preauth_info
     *
     * @mbg.generated
     */
    public PreAuth getPreauthInfo() {
        return preauthInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_deposit.preauth_info
     *
     * @param preauthInfo the value for moe_book_online_deposit.preauth_info
     *
     * @mbg.generated
     */
    public void setPreauthInfo(PreAuth preauthInfo) {
        this.preauthInfo = preauthInfo;
    }
}
