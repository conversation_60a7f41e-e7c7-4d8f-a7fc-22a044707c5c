package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_notification
 */
public class MoeBookOnlineNotification {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   1 email 2 message 3 email&message 4nothing
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.submit_client_type
     *
     * @mbg.generated
     */
    private Byte submitClientType;

    /**
     * Database Column Remarks:
     *   1 email  4nothing
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.submit_business_type
     *
     * @mbg.generated
     */
    private Byte submitBusinessType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.submit_email_subject_template
     *
     * @mbg.generated
     */
    private String submitEmailSubjectTemplate;

    /**
     * Database Column Remarks:
     *   1 email 2 message 3 email&message 4nothing
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.accept_client_type
     *
     * @mbg.generated
     */
    private Byte acceptClientType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.accept_email_subject_template
     *
     * @mbg.generated
     */
    private String acceptEmailSubjectTemplate;

    /**
     * Database Column Remarks:
     *   1 email  4nothing
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.accept_business_type
     *
     * @mbg.generated
     */
    private Byte acceptBusinessType;

    /**
     * Database Column Remarks:
     *   1 email 2 message 3 email&message 4nothing
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.auto_move_client_type
     *
     * @mbg.generated
     */
    private Byte autoMoveClientType;

    /**
     * Database Column Remarks:
     *   1 email  4nothing
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.auto_move_business_type
     *
     * @mbg.generated
     */
    private Byte autoMoveBusinessType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.auto_move_email_subject_template
     *
     * @mbg.generated
     */
    private String autoMoveEmailSubjectTemplate;

    /**
     * Database Column Remarks:
     *   1 email 2 message 3 email&message 4nothing
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.decline_client_type
     *
     * @mbg.generated
     */
    private Byte declineClientType;

    /**
     * Database Column Remarks:
     *   1 email  4nothing
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.decline_business_type
     *
     * @mbg.generated
     */
    private Byte declineBusinessType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.decline_email_subject_template
     *
     * @mbg.generated
     */
    private String declineEmailSubjectTemplate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.submit_template
     *
     * @mbg.generated
     */
    private String submitTemplate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.submit_email_content_template
     *
     * @mbg.generated
     */
    private String submitEmailContentTemplate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.accept_template
     *
     * @mbg.generated
     */
    private String acceptTemplate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.accept_email_content_template
     *
     * @mbg.generated
     */
    private String acceptEmailContentTemplate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.auto_move_template
     *
     * @mbg.generated
     */
    private String autoMoveTemplate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.auto_move_email_content_template
     *
     * @mbg.generated
     */
    private String autoMoveEmailContentTemplate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.decline_template
     *
     * @mbg.generated
     */
    private String declineTemplate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_notification.decline_email_content_template
     *
     * @mbg.generated
     */
    private String declineEmailContentTemplate;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.id
     *
     * @return the value of moe_book_online_notification.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.id
     *
     * @param id the value for moe_book_online_notification.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.business_id
     *
     * @return the value of moe_book_online_notification.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.business_id
     *
     * @param businessId the value for moe_book_online_notification.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.submit_client_type
     *
     * @return the value of moe_book_online_notification.submit_client_type
     *
     * @mbg.generated
     */
    public Byte getSubmitClientType() {
        return submitClientType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.submit_client_type
     *
     * @param submitClientType the value for moe_book_online_notification.submit_client_type
     *
     * @mbg.generated
     */
    public void setSubmitClientType(Byte submitClientType) {
        this.submitClientType = submitClientType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.submit_business_type
     *
     * @return the value of moe_book_online_notification.submit_business_type
     *
     * @mbg.generated
     */
    public Byte getSubmitBusinessType() {
        return submitBusinessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.submit_business_type
     *
     * @param submitBusinessType the value for moe_book_online_notification.submit_business_type
     *
     * @mbg.generated
     */
    public void setSubmitBusinessType(Byte submitBusinessType) {
        this.submitBusinessType = submitBusinessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.submit_email_subject_template
     *
     * @return the value of moe_book_online_notification.submit_email_subject_template
     *
     * @mbg.generated
     */
    public String getSubmitEmailSubjectTemplate() {
        return submitEmailSubjectTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.submit_email_subject_template
     *
     * @param submitEmailSubjectTemplate the value for moe_book_online_notification.submit_email_subject_template
     *
     * @mbg.generated
     */
    public void setSubmitEmailSubjectTemplate(String submitEmailSubjectTemplate) {
        this.submitEmailSubjectTemplate = submitEmailSubjectTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.accept_client_type
     *
     * @return the value of moe_book_online_notification.accept_client_type
     *
     * @mbg.generated
     */
    public Byte getAcceptClientType() {
        return acceptClientType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.accept_client_type
     *
     * @param acceptClientType the value for moe_book_online_notification.accept_client_type
     *
     * @mbg.generated
     */
    public void setAcceptClientType(Byte acceptClientType) {
        this.acceptClientType = acceptClientType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.accept_email_subject_template
     *
     * @return the value of moe_book_online_notification.accept_email_subject_template
     *
     * @mbg.generated
     */
    public String getAcceptEmailSubjectTemplate() {
        return acceptEmailSubjectTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.accept_email_subject_template
     *
     * @param acceptEmailSubjectTemplate the value for moe_book_online_notification.accept_email_subject_template
     *
     * @mbg.generated
     */
    public void setAcceptEmailSubjectTemplate(String acceptEmailSubjectTemplate) {
        this.acceptEmailSubjectTemplate = acceptEmailSubjectTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.accept_business_type
     *
     * @return the value of moe_book_online_notification.accept_business_type
     *
     * @mbg.generated
     */
    public Byte getAcceptBusinessType() {
        return acceptBusinessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.accept_business_type
     *
     * @param acceptBusinessType the value for moe_book_online_notification.accept_business_type
     *
     * @mbg.generated
     */
    public void setAcceptBusinessType(Byte acceptBusinessType) {
        this.acceptBusinessType = acceptBusinessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.auto_move_client_type
     *
     * @return the value of moe_book_online_notification.auto_move_client_type
     *
     * @mbg.generated
     */
    public Byte getAutoMoveClientType() {
        return autoMoveClientType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.auto_move_client_type
     *
     * @param autoMoveClientType the value for moe_book_online_notification.auto_move_client_type
     *
     * @mbg.generated
     */
    public void setAutoMoveClientType(Byte autoMoveClientType) {
        this.autoMoveClientType = autoMoveClientType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.auto_move_business_type
     *
     * @return the value of moe_book_online_notification.auto_move_business_type
     *
     * @mbg.generated
     */
    public Byte getAutoMoveBusinessType() {
        return autoMoveBusinessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.auto_move_business_type
     *
     * @param autoMoveBusinessType the value for moe_book_online_notification.auto_move_business_type
     *
     * @mbg.generated
     */
    public void setAutoMoveBusinessType(Byte autoMoveBusinessType) {
        this.autoMoveBusinessType = autoMoveBusinessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.auto_move_email_subject_template
     *
     * @return the value of moe_book_online_notification.auto_move_email_subject_template
     *
     * @mbg.generated
     */
    public String getAutoMoveEmailSubjectTemplate() {
        return autoMoveEmailSubjectTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.auto_move_email_subject_template
     *
     * @param autoMoveEmailSubjectTemplate the value for moe_book_online_notification.auto_move_email_subject_template
     *
     * @mbg.generated
     */
    public void setAutoMoveEmailSubjectTemplate(String autoMoveEmailSubjectTemplate) {
        this.autoMoveEmailSubjectTemplate = autoMoveEmailSubjectTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.decline_client_type
     *
     * @return the value of moe_book_online_notification.decline_client_type
     *
     * @mbg.generated
     */
    public Byte getDeclineClientType() {
        return declineClientType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.decline_client_type
     *
     * @param declineClientType the value for moe_book_online_notification.decline_client_type
     *
     * @mbg.generated
     */
    public void setDeclineClientType(Byte declineClientType) {
        this.declineClientType = declineClientType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.decline_business_type
     *
     * @return the value of moe_book_online_notification.decline_business_type
     *
     * @mbg.generated
     */
    public Byte getDeclineBusinessType() {
        return declineBusinessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.decline_business_type
     *
     * @param declineBusinessType the value for moe_book_online_notification.decline_business_type
     *
     * @mbg.generated
     */
    public void setDeclineBusinessType(Byte declineBusinessType) {
        this.declineBusinessType = declineBusinessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.decline_email_subject_template
     *
     * @return the value of moe_book_online_notification.decline_email_subject_template
     *
     * @mbg.generated
     */
    public String getDeclineEmailSubjectTemplate() {
        return declineEmailSubjectTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.decline_email_subject_template
     *
     * @param declineEmailSubjectTemplate the value for moe_book_online_notification.decline_email_subject_template
     *
     * @mbg.generated
     */
    public void setDeclineEmailSubjectTemplate(String declineEmailSubjectTemplate) {
        this.declineEmailSubjectTemplate = declineEmailSubjectTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.company_id
     *
     * @return the value of moe_book_online_notification.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.company_id
     *
     * @param companyId the value for moe_book_online_notification.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.submit_template
     *
     * @return the value of moe_book_online_notification.submit_template
     *
     * @mbg.generated
     */
    public String getSubmitTemplate() {
        return submitTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.submit_template
     *
     * @param submitTemplate the value for moe_book_online_notification.submit_template
     *
     * @mbg.generated
     */
    public void setSubmitTemplate(String submitTemplate) {
        this.submitTemplate = submitTemplate == null ? null : submitTemplate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.submit_email_content_template
     *
     * @return the value of moe_book_online_notification.submit_email_content_template
     *
     * @mbg.generated
     */
    public String getSubmitEmailContentTemplate() {
        return submitEmailContentTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.submit_email_content_template
     *
     * @param submitEmailContentTemplate the value for moe_book_online_notification.submit_email_content_template
     *
     * @mbg.generated
     */
    public void setSubmitEmailContentTemplate(String submitEmailContentTemplate) {
        this.submitEmailContentTemplate = submitEmailContentTemplate == null ? null : submitEmailContentTemplate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.accept_template
     *
     * @return the value of moe_book_online_notification.accept_template
     *
     * @mbg.generated
     */
    public String getAcceptTemplate() {
        return acceptTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.accept_template
     *
     * @param acceptTemplate the value for moe_book_online_notification.accept_template
     *
     * @mbg.generated
     */
    public void setAcceptTemplate(String acceptTemplate) {
        this.acceptTemplate = acceptTemplate == null ? null : acceptTemplate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.accept_email_content_template
     *
     * @return the value of moe_book_online_notification.accept_email_content_template
     *
     * @mbg.generated
     */
    public String getAcceptEmailContentTemplate() {
        return acceptEmailContentTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.accept_email_content_template
     *
     * @param acceptEmailContentTemplate the value for moe_book_online_notification.accept_email_content_template
     *
     * @mbg.generated
     */
    public void setAcceptEmailContentTemplate(String acceptEmailContentTemplate) {
        this.acceptEmailContentTemplate = acceptEmailContentTemplate == null ? null : acceptEmailContentTemplate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.auto_move_template
     *
     * @return the value of moe_book_online_notification.auto_move_template
     *
     * @mbg.generated
     */
    public String getAutoMoveTemplate() {
        return autoMoveTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.auto_move_template
     *
     * @param autoMoveTemplate the value for moe_book_online_notification.auto_move_template
     *
     * @mbg.generated
     */
    public void setAutoMoveTemplate(String autoMoveTemplate) {
        this.autoMoveTemplate = autoMoveTemplate == null ? null : autoMoveTemplate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.auto_move_email_content_template
     *
     * @return the value of moe_book_online_notification.auto_move_email_content_template
     *
     * @mbg.generated
     */
    public String getAutoMoveEmailContentTemplate() {
        return autoMoveEmailContentTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.auto_move_email_content_template
     *
     * @param autoMoveEmailContentTemplate the value for moe_book_online_notification.auto_move_email_content_template
     *
     * @mbg.generated
     */
    public void setAutoMoveEmailContentTemplate(String autoMoveEmailContentTemplate) {
        this.autoMoveEmailContentTemplate =
                autoMoveEmailContentTemplate == null ? null : autoMoveEmailContentTemplate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.decline_template
     *
     * @return the value of moe_book_online_notification.decline_template
     *
     * @mbg.generated
     */
    public String getDeclineTemplate() {
        return declineTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.decline_template
     *
     * @param declineTemplate the value for moe_book_online_notification.decline_template
     *
     * @mbg.generated
     */
    public void setDeclineTemplate(String declineTemplate) {
        this.declineTemplate = declineTemplate == null ? null : declineTemplate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_notification.decline_email_content_template
     *
     * @return the value of moe_book_online_notification.decline_email_content_template
     *
     * @mbg.generated
     */
    public String getDeclineEmailContentTemplate() {
        return declineEmailContentTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_notification.decline_email_content_template
     *
     * @param declineEmailContentTemplate the value for moe_book_online_notification.decline_email_content_template
     *
     * @mbg.generated
     */
    public void setDeclineEmailContentTemplate(String declineEmailContentTemplate) {
        this.declineEmailContentTemplate =
                declineEmailContentTemplate == null ? null : declineEmailContentTemplate.trim();
    }
}
