package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Group Class Enroll Result DTO
 * 团体课程报名结果
 */
@Data
@Schema(description = "团体课程报名结果")
public class GroupClassEnrollResultDTO {

    @Schema(description = "是否成功")
    private Boolean success;

    @Schema(description = "预约ID")
    private Integer appointmentId;

    @Schema(description = "客户ID")
    private Integer customerId;

    @Schema(description = "成功报名的宠物ID列表")
    private List<Integer> enrolledPetIds;

    @Schema(description = "失败的宠物ID列表")
    private List<Integer> failedPetIds;

    @Schema(description = "支付金额")
    private BigDecimal chargedAmount;

    @Schema(description = "发票ID")
    private String invoiceId;

    @Schema(description = "支付状态")
    private String paymentStatus;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "剩余容量")
    private Integer remainingCapacity;
}
