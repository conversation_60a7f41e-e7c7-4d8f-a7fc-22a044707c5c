package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_landing_page_config
 */
public class MoeBookOnlineLandingPageConfig {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   moe_business.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   Amenities, 8 features and 4 payment options
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.amenities
     *
     * @mbg.generated
     */
    private String amenities;

    /**
     * Database Column Remarks:
     *   showcase before image
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.showcase_before_image
     *
     * @mbg.generated
     */
    private String showcaseBeforeImage;

    /**
     * Database Column Remarks:
     *   showcase after image
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.showcase_after_image
     *
     * @mbg.generated
     */
    private String showcaseAfterImage;

    /**
     * Database Column Remarks:
     *   Inherited from moe_book_online_profile.button_color
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.theme_color
     *
     * @mbg.generated
     */
    private String themeColor;

    /**
     * Database Column Remarks:
     *   Customized URL domain name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.url_domain_name
     *
     * @mbg.generated
     */
    private String urlDomainName;

    /**
     * Database Column Remarks:
     *   0-false, 1-true
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.is_published
     *
     * @mbg.generated
     */
    private Boolean isPublished;

    /**
     * Database Column Remarks:
     *   GA4 MEASUREMENT ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.ga_measurement_id
     *
     * @mbg.generated
     */
    private String gaMeasurementId;

    /**
     * Database Column Remarks:
     *   Page components
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.page_components
     *
     * @mbg.generated
     */
    private String pageComponents;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   是否展示 client review showcase photo
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.is_display_client_review_showcase_photo
     *
     * @mbg.generated
     */
    private Boolean isDisplayClientReviewShowcasePhoto;

    /**
     * Database Column Remarks:
     *   Inherited from moe_book_online_profile.description
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.about_us
     *
     * @mbg.generated
     */
    private String aboutUs;

    /**
     * Database Column Remarks:
     *   Inherited from moe_business_book_online.description
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.welcome_page_message
     *
     * @mbg.generated
     */
    private String welcomePageMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_config.thank_you_page_url
     *
     * @mbg.generated
     */
    private String thankYouPageUrl;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.id
     *
     * @return the value of moe_book_online_landing_page_config.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.id
     *
     * @param id the value for moe_book_online_landing_page_config.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.business_id
     *
     * @return the value of moe_book_online_landing_page_config.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.business_id
     *
     * @param businessId the value for moe_book_online_landing_page_config.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.amenities
     *
     * @return the value of moe_book_online_landing_page_config.amenities
     *
     * @mbg.generated
     */
    public String getAmenities() {
        return amenities;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.amenities
     *
     * @param amenities the value for moe_book_online_landing_page_config.amenities
     *
     * @mbg.generated
     */
    public void setAmenities(String amenities) {
        this.amenities = amenities == null ? null : amenities.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.showcase_before_image
     *
     * @return the value of moe_book_online_landing_page_config.showcase_before_image
     *
     * @mbg.generated
     */
    public String getShowcaseBeforeImage() {
        return showcaseBeforeImage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.showcase_before_image
     *
     * @param showcaseBeforeImage the value for moe_book_online_landing_page_config.showcase_before_image
     *
     * @mbg.generated
     */
    public void setShowcaseBeforeImage(String showcaseBeforeImage) {
        this.showcaseBeforeImage = showcaseBeforeImage == null ? null : showcaseBeforeImage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.showcase_after_image
     *
     * @return the value of moe_book_online_landing_page_config.showcase_after_image
     *
     * @mbg.generated
     */
    public String getShowcaseAfterImage() {
        return showcaseAfterImage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.showcase_after_image
     *
     * @param showcaseAfterImage the value for moe_book_online_landing_page_config.showcase_after_image
     *
     * @mbg.generated
     */
    public void setShowcaseAfterImage(String showcaseAfterImage) {
        this.showcaseAfterImage = showcaseAfterImage == null ? null : showcaseAfterImage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.theme_color
     *
     * @return the value of moe_book_online_landing_page_config.theme_color
     *
     * @mbg.generated
     */
    public String getThemeColor() {
        return themeColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.theme_color
     *
     * @param themeColor the value for moe_book_online_landing_page_config.theme_color
     *
     * @mbg.generated
     */
    public void setThemeColor(String themeColor) {
        this.themeColor = themeColor == null ? null : themeColor.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.url_domain_name
     *
     * @return the value of moe_book_online_landing_page_config.url_domain_name
     *
     * @mbg.generated
     */
    public String getUrlDomainName() {
        return urlDomainName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.url_domain_name
     *
     * @param urlDomainName the value for moe_book_online_landing_page_config.url_domain_name
     *
     * @mbg.generated
     */
    public void setUrlDomainName(String urlDomainName) {
        this.urlDomainName = urlDomainName == null ? null : urlDomainName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.is_published
     *
     * @return the value of moe_book_online_landing_page_config.is_published
     *
     * @mbg.generated
     */
    public Boolean getIsPublished() {
        return isPublished;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.is_published
     *
     * @param isPublished the value for moe_book_online_landing_page_config.is_published
     *
     * @mbg.generated
     */
    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.ga_measurement_id
     *
     * @return the value of moe_book_online_landing_page_config.ga_measurement_id
     *
     * @mbg.generated
     */
    public String getGaMeasurementId() {
        return gaMeasurementId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.ga_measurement_id
     *
     * @param gaMeasurementId the value for moe_book_online_landing_page_config.ga_measurement_id
     *
     * @mbg.generated
     */
    public void setGaMeasurementId(String gaMeasurementId) {
        this.gaMeasurementId = gaMeasurementId == null ? null : gaMeasurementId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.page_components
     *
     * @return the value of moe_book_online_landing_page_config.page_components
     *
     * @mbg.generated
     */
    public String getPageComponents() {
        return pageComponents;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.page_components
     *
     * @param pageComponents the value for moe_book_online_landing_page_config.page_components
     *
     * @mbg.generated
     */
    public void setPageComponents(String pageComponents) {
        this.pageComponents = pageComponents == null ? null : pageComponents.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.create_time
     *
     * @return the value of moe_book_online_landing_page_config.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.create_time
     *
     * @param createTime the value for moe_book_online_landing_page_config.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.update_time
     *
     * @return the value of moe_book_online_landing_page_config.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.update_time
     *
     * @param updateTime the value for moe_book_online_landing_page_config.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.company_id
     *
     * @return the value of moe_book_online_landing_page_config.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.company_id
     *
     * @param companyId the value for moe_book_online_landing_page_config.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.is_display_client_review_showcase_photo
     *
     * @return the value of moe_book_online_landing_page_config.is_display_client_review_showcase_photo
     *
     * @mbg.generated
     */
    public Boolean getIsDisplayClientReviewShowcasePhoto() {
        return isDisplayClientReviewShowcasePhoto;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.is_display_client_review_showcase_photo
     *
     * @param isDisplayClientReviewShowcasePhoto the value for moe_book_online_landing_page_config.is_display_client_review_showcase_photo
     *
     * @mbg.generated
     */
    public void setIsDisplayClientReviewShowcasePhoto(Boolean isDisplayClientReviewShowcasePhoto) {
        this.isDisplayClientReviewShowcasePhoto = isDisplayClientReviewShowcasePhoto;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.about_us
     *
     * @return the value of moe_book_online_landing_page_config.about_us
     *
     * @mbg.generated
     */
    public String getAboutUs() {
        return aboutUs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.about_us
     *
     * @param aboutUs the value for moe_book_online_landing_page_config.about_us
     *
     * @mbg.generated
     */
    public void setAboutUs(String aboutUs) {
        this.aboutUs = aboutUs == null ? null : aboutUs.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.welcome_page_message
     *
     * @return the value of moe_book_online_landing_page_config.welcome_page_message
     *
     * @mbg.generated
     */
    public String getWelcomePageMessage() {
        return welcomePageMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.welcome_page_message
     *
     * @param welcomePageMessage the value for moe_book_online_landing_page_config.welcome_page_message
     *
     * @mbg.generated
     */
    public void setWelcomePageMessage(String welcomePageMessage) {
        this.welcomePageMessage = welcomePageMessage == null ? null : welcomePageMessage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_config.thank_you_page_url
     *
     * @return the value of moe_book_online_landing_page_config.thank_you_page_url
     *
     * @mbg.generated
     */
    public String getThankYouPageUrl() {
        return thankYouPageUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_config.thank_you_page_url
     *
     * @param thankYouPageUrl the value for moe_book_online_landing_page_config.thank_you_page_url
     *
     * @mbg.generated
     */
    public void setThankYouPageUrl(String thankYouPageUrl) {
        this.thankYouPageUrl = thankYouPageUrl == null ? null : thankYouPageUrl.trim();
    }
}
