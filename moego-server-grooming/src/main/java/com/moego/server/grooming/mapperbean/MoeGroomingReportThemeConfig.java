package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_report_theme_config
 */
public class MoeGroomingReportThemeConfig {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   theme name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     * Database Column Remarks:
     *   theme code
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.code
     *
     * @mbg.generated
     */
    private String code;

    /**
     * Database Column Remarks:
     *   theme color
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.color
     *
     * @mbg.generated
     */
    private String color;

    /**
     * Database Column Remarks:
     *   theme light color
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.light_color
     *
     * @mbg.generated
     */
    private String lightColor;

    /**
     * Database Column Remarks:
     *   theme img url
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.img_url
     *
     * @mbg.generated
     */
    private String imgUrl;

    /**
     * Database Column Remarks:
     *   theme icon
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.icon
     *
     * @mbg.generated
     */
    private String icon;

    /**
     * Database Column Remarks:
     *   email bottom img url
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.email_bottom_img_url
     *
     * @mbg.generated
     */
    private String emailBottomImgUrl;

    /**
     * Database Column Remarks:
     *   is recommend: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.recommend
     *
     * @mbg.generated
     */
    private Boolean recommend;

    /**
     * Database Column Remarks:
     *   0-inactive, 1-active, 2-hide
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   theme config visible level
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.visible_level
     *
     * @mbg.generated
     */
    private Integer visibleLevel;

    /**
     * Database Column Remarks:
     *   flag: 1-growth+only
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.flag
     *
     * @mbg.generated
     */
    private Byte flag;

    /**
     * Database Column Remarks:
     *   sort value, descending order
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_theme_config.sort
     *
     * @mbg.generated
     */
    private Byte sort;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.id
     *
     * @return the value of moe_grooming_report_theme_config.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.id
     *
     * @param id the value for moe_grooming_report_theme_config.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.name
     *
     * @return the value of moe_grooming_report_theme_config.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.name
     *
     * @param name the value for moe_grooming_report_theme_config.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.code
     *
     * @return the value of moe_grooming_report_theme_config.code
     *
     * @mbg.generated
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.code
     *
     * @param code the value for moe_grooming_report_theme_config.code
     *
     * @mbg.generated
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.color
     *
     * @return the value of moe_grooming_report_theme_config.color
     *
     * @mbg.generated
     */
    public String getColor() {
        return color;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.color
     *
     * @param color the value for moe_grooming_report_theme_config.color
     *
     * @mbg.generated
     */
    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.light_color
     *
     * @return the value of moe_grooming_report_theme_config.light_color
     *
     * @mbg.generated
     */
    public String getLightColor() {
        return lightColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.light_color
     *
     * @param lightColor the value for moe_grooming_report_theme_config.light_color
     *
     * @mbg.generated
     */
    public void setLightColor(String lightColor) {
        this.lightColor = lightColor == null ? null : lightColor.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.img_url
     *
     * @return the value of moe_grooming_report_theme_config.img_url
     *
     * @mbg.generated
     */
    public String getImgUrl() {
        return imgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.img_url
     *
     * @param imgUrl the value for moe_grooming_report_theme_config.img_url
     *
     * @mbg.generated
     */
    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl == null ? null : imgUrl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.icon
     *
     * @return the value of moe_grooming_report_theme_config.icon
     *
     * @mbg.generated
     */
    public String getIcon() {
        return icon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.icon
     *
     * @param icon the value for moe_grooming_report_theme_config.icon
     *
     * @mbg.generated
     */
    public void setIcon(String icon) {
        this.icon = icon == null ? null : icon.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.email_bottom_img_url
     *
     * @return the value of moe_grooming_report_theme_config.email_bottom_img_url
     *
     * @mbg.generated
     */
    public String getEmailBottomImgUrl() {
        return emailBottomImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.email_bottom_img_url
     *
     * @param emailBottomImgUrl the value for moe_grooming_report_theme_config.email_bottom_img_url
     *
     * @mbg.generated
     */
    public void setEmailBottomImgUrl(String emailBottomImgUrl) {
        this.emailBottomImgUrl = emailBottomImgUrl == null ? null : emailBottomImgUrl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.recommend
     *
     * @return the value of moe_grooming_report_theme_config.recommend
     *
     * @mbg.generated
     */
    public Boolean getRecommend() {
        return recommend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.recommend
     *
     * @param recommend the value for moe_grooming_report_theme_config.recommend
     *
     * @mbg.generated
     */
    public void setRecommend(Boolean recommend) {
        this.recommend = recommend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.status
     *
     * @return the value of moe_grooming_report_theme_config.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.status
     *
     * @param status the value for moe_grooming_report_theme_config.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.create_time
     *
     * @return the value of moe_grooming_report_theme_config.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.create_time
     *
     * @param createTime the value for moe_grooming_report_theme_config.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.update_time
     *
     * @return the value of moe_grooming_report_theme_config.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.update_time
     *
     * @param updateTime the value for moe_grooming_report_theme_config.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.visible_level
     *
     * @return the value of moe_grooming_report_theme_config.visible_level
     *
     * @mbg.generated
     */
    public Integer getVisibleLevel() {
        return visibleLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.visible_level
     *
     * @param visibleLevel the value for moe_grooming_report_theme_config.visible_level
     *
     * @mbg.generated
     */
    public void setVisibleLevel(Integer visibleLevel) {
        this.visibleLevel = visibleLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.flag
     *
     * @return the value of moe_grooming_report_theme_config.flag
     *
     * @mbg.generated
     */
    public Byte getFlag() {
        return flag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.flag
     *
     * @param flag the value for moe_grooming_report_theme_config.flag
     *
     * @mbg.generated
     */
    public void setFlag(Byte flag) {
        this.flag = flag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_theme_config.sort
     *
     * @return the value of moe_grooming_report_theme_config.sort
     *
     * @mbg.generated
     */
    public Byte getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_theme_config.sort
     *
     * @param sort the value for moe_grooming_report_theme_config.sort
     *
     * @mbg.generated
     */
    public void setSort(Byte sort) {
        this.sort = sort;
    }
}
