package com.moego.server.grooming.service.params;

import com.moego.server.business.dto.SmartScheduleDrivingRuleDTO;
import com.moego.server.grooming.service.TimeSlot;
import com.moego.server.grooming.service.utils.DriverTimeCalculator;
import java.util.List;
import lombok.Data;

@Data
public class GetAvailableSlotParams {
    List<TimeSlot> timeSlots; // 待检查的时间段
    int serviceDuration; // 服务所需时长
    int needCount; // 待返回的 availableSlot 数量

    // ss enable 时有效
    Long locationId; // 目标地址，SmartScheduleParams 中的 locations 的 key。对应地址信息有误时，返回空结果。
    SmartScheduleDrivingRuleDTO drivingRule;
    DriverTimeCalculator driverTimeCalculator; // 有多种实现：1）计算经纬度间直线距离预估 driver time 2）请求谷歌服务实时计算 driver time
    Double distanceScaleFactor; // 计算距离伸缩比例，默认为 null 即可。目前用在“经纬度间直线距离预估”场景，用于减少预估误差
}
