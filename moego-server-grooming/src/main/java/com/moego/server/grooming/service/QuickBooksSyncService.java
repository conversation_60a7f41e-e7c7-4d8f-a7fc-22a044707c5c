package com.moego.server.grooming.service;

import com.google.gson.Gson;
import com.intuit.ipp.core.Context;
import com.intuit.ipp.core.IEntity;
import com.intuit.ipp.data.Account;
import com.intuit.ipp.data.AccountBasedExpenseLineDetail;
import com.intuit.ipp.data.AccountSubTypeEnum;
import com.intuit.ipp.data.AccountTypeEnum;
import com.intuit.ipp.data.CreditMemo;
import com.intuit.ipp.data.Customer;
import com.intuit.ipp.data.EntityStatusEnum;
import com.intuit.ipp.data.Invoice;
import com.intuit.ipp.data.Item;
import com.intuit.ipp.data.Line;
import com.intuit.ipp.data.LineDetailTypeEnum;
import com.intuit.ipp.data.Payment;
import com.intuit.ipp.data.PaymentMethod;
import com.intuit.ipp.data.PaymentTypeEnum;
import com.intuit.ipp.data.Purchase;
import com.intuit.ipp.data.ReferenceType;
import com.intuit.ipp.data.RefundReceipt;
import com.intuit.ipp.data.SalesItemLineDetail;
import com.intuit.ipp.data.SalesReceipt;
import com.intuit.ipp.data.SalesTransaction;
import com.intuit.ipp.data.TaxAgency;
import com.intuit.ipp.data.TaxCode;
import com.intuit.ipp.data.TaxRateApplicableOnEnum;
import com.intuit.ipp.data.TaxRateDetails;
import com.intuit.ipp.data.TaxService;
import com.intuit.ipp.data.TxnTaxDetail;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.services.GlobalTaxService;
import com.intuit.ipp.services.QueryResult;
import com.moego.common.dto.PaymentSummary;
import com.moego.common.enums.FeatureConst;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.QuickBooksConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.order.ExtraFeeType;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.QuickBooksDateUtils;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressResponse;
import com.moego.idl.service.business_customer.v1.GetCustomerRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.utils.RandomUtils;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerQbQueryDto;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import com.moego.server.grooming.dto.quickbooks.ListQuickBookInvoiceDTO;
import com.moego.server.grooming.dto.quickbooks.QuickBookPaymentDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.IntuitAccountTypeEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeQbConnectMapper;
import com.moego.server.grooming.mapper.MoeQbSettingMapper;
import com.moego.server.grooming.mapper.MoeQbSyncAccountMapper;
import com.moego.server.grooming.mapper.MoeQbSyncCustomerMapper;
import com.moego.server.grooming.mapper.MoeQbSyncInvoiceMapper;
import com.moego.server.grooming.mapper.MoeQbSyncPackageMapper;
import com.moego.server.grooming.mapper.MoeQbSyncPaymentMapper;
import com.moego.server.grooming.mapper.MoeQbSyncPaymentMethodMapper;
import com.moego.server.grooming.mapper.MoeQbSyncProductMapper;
import com.moego.server.grooming.mapper.MoeQbSyncReceiptMapper;
import com.moego.server.grooming.mapper.MoeQbSyncRefundMapper;
import com.moego.server.grooming.mapper.MoeQbSyncServiceChargeMapper;
import com.moego.server.grooming.mapper.MoeQbSyncServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeQbConnect;
import com.moego.server.grooming.mapperbean.MoeQbSyncAccount;
import com.moego.server.grooming.mapperbean.MoeQbSyncCustomer;
import com.moego.server.grooming.mapperbean.MoeQbSyncInvoice;
import com.moego.server.grooming.mapperbean.MoeQbSyncPackage;
import com.moego.server.grooming.mapperbean.MoeQbSyncPayment;
import com.moego.server.grooming.mapperbean.MoeQbSyncPaymentMethod;
import com.moego.server.grooming.mapperbean.MoeQbSyncProduct;
import com.moego.server.grooming.mapperbean.MoeQbSyncReceipt;
import com.moego.server.grooming.mapperbean.MoeQbSyncRefund;
import com.moego.server.grooming.mapperbean.MoeQbSyncService;
import com.moego.server.grooming.mapperbean.MoeQbSyncServiceCharge;
import com.moego.server.grooming.mapstruct.QuickBookMapper;
import com.moego.server.grooming.params.quickbook.ListQuickBookInvoiceParams;
import com.moego.server.grooming.service.dto.DataServiceTmpDto;
import com.moego.server.grooming.service.dto.QbInvoiceStatusDto;
import com.moego.server.grooming.service.intuit.helper.AccountHelper;
import com.moego.server.grooming.service.intuit.helper.CustomerHelper;
import com.moego.server.grooming.service.intuit.helper.InvoiceHelper;
import com.moego.server.grooming.service.intuit.helper.PaymentHelper;
import com.moego.server.grooming.service.intuit.helper.PaymentMethodHelper;
import com.moego.server.grooming.service.intuit.helper.ReceiptHelper;
import com.moego.server.grooming.service.intuit.helper.ServiceHelper;
import com.moego.server.grooming.service.intuit.qbo.BusinessDataServiceFactory;
import com.moego.server.grooming.service.intuit.qbo.OAuth2PlatformClientFactory;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentRefundClient;
import com.moego.server.payment.client.IPaymentSettingClient;
import com.moego.server.payment.client.IPaymentTransactionHistoryClient;
import com.moego.server.payment.dto.PayDetailDTO;
import com.moego.server.payment.dto.PaymentDTO;
import com.moego.server.payment.dto.PaymentSettingDTO;
import com.moego.server.payment.dto.RefundDTO;
import com.moego.server.payment.params.GetPaymentParams;
import com.moego.server.retail.api.IPackageService;
import com.moego.server.retail.client.IProductClient;
import com.moego.server.retail.dto.ProductInfoDTO;
import com.moego.server.retail.param.GetPackageParams;
import com.moego.server.retail.result.GetPackageResult;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class QuickBooksSyncService {

    @Autowired
    private MoeQbSyncCustomerMapper syncCustomerMapper;

    @Autowired
    private MoeQbSyncAccountMapper syncAccountMapper;

    @Autowired
    private MoeQbSyncInvoiceMapper syncInvoiceMapper;

    @Autowired
    private MoeQbConnectMapper moeQbConnectMapper;

    @Autowired
    private MoeQbSyncPaymentMethodMapper syncPaymentMethodMapper;

    @Autowired
    private OAuth2PlatformClientFactory factory;

    @Autowired
    private MoeQbSyncPaymentMapper syncPaymentMapper;

    @Autowired
    private MoeQbSyncServiceMapper syncServiceMapper;

    @Autowired
    private MoeQbSyncProductMapper syncProductMapper;

    @Autowired
    private MoeQbSyncServiceChargeMapper syncServiceChargeMapper;

    @Autowired
    private BusinessDataServiceFactory dataServiceFactory;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Autowired
    private IPaymentPaymentClient iPaymentService;

    @Autowired
    private IPaymentSettingClient iPaymentSettingClient;

    @Autowired
    private IPaymentTransactionHistoryClient iPaymentTransactionHistoryClient;

    @Autowired
    private IPaymentRefundClient iPaymentRefundClient;

    @Autowired
    private IProductClient iProductClient;

    @Autowired
    private ServiceChargeService serviceChargeService;

    @Autowired
    private IPackageService iPackageService;

    @Autowired
    private MoeQbSettingMapper moeQbSettingMapper;

    @Autowired
    private MoeQbSyncReceiptMapper syncReceiptMapper;

    @Autowired
    private MoeQbSyncPackageMapper syncPackageMapper;

    @Autowired
    private MoeQbSyncRefundMapper syncRefundMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerServiceBlockingStub;

    @Autowired
    private BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub
            businessCustomerAddressServiceBlockingStub;

    private static final String MOE_GO = "MoeGo";

    private static final String SALES_TAX = "MoeGo Sales Tax {0}%";

    private static final String SALES_TAX_RATE = "MoeGo Sales TaxRate {0}";

    public void checkBusinessIdAndCompanyId(Integer businessId, Long companyId) {
        if (!businessInfoHelper.getCompanyIdByBusinessId(businessId).equals(companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid business id");
        }
    }

    /**
     * 这个key 用于标记商家是否处于迁移状态，迁移状态会无视判断规则进行插入数据, 只会执行一次迁移
     */
    public String getQBMigrationKey(Integer businessId) {
        return String.format(QuickBooksConst.REDIS_KEY_QUICK_BOOK_MIGRATION, businessId);
    }

    /**
     * 标记商家处于迁移状态
     * @param businessId  商家id
     */
    public void setMigrationByBusinessId(Integer businessId) {
        String key = getQBMigrationKey(businessId);
        redisUtil.set(key, String.valueOf(QuickBooksConst.QB_SYNC_MIGRATION_OPEN));
    }

    public String getRedisQBMigrationStatus(Integer businessId) {
        return redisUtil.get(getQBMigrationKey(businessId));
    }

    public void delRedisQBMigrationKey(Integer businessId) {
        String qbRedisListKey = getQBMigrationKey(businessId);
        redisUtil.delete(qbRedisListKey);
    }

    /**
     * 检查qb sync 是否处于迁移状态
     * @param business
     * @return
     */
    public Boolean checkIsMigration(Integer business) {
        var migration = getRedisQBMigrationStatus(business);
        return migration != null && QuickBooksConst.QB_SYNC_MIGRATION_OPEN.equals(Byte.valueOf(migration));
    }

    /**
     * 这个key用于记录迁移前后, 迁移后invoice id对应的迁移前invoice id, 最多存三年
     */
    public String getMigrationIDAssociationKey(Integer businessId, String realmId) {
        return String.format(QuickBooksConst.REDIS_KEY_QUICK_BOOK_MIGRATION_ID_ASSOCIATION, businessId, realmId);
    }

    /**
     * 根据business id 和迁移后的qb invoice id查询迁移之前的qb invoice id
     * @param businessId business id
     * @param afterId 迁移后的qb invoice id
     * @return 迁移前的invoice id
     */
    public Integer getMigrationIdAssociationByAfterId(Integer businessId, String realmId, Integer afterId) {
        String key = getMigrationIDAssociationKey(businessId, realmId);
        return (Integer) redisUtil.hGet(key, afterId.toString());
    }

    public void setMigrationIdAssociationKey(Integer businessId, String realmId, String afterId, String beforeId) {
        String key = getMigrationIDAssociationKey(businessId, realmId);
        redisUtil.hPut(key, afterId, beforeId);
    }

    /**
     * 这个key记录business存储需要同步的退款
     * @param businessId 商家id
     * @return key
     */
    public String getQBSyncPaymentKey(Integer businessId) {
        return String.format(QuickBooksConst.REDIS_KEY_QUICK_BOOK_PAYMENT_NEED_SYNC_LIST, businessId);
    }

    public Set<Integer> getRedisNeedSyncPaymentList(Integer businessId) {
        String qbRedisListKey = getQBSyncPaymentKey(businessId);
        List<String> paymentIdList = redisUtil.lRange(qbRedisListKey, 0, -1);
        return paymentIdList.stream().map(Integer::parseInt).collect(Collectors.toSet());
    }

    public void delRedisNeedSyncPaymentKey(Integer businessId) {
        String qbRedisListKey = getQBSyncPaymentKey(businessId);
        redisUtil.delete(qbRedisListKey);
    }

    /**
     * 获取qb_service绑定的account(账本)，没有就创建
     *
     * @return
     */
    public ReferenceType getServiceAccount(Integer businessId, DataServiceTmpDto dataServiceTmpDto) {
        return getServiceAccount(businessId, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE);
    }

    public ReferenceType getServiceAccount(
            Integer businessId, DataServiceTmpDto dataServiceTmpDto, IntuitAccountTypeEnum accType) {
        MoeQbSyncAccount qbSyncAccount = getAssociationAccount(businessId, dataServiceTmpDto, accType);
        ReferenceType accountRef = new ReferenceType();
        accountRef.setName(qbSyncAccount.getName());
        accountRef.setValue(qbSyncAccount.getQbAccountId());
        return accountRef;
    }

    private MoeQbSyncAccount getAssociationAccount(
            Integer businessId, DataServiceTmpDto dataServiceTmpDto, IntuitAccountTypeEnum accType) {
        String realmId = dataServiceTmpDto.getRealmId();
        var qbSyncAccount =
                syncAccountMapper.selectByBusinessRealmIdAndAccountType(businessId, realmId, accType.getCode());
        DataService dataService = dataServiceTmpDto.getDataService();
        log.info("get association account, businessId:{}, realmId:{}, accType:{}", businessId, realmId, accType);
        if (qbSyncAccount == null) {
            // 不存在指定类型的关联账号
            qbSyncAccount = saveMoegoDefaultServiceAccount(dataService, businessId, realmId, accType);
        } else {
            // 存在指定类型的关联账号
            Account queryAccount = dataServiceFactory.dataServiceFindById(
                    dataService, AccountHelper.getAccountWithId(qbSyncAccount.getQbAccountId()));
            log.info(new Gson().toJson(queryAccount));
            if (queryAccount == null
                    || !queryAccount.isActive()
                    || EntityStatusEnum.DELETED.equals(queryAccount.getStatus())) {
                // 存在但是被删除了，创建新的createAccount
                qbSyncAccount = saveMoegoDefaultServiceAccount(dataService, businessId, realmId, accType);
            }
        }
        return qbSyncAccount;
    }

    public QbInvoiceStatusDto getInvoiceSyncStatus(Integer tokenBusinessId, Integer invoiceId) {
        MoeQbSyncInvoice syncInvoice = syncInvoiceMapper.selectByInvoiceId(tokenBusinessId, invoiceId);
        QbInvoiceStatusDto invoiceStatusDto = new QbInvoiceStatusDto();
        if (syncInvoice == null || QuickBooksConst.QB_INVOICE_STATUS_DELETE.equals(syncInvoice.getQbInvoiceStatus())) {
            invoiceStatusDto.setIsSync(false);
        } else {
            invoiceStatusDto.setIsSync(true);
            invoiceStatusDto.setSyncTime(syncInvoice.getUpdateTime());
            invoiceStatusDto.setQbInvoiceLink(factory.getInvoiceUrl(syncInvoice.getQbInvoiceId()));
        }
        return invoiceStatusDto;
    }

    public boolean testSyncInvoiceByGroomingIdBusinessId(Integer businessId, Integer groomingId) {
        MoeGroomingAppointment groomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (groomingAppointment == null || !businessId.equals(groomingAppointment.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.GROOMING_NOT_FOUND);
        }
        return syncInvoiceByGroomingId(
                groomingAppointment.getBusinessId(), groomingAppointment, DateUtil.get10Timestamp());
    }

    public boolean testSyncInvoiceByGroomingId(Integer groomingId) {
        MoeGroomingAppointment groomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        return syncInvoiceByGroomingId(
                groomingAppointment.getBusinessId(), groomingAppointment, DateUtil.get10Timestamp());
    }

    public String testSyncInvoiceByInvoiceId(Integer businessId, Integer invoiceId) {
        return syncInvoiceByInvoiceId(businessId, invoiceId, DateUtil.get10Timestamp());
    }

    public String deleteAllInvoice(Integer businessId, Integer inputBusinessId) {
        if (!Objects.equals(businessId, inputBusinessId)) {
            return "businessId not equals";
        }
        List<MoeQbSyncInvoice> invoiceList = syncInvoiceMapper.selectByBusinessId(businessId);
        invoiceList.stream().limit(100).forEach(invoice -> ThreadPool.submit(() -> deleteInvoice(businessId, invoice)));
        return "success progress: " + invoiceList.size();
    }

    public String deleteInvoice(Integer businessId, Integer invoiceId) {
        MoeQbSyncInvoice localInvoice = syncInvoiceMapper.selectByInvoiceId(businessId, invoiceId);
        return deleteInvoice(businessId, localInvoice);
    }

    public String deleteInvoice(Integer businessId, MoeQbSyncInvoice localInvoice) {
        if (localInvoice == null) {
            return "invoice not found";
        }
        if (QuickBooksConst.QB_INVOICE_STATUS_DELETE.equals(localInvoice.getQbInvoiceStatus())) {
            return "invoice already deleted";
        }

        // delete invoice
        DataServiceTmpDto dataServiceTmpDto = new DataServiceTmpDto(
                dataServiceFactory.getDataService(businessId), dataServiceFactory.getConnect(businessId));
        DataService dataService = dataServiceTmpDto.getDataService();

        Invoice remoteInvoice = dataServiceFactory.dataServiceFindById(
                dataService, InvoiceHelper.getInvoiceWithId(localInvoice.getQbInvoiceId()));

        if (Objects.isNull(remoteInvoice)) {
            return "invoice already deleted";
        }

        // 1、删除 payment
        deleteQbPayment(businessId, localInvoice.getInvoiceId(), dataServiceTmpDto);
        // 2、删除 invoice
        Invoice deletedInvoice = dataServiceFactory.dataServiceDelete(dataService, remoteInvoice);
        log.info("qb Invoice deleted:{}, status:{}", deletedInvoice.getId(), deletedInvoice.getStatus());

        // delete sync invoice
        localInvoice.setQbInvoiceStatus(QuickBooksConst.QB_INVOICE_STATUS_DELETE);
        localInvoice.setUpdateTime(DateUtil.get10Timestamp());
        syncInvoiceMapper.updateByPrimaryKeySelective(localInvoice);

        return "success";
    }

    /**
     * 检查service(Item) 是否合法, 在后续逻辑中, 不合法的Item需要重新创建
     * <br/>
     * 合法条件:
     * <ul>
     *    <li>non null</li>
     *    <li>non deleted</li>
     *    <li>未被标记为不活跃(no active)</li>
     *    <li>这个service在qb中属于accType所对应的账号</li>
     * </ul>
     * @param queryItem 查询到的Item
     * @param acct 账号类型
     * @return Item是否合法
     */
    private Boolean checkQbItemStatus(Item queryItem, ReferenceType acct) {
        return checkQbItemStatus(queryItem, acct.getValue());
    }

    private Boolean checkQbItemStatus(Item queryItem, String qbAccountId) {
        if (Strings.isEmpty(qbAccountId)) return false;
        return queryItem != null && !EntityStatusEnum.DELETED.equals(queryItem.getStatus()) && queryItem.isActive();
    }

    /**
     * 检查item的income account 是否符合预期
     */
    private Boolean checkItemIncomeAccountRef(Item item, ReferenceType accountRef) {
        return item.getIncomeAccountRef().getValue().equals(accountRef.getValue());
    }
    /**
     * 用于获取几种特殊的qb service
     * Moego Discount
     * Moego Tax
     * Moego Tips
     * Moego No show fee
     *
     * @param businessId
     * @param itemName
     * @return
     */
    public Item getDefaultInvoiceItem(Integer businessId, String itemName, DataServiceTmpDto dataServiceTmpDto) {
        return getDefaultInvoiceItem(
                businessId, itemName, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE);
    }

    public Item getDefaultInvoiceItem(
            Integer businessId, String itemName, DataServiceTmpDto dataServiceTmpDto, IntuitAccountTypeEnum accType) {
        String realmId = dataServiceTmpDto.getRealmId();
        MoeQbSyncService syncService =
                syncServiceMapper.selectByBusinessIdRealmIdItemName(businessId, realmId, itemName);
        DataService dataService = dataServiceTmpDto.getDataService();
        ReferenceType accountRef = getServiceAccount(businessId, dataServiceTmpDto, accType);
        var needMigration = checkIsMigration(businessId);
        // 迁移状态下才检查, 避免创建多余账号
        var moegoIncomeAccountRef = needMigration ? getServiceAccount(businessId, dataServiceTmpDto) : null;
        if (syncService != null) {
            Item queryItem = dataServiceFactory.dataServiceFindById(
                    dataService, ServiceHelper.getItemWithId(syncService.getQbServiceId()));
            if (checkQbItemStatus(queryItem, accountRef)) {
                if (needMigration && checkItemIncomeAccountRef(queryItem, moegoIncomeAccountRef)) {
                    // 迁移状态下, 如果item的income account是moego的旧逻辑income account则更新到新的账号
                    queryItem.setIncomeAccountRef(accountRef);
                    dataServiceFactory.dataServiceUpdate(dataService, queryItem);
                }
                // 合法说明item存在期望账号中, 状态为活跃且未删除, 可以复用
                return queryItem;
            }
        }
        Item saveItem = addUpdateQbItem(
                dataService, ServiceHelper.getDefaultInvoiceService(itemName, accountRef), 0, businessId);
        log.info("add or update qb item: {}", saveItem);
        if (syncService != null) {
            // update
            MoeQbSyncService updateService = new MoeQbSyncService();
            updateService.setRealmId(realmId);
            updateService.setBusinessId(businessId);
            updateService.setConnectId(dataServiceFactory.getConnectId(businessId));
            updateService.setServiceId(0);
            updateService.setQbServiceId(saveItem.getId());
            updateService.setUpdateTime(DateUtil.get10Timestamp());
            updateService.setServiceName(itemName);
            updateService.setServiceDescription(itemName);
            syncServiceMapper.updateByBusinessIdRealmIdServiceId(updateService);
        } else {
            // insert
            MoeQbSyncService saveService = new MoeQbSyncService();
            saveService.setRealmId(realmId);
            saveService.setBusinessId(businessId);
            saveService.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
            saveService.setQbServiceId(saveItem.getId());
            saveService.setConnectId(dataServiceFactory.getConnectId(businessId));
            saveService.setServiceId(0);
            saveService.setCreateTime(DateUtil.get10Timestamp());
            saveService.setUpdateTime(saveService.getCreateTime());
            saveService.setServiceName(itemName);
            saveService.setServiceDescription(itemName);
            syncServiceMapper.insertSelective(saveService);
        }
        return saveItem;
    }

    private String getCustomizedProcessingFeeName(Integer businessId) {
        PaymentSettingDTO paymentSetting = iPaymentSettingClient.getPaymentSetting(businessId);
        if (Objects.nonNull(paymentSetting)
                && !ExtraFeeType.CONVENIENCE_FEE.getType().equalsIgnoreCase(paymentSetting.getCustomizedFeeName())) {
            return paymentSetting.getCustomizedFeeName();
        }
        return QuickBooksConst.QB_SERVICE_PROCESSING_FEE_BY_CLIENT;
    }

    /**
     * 增加Item时，可能会重复插入service name，下面的代码就是解决插入时重复命名的情况
     *
     * @param dataService
     * @param addItem
     * @return
     */
    public Item addUpdateQbItem(DataService dataService, Item addItem, Integer serviceId, Integer businessId) {
        Item saveItem = null;
        int tryCount = 0;
        while (tryCount < 3) {
            try {
                saveItem = dataServiceFactory.dataServiceAdd(dataService, addItem);
                break;
            } catch (CommonException e) {
                if (ResponseCodeEnum.QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS
                        .getCode()
                        .equals(e.getCode())) {
                    if (serviceId == null || serviceId == 0) {
                        addItem.setName(addItem.getName() + "-" + RandomUtils.randomNumString(4));
                        InfoIdParams businessIdParams = new InfoIdParams();
                        businessIdParams.setInfoId(businessId);
                        MoeBusinessDto businessInfo =
                                iBusinessBusinessClient.getBusinessInfoWithOwnerEmail(businessIdParams);
                        addItem.setDescription(addItem.getDescription() + " " + businessInfo.getBusinessName());
                    } else {
                        // 第一次尝试
                        if (tryCount == 0) {
                            addItem.setName(addItem.getName() + " " + serviceId);
                        } else {
                            addItem.setName(addItem.getName() + "-" + RandomUtils.randomNumString(4));
                        }
                    }
                } else {
                    log.info("add update qb item:[{}] error:{}", new Gson().toJson(addItem), e.getMessage());
                    throw e;
                }
            }
            tryCount++;
        }
        if (Objects.isNull(saveItem)) throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        return saveItem;
    }

    public Customer addUpdateQbCustomer(DataService dataService, Customer addCustomer, Integer customerId) {
        Customer saveCustomer = null;
        try {
            saveCustomer = dataServiceFactory.dataServiceAdd(dataService, addCustomer);
        } catch (CommonException e) {
            // 如果customer重名，则在customer信息的后缀加上customerId，确保唯一
            if (ResponseCodeEnum.QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS.getCode().equals(e.getCode())) {
                addCustomer.setSuffix(customerId.toString());
                addCustomer.setDisplayName(addCustomer.getDisplayName() + " " + customerId.toString());
                return dataServiceFactory.dataServiceAdd(dataService, addCustomer);
            } else if (ResponseCodeEnum.QUICKBOOKS_DATA_DUPLICATE_EMAIL_FORMAT_ERROR
                    .getCode()
                    .equals(e.getCode())) {
                addCustomer.setPrimaryEmailAddr(null);
                return dataServiceFactory.dataServiceAdd(dataService, addCustomer);
            } else {
                log.info("add update qb customer:[{}] error:{}", new Gson().toJson(addCustomer), e.getMessage());
                throw e;
            }
        }
        return saveCustomer;
    }

    public Map<Integer, Item> syncBatchService(
            Integer businessId, Map<String, Set<Integer>> needSyncIdMap, DataServiceTmpDto dataServiceTmpDto) {
        return syncBatchService(
                businessId, needSyncIdMap, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE);
    }

    public Map<Integer, Item> syncBatchService(
            Integer businessId,
            Map<String, Set<Integer>> needSyncIdMap,
            DataServiceTmpDto dataServiceTmpDto,
            IntuitAccountTypeEnum accType) {
        if (CollectionUtils.isEmpty(needSyncIdMap)
                || CollectionUtils.isEmpty(needSyncIdMap.get(InvoiceHelper.TYPE_SERVICE))) {
            return Collections.emptyMap();
        }
        String realmId = dataServiceTmpDto.getRealmId();
        List<Integer> needSyncServiceIdList = needSyncIdMap.get(InvoiceHelper.TYPE_SERVICE).stream()
                .distinct()
                .toList();
        List<MoeQbSyncService> services =
                syncServiceMapper.selectByBusinessIdRealmIdServiceIds(businessId, realmId, needSyncServiceIdList);
        List<Integer> foundServiceIdList = new ArrayList<>();
        List<Integer> errorServiceId = new ArrayList<>();
        Map<Integer, Item> returnDataMap = new HashMap<>();
        DataService dataService = dataServiceTmpDto.getDataService();
        ReferenceType accountRef = getServiceAccount(businessId, dataServiceTmpDto, accType);
        var needMigration = checkIsMigration(businessId);
        // 迁移状态下才检查, 避免创建多余账号
        var moegoIncomeAccountRef = needMigration ? getServiceAccount(businessId, dataServiceTmpDto) : null;
        for (MoeQbSyncService syncService : services) {
            Item queryItem = dataServiceFactory.dataServiceFindById(
                    dataService, ServiceHelper.getItemWithId(syncService.getQbServiceId()));
            if (!checkQbItemStatus(queryItem, accountRef)) {
                // 不合法说明这个service在qb中不存在或者被删除了，或者不属于acctType的账号，后续重新创建
                errorServiceId.add(syncService.getServiceId());
                continue;
            }
            // 迁移状态下, 如果item的income account是moego的旧逻辑income account则更新到新的账号
            if (needMigration && checkItemIncomeAccountRef(queryItem, moegoIncomeAccountRef)) {
                queryItem.setIncomeAccountRef(accountRef);
                dataServiceFactory.dataServiceUpdate(dataService, queryItem);
            }
            returnDataMap.put(syncService.getServiceId(), queryItem);
            foundServiceIdList.add(syncService.getServiceId());
        }
        if (!CollectionUtils.isEmpty(foundServiceIdList)) {
            // 更新描述
            List<MoeGroomingService> foundServiceList =
                    companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                            businessId, foundServiceIdList);
            for (MoeGroomingService service : foundServiceList) {
                returnDataMap.get(service.getId()).setDescription(service.getDescription());
            }
        }

        // 没有找到的service，在qb内重新创建
        List<Integer> notFoundServiceIdList = new ArrayList<>(needSyncServiceIdList);
        notFoundServiceIdList.removeAll(foundServiceIdList);
        if (!CollectionUtils.isEmpty(notFoundServiceIdList)) {
            List<MoeGroomingService> notFoundServiceList =
                    companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                            businessId, notFoundServiceIdList);
            returnDataMap.putAll(rebuildNotFoundServiceInQB(
                    errorServiceId, notFoundServiceList, dataService, accountRef, businessId, realmId));
        }
        return returnDataMap;
    }

    private Map<Integer, Item> rebuildNotFoundServiceInQB(
            List<Integer> errorServiceId,
            List<MoeGroomingService> notFoundServiceList,
            DataService dataService,
            ReferenceType accountRef,
            Integer businessId,
            String realmId) {
        Map<Integer, Item> returnDataMap = new HashMap<>();
        for (MoeGroomingService service : notFoundServiceList) {
            Item saveItem = addUpdateQbItem(
                    dataService,
                    ServiceHelper.convertGroomingServiceToItem(service, accountRef),
                    service.getId(),
                    businessId);

            insertOrUpdateSyncService(errorServiceId, service, saveItem, businessId, realmId);

            saveItem.setDescription(service.getDescription());
            returnDataMap.put(service.getId(), saveItem);
        }
        return returnDataMap;
    }

    private void insertOrUpdateSyncService(
            List<Integer> errorServiceId,
            MoeGroomingService service,
            Item saveItem,
            Integer businessId,
            String realmId) {
        if (errorServiceId.contains(service.getId())) {
            // update
            MoeQbSyncService updateService = new MoeQbSyncService();
            updateService.setRealmId(realmId);
            updateService.setBusinessId(businessId);
            updateService.setConnectId(dataServiceFactory.getConnectId(businessId));
            updateService.setServiceId(service.getId());
            updateService.setQbServiceId(saveItem.getId());
            updateService.setUpdateTime(DateUtil.get10Timestamp());
            updateService.setServiceName(service.getName());
            updateService.setServiceDescription(service.getDescription());
            syncServiceMapper.updateByBusinessIdRealmIdServiceId(updateService);
        } else {
            // insert
            MoeQbSyncService saveService = new MoeQbSyncService();
            saveService.setRealmId(realmId);
            saveService.setBusinessId(businessId);
            saveService.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
            saveService.setQbServiceId(saveItem.getId());
            saveService.setConnectId(dataServiceFactory.getConnectId(businessId));
            saveService.setServiceId(service.getId());
            saveService.setCreateTime(DateUtil.get10Timestamp());
            saveService.setUpdateTime(saveService.getCreateTime());
            saveService.setServiceName(service.getName());
            saveService.setServiceDescription(service.getDescription());
            syncServiceMapper.insertSelective(saveService);
        }
    }

    // 这个方法提供给version 1.0 的qb sync
    public Map<Integer, Item> syncBatchProduct(
            Integer businessId, Map<String, Set<Integer>> needSyncServiceIdMap, DataServiceTmpDto dataServiceTmpDto) {
        return syncBatchProduct(
                businessId, needSyncServiceIdMap, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE);
    }

    public Map<Integer, Item> syncBatchProduct(
            Integer businessId,
            Map<String, Set<Integer>> needSyncServiceIdMap,
            DataServiceTmpDto dataServiceTmpDto,
            IntuitAccountTypeEnum accType) {
        if (CollectionUtils.isEmpty(needSyncServiceIdMap)
                || CollectionUtils.isEmpty(needSyncServiceIdMap.get(InvoiceHelper.TYPE_PRODUCT))) {
            return Collections.emptyMap();
        }
        String realmId = dataServiceTmpDto.getRealmId();
        List<Integer> needSyncProductIdList = needSyncServiceIdMap.get(InvoiceHelper.TYPE_PRODUCT).stream()
                .distinct()
                .toList();
        List<MoeQbSyncProduct> productList =
                syncProductMapper.selectByBusinessIdRealmIdProductIds(businessId, realmId, needSyncProductIdList);
        List<Integer> foundProductdList = new ArrayList<>();
        Map<Integer, Item> returnDataMap = new HashMap<>(16);
        DataService dataService = dataServiceTmpDto.getDataService();
        ReferenceType accountRef = getServiceAccount(businessId, dataServiceTmpDto, accType);
        var needMigration = checkIsMigration(businessId);
        // 迁移状态下才检查, 避免创建多余账号
        var moegoIncomeAccountRef = needMigration ? getServiceAccount(businessId, dataServiceTmpDto) : null;
        for (MoeQbSyncProduct syncProduct : productList) {
            Item queryItem = dataServiceFactory.dataServiceFindById(
                    dataService, ServiceHelper.getItemWithId(syncProduct.getQbServiceId()));
            if (!checkQbItemStatus(queryItem, accountRef)) {
                // 不合法说明这个product在qb中不存在或者被删除了，或者不属于acctType的账号，后续重新创建
                continue;
            }
            if (needMigration && checkItemIncomeAccountRef(queryItem, moegoIncomeAccountRef)) {
                // 迁移状态下, 如果item的income account是moego的旧逻辑income account则更新到新的账号
                queryItem.setIncomeAccountRef(accountRef);
                dataServiceFactory.dataServiceUpdate(dataService, queryItem);
            }
            returnDataMap.put(syncProduct.getProductId(), queryItem);
            foundProductdList.add(syncProduct.getProductId());
        }
        if (!CollectionUtils.isEmpty(foundProductdList)) {
            // 更新描述
            List<ProductInfoDTO> productInfoList = iProductClient.getProductInfoList(businessId, foundProductdList);
            for (ProductInfoDTO product : productInfoList) {
                returnDataMap.get(product.getId()).setDescription(product.getDescription());
            }
        }
        // 没有同步的 product，在 qb 内重新创建
        List<Integer> notFoundProductIdList = new ArrayList<>(needSyncProductIdList);
        notFoundProductIdList.removeAll(foundProductdList);
        if (CollectionUtils.isEmpty(notFoundProductIdList)) {
            return returnDataMap;
        }

        List<ProductInfoDTO> notFoundProductList = iProductClient.getProductInfoList(businessId, notFoundProductIdList);
        for (ProductInfoDTO product : notFoundProductList) {
            Item saveItem = addUpdateQbItem(
                    dataService, ServiceHelper.convertProductToItem(product, accountRef), product.getId(), businessId);
            MoeQbSyncProduct saveProduct = new MoeQbSyncProduct();
            saveProduct.setRealmId(realmId);
            saveProduct.setBusinessId(businessId);
            saveProduct.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
            saveProduct.setConnectId(dataServiceFactory.getConnectId(businessId));
            saveProduct.setProductId(product.getId());
            saveProduct.setQbServiceId(saveItem.getId());
            saveProduct.setProductName(product.getName());
            saveProduct.setProductDescription(product.getDescription());
            syncProductMapper.insertOrUpdate(saveProduct);
            saveItem.setDescription(product.getDescription());
            returnDataMap.put(product.getId(), saveItem);
        }
        return returnDataMap;
    }

    public Map<Integer, Item> syncBatchServicePackage(
            Integer businessId,
            Map<String, Set<Integer>> needSyncServiceIdMap,
            DataServiceTmpDto dataServiceTmpDto,
            IntuitAccountTypeEnum acctType) {
        if (CollectionUtils.isEmpty(needSyncServiceIdMap)
                || CollectionUtils.isEmpty(needSyncServiceIdMap.get(InvoiceHelper.TYPE_PACKAGE))) {
            return Collections.emptyMap();
        }
        String realmId = dataServiceTmpDto.getRealmId();
        List<Integer> needSyncServicePackageIdList = needSyncServiceIdMap.get(InvoiceHelper.TYPE_PACKAGE).stream()
                .distinct()
                .toList();
        List<MoeQbSyncPackage> moeQbSyncPackages = syncPackageMapper.selectByBusinessIdRealmIdPackageIds(
                businessId, realmId, needSyncServicePackageIdList);

        List<Integer> foundServicePackageIdList = new ArrayList<>();
        Map<Integer, Item> returnDataMap = new HashMap<>(16);
        DataService dataService = dataServiceTmpDto.getDataService();
        ReferenceType accountRef = getServiceAccount(businessId, dataServiceTmpDto, acctType);
        var needMigration = checkIsMigration(businessId);
        // 迁移状态下才检查, 避免创建多余账号
        var moegoIncomeAccountRef = needMigration ? getServiceAccount(businessId, dataServiceTmpDto) : null;
        for (MoeQbSyncPackage syncPackage : moeQbSyncPackages) {
            Item queryItem = dataServiceFactory.dataServiceFindById(
                    dataService, ServiceHelper.getItemWithId(syncPackage.getQbServiceId()));
            if (!checkQbItemStatus(queryItem, accountRef)) {
                // 不合法说明这个service package在qb中不存在或者被删除了，或者不属于acctType的账号，后续重新创建
                continue;
            }
            if (needMigration && checkItemIncomeAccountRef(queryItem, moegoIncomeAccountRef)) {
                // 迁移状态下, 如果item的income account是moego的旧逻辑income account则更新到新的账号
                queryItem.setIncomeAccountRef(accountRef);
                dataServiceFactory.dataServiceUpdate(dataService, queryItem);
            }
            returnDataMap.put(syncPackage.getPackageId(), queryItem);
            foundServicePackageIdList.add(syncPackage.getPackageId());
        }
        if (!CollectionUtils.isEmpty(foundServicePackageIdList)) {
            foundServicePackageIdList.stream()
                    .map(id -> iPackageService.getPackage(new GetPackageParams(id)))
                    .map(GetPackageResult::packageInfo)
                    .forEach(pkg -> returnDataMap.get(pkg.getId()).setDescription(pkg.getDescription()));
        }
        // 没有同步的 service package，在 qb 内重新创建
        List<Integer> notFoundServicePackageIdList = new ArrayList<>(needSyncServicePackageIdList);
        notFoundServicePackageIdList.removeAll(foundServicePackageIdList);
        if (CollectionUtils.isEmpty(notFoundServicePackageIdList)) {
            return returnDataMap;
        }

        notFoundServicePackageIdList.stream()
                .map(id -> iPackageService.getPackage(new GetPackageParams(id)))
                .map(GetPackageResult::packageInfo)
                .forEach(servicePackage -> {
                    Item saveItem = addUpdateQbItem(
                            dataService,
                            ServiceHelper.convertServicePackageToItem(servicePackage, accountRef),
                            servicePackage.getId(),
                            businessId);
                    MoeQbSyncPackage saveServicePackage = new MoeQbSyncPackage();
                    saveServicePackage.setRealmId(realmId);
                    saveServicePackage.setBusinessId(businessId);
                    saveServicePackage.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
                    saveServicePackage.setConnectId(dataServiceFactory.getConnectId(businessId));
                    saveServicePackage.setPackageId(servicePackage.getId());
                    saveServicePackage.setQbServiceId(saveItem.getId());
                    saveServicePackage.setPackageName(servicePackage.getName());
                    saveServicePackage.setPackageDescription(servicePackage.getDescription());
                    syncPackageMapper.insertOrUpdate(saveServicePackage);
                    saveItem.setDescription(servicePackage.getDescription());
                    returnDataMap.put(saveServicePackage.getPackageId(), saveItem);
                });
        return returnDataMap;
    }

    public Map<Integer, Item> syncBatchServiceCharge(
            Integer businessId, Map<String, Set<Integer>> needSyncServiceIdMap, DataServiceTmpDto dataServiceTmpDto) {
        return syncBatchServiceCharge(
                businessId, needSyncServiceIdMap, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE);
    }

    public Map<Integer, Item> syncBatchServiceCharge(
            Integer businessId,
            Map<String, Set<Integer>> needSyncServiceIdMap,
            DataServiceTmpDto dataServiceTmpDto,
            IntuitAccountTypeEnum acctType) {
        if (CollectionUtils.isEmpty(needSyncServiceIdMap)
                || CollectionUtils.isEmpty(needSyncServiceIdMap.get(InvoiceHelper.TYPE_SERVICE_CHARGE))) {
            return Collections.emptyMap();
        }
        String realmId = dataServiceTmpDto.getRealmId();
        List<Integer> needSyncServiceChargeIdList = needSyncServiceIdMap.get(InvoiceHelper.TYPE_SERVICE_CHARGE).stream()
                .distinct()
                .toList();
        List<MoeQbSyncServiceCharge> serviceChargeList =
                syncServiceChargeMapper.selectByBusinessIdRealmIdServiceChargeIds(
                        businessId, realmId, needSyncServiceChargeIdList);
        List<Integer> foundServiceChargeIdList = new ArrayList<>();
        Map<Integer, Item> returnDataMap = new HashMap<>(16);
        DataService dataService = dataServiceTmpDto.getDataService();
        ReferenceType accountRef = getServiceAccount(businessId, dataServiceTmpDto, acctType);
        var needMigration = checkIsMigration(businessId);
        // 迁移状态下才检查, 避免创建多余账号
        var moegoIncomeAccountRef = needMigration ? getServiceAccount(businessId, dataServiceTmpDto) : null;
        for (MoeQbSyncServiceCharge syncServiceCharge : serviceChargeList) {
            Item queryItem = dataServiceFactory.dataServiceFindById(
                    dataService, ServiceHelper.getItemWithId(syncServiceCharge.getQbServiceId()));
            if (!checkQbItemStatus(queryItem, accountRef)) {
                // 不合法说明这个service charge在qb中不存在或者被删除了，或者不属于acctType的账号，后续重新创建
                continue;
            }
            if (needMigration && checkItemIncomeAccountRef(queryItem, moegoIncomeAccountRef)) {
                // 迁移状态下, 如果item的income account是moego的旧逻辑income account则更新到新的账号
                queryItem.setIncomeAccountRef(accountRef);
                dataServiceFactory.dataServiceUpdate(dataService, queryItem);
            }
            returnDataMap.put(syncServiceCharge.getServiceChargeId(), queryItem);
            foundServiceChargeIdList.add(syncServiceCharge.getServiceChargeId());
        }
        if (!CollectionUtils.isEmpty(foundServiceChargeIdList)) {
            // 更新描述
            serviceChargeService
                    .getServiceChargeList(businessId, foundServiceChargeIdList)
                    .forEach(serviceCharge -> returnDataMap
                            .get(serviceCharge.getId().intValue())
                            .setDescription(serviceCharge.getDescription()));
        }
        // 没有同步的 service charge，在 qb 内重新创建
        List<Integer> notFoundServiceChargeIdList = new ArrayList<>(needSyncServiceChargeIdList);
        notFoundServiceChargeIdList.removeAll(foundServiceChargeIdList);
        if (CollectionUtils.isEmpty(notFoundServiceChargeIdList)) {
            return returnDataMap;
        }
        List<ServiceChargeDTO> notFoundServiceChargeList =
                serviceChargeService.getServiceChargeList(businessId, notFoundServiceChargeIdList);
        for (ServiceChargeDTO serviceCharge : notFoundServiceChargeList) {
            Item saveItem = addUpdateQbItem(
                    dataService,
                    ServiceHelper.convertServiceChargeToItem(serviceCharge, accountRef),
                    serviceCharge.getId().intValue(),
                    businessId);
            MoeQbSyncServiceCharge saveServiceCharge = new MoeQbSyncServiceCharge();
            saveServiceCharge.setRealmId(realmId);
            saveServiceCharge.setBusinessId(businessId);
            saveServiceCharge.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
            saveServiceCharge.setConnectId(dataServiceFactory.getConnectId(businessId));
            saveServiceCharge.setServiceChargeId(serviceCharge.getId().intValue());
            saveServiceCharge.setQbServiceId(saveItem.getId());
            saveServiceCharge.setServiceChargeName(serviceCharge.getName());
            saveServiceCharge.setServiceChargeDescription(serviceCharge.getDescription());
            syncServiceChargeMapper.insertOrUpdate(saveServiceCharge);
            saveItem.setDescription(serviceCharge.getDescription());
            returnDataMap.put(saveServiceCharge.getServiceChargeId(), saveItem);
        }
        return returnDataMap;
    }

    public void deleteQbPayment(Integer businessId, Integer invoiceId, DataServiceTmpDto dataServiceTmpDto) {
        String realmId = dataServiceFactory.getConnectRealmId(businessId);
        List<MoeQbSyncPayment> syncPaymentList = syncPaymentMapper.selectByInvoiceId(businessId, realmId, invoiceId);
        for (MoeQbSyncPayment syncPayment : syncPaymentList) {
            if (QuickBooksConst.QB_INVOICE_STATUS_NORMAL.equals(syncPayment.getQbPaymentStatus())) {
                Payment queryPayment = dataServiceFactory.dataServiceFindById(
                        dataServiceTmpDto.getDataService(), PaymentHelper.getPaymentById(syncPayment.getQbPaymentId()));
                if (queryPayment != null && !EntityStatusEnum.DELETED.equals(queryPayment.getStatus())) {
                    Payment deletePayment =
                            dataServiceFactory.dataServiceDelete(dataServiceTmpDto.getDataService(), queryPayment);
                }
                MoeQbSyncPayment updateSyncPayment = new MoeQbSyncPayment();
                updateSyncPayment.setId(syncPayment.getId());
                updateSyncPayment.setQbPaymentStatus(QuickBooksConst.QB_INVOICE_STATUS_DELETE);
                updateSyncPayment.setUpdateTime(DateUtil.get10Timestamp());
                syncPaymentMapper.updateByPrimaryKeySelective(updateSyncPayment);
            }
        }
    }

    public void migrationSyncPayment(
            Integer businessId,
            Invoice invoice,
            Integer invoiceId,
            DataServiceTmpDto dataServiceTmpDto,
            PaymentSummary paymentSummary) {
        log.info(
                "<migration> business id:{}, invoice id:{}, begin sync invoice payment...",
                businessId,
                invoice.getId());
        // 从moego数据库内找已有的payment
        paymentSummary = checkAndSelectPaymentSummary(paymentSummary, invoiceId);
        if (Objects.isNull(paymentSummary) || CollectionUtils.isEmpty(paymentSummary.getPayments())) {
            log.warn("<migration> no payment found, business id:{}, invoiceId:{}", businessId, invoiceId);
            return;
        }
        // 迁移支付记录
        List<PaymentSummary.PaymentDto> paymentDtos = paymentSummary.getPayments();
        String realmId = dataServiceFactory.getConnectRealmId(businessId);
        var setting = moeQbSettingMapper.selectByBusinessIdAndConnectId(businessId, dataServiceTmpDto.getConnectId());
        log.info(
                "<migration> start sync payment and detail,invoice id:{}, business id:{}, user setting version:{}",
                invoice.getId(),
                businessId,
                setting.getUserVersion());
        ReferenceType customerRef = CustomerHelper.getCustomerRef(
                syncCustomer(businessId, paymentSummary.getCustomerId(), dataServiceTmpDto));
        // 迁移时不用考虑
        for (PaymentSummary.PaymentDto paymentDto : paymentDtos) {
            var paymentId = paymentDto.getId();
            PaymentMethod paymentMethod = syncPaymentMethod(businessId, paymentDto.getMethod(), dataServiceTmpDto);
            // 同步过的 payment 就不再同步
            // 但是在新的同步逻辑里要检查 refund 是否有记录
            Long companyId = businessInfoHelper.getCompanyIdByBusinessId(businessId);
            var paymentDepositAccount = getPaymentDepositAccount(businessId, dataServiceTmpDto);
            log.info(
                    "<migration> sync payment, business id:{}, invoice id:{}, paymentId: {}",
                    businessId,
                    invoice.getId(),
                    paymentId);
            var syncPaymentRecord = syncPaymentMapper.selectByBusinessIdAndPaymentId(businessId, realmId, paymentId);
            Payment insertPayment = PaymentHelper.getPaymentByPaymentDto(
                    invoice,
                    customerRef,
                    paymentDto.getAmount(),
                    paymentDto.getCreateTime(),
                    paymentDepositAccount,
                    paymentMethod);
            var businessInfo = iBusinessBusinessClient.getBusinessInfo(
                    InfoIdParams.builder().infoId(businessId).build());
            PaymentHelper.paymentAddNote(insertPayment, paymentDto.getDescription());
            PaymentHelper.paymentAddDocNumber(insertPayment, paymentId.toString());
            if (Objects.nonNull(syncPaymentRecord)) {
                Payment payment = dataServiceFactory.dataServiceFindById(
                        dataServiceTmpDto.getDataService(),
                        PaymentHelper.getPaymentById(syncPaymentRecord.getQbPaymentId()));
                insertPayment.setId(payment.getId());
                insertPayment.setSyncToken(payment.getSyncToken());
            }
            insertPayment = dataServiceFactory.dataServiceUpdate(dataServiceTmpDto.getDataService(), insertPayment);
            MoeQbSyncPayment syncPayment = new MoeQbSyncPayment();
            syncPayment.setBusinessId(businessId);
            syncPayment.setCompanyId(companyId);
            syncPayment.setConnectId(dataServiceFactory.getConnectId(businessId));
            syncPayment.setRealmId(realmId);
            syncPayment.setPaymentId(paymentId);
            syncPayment.setInvoiceId(invoiceId);
            syncPayment.setPaidAmount(insertPayment.getTotalAmt());
            syncPayment.setQbInvoiceId(invoice.getId());
            syncPayment.setQbPaymentId(insertPayment.getId());
            syncPayment.setQbPaymentStatus(QuickBooksConst.QB_INVOICE_STATUS_NORMAL);
            syncPayment.setCreateTime(DateUtil.get10Timestamp());
            syncPayment.setUpdateTime(DateUtil.get10Timestamp());
            if (Objects.isNull(syncPaymentRecord)) {
                syncPaymentMapper.insertSelective(syncPayment);
            } else {
                // 支付记录已经存在, 需要记录一下迁移之前这个payment对应的qb payment的id
                // 避免之后删除之前记录时不知道对应的是哪个
                // 数据模型上payment id 是基于invoice id 创建的, 具体为invoice id + n 是不会出现qb_invoice_id == qb_payment_id 的, 直接记录就行
                setMigrationIdAssociationKey(
                        businessId, realmId, insertPayment.getId(), syncPaymentRecord.getQbPaymentId());
                syncPayment.setId(syncPaymentRecord.getId());
                syncPaymentMapper.updateByPrimaryKeySelective(syncPayment);
            }

            // 只有 moego pay 才会同步 processing fee 到 expanse 账号
            if (paymentDto.getVendor().equalsIgnoreCase(FeatureConst.FC_STRIPE)) {
                log.info(
                        "<migration> sync processing fee, business id:{}, invoice id:{}, paymentId: {}",
                        businessId,
                        invoiceId,
                        paymentId);
                syncProcessingFee(
                        businessId, setting.getSalesReceiptEnable(), paymentDto, paymentMethod, dataServiceTmpDto);
            }

            // 同步 payment 细节
            var details = iPaymentTransactionHistoryClient.getPayDetailsByPaymentIds(List.of(paymentId));
            PayDetailDTO detail = null;
            if (!(Objects.isNull(details) || details.isEmpty())) {
                detail = details.get(0);
                log.info(
                        "<migration> sync payment detail, business id:{}, invoice id:{}, id: {}",
                        businessId,
                        invoiceId,
                        detail.getId());
                // 迁移逻辑不用考虑是否同步过
                syncPaymentDetails(detail, businessId, dataServiceTmpDto, customerRef, paymentMethod);
            }

            // 同步 refunds, 迁移前不可能有refund的同步, 所以不用考虑迁移，都是新增
            log.info(
                    "<migration> sync refunds, business id:{}, invoice id:{}, paymentId: {}",
                    businessId,
                    invoiceId,
                    paymentId);
            syncRefunds(businessId, paymentId, dataServiceTmpDto, customerRef, detail);
        }
    }

    public void syncInvoicePayment(
            Integer businessId,
            Invoice invoice,
            Integer invoiceId,
            DataServiceTmpDto dataServiceTmpDto,
            PaymentSummary paymentSummary) {
        log.info("business id:{}, invoice id:{}, begin sync invoice payment...", businessId, invoice.getId());
        // 从moego数据库内找已有的payment
        paymentSummary = checkAndSelectPaymentSummary(paymentSummary, invoiceId);
        if (Objects.isNull(paymentSummary) || CollectionUtils.isEmpty(paymentSummary.getPayments())) {
            log.warn("no payment found, business id:{}, invoiceId:{}", businessId, invoiceId);
            return;
        }
        List<PaymentSummary.PaymentDto> paymentDtos = paymentSummary.getPayments();
        // 查询qb payment记录，查看同步记录
        String realmId = dataServiceFactory.getConnectRealmId(businessId);
        //
        List<MoeQbSyncPayment> syncPaymentList = syncPaymentMapper.selectByInvoiceId(businessId, realmId, invoiceId);
        var syncedPayment =
                syncPaymentList.stream().map(MoeQbSyncPayment::getPaymentId).collect(Collectors.toSet());
        var setting = moeQbSettingMapper.selectByBusinessIdAndConnectId(businessId, dataServiceTmpDto.getConnectId());
        log.info(
                "start sync payment and detail,invoice id:{}, business id:{}, user setting version:{}",
                invoice.getId(),
                businessId,
                setting.getUserVersion());
        ReferenceType customerRef = CustomerHelper.getCustomerRef(
                syncCustomer(businessId, paymentSummary.getCustomerId(), dataServiceTmpDto));
        for (PaymentSummary.PaymentDto paymentDto : paymentDtos) {
            var paymentId = paymentDto.getId();
            PaymentMethod paymentMethod = syncPaymentMethod(businessId, paymentDto.getMethod(), dataServiceTmpDto);
            // 同步过的 payment 就不再同步
            // 但是在新的同步逻辑里要检查 refund 是否有记录
            Long companyId = businessInfoHelper.getCompanyIdByBusinessId(businessId);
            if (!syncedPayment.contains(paymentId)) {
                var paymentDepositAccount = getPaymentDepositAccount(businessId, dataServiceTmpDto);
                log.info(
                        "sync payment, business id:{}, invoice id:{}, paymentId: {}",
                        businessId,
                        invoice.getId(),
                        paymentId);
                Payment insertPayment = PaymentHelper.getPaymentByPaymentDto(
                        invoice,
                        customerRef,
                        paymentDto.getAmount(),
                        paymentDto.getCreateTime(),
                        paymentDepositAccount,
                        paymentMethod);
                PaymentHelper.paymentAddNote(insertPayment, paymentDto.getDescription());
                PaymentHelper.paymentAddDocNumber(insertPayment, paymentId.toString());
                insertPayment = dataServiceFactory.dataServiceAdd(dataServiceTmpDto.getDataService(), insertPayment);
                MoeQbSyncPayment syncPayment = new MoeQbSyncPayment();
                syncPayment.setBusinessId(businessId);
                syncPayment.setCompanyId(companyId);
                syncPayment.setConnectId(dataServiceFactory.getConnectId(businessId));
                syncPayment.setRealmId(realmId);
                syncPayment.setPaymentId(paymentId);
                syncPayment.setInvoiceId(invoiceId);
                syncPayment.setPaidAmount(insertPayment.getTotalAmt());
                syncPayment.setQbInvoiceId(invoice.getId());
                syncPayment.setQbPaymentId(insertPayment.getId());
                syncPayment.setQbPaymentStatus(QuickBooksConst.QB_INVOICE_STATUS_NORMAL);
                syncPayment.setCreateTime(DateUtil.get10Timestamp());
                syncPayment.setUpdateTime(DateUtil.get10Timestamp());
                syncPaymentMapper.insertSelective(syncPayment);
            }

            // 对于新逻辑, 在同步 bank account 之后同步 payment details
            if (QuickBooksConst.USER_VERSION_NEW.equals(setting.getUserVersion())) {
                // 只有 moego pay 才会同步 processing fee 到 expanse 账号
                if (!syncedPayment.contains(paymentId)
                        && paymentDto.getVendor().equalsIgnoreCase(FeatureConst.FC_STRIPE)) {
                    syncProcessingFee(
                            businessId, setting.getSalesReceiptEnable(), paymentDto, paymentMethod, dataServiceTmpDto);
                }

                // 同步 payment 细节
                var details = iPaymentTransactionHistoryClient.getPayDetailsByPaymentIds(List.of(paymentId));
                PayDetailDTO detail = null;
                if (!(Objects.isNull(details) || details.isEmpty())) {
                    detail = details.get(0);
                    // ERP-12053 部分用户反馈不需要sales receipts, 在setting 控制一个开关判断是否同步
                    if (!syncedPayment.contains(paymentId)
                            && QuickBooksConst.QB_SYNC_SALES_RECEIPT_OPEN.equals(setting.getSalesReceiptEnable())) {
                        log.info(
                                "sync payment detail, business id:{}, invoice id:{}, id: {}",
                                businessId,
                                invoiceId,
                                detail.getId());
                        syncPaymentDetails(detail, businessId, dataServiceTmpDto, customerRef, paymentMethod);
                    }
                }
                // 同步 refunds
                syncRefunds(businessId, paymentId, dataServiceTmpDto, customerRef, detail);
            }
        }
        if (QuickBooksConst.USER_VERSION_NEW.equals(setting.getUserVersion())) {
            // 该商家的历史invoice中可能会有refund, 会在这里进行同步
            Set<Integer> redisNeedSyncPaymentList = getRedisNeedSyncPaymentList(businessId);
            log.info(
                    "synchronize individual payments,business id:{}, invoice id:{}, size: {}",
                    businessId,
                    invoiceId,
                    redisNeedSyncPaymentList.size());
            redisNeedSyncPaymentList.forEach(paymentId -> {
                var details = iPaymentTransactionHistoryClient.getPayDetailsByPaymentIds(List.of(paymentId));
                PayDetailDTO detail = details.stream().findFirst().orElse(null);

                PaymentDTO payment = iPaymentService.getPaymentMethodById(paymentId);
                var paymentInvoiceId = payment.getInvoiceId();
                var ps = getPaymentSummary(paymentInvoiceId);
                ps = checkAndSelectPaymentSummary(ps, paymentInvoiceId);
                // 需要找到对应的 invoice 和 payment customer, 不然会乱
                ReferenceType paymentCustomer =
                        CustomerHelper.getCustomerRef(syncCustomer(businessId, ps.getCustomerId(), dataServiceTmpDto));

                syncRefunds(businessId, paymentId, dataServiceTmpDto, paymentCustomer, detail);
            });
            delRedisNeedSyncPaymentKey(businessId);
        }
        log.info("business id: {}, invoice id:{}, sync payment end...", businessId, invoiceId);
    }

    private PaymentSummary getPaymentSummary(Integer invoiceId) {
        GetPaymentParams p = new GetPaymentParams();
        p.setModule(PaymentMethodEnum.MODULE_GROOMING);
        p.setInvoiceId(Math.toIntExact(invoiceId));
        return Optional.ofNullable(iPaymentService.getPayments(p))
                .map(ResponseResult::getData)
                .orElse(null);
    }

    public PaymentSummary checkAndSelectPaymentSummary(PaymentSummary paymentSummary, Integer invoiceId) {
        if (paymentSummary == null) {
            GetPaymentParams p = new GetPaymentParams();
            p.setModule(PaymentMethodEnum.MODULE_GROOMING);
            p.setInvoiceId(invoiceId);
            paymentSummary = iPaymentService.getPayments(p).getData();
        }
        if (paymentSummary == null) {
            GetPaymentParams p = new GetPaymentParams();
            p.setModule(PaymentMethodEnum.MODULE_RETAIL);
            p.setInvoiceId(invoiceId);
            paymentSummary = iPaymentService.getPayments(p).getData();
        }
        return paymentSummary;
    }

    /**
     * 同步refund, 在这一步确定要同步的refund list
     */
    public void syncRefunds(
            Integer businessId,
            Integer paymentId,
            DataServiceTmpDto dataServiceTmpDto,
            ReferenceType customerRef,
            PayDetailDTO detail) {
        // 已经同步过的refunds就不要同步了
        Set<Integer> syncedRefundIds =
                syncRefundMapper
                        .selectByBusinessIdAndPaymentId(businessId, dataServiceTmpDto.getRealmId(), paymentId)
                        .stream()
                        .map(MoeQbSyncRefund::getRefundId)
                        .collect(Collectors.toSet());
        List<RefundDTO> refunds = iPaymentRefundClient.getRefunds(paymentId).stream()
                .filter(refund -> !syncedRefundIds.contains(refund.getId()))
                .toList();
        syncPaymentRefunds(businessId, paymentId, refunds, dataServiceTmpDto, customerRef, detail);
    }

    /**
     * 同步 payment refunds
     */
    public void syncPaymentRefunds(
            Integer businessId,
            Integer paymentId,
            List<RefundDTO> refunds,
            DataServiceTmpDto dataServiceTmpDto,
            ReferenceType customerRef,
            PayDetailDTO detail) {
        log.info("business id:{}, sync payment refunds, size: {}", businessId, refunds.size());
        if (CollectionUtils.isEmpty(refunds)) return;
        RefundReceipt refundReceipt = new RefundReceipt();
        CreditMemo creditMemo = new CreditMemo();
        // refund 的同步有两种情况, 第一种: 直接同步到moego pay payments
        long refundDate = 0L;
        for (var refund : refunds) {
            refundDate = Math.max(refundDate, refund.getUpdateTime());
            // 对于 qb 来说, refund 的 item 也是 service, 所以直接复用方法
            Item item = getDefaultInvoiceItem(
                    businessId,
                    QuickBooksConst.QB_SERVICE_REFUND_FROM_PAYMENT,
                    dataServiceTmpDto,
                    IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_REFUND_FROM_MOEGO_PAYMENT);
            salesReceiptAddLine(refundReceipt, item, refund.getAmount());
            // ERP-12053 credit memo 不区分moego pay
            Item creaditMemoItem = getDefaultInvoiceItem(
                    businessId,
                    QuickBooksConst.QB_SERVICE_REFUND_FROM_CREDIT_MEMO,
                    dataServiceTmpDto,
                    IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_REFUND_FROM_CREDIT_MEMO);
            salesReceiptAddLine(creditMemo, creaditMemoItem, refund.getAmount());
            ThreadPool.execute(() -> {
                var syncRefund = new MoeQbSyncRefund();
                syncRefund.setBusinessId(businessId);
                syncRefund.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
                syncRefund.setQbRefundId(item.getId());
                syncRefund.setRefundId(refund.getId());
                syncRefund.setPaymentId(paymentId);
                syncRefund.setRefundName(QuickBooksConst.QB_SERVICE_REFUND_FROM_PAYMENT);
                syncRefund.setConnectId(dataServiceTmpDto.getConnectId());
                syncRefund.setRealmId(dataServiceTmpDto.getRealmId());
                syncRefund.setCreateTime(DateUtil.get10Timestamp());
                syncRefund.setUpdateTime(DateUtil.get10Timestamp());
                syncRefund.setRefundDescription(refund.getReason());
                syncRefundMapper.insertSelective(syncRefund);
            });
        }
        refundReceipt.setTxnDate(QuickBooksDateUtils.getDateTimeByLong(refundDate * 1000L));
        refundReceipt.setCustomerRef(customerRef);
        refundReceipt.setPrivateNote(QuickBooksConst.QB_SYNC_REFUND_BY_PAYMENT);
        refundReceipt.setDepositToAccountRef(
                getServiceAccount(businessId, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_PAYMENTS));
        dataServiceFactory.dataServiceAdd(dataServiceTmpDto.getDataService(), refundReceipt);

        saveReceiptToDB(
                detail.getId(), businessId, dataServiceTmpDto, refundReceipt, QuickBooksConst.RECEIPT_TYPE_REFUND);

        creditMemo.setTxnDate(QuickBooksDateUtils.getDateTimeByLong(refundDate * 1000L));
        creditMemo.setCustomerRef(customerRef);
        // creditMemo 同步ar 账号
        //        creditMemo.setDepositToAccountRef(depositAccount);
        dataServiceFactory.dataServiceAdd(dataServiceTmpDto.getDataService(), creditMemo);
        saveReceiptToDB(
                detail.getId(), businessId, dataServiceTmpDto, creditMemo, QuickBooksConst.RECEIPT_TYPE_CREDIT_MEMO);

        // ERP-12053 这里根据是否同步sales receipt来判断是否需要同步多一个refund receipt
        var setting = moeQbSettingMapper.selectByBusinessIdAndConnectId(businessId, dataServiceTmpDto.getConnectId());
        if (QuickBooksConst.QB_SYNC_SALES_RECEIPT_OPEN.equals(setting.getSalesReceiptEnable())) {
            syncPaymentDetailRefunds(businessId, refunds, dataServiceTmpDto, customerRef, detail);
        }
    }

    /**
     * 根据支付记录的支付类型选择 deposit account
     * 如果支付记录属于moego pay, 不能放在默认的 bank account 中, 因为会导致记录重复(与银行同步重复)
     * 如果不属于 moego pay(例如 cash), 不会有银行记录同步, 所以同步到 business 自己选择的 bank account 中
     * 其中兼容旧逻辑, 旧逻辑不考虑支付类型
     */
    private ReferenceType getPaymentDepositAccount(Integer businessId, DataServiceTmpDto dataServiceTmpDto) {
        var setting = moeQbSettingMapper.selectByBusinessIdAndConnectId(businessId, dataServiceTmpDto.getConnectId());
        if (QuickBooksConst.USER_VERSION_OLD.equals(setting.getUserVersion()))
            return AccountHelper.getAccountRef(getBusinessDepositAccount(dataServiceTmpDto));
        return getServiceAccount(businessId, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_PAYMENTS);
    }
    /**
     * 同步 payment detail 对应的 refund 记录
     * @param refunds payment detail 对应的 refund 记录
     */
    public void syncPaymentDetailRefunds(
            Integer businessId,
            List<RefundDTO> refunds,
            DataServiceTmpDto dataServiceTmpDto,
            ReferenceType customerRef,
            PayDetailDTO detail) {
        log.info("business id:{}, sync moego pay payment refunds, size: {}", businessId, refunds.size());
        RefundReceipt refundReceipt = new RefundReceipt();
        long refundDate = 0L;
        for (var refund : refunds) {
            // sales receipt
            refundDate = Math.max(refundDate, refund.getUpdateTime());
            Item saleReceiptRefundItem = getDefaultInvoiceItem(
                    businessId,
                    QuickBooksConst.QB_SERVICE_REFUND_FROM_SALE_RECEIPT,
                    dataServiceTmpDto,
                    IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_RECEIVED_NET_SALE);
            // moego pay 的 退款记录需要同步到 expanse 账号
            salesReceiptAddLine(refundReceipt, saleReceiptRefundItem, refund.getAmount());
        }
        ReferenceType depositAccount = getServiceAccount(
                businessId, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_PAY_SALES_RECEIPT);
        refundReceipt.setTxnDate(QuickBooksDateUtils.getDateTimeByLong(refundDate * 1000L));
        refundReceipt.setDepositToAccountRef(depositAccount);
        refundReceipt.setCustomerRef(customerRef);
        refundReceipt.setPrivateNote(QuickBooksConst.QB_SYNC_REFUND_BY_SALES_RECEIPT);
        refundReceipt = dataServiceFactory.dataServiceAdd(dataServiceTmpDto.getDataService(), refundReceipt);
        saveReceiptToDB(
                detail.getId(), businessId, dataServiceTmpDto, refundReceipt, QuickBooksConst.RECEIPT_TYPE_REFUND);
    }

    /**
     * 同步 payment 中的 processing fee, 以 purchase 的形式, 其中 processing fee是去向 expanse 账号的
     */
    public void syncProcessingFee(
            Integer businessId,
            Byte receiptEnable,
            PaymentSummary.PaymentDto paymentDto,
            PaymentMethod paymentMethod,
            DataServiceTmpDto dataServiceTmpDto) {
        var processingFee = paymentDto.getProcessingFee();
        if (processingFee.compareTo(BigDecimal.ZERO) > 0) {
            log.info("business id:{}, sync processing fee, payment id:{}", businessId, paymentDto.getId());
            // processing fee 以 purchase 的形式上传到 qb
            Purchase purchase = new Purchase();
            purchase.setPaymentMethodRef(PaymentMethodHelper.getPaymentMethodRef(paymentMethod));
            purchase.setPaymentType(PaymentTypeEnum.CASH);
            if (Objects.nonNull(paymentDto.getCreateTime())) {
                purchase.setTxnDate(QuickBooksDateUtils.getDateTimeByLong(paymentDto.getCreateTime() * 1000));
            }
            // ERP-12503 sales receipt 账号中的expanse 迁移到moego payments 里, 对存量用户不变, 增量用户为Close
            if (QuickBooksConst.QB_SYNC_SALES_RECEIPT_OPEN.equals(receiptEnable)) {
                purchase.setAccountRef(getServiceAccount(
                        businessId, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_PAY_SALES_RECEIPT));
            } else {
                purchase.setAccountRef(getServiceAccount(
                        businessId, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_PAYMENTS));
            }
            AccountBasedExpenseLineDetail expenseLineDetail = new AccountBasedExpenseLineDetail();
            expenseLineDetail.setAccountRef(
                    getServiceAccount(businessId, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_EXPENSE));
            Line line = new Line();
            line.setDetailType(LineDetailTypeEnum.ACCOUNT_BASED_EXPENSE_LINE_DETAIL);
            line.setAmount(processingFee);
            line.setAccountBasedExpenseLineDetail(expenseLineDetail);
            purchase.setLine(List.of(line));
            dataServiceFactory.dataServiceAdd(dataServiceTmpDto.getDataService(), purchase);
        }
    }

    /**
     * 同步 payment 的细节, 即将每笔支付记录拆分成 gross sales\tips \tax 进行同步
     * @param detail payment detail
     */
    public void syncPaymentDetails(
            PayDetailDTO detail,
            Integer businessId,
            DataServiceTmpDto dataServiceTmpDto,
            ReferenceType customerRef,
            PaymentMethod paymentMethod) {
        // payment detail 是以 sales receipt 形式同步的, detail\tips\tax 都是其中的 item
        SalesReceipt salesReceipt = new SalesReceipt();
        salesReceipt.setCustomerRef(customerRef);
        salesReceipt.setTxnDate(detail.getCreateTime());
        salesReceipt.setPaymentMethodRef(PaymentMethodHelper.getPaymentMethodRef(paymentMethod));
        salesReceipt.setDepositToAccountRef(getServiceAccount(
                businessId, dataServiceTmpDto, IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_PAY_SALES_RECEIPT));
        // 同步 payment detail 的 net sale 与 grow sale, 这部分和payment一样, 不会更新
        syncPaymentDetailItem(
                salesReceipt, businessId, QuickBooksConst.QB_SERVICE_RECEIVED_RECEIVED_SALE, detail, dataServiceTmpDto);
        // 同步 tips
        if (detail.getTips().compareTo(BigDecimal.ZERO) != 0) {
            modifySalesTaxPayableAccType(
                    businessId,
                    dataServiceTmpDto,
                    IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_RECEIVED_TIPS,
                    AccountTypeEnum.INCOME);
            syncPaymentDetailItem(
                    salesReceipt, businessId, QuickBooksConst.QB_SERVICE_RECEIVED_TIPS, detail, dataServiceTmpDto);
            modifySalesTaxPayableAccType(
                    businessId,
                    dataServiceTmpDto,
                    IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_RECEIVED_TIPS,
                    AccountTypeEnum.OTHER_CURRENT_LIABILITY);
        }
        // 同步 tax
        if (detail.getTax().compareTo(BigDecimal.ZERO) != 0) {
            modifySalesTaxPayableAccType(
                    businessId,
                    dataServiceTmpDto,
                    IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_RECEIVED_TAX_PAYABLE,
                    AccountTypeEnum.INCOME);
            syncPaymentDetailItem(
                    salesReceipt, businessId, QuickBooksConst.QB_SERVICE_RECEIVED_TAX, detail, dataServiceTmpDto);
            modifySalesTaxPayableAccType(
                    businessId,
                    dataServiceTmpDto,
                    IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_RECEIVED_TAX_PAYABLE,
                    AccountTypeEnum.OTHER_CURRENT_LIABILITY);
        }
        var addSalesReceipt = dataServiceFactory.dataServiceAdd(dataServiceTmpDto.getDataService(), salesReceipt);
        // 如果数据同步 QB 成功,会返回数据,此时存储到moego的db中
        if (Objects.nonNull(addSalesReceipt)) {
            saveReceiptToDB(
                    detail.getId(), businessId, dataServiceTmpDto, addSalesReceipt, QuickBooksConst.RECEIPT_TYPE_SALES);
        }
    }

    /**
     * 同步 payment detail 的数据, 拆分成 gross sales\tips\tax
     * @param businessId 商家的business id
     * @param salesReceiptType 这个 sales receipt 的类型, {@see com.moego.common.enums.QuickBooksConst}
     * @param detail pay detail 的数据
     * @param dataServiceTmpDto 链接 QB 的信息
     */
    public void syncPaymentDetailItem(
            SalesReceipt salesReceipt,
            Integer businessId,
            String salesReceiptType,
            PayDetailDTO detail,
            DataServiceTmpDto dataServiceTmpDto) {
        // 基于 item name 创建 item,并且这个 item 归属于指定类型账号
        // 这里可以共用 invoice item 的方法, 因为每一个 item 都是一个 service
        Item item = getDefaultInvoiceItem(
                businessId, salesReceiptType, dataServiceTmpDto, ReceiptHelper.getAccountType(salesReceiptType));
        if (QuickBooksConst.QB_SERVICE_RECEIVED_RECEIVED_SALE.equals(salesReceiptType)) {
            item.setDescription("The value after discounting from gross sales, if any discounts are applied");
        }
        salesReceiptAddLine(salesReceipt, item, ReceiptHelper.getPaymentDetailAmount(salesReceiptType, detail));
    }

    /**
     * 基于 item 在 sales receipt 中添加条目(line)
     * @param salesReceipt 需要添加条目的 sales receipt
     * @param item 每个条目的内容
     * @param price 条目金额
     */
    private void salesReceiptAddLine(SalesTransaction salesReceipt, Item item, BigDecimal price) {
        Line line = new Line();
        if (StringUtils.hasText(item.getDescription())) {
            line.setDescription(item.getDescription());
        }
        line.setAmount(price);
        line.setDetailType(LineDetailTypeEnum.SALES_ITEM_LINE_DETAIL);
        // line 是指一个条目, 而条目的具体内容是 salesItem, 所以这里需要先创建一个 sales item, 并 set 到 line 里
        SalesItemLineDetail silDetails = new SalesItemLineDetail();
        silDetails.setItemRef(ServiceHelper.getItemRef(item));
        silDetails.setQty(new BigDecimal(1));
        silDetails.setRatePercent(new BigDecimal(0));
        silDetails.setUnitPrice(price);
        silDetails.setTaxCodeRef(null);
        silDetails.setTaxInclusiveAmt(null);
        line.setSalesItemLineDetail(silDetails);
        salesReceipt.getLine().add(line);
    }

    private void saveReceiptToDB(
            Integer paymentDetailId,
            Integer businessId,
            DataServiceTmpDto dataServiceTmpDto,
            SalesTransaction receipt,
            Byte receiptType) {
        MoeQbSyncReceipt saveReceipt = new MoeQbSyncReceipt();
        saveReceipt.setBusinessId(businessId);
        saveReceipt.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
        saveReceipt.setConnectId(dataServiceTmpDto.getConnectId());
        saveReceipt.setRealmId(dataServiceTmpDto.getRealmId());
        saveReceipt.setQbReceiptId(receipt.getId());
        saveReceipt.setQbReceiptStatus(QuickBooksConst.QB_INVOICE_STATUS_NORMAL);
        saveReceipt.setCreateTime(new Date());
        saveReceipt.setUpdateTime(new Date());
        saveReceipt.setPaymentDetailId(paymentDetailId);
        saveReceipt.setAmount(receipt.getLine().stream().map(Line::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        saveReceipt.setReceiptType(receiptType);
        var receipts = syncReceiptMapper.selectByBusinessRealmIdDetailIdAndType(
                businessId, dataServiceTmpDto.getRealmId(), paymentDetailId, receiptType);
        Optional.ofNullable(receipts)
                .map(List::stream)
                .flatMap(Stream::findFirst)
                .ifPresentOrElse(
                        save -> {
                            saveReceipt.setId(save.getId());
                            syncReceiptMapper.updateByPrimaryKeySelective(saveReceipt);
                        },
                        () -> syncReceiptMapper.insertSelective(saveReceipt));
    }

    private Account getBusinessDepositAccount(DataServiceTmpDto dataServiceTmpDto) {
        MoeQbConnect connect = dataServiceTmpDto.getQbConnect();
        if (!StringUtils.isEmpty(connect.getAccountId()) && !StringUtils.isEmpty(connect.getAccountName())) {
            Account queryAccount = dataServiceFactory.dataServiceFindById(
                    dataServiceTmpDto.getDataService(), AccountHelper.getAccountWithId(connect.getAccountId()));
            if (queryAccount == null
                    || !queryAccount.isActive()
                    || EntityStatusEnum.DELETED.equals(queryAccount.getStatus())) {
                // 把设置清除
                MoeQbConnect updateConnect = new MoeQbConnect();
                updateConnect.setId(connect.getId());
                updateConnect.setUpdateTime(DateUtil.get10Timestamp());
                updateConnect.setAccountName("");
                updateConnect.setAccountId("");
                moeQbConnectMapper.updateByPrimaryKeySelective(updateConnect);
                // 存在但是被删除了，返回null
                return null;
            }
            return queryAccount;
        }
        return null;
    }

    public MoeQbSyncAccount saveMoegoDefaultServiceAccount(
            DataService dataService, Integer businessId, String realmId, IntuitAccountTypeEnum accType) {
        // create quickbook account, 适配新逻辑, 旧逻辑这里依然会创建默认的MoeGo Invoice 帐号
        var createAccount = createAccountByQuickBook(dataService, accType);
        // save sync account in moego db
        MoeQbSyncAccount qbSyncAccount = new MoeQbSyncAccount();
        qbSyncAccount.setRealmId(realmId);
        qbSyncAccount.setBusinessId(businessId);
        qbSyncAccount.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
        qbSyncAccount.setConnectId(dataServiceFactory.getConnectId(businessId));
        qbSyncAccount.setName(createAccount.getName());
        qbSyncAccount.setQbAccountId(createAccount.getId());
        qbSyncAccount.setType(accType.getCode());
        qbSyncAccount.setCreateTime(DateUtil.get10Timestamp());
        qbSyncAccount.setUpdateTime(qbSyncAccount.getCreateTime());
        syncAccountMapper.insertSelective(qbSyncAccount);
        return qbSyncAccount;
    }

    private Account createAccountByQuickBook(DataService dataService, IntuitAccountTypeEnum accType) {
        if (Objects.isNull(accType))
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "create quick book account type cannot be null!");
        log.info("create quick book account, type: {}", accType.getName());
        Account createAccount =
                AccountHelper.createAccount(accType.getName(), AccountHelper.convertAccTypeEnum(accType));
        if (accType.getIsSubAccount()) {
            // 如果是子帐号类型, 就要先创建父类型帐号, 并关联
            Account parentAccount =
                    createAccountByQuickBook(dataService, IntuitAccountTypeEnum.getParentAccountType(accType));
            createAccount = AccountHelper.createAccountWithParent(
                    accType.getName(),
                    AccountHelper.convertAccTypeEnum(accType),
                    AccountHelper.getAccountRef(parentAccount));
        }
        try {
            createAccount = dataServiceFactory.dataServiceAdd(dataService, createAccount);
        } catch (CommonException e) {
            if (Code.CODE_QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS.getNumber() == e.getCode()) {
                log.info("quick book account name exists...");
                QueryResult queryAccount = dataServiceFactory.executeQuery(
                        dataService, String.format("select * from account where name = '%s'", createAccount.getName()));
                if (queryAccount != null && !CollectionUtils.isEmpty(queryAccount.getEntities())) {
                    createAccount = (Account) queryAccount.getEntities().get(0);
                } else {
                    throw e;
                }
            } else {
                throw e;
            }
        }
        return createAccount;
    }

    /**
     * 操作 quickbook 进行 update account 操作
     *
     * @param dataService qb online 组件, 用于进行 crud 操作
     * @param account     需要更新的 account
     */
    private void updateAccountByQuickBook(DataService dataService, Account account) {
        if (Objects.isNull(account))
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "update quick book account cannot be null!");
        dataServiceFactory.dataServiceUpdate(dataService, account);
    }

    public Customer syncCustomer(Integer businessId, Integer customerId, DataServiceTmpDto dataServiceTmpDto) {
        List<Integer> customerIdList = new ArrayList<>();
        customerIdList.add(customerId);
        return syncBatchCustomer(businessId, dataServiceTmpDto, customerIdList).get(customerId);
    }

    public PaymentMethod syncPaymentMethod(Integer businessId, String method, DataServiceTmpDto dataServiceTmpDto) {
        String realmId = dataServiceTmpDto.getRealmId();
        DataService dataService = dataServiceTmpDto.getDataService();
        MoeQbSyncPaymentMethod syncPaymentMethod =
                syncPaymentMethodMapper.selectByBusinessIdRealmIdMethodName(businessId, realmId, method);
        PaymentMethod paymentMethod = null;
        if (syncPaymentMethod == null) {
            // create qb_payment_method
            paymentMethod = insertQbPaymentMethod(
                    businessId, PaymentMethodHelper.getPaymentMethodByName(method), null, dataServiceTmpDto);
        } else {
            // query qb_payment_method
            paymentMethod = dataServiceFactory.dataServiceFindById(
                    dataService, PaymentMethodHelper.getPaymentMethodById(syncPaymentMethod.getQbPaymentMethodId()));
            if (paymentMethod == null
                    || !paymentMethod.isActive()
                    || EntityStatusEnum.DELETED.equals(paymentMethod.getStatus())) {
                paymentMethod = insertQbPaymentMethod(
                        businessId,
                        PaymentMethodHelper.getPaymentMethodByName(method),
                        syncPaymentMethod.getId(),
                        dataServiceTmpDto);
            }
        }
        return paymentMethod;
    }

    public PaymentMethod insertQbPaymentMethod(
            Integer businessId,
            PaymentMethod paymentMethod,
            Integer syncPaymentMethodId,
            DataServiceTmpDto dataServiceTmpDto) {
        QueryResult queryResult = dataServiceFactory.executeQuery(
                dataServiceTmpDto.getDataService(),
                String.format("select * from paymentmethod where Name ='%s'", paymentMethod.getName()));
        PaymentMethod returnPaymentMethod = null;
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getEntities())) {
            returnPaymentMethod = dataServiceFactory.dataServiceAdd(dataServiceTmpDto.getDataService(), paymentMethod);
        } else {
            returnPaymentMethod = (PaymentMethod) queryResult.getEntities().get(0);
        }
        MoeQbSyncPaymentMethod syncPaymentMethod = new MoeQbSyncPaymentMethod();
        syncPaymentMethod.setConnectId(dataServiceFactory.getConnectId(businessId));
        syncPaymentMethod.setMethod(paymentMethod.getName());
        syncPaymentMethod.setQbPaymentMethodId(returnPaymentMethod.getId());
        syncPaymentMethod.setUpdateTime(DateUtil.get10Timestamp());
        if (syncPaymentMethodId == null) {
            syncPaymentMethod.setCreateTime(DateUtil.get10Timestamp());
            syncPaymentMethod.setBusinessId(businessId);
            syncPaymentMethod.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
            syncPaymentMethod.setRealmId(dataServiceFactory.getConnectRealmId(businessId));
            syncPaymentMethodMapper.insertSelective(syncPaymentMethod);
        } else {
            syncPaymentMethod.setId(syncPaymentMethodId);
            syncPaymentMethodMapper.updateByPrimaryKeySelective(syncPaymentMethod);
        }
        return returnPaymentMethod;
    }

    public Map<Integer, Customer> syncBatchCustomer(
            Integer businessId, DataServiceTmpDto dataServiceTmpDto, List<Integer> customerIdList) {
        String realmId = dataServiceTmpDto.getRealmId();
        List<MoeQbSyncCustomer> customers =
                syncCustomerMapper.selectByBusinessIdRealmIdCustomerIds(businessId, realmId, customerIdList);
        List<Integer> foundCustomerIdList = new ArrayList<>();
        List<Integer> errorCustomerId = new ArrayList<>();
        Map<Integer, Customer> returnDataMap = new HashMap<>();
        DataService dataService = dataServiceTmpDto.getDataService();
        var businessInfo = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(businessId).build());
        for (MoeQbSyncCustomer syncCustomer : customers) {
            Customer queryCustomer = dataServiceFactory.dataServiceFindById(
                    dataService, CustomerHelper.getCustomerWithId(syncCustomer.getQbCustomerId()));
            if (queryCustomer == null
                    || EntityStatusEnum.DELETED.equals(queryCustomer.getStatus())
                    || !queryCustomer.isActive()) {
                // 存在但是被删除了，需要更新数据
                errorCustomerId.add(syncCustomer.getCustomerId());
                continue;
            }
            returnDataMap.put(syncCustomer.getCustomerId(), queryCustomer);
            foundCustomerIdList.add(syncCustomer.getCustomerId());
        }
        List<Integer> notFoundCustomerIdList = new ArrayList<>(customerIdList);
        notFoundCustomerIdList.removeAll(foundCustomerIdList);
        if (!CollectionUtils.isEmpty(notFoundCustomerIdList)) {
            // query customer from qb
            List<CustomerQbQueryDto> customerDtoList = getCustomerQBDTOList(notFoundCustomerIdList);

            for (CustomerQbQueryDto customerDto : customerDtoList) {
                Customer saveCustomer = addUpdateQbCustomer(
                        dataService,
                        CustomerHelper.convertDtoToModel(customerDto, businessInfo.getBusinessName()),
                        customerDto.getCustomerId());
                if (errorCustomerId.contains(customerDto.getCustomerId())) {
                    // update
                    MoeQbSyncCustomer updateCustomer = new MoeQbSyncCustomer();
                    // where
                    updateCustomer.setRealmId(realmId);
                    updateCustomer.setBusinessId(businessId);
                    updateCustomer.setConnectId(dataServiceFactory.getConnectId(businessId));
                    updateCustomer.setCustomerId(customerDto.getCustomerId());
                    updateCustomer.setUpdateTime(DateUtil.get10Timestamp());
                    // data
                    updateCustomer.setQbCustomerId(saveCustomer.getId());
                    syncCustomerMapper.updateByBusinessIdRealmIdCustomerId(updateCustomer);
                } else {
                    // insert
                    MoeQbSyncCustomer addCustomer = new MoeQbSyncCustomer();
                    addCustomer.setRealmId(realmId);
                    addCustomer.setBusinessId(businessId);
                    addCustomer.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
                    addCustomer.setQbCustomerId(saveCustomer.getId());
                    addCustomer.setConnectId(dataServiceFactory.getConnectId(businessId));
                    addCustomer.setCustomerId(customerDto.getCustomerId());
                    addCustomer.setCreateTime(DateUtil.get10Timestamp());
                    addCustomer.setUpdateTime(addCustomer.getCreateTime());
                    syncCustomerMapper.insertSelective(addCustomer);
                }
                returnDataMap.put(customerDto.getCustomerId(), saveCustomer);
            }
        }
        return returnDataMap;
    }

    private List<CustomerQbQueryDto> getCustomerQBDTOList(List<Integer> customerIdList) {
        return customerIdList.stream()
                .map(id -> {
                    var dto = new CustomerQbQueryDto();
                    GetCustomerResponse customerInfoResp = businessCustomerServiceBlockingStub.getCustomer(
                            GetCustomerRequest.newBuilder().setId(id).build());
                    if (Objects.isNull(customerInfoResp)) {
                        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "qb customer not found, id: " + id);
                    }
                    GetCustomerPrimaryAddressResponse customerAddressResp =
                            businessCustomerAddressServiceBlockingStub.getCustomerPrimaryAddress(
                                    GetCustomerPrimaryAddressRequest.newBuilder()
                                            .setCustomerId(id)
                                            .build());
                    if (Objects.isNull(customerAddressResp)) {
                        throw ExceptionUtil.bizException(
                                Code.CODE_PARAMS_ERROR, "qb customer address not found, id: " + id);
                    }
                    var customerInfo = customerInfoResp.getCustomer();
                    dto.setCustomerId(id);
                    dto.setFirstName(customerInfo.getFirstName());
                    dto.setLastName(customerInfo.getLastName());
                    dto.setOwnerPhoneNumber(customerInfo.getPhoneNumber());
                    dto.setOwnerEmail(customerInfo.getEmail());
                    dto.setPrimaryAddress(getCustomerAddressDto(id, customerAddressResp));
                    return dto;
                })
                .toList();
    }

    private static CustomerAddressDto getCustomerAddressDto(
            Integer id, GetCustomerPrimaryAddressResponse customerAddressResp) {
        // Notice(Well): customer 不一定有 address，这里应该先判断 customerAddressResp.hasAddress()
        // 如果没有 address，下面这些字段会是空字符串。跟 Jett 确认过这里取空字符串没问题。
        // 后面如果有其他地方要复用这段代码的时候需要注意这个问题。
        var address = customerAddressResp.getAddress();
        CustomerAddressDto addressDTO = new CustomerAddressDto();
        addressDTO.setCustomerId(id);
        addressDTO.setCustomerAddressId((int) address.getId());
        addressDTO.setAddress1(address.getAddress1());
        addressDTO.setAddress2(address.getAddress2());
        addressDTO.setCity(address.getCity());
        addressDTO.setCountry(address.getCountry());
        addressDTO.setState(address.getState());
        addressDTO.setZipcode(address.getZipcode());
        return addressDTO;
    }

    public Invoice getSaveInvoiceFromGroomingDataNew(
            Long invoiceDateTime, OrderDetailModel orderDetail, DataServiceTmpDto dataServiceTmpDto) {
        if (Objects.isNull(invoiceDateTime)) {
            log.error(
                    "invoiceDateTime is null, order id: {}",
                    orderDetail.getOrder().getId());
            return null;
        }
        Integer businessId = Math.toIntExact(orderDetail.getOrder().getBusinessId());

        // 获取并检查invoice item
        List<OrderLineItemModel> itemList = orderDetail.getLineItemsList().stream()
                .filter(item -> !item.getIsDeleted())
                .toList();
        if (CollectionUtils.isEmpty(itemList)) {
            log.warn(
                    "order detail has no item, business id:{},  order id: {}",
                    businessId,
                    orderDetail.getOrder().getId());
            return null;
        }

        Map<String, Set<Integer>> needSyncIdMap = itemList.stream()
                .filter(item -> !Objects.equals(item.getObjectId(), 0L))
                .collect(Collectors.groupingBy(
                        OrderLineItemModel::getType,
                        Collectors.mapping(item -> Math.toIntExact(item.getObjectId()), Collectors.toSet())));

        // 新版本的sync invoice时, 要分别把item sync 到不同的帐号, 这部分是与旧逻辑有区别的地方
        Invoice addInvoice = InvoiceHelper.convertDataToInvoice(
                orderDetail,
                itemList,
                syncCustomer(businessId, Math.toIntExact(orderDetail.getOrder().getCustomerId()), dataServiceTmpDto),
                syncBatchService(
                        businessId,
                        needSyncIdMap,
                        dataServiceTmpDto,
                        IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_SALES_REVENUE),
                syncBatchProduct(
                        businessId,
                        needSyncIdMap,
                        dataServiceTmpDto,
                        IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_SALES_REVENUE),
                syncBatchServiceCharge(
                        businessId,
                        needSyncIdMap,
                        dataServiceTmpDto,
                        IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_SALES_REVENUE),
                syncBatchServicePackage(
                        businessId,
                        needSyncIdMap,
                        dataServiceTmpDto,
                        IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_SALES_REVENUE),
                getDefaultInvoiceItem(
                        businessId,
                        QuickBooksConst.QB_SERVICE_NO_SHOW_FEE,
                        dataServiceTmpDto,
                        IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_SALES_REVENUE));

        // 获取并添加 invoice due time
        InvoiceHelper.invoiceAddTxnDate(addInvoice, invoiceDateTime);
        InvoiceHelper.invoiceAddDueTime(addInvoice, invoiceDateTime + QuickBooksConst.QB_INVOICE_DUE_TIME_DIFF);

        // tax 不为空时，保存tax
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(orderDetail.getOrder().getTaxAmount())) != 0) {
            Byte taxSyncType = moeQbSettingMapper.selectTaxSyncTypeByBusinessId(businessId);
            if (Objects.equals(QuickBooksConst.TAX_SYNC_IN_TOTAL, taxSyncType)) {
                // add tax detail
                addInvoice.setTxnTaxDetail(getTaxDetail(orderDetail, dataServiceTmpDto));
            } else {
                // clean tax code
                addInvoice.getLine().stream()
                        .map(Line::getSalesItemLineDetail)
                        .filter(Objects::nonNull)
                        .forEach(detail -> detail.setTaxCodeRef(null));
                // add tax line
                modifySalesTaxPayableAccType(
                        businessId,
                        dataServiceTmpDto,
                        IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_SALES_TAX_PAYABLE,
                        AccountTypeEnum.INCOME);
                InvoiceHelper.invoiceAddLine(
                        addInvoice,
                        getDefaultInvoiceItem(
                                businessId,
                                QuickBooksConst.QB_SERVICE_TAX,
                                dataServiceTmpDto,
                                IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_SALES_TAX_PAYABLE),
                        BigDecimal.valueOf(orderDetail.getOrder().getTaxAmount()));
                // 同步 tax item 后, 把 tax 相关的账号类型修改成current liability
                modifySalesTaxPayableAccType(
                        businessId,
                        dataServiceTmpDto,
                        IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_SALES_TAX_PAYABLE,
                        AccountTypeEnum.OTHER_CURRENT_LIABILITY);
            }
        }
        // discount 不为空时，保存discount
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(orderDetail.getOrder().getDiscountAmount())) != 0) {
            InvoiceHelper.invoiceAddLine(
                    addInvoice,
                    getDefaultInvoiceItem(
                            businessId,
                            QuickBooksConst.QB_SERVICE_DISCOUNT,
                            dataServiceTmpDto,
                            IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_SALES_REVENUE),
                    BigDecimal.valueOf(orderDetail.getOrder().getDiscountAmount())
                            .negate());
        }
        // tips不为空时，保存tips
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(orderDetail.getOrder().getTipsAmount())) != 0) {
            // add tax line
            modifySalesTaxPayableAccType(
                    businessId,
                    dataServiceTmpDto,
                    IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_TIPS,
                    AccountTypeEnum.INCOME);
            InvoiceHelper.invoiceAddLine(
                    addInvoice,
                    getDefaultInvoiceItem(
                            businessId,
                            QuickBooksConst.QB_SERVICE_TIPS,
                            dataServiceTmpDto,
                            IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_TIPS),
                    BigDecimal.valueOf(orderDetail.getOrder().getTipsAmount()));
            // add tax line
            modifySalesTaxPayableAccType(
                    businessId,
                    dataServiceTmpDto,
                    IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_TIPS,
                    AccountTypeEnum.OTHER_CURRENT_LIABILITY);
        }
        // processing fee by client(convenience fee) 不为空时，保存
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(orderDetail.getOrder().getExtraFeeAmount())) != 0) {
            InvoiceHelper.invoiceAddLine(
                    addInvoice,
                    getDefaultInvoiceItem(
                            businessId,
                            getCustomizedProcessingFeeName(businessId),
                            dataServiceTmpDto,
                            IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INCOME_FEE_BY_CLIENT_REVENUE),
                    BigDecimal.valueOf(orderDetail.getOrder().getExtraFeeAmount()));
        }
        log.info(
                "business id:{}, sync invoice from grooming data, order id: {}",
                businessId,
                orderDetail.getOrder().getId());
        return addInvoice;
    }

    /**
     * 修改 sales tax payable 账号的类型
     *
     * @param dataServiceTmpDto quick book 相关的数据
     */
    private void modifySalesTaxPayableAccType(
            Integer businessId,
            DataServiceTmpDto dataServiceTmpDto,
            IntuitAccountTypeEnum syncAccountType,
            AccountTypeEnum changeType) {
        var qbSyncAccount = syncAccountMapper.selectByBusinessRealmIdAndAccountType(
                businessId, dataServiceTmpDto.getRealmId(), syncAccountType.getCode());
        if (Objects.isNull(qbSyncAccount)) return;
        Account account = dataServiceFactory.dataServiceFindById(
                dataServiceTmpDto.getDataService(), AccountHelper.getAccountWithId(qbSyncAccount.getQbAccountId()));
        if (Objects.nonNull(account) && account.isActive() && !EntityStatusEnum.DELETED.equals(account.getStatus())) {
            account.setAccountType(changeType);
            if (account.isSubAccount() && isTipsAccount(syncAccountType)) {
                account.setSubAccount(false);
                account.setParentRef(null);
            }
            if (AccountTypeEnum.OTHER_CURRENT_LIABILITY.equals(changeType)) {
                account.setAccountSubType(AccountSubTypeEnum.SALES_TAX_PAYABLE.value());
                if (isTipsAccount(syncAccountType)) {
                    account.setAccountSubType(AccountSubTypeEnum.OTHER_CURRENT_LIABILITIES.value());
                }
            } else if (AccountTypeEnum.INCOME.equals(changeType)) {
                account.setAccountSubType(AccountSubTypeEnum.DISCOUNTS_REFUNDS_GIVEN.value());
            }
            updateAccountByQuickBook(dataServiceTmpDto.getDataService(), account);
        }
    }

    private Boolean isTipsAccount(IntuitAccountTypeEnum syncAccountType) {
        return IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE_TIPS.equals(syncAccountType)
                || IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_RECEIVED_TIPS.equals(syncAccountType);
    }

    public Invoice getSaveInvoiceFromGroomingData(
            Long invoiceDateTime, OrderDetailModel orderDetail, DataServiceTmpDto dataServiceTmpDto) {
        if (Objects.isNull(invoiceDateTime)) {
            return null;
        }
        Integer businessId = Math.toIntExact(orderDetail.getOrder().getBusinessId());

        // 获取并检查invoice item
        List<OrderLineItemModel> itemList = orderDetail.getLineItemsList().stream()
                .filter(item -> !item.getIsDeleted())
                .toList();
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        Map<String, Set<Integer>> needSyncIdMap = itemList.stream()
                .filter(item -> !Objects.equals(item.getObjectId(), 0L))
                .collect(Collectors.groupingBy(
                        OrderLineItemModel::getType,
                        Collectors.mapping(item -> Math.toIntExact(item.getObjectId()), Collectors.toSet())));

        // 开始准备dataService数据，并检查customer service

        // Integer businessId, Integer customerId, DataServiceTmpDto dataServiceTmpDto
        Invoice addInvoice = InvoiceHelper.convertDataToInvoice(
                orderDetail,
                itemList,
                syncCustomer(businessId, Math.toIntExact(orderDetail.getOrder().getCustomerId()), dataServiceTmpDto),
                syncBatchService(businessId, needSyncIdMap, dataServiceTmpDto),
                syncBatchProduct(businessId, needSyncIdMap, dataServiceTmpDto),
                syncBatchServiceCharge(businessId, needSyncIdMap, dataServiceTmpDto),
                getDefaultInvoiceItem(businessId, QuickBooksConst.QB_SERVICE_NO_SHOW_FEE, dataServiceTmpDto));
        // 获取并添加 invoice due time
        InvoiceHelper.invoiceAddTxnDate(addInvoice, invoiceDateTime);
        InvoiceHelper.invoiceAddDueTime(addInvoice, invoiceDateTime + QuickBooksConst.QB_INVOICE_DUE_TIME_DIFF);
        // tax 不为空时，保存tax
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(orderDetail.getOrder().getTaxAmount())) != 0) {
            Byte taxSyncType = moeQbSettingMapper.selectTaxSyncTypeByBusinessId(businessId);
            if (Objects.equals(QuickBooksConst.TAX_SYNC_IN_TOTAL, taxSyncType)) {
                // add tax detail
                addInvoice.setTxnTaxDetail(getTaxDetail(orderDetail, dataServiceTmpDto));
            } else {
                // clean tax code
                addInvoice.getLine().stream()
                        .map(Line::getSalesItemLineDetail)
                        .filter(Objects::nonNull)
                        .forEach(detail -> detail.setTaxCodeRef(null));
                // add tax line
                InvoiceHelper.invoiceAddLine(
                        addInvoice,
                        getDefaultInvoiceItem(businessId, QuickBooksConst.QB_SERVICE_TAX, dataServiceTmpDto),
                        BigDecimal.valueOf(orderDetail.getOrder().getTaxAmount()));
            }
        }
        // discount 不为空时，保存discount
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(orderDetail.getOrder().getDiscountAmount())) != 0) {
            InvoiceHelper.invoiceAddLine(
                    addInvoice,
                    getDefaultInvoiceItem(businessId, QuickBooksConst.QB_SERVICE_DISCOUNT, dataServiceTmpDto),
                    BigDecimal.valueOf(orderDetail.getOrder().getDiscountAmount())
                            .negate());
        }
        // tips不为空时，保存tips
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(orderDetail.getOrder().getTipsAmount())) != 0) {
            InvoiceHelper.invoiceAddLine(
                    addInvoice,
                    getDefaultInvoiceItem(businessId, QuickBooksConst.QB_SERVICE_TIPS, dataServiceTmpDto),
                    BigDecimal.valueOf(orderDetail.getOrder().getTipsAmount()));
        }
        // processing fee by client(convenience fee ) 不为空时，保存
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(orderDetail.getOrder().getExtraFeeAmount())) != 0) {
            InvoiceHelper.invoiceAddLine(
                    addInvoice,
                    getDefaultInvoiceItem(businessId, getCustomizedProcessingFeeName(businessId), dataServiceTmpDto),
                    BigDecimal.valueOf(orderDetail.getOrder().getExtraFeeAmount()));
        }
        return addInvoice;
    }

    /**
     * <a href="https://developer.intuit.com/app/developer/qbo/docs/develop/sdks-and-samples-collections/java/taxes#create-a-taxcode">Create TaxCode</a>
     *
     * @param orderDetail
     * @param dataServiceTmpDto
     * @return txnTaxDetail
     */
    private TxnTaxDetail getTaxDetail(OrderDetailModel orderDetail, DataServiceTmpDto dataServiceTmpDto) {
        BigDecimal allTotalAmount = orderDetail.getLineItemsList().stream()
                .filter(item -> !item.getIsDeleted())
                .filter(item -> BigDecimal.valueOf(item.getTaxAmount()).compareTo(BigDecimal.ZERO) > 0)
                .map(item -> BigDecimal.valueOf(item.getTotalAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal taxPercentage = BigDecimal.valueOf(orderDetail.getOrder().getTaxAmount())
                .scaleByPowerOfTen(2)
                .divide(allTotalAmount, 2, RoundingMode.HALF_UP);

        String taxCodeId = getTaxCodeId(taxPercentage, dataServiceTmpDto);

        // add total tax
        TxnTaxDetail txnTaxDetail = new TxnTaxDetail();
        ReferenceType referenceType = new ReferenceType();
        referenceType.setValue(taxCodeId);
        txnTaxDetail.setTxnTaxCodeRef(referenceType);
        txnTaxDetail.setTotalTax(BigDecimal.valueOf(orderDetail.getOrder().getTaxAmount()));
        return txnTaxDetail;
    }

    private String getTaxCodeId(BigDecimal taxPercentage, DataServiceTmpDto dataServiceTmpDto) {
        String taxCodeName = MessageFormat.format(SALES_TAX, taxPercentage);
        DataService dataService = dataServiceTmpDto.getDataService();
        Optional<TaxCode> taxCodeOptional;
        try {
            taxCodeOptional = dataService.findAll(new TaxCode()).stream()
                    .filter(taxCode -> Objects.equals(taxCode.getName(), taxCodeName))
                    .findFirst();
        } catch (FMSException e) {
            throw ExceptionUtil.bizException(Code.CODE_QUICKBOOKS_UNEXPECTED_EXCEPTION, e);
        }
        if (taxCodeOptional.isPresent()) {
            return taxCodeOptional.get().getId();
        }

        List<TaxAgency> taxAgencyList;
        try {
            taxAgencyList = dataService.findAll(new TaxAgency());
        } catch (FMSException e) {
            throw ExceptionUtil.bizException(Code.CODE_QUICKBOOKS_UNEXPECTED_EXCEPTION, e);
        }
        TaxAgency taxAgency = taxAgencyList.stream()
                .filter(taxAgency1 -> taxAgency1.getDisplayName().equalsIgnoreCase(MOE_GO))
                .findFirst()
                .orElseGet(() -> {
                    TaxAgency taxAgencyTemp = new TaxAgency();
                    taxAgencyTemp.setDisplayName(MOE_GO);
                    taxAgencyTemp.setActive(true);
                    try {
                        return dataService.add(taxAgencyTemp);
                    } catch (FMSException e) {
                        throw ExceptionUtil.bizException(Code.CODE_QUICKBOOKS_UNEXPECTED_EXCEPTION, e);
                    }
                });

        try {
            TaxRateDetails trd = new TaxRateDetails();
            trd.setRateValue(taxPercentage);
            trd.setTaxAgencyId(taxAgency.getId());
            trd.setTaxRateName(MessageFormat.format(SALES_TAX_RATE, trd.getRateValue()));
            trd.setTaxApplicableOn(TaxRateApplicableOnEnum.SALES);
            List<TaxRateDetails> taxRateDetails = new ArrayList<>();
            taxRateDetails.add(trd);

            TaxService taxservice = new TaxService();
            taxservice.setTaxCode(taxCodeName);
            taxservice.setTaxRateDetails(taxRateDetails);

            Context context = dataServiceFactory.getContext(dataServiceTmpDto.getQbConnect());
            GlobalTaxService taxCodeService = new GlobalTaxService(context);
            TaxService ts = taxCodeService.addTaxCode(taxservice);
            return ts.getTaxCodeId();
        } catch (FMSException e) {
            throw ExceptionUtil.bizException(Code.CODE_QUICKBOOKS_UNEXPECTED_EXCEPTION, e);
        }
    }

    private Long getInvoiceDateTime(Long timestamp, Integer businessId) {
        // 检查grooming appointment date
        if (Objects.isNull(timestamp)) {
            return null;
        }
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfoWithOwnerEmail(businessIdParams);
        return Instant.ofEpochSecond(timestamp)
                .atZone(ZoneId.of(businessInfo.getTimezoneName()))
                .toInstant()
                .toEpochMilli();
    }

    private Long getInvoiceDateTime(String appointmentDate, Integer businessId) {
        // 检查grooming appointment date
        if (!DateUtil.testDateIsValid(appointmentDate)) {
            return null;
        }
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfoWithOwnerEmail(businessIdParams);
        return DateUtil.getLongTimeByAppointmentDate(appointmentDate, businessInfo.getTimezoneName());
    }

    public void migrationQbInvoice(
            MoeGroomingAppointment groomingAppointment,
            OrderDetailModel orderDetail,
            DataServiceTmpDto dataServiceTmpDto) {
        // 有payment 同步记录的预约才会同步
        GetPaymentParams p = new GetPaymentParams();
        p.setModule(PaymentMethodEnum.MODULE_GROOMING);
        p.setInvoiceId(Math.toIntExact(orderDetail.getOrder().getId()));
        PaymentSummary paymentSummary = iPaymentService.getPayments(p).getData();
        Integer businessId = Math.toIntExact(orderDetail.getOrder().getBusinessId());
        if (paymentSummary == null || CollectionUtils.isEmpty(paymentSummary.getPayments())) {
            log.warn(
                    "<migration> insertQbInvoice paymentSummary is null or empty, business id:{}, invoiceId:{}",
                    businessId,
                    orderDetail.getOrder().getId());
            return;
        }

        log.info(
                "<migration> insertQbInvoice start, business id:{}, invoiceId:{}",
                businessId,
                orderDetail.getOrder().getBusinessId());

        long invoiceId = orderDetail.getOrder().getId();
        Long companyId = businessInfoHelper.getCompanyIdByBusinessId(businessId);
        String realmId = dataServiceTmpDto.getRealmId();
        Integer connectId = dataServiceTmpDto.getConnectId();
        DataService dataService = dataServiceTmpDto.getDataService();
        var setting = moeQbSettingMapper.selectByBusinessIdAndConnectId(businessId, connectId);
        var invoiceTime = getInvoiceDateTime(orderDetail.getOrder().getCreateTime(), businessId);
        if (Objects.nonNull(groomingAppointment)) {
            invoiceTime = getInvoiceDateTime(groomingAppointment.getAppointmentDate(), businessId);
        }
        // 这里走的一定是user version = 1;
        var addInvoice = getSaveInvoiceByUserVersion(
                setting.getUserVersion(), businessId, invoiceTime, orderDetail, dataServiceTmpDto);
        if (Objects.isNull(addInvoice)) {
            log.info(String.format(
                    "<migration> insertQbInvoice is null invoice :%s, business id:%s", invoiceId, businessId));
            return;
        }
        MoeQbSyncInvoice localInvoice = syncInvoiceMapper.selectByInvoiceId(
                businessId, Math.toIntExact(orderDetail.getOrder().getId()));
        if (Objects.nonNull(localInvoice)) {
            // update
            Invoice queryInvoice = dataServiceFactory.dataServiceFindById(
                    dataService, InvoiceHelper.getInvoiceWithId(localInvoice.getQbInvoiceId()));
            addInvoice.setSyncToken(queryInvoice.getSyncToken());
            addInvoice.setId(queryInvoice.getId());
        }

        Invoice addQbInvoice = dataServiceFactory.dataServiceUpdate(dataService, addInvoice);
        // 同步qb
        // 一般来说迁移之前都有同步过数据, 也有gap期间新增的invoice没有同步的情况
        var syncInvoice = syncInvoiceMapper.selectByInvoiceId(businessId, Math.toIntExact(invoiceId));
        MoeQbSyncInvoice addSyncInvoice = new MoeQbSyncInvoice();
        addSyncInvoice.setBusinessId(businessId);
        addSyncInvoice.setCompanyId(companyId);
        addSyncInvoice.setConnectId(connectId);
        addSyncInvoice.setRealmId(realmId);
        if (Objects.nonNull(groomingAppointment)) {
            addSyncInvoice.setGroomingId(groomingAppointment.getId());
            addSyncInvoice.setPayStatus(groomingAppointment.getIsPaid());
        }
        addSyncInvoice.setQbInvoiceId(addQbInvoice.getId());
        addSyncInvoice.setQbInvoiceStatus(QuickBooksConst.QB_INVOICE_STATUS_NORMAL);
        addSyncInvoice.setCreateTime(DateUtil.get10Timestamp());
        addSyncInvoice.setUpdateTime(DateUtil.get10Timestamp());
        addSyncInvoice.setInvoiceType(orderDetail.getOrder().getSourceType());
        addSyncInvoice.setInvoiceId(Math.toIntExact(invoiceId));
        addSyncInvoice.setTotalAmount(BigDecimal.valueOf(orderDetail.getOrder().getTotalAmount()));
        addSyncInvoice.setPaidAmount(BigDecimal.valueOf(orderDetail.getOrder().getPaidAmount()));
        if (Objects.isNull(syncInvoice)) {
            syncInvoiceMapper.insertSelective(addSyncInvoice);
        } else {
            // 如果是已经存在的数据, 为了避免以后有删除, 在redis里存个map记录一下迁移前迁移后的关系
            // 具体为<biz_id, <new_qb_invoice_id, old_qb_invoice_id>>
            setMigrationIdAssociationKey(businessId, realmId, addQbInvoice.getId(), syncInvoice.getQbInvoiceId());
            // 更新记录
            addSyncInvoice.setId(syncInvoice.getId());
            syncInvoiceMapper.updateByPrimaryKeySelective(addSyncInvoice);
        }
        // 迁移invoice 完成, 迁移该订单下的payment记录
        log.info("<migration> insertQbInvoice success, business id:{}, invoiceId:{}", businessId, invoiceId);
        migrationSyncPayment(businessId, addQbInvoice, Math.toIntExact(invoiceId), dataServiceTmpDto, paymentSummary);
    }

    public void updateQbInvoice(
            MoeGroomingAppointment groomingAppointment,
            OrderDetailModel groomingInvoice,
            MoeQbSyncInvoice syncInvoice,
            Invoice queryInvoice,
            DataServiceTmpDto dataServiceTmpDto) {
        Integer businessId = groomingAppointment.getBusinessId();
        dataServiceFactory.getConnectRealmId(businessId);
        Integer connectId = dataServiceFactory.getConnectId(businessId);
        DataService dataService = dataServiceFactory.getDataService(businessId);
        var setting = moeQbSettingMapper.selectByBusinessIdAndConnectId(businessId, connectId);
        var addInvoice = getSaveInvoiceByUserVersion(
                setting.getUserVersion(),
                businessId,
                getInvoiceDateTime(groomingAppointment.getAppointmentDate(), businessId),
                groomingInvoice,
                dataServiceTmpDto);
        if (addInvoice != null) {
            addInvoice.setSyncToken(queryInvoice.getSyncToken());
            addInvoice.setId(queryInvoice.getId());
            Invoice addQbInvoice = dataServiceFactory.dataServiceUpdate(dataService, addInvoice);
            MoeQbSyncInvoice updateSyncInvoice = new MoeQbSyncInvoice();
            updateSyncInvoice.setConnectId(connectId);
            updateSyncInvoice.setQbInvoiceStatus(QuickBooksConst.QB_INVOICE_STATUS_NORMAL);
            updateSyncInvoice.setUpdateTime(DateUtil.get10Timestamp());
            updateSyncInvoice.setInvoiceType(groomingInvoice.getOrder().getSourceType());
            updateSyncInvoice.setInvoiceId(
                    Math.toIntExact(groomingInvoice.getOrder().getId()));
            updateSyncInvoice.setTotalAmount(
                    BigDecimal.valueOf(groomingInvoice.getOrder().getTotalAmount()));
            updateSyncInvoice.setPaidAmount(
                    BigDecimal.valueOf(groomingInvoice.getOrder().getPaidAmount()));
            updateSyncInvoice.setPayStatus(groomingAppointment.getIsPaid());
            updateSyncInvoice.setId(syncInvoice.getId());
            syncInvoiceMapper.updateByPrimaryKeySelective(updateSyncInvoice);
            syncInvoicePayment(
                    businessId,
                    addQbInvoice,
                    Math.toIntExact(groomingInvoice.getOrder().getId()),
                    dataServiceTmpDto,
                    null);
        } else {
            log.info(String.format("updateQbInvoice is null groomingId:%s", groomingAppointment.getId()));
        }
    }

    public void updateQbInvoice(
            OrderDetailModel orderDetail,
            MoeQbSyncInvoice syncInvoice,
            Invoice queryInvoice,
            DataServiceTmpDto dataServiceTmpDto) {
        Integer businessId = Math.toIntExact(orderDetail.getOrder().getBusinessId());
        Integer connectId = dataServiceFactory.getConnectId(businessId);
        DataService dataService = dataServiceFactory.getDataService(businessId);
        var setting = moeQbSettingMapper.selectByBusinessIdAndConnectId(businessId, connectId);
        var addInvoice = getSaveInvoiceByUserVersion(
                setting.getUserVersion(),
                businessId,
                getInvoiceDateTime(orderDetail.getOrder().getCreateTime(), businessId),
                orderDetail,
                dataServiceTmpDto);
        if (addInvoice != null) {
            addInvoice.setSyncToken(queryInvoice.getSyncToken());
            addInvoice.setId(queryInvoice.getId());
            Invoice addQbInvoice = dataServiceFactory.dataServiceUpdate(dataService, addInvoice);
            MoeQbSyncInvoice updateSyncInvoice = new MoeQbSyncInvoice();
            updateSyncInvoice.setConnectId(connectId);
            updateSyncInvoice.setQbInvoiceStatus(QuickBooksConst.QB_INVOICE_STATUS_NORMAL);
            updateSyncInvoice.setUpdateTime(DateUtil.get10Timestamp());
            updateSyncInvoice.setInvoiceType(orderDetail.getOrder().getSourceType());
            updateSyncInvoice.setInvoiceId(
                    Math.toIntExact(orderDetail.getOrder().getId()));
            updateSyncInvoice.setTotalAmount(
                    BigDecimal.valueOf(orderDetail.getOrder().getTotalAmount()));
            updateSyncInvoice.setPaidAmount(
                    BigDecimal.valueOf(orderDetail.getOrder().getPaidAmount()));
            updateSyncInvoice.setId(syncInvoice.getId());
            syncInvoiceMapper.updateByPrimaryKeySelective(updateSyncInvoice);
            syncInvoicePayment(
                    businessId,
                    addQbInvoice,
                    Math.toIntExact(orderDetail.getOrder().getId()),
                    dataServiceTmpDto,
                    null);
        } else {
            log.info(String.format("updateQbInvoice is null invoiceId:%s", queryInvoice));
        }
    }

    public void insertQbInvoice(
            MoeGroomingAppointment groomingAppointment,
            OrderDetailModel orderDetail,
            Integer syncInvoiceId,
            DataServiceTmpDto dataServiceTmpDto) {
        // 有payment 同步记录的预约才会同步
        GetPaymentParams p = new GetPaymentParams();
        p.setModule(PaymentMethodEnum.MODULE_GROOMING);
        p.setInvoiceId(Math.toIntExact(orderDetail.getOrder().getId()));
        PaymentSummary paymentSummary = iPaymentService.getPayments(p).getData();
        Integer businessId = groomingAppointment.getBusinessId();
        if (paymentSummary == null || CollectionUtils.isEmpty(paymentSummary.getPayments())) {
            log.warn(
                    "insertQbInvoice paymentSummary is null or empty, business id:{}, invoiceId:{}",
                    businessId,
                    orderDetail.getOrder().getId());
            return;
        }

        Long companyId = groomingAppointment.getCompanyId();
        String realmId = dataServiceTmpDto.getRealmId();
        Integer connectId = dataServiceTmpDto.getConnectId();
        DataService dataService = dataServiceTmpDto.getDataService();
        var setting = moeQbSettingMapper.selectByBusinessIdAndConnectId(businessId, connectId);
        var addInvoice = getSaveInvoiceByUserVersion(
                setting.getUserVersion(),
                businessId,
                getInvoiceDateTime(groomingAppointment.getAppointmentDate(), businessId),
                orderDetail,
                dataServiceTmpDto);
        if (addInvoice != null) {
            Invoice addQbInvoice = dataServiceFactory.dataServiceAdd(dataService, addInvoice);
            MoeQbSyncInvoice addSyncInvoice = new MoeQbSyncInvoice();
            addSyncInvoice.setBusinessId(businessId);
            addSyncInvoice.setCompanyId(companyId);
            addSyncInvoice.setConnectId(connectId);
            addSyncInvoice.setRealmId(realmId);
            addSyncInvoice.setGroomingId(groomingAppointment.getId());
            addSyncInvoice.setQbInvoiceId(addQbInvoice.getId());
            addSyncInvoice.setQbInvoiceStatus(QuickBooksConst.QB_INVOICE_STATUS_NORMAL);
            addSyncInvoice.setCreateTime(DateUtil.get10Timestamp());
            addSyncInvoice.setUpdateTime(DateUtil.get10Timestamp());
            addSyncInvoice.setInvoiceType(orderDetail.getOrder().getSourceType());
            addSyncInvoice.setInvoiceId(Math.toIntExact(orderDetail.getOrder().getId()));
            addSyncInvoice.setTotalAmount(
                    BigDecimal.valueOf(orderDetail.getOrder().getTotalAmount()));
            addSyncInvoice.setPaidAmount(
                    BigDecimal.valueOf(orderDetail.getOrder().getPaidAmount()));
            addSyncInvoice.setPayStatus(groomingAppointment.getIsPaid());
            if (syncInvoiceId != null) {
                addSyncInvoice.setId(syncInvoiceId);
                syncInvoiceMapper.updateByPrimaryKeySelective(addSyncInvoice);
            } else {
                syncInvoiceMapper.insertSelective(addSyncInvoice);
            }
            log.info("business id:{}, sync invoice complete", businessId);
            syncInvoicePayment(
                    businessId,
                    addQbInvoice,
                    Math.toIntExact(orderDetail.getOrder().getId()),
                    dataServiceTmpDto,
                    paymentSummary);
        } else {
            log.info(String.format(
                    "insertQbInvoice is null groomingId:%s, business id:%s", groomingAppointment.getId(), businessId));
        }
    }

    public void insertQbInvoice(
            OrderDetailModel orderDetail, Integer syncInvoiceId, DataServiceTmpDto dataServiceTmpDto) {
        int invoiceId = Math.toIntExact(orderDetail.getOrder().getId());

        // 从moego数据库内找已有的payment
        GetPaymentParams param = new GetPaymentParams();
        param.setModule(PaymentMethodEnum.MODULE_GROOMING);
        param.setInvoiceId(invoiceId);
        PaymentSummary paymentSummary = iPaymentService.getPayments(param).getData();
        if (paymentSummary == null) {
            param = new GetPaymentParams();
            param.setModule(PaymentMethodEnum.MODULE_RETAIL);
            param.setInvoiceId(invoiceId);
            paymentSummary = iPaymentService.getPayments(param).getData();
        }
        if (paymentSummary == null || CollectionUtils.isEmpty(paymentSummary.getPayments())) {
            return;
        }

        Integer businessId = Math.toIntExact(orderDetail.getOrder().getBusinessId());
        String realmId = dataServiceTmpDto.getRealmId();
        Integer connectId = dataServiceTmpDto.getConnectId();
        DataService dataService = dataServiceTmpDto.getDataService();

        var setting = moeQbSettingMapper.selectByBusinessIdAndConnectId(businessId, connectId);
        var addInvoice = getSaveInvoiceByUserVersion(
                setting.getUserVersion(),
                businessId,
                getInvoiceDateTime(orderDetail.getOrder().getCreateTime(), businessId),
                orderDetail,
                dataServiceTmpDto);

        if (addInvoice != null) {
            Invoice addQbInvoice = dataServiceFactory.dataServiceAdd(dataService, addInvoice);
            MoeQbSyncInvoice addSyncInvoice = new MoeQbSyncInvoice();
            addSyncInvoice.setBusinessId(businessId);
            addSyncInvoice.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
            addSyncInvoice.setConnectId(connectId);
            addSyncInvoice.setRealmId(realmId);
            addSyncInvoice.setQbInvoiceId(addQbInvoice.getId());
            addSyncInvoice.setQbInvoiceStatus(QuickBooksConst.QB_INVOICE_STATUS_NORMAL);
            addSyncInvoice.setCreateTime(DateUtil.get10Timestamp());
            addSyncInvoice.setUpdateTime(DateUtil.get10Timestamp());
            addSyncInvoice.setInvoiceType(orderDetail.getOrder().getSourceType());
            addSyncInvoice.setInvoiceId(invoiceId);
            addSyncInvoice.setTotalAmount(
                    BigDecimal.valueOf(orderDetail.getOrder().getTotalAmount()));
            addSyncInvoice.setPaidAmount(
                    BigDecimal.valueOf(orderDetail.getOrder().getPaidAmount()));
            addSyncInvoice.setPayStatus((byte) 1);
            if (syncInvoiceId != null) {
                addSyncInvoice.setId(syncInvoiceId);
                syncInvoiceMapper.updateByPrimaryKeySelective(addSyncInvoice);
            } else {
                syncInvoiceMapper.insertSelective(addSyncInvoice);
            }
            syncInvoicePayment(businessId, addQbInvoice, invoiceId, dataServiceTmpDto, paymentSummary);
        } else {
            log.info(String.format("insertQbInvoice is null invoiceId:%s", syncInvoiceId));
        }
    }

    private Invoice getSaveInvoiceByUserVersion(
            Integer userVersion,
            Integer businessId,
            Long invoiceDateTime,
            OrderDetailModel orderDetail,
            DataServiceTmpDto dataServiceTmpDto) {
        // The new logic splits the account,
        // so different synchronization logic will be selected according to the version of the account.
        log.info("sync invoice...,business id:{}, user version:{}", businessId, userVersion);
        return Objects.equals(userVersion, QuickBooksConst.USER_VERSION_OLD)
                ? getSaveInvoiceFromGroomingData(invoiceDateTime, orderDetail, dataServiceTmpDto)
                : getSaveInvoiceFromGroomingDataNew(invoiceDateTime, orderDetail, dataServiceTmpDto);
    }

    public Boolean checkGroomingAppointmentIsNormal(MoeGroomingAppointment groomingAppointment) {
        if (groomingAppointment == null) {
            return false;
        }
        if (AppointmentStatusEnum.CANCELED.getValue().equals(groomingAppointment.getStatus())) {
            return false;
        }
        if (GroomingAppointmentEnum.IS_WAITING_LIST.equals(groomingAppointment.getIsWaitingList())) {
            return false;
        }
        if (GroomingAppointmentEnum.IS_BLOCK_TRUE.equals(groomingAppointment.getIsBlock())) {
            return false;
        }
        if (GroomingAppointmentEnum.IS_DEPRECATE_TRUE.equals(groomingAppointment.getIsDeprecate())) {
            return false;
        }
        return true;
    }

    /**
     * @param businessId
     * @param needSyncTime 需要再次同步的时间
     * @return
     */
    public boolean syncInvoiceByGroomingId(
            Integer businessId, MoeGroomingAppointment groomingAppointment, Long needSyncTime) {
        //        MoeGroomingAppointment groomingAppointment =
        // moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (groomingAppointment == null || !groomingAppointment.getBusinessId().equals(businessId)) {
            throw new CommonException(ResponseCodeEnum.GROOMING_NOT_FOUND);
        }
        Integer groomingId = groomingAppointment.getId();
        DataServiceTmpDto dataServiceTmpDto = new DataServiceTmpDto(
                dataServiceFactory.getDataService(businessId), dataServiceFactory.getConnect(businessId));
        String realmId = dataServiceTmpDto.getRealmId();
        DataService dataService = dataServiceTmpDto.getDataService();
        MoeQbSyncInvoice syncInvoice = syncInvoiceMapper.selectByGroomingId(businessId, groomingId, realmId);
        if (syncInvoice != null) {
            if (needSyncTime <= syncInvoice.getUpdateTime()) {
                // 已经同步过了，不需要再次同步
                return true;
            }
        }
        // 获取并检查invoice
        OrderDetailModel orderDetail =
                orderService.mustGetOrderByGroomingId(groomingAppointment.getBusinessId(), groomingId);
        boolean isNormalGrooming = checkGroomingAppointmentIsNormal(groomingAppointment);

        // 如果现在走的迁移流程, 一定是历史同步过的数据去到新数据, 不需要再判断后面的内容
        if (checkIsMigration(businessId)) {
            migrationQbInvoice(groomingAppointment, orderDetail, dataServiceTmpDto);
            return true;
        }

        if (isNormalGrooming) {
            if (orderDetail == null) {
                return true;
            }
            if (syncInvoice == null
                    || QuickBooksConst.QB_INVOICE_STATUS_DELETE.equals(syncInvoice.getQbInvoiceStatus())) {
                // create qbInvoice
                Integer syncInvoiceId = null;
                if (syncInvoice != null) {
                    syncInvoiceId = syncInvoice.getId();
                }
                insertQbInvoice(groomingAppointment, orderDetail, syncInvoiceId, dataServiceTmpDto);
            } else {
                // update
                Invoice queryInvoice = dataServiceFactory.dataServiceFindById(
                        dataService, InvoiceHelper.getInvoiceWithId(syncInvoice.getQbInvoiceId()));
                if (queryInvoice == null || EntityStatusEnum.DELETED.equals(queryInvoice.getStatus())) {
                    insertQbInvoice(groomingAppointment, orderDetail, syncInvoice.getId(), dataServiceTmpDto);
                } else {
                    updateQbInvoice(groomingAppointment, orderDetail, syncInvoice, queryInvoice, dataServiceTmpDto);
                }
            }
        } else {
            // 检查syncInvoice
            Invoice queryInvoice = null;
            if (syncInvoice != null) {
                if (QuickBooksConst.QB_INVOICE_STATUS_NORMAL.equals(syncInvoice.getQbInvoiceStatus())) {
                    // 有同步过invoice信息
                    queryInvoice = dataServiceFactory.dataServiceFindById(
                            dataService, InvoiceHelper.getInvoiceWithId(syncInvoice.getQbInvoiceId()));
                    if (queryInvoice == null || EntityStatusEnum.DELETED.equals(queryInvoice.getStatus())) {
                        queryInvoice = null;
                    }
                }
            }

            // 检查是否是no show grooming
            OrderDetailModel noShowOrderDetail = null;
            if (AppointmentStatusEnum.CANCELED.getValue().equals(groomingAppointment.getStatus())
                    && GroomingAppointmentEnum.NO_SHOW_TRUE.equals(groomingAppointment.getNoShow())) {
                noShowOrderDetail = orderService.getOrderDetailByGroomingIdAndType(
                        groomingAppointment.getBusinessId(),
                        groomingAppointment.getId(),
                        OrderSourceType.NO_SHOW.getSource());
            }
            // 取消 & 不no show      取消 &  no show & no invoice
            // 有->删除  没有->跳过
            // 取消 & noshow * 有 invoice
            // 有->更新  没有->插入

            // 以上相等于
            // 有no show invoice
            // 当前有->插入更新/更新更新  当前没有->插入更新
            // 没有no show invoice
            // 当前有->删除
            if (noShowOrderDetail != null) {
                if (syncInvoice != null) {
                    if (syncInvoice
                            .getInvoiceId()
                            .equals(Math.toIntExact(noShowOrderDetail.getOrder().getId()))) {
                        // 是同一个invoice的同步，需要更新覆盖
                        if (queryInvoice != null) {
                            updateQbInvoice(
                                    groomingAppointment,
                                    noShowOrderDetail,
                                    syncInvoice,
                                    queryInvoice,
                                    dataServiceTmpDto);
                        } else {
                            // 插入新的invoice，并同步syncInvoice
                            insertQbInvoice(
                                    groomingAppointment, noShowOrderDetail, syncInvoice.getId(), dataServiceTmpDto);
                        }
                    } else {
                        // 不是同一个invoice的同步，需要删除旧的payment、删除旧的invoice、插入新的invoice，并同步syncInvoice
                        if (queryInvoice != null) {
                            // 1、删除已有invoice的payment
                            deleteQbPayment(businessId, syncInvoice.getInvoiceId(), dataServiceTmpDto);
                            // 2、删除invoice
                            dataServiceFactory.dataServiceDelete(dataService, queryInvoice);
                        }
                        // 插入新的invoice，并同步syncInvoice
                        insertQbInvoice(groomingAppointment, noShowOrderDetail, syncInvoice.getId(), dataServiceTmpDto);
                    }
                } else {
                    // 没有同步过，需要插入
                    insertQbInvoice(groomingAppointment, noShowOrderDetail, null, dataServiceTmpDto);
                }
            } else {
                if (queryInvoice != null) {
                    // 1、删除已有invoice的payment
                    deleteQbPayment(businessId, syncInvoice.getInvoiceId(), dataServiceTmpDto);
                    // 2、删除invoice
                    Invoice deletedInvoice = dataServiceFactory.dataServiceDelete(dataService, queryInvoice);
                    log.info("qb Invoice deleted:{}, status:{}", deletedInvoice.getId(), deletedInvoice.getStatus());
                }
                if (syncInvoice != null) {
                    // delete sync invoice
                    MoeQbSyncInvoice deleteSyncInvoice = new MoeQbSyncInvoice();
                    deleteSyncInvoice.setId(syncInvoice.getId());
                    deleteSyncInvoice.setQbInvoiceStatus(QuickBooksConst.QB_INVOICE_STATUS_DELETE);
                    deleteSyncInvoice.setUpdateTime(DateUtil.get10Timestamp());
                    syncInvoiceMapper.updateByPrimaryKeySelective(deleteSyncInvoice);
                }
            }
        }
        return true;
    }

    public String syncInvoiceByInvoiceId(Integer businessId, Integer invoiceId, Long needSyncTime) {
        OrderDetailModel orderDetail = orderService.getOrderDetailByOrderId(businessId, invoiceId);
        return syncInvoiceByInvoiceId(businessId, orderDetail, needSyncTime);
    }

    public String syncInvoiceByInvoiceId(Integer businessId, OrderDetailModel orderDetail, Long needSyncTime) {
        if (Objects.isNull(orderDetail)) {
            return "invoice is not exist";
        }
        DataServiceTmpDto dataServiceTmpDto = new DataServiceTmpDto(
                dataServiceFactory.getDataService(businessId), dataServiceFactory.getConnect(businessId));
        DataService dataService = dataServiceTmpDto.getDataService();

        MoeQbSyncInvoice localInvoice = syncInvoiceMapper.selectByInvoiceId(
                businessId, Math.toIntExact(orderDetail.getOrder().getId()));
        if (localInvoice != null && needSyncTime <= localInvoice.getUpdateTime()) {
            // 已经同步过了，不需要再次同步
            log.info(
                    "invoiceId : {} has been sync before",
                    Math.toIntExact(orderDetail.getOrder().getId()));
            return "has been sync before";
        }

        if (checkIsMigration(businessId)) {
            // 有的订单没有依赖grooming
            migrationQbInvoice(null, orderDetail, dataServiceTmpDto);
            return "success";
        }

        if (localInvoice == null
                || QuickBooksConst.QB_INVOICE_STATUS_DELETE.equals(localInvoice.getQbInvoiceStatus())) {
            // create qbInvoice
            Integer syncInvoiceId = null;
            if (localInvoice != null) {
                syncInvoiceId = localInvoice.getId();
            }
            insertQbInvoice(orderDetail, syncInvoiceId, dataServiceTmpDto);
        } else {
            // update
            Invoice remoteInvoice = dataServiceFactory.dataServiceFindById(
                    dataService, InvoiceHelper.getInvoiceWithId(localInvoice.getQbInvoiceId()));
            if (remoteInvoice == null || EntityStatusEnum.DELETED.equals(remoteInvoice.getStatus())) {
                insertQbInvoice(orderDetail, localInvoice.getId(), dataServiceTmpDto);
            } else {
                updateQbInvoice(orderDetail, localInvoice, remoteInvoice, dataServiceTmpDto);
            }
        }
        return "success";
    }

    public void deleteAll(Integer businessId, Long startDate, Long endDate) {
        Set<Integer> whiteBuinessSet = Set.of(114561, 111859);
        if (whiteBuinessSet.contains(businessId)) {
            throw ExceptionUtil.bizException(Code.CODE_QUICKBOOKS_UNEXPECTED_EXCEPTION, "white business");
        }

        DataServiceTmpDto dataServiceTmpDto = new DataServiceTmpDto(
                dataServiceFactory.getDataService(businessId), dataServiceFactory.getConnect(businessId));
        String realmId = dataServiceFactory.getConnectRealmId(businessId);

        // 这里的invoice 取的是sync invoice的数据, 这里的时间是同步时间, 所以只需要过滤错误同步的时间就行
        List<MoeQbSyncInvoice> invoiceList = syncInvoiceMapper.selectByBusinessId(businessId);
        invoiceList.stream()
                .filter(invoice -> (invoice.getUpdateTime() >= startDate && invoice.getUpdateTime() <= endDate))
                .limit(100)
                .forEach(invoice -> deleteInvoice(businessId, invoice));

        // 拿到moe go创建的所有账号, 过滤掉旧版本的MoeGo Invoice account
        IntuitAccountTypeEnum[] values = IntuitAccountTypeEnum.values();
        Arrays.sort(values, (o1, o2) -> o2.getCode().compareTo(o1.getCode()));
        List<IntuitAccountTypeEnum> intuitAccountTypeEnumStream = Arrays.stream(values)
                .filter(e -> !e.equals(IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_INVOICE))
                .toList();
        Set<String> qbAccounts = intuitAccountTypeEnumStream.stream()
                .map(e -> {
                    var qbAcct =
                            syncAccountMapper.selectByBusinessRealmIdAndAccountType(businessId, realmId, e.getCode());
                    if (Objects.isNull(qbAcct)) return null;
                    return qbAcct.getQbAccountId();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // service
        var serResult = dataServiceFactory.executeQuery(
                dataServiceTmpDto.getDataService(), "select * from Item where MetaData.CreateTime > '2024-04-10'");
        if (Objects.nonNull(serResult) && !CollectionUtils.isEmpty(serResult.getEntities())) {
            for (var ser : serResult.getEntities()) {
                var service = (Item) ser;
                if (Objects.nonNull(service)
                        && service.isActive()
                        && !EntityStatusEnum.DELETED.equals(service.getStatus())) {
                    if (!qbAccounts.contains(service.getIncomeAccountRef().getValue())) {
                        continue;
                    }
                    try {
                        service.setActive(false);
                        dataServiceFactory.dataServiceUpdate(dataServiceTmpDto.getDataService(), service);
                        log.info("delete service: {}, service name: {}", service.getId(), service.getName());
                    } catch (Exception e) {
                        log.error("delete service error: {}", e.getMessage());
                    }
                }
            }
        }
        // purchase
        var purResult = dataServiceFactory.executeQuery(
                dataServiceTmpDto.getDataService(), "select * from Purchase where TxnDate > '2024-04-10'");
        if (Objects.nonNull(purResult) && !CollectionUtils.isEmpty(purResult.getEntities())) {
            log.info(
                    "business id:{}. purchase size: {}",
                    businessId,
                    purResult.getEntities().size());
            for (IEntity entity : purResult.getEntities()) {
                try {
                    var et = (Purchase) entity;
                    if (Objects.nonNull(et) && !EntityStatusEnum.DELETED.equals(et.getStatus())) {
                        if (Objects.nonNull(et.getAccountRef())
                                && qbAccounts.contains(et.getAccountRef().getValue())) {
                            dataServiceFactory.dataServiceDelete(dataServiceTmpDto.getDataService(), et);
                            log.info("delete purchase: {}", et);
                        }
                    }
                } catch (Exception e) {
                    log.error("delete purchase error: {}", e.getMessage());
                }
            }
        }

        // refund
        var rrResult = dataServiceFactory.executeQuery(
                dataServiceTmpDto.getDataService(), "select * from RefundReceipt where TxnDate > '2024-04-10'");
        if (Objects.nonNull(rrResult) && !CollectionUtils.isEmpty(rrResult.getEntities())) {
            log.info(
                    "business id:{}. refund size: {}",
                    businessId,
                    rrResult.getEntities().size());
            for (IEntity entity : rrResult.getEntities()) {
                try {
                    var et = (RefundReceipt) entity;
                    if (Objects.nonNull(et) && !EntityStatusEnum.DELETED.equals(et.getStatus())) {
                        // 通过 private note 确认是moego qb 同步的refund
                        if (et.getPrivateNote().equalsIgnoreCase("Refund from MoeGo Pay Sales Receipt")
                                || et.getPrivateNote().equalsIgnoreCase("Refund from All MoeGo payment")) {
                            dataServiceFactory.dataServiceDelete(dataServiceTmpDto.getDataService(), et);
                            log.info("business id: {}, delete refund: {}", businessId, et);
                        }
                    }
                } catch (Exception e) {
                    log.error("delete item error", e);
                }
            }
        }
        // receipt
        var receipt = syncReceiptMapper.selectByBusinessIdAndRealmId(businessId, realmId);
        for (var rec : receipt) {
            // refund 单独处理, 这里只需要关注 sales receipt 和 credit memo
            if (QuickBooksConst.RECEIPT_TYPE_SALES.equals(rec.getReceiptType())) {
                SalesReceipt salesReceipt = new SalesReceipt();
                salesReceipt.setId(rec.getQbReceiptId());
                var salesTransaction =
                        dataServiceFactory.dataServiceFindById(dataServiceTmpDto.getDataService(), salesReceipt);
                if (Objects.nonNull(salesTransaction)
                        && !EntityStatusEnum.DELETED.equals(salesTransaction.getStatus())) {
                    try {
                        log.info("business id: {}, delete sales receipt: {}", businessId, salesTransaction);
                        dataServiceFactory.dataServiceDelete(dataServiceTmpDto.getDataService(), salesTransaction);
                    } catch (Exception e) {
                        log.error("delete receipt error", e);
                    }
                }
            } else if (QuickBooksConst.RECEIPT_TYPE_CREDIT_MEMO.equals(rec.getReceiptType())) {
                var cm = new CreditMemo();
                cm.setId(rec.getQbReceiptId());
                var salesTransaction = dataServiceFactory.dataServiceFindById(dataServiceTmpDto.getDataService(), cm);
                if (Objects.nonNull(salesTransaction) && !EntityStatusEnum.DELETED.equals(cm.getStatus())) {
                    try {
                        log.info("business id: {}, delete credit memo: {}", businessId, salesTransaction);
                        dataServiceFactory.dataServiceDelete(dataServiceTmpDto.getDataService(), salesTransaction);
                    } catch (Exception e) {
                        log.error("delete item error", e);
                    }
                }
            }
        }

        // account
        qbAccounts.forEach(account -> {
            var acc = dataServiceFactory.dataServiceFindById(
                    dataServiceTmpDto.getDataService(), AccountHelper.getAccountWithId(account));
            if (Objects.nonNull(acc) && acc.isActive() && !EntityStatusEnum.DELETED.equals(acc.getStatus())) {
                try {
                    acc.setActive(false);
                    dataServiceFactory.dataServiceUpdate(dataServiceTmpDto.getDataService(), acc);
                    log.info("business id:{}. delete account: {}", businessId, acc.getName());
                } catch (Exception e) {
                    log.error("delete account error1, e:{}", e.getMessage());
                }
            }
        });

        for (var en : intuitAccountTypeEnumStream) {
            // 二次检查账号删除情况
            var acResult = dataServiceFactory.executeQuery(
                    dataServiceTmpDto.getDataService(),
                    String.format("select * from Account where Name = '%s'", en.getName()));
            if (Objects.nonNull(acResult) && !CollectionUtils.isEmpty(acResult.getEntities())) {
                for (IEntity entity : acResult.getEntities()) {
                    try {
                        var et = (Account) entity;
                        if (Objects.nonNull(et) && et.isActive() && !EntityStatusEnum.DELETED.equals(et.getStatus())) {
                            et.setActive(false);
                            dataServiceFactory.dataServiceUpdate(dataServiceTmpDto.getDataService(), et);
                            log.info("business id:{}. double delete account: {}", businessId, et.getName());
                        }
                    } catch (Exception e) {
                        log.error("delete account error2: {}", e.getMessage());
                    }
                }
            }
        }
        log.info("business id: {}, delete all data task complete", businessId);
    }

    public ListQuickBookInvoiceDTO listQuickBookInvoice(ListQuickBookInvoiceParams params) {
        var pagination = Optional.ofNullable(params.getPagination()).orElse(Pagination.DEFAULT);
        var builder = ListQuickBookInvoiceDTO.builder();

        var invoices = syncInvoiceMapper.listInvoice(
                params, pagination.pageSize(), CommonUtil.getLimitOffset(pagination.pageNum(), pagination.pageSize()));
        if (invoices.isEmpty()) return builder.build();

        Map<Integer, List<QuickBookPaymentDTO>> collect = syncPaymentMapper
                .listByBusinessAndInvoiceIds(
                        params.getBusinessId(),
                        invoices.stream().map(MoeQbSyncInvoice::getInvoiceId).toList())
                .stream()
                .map(QuickBookMapper.INSTANCE::paymentToDTO)
                .collect(Collectors.groupingBy(QuickBookPaymentDTO::getInvoiceId));

        return builder.quickBookInvoices(QuickBookMapper.INSTANCE.listInvoiceToDto(invoices).stream()
                        .peek(d -> d.setAssociationPayments(
                                collect.getOrDefault(d.getInvoiceId(), Collections.emptyList())))
                        .toList())
                .count(syncInvoiceMapper.countInvoice(params))
                .build();
    }
}
