package com.moego.server.grooming.service.report;

import static com.moego.common.utils.PageUtil.hasEmptyCollectionFilter;
import static com.moego.common.utils.PageUtil.selectPage;
import static com.moego.server.grooming.constant.AppointmentStatusSet.ACTIVE_STATUS_SET;
import static com.moego.server.grooming.service.utils.ReportBeanUtil.buildBaseAppt;
import static com.moego.server.grooming.service.utils.ReportBeanUtil.setInvoiceAmount;

import com.google.common.collect.ImmutableList;
import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.groomingreport.GroomingReportConst;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.GroomingUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.customer.client.ICustomerReportClient;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.grooming.dto.report.GroomingReportInfo;
import com.moego.server.grooming.dto.report.ReportAppointment;
import com.moego.server.grooming.dto.report.ReportAppointmentResponse;
import com.moego.server.grooming.dto.report.ReportApptType;
import com.moego.server.grooming.dto.report.ReportWebAppointment;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.mapperbean.MoeWaitList;
import com.moego.server.grooming.mapstruct.ReportBeanMapper;
import com.moego.server.grooming.params.ReportWebApptsRequest;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.grooming.params.report.DescribeAppointmentReportsParams;
import com.moego.server.grooming.params.report.DescribeOperationReportsParams;
import com.moego.server.grooming.params.report.DescribePetDetailReportsParams;
import com.moego.server.grooming.params.report.ScanAppointmentReportsParams;
import com.moego.server.grooming.service.AppointmentServiceDetailService;
import com.moego.server.grooming.service.EvaluationServiceDetailService;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.MoeGroomingNoteService;
import com.moego.server.grooming.service.MoeGroomingReportService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.WaitListService;
import com.moego.server.grooming.service.dto.GetTipsInvoicesDTO;
import com.moego.server.grooming.service.dto.GroomingReportApptDetail;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportAppointmentDAO;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.TipsApptsPageDTO;
import com.moego.server.grooming.service.dto.ob.OBPrepayDetailDTO;
import com.moego.server.grooming.service.remote.StaffService;
import com.moego.server.grooming.service.utils.ReportUtil;
import com.moego.server.grooming.utils.StatusUtil;
import com.moego.server.message.client.IGroomingReportSendClient;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReportAppointmentService {

    private final AppointmentMapperProxy appointmentMapper;
    private final MoeGroomingServiceOperationMapper operationMapper;

    private final OrderService orderService;
    private final ReportOrderService reportOrderService;
    private final ICustomerReportClient iCustomerReportClient;
    private final WaitListService waitListService;
    private final MoeGroomingReportService moeGroomingReportService;
    private final IGroomingReportSendClient iGroomingReportSendClient;
    private final AppointmentServiceDetailService serviceDetailService;
    private final EvaluationServiceDetailService evaluationServiceDetailService;
    private final GroomingServiceService groomingServiceService;
    private final MoeGroomingNoteService moeGroomingNoteService;
    private final PetDetailMapperProxy petDetailMapper;
    private final StaffService staffService;

    public List<GroomingReportApptDetail> getReportApptByStartDateRange(
            Integer businessId, String startDateGte, String startDateLte, Boolean withDelAppt) {
        List<AppointmentStatusEnum> statusList = null;
        if (!Boolean.TRUE.equals(withDelAppt)) {
            statusList = ACTIVE_STATUS_SET;
        }
        List<MoeGroomingAppointment> appointments =
                appointmentMapper.getAllApptByStartDateRange(null, businessId, startDateGte, startDateLte, statusList);
        return appointments.stream()
                .map(ReportBeanMapper.INSTANCE::toGroomingReportApptDetail)
                .collect(Collectors.toList());
    }

    public List<GroomingReportWebAppointment> getReportWebApptByStartDateRange(
            Integer businessId,
            String startDateGte,
            String startDateLte,
            Boolean withServiceInfo,
            Boolean withOperation) {
        var appointments =
                appointmentMapper
                        .getAllApptByStartDateRange(null, businessId, startDateGte, startDateLte, ACTIVE_STATUS_SET)
                        .stream()
                        .map(ReportBeanMapper.INSTANCE::toGroomingReportWebAppointment)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appointments)) {
            return List.of();
        }
        fillReportWebApptPetDetail(businessId, appointments, withServiceInfo, withOperation);

        appointments.sort(Comparator.comparing(GroomingReportWebAppointment::getAppointmentDate)
                .thenComparing(GroomingReportWebAppointment::getAppointmentStartTime)
                .reversed());
        return appointments;
    }

    public List<MoeGroomingAppointment> scanAppointmentReports(ScanAppointmentReportsParams params) {
        return appointmentMapper.scanAppointmentReports(params);
    }

    public ReportAppointmentResponse getReportAppointments(
            Integer businessId, String startDate, String endDate, ReportApptType type) {
        List<ReportAppointmentDAO> appts;
        String today = DateUtil.getNowDateString();

        switch (type) {
            case UNPAID:
                appts = reportOrderService.queryUnpaidApptsWithAmount(businessId, startDate, endDate);
                return processAppts(businessId, startDate, endDate, appts, ReportApptType.UNPAID);
            case UNCLOSED:
                appts = appointmentMapper.queryUnclosedAppts(businessId, startDate, endDate, today);
                return processAppts(businessId, startDate, endDate, appts, ReportApptType.UNCLOSED);
            case CANCELLED:
                appts = reportOrderService.queryCancelledApptsWithNoShowInfo(businessId, startDate, endDate);
                return processAppts(businessId, startDate, endDate, appts, ReportApptType.CANCELLED);
            case NO_SHOW:
                appts = reportOrderService.queryNoShowApptsWithNoShowInfo(businessId, startDate, endDate);
                return processAppts(businessId, startDate, endDate, appts, ReportApptType.NO_SHOW);
            case UPCOMING:
                appts = appointmentMapper.queryUpcomingAppts(businessId, startDate, endDate, today);
                return processAppts(businessId, startDate, endDate, appts, ReportApptType.UPCOMING);
            case WAIT_LIST:
                appts = appointmentMapper.queryWaitListAppts(businessId, startDate, endDate);
                return processAppts(businessId, startDate, endDate, appts, ReportApptType.WAIT_LIST);
            case ONLINE_BOOK:
                appts = appointmentMapper.queryApptsByOnlineBook(businessId, startDate, endDate);
                return processAppts(businessId, startDate, endDate, appts, ReportApptType.ONLINE_BOOK);
        }
        log.error("Unexpected report appointment type: {}", type);
        throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Unexpected report appointment type: " + type);
    }

    private ReportAppointmentResponse processAppts(
            Integer businessId,
            String startDate,
            String endDate,
            List<ReportAppointmentDAO> appointments,
            ReportApptType reportType) {
        ReportAppointmentResponse result = new ReportAppointmentResponse();
        result.setBusinessId(businessId);
        result.setEndDate(endDate);
        result.setStartDate(startDate);
        result.setType(reportType);

        if (appointments.isEmpty()) {
            result.setAppt(ImmutableList.of());
        } else {
            // no show fee invoice去重，只保留no show invoice
            Map<Integer, ReportAppointmentDAO> cancelApptMap = new HashMap<>();
            for (ReportAppointmentDAO appt : appointments) {
                // 非noshoe invoice可能反向覆盖invoice，额外判断一次
                if (!InvoiceStatusEnum.TYPE_NOSHOW.equals(appt.getType())) {
                    ReportAppointmentDAO appointmentDAO = cancelApptMap.get(appt.getId());
                    if (appointmentDAO != null && InvoiceStatusEnum.TYPE_NOSHOW.equals(appointmentDAO.getType())) {
                        continue;
                    }
                }
                // noshow invoice或不覆盖的情况下，更新map
                cancelApptMap.put(appt.getId(), appt);
            }
            // 重新初始化appts
            appointments = new ArrayList<>(cancelApptMap.values());

            List<Integer> customerIds = appointments.stream()
                    .map(ReportAppointmentDAO::getCustomerId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, String> customerNameMap = new HashMap<>();
            if (!customerIds.isEmpty()) {
                List<MoeBusinessCustomerDTO> customers = iCustomerReportClient.queryCustomerBasicInfo(customerIds);
                customers.forEach(
                        c -> customerNameMap.put(c.getCustomerId(), c.getFirstName() + " " + c.getLastName()));
            }
            var staffIds = appointments.stream()
                    .map(ReportAppointmentDAO::getCancelById)
                    .filter(CommonUtil::isNormal)
                    .distinct()
                    .toList();
            var staffMap = staffService.getStaffMap(staffIds);

            List<ReportAppointment> appts = appointments.stream()
                    .map(u -> buildReportMobileAppt(reportType, customerNameMap, staffMap, u))
                    .sorted((o1, o2) -> {
                        // 增加排序，和web端接口保持一致：按appt date和start time降序排序
                        int compareVal = o2.getApptDate().compareTo(o1.getApptDate());
                        return compareVal != 0 ? compareVal : o2.getStartTime().compareTo(o1.getStartTime());
                    })
                    .collect(Collectors.toList());

            result.setAppt(appts);
        }

        return result;
    }

    private ReportAppointment buildReportMobileAppt(
            ReportApptType reportType,
            Map<Integer, String> customerNameMap,
            Map<Long, StaffModel> staffMap,
            ReportAppointmentDAO u) {
        ReportAppointment.ReportAppointmentBuilder builder = ReportAppointment.builder()
                .apptDate(u.getAppointmentDate())
                .apptId(Math.toIntExact(u.getId()))
                .customerId(u.getCustomerId())
                .customerName(customerNameMap.get(u.getCustomerId()))
                .endTime(u.getEndTime())
                .startTime(u.getStartTime())
                .orderId(u.getOrderId())
                .status(u.getStatus().intValue());
        if (ReportApptType.UNPAID == reportType) {
            builder.unpaidAmount(u.getRemainAmount());
        }
        if (ReportApptType.CANCELLED == reportType || ReportApptType.NO_SHOW == reportType) {
            builder.cancelByType(u.getCancelByType())
                    .cancelByName(ReportUtil.buildStaffName(
                            staffMap.get(u.getCancelById().longValue())))
                    .noShow(BooleanEnum.VALUE_TRUE.equals(u.getNoShow()))
                    .noShowCharged(Integer.valueOf(2).equals(u.getInvoiceStatus()));
        }
        return builder.build();
    }

    /**
     * Use cancel reason for both cancel reason and no-show reason.
     * Use cancel_by for both cancel by staff id and no-show by staff id
     */
    public List<ReportWebAppointment> getReportWebAppts(ReportWebApptsRequest request) {
        if (request.getReportId() == 1002) {
            return getUnpaidReportWebAppt(request);
        } else if (request.getReportId() == 1003) {
            return getCancelReportWebAppt(request);
        } else if (request.getReportId() == 1004) {
            return getNoShowReportWebAppt(request);
        } else if (request.getReportId() == 1001) {
            return getDetailReportWebAppt(request);
        } else if (request.getReportId() == 1005) {
            return getDetailReportWaitList(request);
        } else if (request.getReportId() == 1006) {
            return getByStaffReportWebAppt(request);
        }

        throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Unknown reportId");
    }

    private List<ReportWebAppointment> getUnpaidReportWebAppt(ReportWebApptsRequest request) {
        List<GroomingReportWebAppointment> appointments = reportOrderService.queryWebReportUnpaidAppts(
                request.getBusinessId(), request.getStartDate(), request.getEndDate());
        // 查询refund记录
        Set<Integer> invoiceIds = appointments.stream()
                .map(GroomingReportWebAppointment::getInvoiceId)
                .collect(Collectors.toSet());
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(invoiceIds);

        return appointments.stream()
                .map(a -> buildBaseAppt(request, a)
                        .totalPrice(a.getTotalAmount())
                        .collectedRevenue(
                                a.getPaidAmount().subtract(refundMap.getOrDefault(a.getInvoiceId(), BigDecimal.ZERO)))
                        .totalPayment(a.getPaidAmount())
                        .totalRefund(refundMap.getOrDefault(a.getInvoiceId(), BigDecimal.ZERO))
                        .unpaidPrice(a.getRemainAmount())
                        .build())
                .collect(Collectors.toList());
    }

    private List<ReportWebAppointment> getCancelReportWebAppt(ReportWebApptsRequest request) {
        List<GroomingReportWebAppointment> appointments = appointmentMapper.queryWebReportCancelledAppts(
                request.getBusinessId(), request.getStartDate(), request.getEndDate());
        return appointments.stream()
                .map(a -> {
                    String[] cancelReason = new String[1];
                    a.getAppointmentNotes().stream()
                            .filter(ap -> GroomingAppointmentEnum.NOTE_CANCEL.equals(ap.getType()))
                            .findAny()
                            .ifPresent(n -> cancelReason[0] = n.getNote());
                    return buildBaseAppt(request, a)
                            .cancelDate(DateUtil.convertDateBySeconds(
                                    a.getUpdateTime(), request.getTimezoneName(), request.getDateFormat()))
                            .cancelById(a.getCancelBy())
                            .cancelReason(cancelReason[0])
                            .build();
                })
                .collect(Collectors.toList());
    }

    private List<ReportWebAppointment> getNoShowReportWebAppt(ReportWebApptsRequest request) {
        List<GroomingReportWebAppointment> appointments = appointmentMapper.queryWebReportNoShowAppts(
                request.getBusinessId(), request.getStartDate(), request.getEndDate());
        var appointmentIds = appointments.stream()
                .map(GroomingReportWebAppointment::getId)
                .distinct()
                .toList();
        // Appointment 表的 no_show_fee 字段不准确，查询 appointment 对应的 NoShow invoice 获取准确的 NoShow fee
        var appointmentIdToInvoice = reportOrderService.getGroomingIdInvoiceMap(
                request.getBusinessId(), appointmentIds, OrderSourceType.NO_SHOW.getSource());
        return appointments.stream()
                .map(a -> {
                    String[] cancelReason = new String[1];
                    a.getAppointmentNotes().stream()
                            .filter(ap -> GroomingAppointmentEnum.NOTE_CANCEL.equals(ap.getType()))
                            .findAny()
                            .ifPresent(n -> cancelReason[0] = n.getNote());
                    var noShowAppt = buildBaseAppt(request, a)
                            .noShowMarkDate(DateUtil.convertDateBySeconds(
                                    a.getUpdateTime(), request.getTimezoneName(), request.getDateFormat()))
                            .noShowReason(cancelReason[0])
                            .build();
                    if (a.getNoShowBy() != 0) {
                        noShowAppt.setNoShowMarkById(a.getNoShowBy().intValue());
                    } else {
                        noShowAppt.setNoShowMarkById(a.getCancelBy());
                    }
                    if (appointmentIdToInvoice.containsKey(a.getId())) {
                        noShowAppt.setNoShowFee(
                                appointmentIdToInvoice.get(a.getId()).getTotalAmount());
                    }
                    return noShowAppt;
                })
                .collect(Collectors.toList());
    }

    private List<ReportWebAppointment> getDetailReportWebAppt(ReportWebApptsRequest request) {
        List<GroomingReportWebAppointment> appointments = getReportWebApptByStartDateRange(
                request.getBusinessId(), request.getStartDate(), request.getEndDate(), true, true);
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyList();
        }
        fillReportWebApptNotes(appointments);
        reportOrderService.queryWebReportApptDetails(request.getBusinessId(), appointments);

        Map<Integer, List<GroomingReportDTO>> groomingReportMap = new HashMap<>();
        Map<Integer, List<GroomingReportSendLogDTO>> groomingReportSendLogMap = new HashMap<>();
        // 暂时只有 1001 需要查询 GroomingReport，其它 Report 暂时不额外查询
        List<Integer> groomingIds = appointments.stream()
                .map(GroomingReportWebAppointment::getId)
                .distinct()
                .toList();
        groomingReportMap.putAll(
                moeGroomingReportService
                        .getGroomingReportListByGroomingIdList(request.getBusinessId(), groomingIds)
                        .stream()
                        .collect(Collectors.groupingBy(GroomingReportDTO::getGroomingId)));
        groomingReportSendLogMap.putAll(iGroomingReportSendClient.getGroomingLastReportSendLogsMap(
                new GroomingIdListParams(request.getBusinessId(), groomingIds)));

        appointments.sort(Comparator.comparing(GroomingReportWebAppointment::getAppointmentDate)
                .thenComparing(GroomingReportWebAppointment::getAppointmentStartTime)
                .reversed());
        return appointments.stream()
                .map(a -> {
                    List<String> services = a.getPetDetails().stream()
                            .map(ReportWebApptPetDetail::getServiceName)
                            .collect(Collectors.toList());
                    services.addAll(a.getEvaluationDetails().stream()
                            .map(EvaluationServiceDetailDTO::getServiceName)
                            .toList());

                    String[] comment = new String[1], alertNote = new String[1];
                    a.getAppointmentNotes().forEach(n -> {
                        if (GroomingAppointmentEnum.NOTE_ALERT.equals(n.getType())) {
                            alertNote[0] = n.getNote();
                        } else if (GroomingAppointmentEnum.NOTE_COMMENT.equals(n.getType())) {
                            comment[0] = n.getNote();
                        }
                    });
                    // 当前预约的 Grooming report list
                    List<GroomingReportDTO> groomingReportList = groomingReportMap.getOrDefault(a.getId(), List.of());
                    List<GroomingReportSendLogDTO> groomingReportSendLogList =
                            groomingReportSendLogMap.getOrDefault(a.getId(), List.of());

                    return buildBaseAppt(request, a)
                            .petIds(ReportUtil.getReportWebApptPetIds(List.of(a)))
                            .serviceAndAddOns(services)
                            .serviceCharges(a.getServiceChargeList())
                            .totalPrice(a.getPaymentAmount())
                            .staffIds(ReportUtil.getReportWebApptStaffIds(List.of(a)))
                            .ticketComment(comment[0])
                            .alertNote(alertNote[0])
                            .status(StatusUtil.getApptStatusForReport(a.getStatus()))
                            .paymentStatus(GroomingUtil.getAppPaidDesc(a.getIsPaid()))
                            .revenue(a.getPaymentAmount())
                            .tips(a.getTipsAmount())
                            .tax(a.getTaxAmount())
                            .discount(a.getDiscountAmount())
                            .invoiceId(a.getInvoiceId())
                            .totalPayment(a.getPaidAmount())
                            .totalUnpaid(a.getRemainAmount())
                            .groomingReportInfo(buildGroomingReportInfo(groomingReportList, groomingReportSendLogList))
                            .build();
                })
                .collect(Collectors.toList());
    }

    private List<ReportWebAppointment> getDetailReportWaitList(ReportWebApptsRequest request) {
        LocalDateTime createFrom = LocalDate.parse(request.getStartDate()).atTime(LocalTime.MIN);
        LocalDateTime createTo = LocalDate.parse(request.getEndDate()).atTime(LocalTime.MAX);
        List<MoeWaitList> waitLists = waitListService.queryWaitList(
                request.getCompanyId(), request.getBusinessId().longValue(), createFrom, createTo, "created_at desc");
        Map<Long, MoeGroomingAppointment> waitListAppointment =
                waitListService.batchGetAppointment(request.getBusinessId().longValue(), waitLists);
        Map<Long, MoeGroomingNote> waitListComment = waitListService.batchGetTicketComment(waitLists);
        Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> waitListPetService =
                waitListService.batchGetPetService(request.getBusinessId().longValue(), waitListAppointment);
        Map<Long, OBPrepayDetailDTO> waitListPrePay =
                waitListService.batchGetPrepayDetail(request.getBusinessId().longValue(), waitListAppointment);
        Map<Integer, MoeGroomingService> serviceMap =
                waitListService.batchGetService(request.getBusinessId().longValue(), waitListPetService);
        Map<Integer, MoeStaffDto> staffMap =
                waitListService.getStaffInfo(request.getBusinessId().longValue(), waitLists);
        List<ReportWebAppointment> reportWebAppointments = new ArrayList<>();
        waitLists.forEach(waitList -> {
            MoeGroomingAppointment appointment = waitListAppointment.get(waitList.getId());
            if (appointment == null) {
                return;
            }
            Map<Integer, List<MoeGroomingPetDetail>> petService =
                    waitListPetService.getOrDefault(waitList.getId(), Collections.emptyMap());
            List<Integer> petIds = petService.keySet().stream().toList();
            List<String> services = petService.values().stream()
                    .flatMap(Collection::stream)
                    .filter(k -> k.getServiceId() > 0)
                    .map(k -> serviceMap.get(k.getServiceId()) == null
                            ? ""
                            : serviceMap.get(k.getServiceId()).getName())
                    .toList();
            ReportWebAppointment reportWebAppointment = new ReportWebAppointment();
            reportWebAppointment.setBookingId(appointment.getId());
            reportWebAppointment.setClientId(appointment.getCustomerId());
            reportWebAppointment.setDatePreference(
                    waitListService.getReportDatePreference(waitList.getDatePreference()));
            reportWebAppointment.setTimePreference(
                    waitListService.getReportTimePreference(waitList.getTimePreference()));
            reportWebAppointment.setStaffPreference(
                    waitListService.getReportStaffPreference(waitList.getStaffPreference(), staffMap));
            reportWebAppointment.setCreateDate(
                    waitList.getCreatedAt().toLocalDate().toString());
            reportWebAppointment.setCreateById(waitList.getCreatedBy().intValue());
            reportWebAppointment.setServiceAndAddOns(services);
            reportWebAppointment.setPetIds(petIds);
            MoeGroomingNote comment = waitListComment.get(waitList.getId());
            reportWebAppointment.setTicketComment(comment == null ? "" : comment.getNote());
            if (appointment.getWaitListStatus() == WaitListStatusEnum.APPTANDWAITLIST) {
                reportWebAppointment.setRelateAppointmentId(waitList.getAppointmentId());
            }
            reportWebAppointment.setValidTill(waitList.getValidTill().toString());
            reportWebAppointment.setPaymentStatus(GroomingUtil.getAppPaidDesc(appointment.getIsPaid()));
            OBPrepayDetailDTO prepayDetailDTO = waitListPrePay.get(waitList.getId());
            if (prepayDetailDTO != null
                    && BookOnlineDepositConst.REQUIRE_CAPTURE.equals(prepayDetailDTO.getPrepayStatus())) {
                reportWebAppointment.setPaymentStatus("Prepaid");
            }
            reportWebAppointment.setCreateById(waitList.getCreatedBy().intValue());
            reportWebAppointment.setRevenue(petService.values().stream()
                    .flatMap(Collection::stream)
                    .map(MoeGroomingPetDetail::getServicePrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            reportWebAppointments.add(reportWebAppointment);
        });
        reportOrderService.queryWaitListReportOrderInfo(request.getBusinessId(), reportWebAppointments);
        return reportWebAppointments;
    }

    private List<ReportWebAppointment> getByStaffReportWebAppt(ReportWebApptsRequest request) {
        // return appointments by staff, one appointment may have multiple services, hence multiple records returned
        List<GroomingReportWebAppointment> appointments = appointmentMapper.queryApptStaffAndCustomer(
                request.getBusinessId(), request.getStartDate(), request.getEndDate());

        reportOrderService.setOperationList(request.getBusinessId(), appointments);

        return appointments.stream()
                .map(a -> a.getPetDetails().stream()
                        .flatMap(petDetail -> {
                            if (CollectionUtils.isEmpty(petDetail.getOperationList())) {
                                return Stream.of(petDetail.getStaffId());
                            }
                            return petDetail.getOperationList().stream().map(GroomingServiceOperationDTO::getStaffId);
                        })
                        .distinct()
                        .map(staffId -> ReportWebAppointment.builder()
                                .bookingId(a.getId())
                                .staffId(staffId)
                                .clientId(a.getCustomerId())
                                .apptDateTime(
                                        DateUtil.dateToBusinessFormat(a.getAppointmentDate(), request.getDateFormat())
                                                + " "
                                                + DateUtil.minuteToBusinessTime(
                                                        a.getAppointmentStartTime(), request.getTimeFormatType()))
                                .build())
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private void fillReportWebApptPetDetail(
            Integer businessId,
            List<GroomingReportWebAppointment> appointments,
            Boolean withServiceInfo,
            Boolean withOperation) {
        if (CollectionUtils.isEmpty(appointments)) {
            return;
        }
        List<Integer> appointmentIds =
                appointments.stream().map(GroomingReportWebAppointment::getId).toList();
        var detailsMap = serviceDetailService.getByAppointments(appointmentIds);

        List<ReportWebApptPetDetail> allPetDetails = new ArrayList<>();
        List<EvaluationServiceDetailDTO> allEvaluations = new ArrayList<>();
        appointments.forEach(a -> {
            com.moego.lib.utils.model.Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>> details =
                    detailsMap.get(a.getId());
            List<MoeGroomingPetDetail> petDetails = details.key();
            List<MoeGroomingPetDetail> servicePetDetails = petDetails.stream()
                    .filter(d -> Objects.equals(d.getServiceType(), ServiceType.SERVICE_VALUE))
                    .toList();
            List<EvaluationServiceDetail> evaluations = details.value();
            a.setPetDetails(ReportBeanMapper.INSTANCE.toReportWebApptPetDetailList(petDetails));
            a.setEvaluationDetails(serviceDetailService.toEvaluationServiceDetailDTOListV2(evaluations));
            a.setTotalServicePrice(serviceDetailService.calculateAmount(petDetails, servicePetDetails, evaluations));

            allPetDetails.addAll(a.getPetDetails());
            allEvaluations.addAll(a.getEvaluationDetails());
        });
        if (Boolean.TRUE.equals(withServiceInfo)) {
            fillReportApptServiceInfo(businessId, allPetDetails, allEvaluations);
        }
        if (Boolean.TRUE.equals(withOperation)) {
            reportOrderService.setOperationList(businessId, appointments);
        }
    }

    public void fillReportApptServiceInfo(
            Integer businessId, List<ReportWebApptPetDetail> petDetails, List<EvaluationServiceDetailDTO> evaluations) {
        if (!CollectionUtils.isEmpty(petDetails)) {
            List<Integer> serviceIds = petDetails.stream()
                    .map(ReportWebApptPetDetail::getServiceId)
                    .toList();
            Map<Integer, MoeGroomingService> serviceMap = groomingServiceService.getServiceMap(businessId, serviceIds);
            petDetails.forEach(p -> {
                MoeGroomingService service = serviceMap.get(p.getServiceId());
                p.setServiceName(service == null ? null : service.getName());
            });
        }

        if (!CollectionUtils.isEmpty(evaluations)) {
            List<Long> evaluationIds = evaluations.stream()
                    .map(EvaluationServiceDetailDTO::getServiceId)
                    .toList();
            Map<Long, EvaluationBriefView> evaluationMap =
                    evaluationServiceDetailService.getEvaluationServiceMap(evaluationIds);
            evaluations.forEach(e -> {
                EvaluationBriefView service = evaluationMap.get(e.getServiceId());
                e.setServiceName(service == null ? null : service.getName());
            });
        }
    }

    public void fillReportApptNotes(List<GroomingReportApptDetail> appointments) {
        if (CollectionUtils.isEmpty(appointments)) {
            return;
        }
        List<Integer> appointmentIds =
                appointments.stream().map(GroomingReportApptDetail::getId).toList();
        Map<Integer, List<MoeGroomingNote>> appointmentNotes =
                moeGroomingNoteService.getNoteListByGroomingIdList(appointmentIds).stream()
                        .collect(Collectors.groupingBy(MoeGroomingNote::getGroomingId));
        appointments.forEach(a -> {
            List<MoeGroomingNote> notes = appointmentNotes.get(a.getId());
            a.setAppointmentNotes(moeGroomingNoteService.toAppointmentNoteList(notes));
        });
    }

    void fillReportWebApptNotes(List<GroomingReportWebAppointment> appointments) {
        if (CollectionUtils.isEmpty(appointments)) {
            return;
        }
        List<Integer> appointmentIds =
                appointments.stream().map(GroomingReportWebAppointment::getId).toList();
        Map<Integer, List<MoeGroomingNote>> appointmentNotes =
                moeGroomingNoteService.getNoteListByGroomingIdList(appointmentIds).stream()
                        .collect(Collectors.groupingBy(MoeGroomingNote::getGroomingId));
        appointments.forEach(a -> {
            List<MoeGroomingNote> notes = appointmentNotes.get(a.getId());
            a.setAppointmentNotes(moeGroomingNoteService.toAppointmentNoteList(notes));
        });
    }

    private GroomingReportInfo buildGroomingReportInfo(
            List<GroomingReportDTO> groomingReportList, List<GroomingReportSendLogDTO> sendLogList) {
        // 没有 sent 状态则不显示 grooming report 信息
        if (CollectionUtils.isEmpty(groomingReportList)
                || groomingReportList.stream()
                        .noneMatch(gr -> GroomingReportStatusEnum.sent.name().equals(gr.getStatus()))) {
            return null;
        }
        Map<Integer, List<GroomingReportSendLogDTO>> sendLogMap =
                sendLogList.stream().collect(Collectors.groupingBy(GroomingReportSendLogDTO::getGroomingId));
        boolean sentSuccess = false;
        Integer totalCount = 0;
        // 上次的发送方式
        Byte lastSendingMethod = sendLogList.stream()
                .max(Comparator.comparing(GroomingReportSendLogDTO::getSentTime))
                .map(GroomingReportSendLogDTO::getSendingMethod)
                .orElse(GroomingReportConst.SEND_BY_SMS);
        for (GroomingReportDTO groomingReport : groomingReportList) {
            totalCount += groomingReport.getLinkOpenedCount();
            List<GroomingReportSendLogDTO> curSendLogList =
                    sendLogMap.getOrDefault(groomingReport.getGroomingId(), List.of());
            // 有一个 Grooming report 发送成功则 sentSuccess
            if (GroomingReportStatusEnum.sent.name().equals(groomingReport.getStatus())) {
                sentSuccess = sentSuccess
                        || curSendLogList.stream()
                                .anyMatch(sendLog -> sendLog.getStatus().equals((byte) 0));
            }
        }
        return new GroomingReportInfo(sentSuccess, totalCount, lastSendingMethod);
    }

    public void fillReportApptPetDetail(
            Integer businessId,
            List<GroomingReportApptDetail> appointments,
            Boolean withServiceInfo,
            Boolean withOperation) {
        if (CollectionUtils.isEmpty(appointments)) {
            return;
        }
        List<Integer> appointmentIds =
                appointments.stream().map(GroomingReportApptDetail::getId).toList();
        var detailsMap = serviceDetailService.getByAppointments(appointmentIds);

        List<ReportWebApptPetDetail> allPetDetails = new ArrayList<>();
        List<EvaluationServiceDetailDTO> allEvaluations = new ArrayList<>();
        appointments.forEach(a -> {
            com.moego.lib.utils.model.Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>> details =
                    detailsMap.get(a.getId());
            List<MoeGroomingPetDetail> petDetails = details.key();
            List<MoeGroomingPetDetail> servicePetDetails = petDetails.stream()
                    .filter(d -> Objects.equals(d.getServiceType(), ServiceType.SERVICE_VALUE))
                    .toList();
            List<MoeGroomingPetDetail> addonPetDetails = petDetails.stream()
                    .filter(d -> Objects.equals(d.getServiceType(), ServiceType.ADDON_VALUE))
                    .toList();

            List<EvaluationServiceDetail> evaluations = details.value();
            a.setPetDetails(ReportBeanMapper.INSTANCE.toReportWebApptPetDetailList(petDetails));
            a.setEvaluationDetails(serviceDetailService.toEvaluationServiceDetailDTOListV2(evaluations));
            a.setTotalServicePrice(serviceDetailService.calculateAmount(petDetails, servicePetDetails, evaluations));
            a.setTotalAddOnsPrice(serviceDetailService.calculateAmount(petDetails, addonPetDetails, null));

            allPetDetails.addAll(a.getPetDetails());
            allEvaluations.addAll(a.getEvaluationDetails());
        });
        if (Boolean.TRUE.equals(withServiceInfo)) {
            fillReportApptServiceInfo(businessId, allPetDetails, allEvaluations);
        }
        if (Boolean.TRUE.equals(withOperation)) {
            reportOrderService.setOperationListForApptDetail(businessId, appointments);
        }
    }

    public Pair<List<MoeGroomingServiceOperation>, Pagination> describeOperationReports(
            DescribeOperationReportsParams params) {
        if (hasEmptyCollectionFilter(params.ids(), params.groomingIds(), params.groomingServiceIds())) {
            return Pair.of(
                    Collections.emptyList(),
                    new Pagination(
                            params.pagination().pageNum(), params.pagination().pageSize(), 0));
        }
        return selectPage(params.pagination(), () -> operationMapper.describeOperationReports(params));
    }

    public Pair<List<MoeGroomingPetDetail>, Pagination> describePetDetailReports(
            DescribePetDetailReportsParams params) {
        if (hasEmptyCollectionFilter(params.ids(), params.groomingIds(), params.staffIds(), params.petIds())) {
            return Pair.of(
                    Collections.emptyList(),
                    new Pagination(
                            params.pagination().pageNum(), params.pagination().pageSize(), 0));
        }
        return selectPage(params.pagination(), () -> petDetailMapper.describePetDetailReports(params));
    }

    public Pair<List<MoeGroomingAppointment>, Pagination> describeAppointmentReports(
            DescribeAppointmentReportsParams params) {
        if (hasEmptyCollectionFilter(params.ids(), params.businessIds())) {
            return Pair.of(
                    Collections.emptyList(),
                    new Pagination(
                            params.pagination().pageNum(), params.pagination().pageSize(), 0));
        }
        return selectPage(params.pagination(), () -> appointmentMapper.describeAppointmentReports(params));
    }

    public List<GroomingReportWebAppointment> getPayrollAppointmentDetails(
            Integer businessId, String startDate, String endDate) {
        var appointments =
                appointmentMapper.listAppointmentsForPayroll(businessId, startDate, endDate, ACTIVE_STATUS_SET).stream()
                        .map(ReportBeanMapper.INSTANCE::toGroomingReportWebAppointment)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appointments)) {
            return List.of();
        }
        // 查询关联 petDetail 和 operation
        fillReportWebApptPetDetail(businessId, appointments, true, true);
        // 查询关联 order
        reportOrderService.fillInvoiceInfoForPayroll(businessId, appointments);
        // 按 appointment 日期+时间倒序排序
        appointments.sort(Comparator.comparing(GroomingReportWebAppointment::getAppointmentDate)
                .thenComparing(GroomingReportWebAppointment::getAppointmentStartTime)
                .reversed());
        return appointments;
    }

    public Integer getStaffTipsAppointmentCount(Integer businessId, Integer staffId, String startDate, String endDate) {
        List<Integer> apptIds =
                appointmentMapper.queryApptIdsByPageV2(List.of(businessId), staffId, startDate, endDate, null, null);
        if (CollectionUtils.isEmpty(apptIds)) {
            return 0;
        }
        // 查询数量，分页拉取第一页即可
        return orderService
                .getTipsInvoiceByPage(List.of(businessId), apptIds, 1, 1)
                .getCount();
    }

    public TipsApptsPageDTO getStaffTipsAppointmentsByPage(
            List<Integer> businessIds,
            Integer staffId,
            String startDate,
            String endDate,
            Integer pageNum,
            Integer pageSize) {
        List<Integer> apptIds =
                appointmentMapper.queryApptIdsByPageV2(businessIds, staffId, startDate, endDate, null, null);
        if (CollectionUtils.isEmpty(apptIds)) {
            return new TipsApptsPageDTO().setCount(0).setAppointments(Collections.emptyList());
        }
        GetTipsInvoicesDTO result = orderService.getTipsInvoiceByPage(businessIds, apptIds, pageNum, pageSize);
        List<MoeGroomingInvoice> invoices = result.getInvoiceList();
        Map<Integer, MoeGroomingInvoice> invoiceMap = invoices.stream()
                .collect(Collectors.toMap(
                        MoeGroomingInvoice::getGroomingId, invoice -> invoice, (invoice1, invoice2) -> invoice2));

        List<Integer> apptIdsForPage =
                invoices.stream().map(MoeGroomingInvoice::getGroomingId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(apptIdsForPage)) {
            return new TipsApptsPageDTO().setCount(result.getCount()).setAppointments(Collections.emptyList());
        }
        List<GroomingReportWebAppointment> appointments = appointmentMapper.selectByIdList(apptIdsForPage).stream()
                .map(ReportBeanMapper.INSTANCE::toGroomingReportWebAppointment)
                .collect(Collectors.toList());
        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId()));
        appointments.forEach(appointment -> setInvoiceAmount(appointment, invoiceMap.get(appointment.getId())));

        Map<Integer, List<GroomingReportWebAppointment>> apptMap =
                appointments.stream().collect(Collectors.groupingBy(GroomingReportWebAppointment::getBusinessId));
        for (Map.Entry<Integer, List<GroomingReportWebAppointment>> entry : apptMap.entrySet()) {
            fillReportWebApptPetDetail(entry.getKey(), entry.getValue(), false, true);
            reportOrderService.fillInvoiceInfoForPayroll(entry.getKey(), appointments);
        }
        return new TipsApptsPageDTO().setCount(result.getCount()).setAppointments(appointments);
    }

    public Map<Integer, List<GroomingReportWebAppointment>> getStaffPayrollAppointmentsByPage(
            List<Integer> businessIds,
            Integer staffId,
            String startDate,
            String endDate,
            Integer offset,
            Integer size) {
        // 先分页查id，再查appointment
        List<Integer> apptIds =
                appointmentMapper.queryApptIdsByPageV2(businessIds, staffId, startDate, endDate, offset, size);
        if (CollectionUtils.isEmpty(apptIds)) {
            return Map.of();
        }
        Map<Integer, List<GroomingReportWebAppointment>> apptMap = appointmentMapper.selectByIdList(apptIds).stream()
                .map(ReportBeanMapper.INSTANCE::toGroomingReportWebAppointment)
                .collect(Collectors.groupingBy(GroomingReportWebAppointment::getBusinessId));

        for (Map.Entry<Integer, List<GroomingReportWebAppointment>> entry : apptMap.entrySet()) {
            Integer businessId = entry.getKey();
            List<GroomingReportWebAppointment> appts = entry.getValue();
            fillReportWebApptPetDetail(businessId, appts, true, true);
            // 查询关联 order
            reportOrderService.fillInvoiceInfoForPayroll(businessId, appts);
        }
        return apptMap;
    }
}
