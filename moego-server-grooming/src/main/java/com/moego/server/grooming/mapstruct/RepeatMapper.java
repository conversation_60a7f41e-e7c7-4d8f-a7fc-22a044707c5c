package com.moego.server.grooming.mapstruct;

import com.intuit.ipp.util.StringUtils;
import com.moego.common.utils.DateUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.GroomingRepeatDTO;
import com.moego.server.grooming.dto.MoeRepeatInfoDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingRepeat;
import com.moego.server.grooming.params.AddRepeatParams;
import com.moego.server.grooming.params.AppointmentRepeatModifyParams;
import com.moego.server.grooming.params.AppointmentRepeatParams;
import com.moego.server.grooming.params.PreAuthParams;
import com.moego.server.grooming.params.RepeatParams;
import com.moego.server.grooming.params.SaveRepeatAppointmentParams;
import com.moego.server.grooming.web.params.MoeGroomingRepeatParam;
import java.util.Date;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RepeatMapper {
    RepeatMapper INSTANCE = Mappers.getMapper(RepeatMapper.class);

    // DM 相关
    MoeGroomingRepeat paramToBean(MoeGroomingRepeatParam param);

    // 以下是业务相关
    @Mapping(source = "setEndOn", target = "setEndOn", qualifiedByName = "stringToDate")
    @Mapping(source = "startsOn", target = "startsOn", qualifiedByName = "stringToDate")
    @Mapping(source = "repeatBy", target = "repeatBy", qualifiedByName = "convertIntToString")
    @Mapping(source = "repeatByDays", target = "repeatByDays", qualifiedByName = "convertIntArrayToString")
    MoeGroomingRepeat repeatParamsToBean(RepeatParams params);

    @Mapping(source = "setEndOn", target = "setEndOn", qualifiedByName = "stringToDate")
    @Mapping(source = "startsOn", target = "startsOn", qualifiedByName = "stringToDate")
    @Mapping(source = "repeatBy", target = "repeatBy", qualifiedByName = "convertIntToString")
    MoeGroomingRepeat repeatParamsToBean(AddRepeatParams params);

    @Mapping(source = "setEndOn", target = "setEndOn", qualifiedByName = "dateToString")
    @Mapping(source = "startsOn", target = "startsOn", qualifiedByName = "dateToString")
    @Mapping(source = "repeatBy", target = "repeatBy", qualifiedByName = "convertStringToInt")
    @Mapping(source = "repeatByDays", target = "repeatByDays", qualifiedByName = "convertStringToIntArray")
    RepeatParams beanToRepeatParams(MoeGroomingRepeat bean);

    @Mapping(source = "repeatBy", target = "repeatBy", qualifiedByName = "convertStringToInt")
    @Mapping(source = "repeatByDays", target = "repeatByDays", qualifiedByName = "convertStringToIntArray")
    @Mapping(source = "startsOn", target = "startsOn", qualifiedByName = "dateToString")
    @Mapping(source = "setEndOn", target = "setEndOn", qualifiedByName = "dateToString")
    MoeRepeatInfoDTO beanToDTO(MoeGroomingRepeat bean);

    MoeRepeatInfoDTO copyToRepeatInfoDTO(GroomingRepeatDTO dto);

    RepeatParams toRepeatParams(AddRepeatParams params);

    // 转化参数：接口入参 -> 更新 repeat appointment 参数
    @Mapping(source = "appointmentId", target = "id")
    @Mapping(source = "allPetsStartAtSameTime", target = "startAtSameTime")
    AppointmentRepeatModifyParams toAppointmentRepeatModifyParams(SaveRepeatAppointmentParams params);

    // 转化参数：接口入参 -> 新增 repeat appointment 参数
    @Mapping(source = "params.serviceList", target = "petServices")
    @Mapping(source = "params.staffId", target = "createdById")
    AppointmentRepeatParams toAppointmentRepeatParams(
            SaveRepeatAppointmentParams params, PreAuthParams preAuthParams, Integer repeatId);

    @Named("stringToDate")
    default Date stringToDate(String dateString) {
        if (!StringUtils.hasText(dateString)) {
            return null;
        }
        return DateUtil.parseDate(dateString, "yyyy-MM-dd");
    }

    @Named("dateToString")
    default String dateToString(Date date) {
        if (date == null) {
            return null;
        }
        return DateUtil.dateToStr(date);
    }

    @Named("convertStringToInt")
    default Integer convertStringToInt(String str) {
        if (!StringUtils.hasText(str)) {
            return null;
        }
        return Integer.parseInt(str);
    }

    @Named("convertIntToString")
    default String convertIntToString(Integer i) {
        if (i == null) {
            return null;
        }
        return String.valueOf(i);
    }

    @Named("convertIntArrayToString")
    default String convertIntArrayToString(List<Integer> list) {
        if (list == null) {
            list = List.of();
        }
        return JsonUtil.toJson(list);
    }

    @Named("convertStringToIntArray")
    default List<Integer> convertStringToIntArray(String s) {
        if (s == null) {
            return List.of();
        }
        return JsonUtil.toList(s, Integer.class);
    }
}
