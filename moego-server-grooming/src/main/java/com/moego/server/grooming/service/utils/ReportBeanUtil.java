package com.moego.server.grooming.service.utils;

import static com.moego.common.enums.ServiceEnum.TYPE_ADD_ONS;
import static com.moego.common.enums.ServiceEnum.TYPE_SERVICE;
import static com.moego.common.utils.AmountUtils.sum;

import com.moego.common.utils.AmountUtils;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.GroomingUtil;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.report.EmployeeContribute;
import com.moego.server.grooming.dto.report.ReportPaymentSummaryDto;
import com.moego.server.grooming.dto.report.ReportWebAppointment;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapstruct.ReportBeanMapper;
import com.moego.server.grooming.params.ReportWebApptsRequest;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.StaffPaymentForReportDTO;
import com.moego.server.grooming.service.dto.report.ReportBaseAmountDTO;
import com.moego.server.grooming.service.dto.report.StaffRevenueDetail;
import com.moego.server.grooming.utils.StatusUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.util.CollectionUtils;

public class ReportBeanUtil {
    private static ReportWebEmployee.ReportWebEmployeeBuilder initEmployeeReport() {
        return ReportWebEmployee.builder()
                .revenue(BigDecimal.ZERO)
                .totalTips(BigDecimal.ZERO)
                .totalTax(BigDecimal.ZERO)
                .tips(BigDecimal.ZERO)
                .tax(BigDecimal.ZERO)
                .discount(BigDecimal.ZERO)
                .collectedTips(BigDecimal.ZERO)
                .collectedServicePrice(BigDecimal.ZERO)
                .collectedAddonPrice(BigDecimal.ZERO)
                .collectedProductPrice(BigDecimal.ZERO)
                .collectedTax(BigDecimal.ZERO)
                .collectedRevenue(BigDecimal.ZERO)
                .totalProductAmount(BigDecimal.ZERO)
                .totalPayment(BigDecimal.ZERO)
                .totalRefund(BigDecimal.ZERO)
                .unpaidRevenue(BigDecimal.ZERO);
    }

    public static ReportWebEmployee buildInitEmployeeReport(
            Integer staffId, GroomingReportWebAppointment apt, Set<Integer> petIds, List<String> products) {
        return initEmployeeReport()
                .staffId(staffId)
                .aptId(apt.getId())
                .ownerId(apt.getCustomerId())
                .apptDate(apt.getAppointmentDate())
                .apptTime(apt.getAppointmentStartTime())
                .petNum(petIds.size())
                .apptStatus(StatusUtil.getApptStatusForReport(apt.getStatus()))
                .paymentStatus(GroomingUtil.getAppPaidDesc(apt.getIsPaid()))
                .services(ReportUtil.getServices(apt.getPetDetails(), TYPE_SERVICE.intValue(), staffId))
                .addOns(ReportUtil.getServices(apt.getPetDetails(), TYPE_ADD_ONS.intValue(), staffId))
                .products(products)
                .petIds(new ArrayList<>(petIds))
                .customerId(apt.getCustomerId())
                .invoiceId(apt.getInvoiceId())
                .build();
    }

    public static Map<Integer, List<StaffRevenueDetail>> buildStaffRevenueDetails(
            GroomingReportWebAppointment appt, List<ReportWebApptPetDetail> petDetails) {
        Map<Integer, List<StaffRevenueDetail>> petDetailToEmployees = new HashMap<>();
        for (var petDetail : petDetails) {
            if (CollectionUtils.isEmpty(petDetail.getOperationList())) {
                petDetailToEmployees.put(
                        petDetail.getId(), List.of(buildStaffRevenueDetail(petDetail.getStaffId(), appt, petDetail)));
                continue;
            }
            petDetailToEmployees.put(
                    petDetail.getId(),
                    petDetail.getOperationList().stream()
                            .map(GroomingServiceOperationDTO::getStaffId)
                            .distinct()
                            .map(staffId -> buildStaffRevenueDetail(staffId, appt, petDetail))
                            .toList());
        }
        return petDetailToEmployees;
    }

    public static StaffRevenueDetail buildStaffRevenueDetail(
            Integer staffId, GroomingReportWebAppointment appointment, ReportWebApptPetDetail petDetail) {
        return StaffRevenueDetail.builder()
                .staffId(staffId)
                .invoiceId(appointment.getInvoiceId())
                .appointmentId(appointment.getId())
                .appointmentStatus(StatusUtil.getApptStatusForReport(appointment.getStatus()))
                .date(petDetail.getStartDate())
                .startTime(petDetail.getStartTime().intValue())
                .paymentStatus(GroomingUtil.getAppPaidDesc(appointment.getIsPaid()))
                .customerId(appointment.getCustomerId())
                .services(ReportUtil.getServices(List.of(petDetail), TYPE_SERVICE.intValue(), petDetail.getStaffId()))
                .addOns(ReportUtil.getServices(List.of(petDetail), TYPE_ADD_ONS.intValue(), petDetail.getStaffId()))
                .petIds(List.of(petDetail.getPetId()))
                .build();
    }

    public static StaffRevenueDetail buildStaffRevenueDetail(
            Integer staffId, GroomingReportWebAppointment appointment, List<String> products) {
        return StaffRevenueDetail.builder()
                .staffId(staffId)
                .invoiceId(appointment.getInvoiceId())
                .appointmentId(appointment.getId())
                .appointmentStatus(StatusUtil.getApptStatusForReport(appointment.getStatus()))
                .date(appointment.getAppointmentDate())
                .startTime(appointment.getAppointmentStartTime())
                .paymentStatus(GroomingUtil.getAppPaidDesc(appointment.getIsPaid()))
                .customerId(appointment.getCustomerId())
                .products(products)
                .build();
    }

    public static ReportWebAppointment.ReportWebAppointmentBuilder buildBaseAppt(
            ReportWebApptsRequest request, GroomingReportWebAppointment a) {
        return ReportWebAppointment.builder()
                .bookingId(a.getId())
                .apptDate(DateUtil.dateToBusinessFormat(a.getAppointmentDate(), request.getDateFormat()))
                .apptTime(DateUtil.minuteToBusinessTime(a.getAppointmentStartTime(), request.getTimeFormatType()))
                .clientId(a.getCustomerId())
                .createDate(DateUtil.convertDateBySeconds(
                        a.getCreateTime(), request.getTimezoneName(), request.getDateFormat()))
                .createById(a.getCreatedById());
    }

    public static ReportPaymentSummaryDto buildPaymentSummaryDTO(
            String method, StaffPaymentForReportDTO dto, Integer ticketNum) {
        return ReportPaymentSummaryDto.builder()
                .paymentMethod(method)
                .sumAmount(dto.getTotalPayment().subtract(dto.getTotalRefund()))
                .apptSale(dto.getAptPayment().subtract(dto.getAptRefund()))
                .noShowFee(dto.getNoShowPayment().subtract(dto.getNoShowRefund()))
                .ticketNum(ticketNum)
                // 以下是新字段
                .totalCollectedRevenue(dto.getTotalPayment().subtract(dto.getTotalRefund()))
                .totalPayment(dto.getTotalPayment())
                .totalRefund(dto.getTotalRefund())
                .apptCollectedRevenue(dto.getAptPayment().subtract(dto.getAptRefund()))
                .apptPaidAmount(dto.getAptPayment())
                .apptRefundAmount(dto.getAptRefund())
                .noShowCollectedRevenue(dto.getNoShowPayment().subtract(dto.getNoShowRefund()))
                .noShowPaidAmount(dto.getNoShowPayment())
                .noShowRefundAmount(dto.getNoShowRefund())
                .collectedServicePrice(dto.getCollectedServicePrice())
                .collectedProductPrice(dto.getCollectedProductPrice())
                .collectedTax(dto.getCollectedTax())
                .collectedTips(dto.getCollectedTips())
                .discount(dto.getDiscount())
                .netSaleRevenue(dto.getNetSaleRevenue())
                .build();
    }

    public static void mergeEmployeeOverviewList(
            List<EmployeeContribute> mergeDataList, List<EmployeeContribute> reportDataList) {
        Map<Integer, EmployeeContribute> staffMap =
                mergeDataList.stream().collect(Collectors.toMap(EmployeeContribute::getId, Function.identity()));

        reportDataList.forEach(reportData -> {
            if (!staffMap.containsKey(reportData.getId())) {
                return;
            }
            EmployeeContribute mergeData = staffMap.get(reportData.getId());
            mergeData.setTotalAppts(mergeData.getTotalAppts() + reportData.getTotalAppts());
            mergeData.setCollectedRevenue(mergeData.getCollectedRevenue().add(reportData.getCollectedRevenue()));
            mergeData.setUnpaidRevenue(mergeData.getUnpaidRevenue().add(reportData.getUnpaidRevenue()));
            mergeData.setTotalTips(mergeData.getTotalTips().add(reportData.getTotalTips()));
            mergeData.setTotalTaxes(mergeData.getTotalTaxes().add(reportData.getTotalTaxes()));
        });
    }

    public static Map<Integer, ReportWebEmployee> buildInitEmployeeReports(
            List<GroomingReportWebAppointment> appointments) {
        Map<Integer, ReportWebEmployee> idToStaff = new HashMap<>();
        appointments.forEach(appointment -> {
            for (ReportWebApptPetDetail pd : appointment.getPetDetails()) {
                if (CommonUtil.isNormal(pd.getStaffId()) && !idToStaff.containsKey(pd.getStaffId())) {
                    idToStaff.put(
                            pd.getStaffId(),
                            buildInitEmployeeReport(pd.getStaffId(), appointment, Set.of(), List.of()));
                }
                pd.getOperationList().forEach(op -> {
                    if (CommonUtil.isNormal(op.getStaffId()) && !idToStaff.containsKey(op.getStaffId())) {
                        idToStaff.put(
                                op.getStaffId(),
                                buildInitEmployeeReport(op.getStaffId(), appointment, Set.of(), List.of()));
                    }
                });
            }
        });
        return idToStaff;
    }

    public static void initEmployeeReportCommissionAmount(StaffRevenueDetail employee) {
        employee.setServiceCommission(BigDecimal.ZERO);
        employee.setAddonCommission(BigDecimal.ZERO);
        employee.setTipsCommission(BigDecimal.ZERO);
        employee.setTotalCommission(BigDecimal.ZERO);
    }

    public static List<StaffRevenueDetail> mergeRevenueDetails(List<StaffRevenueDetail> staffRevenueDetails) {
        Map<String, StaffRevenueDetail> staffRevenueDetailMap = new HashMap<>();
        for (var revenue : staffRevenueDetails) {
            staffRevenueDetailMap.merge(
                    revenue.getStaffId() + revenue.getDate(), revenue, ReportBeanUtil::mergeStaffPayrollDetail);
        }
        return new ArrayList<>(staffRevenueDetailMap.values());
    }

    private static StaffRevenueDetail mergeStaffPayrollDetail(StaffRevenueDetail oldVal, StaffRevenueDetail newVal) {
        return oldVal.setStartTime(Math.min(oldVal.getStartTime(), newVal.getStartTime()))
                .setServices(mergeList(oldVal.getServices(), newVal.getServices()))
                .setAddOns(mergeList(oldVal.getAddOns(), newVal.getAddOns()))
                .setProducts(mergeList(oldVal.getProducts(), newVal.getProducts()))
                .setPetIds(mergeList(oldVal.getPetIds(), newVal.getPetIds()))
                .setExpectedRevenue(sum(oldVal.getExpectedRevenue(), newVal.getExpectedRevenue()))
                .setCollectedRevenue(sum(oldVal.getCollectedRevenue(), newVal.getCollectedRevenue()))
                .setNetSaleRevenue(sum(oldVal.getNetSaleRevenue(), newVal.getNetSaleRevenue()))
                .setUnpaidRevenue(sum(oldVal.getUnpaidRevenue(), newVal.getUnpaidRevenue()))
                .setTotalPayment(sum(oldVal.getTotalPayment(), newVal.getTotalPayment()))
                .setTotalRefund(sum(oldVal.getTotalRefund(), newVal.getTotalRefund()))
                .setTotalTips(sum(oldVal.getTotalTips(), newVal.getTotalTips()))
                .setTotalTax(sum(oldVal.getTotalTax(), newVal.getTotalTax()))
                .setDiscount(sum(oldVal.getDiscount(), newVal.getDiscount()))
                .setServicePrice(sum(oldVal.getServicePrice(), newVal.getServicePrice()))
                .setAddonPrice(sum(oldVal.getAddonPrice(), newVal.getAddonPrice()))
                .setProductPrice(sum(oldVal.getProductPrice(), newVal.getProductPrice()))
                .setCollectedServicePrice(sum(oldVal.getCollectedServicePrice(), newVal.getCollectedServicePrice()))
                .setCollectedAddonPrice(sum(oldVal.getCollectedAddonPrice(), newVal.getCollectedAddonPrice()))
                .setCollectedProductPrice(sum(oldVal.getCollectedProductPrice(), newVal.getCollectedProductPrice()))
                .setCollectedTips(sum(oldVal.getCollectedTips(), newVal.getCollectedTips()))
                .setCollectedTax(sum(oldVal.getCollectedTax(), newVal.getCollectedTax()))
                .setServiceCommissionBase(sum(oldVal.getServiceCommissionBase(), newVal.getServiceCommissionBase()))
                .setServiceCommission(sum(oldVal.getServiceCommission(), newVal.getServiceCommission()))
                .setAddonCommissionBase(sum(oldVal.getAddonCommissionBase(), newVal.getAddonCommissionBase()))
                .setAddonCommission(sum(oldVal.getAddonCommission(), newVal.getAddonCommission()))
                .setTipsCommissionBase(sum(oldVal.getTipsCommissionBase(), newVal.getTipsCommissionBase()))
                .setTipsCommission(sum(oldVal.getTipsCommission(), newVal.getTipsCommission()))
                .setTotalCommission(sum(oldVal.getTotalCommission(), newVal.getTotalCommission()))
                .setPaymentMap(mergeMap(oldVal.getPaymentMap(), newVal.getPaymentMap()))
                .setServiceCollectedMap(mergeMap(oldVal.getServiceCollectedMap(), newVal.getServiceCollectedMap()))
                .setAddonCollectedMap(mergeMap(oldVal.getAddonCollectedMap(), newVal.getAddonCollectedMap()))
                .setServiceExpectedMap(mergeMap(oldVal.getServiceExpectedMap(), newVal.getServiceExpectedMap()))
                .setAddonExpectedMap(mergeMap(oldVal.getAddonExpectedMap(), newVal.getAddonExpectedMap()))
                .setFinishExpectedAddonPrice(
                        sum(oldVal.getFinishExpectedAddonPrice(), newVal.getFinishExpectedAddonPrice()))
                .setFinishExpectedServicePrice(
                        sum(oldVal.getFinishExpectedServicePrice(), newVal.getFinishExpectedServicePrice()))
                .setFinishExpectedTips(sum(oldVal.getFinishExpectedTips(), newVal.getFinishExpectedTips()))
                .setExceptionAddonBase(sum(oldVal.getExceptionAddonBase(), newVal.getExceptionAddonBase()))
                .setExceptionServiceBase(sum(oldVal.getExceptionServiceBase(), newVal.getExceptionServiceBase()));
    }

    private static <T> List<T> mergeList(List<T> list1, List<T> list2) {
        return Stream.of(list1, list2).reduce(new ArrayList<>(), (oldList, newList) -> {
            oldList.addAll(newList);
            return oldList;
        });
    }

    private static <T> Map<T, BigDecimal> mergeMap(Map<T, BigDecimal> map1, Map<T, BigDecimal> map2) {
        return Stream.of(map1, map2)
                .map(Map::entrySet)
                .flatMap(Set::stream)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, AmountUtils::sum, HashMap::new));
    }

    public static List<ReportWebEmployee> convertToStaffPayrollSummary(List<StaffRevenueDetail> staffRevenueDetails) {
        Map<Integer, StaffRevenueDetail> staffMap = staffRevenueDetails.stream()
                .collect(Collectors.toMap(
                        StaffRevenueDetail::getStaffId, Function.identity(), ReportBeanUtil::mergeStaffPayrollDetail));
        return staffMap.values().stream()
                .map(revenue -> {
                    var employee = ReportBeanMapper.INSTANCE.toReportWebEmployee(revenue);
                    employee.setAddOns(revenue.getAddOns()); // addOns 这个字段 mapstruct 匹配不上，手动 set 下
                    return employee;
                })
                .collect(Collectors.toList());
    }

    public static List<ReportWebEmployee> convertToStaffPayrollDetail(List<StaffRevenueDetail> staffRevenueDetails) {
        return staffRevenueDetails.stream()
                .map(revenue -> {
                    var employee = ReportBeanMapper.INSTANCE.toReportWebEmployee(revenue);
                    employee.setAddOns(revenue.getAddOns());
                    return employee;
                })
                .collect(Collectors.toList());
    }

    /**
     * 设置 invoice 金额字段
     *
     * @param dto
     * @param invoice
     */
    public static void setInvoiceAmount(ReportBaseAmountDTO dto, MoeGroomingInvoice invoice) {
        dto.setInvoiceId(invoice.getId());
        dto.setTotalAmount(invoice.getTotalAmount());
        dto.setTipsAmount(invoice.getTipsAmount());
        dto.setTipsBaseAmount(invoice.getTipsBaseAmount());
        dto.setPaymentAmount(invoice.getPaymentAmount());
        dto.setPaidAmount(invoice.getPaidAmount());
        dto.setRemainAmount(invoice.getRemainAmount());
        dto.setTaxAmount(invoice.getTaxAmount());
        dto.setServiceTaxAmount(invoice.getTaxAmount());
        dto.setProductTaxAmount(BigDecimal.ZERO); // 初始化，后面会对product重新设置
        dto.setServiceChargeTaxAmount(BigDecimal.ZERO);
        dto.setSubTotalAmount(invoice.getSubTotalAmount());
        dto.setDiscountAmount(invoice.getDiscountAmount());
        dto.setServiceDiscountAmount(invoice.getDiscountAmount());
        dto.setProductDiscountAmount(BigDecimal.ZERO); // 初始化，后面会对product重新设置
        dto.setServiceChargeDiscountAmount(BigDecimal.ZERO);
        dto.setInvoiceType(invoice.getType());
        dto.setConvenienceFee(invoice.getConvenienceFee());
        dto.setRefundedAmount(invoice.getRefundedAmount());
    }
}
