package com.moego.server.grooming.mapstruct;

import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.grooming.web.vo.client.StaffDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface StaffMapper {
    StaffMapper INSTANCE = Mappers.getMapper(StaffMapper.class);

    @Mappings({@Mapping(target = "staffId", source = "id")})
    StaffDetailVO dto2VO(MoeStaffDto staffDto);
}
