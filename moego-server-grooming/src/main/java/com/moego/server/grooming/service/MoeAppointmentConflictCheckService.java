package com.moego.server.grooming.service;

import com.moego.common.enums.ConflictTypeEnum;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.grooming.dto.ConflictInfoDTO;
import com.moego.server.grooming.dto.RepeatPreviewInfoDTO;
import com.moego.server.grooming.dto.StaffConflictDTO;
import com.moego.server.grooming.dto.StaffConflictInfoDTO;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.params.PreviewRepeatParams;
import com.moego.server.grooming.params.RepeatStaffInfoParams;
import com.moego.server.grooming.params.appointment.conflict.ConflictCheckParams;
import com.moego.server.grooming.service.utils.AppointmentUtil;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class MoeAppointmentConflictCheckService {

    private final PetDetailMapperProxy moeGroomingPetDetailMapper;
    private final IBusinessStaffClient iBusinessStaffClient;

    public void checkConflictForRepeatV2(
            List<RepeatPreviewInfoDTO> previewInfoList, PreviewRepeatParams previewRepeatParams) {
        if (CollectionUtils.isEmpty(previewInfoList)) {
            return;
        }

        Integer businessId = previewRepeatParams.getBusinessId();
        Integer existAppointmentId = previewRepeatParams.getExistAppointmentId(); // 基于已有预约创建 repeat 时，检查冲突需要排除自己
        List<Integer> existAppointmentIdList =
                Objects.nonNull(existAppointmentId) ? List.of(existAppointmentId) : List.of();
        Integer existRepeatId = previewRepeatParams.getRepeatId(); // 编辑已有 repeat 的时候，检查冲突需要排除自己
        List<String> dateList =
                previewInfoList.stream().map(ConflictInfoDTO::getDate).sorted().toList();
        List<Integer> staffIdList = previewInfoList.stream()
                .map(RepeatPreviewInfoDTO::getStaffIdList)
                .flatMap(Collection::stream)
                .distinct()
                .toList();

        // 查询 staff 的 working hour 和 appointment/block
        Map<Integer, StaffWorkingRangeDto> staffWorkTimeMap =
                getStaffWorkTimeMap(businessId, staffIdList, dateList.get(0), dateList.get(dateList.size() - 1));
        Map<String, List<StaffConflictDTO>> appointmentBlockMap =
                getDateAppointmentBlockMap(businessId, dateList, staffIdList, existAppointmentIdList, existRepeatId);

        for (RepeatPreviewInfoDTO previewInfo : previewInfoList) {
            // 检查 staff 工作时间
            checkWorkingHourConflict(previewInfo, staffWorkTimeMap);
            // 检查 appointment 和 block 是否冲突
            checkAppointmentAndBlockConflict(previewInfo, appointmentBlockMap.get(previewInfo.getDate()));
        }

        // 对外层的 preview info 的 isNotConflict 赋值：当所有 staff 都不冲突时为 true
        for (ConflictInfoDTO conflictInfo : previewInfoList) {
            conflictInfo.setIsNotConflict(
                    conflictInfo.getStaffConflictInfoList().stream().allMatch(StaffConflictInfoDTO::getIsNotConflict));
        }
    }

    public List<ConflictInfoDTO> checkConflictBatch(Integer businessId, List<ConflictCheckParams> paramsList) {
        List<ConflictInfoDTO> resultList = new ArrayList<>();

        List<String> dateList =
                paramsList.stream().map(ConflictCheckParams::getDate).sorted().toList();
        List<Integer> staffIdList = paramsList.stream()
                .map(ConflictCheckParams::getStaffConflictCheckParams)
                .flatMap(Collection::stream)
                .map(RepeatStaffInfoParams::getStaffId)
                .distinct()
                .toList();
        List<Integer> appointmentIdList = paramsList.stream()
                .map(ConflictCheckParams::getAppointmentId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 查询 staff 的 working hour 和 appointment/block
        Map<Integer, StaffWorkingRangeDto> staffWorkTimeMap =
                getStaffWorkTimeMap(businessId, staffIdList, dateList.get(0), dateList.get(dateList.size() - 1));
        Map<String, List<StaffConflictDTO>> appointmentBlockMap =
                getDateAppointmentBlockMap(businessId, dateList, staffIdList, appointmentIdList, null);

        for (ConflictCheckParams params : paramsList) {
            ConflictInfoDTO conflictInfo = buildConflictInfo(params);
            // 检查 staff 工作时间
            checkWorkingHourConflict(conflictInfo, staffWorkTimeMap);
            // 检查 appointment 和 block 是否冲突
            checkAppointmentAndBlockConflict(conflictInfo, appointmentBlockMap.get(conflictInfo.getDate()));
            resultList.add(conflictInfo);
        }

        // 对外层的 preview info 的 isNotConflict 赋值：当所有 staff 都不冲突时为 true
        for (ConflictInfoDTO conflictInfo : resultList) {
            conflictInfo.setIsNotConflict(
                    conflictInfo.getStaffConflictInfoList().stream().allMatch(StaffConflictInfoDTO::getIsNotConflict));
        }
        return resultList;
    }

    private void checkWorkingHourConflict(
            ConflictInfoDTO conflictInfo, Map<Integer, StaffWorkingRangeDto> staffWorkTimeMap) {
        if (Boolean.FALSE.equals(conflictInfo.getIsNotConflict())) {
            return;
        }

        conflictInfo.getStaffConflictInfoList().forEach(staffConflictInfo -> {
            // 未查到工作时间，返回 false
            if (!staffWorkTimeMap.containsKey(staffConflictInfo.getStaffId())) {
                staffConflictInfo.setIsNotConflict(false);
                staffConflictInfo.setConflictType(ConflictTypeEnum.WORK_TIME.getValue());
                return;
            }

            Map<String, List<TimeRangeDto>> timeRange =
                    staffWorkTimeMap.get(staffConflictInfo.getStaffId()).getTimeRange();
            int compareStart = staffConflictInfo.getStartTime();
            int compareEnd = staffConflictInfo.getStartTime() + staffConflictInfo.getDuration();
            List<TimeRangeDto> timeRangeList = timeRange.get(conflictInfo.getDate());
            // 比对是否工作时间内
            if (AppointmentUtil.checkWorkingHourConflict(compareStart, compareEnd, timeRangeList)) {
                staffConflictInfo.setIsNotConflict(false);
                staffConflictInfo.setConflictType(ConflictTypeEnum.WORK_TIME.getValue());
            }
        });
    }

    private void checkAppointmentAndBlockConflict(
            ConflictInfoDTO conflictInfo, List<StaffConflictDTO> appointmentBlockList) {
        // 当前日期没有 appointment 和 block，跳过检查
        if (CollectionUtils.isEmpty(appointmentBlockList)) {
            return;
        }

        Map<Integer, List<StaffConflictDTO>> staffAppointmentBlockMap =
                appointmentBlockList.stream().collect(Collectors.groupingBy(StaffConflictDTO::getStaffId));
        for (StaffConflictInfoDTO staffConflictInfo : conflictInfo.getStaffConflictInfoList()) {
            // 当前日期 staff 没有 appointment 和 block 或者已有 conflictType，跳过检查
            if (!staffAppointmentBlockMap.containsKey(staffConflictInfo.getStaffId())
                    || staffConflictInfo.getConflictType() != null) {
                continue;
            }
            Integer staffId = staffConflictInfo.getStaffId();
            int compareStart = staffConflictInfo.getStartTime();
            int compareEnd = staffConflictInfo.getStartTime() + staffConflictInfo.getDuration();
            for (StaffConflictDTO appointmentBlock : staffAppointmentBlockMap.get(staffId)) {
                if (AppointmentUtil.checkAppointmentOrBlockConflict(compareStart, compareEnd, appointmentBlock)) {
                    Integer conflictType = AppointmentUtil.isBlock(appointmentBlock.getIsBlock())
                            ? ConflictTypeEnum.BLOCK.getValue()
                            : ConflictTypeEnum.APPOINTMENT.getValue();

                    staffConflictInfo.setIsNotConflict(false);
                    staffConflictInfo.setConflictType(conflictType);
                    break;
                }
            }
        }
    }

    public Map<Integer, StaffWorkingRangeDto> getStaffWorkTimeMap(
            Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {
        WorkingDailyQueryRangeVo workingDailyQueryParams = new WorkingDailyQueryRangeVo()
                .setStaffIdList(staffIdList)
                .setStartDate(startDate)
                .setEndDate(endDate);
        // 内部查询可以忽略 tokenStaffId 这个参数
        return iBusinessStaffClient.queryRange(businessId, null, workingDailyQueryParams).getData().stream()
                .collect(Collectors.toMap(StaffWorkingRangeDto::getStaffId, Function.identity()));
    }

    public Map<String, List<StaffConflictDTO>> getDateAppointmentBlockMap(
            Integer businessId,
            List<String> dateList,
            List<Integer> staffIdList,
            List<Integer> existAppointmentIdList,
            Integer existRepeatId) {
        return moeGroomingPetDetailMapper
                .queryPetDetailByAppointmentDatesAndStaffIds(
                        businessId, dateList, staffIdList, existAppointmentIdList, existRepeatId)
                .stream()
                .collect(Collectors.groupingBy(StaffConflictDTO::getAppointmentDate));
    }

    private ConflictInfoDTO buildConflictInfo(ConflictCheckParams params) {
        Integer appointmentStartTime = params.getStaffConflictCheckParams().stream()
                .map(RepeatStaffInfoParams::getStartTime)
                .min(Integer::compareTo)
                .orElse(0);
        Integer appointmentDuration = params.getStaffConflictCheckParams().stream()
                .map(RepeatStaffInfoParams::getServiceTime)
                .reduce(Integer::sum)
                .orElse(0);
        List<Integer> staffIdList = params.getStaffConflictCheckParams().stream()
                .map(RepeatStaffInfoParams::getStaffId)
                .distinct()
                .toList();
        List<StaffConflictInfoDTO> staffConflictInfoList = params.getStaffConflictCheckParams().stream()
                .map(staff -> new StaffConflictInfoDTO()
                        .setStaffId(staff.getStaffId())
                        .setStartTime(staff.getStartTime())
                        .setDuration(staff.getServiceTime())
                        .setIsNotConflict(true))
                .toList();

        return new ConflictInfoDTO()
                .setStaffIdList(staffIdList)
                .setDate(params.getDate())
                .setStartTime(appointmentStartTime)
                .setDuration(appointmentDuration)
                .setIsNotConflict(true)
                .setStaffConflictInfoList(staffConflictInfoList);
    }
}
