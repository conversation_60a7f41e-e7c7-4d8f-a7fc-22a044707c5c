package com.moego.server.grooming.service.client;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.ClientApptConst;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.params.PageQuery;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.dto.client.ListClientApptDTO;
import com.moego.server.grooming.service.dto.client.ListCommonApptDTO;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@Service
@AllArgsConstructor
public class ClientRequestApptService implements IBaseClientApptService {

    private final AppointmentMapperProxy appointmentMapper;

    @Override
    public Page<MoeGroomingAppointment> getApptList(ListClientApptDTO listClientApptDTO, PageQuery pageQuery) {
        Map<Integer, BusinessDateTimeDTO> businessDateTimeDTOMap = listClientApptDTO.getBusinessDateTimeDTOMap();
        List<ListCommonApptDTO> commonApptDTOList = listClientApptDTO.getCustomerIdDTOList().stream()
                .filter(customerIdDTO -> businessDateTimeDTOMap.containsKey(customerIdDTO.getBusinessId()))
                .map(customerIdDTO -> {
                    BusinessDateTimeDTO dateTimeDTO = businessDateTimeDTOMap.get(customerIdDTO.getBusinessId());
                    ListCommonApptDTO commonApptDTO = new ListCommonApptDTO()
                            .setIsDeprecate(GroomingAppointmentEnum.IS_DEPRECATE_FALSE.byteValue())
                            .setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue())
                            .setBookOnlineStatus(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)
                            .setSource(GroomingAppointmentEnum.SOURCE_OB)
                            .setCreatedById(ClientApptConst.CREATED_BY_OB)
                            .setCurrentDate(dateTimeDTO.getCurrentDate())
                            .setCurrentMinutes(dateTimeDTO.getCurrentMinutes());
                    commonApptDTO.setBusinessId(customerIdDTO.getBusinessId());
                    commonApptDTO.setCustomerId(customerIdDTO.getCustomerId());
                    commonApptDTO.setCompanyId(customerIdDTO.getCompanyId());
                    return commonApptDTO;
                })
                .collect(Collectors.toList());
        return PageHelper.startPage(pageQuery.getPageNum(), pageQuery.getPageSize())
                .doSelectPage(() -> appointmentMapper.listClientAppt(commonApptDTOList, pageQuery));
    }

    @Override
    public Byte getApptType() {
        return ClientApptConst.APPT_TYPE_REQUESTS;
    }
}
