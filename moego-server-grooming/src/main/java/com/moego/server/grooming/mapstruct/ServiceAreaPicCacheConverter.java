package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.mapperbean.ServiceAreaPicCache;
import com.moego.server.grooming.service.params.ServiceAreaPicCacheOperationParams;
import com.moego.server.grooming.web.vo.ServiceAreaPicCacheVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ServiceAreaPicCacheConverter {

    ServiceAreaPicCacheConverter INSTANCE = Mappers.getMapper(ServiceAreaPicCacheConverter.class);

    ServiceAreaPicCacheVO entityToVO(ServiceAreaPicCache entity);

    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "businessId", ignore = true)
    ServiceAreaPicCache paramToEntity(ServiceAreaPicCacheOperationParams param);
}
