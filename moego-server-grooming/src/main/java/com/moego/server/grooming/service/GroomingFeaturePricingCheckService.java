package com.moego.server.grooming.service;

import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.FeatureConst;
import com.moego.common.utils.DateUtil;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.payment.client.IPaymentPlanClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class GroomingFeaturePricingCheckService {

    private final IPaymentPlanClient iPaymentPlanClient;
    private final MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;

    public void checkOnlineBooking(Integer businessId, MoeBusinessBookOnline obSettingInfo) {
        if (obSettingInfo == null || CustomerContactEnum.BUSINESS_IS_NOT_ENABLE.equals(obSettingInfo.getIsEnable())) {
            return;
        }
        // 检查商家的 pricing 套餐内，是否允许使用 online booking
        boolean allowOB = iPaymentPlanClient.checkFeatureCodeIsEnableByBid(businessId, FeatureConst.FC_ONLINE_BOOKING);
        if (!allowOB) {
            if (obSettingInfo.getId() != null) {
                MoeBusinessBookOnline updateBean = new MoeBusinessBookOnline();
                updateBean.setId(obSettingInfo.getId());
                updateBean.setIsEnable(CustomerContactEnum.BUSINESS_IS_NOT_ENABLE);
                updateBean.setUpdateTime(DateUtil.get10Timestamp());
                moeBusinessBookOnlineMapper.updateByPrimaryKeySelective(updateBean);
            }
            // 同时修改返回值
            obSettingInfo.setIsEnable(CustomerContactEnum.BUSINESS_IS_NOT_ENABLE);
        }
    }
}
