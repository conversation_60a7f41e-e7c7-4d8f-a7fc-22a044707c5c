package com.moego.server.grooming.service.statemachine.action;

import com.google.protobuf.util.Timestamps;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.statemachine.context.ActionContext;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UnConfirmAction implements IStateTransitionAction {

    private final AppointmentServiceBlockingStub appointmentStub;

    @Override
    public boolean suit(AppointmentStatusEnum newStatus) {
        return AppointmentStatusEnum.UNCONFIRMED.equals(newStatus);
    }

    @Override
    public int execute(MoeGroomingAppointment moeGroomingAppointment, ActionContext actionContext) {
        if (!Objects.equals(AppointmentStatusEnum.CONFIRMED.getValue(), moeGroomingAppointment.getStatus())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "Appointment[%d] status is %s, can not unconfirm",
                            moeGroomingAppointment.getId(), moeGroomingAppointment.getStatus()));
        }

        var builder = UpdateAppointmentSelectiveRequest.newBuilder();
        builder.setId(moeGroomingAppointment.getId());
        builder.setStatus(AppointmentStatus.UNCONFIRMED);
        builder.setUpdateTime(Timestamps.now());
        Optional.ofNullable(AuthContext.get().staffId()).ifPresent(builder::setUpdatedById);
        builder.setConfirmedTime(0L);
        builder.setConfirmBy(0);

        return appointmentStub.updateAppointmentSelective(builder.build()).getAffectedRows();
    }

    @Override
    public int revert(MoeGroomingAppointment moeGroomingAppointment) {
        return 0;
    }
}
