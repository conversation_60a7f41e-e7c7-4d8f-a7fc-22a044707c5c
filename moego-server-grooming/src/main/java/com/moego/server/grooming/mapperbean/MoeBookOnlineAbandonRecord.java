package com.moego.server.grooming.mapperbean;

import java.util.Date;
import java.util.List;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_abandon_record
 */
public class MoeBookOnlineAbandonRecord {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   moe_business.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   Unique ID for each booking flow
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.booking_flow_id
     *
     * @mbg.generated
     */
    private String bookingFlowId;

    /**
     * Database Column Remarks:
     *   existing client id, moe_customer.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   referer source url
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.referer
     *
     * @mbg.generated
     */
    private String referer;

    /**
     * Database Column Remarks:
     *   new client & existing client phone number
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.phone_number
     *
     * @mbg.generated
     */
    private String phoneNumber;

    /**
     * Database Column Remarks:
     *   new client first name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.first_name
     *
     * @mbg.generated
     */
    private String firstName;

    /**
     * Database Column Remarks:
     *   new client last name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.last_name
     *
     * @mbg.generated
     */
    private String lastName;

    /**
     * Database Column Remarks:
     *   email
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.email
     *
     * @mbg.generated
     */
    private String email;

    /**
     * Database Column Remarks:
     *   referral source, moe_customer_source.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.referral_source_id
     *
     * @mbg.generated
     */
    private Integer referralSourceId;

    /**
     * Database Column Remarks:
     *   preferred groomer id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.preferred_groomer_id
     *
     * @mbg.generated
     */
    private Integer preferredGroomerId;

    /**
     * Database Column Remarks:
     *   preferred frequency day, unit day
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.preferred_frequency_day
     *
     * @mbg.generated
     */
    private Integer preferredFrequencyDay;

    /**
     * Database Column Remarks:
     *   0-by days, 1-by weeks
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.preferred_frequency_type
     *
     * @mbg.generated
     */
    private Byte preferredFrequencyType;

    /**
     * Database Column Remarks:
     *   customer preferred apt day, int array, range: 0-6
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.preferred_day
     *
     * @mbg.generated
     */
    private String preferredDay;

    /**
     * Database Column Remarks:
     *   customer preferred time in minutes array
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.preferred_time
     *
     * @mbg.generated
     */
    private String preferredTime;

    /**
     * Database Column Remarks:
     *   custom question answers, question ID to answer
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.customer_question_answers
     *
     * @mbg.generated
     */
    private String customerQuestionAnswers;

    /**
     * Database Column Remarks:
     *   existing address id, moe_address.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.address_id
     *
     * @mbg.generated
     */
    private Integer addressId;

    /**
     * Database Column Remarks:
     *   address line 1
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.address1
     *
     * @mbg.generated
     */
    private String address1;

    /**
     * Database Column Remarks:
     *   address line 2
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.address2
     *
     * @mbg.generated
     */
    private String address2;

    /**
     * Database Column Remarks:
     *   city
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.city
     *
     * @mbg.generated
     */
    private String city;

    /**
     * Database Column Remarks:
     *   state
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.state
     *
     * @mbg.generated
     */
    private String state;

    /**
     * Database Column Remarks:
     *   zipcode
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.zipcode
     *
     * @mbg.generated
     */
    private String zipcode;

    /**
     * Database Column Remarks:
     *   country
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.country
     *
     * @mbg.generated
     */
    private String country;

    /**
     * Database Column Remarks:
     *   latitude
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.lat
     *
     * @mbg.generated
     */
    private String lat;

    /**
     * Database Column Remarks:
     *   longitude
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.lng
     *
     * @mbg.generated
     */
    private String lng;

    /**
     * Database Column Remarks:
     *   The next step is the abandon step (welcome_page, basic_info, select_address, select_pet, select_service, select_groomer, select_time, additional_pet_info, personal_info, card_on_file, prepay, submit)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.abandon_step
     *
     * @mbg.generated
     */
    private String abandonStep;

    /**
     * Database Column Remarks:
     *   The currently completed step time is the abandon time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.abandon_time
     *
     * @mbg.generated
     */
    private Long abandonTime;

    /**
     * Database Column Remarks:
     *   abandoned, contacted, recovered
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.abandon_status
     *
     * @mbg.generated
     */
    private String abandonStatus;

    /**
     * Database Column Remarks:
     *   time of last message sent
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.last_texted_time
     *
     * @mbg.generated
     */
    private Long lastTextedTime;

    /**
     * Database Column Remarks:
     *   time of last email sent
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.last_emailed_time
     *
     * @mbg.generated
     */
    private Long lastEmailedTime;

    /**
     * Database Column Remarks:
     *   The way the abandon behavior is recovered (book_by_email_link, schedule)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.recovery_type
     *
     * @mbg.generated
     */
    private Long recoveryType;

    /**
     * Database Column Remarks:
     *   The time when the abandon behavior was recovered
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.recovery_time
     *
     * @mbg.generated
     */
    private Long recoveryTime;

    /**
     * Database Column Remarks:
     *   select groomer id, moe_staff.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   related appointment id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.appointment_id
     *
     * @mbg.generated
     */
    private Integer appointmentId;

    /**
     * Database Column Remarks:
     *   lead type, new_visitor, existing_client
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.lead_type
     *
     * @mbg.generated
     */
    private String leadType;

    /**
     * Database Column Remarks:
     *   appointment date, format yyyy-MM-dd
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.appointment_date
     *
     * @mbg.generated
     */
    private String appointmentDate;

    /**
     * Database Column Remarks:
     *   appointment start time, current minutes offset
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.appointment_start_time
     *
     * @mbg.generated
     */
    private Integer appointmentStartTime;

    /**
     * Database Column Remarks:
     *   signed agreement info, contains agreement ID, base64 image signature
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.agreement_info
     *
     * @mbg.generated
     */
    private String agreementInfo;

    /**
     * Database Column Remarks:
     *   0-false, 1-true
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.is_deleted
     *
     * @mbg.generated
     */
    private Boolean isDeleted;

    /**
     * Database Column Remarks:
     *   1-test data, 2-manual delete, 3-block client
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.delete_type
     *
     * @mbg.generated
     */
    private Byte deleteType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   if use payment seg setting
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.use_payment_seg_setting
     *
     * @mbg.generated
     */
    private Boolean usePaymentSegSetting;

    /**
     * Database Column Remarks:
     *   if use payment seg setting
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.payment_seg_setting_rule
     *
     * @mbg.generated
     */
    private String paymentSegSettingRule;

    /**
     * Database Column Remarks:
     *   是否发送过 schedule message
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.is_send_schedule_message
     *
     * @mbg.generated
     */
    private Boolean isSendScheduleMessage;

    /**
     * Database Column Remarks:
     *   GROOMING, BOARDING, DAYCARE, EVALUATION
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.care_type
     *
     * @mbg.generated
     */
    private String careType;

    /**
     * Database Column Remarks:
     *   daycare specific dates, yyyy-MM-dd
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.specific_dates
     *
     * @mbg.generated
     */
    private List<String> specificDates;

    /**
     * Database Column Remarks:
     *   appointment end date, yyyy-MM-dd, boarding end date
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.appointment_end_date
     *
     * @mbg.generated
     */
    private String appointmentEndDate;

    /**
     * Database Column Remarks:
     *   appointment end time, current minutes offset, boarding or daycare pickup time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.appointment_end_time
     *
     * @mbg.generated
     */
    private Integer appointmentEndTime;

    /**
     * Database Column Remarks:
     *   是否发送过 notification
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.is_notification_sent
     *
     * @mbg.generated
     */
    private Boolean isNotificationSent;

    /**
     * Database Column Remarks:
     *   alert note
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record.additional_note
     *
     * @mbg.generated
     */
    private String additionalNote;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.id
     *
     * @return the value of moe_book_online_abandon_record.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.id
     *
     * @param id the value for moe_book_online_abandon_record.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.business_id
     *
     * @return the value of moe_book_online_abandon_record.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.business_id
     *
     * @param businessId the value for moe_book_online_abandon_record.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.booking_flow_id
     *
     * @return the value of moe_book_online_abandon_record.booking_flow_id
     *
     * @mbg.generated
     */
    public String getBookingFlowId() {
        return bookingFlowId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.booking_flow_id
     *
     * @param bookingFlowId the value for moe_book_online_abandon_record.booking_flow_id
     *
     * @mbg.generated
     */
    public void setBookingFlowId(String bookingFlowId) {
        this.bookingFlowId = bookingFlowId == null ? null : bookingFlowId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.customer_id
     *
     * @return the value of moe_book_online_abandon_record.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.customer_id
     *
     * @param customerId the value for moe_book_online_abandon_record.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.referer
     *
     * @return the value of moe_book_online_abandon_record.referer
     *
     * @mbg.generated
     */
    public String getReferer() {
        return referer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.referer
     *
     * @param referer the value for moe_book_online_abandon_record.referer
     *
     * @mbg.generated
     */
    public void setReferer(String referer) {
        this.referer = referer == null ? null : referer.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.phone_number
     *
     * @return the value of moe_book_online_abandon_record.phone_number
     *
     * @mbg.generated
     */
    public String getPhoneNumber() {
        return phoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.phone_number
     *
     * @param phoneNumber the value for moe_book_online_abandon_record.phone_number
     *
     * @mbg.generated
     */
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber == null ? null : phoneNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.first_name
     *
     * @return the value of moe_book_online_abandon_record.first_name
     *
     * @mbg.generated
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.first_name
     *
     * @param firstName the value for moe_book_online_abandon_record.first_name
     *
     * @mbg.generated
     */
    public void setFirstName(String firstName) {
        this.firstName = firstName == null ? null : firstName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.last_name
     *
     * @return the value of moe_book_online_abandon_record.last_name
     *
     * @mbg.generated
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.last_name
     *
     * @param lastName the value for moe_book_online_abandon_record.last_name
     *
     * @mbg.generated
     */
    public void setLastName(String lastName) {
        this.lastName = lastName == null ? null : lastName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.email
     *
     * @return the value of moe_book_online_abandon_record.email
     *
     * @mbg.generated
     */
    public String getEmail() {
        return email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.email
     *
     * @param email the value for moe_book_online_abandon_record.email
     *
     * @mbg.generated
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.referral_source_id
     *
     * @return the value of moe_book_online_abandon_record.referral_source_id
     *
     * @mbg.generated
     */
    public Integer getReferralSourceId() {
        return referralSourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.referral_source_id
     *
     * @param referralSourceId the value for moe_book_online_abandon_record.referral_source_id
     *
     * @mbg.generated
     */
    public void setReferralSourceId(Integer referralSourceId) {
        this.referralSourceId = referralSourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.preferred_groomer_id
     *
     * @return the value of moe_book_online_abandon_record.preferred_groomer_id
     *
     * @mbg.generated
     */
    public Integer getPreferredGroomerId() {
        return preferredGroomerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.preferred_groomer_id
     *
     * @param preferredGroomerId the value for moe_book_online_abandon_record.preferred_groomer_id
     *
     * @mbg.generated
     */
    public void setPreferredGroomerId(Integer preferredGroomerId) {
        this.preferredGroomerId = preferredGroomerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.preferred_frequency_day
     *
     * @return the value of moe_book_online_abandon_record.preferred_frequency_day
     *
     * @mbg.generated
     */
    public Integer getPreferredFrequencyDay() {
        return preferredFrequencyDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.preferred_frequency_day
     *
     * @param preferredFrequencyDay the value for moe_book_online_abandon_record.preferred_frequency_day
     *
     * @mbg.generated
     */
    public void setPreferredFrequencyDay(Integer preferredFrequencyDay) {
        this.preferredFrequencyDay = preferredFrequencyDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.preferred_frequency_type
     *
     * @return the value of moe_book_online_abandon_record.preferred_frequency_type
     *
     * @mbg.generated
     */
    public Byte getPreferredFrequencyType() {
        return preferredFrequencyType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.preferred_frequency_type
     *
     * @param preferredFrequencyType the value for moe_book_online_abandon_record.preferred_frequency_type
     *
     * @mbg.generated
     */
    public void setPreferredFrequencyType(Byte preferredFrequencyType) {
        this.preferredFrequencyType = preferredFrequencyType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.preferred_day
     *
     * @return the value of moe_book_online_abandon_record.preferred_day
     *
     * @mbg.generated
     */
    public String getPreferredDay() {
        return preferredDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.preferred_day
     *
     * @param preferredDay the value for moe_book_online_abandon_record.preferred_day
     *
     * @mbg.generated
     */
    public void setPreferredDay(String preferredDay) {
        this.preferredDay = preferredDay == null ? null : preferredDay.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.preferred_time
     *
     * @return the value of moe_book_online_abandon_record.preferred_time
     *
     * @mbg.generated
     */
    public String getPreferredTime() {
        return preferredTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.preferred_time
     *
     * @param preferredTime the value for moe_book_online_abandon_record.preferred_time
     *
     * @mbg.generated
     */
    public void setPreferredTime(String preferredTime) {
        this.preferredTime = preferredTime == null ? null : preferredTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.customer_question_answers
     *
     * @return the value of moe_book_online_abandon_record.customer_question_answers
     *
     * @mbg.generated
     */
    public String getCustomerQuestionAnswers() {
        return customerQuestionAnswers;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.customer_question_answers
     *
     * @param customerQuestionAnswers the value for moe_book_online_abandon_record.customer_question_answers
     *
     * @mbg.generated
     */
    public void setCustomerQuestionAnswers(String customerQuestionAnswers) {
        this.customerQuestionAnswers = customerQuestionAnswers == null ? null : customerQuestionAnswers.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.address_id
     *
     * @return the value of moe_book_online_abandon_record.address_id
     *
     * @mbg.generated
     */
    public Integer getAddressId() {
        return addressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.address_id
     *
     * @param addressId the value for moe_book_online_abandon_record.address_id
     *
     * @mbg.generated
     */
    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.address1
     *
     * @return the value of moe_book_online_abandon_record.address1
     *
     * @mbg.generated
     */
    public String getAddress1() {
        return address1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.address1
     *
     * @param address1 the value for moe_book_online_abandon_record.address1
     *
     * @mbg.generated
     */
    public void setAddress1(String address1) {
        this.address1 = address1 == null ? null : address1.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.address2
     *
     * @return the value of moe_book_online_abandon_record.address2
     *
     * @mbg.generated
     */
    public String getAddress2() {
        return address2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.address2
     *
     * @param address2 the value for moe_book_online_abandon_record.address2
     *
     * @mbg.generated
     */
    public void setAddress2(String address2) {
        this.address2 = address2 == null ? null : address2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.city
     *
     * @return the value of moe_book_online_abandon_record.city
     *
     * @mbg.generated
     */
    public String getCity() {
        return city;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.city
     *
     * @param city the value for moe_book_online_abandon_record.city
     *
     * @mbg.generated
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.state
     *
     * @return the value of moe_book_online_abandon_record.state
     *
     * @mbg.generated
     */
    public String getState() {
        return state;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.state
     *
     * @param state the value for moe_book_online_abandon_record.state
     *
     * @mbg.generated
     */
    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.zipcode
     *
     * @return the value of moe_book_online_abandon_record.zipcode
     *
     * @mbg.generated
     */
    public String getZipcode() {
        return zipcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.zipcode
     *
     * @param zipcode the value for moe_book_online_abandon_record.zipcode
     *
     * @mbg.generated
     */
    public void setZipcode(String zipcode) {
        this.zipcode = zipcode == null ? null : zipcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.country
     *
     * @return the value of moe_book_online_abandon_record.country
     *
     * @mbg.generated
     */
    public String getCountry() {
        return country;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.country
     *
     * @param country the value for moe_book_online_abandon_record.country
     *
     * @mbg.generated
     */
    public void setCountry(String country) {
        this.country = country == null ? null : country.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.lat
     *
     * @return the value of moe_book_online_abandon_record.lat
     *
     * @mbg.generated
     */
    public String getLat() {
        return lat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.lat
     *
     * @param lat the value for moe_book_online_abandon_record.lat
     *
     * @mbg.generated
     */
    public void setLat(String lat) {
        this.lat = lat == null ? null : lat.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.lng
     *
     * @return the value of moe_book_online_abandon_record.lng
     *
     * @mbg.generated
     */
    public String getLng() {
        return lng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.lng
     *
     * @param lng the value for moe_book_online_abandon_record.lng
     *
     * @mbg.generated
     */
    public void setLng(String lng) {
        this.lng = lng == null ? null : lng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.abandon_step
     *
     * @return the value of moe_book_online_abandon_record.abandon_step
     *
     * @mbg.generated
     */
    public String getAbandonStep() {
        return abandonStep;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.abandon_step
     *
     * @param abandonStep the value for moe_book_online_abandon_record.abandon_step
     *
     * @mbg.generated
     */
    public void setAbandonStep(String abandonStep) {
        this.abandonStep = abandonStep == null ? null : abandonStep.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.abandon_time
     *
     * @return the value of moe_book_online_abandon_record.abandon_time
     *
     * @mbg.generated
     */
    public Long getAbandonTime() {
        return abandonTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.abandon_time
     *
     * @param abandonTime the value for moe_book_online_abandon_record.abandon_time
     *
     * @mbg.generated
     */
    public void setAbandonTime(Long abandonTime) {
        this.abandonTime = abandonTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.abandon_status
     *
     * @return the value of moe_book_online_abandon_record.abandon_status
     *
     * @mbg.generated
     */
    public String getAbandonStatus() {
        return abandonStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.abandon_status
     *
     * @param abandonStatus the value for moe_book_online_abandon_record.abandon_status
     *
     * @mbg.generated
     */
    public void setAbandonStatus(String abandonStatus) {
        this.abandonStatus = abandonStatus == null ? null : abandonStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.last_texted_time
     *
     * @return the value of moe_book_online_abandon_record.last_texted_time
     *
     * @mbg.generated
     */
    public Long getLastTextedTime() {
        return lastTextedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.last_texted_time
     *
     * @param lastTextedTime the value for moe_book_online_abandon_record.last_texted_time
     *
     * @mbg.generated
     */
    public void setLastTextedTime(Long lastTextedTime) {
        this.lastTextedTime = lastTextedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.last_emailed_time
     *
     * @return the value of moe_book_online_abandon_record.last_emailed_time
     *
     * @mbg.generated
     */
    public Long getLastEmailedTime() {
        return lastEmailedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.last_emailed_time
     *
     * @param lastEmailedTime the value for moe_book_online_abandon_record.last_emailed_time
     *
     * @mbg.generated
     */
    public void setLastEmailedTime(Long lastEmailedTime) {
        this.lastEmailedTime = lastEmailedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.recovery_type
     *
     * @return the value of moe_book_online_abandon_record.recovery_type
     *
     * @mbg.generated
     */
    public Long getRecoveryType() {
        return recoveryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.recovery_type
     *
     * @param recoveryType the value for moe_book_online_abandon_record.recovery_type
     *
     * @mbg.generated
     */
    public void setRecoveryType(Long recoveryType) {
        this.recoveryType = recoveryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.recovery_time
     *
     * @return the value of moe_book_online_abandon_record.recovery_time
     *
     * @mbg.generated
     */
    public Long getRecoveryTime() {
        return recoveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.recovery_time
     *
     * @param recoveryTime the value for moe_book_online_abandon_record.recovery_time
     *
     * @mbg.generated
     */
    public void setRecoveryTime(Long recoveryTime) {
        this.recoveryTime = recoveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.staff_id
     *
     * @return the value of moe_book_online_abandon_record.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.staff_id
     *
     * @param staffId the value for moe_book_online_abandon_record.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.appointment_id
     *
     * @return the value of moe_book_online_abandon_record.appointment_id
     *
     * @mbg.generated
     */
    public Integer getAppointmentId() {
        return appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.appointment_id
     *
     * @param appointmentId the value for moe_book_online_abandon_record.appointment_id
     *
     * @mbg.generated
     */
    public void setAppointmentId(Integer appointmentId) {
        this.appointmentId = appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.lead_type
     *
     * @return the value of moe_book_online_abandon_record.lead_type
     *
     * @mbg.generated
     */
    public String getLeadType() {
        return leadType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.lead_type
     *
     * @param leadType the value for moe_book_online_abandon_record.lead_type
     *
     * @mbg.generated
     */
    public void setLeadType(String leadType) {
        this.leadType = leadType == null ? null : leadType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.appointment_date
     *
     * @return the value of moe_book_online_abandon_record.appointment_date
     *
     * @mbg.generated
     */
    public String getAppointmentDate() {
        return appointmentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.appointment_date
     *
     * @param appointmentDate the value for moe_book_online_abandon_record.appointment_date
     *
     * @mbg.generated
     */
    public void setAppointmentDate(String appointmentDate) {
        this.appointmentDate = appointmentDate == null ? null : appointmentDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.appointment_start_time
     *
     * @return the value of moe_book_online_abandon_record.appointment_start_time
     *
     * @mbg.generated
     */
    public Integer getAppointmentStartTime() {
        return appointmentStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.appointment_start_time
     *
     * @param appointmentStartTime the value for moe_book_online_abandon_record.appointment_start_time
     *
     * @mbg.generated
     */
    public void setAppointmentStartTime(Integer appointmentStartTime) {
        this.appointmentStartTime = appointmentStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.agreement_info
     *
     * @return the value of moe_book_online_abandon_record.agreement_info
     *
     * @mbg.generated
     */
    public String getAgreementInfo() {
        return agreementInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.agreement_info
     *
     * @param agreementInfo the value for moe_book_online_abandon_record.agreement_info
     *
     * @mbg.generated
     */
    public void setAgreementInfo(String agreementInfo) {
        this.agreementInfo = agreementInfo == null ? null : agreementInfo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.is_deleted
     *
     * @return the value of moe_book_online_abandon_record.is_deleted
     *
     * @mbg.generated
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.is_deleted
     *
     * @param isDeleted the value for moe_book_online_abandon_record.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.delete_type
     *
     * @return the value of moe_book_online_abandon_record.delete_type
     *
     * @mbg.generated
     */
    public Byte getDeleteType() {
        return deleteType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.delete_type
     *
     * @param deleteType the value for moe_book_online_abandon_record.delete_type
     *
     * @mbg.generated
     */
    public void setDeleteType(Byte deleteType) {
        this.deleteType = deleteType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.create_time
     *
     * @return the value of moe_book_online_abandon_record.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.create_time
     *
     * @param createTime the value for moe_book_online_abandon_record.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.update_time
     *
     * @return the value of moe_book_online_abandon_record.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.update_time
     *
     * @param updateTime the value for moe_book_online_abandon_record.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.company_id
     *
     * @return the value of moe_book_online_abandon_record.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.company_id
     *
     * @param companyId the value for moe_book_online_abandon_record.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.use_payment_seg_setting
     *
     * @return the value of moe_book_online_abandon_record.use_payment_seg_setting
     *
     * @mbg.generated
     */
    public Boolean getUsePaymentSegSetting() {
        return usePaymentSegSetting;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.use_payment_seg_setting
     *
     * @param usePaymentSegSetting the value for moe_book_online_abandon_record.use_payment_seg_setting
     *
     * @mbg.generated
     */
    public void setUsePaymentSegSetting(Boolean usePaymentSegSetting) {
        this.usePaymentSegSetting = usePaymentSegSetting;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.payment_seg_setting_rule
     *
     * @return the value of moe_book_online_abandon_record.payment_seg_setting_rule
     *
     * @mbg.generated
     */
    public String getPaymentSegSettingRule() {
        return paymentSegSettingRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.payment_seg_setting_rule
     *
     * @param paymentSegSettingRule the value for moe_book_online_abandon_record.payment_seg_setting_rule
     *
     * @mbg.generated
     */
    public void setPaymentSegSettingRule(String paymentSegSettingRule) {
        this.paymentSegSettingRule = paymentSegSettingRule == null ? null : paymentSegSettingRule.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.is_send_schedule_message
     *
     * @return the value of moe_book_online_abandon_record.is_send_schedule_message
     *
     * @mbg.generated
     */
    public Boolean getIsSendScheduleMessage() {
        return isSendScheduleMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.is_send_schedule_message
     *
     * @param isSendScheduleMessage the value for moe_book_online_abandon_record.is_send_schedule_message
     *
     * @mbg.generated
     */
    public void setIsSendScheduleMessage(Boolean isSendScheduleMessage) {
        this.isSendScheduleMessage = isSendScheduleMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.care_type
     *
     * @return the value of moe_book_online_abandon_record.care_type
     *
     * @mbg.generated
     */
    public String getCareType() {
        return careType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.care_type
     *
     * @param careType the value for moe_book_online_abandon_record.care_type
     *
     * @mbg.generated
     */
    public void setCareType(String careType) {
        this.careType = careType == null ? null : careType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.specific_dates
     *
     * @return the value of moe_book_online_abandon_record.specific_dates
     *
     * @mbg.generated
     */
    public List<String> getSpecificDates() {
        return specificDates;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.specific_dates
     *
     * @param specificDates the value for moe_book_online_abandon_record.specific_dates
     *
     * @mbg.generated
     */
    public void setSpecificDates(List<String> specificDates) {
        this.specificDates = specificDates;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.appointment_end_date
     *
     * @return the value of moe_book_online_abandon_record.appointment_end_date
     *
     * @mbg.generated
     */
    public String getAppointmentEndDate() {
        return appointmentEndDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.appointment_end_date
     *
     * @param appointmentEndDate the value for moe_book_online_abandon_record.appointment_end_date
     *
     * @mbg.generated
     */
    public void setAppointmentEndDate(String appointmentEndDate) {
        this.appointmentEndDate = appointmentEndDate == null ? null : appointmentEndDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.appointment_end_time
     *
     * @return the value of moe_book_online_abandon_record.appointment_end_time
     *
     * @mbg.generated
     */
    public Integer getAppointmentEndTime() {
        return appointmentEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.appointment_end_time
     *
     * @param appointmentEndTime the value for moe_book_online_abandon_record.appointment_end_time
     *
     * @mbg.generated
     */
    public void setAppointmentEndTime(Integer appointmentEndTime) {
        this.appointmentEndTime = appointmentEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.is_notification_sent
     *
     * @return the value of moe_book_online_abandon_record.is_notification_sent
     *
     * @mbg.generated
     */
    public Boolean getIsNotificationSent() {
        return isNotificationSent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.is_notification_sent
     *
     * @param isNotificationSent the value for moe_book_online_abandon_record.is_notification_sent
     *
     * @mbg.generated
     */
    public void setIsNotificationSent(Boolean isNotificationSent) {
        this.isNotificationSent = isNotificationSent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record.additional_note
     *
     * @return the value of moe_book_online_abandon_record.additional_note
     *
     * @mbg.generated
     */
    public String getAdditionalNote() {
        return additionalNote;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record.additional_note
     *
     * @param additionalNote the value for moe_book_online_abandon_record.additional_note
     *
     * @mbg.generated
     */
    public void setAdditionalNote(String additionalNote) {
        this.additionalNote = additionalNote == null ? null : additionalNote.trim();
    }
}
