<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordPetMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="booking_flow_id" jdbcType="VARCHAR" property="bookingFlowId" />
    <result column="pet_id" jdbcType="INTEGER" property="petId" />
    <result column="index_id" jdbcType="INTEGER" property="indexId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="addon_ids" jdbcType="CHAR" property="addonIds" />
    <result column="pet_type_id" jdbcType="INTEGER" property="petTypeId" />
    <result column="avatar_path" jdbcType="VARCHAR" property="avatarPath" />
    <result column="pet_name" jdbcType="VARCHAR" property="petName" />
    <result column="breed" jdbcType="VARCHAR" property="breed" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="hair_length" jdbcType="VARCHAR" property="hairLength" />
    <result column="vaccine_list" jdbcType="CHAR" property="vaccineList" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="gender" jdbcType="TINYINT" property="gender" />
    <result column="fixed" jdbcType="VARCHAR" property="fixed" />
    <result column="behavior" jdbcType="VARCHAR" property="behavior" />
    <result column="vet_name" jdbcType="VARCHAR" property="vetName" />
    <result column="vet_phone_number" jdbcType="VARCHAR" property="vetPhoneNumber" />
    <result column="vet_address" jdbcType="VARCHAR" property="vetAddress" />
    <result column="emergency_contact_name" jdbcType="VARCHAR" property="emergencyContactName" />
    <result column="emergency_contact_phone" jdbcType="VARCHAR" property="emergencyContactPhone" />
    <result column="pet_question_answers" jdbcType="CHAR" property="petQuestionAnswers" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="health_issues" jdbcType="LONGVARCHAR" property="healthIssues" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, booking_flow_id, pet_id, index_id, service_id, addon_ids, pet_type_id,
    avatar_path, pet_name, breed, weight, hair_length, vaccine_list, birthday, gender,
    fixed, behavior, vet_name, vet_phone_number, vet_address, emergency_contact_name,
    emergency_contact_phone, pet_question_answers, create_time, update_time, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    health_issues
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPetExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_abandon_record_pet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPetExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_book_online_abandon_record_pet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_abandon_record_pet
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_abandon_record_pet
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPetExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_abandon_record_pet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_abandon_record_pet (business_id, booking_flow_id, pet_id,
      index_id, service_id, addon_ids,
      pet_type_id, avatar_path, pet_name,
      breed, weight, hair_length,
      vaccine_list, birthday, gender,
      fixed, behavior, vet_name,
      vet_phone_number, vet_address, emergency_contact_name,
      emergency_contact_phone, pet_question_answers,
      create_time, update_time, company_id,
      health_issues)
    values (#{businessId,jdbcType=INTEGER}, #{bookingFlowId,jdbcType=VARCHAR}, #{petId,jdbcType=INTEGER},
      #{indexId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER}, #{addonIds,jdbcType=CHAR},
      #{petTypeId,jdbcType=INTEGER}, #{avatarPath,jdbcType=VARCHAR}, #{petName,jdbcType=VARCHAR},
      #{breed,jdbcType=VARCHAR}, #{weight,jdbcType=VARCHAR}, #{hairLength,jdbcType=VARCHAR},
      #{vaccineList,jdbcType=CHAR}, #{birthday,jdbcType=VARCHAR}, #{gender,jdbcType=TINYINT},
      #{fixed,jdbcType=VARCHAR}, #{behavior,jdbcType=VARCHAR}, #{vetName,jdbcType=VARCHAR},
      #{vetPhoneNumber,jdbcType=VARCHAR}, #{vetAddress,jdbcType=VARCHAR}, #{emergencyContactName,jdbcType=VARCHAR},
      #{emergencyContactPhone,jdbcType=VARCHAR}, #{petQuestionAnswers,jdbcType=CHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT},
      #{healthIssues,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_abandon_record_pet
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="bookingFlowId != null">
        booking_flow_id,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="indexId != null">
        index_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="addonIds != null">
        addon_ids,
      </if>
      <if test="petTypeId != null">
        pet_type_id,
      </if>
      <if test="avatarPath != null">
        avatar_path,
      </if>
      <if test="petName != null">
        pet_name,
      </if>
      <if test="breed != null">
        breed,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="hairLength != null">
        hair_length,
      </if>
      <if test="vaccineList != null">
        vaccine_list,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="fixed != null">
        fixed,
      </if>
      <if test="behavior != null">
        behavior,
      </if>
      <if test="vetName != null">
        vet_name,
      </if>
      <if test="vetPhoneNumber != null">
        vet_phone_number,
      </if>
      <if test="vetAddress != null">
        vet_address,
      </if>
      <if test="emergencyContactName != null">
        emergency_contact_name,
      </if>
      <if test="emergencyContactPhone != null">
        emergency_contact_phone,
      </if>
      <if test="petQuestionAnswers != null">
        pet_question_answers,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="healthIssues != null">
        health_issues,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="bookingFlowId != null">
        #{bookingFlowId,jdbcType=VARCHAR},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=INTEGER},
      </if>
      <if test="indexId != null">
        #{indexId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="addonIds != null">
        #{addonIds,jdbcType=CHAR},
      </if>
      <if test="petTypeId != null">
        #{petTypeId,jdbcType=INTEGER},
      </if>
      <if test="avatarPath != null">
        #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="petName != null">
        #{petName,jdbcType=VARCHAR},
      </if>
      <if test="breed != null">
        #{breed,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=VARCHAR},
      </if>
      <if test="hairLength != null">
        #{hairLength,jdbcType=VARCHAR},
      </if>
      <if test="vaccineList != null">
        #{vaccineList,jdbcType=CHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=TINYINT},
      </if>
      <if test="fixed != null">
        #{fixed,jdbcType=VARCHAR},
      </if>
      <if test="behavior != null">
        #{behavior,jdbcType=VARCHAR},
      </if>
      <if test="vetName != null">
        #{vetName,jdbcType=VARCHAR},
      </if>
      <if test="vetPhoneNumber != null">
        #{vetPhoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="vetAddress != null">
        #{vetAddress,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContactName != null">
        #{emergencyContactName,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContactPhone != null">
        #{emergencyContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="petQuestionAnswers != null">
        #{petQuestionAnswers,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="healthIssues != null">
        #{healthIssues,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPetExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_book_online_abandon_record_pet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record_pet
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.bookingFlowId != null">
        booking_flow_id = #{record.bookingFlowId,jdbcType=VARCHAR},
      </if>
      <if test="record.petId != null">
        pet_id = #{record.petId,jdbcType=INTEGER},
      </if>
      <if test="record.indexId != null">
        index_id = #{record.indexId,jdbcType=INTEGER},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=INTEGER},
      </if>
      <if test="record.addonIds != null">
        addon_ids = #{record.addonIds,jdbcType=CHAR},
      </if>
      <if test="record.petTypeId != null">
        pet_type_id = #{record.petTypeId,jdbcType=INTEGER},
      </if>
      <if test="record.avatarPath != null">
        avatar_path = #{record.avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="record.petName != null">
        pet_name = #{record.petName,jdbcType=VARCHAR},
      </if>
      <if test="record.breed != null">
        breed = #{record.breed,jdbcType=VARCHAR},
      </if>
      <if test="record.weight != null">
        weight = #{record.weight,jdbcType=VARCHAR},
      </if>
      <if test="record.hairLength != null">
        hair_length = #{record.hairLength,jdbcType=VARCHAR},
      </if>
      <if test="record.vaccineList != null">
        vaccine_list = #{record.vaccineList,jdbcType=CHAR},
      </if>
      <if test="record.birthday != null">
        birthday = #{record.birthday,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        gender = #{record.gender,jdbcType=TINYINT},
      </if>
      <if test="record.fixed != null">
        fixed = #{record.fixed,jdbcType=VARCHAR},
      </if>
      <if test="record.behavior != null">
        behavior = #{record.behavior,jdbcType=VARCHAR},
      </if>
      <if test="record.vetName != null">
        vet_name = #{record.vetName,jdbcType=VARCHAR},
      </if>
      <if test="record.vetPhoneNumber != null">
        vet_phone_number = #{record.vetPhoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.vetAddress != null">
        vet_address = #{record.vetAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.emergencyContactName != null">
        emergency_contact_name = #{record.emergencyContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.emergencyContactPhone != null">
        emergency_contact_phone = #{record.emergencyContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.petQuestionAnswers != null">
        pet_question_answers = #{record.petQuestionAnswers,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.healthIssues != null">
        health_issues = #{record.healthIssues,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record_pet
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      booking_flow_id = #{record.bookingFlowId,jdbcType=VARCHAR},
      pet_id = #{record.petId,jdbcType=INTEGER},
      index_id = #{record.indexId,jdbcType=INTEGER},
      service_id = #{record.serviceId,jdbcType=INTEGER},
      addon_ids = #{record.addonIds,jdbcType=CHAR},
      pet_type_id = #{record.petTypeId,jdbcType=INTEGER},
      avatar_path = #{record.avatarPath,jdbcType=VARCHAR},
      pet_name = #{record.petName,jdbcType=VARCHAR},
      breed = #{record.breed,jdbcType=VARCHAR},
      weight = #{record.weight,jdbcType=VARCHAR},
      hair_length = #{record.hairLength,jdbcType=VARCHAR},
      vaccine_list = #{record.vaccineList,jdbcType=CHAR},
      birthday = #{record.birthday,jdbcType=VARCHAR},
      gender = #{record.gender,jdbcType=TINYINT},
      fixed = #{record.fixed,jdbcType=VARCHAR},
      behavior = #{record.behavior,jdbcType=VARCHAR},
      vet_name = #{record.vetName,jdbcType=VARCHAR},
      vet_phone_number = #{record.vetPhoneNumber,jdbcType=VARCHAR},
      vet_address = #{record.vetAddress,jdbcType=VARCHAR},
      emergency_contact_name = #{record.emergencyContactName,jdbcType=VARCHAR},
      emergency_contact_phone = #{record.emergencyContactPhone,jdbcType=VARCHAR},
      pet_question_answers = #{record.petQuestionAnswers,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=BIGINT},
      health_issues = #{record.healthIssues,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record_pet
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      booking_flow_id = #{record.bookingFlowId,jdbcType=VARCHAR},
      pet_id = #{record.petId,jdbcType=INTEGER},
      index_id = #{record.indexId,jdbcType=INTEGER},
      service_id = #{record.serviceId,jdbcType=INTEGER},
      addon_ids = #{record.addonIds,jdbcType=CHAR},
      pet_type_id = #{record.petTypeId,jdbcType=INTEGER},
      avatar_path = #{record.avatarPath,jdbcType=VARCHAR},
      pet_name = #{record.petName,jdbcType=VARCHAR},
      breed = #{record.breed,jdbcType=VARCHAR},
      weight = #{record.weight,jdbcType=VARCHAR},
      hair_length = #{record.hairLength,jdbcType=VARCHAR},
      vaccine_list = #{record.vaccineList,jdbcType=CHAR},
      birthday = #{record.birthday,jdbcType=VARCHAR},
      gender = #{record.gender,jdbcType=TINYINT},
      fixed = #{record.fixed,jdbcType=VARCHAR},
      behavior = #{record.behavior,jdbcType=VARCHAR},
      vet_name = #{record.vetName,jdbcType=VARCHAR},
      vet_phone_number = #{record.vetPhoneNumber,jdbcType=VARCHAR},
      vet_address = #{record.vetAddress,jdbcType=VARCHAR},
      emergency_contact_name = #{record.emergencyContactName,jdbcType=VARCHAR},
      emergency_contact_phone = #{record.emergencyContactPhone,jdbcType=VARCHAR},
      pet_question_answers = #{record.petQuestionAnswers,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record_pet
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="bookingFlowId != null">
        booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=INTEGER},
      </if>
      <if test="indexId != null">
        index_id = #{indexId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="addonIds != null">
        addon_ids = #{addonIds,jdbcType=CHAR},
      </if>
      <if test="petTypeId != null">
        pet_type_id = #{petTypeId,jdbcType=INTEGER},
      </if>
      <if test="avatarPath != null">
        avatar_path = #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="petName != null">
        pet_name = #{petName,jdbcType=VARCHAR},
      </if>
      <if test="breed != null">
        breed = #{breed,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=VARCHAR},
      </if>
      <if test="hairLength != null">
        hair_length = #{hairLength,jdbcType=VARCHAR},
      </if>
      <if test="vaccineList != null">
        vaccine_list = #{vaccineList,jdbcType=CHAR},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=TINYINT},
      </if>
      <if test="fixed != null">
        fixed = #{fixed,jdbcType=VARCHAR},
      </if>
      <if test="behavior != null">
        behavior = #{behavior,jdbcType=VARCHAR},
      </if>
      <if test="vetName != null">
        vet_name = #{vetName,jdbcType=VARCHAR},
      </if>
      <if test="vetPhoneNumber != null">
        vet_phone_number = #{vetPhoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="vetAddress != null">
        vet_address = #{vetAddress,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContactName != null">
        emergency_contact_name = #{emergencyContactName,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContactPhone != null">
        emergency_contact_phone = #{emergencyContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="petQuestionAnswers != null">
        pet_question_answers = #{petQuestionAnswers,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="healthIssues != null">
        health_issues = #{healthIssues,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record_pet
    set business_id = #{businessId,jdbcType=INTEGER},
      booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR},
      pet_id = #{petId,jdbcType=INTEGER},
      index_id = #{indexId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      addon_ids = #{addonIds,jdbcType=CHAR},
      pet_type_id = #{petTypeId,jdbcType=INTEGER},
      avatar_path = #{avatarPath,jdbcType=VARCHAR},
      pet_name = #{petName,jdbcType=VARCHAR},
      breed = #{breed,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=VARCHAR},
      hair_length = #{hairLength,jdbcType=VARCHAR},
      vaccine_list = #{vaccineList,jdbcType=CHAR},
      birthday = #{birthday,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=TINYINT},
      fixed = #{fixed,jdbcType=VARCHAR},
      behavior = #{behavior,jdbcType=VARCHAR},
      vet_name = #{vetName,jdbcType=VARCHAR},
      vet_phone_number = #{vetPhoneNumber,jdbcType=VARCHAR},
      vet_address = #{vetAddress,jdbcType=VARCHAR},
      emergency_contact_name = #{emergencyContactName,jdbcType=VARCHAR},
      emergency_contact_phone = #{emergencyContactPhone,jdbcType=VARCHAR},
      pet_question_answers = #{petQuestionAnswers,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      health_issues = #{healthIssues,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record_pet
    set business_id = #{businessId,jdbcType=INTEGER},
      booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR},
      pet_id = #{petId,jdbcType=INTEGER},
      index_id = #{indexId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      addon_ids = #{addonIds,jdbcType=CHAR},
      pet_type_id = #{petTypeId,jdbcType=INTEGER},
      avatar_path = #{avatarPath,jdbcType=VARCHAR},
      pet_name = #{petName,jdbcType=VARCHAR},
      breed = #{breed,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=VARCHAR},
      hair_length = #{hairLength,jdbcType=VARCHAR},
      vaccine_list = #{vaccineList,jdbcType=CHAR},
      birthday = #{birthday,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=TINYINT},
      fixed = #{fixed,jdbcType=VARCHAR},
      behavior = #{behavior,jdbcType=VARCHAR},
      vet_name = #{vetName,jdbcType=VARCHAR},
      vet_phone_number = #{vetPhoneNumber,jdbcType=VARCHAR},
      vet_address = #{vetAddress,jdbcType=VARCHAR},
      emergency_contact_name = #{emergencyContactName,jdbcType=VARCHAR},
      emergency_contact_phone = #{emergencyContactPhone,jdbcType=VARCHAR},
      pet_question_answers = #{petQuestionAnswers,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsertRecordPet" keyProperty="id" useGeneratedKeys="true">
    insert into moe_book_online_abandon_record_pet
        (business_id, booking_flow_id, pet_id,
         index_id, service_id, addon_ids,
         pet_type_id, avatar_path, pet_name,
         breed, weight, hair_length,
         vaccine_list, birthday, gender,
         fixed, behavior, vet_name,
         vet_phone_number, vet_address, emergency_contact_name,
         emergency_contact_phone, pet_question_answers, health_issues,
         company_id
         )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessId,jdbcType=INTEGER}, #{item.bookingFlowId,jdbcType=VARCHAR}, #{item.petId,jdbcType=INTEGER},
      #{item.indexId,jdbcType=INTEGER}, #{item.serviceId,jdbcType=INTEGER}, #{item.addonIds,jdbcType=CHAR},
      #{item.petTypeId,jdbcType=INTEGER}, #{item.avatarPath,jdbcType=VARCHAR}, #{item.petName,jdbcType=VARCHAR},
      #{item.breed,jdbcType=VARCHAR}, #{item.weight,jdbcType=VARCHAR}, #{item.hairLength,jdbcType=VARCHAR},
      #{item.vaccineList,jdbcType=CHAR}, #{item.birthday,jdbcType=VARCHAR}, #{item.gender,jdbcType=TINYINT},
      #{item.fixed,jdbcType=VARCHAR}, #{item.behavior,jdbcType=VARCHAR}, #{item.vetName,jdbcType=VARCHAR},
      #{item.vetPhoneNumber,jdbcType=VARCHAR}, #{item.vetAddress,jdbcType=VARCHAR}, #{item.emergencyContactName,jdbcType=VARCHAR},
      #{item.emergencyContactPhone,jdbcType=VARCHAR}, #{item.petQuestionAnswers,jdbcType=CHAR}, #{item.healthIssues,jdbcType=LONGVARCHAR},
      #{item.companyId, jdbcType=BIGINT}
      )
    </foreach>
  </insert>

  <select id="listRecordPetByBookingFlowId" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from moe_book_online_abandon_record_pet
    where business_id = #{businessId,jdbcType=INTEGER}
      and booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR}
  </select>

  <select id="selectBookingFlowIdsByKeyword" resultType="java.lang.String">
    select
        rp.booking_flow_id
    from
        moe_book_online_abandon_record_pet rp
    join moe_book_online_abandon_record r on rp.business_id = r.business_id and rp.booking_flow_id = r.booking_flow_id
    where
        rp.business_id = #{businessId}
        and r.is_deleted = 0
        and rp.pet_name like concat('%', #{keyword}, '%')
  </select>

  <delete id="deleteRecordPetByBookingFlowId">
    delete from moe_book_online_abandon_record_pet
    where business_id = #{businessId,jdbcType=INTEGER}
      and booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR}
  </delete>

  <update id="batchUpdateRecordPet">
    <foreach collection="list" item="item" separator=";">
      update moe_book_online_abandon_record_pet
      set pet_id = #{item.petId,jdbcType=INTEGER},
      index_id = #{item.indexId,jdbcType=INTEGER},
      service_id = #{item.serviceId,jdbcType=INTEGER},
      addon_ids = #{item.addonIds,jdbcType=CHAR},
      pet_type_id = #{item.petTypeId,jdbcType=INTEGER},
      avatar_path = #{item.avatarPath,jdbcType=VARCHAR},
      pet_name = #{item.petName,jdbcType=VARCHAR},
      breed = #{item.breed,jdbcType=VARCHAR},
      weight = #{item.weight,jdbcType=VARCHAR},
      hair_length = #{item.hairLength,jdbcType=VARCHAR},
      vaccine_list = #{item.vaccineList,jdbcType=CHAR},
      birthday = #{item.birthday,jdbcType=VARCHAR},
      gender = #{item.gender,jdbcType=TINYINT},
      fixed = #{item.fixed,jdbcType=VARCHAR},
      behavior = #{item.behavior,jdbcType=VARCHAR},
      vet_name = #{item.vetName,jdbcType=VARCHAR},
      vet_phone_number = #{item.vetPhoneNumber,jdbcType=VARCHAR},
      vet_address = #{item.vetAddress,jdbcType=VARCHAR},
      emergency_contact_name = #{item.emergencyContactName,jdbcType=VARCHAR},
      emergency_contact_phone = #{item.emergencyContactPhone,jdbcType=VARCHAR},
      pet_question_answers = #{item.petQuestionAnswers,jdbcType=CHAR},
      health_issues = #{item.healthIssues,jdbcType=LONGVARCHAR}
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="resetPetIdByBookingFlowId">
    UPDATE
      moe_book_online_abandon_record_pet
    SET
      pet_id = null
    WHERE booking_flow_id = #{bookingFlowId}
      AND business_id = #{businessId}
      <if test="petIds != null and petIds.size() > 0">
        AND pet_id IN
        <foreach collection="petIds" item="petId" open="(" close=")" separator=",">
          #{petId}
        </foreach>
      </if>
  </update>
</mapper>
