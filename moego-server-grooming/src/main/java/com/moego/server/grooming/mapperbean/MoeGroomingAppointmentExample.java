package com.moego.server.grooming.mapperbean;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class MoeGroomingAppointmentExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public MoeGroomingAppointmentExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> statusBeforeCheckinCriteria;

        protected List<Criterion> statusBeforeReadyCriteria;

        protected List<Criterion> statusBeforeFinishCriteria;

        protected List<Criterion> waitListStatusCriteria;

        protected List<Criterion> allCriteria;

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
            statusBeforeCheckinCriteria = new ArrayList<>();
            statusBeforeReadyCriteria = new ArrayList<>();
            statusBeforeFinishCriteria = new ArrayList<>();
            waitListStatusCriteria = new ArrayList<>();
        }

        public List<Criterion> getStatusBeforeCheckinCriteria() {
            return statusBeforeCheckinCriteria;
        }

        protected void addStatusBeforeCheckinCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            statusBeforeCheckinCriteria.add(new Criterion(
                    condition, value, "com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler"));
            allCriteria = null;
        }

        protected void addStatusBeforeCheckinCriterion(
                String condition, AppointmentStatusEnum value1, AppointmentStatusEnum value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            statusBeforeCheckinCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler"));
            allCriteria = null;
        }

        public List<Criterion> getStatusBeforeReadyCriteria() {
            return statusBeforeReadyCriteria;
        }

        protected void addStatusBeforeReadyCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            statusBeforeReadyCriteria.add(new Criterion(
                    condition, value, "com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler"));
            allCriteria = null;
        }

        protected void addStatusBeforeReadyCriterion(
                String condition, AppointmentStatusEnum value1, AppointmentStatusEnum value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            statusBeforeReadyCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler"));
            allCriteria = null;
        }

        public List<Criterion> getStatusBeforeFinishCriteria() {
            return statusBeforeFinishCriteria;
        }

        protected void addStatusBeforeFinishCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            statusBeforeFinishCriteria.add(new Criterion(
                    condition, value, "com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler"));
            allCriteria = null;
        }

        protected void addStatusBeforeFinishCriterion(
                String condition, AppointmentStatusEnum value1, AppointmentStatusEnum value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            statusBeforeFinishCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler"));
            allCriteria = null;
        }

        public List<Criterion> getWaitListStatusCriteria() {
            return waitListStatusCriteria;
        }

        protected void addWaitListStatusCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            waitListStatusCriteria.add(new Criterion(
                    condition, value, "com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler"));
            allCriteria = null;
        }

        protected void addWaitListStatusCriterion(
                String condition, WaitListStatusEnum value1, WaitListStatusEnum value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            waitListStatusCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler"));
            allCriteria = null;
        }

        public boolean isValid() {
            return criteria.size() > 0
                    || statusBeforeCheckinCriteria.size() > 0
                    || statusBeforeReadyCriteria.size() > 0
                    || statusBeforeFinishCriteria.size() > 0
                    || waitListStatusCriteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            if (allCriteria == null) {
                allCriteria = new ArrayList<>();
                allCriteria.addAll(criteria);
                allCriteria.addAll(statusBeforeCheckinCriteria);
                allCriteria.addAll(statusBeforeReadyCriteria);
                allCriteria.addAll(statusBeforeFinishCriteria);
                allCriteria.addAll(waitListStatusCriteria);
            }
            return allCriteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
            allCriteria = null;
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateIsNull() {
            addCriterion("appointment_date is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateIsNotNull() {
            addCriterion("appointment_date is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateEqualTo(String value) {
            addCriterion("appointment_date =", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateNotEqualTo(String value) {
            addCriterion("appointment_date <>", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateGreaterThan(String value) {
            addCriterion("appointment_date >", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_date >=", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateLessThan(String value) {
            addCriterion("appointment_date <", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateLessThanOrEqualTo(String value) {
            addCriterion("appointment_date <=", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateLike(String value) {
            addCriterion("appointment_date like", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateNotLike(String value) {
            addCriterion("appointment_date not like", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateIn(List<String> values) {
            addCriterion("appointment_date in", values, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateNotIn(List<String> values) {
            addCriterion("appointment_date not in", values, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateBetween(String value1, String value2) {
            addCriterion("appointment_date between", value1, value2, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateNotBetween(String value1, String value2) {
            addCriterion("appointment_date not between", value1, value2, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeIsNull() {
            addCriterion("appointment_start_time is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeIsNotNull() {
            addCriterion("appointment_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeEqualTo(Integer value) {
            addCriterion("appointment_start_time =", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeNotEqualTo(Integer value) {
            addCriterion("appointment_start_time <>", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeGreaterThan(Integer value) {
            addCriterion("appointment_start_time >", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("appointment_start_time >=", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeLessThan(Integer value) {
            addCriterion("appointment_start_time <", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeLessThanOrEqualTo(Integer value) {
            addCriterion("appointment_start_time <=", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeIn(List<Integer> values) {
            addCriterion("appointment_start_time in", values, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeNotIn(List<Integer> values) {
            addCriterion("appointment_start_time not in", values, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeBetween(Integer value1, Integer value2) {
            addCriterion("appointment_start_time between", value1, value2, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("appointment_start_time not between", value1, value2, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeIsNull() {
            addCriterion("appointment_end_time is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeIsNotNull() {
            addCriterion("appointment_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeEqualTo(Integer value) {
            addCriterion("appointment_end_time =", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeNotEqualTo(Integer value) {
            addCriterion("appointment_end_time <>", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeGreaterThan(Integer value) {
            addCriterion("appointment_end_time >", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("appointment_end_time >=", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeLessThan(Integer value) {
            addCriterion("appointment_end_time <", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeLessThanOrEqualTo(Integer value) {
            addCriterion("appointment_end_time <=", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeIn(List<Integer> values) {
            addCriterion("appointment_end_time in", values, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeNotIn(List<Integer> values) {
            addCriterion("appointment_end_time not in", values, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeBetween(Integer value1, Integer value2) {
            addCriterion("appointment_end_time between", value1, value2, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("appointment_end_time not between", value1, value2, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListIsNull() {
            addCriterion("is_waiting_list is null");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListIsNotNull() {
            addCriterion("is_waiting_list is not null");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListEqualTo(Byte value) {
            addCriterion("is_waiting_list =", value, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListNotEqualTo(Byte value) {
            addCriterion("is_waiting_list <>", value, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListGreaterThan(Byte value) {
            addCriterion("is_waiting_list >", value, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_waiting_list >=", value, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListLessThan(Byte value) {
            addCriterion("is_waiting_list <", value, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListLessThanOrEqualTo(Byte value) {
            addCriterion("is_waiting_list <=", value, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListIn(List<Byte> values) {
            addCriterion("is_waiting_list in", values, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListNotIn(List<Byte> values) {
            addCriterion("is_waiting_list not in", values, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListBetween(Byte value1, Byte value2) {
            addCriterion("is_waiting_list between", value1, value2, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andIsWaitingListNotBetween(Byte value1, Byte value2) {
            addCriterion("is_waiting_list not between", value1, value2, "isWaitingList");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByIsNull() {
            addCriterion("move_waiting_by is null");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByIsNotNull() {
            addCriterion("move_waiting_by is not null");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByEqualTo(Integer value) {
            addCriterion("move_waiting_by =", value, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByNotEqualTo(Integer value) {
            addCriterion("move_waiting_by <>", value, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByGreaterThan(Integer value) {
            addCriterion("move_waiting_by >", value, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByGreaterThanOrEqualTo(Integer value) {
            addCriterion("move_waiting_by >=", value, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByLessThan(Integer value) {
            addCriterion("move_waiting_by <", value, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByLessThanOrEqualTo(Integer value) {
            addCriterion("move_waiting_by <=", value, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByIn(List<Integer> values) {
            addCriterion("move_waiting_by in", values, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByNotIn(List<Integer> values) {
            addCriterion("move_waiting_by not in", values, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByBetween(Integer value1, Integer value2) {
            addCriterion("move_waiting_by between", value1, value2, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andMoveWaitingByNotBetween(Integer value1, Integer value2) {
            addCriterion("move_waiting_by not between", value1, value2, "moveWaitingBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeIsNull() {
            addCriterion("confirmed_time is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeIsNotNull() {
            addCriterion("confirmed_time is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeEqualTo(Long value) {
            addCriterion("confirmed_time =", value, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeNotEqualTo(Long value) {
            addCriterion("confirmed_time <>", value, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeGreaterThan(Long value) {
            addCriterion("confirmed_time >", value, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("confirmed_time >=", value, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeLessThan(Long value) {
            addCriterion("confirmed_time <", value, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeLessThanOrEqualTo(Long value) {
            addCriterion("confirmed_time <=", value, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeIn(List<Long> values) {
            addCriterion("confirmed_time in", values, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeNotIn(List<Long> values) {
            addCriterion("confirmed_time not in", values, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeBetween(Long value1, Long value2) {
            addCriterion("confirmed_time between", value1, value2, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedTimeNotBetween(Long value1, Long value2) {
            addCriterion("confirmed_time not between", value1, value2, "confirmedTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeIsNull() {
            addCriterion("check_in_time is null");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeIsNotNull() {
            addCriterion("check_in_time is not null");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeEqualTo(Long value) {
            addCriterion("check_in_time =", value, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeNotEqualTo(Long value) {
            addCriterion("check_in_time <>", value, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeGreaterThan(Long value) {
            addCriterion("check_in_time >", value, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("check_in_time >=", value, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeLessThan(Long value) {
            addCriterion("check_in_time <", value, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeLessThanOrEqualTo(Long value) {
            addCriterion("check_in_time <=", value, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeIn(List<Long> values) {
            addCriterion("check_in_time in", values, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeNotIn(List<Long> values) {
            addCriterion("check_in_time not in", values, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeBetween(Long value1, Long value2) {
            addCriterion("check_in_time between", value1, value2, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckInTimeNotBetween(Long value1, Long value2) {
            addCriterion("check_in_time not between", value1, value2, "checkInTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeIsNull() {
            addCriterion("check_out_time is null");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeIsNotNull() {
            addCriterion("check_out_time is not null");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeEqualTo(Long value) {
            addCriterion("check_out_time =", value, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeNotEqualTo(Long value) {
            addCriterion("check_out_time <>", value, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeGreaterThan(Long value) {
            addCriterion("check_out_time >", value, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("check_out_time >=", value, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeLessThan(Long value) {
            addCriterion("check_out_time <", value, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeLessThanOrEqualTo(Long value) {
            addCriterion("check_out_time <=", value, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeIn(List<Long> values) {
            addCriterion("check_out_time in", values, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeNotIn(List<Long> values) {
            addCriterion("check_out_time not in", values, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeBetween(Long value1, Long value2) {
            addCriterion("check_out_time between", value1, value2, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCheckOutTimeNotBetween(Long value1, Long value2) {
            addCriterion("check_out_time not between", value1, value2, "checkOutTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeIsNull() {
            addCriterion("canceled_time is null");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeIsNotNull() {
            addCriterion("canceled_time is not null");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeEqualTo(Long value) {
            addCriterion("canceled_time =", value, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeNotEqualTo(Long value) {
            addCriterion("canceled_time <>", value, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeGreaterThan(Long value) {
            addCriterion("canceled_time >", value, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("canceled_time >=", value, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeLessThan(Long value) {
            addCriterion("canceled_time <", value, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeLessThanOrEqualTo(Long value) {
            addCriterion("canceled_time <=", value, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeIn(List<Long> values) {
            addCriterion("canceled_time in", values, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeNotIn(List<Long> values) {
            addCriterion("canceled_time not in", values, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeBetween(Long value1, Long value2) {
            addCriterion("canceled_time between", value1, value2, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andCanceledTimeNotBetween(Long value1, Long value2) {
            addCriterion("canceled_time not between", value1, value2, "canceledTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsBlockIsNull() {
            addCriterion("is_block is null");
            return (Criteria) this;
        }

        public Criteria andIsBlockIsNotNull() {
            addCriterion("is_block is not null");
            return (Criteria) this;
        }

        public Criteria andIsBlockEqualTo(Integer value) {
            addCriterion("is_block =", value, "isBlock");
            return (Criteria) this;
        }

        public Criteria andIsBlockNotEqualTo(Integer value) {
            addCriterion("is_block <>", value, "isBlock");
            return (Criteria) this;
        }

        public Criteria andIsBlockGreaterThan(Integer value) {
            addCriterion("is_block >", value, "isBlock");
            return (Criteria) this;
        }

        public Criteria andIsBlockGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_block >=", value, "isBlock");
            return (Criteria) this;
        }

        public Criteria andIsBlockLessThan(Integer value) {
            addCriterion("is_block <", value, "isBlock");
            return (Criteria) this;
        }

        public Criteria andIsBlockLessThanOrEqualTo(Integer value) {
            addCriterion("is_block <=", value, "isBlock");
            return (Criteria) this;
        }

        public Criteria andIsBlockIn(List<Integer> values) {
            addCriterion("is_block in", values, "isBlock");
            return (Criteria) this;
        }

        public Criteria andIsBlockNotIn(List<Integer> values) {
            addCriterion("is_block not in", values, "isBlock");
            return (Criteria) this;
        }

        public Criteria andIsBlockBetween(Integer value1, Integer value2) {
            addCriterion("is_block between", value1, value2, "isBlock");
            return (Criteria) this;
        }

        public Criteria andIsBlockNotBetween(Integer value1, Integer value2) {
            addCriterion("is_block not between", value1, value2, "isBlock");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusIsNull() {
            addCriterion("book_online_status is null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusIsNotNull() {
            addCriterion("book_online_status is not null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusEqualTo(Byte value) {
            addCriterion("book_online_status =", value, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusNotEqualTo(Byte value) {
            addCriterion("book_online_status <>", value, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusGreaterThan(Byte value) {
            addCriterion("book_online_status >", value, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("book_online_status >=", value, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusLessThan(Byte value) {
            addCriterion("book_online_status <", value, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusLessThanOrEqualTo(Byte value) {
            addCriterion("book_online_status <=", value, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusIn(List<Byte> values) {
            addCriterion("book_online_status in", values, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusNotIn(List<Byte> values) {
            addCriterion("book_online_status not in", values, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusBetween(Byte value1, Byte value2) {
            addCriterion("book_online_status between", value1, value2, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andBookOnlineStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("book_online_status not between", value1, value2, "bookOnlineStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdIsNull() {
            addCriterion("customer_address_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdIsNotNull() {
            addCriterion("customer_address_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdEqualTo(Integer value) {
            addCriterion("customer_address_id =", value, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdNotEqualTo(Integer value) {
            addCriterion("customer_address_id <>", value, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdGreaterThan(Integer value) {
            addCriterion("customer_address_id >", value, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_address_id >=", value, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdLessThan(Integer value) {
            addCriterion("customer_address_id <", value, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_address_id <=", value, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdIn(List<Integer> values) {
            addCriterion("customer_address_id in", values, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdNotIn(List<Integer> values) {
            addCriterion("customer_address_id not in", values, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_address_id between", value1, value2, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andCustomerAddressIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_address_id not between", value1, value2, "customerAddressId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdIsNull() {
            addCriterion("repeat_id is null");
            return (Criteria) this;
        }

        public Criteria andRepeatIdIsNotNull() {
            addCriterion("repeat_id is not null");
            return (Criteria) this;
        }

        public Criteria andRepeatIdEqualTo(Integer value) {
            addCriterion("repeat_id =", value, "repeatId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdNotEqualTo(Integer value) {
            addCriterion("repeat_id <>", value, "repeatId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdGreaterThan(Integer value) {
            addCriterion("repeat_id >", value, "repeatId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("repeat_id >=", value, "repeatId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdLessThan(Integer value) {
            addCriterion("repeat_id <", value, "repeatId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdLessThanOrEqualTo(Integer value) {
            addCriterion("repeat_id <=", value, "repeatId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdIn(List<Integer> values) {
            addCriterion("repeat_id in", values, "repeatId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdNotIn(List<Integer> values) {
            addCriterion("repeat_id not in", values, "repeatId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdBetween(Integer value1, Integer value2) {
            addCriterion("repeat_id between", value1, value2, "repeatId");
            return (Criteria) this;
        }

        public Criteria andRepeatIdNotBetween(Integer value1, Integer value2) {
            addCriterion("repeat_id not between", value1, value2, "repeatId");
            return (Criteria) this;
        }

        public Criteria andIsPaidIsNull() {
            addCriterion("is_paid is null");
            return (Criteria) this;
        }

        public Criteria andIsPaidIsNotNull() {
            addCriterion("is_paid is not null");
            return (Criteria) this;
        }

        public Criteria andIsPaidEqualTo(Byte value) {
            addCriterion("is_paid =", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidNotEqualTo(Byte value) {
            addCriterion("is_paid <>", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidGreaterThan(Byte value) {
            addCriterion("is_paid >", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_paid >=", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidLessThan(Byte value) {
            addCriterion("is_paid <", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidLessThanOrEqualTo(Byte value) {
            addCriterion("is_paid <=", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidIn(List<Byte> values) {
            addCriterion("is_paid in", values, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidNotIn(List<Byte> values) {
            addCriterion("is_paid not in", values, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidBetween(Byte value1, Byte value2) {
            addCriterion("is_paid between", value1, value2, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidNotBetween(Byte value1, Byte value2) {
            addCriterion("is_paid not between", value1, value2, "isPaid");
            return (Criteria) this;
        }

        public Criteria andColorCodeIsNull() {
            addCriterion("color_code is null");
            return (Criteria) this;
        }

        public Criteria andColorCodeIsNotNull() {
            addCriterion("color_code is not null");
            return (Criteria) this;
        }

        public Criteria andColorCodeEqualTo(String value) {
            addCriterion("color_code =", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotEqualTo(String value) {
            addCriterion("color_code <>", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeGreaterThan(String value) {
            addCriterion("color_code >", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("color_code >=", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeLessThan(String value) {
            addCriterion("color_code <", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeLessThanOrEqualTo(String value) {
            addCriterion("color_code <=", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeLike(String value) {
            addCriterion("color_code like", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotLike(String value) {
            addCriterion("color_code not like", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeIn(List<String> values) {
            addCriterion("color_code in", values, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotIn(List<String> values) {
            addCriterion("color_code not in", values, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeBetween(String value1, String value2) {
            addCriterion("color_code between", value1, value2, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotBetween(String value1, String value2) {
            addCriterion("color_code not between", value1, value2, "colorCode");
            return (Criteria) this;
        }

        public Criteria andNoShowIsNull() {
            addCriterion("no_show is null");
            return (Criteria) this;
        }

        public Criteria andNoShowIsNotNull() {
            addCriterion("no_show is not null");
            return (Criteria) this;
        }

        public Criteria andNoShowEqualTo(Byte value) {
            addCriterion("no_show =", value, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowNotEqualTo(Byte value) {
            addCriterion("no_show <>", value, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowGreaterThan(Byte value) {
            addCriterion("no_show >", value, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowGreaterThanOrEqualTo(Byte value) {
            addCriterion("no_show >=", value, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowLessThan(Byte value) {
            addCriterion("no_show <", value, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowLessThanOrEqualTo(Byte value) {
            addCriterion("no_show <=", value, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowIn(List<Byte> values) {
            addCriterion("no_show in", values, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowNotIn(List<Byte> values) {
            addCriterion("no_show not in", values, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowBetween(Byte value1, Byte value2) {
            addCriterion("no_show between", value1, value2, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowNotBetween(Byte value1, Byte value2) {
            addCriterion("no_show not between", value1, value2, "noShow");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeIsNull() {
            addCriterion("no_show_fee is null");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeIsNotNull() {
            addCriterion("no_show_fee is not null");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeEqualTo(BigDecimal value) {
            addCriterion("no_show_fee =", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeNotEqualTo(BigDecimal value) {
            addCriterion("no_show_fee <>", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeGreaterThan(BigDecimal value) {
            addCriterion("no_show_fee >", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_show_fee >=", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeLessThan(BigDecimal value) {
            addCriterion("no_show_fee <", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_show_fee <=", value, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeIn(List<BigDecimal> values) {
            addCriterion("no_show_fee in", values, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeNotIn(List<BigDecimal> values) {
            addCriterion("no_show_fee not in", values, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_show_fee between", value1, value2, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andNoShowFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_show_fee not between", value1, value2, "noShowFee");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationIsNull() {
            addCriterion("is_pust_notification is null");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationIsNotNull() {
            addCriterion("is_pust_notification is not null");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationEqualTo(Byte value) {
            addCriterion("is_pust_notification =", value, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationNotEqualTo(Byte value) {
            addCriterion("is_pust_notification <>", value, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationGreaterThan(Byte value) {
            addCriterion("is_pust_notification >", value, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_pust_notification >=", value, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationLessThan(Byte value) {
            addCriterion("is_pust_notification <", value, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationLessThanOrEqualTo(Byte value) {
            addCriterion("is_pust_notification <=", value, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationIn(List<Byte> values) {
            addCriterion("is_pust_notification in", values, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationNotIn(List<Byte> values) {
            addCriterion("is_pust_notification not in", values, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationBetween(Byte value1, Byte value2) {
            addCriterion("is_pust_notification between", value1, value2, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andIsPustNotificationNotBetween(Byte value1, Byte value2) {
            addCriterion("is_pust_notification not between", value1, value2, "isPustNotification");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeIsNull() {
            addCriterion("cancel_by_type is null");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeIsNotNull() {
            addCriterion("cancel_by_type is not null");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeEqualTo(Byte value) {
            addCriterion("cancel_by_type =", value, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeNotEqualTo(Byte value) {
            addCriterion("cancel_by_type <>", value, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeGreaterThan(Byte value) {
            addCriterion("cancel_by_type >", value, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("cancel_by_type >=", value, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeLessThan(Byte value) {
            addCriterion("cancel_by_type <", value, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeLessThanOrEqualTo(Byte value) {
            addCriterion("cancel_by_type <=", value, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeIn(List<Byte> values) {
            addCriterion("cancel_by_type in", values, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeNotIn(List<Byte> values) {
            addCriterion("cancel_by_type not in", values, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeBetween(Byte value1, Byte value2) {
            addCriterion("cancel_by_type between", value1, value2, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("cancel_by_type not between", value1, value2, "cancelByType");
            return (Criteria) this;
        }

        public Criteria andCancelByIsNull() {
            addCriterion("cancel_by is null");
            return (Criteria) this;
        }

        public Criteria andCancelByIsNotNull() {
            addCriterion("cancel_by is not null");
            return (Criteria) this;
        }

        public Criteria andCancelByEqualTo(Integer value) {
            addCriterion("cancel_by =", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotEqualTo(Integer value) {
            addCriterion("cancel_by <>", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByGreaterThan(Integer value) {
            addCriterion("cancel_by >", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByGreaterThanOrEqualTo(Integer value) {
            addCriterion("cancel_by >=", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByLessThan(Integer value) {
            addCriterion("cancel_by <", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByLessThanOrEqualTo(Integer value) {
            addCriterion("cancel_by <=", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByIn(List<Integer> values) {
            addCriterion("cancel_by in", values, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotIn(List<Integer> values) {
            addCriterion("cancel_by not in", values, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByBetween(Integer value1, Integer value2) {
            addCriterion("cancel_by between", value1, value2, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotBetween(Integer value1, Integer value2) {
            addCriterion("cancel_by not between", value1, value2, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeIsNull() {
            addCriterion("confirm_by_type is null");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeIsNotNull() {
            addCriterion("confirm_by_type is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeEqualTo(Byte value) {
            addCriterion("confirm_by_type =", value, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeNotEqualTo(Byte value) {
            addCriterion("confirm_by_type <>", value, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeGreaterThan(Byte value) {
            addCriterion("confirm_by_type >", value, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("confirm_by_type >=", value, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeLessThan(Byte value) {
            addCriterion("confirm_by_type <", value, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeLessThanOrEqualTo(Byte value) {
            addCriterion("confirm_by_type <=", value, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeIn(List<Byte> values) {
            addCriterion("confirm_by_type in", values, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeNotIn(List<Byte> values) {
            addCriterion("confirm_by_type not in", values, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeBetween(Byte value1, Byte value2) {
            addCriterion("confirm_by_type between", value1, value2, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("confirm_by_type not between", value1, value2, "confirmByType");
            return (Criteria) this;
        }

        public Criteria andConfirmByIsNull() {
            addCriterion("confirm_by is null");
            return (Criteria) this;
        }

        public Criteria andConfirmByIsNotNull() {
            addCriterion("confirm_by is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmByEqualTo(Integer value) {
            addCriterion("confirm_by =", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByNotEqualTo(Integer value) {
            addCriterion("confirm_by <>", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByGreaterThan(Integer value) {
            addCriterion("confirm_by >", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByGreaterThanOrEqualTo(Integer value) {
            addCriterion("confirm_by >=", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByLessThan(Integer value) {
            addCriterion("confirm_by <", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByLessThanOrEqualTo(Integer value) {
            addCriterion("confirm_by <=", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByIn(List<Integer> values) {
            addCriterion("confirm_by in", values, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByNotIn(List<Integer> values) {
            addCriterion("confirm_by not in", values, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByBetween(Integer value1, Integer value2) {
            addCriterion("confirm_by between", value1, value2, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByNotBetween(Integer value1, Integer value2) {
            addCriterion("confirm_by not between", value1, value2, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdIsNull() {
            addCriterion("created_by_id is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdIsNotNull() {
            addCriterion("created_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdEqualTo(Integer value) {
            addCriterion("created_by_id =", value, "createdById");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdNotEqualTo(Integer value) {
            addCriterion("created_by_id <>", value, "createdById");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdGreaterThan(Integer value) {
            addCriterion("created_by_id >", value, "createdById");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("created_by_id >=", value, "createdById");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdLessThan(Integer value) {
            addCriterion("created_by_id <", value, "createdById");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdLessThanOrEqualTo(Integer value) {
            addCriterion("created_by_id <=", value, "createdById");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdIn(List<Integer> values) {
            addCriterion("created_by_id in", values, "createdById");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdNotIn(List<Integer> values) {
            addCriterion("created_by_id not in", values, "createdById");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdBetween(Integer value1, Integer value2) {
            addCriterion("created_by_id between", value1, value2, "createdById");
            return (Criteria) this;
        }

        public Criteria andCreatedByIdNotBetween(Integer value1, Integer value2) {
            addCriterion("created_by_id not between", value1, value2, "createdById");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaIsNull() {
            addCriterion("out_of_area is null");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaIsNotNull() {
            addCriterion("out_of_area is not null");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaEqualTo(Byte value) {
            addCriterion("out_of_area =", value, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaNotEqualTo(Byte value) {
            addCriterion("out_of_area <>", value, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaGreaterThan(Byte value) {
            addCriterion("out_of_area >", value, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaGreaterThanOrEqualTo(Byte value) {
            addCriterion("out_of_area >=", value, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaLessThan(Byte value) {
            addCriterion("out_of_area <", value, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaLessThanOrEqualTo(Byte value) {
            addCriterion("out_of_area <=", value, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaIn(List<Byte> values) {
            addCriterion("out_of_area in", values, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaNotIn(List<Byte> values) {
            addCriterion("out_of_area not in", values, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaBetween(Byte value1, Byte value2) {
            addCriterion("out_of_area between", value1, value2, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andOutOfAreaNotBetween(Byte value1, Byte value2) {
            addCriterion("out_of_area not between", value1, value2, "outOfArea");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateIsNull() {
            addCriterion("is_deprecate is null");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateIsNotNull() {
            addCriterion("is_deprecate is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateEqualTo(Integer value) {
            addCriterion("is_deprecate =", value, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateNotEqualTo(Integer value) {
            addCriterion("is_deprecate <>", value, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateGreaterThan(Integer value) {
            addCriterion("is_deprecate >", value, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deprecate >=", value, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateLessThan(Integer value) {
            addCriterion("is_deprecate <", value, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateLessThanOrEqualTo(Integer value) {
            addCriterion("is_deprecate <=", value, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateIn(List<Integer> values) {
            addCriterion("is_deprecate in", values, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateNotIn(List<Integer> values) {
            addCriterion("is_deprecate not in", values, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateBetween(Integer value1, Integer value2) {
            addCriterion("is_deprecate between", value1, value2, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andIsDeprecateNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deprecate not between", value1, value2, "isDeprecate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateIsNull() {
            addCriterion("old_appointment_date is null");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateIsNotNull() {
            addCriterion("old_appointment_date is not null");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateEqualTo(String value) {
            addCriterion("old_appointment_date =", value, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateNotEqualTo(String value) {
            addCriterion("old_appointment_date <>", value, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateGreaterThan(String value) {
            addCriterion("old_appointment_date >", value, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateGreaterThanOrEqualTo(String value) {
            addCriterion("old_appointment_date >=", value, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateLessThan(String value) {
            addCriterion("old_appointment_date <", value, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateLessThanOrEqualTo(String value) {
            addCriterion("old_appointment_date <=", value, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateLike(String value) {
            addCriterion("old_appointment_date like", value, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateNotLike(String value) {
            addCriterion("old_appointment_date not like", value, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateIn(List<String> values) {
            addCriterion("old_appointment_date in", values, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateNotIn(List<String> values) {
            addCriterion("old_appointment_date not in", values, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateBetween(String value1, String value2) {
            addCriterion("old_appointment_date between", value1, value2, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentDateNotBetween(String value1, String value2) {
            addCriterion("old_appointment_date not between", value1, value2, "oldAppointmentDate");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeIsNull() {
            addCriterion("old_appointment_start_time is null");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeIsNotNull() {
            addCriterion("old_appointment_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeEqualTo(Integer value) {
            addCriterion("old_appointment_start_time =", value, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeNotEqualTo(Integer value) {
            addCriterion("old_appointment_start_time <>", value, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeGreaterThan(Integer value) {
            addCriterion("old_appointment_start_time >", value, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("old_appointment_start_time >=", value, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeLessThan(Integer value) {
            addCriterion("old_appointment_start_time <", value, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeLessThanOrEqualTo(Integer value) {
            addCriterion("old_appointment_start_time <=", value, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeIn(List<Integer> values) {
            addCriterion("old_appointment_start_time in", values, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeNotIn(List<Integer> values) {
            addCriterion("old_appointment_start_time not in", values, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeBetween(Integer value1, Integer value2) {
            addCriterion("old_appointment_start_time between", value1, value2, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentStartTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("old_appointment_start_time not between", value1, value2, "oldAppointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeIsNull() {
            addCriterion("old_appointment_end_time is null");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeIsNotNull() {
            addCriterion("old_appointment_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeEqualTo(Integer value) {
            addCriterion("old_appointment_end_time =", value, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeNotEqualTo(Integer value) {
            addCriterion("old_appointment_end_time <>", value, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeGreaterThan(Integer value) {
            addCriterion("old_appointment_end_time >", value, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("old_appointment_end_time >=", value, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeLessThan(Integer value) {
            addCriterion("old_appointment_end_time <", value, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeLessThanOrEqualTo(Integer value) {
            addCriterion("old_appointment_end_time <=", value, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeIn(List<Integer> values) {
            addCriterion("old_appointment_end_time in", values, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeNotIn(List<Integer> values) {
            addCriterion("old_appointment_end_time not in", values, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeBetween(Integer value1, Integer value2) {
            addCriterion("old_appointment_end_time between", value1, value2, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldAppointmentEndTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("old_appointment_end_time not between", value1, value2, "oldAppointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andOldApptIdIsNull() {
            addCriterion("old_appt_id is null");
            return (Criteria) this;
        }

        public Criteria andOldApptIdIsNotNull() {
            addCriterion("old_appt_id is not null");
            return (Criteria) this;
        }

        public Criteria andOldApptIdEqualTo(Integer value) {
            addCriterion("old_appt_id =", value, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andOldApptIdNotEqualTo(Integer value) {
            addCriterion("old_appt_id <>", value, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andOldApptIdGreaterThan(Integer value) {
            addCriterion("old_appt_id >", value, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andOldApptIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("old_appt_id >=", value, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andOldApptIdLessThan(Integer value) {
            addCriterion("old_appt_id <", value, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andOldApptIdLessThanOrEqualTo(Integer value) {
            addCriterion("old_appt_id <=", value, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andOldApptIdIn(List<Integer> values) {
            addCriterion("old_appt_id in", values, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andOldApptIdNotIn(List<Integer> values) {
            addCriterion("old_appt_id not in", values, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andOldApptIdBetween(Integer value1, Integer value2) {
            addCriterion("old_appt_id between", value1, value2, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andOldApptIdNotBetween(Integer value1, Integer value2) {
            addCriterion("old_appt_id not between", value1, value2, "oldApptId");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeIsNull() {
            addCriterion("schedule_type is null");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeIsNotNull() {
            addCriterion("schedule_type is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeEqualTo(Byte value) {
            addCriterion("schedule_type =", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotEqualTo(Byte value) {
            addCriterion("schedule_type <>", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeGreaterThan(Byte value) {
            addCriterion("schedule_type >", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("schedule_type >=", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeLessThan(Byte value) {
            addCriterion("schedule_type <", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeLessThanOrEqualTo(Byte value) {
            addCriterion("schedule_type <=", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeIn(List<Byte> values) {
            addCriterion("schedule_type in", values, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotIn(List<Byte> values) {
            addCriterion("schedule_type not in", values, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeBetween(Byte value1, Byte value2) {
            addCriterion("schedule_type between", value1, value2, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("schedule_type not between", value1, value2, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformIsNull() {
            addCriterion("source_platform is null");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformIsNotNull() {
            addCriterion("source_platform is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformEqualTo(String value) {
            addCriterion("source_platform =", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformNotEqualTo(String value) {
            addCriterion("source_platform <>", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformGreaterThan(String value) {
            addCriterion("source_platform >", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformGreaterThanOrEqualTo(String value) {
            addCriterion("source_platform >=", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformLessThan(String value) {
            addCriterion("source_platform <", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformLessThanOrEqualTo(String value) {
            addCriterion("source_platform <=", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformLike(String value) {
            addCriterion("source_platform like", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformNotLike(String value) {
            addCriterion("source_platform not like", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformIn(List<String> values) {
            addCriterion("source_platform in", values, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformNotIn(List<String> values) {
            addCriterion("source_platform not in", values, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformBetween(String value1, String value2) {
            addCriterion("source_platform between", value1, value2, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformNotBetween(String value1, String value2) {
            addCriterion("source_platform not between", value1, value2, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andReadyTimeIsNull() {
            addCriterion("ready_time is null");
            return (Criteria) this;
        }

        public Criteria andReadyTimeIsNotNull() {
            addCriterion("ready_time is not null");
            return (Criteria) this;
        }

        public Criteria andReadyTimeEqualTo(Long value) {
            addCriterion("ready_time =", value, "readyTime");
            return (Criteria) this;
        }

        public Criteria andReadyTimeNotEqualTo(Long value) {
            addCriterion("ready_time <>", value, "readyTime");
            return (Criteria) this;
        }

        public Criteria andReadyTimeGreaterThan(Long value) {
            addCriterion("ready_time >", value, "readyTime");
            return (Criteria) this;
        }

        public Criteria andReadyTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ready_time >=", value, "readyTime");
            return (Criteria) this;
        }

        public Criteria andReadyTimeLessThan(Long value) {
            addCriterion("ready_time <", value, "readyTime");
            return (Criteria) this;
        }

        public Criteria andReadyTimeLessThanOrEqualTo(Long value) {
            addCriterion("ready_time <=", value, "readyTime");
            return (Criteria) this;
        }

        public Criteria andReadyTimeIn(List<Long> values) {
            addCriterion("ready_time in", values, "readyTime");
            return (Criteria) this;
        }

        public Criteria andReadyTimeNotIn(List<Long> values) {
            addCriterion("ready_time not in", values, "readyTime");
            return (Criteria) this;
        }

        public Criteria andReadyTimeBetween(Long value1, Long value2) {
            addCriterion("ready_time between", value1, value2, "readyTime");
            return (Criteria) this;
        }

        public Criteria andReadyTimeNotBetween(Long value1, Long value2) {
            addCriterion("ready_time not between", value1, value2, "readyTime");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusIsNull() {
            addCriterion("pickup_notification_send_status is null");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusIsNotNull() {
            addCriterion("pickup_notification_send_status is not null");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusEqualTo(Integer value) {
            addCriterion("pickup_notification_send_status =", value, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusNotEqualTo(Integer value) {
            addCriterion("pickup_notification_send_status <>", value, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusGreaterThan(Integer value) {
            addCriterion("pickup_notification_send_status >", value, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("pickup_notification_send_status >=", value, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusLessThan(Integer value) {
            addCriterion("pickup_notification_send_status <", value, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusLessThanOrEqualTo(Integer value) {
            addCriterion("pickup_notification_send_status <=", value, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusIn(List<Integer> values) {
            addCriterion("pickup_notification_send_status in", values, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusNotIn(List<Integer> values) {
            addCriterion("pickup_notification_send_status not in", values, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusBetween(Integer value1, Integer value2) {
            addCriterion("pickup_notification_send_status between", value1, value2, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationSendStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("pickup_notification_send_status not between", value1, value2, "pickupNotificationSendStatus");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonIsNull() {
            addCriterion("pickup_notification_failed_reason is null");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonIsNotNull() {
            addCriterion("pickup_notification_failed_reason is not null");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonEqualTo(String value) {
            addCriterion("pickup_notification_failed_reason =", value, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonNotEqualTo(String value) {
            addCriterion("pickup_notification_failed_reason <>", value, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonGreaterThan(String value) {
            addCriterion("pickup_notification_failed_reason >", value, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonGreaterThanOrEqualTo(String value) {
            addCriterion("pickup_notification_failed_reason >=", value, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonLessThan(String value) {
            addCriterion("pickup_notification_failed_reason <", value, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonLessThanOrEqualTo(String value) {
            addCriterion("pickup_notification_failed_reason <=", value, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonLike(String value) {
            addCriterion("pickup_notification_failed_reason like", value, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonNotLike(String value) {
            addCriterion("pickup_notification_failed_reason not like", value, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonIn(List<String> values) {
            addCriterion("pickup_notification_failed_reason in", values, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonNotIn(List<String> values) {
            addCriterion("pickup_notification_failed_reason not in", values, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonBetween(String value1, String value2) {
            addCriterion("pickup_notification_failed_reason between", value1, value2, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andPickupNotificationFailedReasonNotBetween(String value1, String value2) {
            addCriterion(
                    "pickup_notification_failed_reason not between", value1, value2, "pickupNotificationFailedReason");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinIsNull() {
            addCriterion("status_before_checkin is null");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinIsNotNull() {
            addCriterion("status_before_checkin is not null");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeCheckinCriterion("status_before_checkin =", value, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinNotEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeCheckinCriterion("status_before_checkin <>", value, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinGreaterThan(AppointmentStatusEnum value) {
            addStatusBeforeCheckinCriterion("status_before_checkin >", value, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinGreaterThanOrEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeCheckinCriterion("status_before_checkin >=", value, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinLessThan(AppointmentStatusEnum value) {
            addStatusBeforeCheckinCriterion("status_before_checkin <", value, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinLessThanOrEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeCheckinCriterion("status_before_checkin <=", value, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinIn(List<AppointmentStatusEnum> values) {
            addStatusBeforeCheckinCriterion("status_before_checkin in", values, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinNotIn(List<AppointmentStatusEnum> values) {
            addStatusBeforeCheckinCriterion("status_before_checkin not in", values, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinBetween(AppointmentStatusEnum value1, AppointmentStatusEnum value2) {
            addStatusBeforeCheckinCriterion("status_before_checkin between", value1, value2, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeCheckinNotBetween(AppointmentStatusEnum value1, AppointmentStatusEnum value2) {
            addStatusBeforeCheckinCriterion("status_before_checkin not between", value1, value2, "statusBeforeCheckin");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyIsNull() {
            addCriterion("status_before_ready is null");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyIsNotNull() {
            addCriterion("status_before_ready is not null");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeReadyCriterion("status_before_ready =", value, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyNotEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeReadyCriterion("status_before_ready <>", value, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyGreaterThan(AppointmentStatusEnum value) {
            addStatusBeforeReadyCriterion("status_before_ready >", value, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyGreaterThanOrEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeReadyCriterion("status_before_ready >=", value, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyLessThan(AppointmentStatusEnum value) {
            addStatusBeforeReadyCriterion("status_before_ready <", value, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyLessThanOrEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeReadyCriterion("status_before_ready <=", value, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyIn(List<AppointmentStatusEnum> values) {
            addStatusBeforeReadyCriterion("status_before_ready in", values, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyNotIn(List<AppointmentStatusEnum> values) {
            addStatusBeforeReadyCriterion("status_before_ready not in", values, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyBetween(AppointmentStatusEnum value1, AppointmentStatusEnum value2) {
            addStatusBeforeReadyCriterion("status_before_ready between", value1, value2, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeReadyNotBetween(AppointmentStatusEnum value1, AppointmentStatusEnum value2) {
            addStatusBeforeReadyCriterion("status_before_ready not between", value1, value2, "statusBeforeReady");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishIsNull() {
            addCriterion("status_before_finish is null");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishIsNotNull() {
            addCriterion("status_before_finish is not null");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeFinishCriterion("status_before_finish =", value, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishNotEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeFinishCriterion("status_before_finish <>", value, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishGreaterThan(AppointmentStatusEnum value) {
            addStatusBeforeFinishCriterion("status_before_finish >", value, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishGreaterThanOrEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeFinishCriterion("status_before_finish >=", value, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishLessThan(AppointmentStatusEnum value) {
            addStatusBeforeFinishCriterion("status_before_finish <", value, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishLessThanOrEqualTo(AppointmentStatusEnum value) {
            addStatusBeforeFinishCriterion("status_before_finish <=", value, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishIn(List<AppointmentStatusEnum> values) {
            addStatusBeforeFinishCriterion("status_before_finish in", values, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishNotIn(List<AppointmentStatusEnum> values) {
            addStatusBeforeFinishCriterion("status_before_finish not in", values, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishBetween(AppointmentStatusEnum value1, AppointmentStatusEnum value2) {
            addStatusBeforeFinishCriterion("status_before_finish between", value1, value2, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andStatusBeforeFinishNotBetween(AppointmentStatusEnum value1, AppointmentStatusEnum value2) {
            addStatusBeforeFinishCriterion("status_before_finish not between", value1, value2, "statusBeforeFinish");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeIsNull() {
            addCriterion("no_start_time is null");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeIsNotNull() {
            addCriterion("no_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeEqualTo(Boolean value) {
            addCriterion("no_start_time =", value, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeNotEqualTo(Boolean value) {
            addCriterion("no_start_time <>", value, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeGreaterThan(Boolean value) {
            addCriterion("no_start_time >", value, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("no_start_time >=", value, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeLessThan(Boolean value) {
            addCriterion("no_start_time <", value, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeLessThanOrEqualTo(Boolean value) {
            addCriterion("no_start_time <=", value, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeIn(List<Boolean> values) {
            addCriterion("no_start_time in", values, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeNotIn(List<Boolean> values) {
            addCriterion("no_start_time not in", values, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeBetween(Boolean value1, Boolean value2) {
            addCriterion("no_start_time between", value1, value2, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andNoStartTimeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("no_start_time not between", value1, value2, "noStartTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdIsNull() {
            addCriterion("updated_by_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdIsNotNull() {
            addCriterion("updated_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdEqualTo(Long value) {
            addCriterion("updated_by_id =", value, "updatedById");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdNotEqualTo(Long value) {
            addCriterion("updated_by_id <>", value, "updatedById");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdGreaterThan(Long value) {
            addCriterion("updated_by_id >", value, "updatedById");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by_id >=", value, "updatedById");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdLessThan(Long value) {
            addCriterion("updated_by_id <", value, "updatedById");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdLessThanOrEqualTo(Long value) {
            addCriterion("updated_by_id <=", value, "updatedById");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdIn(List<Long> values) {
            addCriterion("updated_by_id in", values, "updatedById");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdNotIn(List<Long> values) {
            addCriterion("updated_by_id not in", values, "updatedById");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdBetween(Long value1, Long value2) {
            addCriterion("updated_by_id between", value1, value2, "updatedById");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIdNotBetween(Long value1, Long value2) {
            addCriterion("updated_by_id not between", value1, value2, "updatedById");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptIsNull() {
            addCriterion("is_auto_accept is null");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptIsNotNull() {
            addCriterion("is_auto_accept is not null");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptEqualTo(Boolean value) {
            addCriterion("is_auto_accept =", value, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptNotEqualTo(Boolean value) {
            addCriterion("is_auto_accept <>", value, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptGreaterThan(Boolean value) {
            addCriterion("is_auto_accept >", value, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_auto_accept >=", value, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptLessThan(Boolean value) {
            addCriterion("is_auto_accept <", value, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptLessThanOrEqualTo(Boolean value) {
            addCriterion("is_auto_accept <=", value, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptIn(List<Boolean> values) {
            addCriterion("is_auto_accept in", values, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptNotIn(List<Boolean> values) {
            addCriterion("is_auto_accept not in", values, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptBetween(Boolean value1, Boolean value2) {
            addCriterion("is_auto_accept between", value1, value2, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andIsAutoAcceptNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_auto_accept not between", value1, value2, "isAutoAccept");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusIsNull() {
            addCriterion("wait_list_status is null");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusIsNotNull() {
            addCriterion("wait_list_status is not null");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusEqualTo(WaitListStatusEnum value) {
            addWaitListStatusCriterion("wait_list_status =", value, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusNotEqualTo(WaitListStatusEnum value) {
            addWaitListStatusCriterion("wait_list_status <>", value, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusGreaterThan(WaitListStatusEnum value) {
            addWaitListStatusCriterion("wait_list_status >", value, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusGreaterThanOrEqualTo(WaitListStatusEnum value) {
            addWaitListStatusCriterion("wait_list_status >=", value, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusLessThan(WaitListStatusEnum value) {
            addWaitListStatusCriterion("wait_list_status <", value, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusLessThanOrEqualTo(WaitListStatusEnum value) {
            addWaitListStatusCriterion("wait_list_status <=", value, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusIn(List<WaitListStatusEnum> values) {
            addWaitListStatusCriterion("wait_list_status in", values, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusNotIn(List<WaitListStatusEnum> values) {
            addWaitListStatusCriterion("wait_list_status not in", values, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusBetween(WaitListStatusEnum value1, WaitListStatusEnum value2) {
            addWaitListStatusCriterion("wait_list_status between", value1, value2, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andWaitListStatusNotBetween(WaitListStatusEnum value1, WaitListStatusEnum value2) {
            addWaitListStatusCriterion("wait_list_status not between", value1, value2, "waitListStatus");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateIsNull() {
            addCriterion("appointment_end_date is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateIsNotNull() {
            addCriterion("appointment_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateEqualTo(String value) {
            addCriterion("appointment_end_date =", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateNotEqualTo(String value) {
            addCriterion("appointment_end_date <>", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateGreaterThan(String value) {
            addCriterion("appointment_end_date >", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_end_date >=", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateLessThan(String value) {
            addCriterion("appointment_end_date <", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateLessThanOrEqualTo(String value) {
            addCriterion("appointment_end_date <=", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateLike(String value) {
            addCriterion("appointment_end_date like", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateNotLike(String value) {
            addCriterion("appointment_end_date not like", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateIn(List<String> values) {
            addCriterion("appointment_end_date in", values, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateNotIn(List<String> values) {
            addCriterion("appointment_end_date not in", values, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateBetween(String value1, String value2) {
            addCriterion("appointment_end_date between", value1, value2, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateNotBetween(String value1, String value2) {
            addCriterion("appointment_end_date not between", value1, value2, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeIsNull() {
            addCriterion("service_type_include is null");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeIsNotNull() {
            addCriterion("service_type_include is not null");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeEqualTo(Integer value) {
            addCriterion("service_type_include =", value, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeNotEqualTo(Integer value) {
            addCriterion("service_type_include <>", value, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeGreaterThan(Integer value) {
            addCriterion("service_type_include >", value, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_type_include >=", value, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeLessThan(Integer value) {
            addCriterion("service_type_include <", value, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeLessThanOrEqualTo(Integer value) {
            addCriterion("service_type_include <=", value, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeIn(List<Integer> values) {
            addCriterion("service_type_include in", values, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeNotIn(List<Integer> values) {
            addCriterion("service_type_include not in", values, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeBetween(Integer value1, Integer value2) {
            addCriterion("service_type_include between", value1, value2, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIncludeNotBetween(Integer value1, Integer value2) {
            addCriterion("service_type_include not between", value1, value2, "serviceTypeInclude");
            return (Criteria) this;
        }

        public Criteria andNoShowByIsNull() {
            addCriterion("no_show_by is null");
            return (Criteria) this;
        }

        public Criteria andNoShowByIsNotNull() {
            addCriterion("no_show_by is not null");
            return (Criteria) this;
        }

        public Criteria andNoShowByEqualTo(Long value) {
            addCriterion("no_show_by =", value, "noShowBy");
            return (Criteria) this;
        }

        public Criteria andNoShowByNotEqualTo(Long value) {
            addCriterion("no_show_by <>", value, "noShowBy");
            return (Criteria) this;
        }

        public Criteria andNoShowByGreaterThan(Long value) {
            addCriterion("no_show_by >", value, "noShowBy");
            return (Criteria) this;
        }

        public Criteria andNoShowByGreaterThanOrEqualTo(Long value) {
            addCriterion("no_show_by >=", value, "noShowBy");
            return (Criteria) this;
        }

        public Criteria andNoShowByLessThan(Long value) {
            addCriterion("no_show_by <", value, "noShowBy");
            return (Criteria) this;
        }

        public Criteria andNoShowByLessThanOrEqualTo(Long value) {
            addCriterion("no_show_by <=", value, "noShowBy");
            return (Criteria) this;
        }

        public Criteria andNoShowByIn(List<Long> values) {
            addCriterion("no_show_by in", values, "noShowBy");
            return (Criteria) this;
        }

        public Criteria andNoShowByNotIn(List<Long> values) {
            addCriterion("no_show_by not in", values, "noShowBy");
            return (Criteria) this;
        }

        public Criteria andNoShowByBetween(Long value1, Long value2) {
            addCriterion("no_show_by between", value1, value2, "noShowBy");
            return (Criteria) this;
        }

        public Criteria andNoShowByNotBetween(Long value1, Long value2) {
            addCriterion("no_show_by not between", value1, value2, "noShowBy");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
