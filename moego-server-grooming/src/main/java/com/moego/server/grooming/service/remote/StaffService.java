package com.moego.server.grooming.service.remote;

import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class StaffService {

    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;

    public Map<Long, StaffModel> getStaffMap(List<Integer> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Map.of();
        }
        var res = staffService.queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                .addAllStaffIds(
                        staffIds.stream().map(Integer::longValue).distinct().toList())
                .build());
        return res.getStaffsList().stream().collect(Collectors.toMap(StaffModel::getId, Function.identity()));
    }
}
