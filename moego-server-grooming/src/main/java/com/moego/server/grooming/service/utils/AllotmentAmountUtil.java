package com.moego.server.grooming.service.utils;

import com.moego.common.utils.AmountUtils;
import com.moego.server.grooming.service.dto.report.StaffRevenueDetail;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

public class AllotmentAmountUtil {

    private final List<StaffRevenueDetail> employees;
    private final Map<Integer, Function<BigDecimal, BigDecimal>> staffPriceRateMap;

    public AllotmentAmountUtil(
            List<StaffRevenueDetail> employees, Map<Integer, Function<BigDecimal, BigDecimal>> staffPriceRateMap) {
        this.employees = employees;
        this.staffPriceRateMap = staffPriceRateMap;
    }

    public void allocate(
            BigDecimal totalPrice,
            Function<StaffRevenueDetail, BigDecimal> getFunction,
            BiConsumer<StaffRevenueDetail, BigDecimal> setFunction) {
        BigDecimal remainAmount = totalPrice;
        for (int sequence = 0; sequence < employees.size(); sequence++) {
            StaffRevenueDetail employee = employees.get(sequence);
            if (sequence == employees.size() - 1) {
                setFunction.accept(employee, AmountUtils.sum(getFunction.apply(employee), remainAmount));
            } else {
                BigDecimal currentAmount =
                        staffPriceRateMap.get(employee.getStaffId()).apply(totalPrice);
                setFunction.accept(employee, AmountUtils.sum(getFunction.apply(employee), currentAmount));
                remainAmount = remainAmount.subtract(currentAmount);
            }
        }
    }
}
