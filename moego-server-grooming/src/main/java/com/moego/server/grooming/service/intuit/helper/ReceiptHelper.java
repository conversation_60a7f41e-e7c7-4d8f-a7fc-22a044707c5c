package com.moego.server.grooming.service.intuit.helper;

import com.intuit.ipp.data.SalesReceipt;
import com.intuit.ipp.data.SalesTransaction;
import com.moego.common.enums.QuickBooksConst;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.enums.IntuitAccountTypeEnum;
import com.moego.server.payment.dto.PayDetailDTO;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
public class ReceiptHelper {

    public static SalesTransaction buildReceipt(String id) {
        var receipt = new SalesReceipt();
        receipt.setId(id);
        return receipt;
    }
    /**
     * 根据 sales receipt type 判断出需要取什么值
     * @param salesReceiptType 类型
     * @param detail payment detail
     * @return 金额
     */
    public static BigDecimal getPaymentDetailAmount(String salesReceiptType, PayDetailDTO detail) {
        return switch (salesReceiptType) {
            case QuickBooksConst.QB_SERVICE_RECEIVED_TAX -> detail.getTax();
            case QuickBooksConst.QB_SERVICE_RECEIVED_GROSS_SALE -> detail.getGrossSales()
                    .add(detail.getDiscount());
            case QuickBooksConst.QB_SERVICE_RECEIVED_NET_SALES,
                    QuickBooksConst.QB_SERVICE_RECEIVED_RECEIVED_SALE -> detail.getGrossSales();
            case QuickBooksConst.QB_SERVICE_RECEIVED_TIPS -> detail.getTips();
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Unexpected value: " + salesReceiptType);
        };
    }

    /**
     * 根据 sale receipt type 转换 IntuitAccountType
     * @param salesReceiptType sales receipt type
     * @return IntuitAccountTypeEnum
     */
    public static IntuitAccountTypeEnum getAccountType(String salesReceiptType) {
        // 这里是创建 QB 的 Sales Receipt,并通过 dataService set 到 QB 里
        return switch (salesReceiptType) {
            case QuickBooksConst.QB_SERVICE_RECEIVED_TAX -> IntuitAccountTypeEnum
                    .ACCOUNT_TYPE_MOEGO_RECEIVED_TAX_PAYABLE;
            case QuickBooksConst.QB_SERVICE_RECEIVED_GROSS_SALE -> IntuitAccountTypeEnum
                    .ACCOUNT_TYPE_MOEGO_RECEIVED_GROSS_SALE;
            case QuickBooksConst.QB_SERVICE_RECEIVED_TIPS -> IntuitAccountTypeEnum.ACCOUNT_TYPE_MOEGO_RECEIVED_TIPS;
            case QuickBooksConst.QB_SERVICE_RECEIVED_NET_SALES,
                    QuickBooksConst.QB_SERVICE_RECEIVED_RECEIVED_SALE -> IntuitAccountTypeEnum
                    .ACCOUNT_TYPE_MOEGO_RECEIVED_NET_SALE;
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Unexpected value: " + salesReceiptType);
        };
    }
}
