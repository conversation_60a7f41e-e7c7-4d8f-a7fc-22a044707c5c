package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_service_operation
 */
public class MoeGroomingServiceOperation {
    /**
     * Database Column Remarks:
     *   record id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   grooming id/ticket id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     * Database Column Remarks:
     *   grooming service id/pet detail id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.grooming_service_id
     *
     * @mbg.generated
     */
    private Integer groomingServiceId;

    /**
     * Database Column Remarks:
     *   pet id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.pet_id
     *
     * @mbg.generated
     */
    private Integer petId;

    /**
     * Database Column Remarks:
     *   staff id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   operation name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.operation_name
     *
     * @mbg.generated
     */
    private String operationName;

    /**
     * Database Column Remarks:
     *   start time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.start_time
     *
     * @mbg.generated
     */
    private Integer startTime;

    /**
     * Database Column Remarks:
     *   duration
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.duration
     *
     * @mbg.generated
     */
    private Integer duration;

    /**
     * Database Column Remarks:
     *   comment
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.comment
     *
     * @mbg.generated
     */
    private String comment;

    /**
     * Database Column Remarks:
     *   price
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.price
     *
     * @mbg.generated
     */
    private BigDecimal price;

    /**
     * Database Column Remarks:
     *   price ratio
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.price_ratio
     *
     * @mbg.generated
     */
    private BigDecimal priceRatio;

    /**
     * Database Column Remarks:
     *   status
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.status
     *
     * @mbg.generated
     */
    private Boolean status;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_operation.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.id
     *
     * @return the value of moe_grooming_service_operation.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.id
     *
     * @param id the value for moe_grooming_service_operation.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.business_id
     *
     * @return the value of moe_grooming_service_operation.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.business_id
     *
     * @param businessId the value for moe_grooming_service_operation.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.grooming_id
     *
     * @return the value of moe_grooming_service_operation.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.grooming_id
     *
     * @param groomingId the value for moe_grooming_service_operation.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.grooming_service_id
     *
     * @return the value of moe_grooming_service_operation.grooming_service_id
     *
     * @mbg.generated
     */
    public Integer getGroomingServiceId() {
        return groomingServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.grooming_service_id
     *
     * @param groomingServiceId the value for moe_grooming_service_operation.grooming_service_id
     *
     * @mbg.generated
     */
    public void setGroomingServiceId(Integer groomingServiceId) {
        this.groomingServiceId = groomingServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.pet_id
     *
     * @return the value of moe_grooming_service_operation.pet_id
     *
     * @mbg.generated
     */
    public Integer getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.pet_id
     *
     * @param petId the value for moe_grooming_service_operation.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Integer petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.staff_id
     *
     * @return the value of moe_grooming_service_operation.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.staff_id
     *
     * @param staffId the value for moe_grooming_service_operation.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.operation_name
     *
     * @return the value of moe_grooming_service_operation.operation_name
     *
     * @mbg.generated
     */
    public String getOperationName() {
        return operationName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.operation_name
     *
     * @param operationName the value for moe_grooming_service_operation.operation_name
     *
     * @mbg.generated
     */
    public void setOperationName(String operationName) {
        this.operationName = operationName == null ? null : operationName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.start_time
     *
     * @return the value of moe_grooming_service_operation.start_time
     *
     * @mbg.generated
     */
    public Integer getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.start_time
     *
     * @param startTime the value for moe_grooming_service_operation.start_time
     *
     * @mbg.generated
     */
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.duration
     *
     * @return the value of moe_grooming_service_operation.duration
     *
     * @mbg.generated
     */
    public Integer getDuration() {
        return duration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.duration
     *
     * @param duration the value for moe_grooming_service_operation.duration
     *
     * @mbg.generated
     */
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.comment
     *
     * @return the value of moe_grooming_service_operation.comment
     *
     * @mbg.generated
     */
    public String getComment() {
        return comment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.comment
     *
     * @param comment the value for moe_grooming_service_operation.comment
     *
     * @mbg.generated
     */
    public void setComment(String comment) {
        this.comment = comment == null ? null : comment.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.price
     *
     * @return the value of moe_grooming_service_operation.price
     *
     * @mbg.generated
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.price
     *
     * @param price the value for moe_grooming_service_operation.price
     *
     * @mbg.generated
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.price_ratio
     *
     * @return the value of moe_grooming_service_operation.price_ratio
     *
     * @mbg.generated
     */
    public BigDecimal getPriceRatio() {
        return priceRatio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.price_ratio
     *
     * @param priceRatio the value for moe_grooming_service_operation.price_ratio
     *
     * @mbg.generated
     */
    public void setPriceRatio(BigDecimal priceRatio) {
        this.priceRatio = priceRatio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.status
     *
     * @return the value of moe_grooming_service_operation.status
     *
     * @mbg.generated
     */
    public Boolean getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.status
     *
     * @param status the value for moe_grooming_service_operation.status
     *
     * @mbg.generated
     */
    public void setStatus(Boolean status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.create_time
     *
     * @return the value of moe_grooming_service_operation.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.create_time
     *
     * @param createTime the value for moe_grooming_service_operation.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.update_time
     *
     * @return the value of moe_grooming_service_operation.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.update_time
     *
     * @param updateTime the value for moe_grooming_service_operation.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_operation.company_id
     *
     * @return the value of moe_grooming_service_operation.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_operation.company_id
     *
     * @param companyId the value for moe_grooming_service_operation.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
