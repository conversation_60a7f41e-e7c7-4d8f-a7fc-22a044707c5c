<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeQbSyncProductMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeQbSyncProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="connect_id" jdbcType="INTEGER" property="connectId" />
    <result column="realm_id" jdbcType="VARCHAR" property="realmId" />
    <result column="qb_service_id" jdbcType="VARCHAR" property="qbServiceId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeQbSyncProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="product_description" jdbcType="LONGVARCHAR" property="productDescription" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, connect_id, realm_id, qb_service_id, product_id, product_name, create_time,
    update_time, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    product_description
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_qb_sync_product
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_sync_product
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_product (business_id, connect_id, realm_id,
      qb_service_id, product_id, product_name,
      create_time, update_time, company_id,
      product_description)
    values (#{businessId,jdbcType=INTEGER}, #{connectId,jdbcType=INTEGER}, #{realmId,jdbcType=VARCHAR},
      #{qbServiceId,jdbcType=VARCHAR}, #{productId,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT},
      #{productDescription,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="connectId != null">
        connect_id,
      </if>
      <if test="realmId != null">
        realm_id,
      </if>
      <if test="qbServiceId != null">
        qb_service_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="productDescription != null">
        product_description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="qbServiceId != null">
        #{qbServiceId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="productDescription != null">
        #{productDescription,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_product
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        connect_id = #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        realm_id = #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="qbServiceId != null">
        qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="productDescription != null">
        product_description = #{productDescription,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_product
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      product_name = #{productName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      product_description = #{productDescription,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_product
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      product_name = #{productName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByBusinessIdRealmIdProductIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_qb_sync_product
    where business_id = #{businessId}
    and realm_id = #{realmId}
    and product_id in
    <foreach close=")" collection="productIdList" item="productId" open="(" separator=",">
      #{productId}
    </foreach>
  </select>

  <insert id="insertOrUpdate" keyProperty="id" useGeneratedKeys="true">
    insert into moe_qb_sync_product
    (business_id, connect_id,
     realm_id, qb_service_id,
     product_id, product_name, product_description, company_id)
    values (#{businessId,jdbcType=INTEGER}, #{connectId,jdbcType=INTEGER},
            #{realmId,jdbcType=VARCHAR}, #{qbServiceId,jdbcType=VARCHAR},
            #{productId,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR},
            #{productDescription,jdbcType=LONGVARCHAR}, #{companyId,jdbcType=BIGINT})
    on duplicate key update
      qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      connect_id = #{connectId,jdbcType=INTEGER},
      product_description = #{productDescription,jdbcType=LONGVARCHAR}
  </insert>

</mapper>
