<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingPackageHistoryMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="package_id" jdbcType="INTEGER" property="packageId" />
    <result column="package_service_id" jdbcType="INTEGER" property="packageServiceId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="appointment_date" jdbcType="DATE" property="appointmentDate" />
    <result column="use_time" jdbcType="BIGINT" property="useTime" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="activity_type" jdbcType="TINYINT" property="activityType" typeHandler="com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler" />
    <result column="after_extend_expire_date" jdbcType="VARCHAR" property="afterExtendExpireDate" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.activityTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.activityTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, package_id, package_service_id, service_id, quantity, appointment_date, use_time,
    grooming_id, invoice_id, status, activity_type, after_extend_expire_date, staff_id
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageHistoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_package_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_package_history
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_package_history
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageHistoryExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_package_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_package_history (package_id, package_service_id, service_id,
      quantity, appointment_date, use_time,
      grooming_id, invoice_id, status,
      activity_type,
      after_extend_expire_date, staff_id)
    values (#{packageId,jdbcType=INTEGER}, #{packageServiceId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER},
      #{quantity,jdbcType=INTEGER}, #{appointmentDate,jdbcType=DATE}, #{useTime,jdbcType=BIGINT},
      #{groomingId,jdbcType=INTEGER}, #{invoiceId,jdbcType=INTEGER}, #{status,jdbcType=TINYINT},
      #{activityType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler},
      #{afterExtendExpireDate,jdbcType=VARCHAR}, #{staffId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_package_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="packageId != null">
        package_id,
      </if>
      <if test="packageServiceId != null">
        package_service_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="appointmentDate != null">
        appointment_date,
      </if>
      <if test="useTime != null">
        use_time,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="activityType != null">
        activity_type,
      </if>
      <if test="afterExtendExpireDate != null">
        after_extend_expire_date,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="packageId != null">
        #{packageId,jdbcType=INTEGER},
      </if>
      <if test="packageServiceId != null">
        #{packageServiceId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="appointmentDate != null">
        #{appointmentDate,jdbcType=DATE},
      </if>
      <if test="useTime != null">
        #{useTime,jdbcType=BIGINT},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="activityType != null">
        #{activityType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler},
      </if>
      <if test="afterExtendExpireDate != null">
        #{afterExtendExpireDate,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageHistoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_package_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.packageId != null">
        package_id = #{record.packageId,jdbcType=INTEGER},
      </if>
      <if test="record.packageServiceId != null">
        package_service_id = #{record.packageServiceId,jdbcType=INTEGER},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=INTEGER},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.appointmentDate != null">
        appointment_date = #{record.appointmentDate,jdbcType=DATE},
      </if>
      <if test="record.useTime != null">
        use_time = #{record.useTime,jdbcType=BIGINT},
      </if>
      <if test="record.groomingId != null">
        grooming_id = #{record.groomingId,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceId != null">
        invoice_id = #{record.invoiceId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.activityType != null">
        activity_type = #{record.activityType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler},
      </if>
      <if test="record.afterExtendExpireDate != null">
        after_extend_expire_date = #{record.afterExtendExpireDate,jdbcType=VARCHAR},
      </if>
      <if test="record.staffId != null">
        staff_id = #{record.staffId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package_history
    set id = #{record.id,jdbcType=INTEGER},
      package_id = #{record.packageId,jdbcType=INTEGER},
      package_service_id = #{record.packageServiceId,jdbcType=INTEGER},
      service_id = #{record.serviceId,jdbcType=INTEGER},
      quantity = #{record.quantity,jdbcType=INTEGER},
      appointment_date = #{record.appointmentDate,jdbcType=DATE},
      use_time = #{record.useTime,jdbcType=BIGINT},
      grooming_id = #{record.groomingId,jdbcType=INTEGER},
      invoice_id = #{record.invoiceId,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      activity_type = #{record.activityType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler},
      after_extend_expire_date = #{record.afterExtendExpireDate,jdbcType=VARCHAR},
      staff_id = #{record.staffId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package_history
    <set>
      <if test="packageId != null">
        package_id = #{packageId,jdbcType=INTEGER},
      </if>
      <if test="packageServiceId != null">
        package_service_id = #{packageServiceId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="appointmentDate != null">
        appointment_date = #{appointmentDate,jdbcType=DATE},
      </if>
      <if test="useTime != null">
        use_time = #{useTime,jdbcType=BIGINT},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="activityType != null">
        activity_type = #{activityType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler},
      </if>
      <if test="afterExtendExpireDate != null">
        after_extend_expire_date = #{afterExtendExpireDate,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package_history
    set package_id = #{packageId,jdbcType=INTEGER},
      package_service_id = #{packageServiceId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      quantity = #{quantity,jdbcType=INTEGER},
      appointment_date = #{appointmentDate,jdbcType=DATE},
      use_time = #{useTime,jdbcType=BIGINT},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      invoice_id = #{invoiceId,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      activity_type = #{activityType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler},
      after_extend_expire_date = #{afterExtendExpireDate,jdbcType=VARCHAR},
      staff_id = #{staffId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <resultMap extends="BaseResultMap" id="GroomingPackageHistoryDTOResultMap" type="com.moego.server.grooming.dto.GroomingPackageHistoryDTO">
    <result column="confirmation_id" jdbcType="VARCHAR" property="confirmationId" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="package_desc" jdbcType="VARCHAR" property="packageDesc" />
    <result column="package_price" jdbcType="DECIMAL" property="packagePrice" />
    <result column="purchase_time" jdbcType="BIGINT" property="purchaseTime" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="retail_invoice_item_id" jdbcType="INTEGER" property="retailInvoiceItemId" />
  </resultMap>

  <insert id="batchAddMoeGroomingPackageHistories" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory">
    insert into moe_grooming_package_history (invoice_id, package_id, package_service_id, service_id, quantity, appointment_date, use_time,grooming_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.invoiceId}, #{item.packageId}, #{item.packageServiceId}, #{item.serviceId}, #{item.quantity},#{item.appointmentDate}, #{item.useTime},#{item.groomingId})
    </foreach>
  </insert>

  <select id="queryPackageHistory" resultMap="GroomingPackageHistoryDTOResultMap">
    SELECT
      h.id,
      h.package_id,
      h.package_service_id,
      h.service_id,
      h.invoice_id,
      h.quantity,
      h.appointment_date,
      h.use_time,
      h.grooming_id,
      h.status,
      h.activity_type,
      h.after_extend_expire_date,
      h.staff_id,
      p.confirmation_id,
      p.package_name,
      p.package_desc,
      p.package_price,
      p.purchase_time,
      p.start_time,
      p.end_time,
      p.retail_invoice_item_id,
      s.name serviceName
    FROM
      moe_grooming_package_history h
        INNER JOIN
      moe_grooming_package p ON h.package_id = p.id
        LEFT JOIN moe_grooming_service s ON h.service_id = s.id
    WHERE
      h.package_id = #{packageId}
      and h.status = 1
    ORDER BY h.use_time DESC
    LIMIT #{limitOffset,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
  </select>

  <select id="getPackageHistoryCount" resultType="java.lang.Integer">
    SELECT count(*) AS packageHistoryCount
    FROM
      moe_grooming_package_history h
    INNER JOIN
      moe_grooming_package p ON h.package_id = p.id
    WHERE
      h.package_id = #{packageId}
      and h.status = 1
  </select>

  <select id="queryPackageHistoryByPackageId" resultMap="GroomingPackageHistoryDTOResultMap">
    SELECT
       h.id,
       h.package_id,
       h.package_service_id,
       h.service_id,
       h.invoice_id,
       h.quantity,
       h.appointment_date,
       h.use_time,
       h.grooming_id,
       h.status,
       h.activity_type,
       h.after_extend_expire_date,
       h.staff_id,
      p.confirmation_id,
      p.package_name,
      p.package_desc,
      p.package_price,
      p.purchase_time,
      p.start_time,
      p.end_time,
      p.retail_invoice_item_id,
      (select name from moe_grooming_service where id = h.service_id) serviceName
    FROM
      moe_grooming_package_history h
    INNER JOIN
      moe_grooming_package p ON h.package_id = p.id
    WHERE
      h.package_id = #{packageId}
    and h.status = 1
  </select>

  <select id="queryPackageHistoryByPackageIdList" resultMap="GroomingPackageHistoryDTOResultMap">
    SELECT
       h.id,
       h.package_id,
       h.package_service_id,
       h.service_id,
       h.invoice_id,
       h.quantity,
       h.appointment_date,
       h.use_time,
       h.grooming_id,
       h.status,
       h.activity_type,
       h.after_extend_expire_date,
       h.staff_id,
      p.confirmation_id,
      p.package_name,
      p.package_desc,
      p.package_price,
      p.purchase_time,
      p.start_time,
      p.end_time,
      p.retail_invoice_item_id,
      (select name from moe_grooming_service where id = h.service_id) serviceName
    FROM
      moe_grooming_package_history h
    INNER JOIN
      moe_grooming_package p ON h.package_id = p.id
    WHERE
      h.package_id in
    <foreach close=")" collection="packageIdList" item="id" open="(" separator=",">
      #{id}
    </foreach>
    and h.status = 1
  </select>

  <select id="queryPackageHistoryByInvoiceId" resultMap="GroomingPackageHistoryDTOResultMap">
    SELECT
      h.id,
      h.package_id,
      h.package_service_id,
      h.service_id,
      h.quantity,
      h.appointment_date,
      h.use_time,
      h.grooming_id,
      h.invoice_id,
      h.status,
      h.activity_type,
      h.after_extend_expire_date,
      h.staff_id,
      p.confirmation_id,
      p.package_name,
      p.package_desc,
      p.package_price,
      p.purchase_time,
      p.start_time,
      p.end_time,
      p.retail_invoice_item_id
    FROM
      moe_grooming_package_history h
    INNER JOIN
      moe_grooming_package p ON h.package_id = p.id
    WHERE
      h.invoice_id = #{invoiceId}
      and h.status = 1
  </select>

  <update id="batchInvalidGroomingPackageHistories">
    update moe_grooming_package_history
    set status = 2
    where id in
    <foreach close=")" collection="ids" index="index" item="id" open="(" separator=",">
      #{id,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="batchInvalidGroomingPackageHistoriesByInvoiceIds">
    update moe_grooming_package_history
    set status = 2
    where invoice_id in
    <foreach close=")" collection="invoiceIds" index="index" item="invoiceId" open="(" separator=",">
      #{invoiceId,jdbcType=INTEGER}
    </foreach>
  </update>

</mapper>
