<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="grooming_service_id" jdbcType="INTEGER" property="groomingServiceId" />
    <result column="pet_id" jdbcType="INTEGER" property="petId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="start_time" jdbcType="INTEGER" property="startTime" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="price_ratio" jdbcType="DECIMAL" property="priceRatio" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, grooming_id, grooming_service_id, pet_id, staff_id, operation_name,
    start_time, duration, comment, price, price_ratio, status, create_time, update_time,
    company_id
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceOperationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_service_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_operation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_service_operation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceOperationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_service_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_operation (business_id, grooming_id, grooming_service_id,
      pet_id, staff_id, operation_name,
      start_time, duration, comment,
      price, price_ratio, status,
      create_time, update_time, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{groomingId,jdbcType=INTEGER}, #{groomingServiceId,jdbcType=INTEGER},
      #{petId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{operationName,jdbcType=VARCHAR},
      #{startTime,jdbcType=INTEGER}, #{duration,jdbcType=INTEGER}, #{comment,jdbcType=VARCHAR},
      #{price,jdbcType=DECIMAL}, #{priceRatio,jdbcType=DECIMAL}, #{status,jdbcType=BIT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="groomingServiceId != null">
        grooming_service_id,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="operationName != null">
        operation_name,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="comment != null">
        comment,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="priceRatio != null">
        price_ratio,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="groomingServiceId != null">
        #{groomingServiceId,jdbcType=INTEGER},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="operationName != null">
        #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="priceRatio != null">
        #{priceRatio,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceOperationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_service_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_operation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.groomingId != null">
        grooming_id = #{record.groomingId,jdbcType=INTEGER},
      </if>
      <if test="record.groomingServiceId != null">
        grooming_service_id = #{record.groomingServiceId,jdbcType=INTEGER},
      </if>
      <if test="record.petId != null">
        pet_id = #{record.petId,jdbcType=INTEGER},
      </if>
      <if test="record.staffId != null">
        staff_id = #{record.staffId,jdbcType=INTEGER},
      </if>
      <if test="record.operationName != null">
        operation_name = #{record.operationName,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=INTEGER},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.comment != null">
        comment = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.priceRatio != null">
        price_ratio = #{record.priceRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_operation
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=INTEGER},
      grooming_id = #{record.groomingId,jdbcType=INTEGER},
      grooming_service_id = #{record.groomingServiceId,jdbcType=INTEGER},
      pet_id = #{record.petId,jdbcType=INTEGER},
      staff_id = #{record.staffId,jdbcType=INTEGER},
      operation_name = #{record.operationName,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=INTEGER},
      duration = #{record.duration,jdbcType=INTEGER},
      comment = #{record.comment,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      price_ratio = #{record.priceRatio,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_operation
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="groomingServiceId != null">
        grooming_service_id = #{groomingServiceId,jdbcType=INTEGER},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="operationName != null">
        operation_name = #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="comment != null">
        comment = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="priceRatio != null">
        price_ratio = #{priceRatio,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_operation
    set business_id = #{businessId,jdbcType=INTEGER},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      grooming_service_id = #{groomingServiceId,jdbcType=INTEGER},
      pet_id = #{petId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      operation_name = #{operationName,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=INTEGER},
      duration = #{duration,jdbcType=INTEGER},
      comment = #{comment,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      price_ratio = #{priceRatio,jdbcType=DECIMAL},
      status = #{status,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
    insert into moe_grooming_service_operation
    (business_id, company_id, grooming_id,
     grooming_service_id, pet_id,
     staff_id, operation_name,
     start_time, duration,
     comment, price,
     price_ratio)
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (#{item.businessId,jdbcType=INTEGER}, #{item.companyId,jdbcType=BIGINT},
       #{item.groomingId,jdbcType=INTEGER}, #{item.groomingServiceId,jdbcType=INTEGER},
       #{item.petId,jdbcType=INTEGER}, #{item.staffId,jdbcType=INTEGER},
       #{item.operationName,jdbcType=VARCHAR}, #{item.startTime,jdbcType=INTEGER},
       #{item.duration,jdbcType=INTEGER}, #{item.comment,jdbcType=VARCHAR},
       #{item.price,jdbcType=DECIMAL}, #{item.priceRatio,jdbcType=DECIMAL})
    </foreach>
  </insert>

  <update id="batchUpdate">
    <foreach collection="list" index="index" item="item" separator=";">
      update moe_grooming_service_operation
      <set>
        <if test="item.businessId != null">
          business_id = #{item.businessId,jdbcType=INTEGER},
        </if>
        <if test="item.groomingId != null">
          grooming_id = #{item.groomingId,jdbcType=INTEGER},
        </if>
        <if test="item.groomingServiceId != null">
          grooming_service_id = #{item.groomingServiceId,jdbcType=INTEGER},
        </if>
        <if test="item.petId != null">
          pet_id = #{item.petId,jdbcType=INTEGER},
        </if>
        <if test="item.staffId != null">
          staff_id = #{item.staffId,jdbcType=INTEGER},
        </if>
        <if test="item.operationName != null">
          operation_name = #{item.operationName,jdbcType=VARCHAR},
        </if>
        <if test="item.startTime != null">
          start_time = #{item.startTime,jdbcType=INTEGER},
        </if>
        <if test="item.duration != null">
          duration = #{item.duration,jdbcType=INTEGER},
        </if>
        <if test="item.comment != null">
          comment = #{item.comment,jdbcType=VARCHAR},
        </if>
        <if test="item.price != null">
          price = #{item.price,jdbcType=DECIMAL},
        </if>
        <if test="item.priceRatio != null">
          price_ratio = #{item.priceRatio,jdbcType=DECIMAL},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=BIT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectByBusinessIdAndGroomingServiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_operation
    where business_id = #{businessId,jdbcType=INTEGER}
      and grooming_service_id = #{groomingServiceId,jdbcType=INTEGER}
  </select>

  <select id="selectByBusinessIdAndGroomingServiceIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_operation
    where business_id = #{businessId,jdbcType=INTEGER}
      and grooming_service_id in
    <foreach close=")" collection="groomingServiceIdList" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectByBusinessIdAndGroomingId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_operation
    where business_id = #{businessId,jdbcType=INTEGER}
      and grooming_id = #{groomingId,jdbcType=INTEGER}
  </select>

  <select id="selectByGroomingIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_operation
    where  grooming_id in
    <foreach close=")" collection="groomingIdList" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectByBusinessIdAndGroomingIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_operation
    where business_id = #{businessId,jdbcType=INTEGER}
      and grooming_id in
    <foreach close=")" collection="groomingIdList" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <delete id="deleteByGroomingId">
    delete
    from moe_grooming_service_operation
    where grooming_id = #{groomingId,jdbcType=INTEGER}
  </delete>

  <update id="deleteByGroomingIdList" parameterType="int">
    delete
    from moe_grooming_service_operation
    where grooming_id in
    <foreach close=")" collection="groomingIdList" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </update>

  <select id="queryStaffIdByGroomingId" resultType="integer">
    select distinct staff_id
    from moe_grooming_service_operation
    where grooming_id = #{groomingId,jdbcType=INTEGER}
  </select>

  <select id="queryStaffIdByGroomingIds" resultMap="com.moego.server.grooming.mapper.MoeGroomingPetDetailMapper.GroomingStaffIdListMap">
    select distinct grooming_id, staff_id
    from moe_grooming_service_operation
    where grooming_id in
    <foreach close=")" collection="groomingIds" item="groomingId" open="(" separator=",">
      #{groomingId}
    </foreach>
  </select>

  <select id="countOperationWithSameStartTimeByGroomingIdList" resultType="java.lang.Integer">
    select count(*)
    from (select grooming_id
          from moe_grooming_service_operation
    where grooming_id in
    <foreach close=")" collection="groomingIdList" item="groomingId" open="(" separator=",">
      #{groomingId}
    </foreach>
    and (staff_id = #{sourceStaffId} or staff_id = #{targetStaffId})
    group by grooming_id, start_time
    having count(*) &gt; 1 ) temp
  </select>

  <update id="transferOperation">
    update moe_grooming_service_operation
    set staff_id = #{targetStaffId}
    where grooming_id in
    <foreach close=")" collection="groomingIdList" item="groomingId" open="(" separator=",">
      #{groomingId}
    </foreach>
    and staff_id = #{sourceStaffId}
  </update>
  <select id="describeOperationReports" parameterType="com.moego.server.grooming.params.report.DescribeOperationReportsParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_operation
    <where>
      <if test="ids != null and ids.size()&gt;0">
        and id in
        <foreach close=")" collection="ids" item="id" open="(" separator=",">
          #{id}
        </foreach>
      </if>
      <if test="groomingIds != null and groomingIds.size()&gt;0">
        and grooming_id in
        <foreach close=")" collection="groomingIds" item="groomingId" open="(" separator=",">
          #{groomingId}
        </foreach>
      </if>
      <if test="groomingServiceIds != null and groomingServiceIds.size()&gt;0">
        and staff_id in
        <foreach close=")" collection="groomingServiceIds" item="groomingServiceId" open="(" separator=",">
          #{groomingServiceId}
        </foreach>
      </if>
    </where>
    order by id
  </select>
  <delete id="deleteByPetDetailId">
    delete
    from moe_grooming_service_operation
    where grooming_service_id = #{petDetailId}
  </delete>
</mapper>
