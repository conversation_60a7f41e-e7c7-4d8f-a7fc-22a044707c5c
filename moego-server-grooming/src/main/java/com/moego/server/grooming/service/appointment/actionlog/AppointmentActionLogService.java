package com.moego.server.grooming.service.appointment.actionlog;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.idl.client.online_booking.v1.UpdateBookingRequestParams;
import com.moego.idl.client.online_booking.v2.UpdateAppointmentParams;
import com.moego.idl.models.activity_log.v1.ActivityLogModel;
import com.moego.idl.models.activity_log.v1.ActivityLogModelSimpleView;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc.ActivityLogServiceBlockingStub;
import com.moego.idl.service.activity_log.v1.ListActivityLogDetailsRequest;
import com.moego.idl.service.activity_log.v1.ListActivityLogDetailsResponse;
import com.moego.idl.service.activity_log.v1.SearchActivityLogPageInput;
import com.moego.idl.service.activity_log.v1.SearchActivityLogPageOutput;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetPetInfoRequest;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetServiceDetailRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.CreateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.appointment.history.ActionHistoryDTO;
import com.moego.server.grooming.dto.appointment.history.AppointmentActionHistory;
import com.moego.server.grooming.dto.appointment.history.AutoRolloverLogDTO;
import com.moego.server.grooming.dto.appointment.history.CancelLogDTO;
import com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO;
import com.moego.server.grooming.dto.appointment.history.CreateLogDTO;
import com.moego.server.grooming.dto.appointment.history.CustomerReplyLogDTO;
import com.moego.server.grooming.dto.appointment.history.NotificationUpdateDTO;
import com.moego.server.grooming.dto.appointment.history.SendNotificationLogDTO;
import com.moego.server.grooming.dto.appointment.history.UpdateStatusLogDTO;
import com.moego.server.grooming.enums.ActionTypeEnum;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.NotificationTypeEnum;
import com.moego.server.grooming.mapstruct.AutoAssignConverter;
import com.moego.server.grooming.service.AutoAssignService;
import com.moego.server.message.enums.ClientReplyTypeEnum;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.svc.activitylog.event.enums.ResourceType;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentActionLogService {

    private final ActivityLogServiceBlockingStub activityLogServiceBlockingStub;
    private final AutoAssignService autoAssignService;
    private final ServiceManagementServiceBlockingStub serviceStub;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub customerStub;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub petStub;
    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationStub;
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestStub;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    public ActionHistoryDTO getActionHistory(Integer businessId, Integer appointmentId) {
        SearchActivityLogPageOutput activities =
                activityLogServiceBlockingStub.searchActivityLogPage(SearchActivityLogPageInput.newBuilder()
                        .addResourceId(appointmentId.toString())
                        .setBusinessId(businessId)
                        .addResourceType(ResourceType.APPOINTMENT.toString())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(999) // 不分页，一次性取完
                                .build())
                        .setQueryRoot(false)
                        .build());
        Map<String, Value> activityLogMap = getActivityLogMap(activities.getActivityLogsList().stream()
                .map(ActivityLogModelSimpleView::getId)
                .toList());
        List<AppointmentActionHistory> actionList = activities.getActivityLogsList().parallelStream()
                .filter(activityLog ->
                        !ActionTypeEnum.fromString(activityLog.getAction()).equals(ActionTypeEnum.UNKNOWN))
                .flatMap(activityLog -> {
                    AppointmentActionHistory action = new AppointmentActionHistory();
                    action.setOperatorId(Optional.of(activityLog.getOperator().getId())
                            .filter(StringUtils::hasText)
                            .map(Integer::parseInt)
                            .orElse(0));
                    action.setActionType(ActionTypeEnum.fromString(activityLog.getAction()));
                    action.setOperateTime(activityLog.getTime().getSeconds());
                    try {
                        Struct struct = activityLogMap.get(activityLog.getId()).getStructValue();
                        switch (activityLog.getAction()) {
                            case AppointmentAction.CREATE -> {
                                boolean isOB = GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB.equals(
                                                (byte) struct.getFieldsOrDefault(
                                                                "bookOnlineStatus", Value.getDefaultInstance())
                                                        .getNumberValue())
                                        || GroomingAppointmentEnum.SOURCE_OB.equals(
                                                (int) struct.getFieldsOrDefault("source", Value.getDefaultInstance())
                                                        .getNumberValue());
                                boolean isAutoAccept = struct.getFieldsOrDefault(
                                                "isAutoAccept", Value.getDefaultInstance())
                                        .getBoolValue();

                                action.setCreateLogDTO(new CreateLogDTO(
                                        isOB,
                                        isAutoAccept,
                                        AutoAssignConverter.INSTANCE.entityToDTO(
                                                autoAssignService.getAutoAssign(appointmentId))));
                            }
                            case AppointmentAction.UPDATE_STATUS -> {
                                UpdateStatusLogDTO updateStatusLogDTO = new UpdateStatusLogDTO(
                                        AppointmentStatusEnum.fromValue((byte)
                                                struct.getFieldsOrDefault("oldStatus", Value.getDefaultInstance())
                                                        .getNumberValue()),
                                        AppointmentStatusEnum.fromValue((byte)
                                                struct.getFieldsOrDefault("newStatus", Value.getDefaultInstance())
                                                        .getNumberValue()));
                                if (updateStatusLogDTO.newStatus().equals(updateStatusLogDTO.oldStatus())) {
                                    return Stream.empty();
                                }
                                action.setUpdateStatusLog(updateStatusLogDTO);
                            }
                            case AppointmentAction.CHANGE_CHECK_IN_TIME,
                                    AppointmentAction.CHANGE_CHECK_OUT_TIME,
                                    AppointmentAction.RESCHEDULE -> {
                                AppointmentUpdatedBy changedByType = AppointmentUpdatedBy.BY_BUSINESS;
                                if (struct.getFieldsMap().containsKey("changedByType")) {
                                    Value changedByTypeValue =
                                            struct.getFieldsOrDefault("changedByType", Value.getDefaultInstance());
                                    if (changedByTypeValue.hasNumberValue()) {
                                        changedByType = AppointmentUpdatedBy.forNumber(
                                                (int) changedByTypeValue.getNumberValue());
                                    } else if (changedByTypeValue.hasStringValue()) {
                                        changedByType =
                                                AppointmentUpdatedBy.valueOf(changedByTypeValue.getStringValue());
                                    }
                                }
                                ChangeTimeLogDTO changeTimeLogDTO = new ChangeTimeLogDTO(
                                        (long) struct.getFieldsOrDefault("oldTime", Value.getDefaultInstance())
                                                .getNumberValue(),
                                        (long) struct.getFieldsOrDefault("newTime", Value.getDefaultInstance())
                                                .getNumberValue(),
                                        changedByType);
                                if (changeTimeLogDTO.newTime().equals(changeTimeLogDTO.oldTime())) {
                                    return Stream.empty();
                                }
                                action.setChangeTimeLog(changeTimeLogDTO);
                            }
                            case AppointmentAction.CANCEL -> {
                                action.setCancelReason(
                                        struct.getFieldsOrDefault("cancelReason", Value.getDefaultInstance())
                                                .getStringValue());

                                // 默认为 business 取消，尽量让能在预约的 update history 展示出来
                                AppointmentUpdatedBy canceledByType = AppointmentUpdatedBy.BY_BUSINESS;
                                if (struct.getFieldsMap().containsKey("cancelByType")) {
                                    Value canceledByTypeValue =
                                            struct.getFieldsOrDefault("cancelByType", Value.getDefaultInstance());
                                    if (canceledByTypeValue.hasNumberValue()) {
                                        canceledByType = AppointmentUpdatedBy.forNumber(
                                                (int) canceledByTypeValue.getNumberValue());
                                    } else if (canceledByTypeValue.hasStringValue()) {
                                        canceledByType =
                                                AppointmentUpdatedBy.valueOf(canceledByTypeValue.getStringValue());
                                    }
                                }
                                if (struct.getFieldsMap().containsKey("canceledByType")) {
                                    canceledByType = AppointmentUpdatedBy.valueOf(struct.getFieldsMap()
                                            .get("canceledByType")
                                            .getStringValue());
                                }
                                action.setCancelLogDTO(new CancelLogDTO(
                                        struct.getFieldsOrDefault("cancelReason", Value.getDefaultInstance())
                                                .getStringValue(),
                                        canceledByType));
                            }
                            case AppointmentAction.SEND_NOTIFICATION -> {
                                action.setSendNotificationLog(new SendNotificationLogDTO(
                                        MessageMethodTypeEnum.fromInteger((int) struct.getFieldsOrDefault(
                                                        "messageMethodType", Value.getDefaultInstance())
                                                .getNumberValue()),
                                        struct.getFieldsOrDefault("sendSuccess", Value.getDefaultInstance())
                                                .getBoolValue(),
                                        struct.getFieldsOrDefault("sendFailedReason", Value.getDefaultInstance())
                                                .getStringValue(),
                                        NotificationTypeEnum.fromString(struct.getFieldsOrDefault(
                                                        "notificationType", Value.getDefaultInstance())
                                                .getStringValue())));
                            }
                            case AppointmentAction.CUSTOMER_REPLY -> {
                                action.setCustomerReplyLogDTO(new CustomerReplyLogDTO(
                                        struct.getFieldsOrDefault("statusUpdated", Value.getDefaultInstance())
                                                .getBoolValue(),
                                        MessageMethodTypeEnum.fromInteger((int) struct.getFieldsOrDefault(
                                                        "messageMethodType", Value.getDefaultInstance())
                                                .getNumberValue()),
                                        ClientReplyTypeEnum.fromString(struct.getFieldsOrDefault(
                                                        "clientReplyTypeEnum", Value.getDefaultInstance())
                                                .getStringValue())));
                            }
                            case AppointmentAction.UPDATE_NOTIFICATION_STATUS -> {
                                action.setNotificationUpdateDTO(new NotificationUpdateDTO(
                                        struct.getFieldsOrDefault("success", Value.getDefaultInstance())
                                                .getBoolValue(),
                                        struct.getFieldsOrDefault("failedReason", Value.getDefaultInstance())
                                                .getStringValue(),
                                        NotificationTypeEnum.fromString(struct.getFieldsOrDefault(
                                                        "notificationTypeEnum", Value.getDefaultInstance())
                                                .getStringValue())));
                            }
                            case AppointmentAction.AUTO_ROLLOVER -> {
                                action.setAutoRolloverLog(getAutoRolloverLog(struct));
                            }
                            case AppointmentAction.CLIENT_UPDATE_OB -> {
                                var params = JsonUtil.toBean(JsonUtil.toJson(struct), UpdateBookingRequestParams.class);
                                action.setClientUpdateLog(getClientUpdateLogForBookingRequest(params));
                                action.setOperatorId(getCustomerIdForBookingRequest(params));
                            }
                            case AppointmentAction.CLIENT_UPDATE_APPOINTMENT -> {
                                var params = JsonUtil.toBean(JsonUtil.toJson(struct), UpdateAppointmentParams.class);
                                action.setClientUpdateLog(getClientUpdateLogForAppointment(params));
                                action.setOperatorId(getCustomerIdForAppointment(params));
                            }
                            default -> {}
                        }
                    } catch (Exception e) {
                        log.error("Failed to parse activity log detail: {}", e.getMessage());
                        return null;
                    }

                    return Stream.of(action);
                })
                .toList();
        return new ActionHistoryDTO(actionList);
    }

    @Nullable
    private Integer getCustomerIdForBookingRequest(UpdateBookingRequestParams params) {
        return Optional.ofNullable(getBookingRequest(params.getBookingRequestId()))
                .map(BookingRequestModel::getCustomerId)
                .map(Long::intValue)
                .orElse(null);
    }

    @Nullable
    private Integer getCustomerIdForAppointment(UpdateAppointmentParams params) {
        return Optional.ofNullable(getAppointment(params.getAppointmentId()))
                .map(AppointmentModel::getCustomerId)
                .map(Long::intValue)
                .orElse(null);
    }

    @Nullable
    private AppointmentActionHistory.ClientUpdateLog getClientUpdateLogForBookingRequest(
            UpdateBookingRequestParams params) {
        var addedServices = new ArrayList<AppointmentActionHistory.ClientUpdateLog.ServiceDetail>();
        for (var service : params.getServiceDetailsList()) {
            switch (service.getActionCase()) {
                case ADD -> {
                    var add = service.getAdd();
                    var pet = Optional.ofNullable(getPetIdForAdd(add))
                            .map(this::getPet)
                            .orElse(null);
                    var addedServiceIds = getAddedServiceIdsForAdd(add);
                    for (var serviceId : addedServiceIds) {
                        var serviceModel = getService(serviceId);
                        var addedService = new AppointmentActionHistory.ClientUpdateLog.ServiceDetail(
                                pet != null ? pet.getPetName() : null,
                                serviceModel != null ? serviceModel.getName() : null);
                        addedServices.add(addedService);
                    }
                }
                case UPDATE -> {
                    var update = service.getUpdate();
                    var pet = Optional.ofNullable(getPetIdForUpdate(update))
                            .map(this::getPet)
                            .orElse(null);
                    var addedServiceIds = getAddedServiceIdsForUpdate(update);
                    for (var serviceId : addedServiceIds) {
                        var serviceModel = getService(serviceId);
                        var addedService = new AppointmentActionHistory.ClientUpdateLog.ServiceDetail(
                                pet != null ? pet.getPetName() : null,
                                serviceModel != null ? serviceModel.getName() : null);
                        addedServices.add(addedService);
                    }
                }
                default -> {}
            }
        }

        if (addedServices.isEmpty()) {
            return null;
        }

        return new AppointmentActionHistory.ClientUpdateLog(addedServices);
    }

    @Nullable
    private static Long getPetIdForAdd(CreateBookingRequestRequest.Service add) {
        return switch (add.getServiceCase()) {
            case GROOMING -> add.getGrooming().getService().getPetId();
            case BOARDING -> add.getBoarding().getService().getPetId();
            case DAYCARE -> add.getDaycare().getService().getPetId();
            case EVALUATION -> add.getEvaluation().getService().getPetId();
            case DOG_WALKING -> add.getDogWalking().getService().getPetId();
            case GROUP_CLASS -> add.getGroupClass().getService().getPetId();
            case SERVICE_NOT_SET -> null;
        };
    }

    @Nullable
    private static Long getPetIdForUpdate(UpdateBookingRequestRequest.Service update) {
        return switch (update.getServiceCase()) {
            case GROOMING -> {
                var grooming = update.getGrooming();
                for (var addon : grooming.getAddonsV2List()) {
                    if (addon.hasAdd()) {
                        yield addon.getAdd().getAddon().getPetId();
                    }
                }
                yield null;
            }
            case BOARDING -> {
                var boarding = update.getBoarding();
                for (var addon : boarding.getAddonsV2List()) {
                    if (addon.hasAdd()) {
                        yield addon.getAdd().getAddon().getPetId();
                    }
                }
                yield null;
            }
            case DAYCARE -> {
                var daycare = update.getDaycare();
                for (var addon : daycare.getAddonsV2List()) {
                    if (addon.hasAdd()) {
                        yield addon.getAdd().getAddon().getPetId();
                    }
                }
                yield null;
            }
            case SERVICE_NOT_SET -> null;
        };
    }

    @Nullable
    private AppointmentActionHistory.ClientUpdateLog getClientUpdateLogForAppointment(UpdateAppointmentParams params) {
        var addedServices = new ArrayList<AppointmentActionHistory.ClientUpdateLog.ServiceDetail>();
        for (var petDetail : params.getPetDetailsList()) {
            switch (petDetail.getActionCase()) {
                case ADD -> {
                    var createPetDetailRequest = petDetail.getAdd().getPetDetail();
                    var pet = getPet(createPetDetailRequest.getPetId());
                    var service = getService(createPetDetailRequest.getServiceId());
                    var addedService = new AppointmentActionHistory.ClientUpdateLog.ServiceDetail(
                            pet != null ? pet.getPetName() : null, service != null ? service.getName() : null);
                    addedServices.add(addedService);
                }
                default -> {}
            }
        }

        if (addedServices.isEmpty()) {
            return null;
        }

        return new AppointmentActionHistory.ClientUpdateLog(addedServices);
    }

    private static List<Long> getAddedServiceIdsForAdd(CreateBookingRequestRequest.Service createServiceDetailRequest) {
        return switch (createServiceDetailRequest.getServiceCase()) {
            case GROOMING -> {
                var serviceIds = new ArrayList<Long>();
                var serviceId =
                        createServiceDetailRequest.getGrooming().getService().getServiceId();
                serviceIds.add(serviceId);
                for (var addon : createServiceDetailRequest.getGrooming().getAddonsV2List()) {
                    serviceIds.add(addon.getAddOnId());
                }
                yield serviceIds;
            }
            case BOARDING -> {
                var serviceIds = new ArrayList<Long>();
                var serviceId =
                        createServiceDetailRequest.getBoarding().getService().getServiceId();
                serviceIds.add(serviceId);
                for (var addon : createServiceDetailRequest.getBoarding().getAddonsList()) {
                    serviceIds.add(addon.getAddOnId());
                }
                yield serviceIds;
            }
            case DAYCARE -> {
                var serviceIds = new ArrayList<Long>();
                var serviceId =
                        createServiceDetailRequest.getDaycare().getService().getServiceId();
                serviceIds.add(serviceId);
                for (var addon : createServiceDetailRequest.getDaycare().getAddonsList()) {
                    serviceIds.add(addon.getAddOnId());
                }
                yield serviceIds;
            }
            case DOG_WALKING -> {
                var serviceIds = new ArrayList<Long>();
                var serviceId =
                        createServiceDetailRequest.getDogWalking().getService().getServiceId();
                serviceIds.add(serviceId);
                yield serviceIds;
            }
            case EVALUATION, GROUP_CLASS, SERVICE_NOT_SET -> List.of();
        };
    }

    private static List<Long> getAddedServiceIdsForUpdate(
            UpdateBookingRequestRequest.Service updateServiceDetailRequest) {
        return switch (updateServiceDetailRequest.getServiceCase()) {
            case GROOMING -> {
                var serviceIds = new ArrayList<Long>();
                for (var addon : updateServiceDetailRequest.getGrooming().getAddonsV2List()) {
                    if (addon.hasAdd()) {
                        serviceIds.add(addon.getAdd().getAddon().getAddOnId());
                    }
                }
                yield serviceIds;
            }
            case BOARDING -> {
                var serviceIds = new ArrayList<Long>();
                for (var addon : updateServiceDetailRequest.getBoarding().getAddonsV2List()) {
                    if (addon.hasAdd()) {
                        serviceIds.add(addon.getAdd().getAddon().getAddOnId());
                    }
                }
                yield serviceIds;
            }
            case DAYCARE -> {
                var serviceIds = new ArrayList<Long>();
                for (var addon : updateServiceDetailRequest.getDaycare().getAddonsV2List()) {
                    if (addon.hasAdd()) {
                        serviceIds.add(addon.getAdd().getAddon().getAddOnId());
                    }
                }
                yield serviceIds;
            }
            case SERVICE_NOT_SET -> List.of();
        };
    }

    private AutoRolloverLogDTO getAutoRolloverLog(Struct struct) {
        double sourceServiceId = struct.getFieldsOrDefault("sourceServiceId", Value.getDefaultInstance())
                .getNumberValue();
        double targetServiceId = struct.getFieldsOrDefault("targetServiceId", Value.getDefaultInstance())
                .getNumberValue();
        var source = isNormal(sourceServiceId)
                ? new AutoRolloverLogDTO.Service(
                        (long) sourceServiceId,
                        Optional.ofNullable(getService(sourceServiceId))
                                .map(ServiceModel::getName)
                                .orElse(null))
                : null;
        var target = isNormal(targetServiceId)
                ? new AutoRolloverLogDTO.Service(
                        (long) targetServiceId,
                        Optional.ofNullable(getService(targetServiceId))
                                .map(ServiceModel::getName)
                                .orElse(null))
                : null;
        return new AutoRolloverLogDTO(source, target);
    }

    private Map<String, Value> getActivityLogMap(List<String> activityLogIdList) {
        if (activityLogIdList.isEmpty()) {
            return Map.of();
        }
        ListActivityLogDetailsResponse response =
                activityLogServiceBlockingStub.listActivityLogDetails(ListActivityLogDetailsRequest.newBuilder()
                        .addAllIds(activityLogIdList)
                        .setQueryRoot(false)
                        .build());
        return response.getActivityLogsList().stream()
                .collect(Collectors.toMap(ActivityLogModel::getId, ActivityLogModel::getDetails));
    }

    @Nullable
    private ServiceModel getService(Number serviceId) {
        var resp = serviceStub.getServiceDetail(GetServiceDetailRequest.newBuilder()
                .setServiceId(serviceId.longValue())
                .build());
        return resp.hasService() ? resp.getService() : null;
    }

    @Nullable
    private BusinessCustomerPetInfoModel getPet(Number petId) {
        var resp = petStub.getPetInfo(
                GetPetInfoRequest.newBuilder().setId(petId.longValue()).build());
        return resp.hasPet() ? resp.getPet() : null;
    }

    @Nullable
    private BookingRequestModel getBookingRequest(Number bookingRequestId) {
        var resp = bookingRequestStub.getBookingRequest(GetBookingRequestRequest.newBuilder()
                .setId(bookingRequestId.longValue())
                .build());
        return resp.hasBookingRequest() ? resp.getBookingRequest() : null;
    }

    @Nullable
    private AppointmentModel getAppointment(Number appointmentId) {
        var resp = appointmentStub.getAppointment(GetAppointmentRequest.newBuilder()
                .setAppointmentId(appointmentId.longValue())
                .build());
        return resp.hasAppointment() ? resp.getAppointment() : null;
    }
}
