package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_gc_event
 */
public class MoeGcEvent {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   同步的预约
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     * Database Column Remarks:
     *   petDetail对应的staff
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   服务开始时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.start_time
     *
     * @mbg.generated
     */
    private Long startTime;

    /**
     * Database Column Remarks:
     *   服务结束时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.end_time
     *
     * @mbg.generated
     */
    private Long endTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.event_id
     *
     * @mbg.generated
     */
    private String eventId;

    /**
     * Database Column Remarks:
     *   同步的日历id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.gc_calendar_id
     *
     * @mbg.generated
     */
    private Integer gcCalendarId;

    /**
     * Database Column Remarks:
     *   1 正常 2删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.event_status
     *
     * @mbg.generated
     */
    private Byte eventStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_event.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.id
     *
     * @return the value of moe_gc_event.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.id
     *
     * @param id the value for moe_gc_event.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.business_id
     *
     * @return the value of moe_gc_event.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.business_id
     *
     * @param businessId the value for moe_gc_event.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.grooming_id
     *
     * @return the value of moe_gc_event.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.grooming_id
     *
     * @param groomingId the value for moe_gc_event.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.staff_id
     *
     * @return the value of moe_gc_event.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.staff_id
     *
     * @param staffId the value for moe_gc_event.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.start_time
     *
     * @return the value of moe_gc_event.start_time
     *
     * @mbg.generated
     */
    public Long getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.start_time
     *
     * @param startTime the value for moe_gc_event.start_time
     *
     * @mbg.generated
     */
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.end_time
     *
     * @return the value of moe_gc_event.end_time
     *
     * @mbg.generated
     */
    public Long getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.end_time
     *
     * @param endTime the value for moe_gc_event.end_time
     *
     * @mbg.generated
     */
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.event_id
     *
     * @return the value of moe_gc_event.event_id
     *
     * @mbg.generated
     */
    public String getEventId() {
        return eventId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.event_id
     *
     * @param eventId the value for moe_gc_event.event_id
     *
     * @mbg.generated
     */
    public void setEventId(String eventId) {
        this.eventId = eventId == null ? null : eventId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.gc_calendar_id
     *
     * @return the value of moe_gc_event.gc_calendar_id
     *
     * @mbg.generated
     */
    public Integer getGcCalendarId() {
        return gcCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.gc_calendar_id
     *
     * @param gcCalendarId the value for moe_gc_event.gc_calendar_id
     *
     * @mbg.generated
     */
    public void setGcCalendarId(Integer gcCalendarId) {
        this.gcCalendarId = gcCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.event_status
     *
     * @return the value of moe_gc_event.event_status
     *
     * @mbg.generated
     */
    public Byte getEventStatus() {
        return eventStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.event_status
     *
     * @param eventStatus the value for moe_gc_event.event_status
     *
     * @mbg.generated
     */
    public void setEventStatus(Byte eventStatus) {
        this.eventStatus = eventStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.create_time
     *
     * @return the value of moe_gc_event.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.create_time
     *
     * @param createTime the value for moe_gc_event.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.update_time
     *
     * @return the value of moe_gc_event.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.update_time
     *
     * @param updateTime the value for moe_gc_event.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_event.company_id
     *
     * @return the value of moe_gc_event.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_event.company_id
     *
     * @param companyId the value for moe_gc_event.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
