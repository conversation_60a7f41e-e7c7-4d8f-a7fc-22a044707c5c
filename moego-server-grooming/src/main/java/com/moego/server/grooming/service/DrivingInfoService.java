package com.moego.server.grooming.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.map.v1.RouteMatrixElement;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessSmartSchedulingClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.StaffSmartScheduleSettingDTO;
import com.moego.server.business.params.GetSmartScheduleSettingParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.grooming.config.DrivingInfoConfiguration;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.service.dto.BatchDrivingDisplayInfo;
import com.moego.server.grooming.service.dto.DrivingDisplayInfo;
import com.moego.server.grooming.service.ob.OBAddressService;
import com.moego.server.grooming.web.params.BatchDrivingInfoParams;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Driving info 服务
 * 根据 staff 当天的预约信息、顾客地址、ss 配置，查询计算预约与预约之间的驾驶时间、驾驶距离等信息
 *
 * <AUTHOR> chris
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrivingInfoService {

    private final MoePetDetailService petDetailService;
    private final OBAddressService obAddressService;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final IBusinessSmartSchedulingClient iBusinessSmartSchedulingClient;
    private final GoogleMapService googleMapService;

    @Autowired
    @Qualifier(value = DrivingInfoConfiguration.DRIVING_INFO_EXECUTOR_SERVICE)
    private ExecutorService executorService;

    /**
     * 查询某个员工某天的预约驾驶信息（从 MoeAppointmentQueryService 迁移过来）
     *
     * @param date 查询日期
     * @param staffId 要查询的 staffId
     * @param businessId token businessId
     * @return List<DrivingDisplayInfo>
     */
    public List<DrivingDisplayInfo> getDrivingInfo(LocalDate date, Integer staffId, Integer businessId) {
        // 查询 staff service list
        List<SmartScheduleGroomingDetailsDTO> serviceList =
                petDetailService.getApptServiceListForSmartSchedule(businessId, date, date, List.of(staffId));
        if (CollectionUtils.isEmpty(serviceList)) {
            return List.of();
        }
        // 查询 business info
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(businessId));
        // 查询 customer 地址
        Map<Integer, CustomerAddressDto> addressMap = getApptCustomerAddressMap(serviceList);
        // 查询 staff ss setting
        GetSmartScheduleSettingParams querySsParams = new GetSmartScheduleSettingParams(businessId, List.of(staffId));
        StaffSmartScheduleSettingDTO staffSsSetting = iBusinessSmartSchedulingClient
                .getStaffSmartScheduleSettingMap(querySsParams)
                .get(staffId);

        return queryOneStaffOneDayDrivingInfo(businessInfo, staffSsSetting, serviceList, addressMap);
    }

    /**
     * 批量查询多个 staff 多天的预约驾驶信息
     * 目前只支持查询 7 天内的数据，避免 Google API 调用次数过多
     *
     * @param params 查询参数
     * @return BatchDrivingDisplayInfo
     */
    public BatchDrivingDisplayInfo getDrivingInfoBatch(BatchDrivingInfoParams params) {
        LocalDate startDate = params.startDate();
        LocalDate endDate =
                Objects.nonNull(params.endDate()) ? params.endDate() : startDate; // endDate 没传时只查 startDate 当天
        if (Period.between(startDate, endDate).getDays() > 7) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Date range should be less than 7 days");
        }

        // 查询预约列表
        List<SmartScheduleGroomingDetailsDTO> serviceList = petDetailService.getApptServiceListForSmartSchedule(
                params.businessId(), startDate, endDate, params.staffIdList());
        if (CollectionUtils.isEmpty(serviceList)) {
            return new BatchDrivingDisplayInfo(List.of());
        }
        // appt 列表按 date 分组，再按 staffId 分组
        Map<String, Map<Integer, List<SmartScheduleGroomingDetailsDTO>>> dateToServiceMap = serviceList.stream()
                .collect(Collectors.groupingBy(SmartScheduleGroomingDetailsDTO::getAppointmentDate))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .collect(Collectors.groupingBy(SmartScheduleGroomingDetailsDTO::getStaffId))));

        // 查询 business info
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(params.businessId()));
        // 查询 customer 地址
        Map<Integer, CustomerAddressDto> addressMap = getApptCustomerAddressMap(serviceList);
        // 查询 staff ss setting
        GetSmartScheduleSettingParams querySsParams =
                new GetSmartScheduleSettingParams(params.businessId(), params.staffIdList());
        Map<Integer, StaffSmartScheduleSettingDTO> staffSsSettingMap =
                iBusinessSmartSchedulingClient.getStaffSmartScheduleSettingMap(querySsParams);

        Map<String, Map<Integer, List<DrivingDisplayInfo>>> resultMap = new ConcurrentHashMap<>();
        // 并发查询计算：每个日期、每个 staff 一个线程
        List<CompletableFuture<Void>> futureList = dateToServiceMap.entrySet().stream()
                .map(entry -> {
                    String date = entry.getKey();
                    Map<Integer, List<SmartScheduleGroomingDetailsDTO>> staffServiceMap = entry.getValue();
                    Map<Integer, List<DrivingDisplayInfo>> dateDrivingInfoMap =
                            resultMap.computeIfAbsent(date, k -> new ConcurrentHashMap<>());

                    return staffServiceMap.entrySet().stream()
                            .map(staffServiceEntry -> {
                                Integer staffId = staffServiceEntry.getKey();
                                List<SmartScheduleGroomingDetailsDTO> staffServiceList = staffServiceEntry.getValue();

                                return CompletableFuture.runAsync(
                                        () -> dateDrivingInfoMap.put(
                                                staffId,
                                                queryOneStaffOneDayDrivingInfo(
                                                        businessInfo,
                                                        staffSsSettingMap.get(staffId),
                                                        staffServiceList,
                                                        addressMap)),
                                        executorService);
                            })
                            .toList();
                })
                .flatMap(Collection::stream)
                .toList();
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

        // 转为 BatchDrivingDisplayInfo 对象返回
        List<BatchDrivingDisplayInfo.DateDrivingDisplayInfo> dateDrivingInfoList = resultMap.entrySet().stream()
                .map(entry -> new BatchDrivingDisplayInfo.DateDrivingDisplayInfo(
                        entry.getKey(),
                        entry.getValue().entrySet().stream()
                                .map(staffDrivingInfoEntry -> new BatchDrivingDisplayInfo.StaffDrivingDisplayInfo(
                                        staffDrivingInfoEntry.getKey(), staffDrivingInfoEntry.getValue()))
                                .toList()))
                .sorted(Comparator.comparing(BatchDrivingDisplayInfo.DateDrivingDisplayInfo::date))
                .toList();
        return new BatchDrivingDisplayInfo(dateDrivingInfoList);
    }

    /**
     * 查询预约 customer 地址
     *
     * @param serviceList 预约列表
     * @return customerId -> address
     */
    private Map<Integer, CustomerAddressDto> getApptCustomerAddressMap(
            List<SmartScheduleGroomingDetailsDTO> serviceList) {
        List<Integer> customerIds = serviceList.stream()
                .map(SmartScheduleGroomingDetailsDTO::getCustomerId)
                .filter(id -> id > 0)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, CustomerAddressDto> addressMap = new HashMap<>(16);
        // 获取所有 customer 的地址
        if (!CollectionUtils.isEmpty(customerIds)) {
            addressMap.putAll(obAddressService.batchGetPrimaryAddress(customerIds));
        }
        return addressMap;
    }

    /**
     * 查询一个 staff 一天的驾驶信息
     *
     * @param businessInfo   business setting
     * @param staffSsSetting staff ss setting
     * @param serviceList    预约列表
     * @param addressMap     customerId -> address
     * @return List<DrivingDisplayInfo>
     */
    private List<DrivingDisplayInfo> queryOneStaffOneDayDrivingInfo(
            MoeBusinessDto businessInfo,
            StaffSmartScheduleSettingDTO staffSsSetting,
            List<SmartScheduleGroomingDetailsDTO> serviceList,
            Map<Integer, CustomerAddressDto> addressMap) {
        List<TimeSlot> timeSlotList = new ArrayList<>();
        SmartScheduleGroomingDetailsDTO preService = new SmartScheduleGroomingDetailsDTO();
        preService.setGroomingId(-1);
        preService.setCustomerId(-1);
        // service list 转为 timeslot list
        for (SmartScheduleGroomingDetailsDTO curService : serviceList) {
            // block time
            if (curService.getCustomerId() == 0) {
                preService.setEndTime(curService.getEndTime());
                continue;
            }
            timeSlotList.add(buildTimeSlot(preService, curService, staffSsSetting, addressMap));
            preService = curService;
        }
        // 添加最后一个 timeslot (最后一个 timeslot 的 afterAddress 为 endLocation)
        timeSlotList.add(buildEndTimeSlot(preService, staffSsSetting, addressMap));
        return queryDrivingInfo(businessInfo, timeSlotList);
    }

    /**
     * 调用 Google API 获取驾驶信息
     *
     * @param businessInfo business setting
     * @param timeSlotList timeslot list
     * @return List<DrivingDisplayInfo>
     */
    private List<DrivingDisplayInfo> queryDrivingInfo(MoeBusinessDto businessInfo, List<TimeSlot> timeSlotList) {
        Map<TimeSlot, Future<RouteMatrixElement>> driveInMap = timeSlotList.stream()
                .filter(timeSlot -> timeSlot.isBeforeAddressValid() && timeSlot.isAfterAddressValid())
                .collect(Collectors.toMap(Function.identity(), timeSlot -> {
                    String fromLat = timeSlot.getBeforeLat();
                    String fromLng = timeSlot.getBeforeLng();
                    String toLat = timeSlot.getAfterLat();
                    String toLng = timeSlot.getAfterLng();

                    var from = GoogleMapService.toGoogleLatLng(fromLat, fromLng);
                    var to = GoogleMapService.toGoogleLatLng(toLat, toLng);

                    return executorService.submit(() -> googleMapService.queryMatrix(from, to));
                }));
        Map<Integer, DrivingDisplayInfo> drivingInfoMap = new HashMap<>(16);
        try {
            for (Map.Entry<TimeSlot, Future<RouteMatrixElement>> entry : driveInMap.entrySet()) {
                TimeSlot timeSlot = entry.getKey();
                var matrixElement = entry.getValue().get();
                log.info("matrixElement: {}", matrixElement);
                if (!GoogleMapService.isValidMatrixElement(matrixElement)) {
                    log.warn(
                            "matrixElement is null or matrixElement.duration is null, timeSlot:{} ",
                            JsonUtil.toJson(timeSlot));
                    continue;
                }
                int drivingMinutes = Math.toIntExact(matrixElement.getDuration().getSeconds() / 60);
                double drivingMiles = GoogleMapService.metersToMiles1ScaleFloor(matrixElement.getDistance());

                if (timeSlot.isDriveToEndLocation()) {
                    // 最后一个 timeslot，驾驶信息补充到 beforeApptId 对应的 timeslot 的 drivingOutMiles/drivingOutMinutes
                    DrivingDisplayInfo drivingDisplayInfo = drivingInfoMap.getOrDefault(
                            timeSlot.getBeforeApptId(),
                            buildInitDrivingInfo(businessInfo.getUnitOfDistanceType(), timeSlot.getAvailableTime()));
                    drivingInfoMap.put(
                            timeSlot.getBeforeApptId(),
                            drivingDisplayInfo.toBuilder()
                                    .groomingId(timeSlot.getBeforeApptId())
                                    .drivingOutMiles(drivingMiles)
                                    .drivingOutMinutes(drivingMinutes)
                                    .build());
                } else {
                    // 非最后一个 timeslot，驾驶信息补充到 afterApptId 对应的 timeslot 的 drivingMiles/drivingMinutes
                    DrivingDisplayInfo drivingDisplayInfo = drivingInfoMap.getOrDefault(
                            timeSlot.getAfterApptId(),
                            buildInitDrivingInfo(businessInfo.getUnitOfDistanceType(), timeSlot.getAvailableTime()));
                    drivingInfoMap.put(
                            timeSlot.getAfterApptId(),
                            drivingDisplayInfo.toBuilder()
                                    .groomingId(timeSlot.getAfterApptId())
                                    .drivingMiles(drivingMiles)
                                    .drivingMinutes(drivingMinutes)
                                    // 非 drive to end location 的 intervalTime，需要重新设置一下
                                    .intervalTime(timeSlot.getAvailableTime())
                                    .build());
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("get driving time error", e);
            throw ExceptionUtil.bizException(Code.CODE_GOOGLE_INVALID_ADDRESS, "Failed calling google api");
        }
        return new ArrayList<>(drivingInfoMap.values());
    }

    private TimeSlot buildTimeSlot(
            SmartScheduleGroomingDetailsDTO preService,
            SmartScheduleGroomingDetailsDTO curService,
            StaffSmartScheduleSettingDTO staffSsSetting,
            Map<Integer, CustomerAddressDto> addressMap) {
        TimeSlot timeSlot = TimeSlot.builder()
                .beforeApptId(preService.getGroomingId())
                .beforeCustomerId(preService.getCustomerId())
                .beforeLat(addressMap
                        .getOrDefault(preService.getCustomerId(), new CustomerAddressDto())
                        .getLat())
                .beforeLng(addressMap
                        .getOrDefault(preService.getCustomerId(), new CustomerAddressDto())
                        .getLng())
                .afterApptId(curService.getGroomingId())
                .afterCustomerId(curService.getCustomerId())
                .afterLat(addressMap
                        .getOrDefault(curService.getCustomerId(), new CustomerAddressDto())
                        .getLat())
                .afterLng(addressMap
                        .getOrDefault(curService.getCustomerId(), new CustomerAddressDto())
                        .getLng())
                .build();
        if (Objects.nonNull(preService.getEndTime())) {
            timeSlot.setAvailableTime(Math.toIntExact(curService.getStartTime() - preService.getEndTime()));
        } else {
            timeSlot.setAvailableTime(0);
        }
        if (preService.getGroomingId() == -1 && staffSsSetting != null && staffSsSetting.getLocation() != null) {
            timeSlot.setBeforeLat(staffSsSetting.getLocation().getStartLat());
            timeSlot.setBeforeLng(staffSsSetting.getLocation().getStartLng());
        }
        return timeSlot;
    }

    private TimeSlot buildEndTimeSlot(
            SmartScheduleGroomingDetailsDTO preService,
            StaffSmartScheduleSettingDTO staffSsSetting,
            Map<Integer, CustomerAddressDto> addressMap) {
        TimeSlot timeSlot = TimeSlot.builder()
                .beforeApptId(preService.getGroomingId())
                .beforeCustomerId(preService.getCustomerId())
                .beforeLat(addressMap
                        .getOrDefault(preService.getCustomerId(), new CustomerAddressDto())
                        .getLat())
                .beforeLng(addressMap
                        .getOrDefault(preService.getCustomerId(), new CustomerAddressDto())
                        .getLng())
                .driveToEndLocation(true)
                .build();
        if (staffSsSetting != null && staffSsSetting.getLocation() != null) {
            timeSlot.setAfterLat(staffSsSetting.getLocation().getEndLat());
            timeSlot.setAfterLng(staffSsSetting.getLocation().getEndLng());
        }
        return timeSlot;
    }

    private DrivingDisplayInfo buildInitDrivingInfo(Byte unitOfDistanceType, Integer availableTime) {
        return new DrivingDisplayInfo(null, null, null, unitOfDistanceType, availableTime, null, null);
    }
}
