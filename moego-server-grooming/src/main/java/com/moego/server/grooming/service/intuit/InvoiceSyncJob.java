package com.moego.server.grooming.service.intuit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceSyncJob {

    private Integer businessId;

    private Integer invoiceId;

    /**
     * 错误次数，大于3次的errorCount不执行
     */
    private Integer errorCount;

    private Long syncTime;

    public InvoiceSyncJob(Integer businessId, Integer invoiceId, Long syncTime) {
        this.businessId = businessId;
        this.invoiceId = invoiceId;
        this.errorCount = 0;
        this.syncTime = syncTime;
    }

    public InvoiceSyncJob(Integer businessId, Integer invoiceId, Long syncTime, Integer errorCount) {
        this.businessId = businessId;
        this.invoiceId = invoiceId;
        this.errorCount = errorCount;
        this.syncTime = syncTime;
    }

    public void errorCountPlus() {
        this.errorCount++;
    }
}
