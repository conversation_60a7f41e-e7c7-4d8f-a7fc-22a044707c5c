package com.moego.server.grooming.service.intuit.helper;

import com.intuit.ipp.data.Item;
import com.intuit.ipp.data.ItemTypeEnum;
import com.intuit.ipp.data.ReferenceType;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.services.DataService;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeQbSyncService;
import com.moego.server.grooming.service.intuit.utils.StringUtil;
import com.moego.server.retail.dto.PackageInfoDto;
import com.moego.server.retail.dto.ProductInfoDTO;
import java.math.BigDecimal;
import java.util.Objects;
import org.apache.commons.lang3.RandomStringUtils;

public class ServiceHelper {

    public static Item getTestItemFields(DataService service) throws FMSException {
        Item item = new Item();
        item.setName("Item" + RandomStringUtils.randomAlphanumeric(5));
        item.setActive(true);
        item.setTaxable(false);
        item.setUnitPrice(new BigDecimal("200"));
        item.setType(ItemTypeEnum.SERVICE);
        item.setTrackQtyOnHand(false);
        return item;
    }

    public static Item convertQbSyncServiceToItem(MoeQbSyncService syncService) {
        Item item = new Item();
        item.setName(syncService.getServiceName());
        item.setId(syncService.getQbServiceId());
        item.setDescription(syncService.getServiceDescription());
        return item;
    }

    public static Item getItemWithId(String qbItemId) {
        Item item = new Item();
        item.setId(qbItemId);
        return item;
    }

    public static ReferenceType getItemRef(Item item) {
        ReferenceType itemRef = new ReferenceType();
        itemRef.setName(item.getName());
        itemRef.setValue(item.getId());
        return itemRef;
    }

    public static Item convertGroomingServiceToItem(MoeGroomingService groomingService, ReferenceType accountRef) {
        Item item = new Item();
        item.setName(StringUtil.replaceInvalidString(groomingService.getName()));
        item.setDescription(groomingService.getDescription());
        item.setType(ItemTypeEnum.SERVICE);
        item.setTrackQtyOnHand(false);
        item.setUnitPrice(groomingService.getPrice());
        item.setIncomeAccountRef(accountRef);
        return item;
    }

    public static Item convertProductToItem(ProductInfoDTO product, ReferenceType accountRef) {
        Item item = new Item();
        item.setName(StringUtil.replaceInvalidString(product.getName()));
        item.setDescription(product.getDescription());
        item.setType(ItemTypeEnum.SERVICE);
        item.setTrackQtyOnHand(false);
        item.setUnitPrice(product.getRetailPrice());
        if (Objects.nonNull(product.getSpecialPrice())) {
            item.setUnitPrice(product.getSpecialPrice());
        }
        item.setIncomeAccountRef(accountRef);
        return item;
    }

    public static Item convertServiceChargeToItem(ServiceChargeDTO serviceCharge, ReferenceType accountRef) {
        Item item = new Item();
        item.setName(StringUtil.replaceInvalidString(serviceCharge.getName()));
        item.setDescription(serviceCharge.getDescription());
        item.setType(ItemTypeEnum.SERVICE);
        item.setTrackQtyOnHand(false);
        item.setUnitPrice(serviceCharge.getPrice());
        item.setIncomeAccountRef(accountRef);
        return item;
    }

    public static Item getDefaultInvoiceService(String serviceName, ReferenceType accountRef) {
        Item item = new Item();
        item.setName(StringUtil.replaceInvalidString(serviceName));
        item.setDescription(serviceName);
        item.setType(ItemTypeEnum.SERVICE);
        item.setTrackQtyOnHand(false);
        item.setUnitPrice(new BigDecimal(0));
        item.setIncomeAccountRef(accountRef);
        return item;
    }

    public static Item convertServicePackageToItem(PackageInfoDto servicePackage, ReferenceType accountRef) {
        Item item = new Item();
        item.setName(StringUtil.replaceInvalidString(servicePackage.getName()));
        item.setDescription(servicePackage.getDescription());
        item.setType(ItemTypeEnum.SERVICE);
        item.setTrackQtyOnHand(false);
        item.setUnitPrice(servicePackage.getPrice());
        item.setIncomeAccountRef(accountRef);
        return item;
    }
}
