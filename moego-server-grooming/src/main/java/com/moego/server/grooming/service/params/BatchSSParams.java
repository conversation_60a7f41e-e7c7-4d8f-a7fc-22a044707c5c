package com.moego.server.grooming.service.params;

import com.moego.server.business.dto.StaffSmartScheduleSettingDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.LocationParams;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class BatchSSParams {
    Integer businessId;
    Boolean isOB;
    List<Integer> staffIdList;

    // 商家 cacd、ss 开关
    Boolean isCACDEnable;
    Boolean isSmartScheduleEnable;

    // ss enable 时有效
    Map<Integer, StaffSmartScheduleSettingDTO>
            staffSsSettingMap; // 商家 ss setting 设置， <staffId, StaffSmartScheduleSettingDTO>

    // ss enable 或 cacd enable 时有效
    Map<Long, LocationParams> locations; // <locationId, LocationParams>

    // 待计算日期范围
    LocalDate startDate;
    LocalDate endDate;

    // 待计算日期范围内，已有预约信息
    List<SmartScheduleGroomingDetailsDTO> existPetDetailInfoList; // 已有预约信息
    Map<Integer, CustomerAddressDto> existAppointmentCustomerAddress; // 已有预约的客户地址 <customerId, CustomerAddressDto>

    // 待计算日期范围内，职工工作时间、地点
    Map<String, Map<Integer, List<TimeRangeDto>>>
            staffPerDayWorkingTime; // 职工工作时间 <date, <staffId, List<TimeRangeDto>>>
    Map<Long, Map<String, Map<Integer, List<TimeRangeDto>>>>
            cacdWorkingTime; // cacd 工作时间 <locationId, <date, <staffId, List<TimeRangeDto>>>>
}
