<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeWaitListMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeWaitList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="appointment_id" jdbcType="BIGINT" property="appointmentId" />
    <result column="date_preference" jdbcType="CHAR" property="datePreference" typeHandler="com.moego.server.grooming.mapper.typehandler.DatePreferenceHandler" />
    <result column="time_preference" jdbcType="CHAR" property="timePreference" typeHandler="com.moego.server.grooming.mapper.typehandler.TimePreferenceHandler" />
    <result column="staff_preference" jdbcType="CHAR" property="staffPreference" typeHandler="com.moego.server.grooming.mapper.typehandler.StaffPreferenceHandler" />
    <result column="valid_from" jdbcType="DATE" property="validFrom" />
    <result column="valid_till" jdbcType="DATE" property="validTill" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, company_id, appointment_id, date_preference, time_preference, staff_preference,
    valid_from, valid_till, created_at, created_by, updated_at, updated_by, deleted_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_wait_list
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_wait_list
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeWaitList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_wait_list (business_id, company_id, appointment_id,
      date_preference,
      time_preference,
      staff_preference,
      valid_from, valid_till, created_at,
      created_by, updated_at, updated_by,
      deleted_at)
    values (#{businessId,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{appointmentId,jdbcType=BIGINT},
      #{datePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.DatePreferenceHandler},
      #{timePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.TimePreferenceHandler},
      #{staffPreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StaffPreferenceHandler},
      #{validFrom,jdbcType=DATE}, #{validTill,jdbcType=DATE}, #{createdAt,jdbcType=TIMESTAMP},
      #{createdBy,jdbcType=BIGINT}, #{updatedAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=BIGINT},
      #{deletedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeWaitList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_wait_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="appointmentId != null">
        appointment_id,
      </if>
      <if test="datePreference != null">
        date_preference,
      </if>
      <if test="timePreference != null">
        time_preference,
      </if>
      <if test="staffPreference != null">
        staff_preference,
      </if>
      <if test="validFrom != null">
        valid_from,
      </if>
      <if test="validTill != null">
        valid_till,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="deletedAt != null">
        deleted_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appointmentId != null">
        #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="datePreference != null">
        #{datePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.DatePreferenceHandler},
      </if>
      <if test="timePreference != null">
        #{timePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.TimePreferenceHandler},
      </if>
      <if test="staffPreference != null">
        #{staffPreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StaffPreferenceHandler},
      </if>
      <if test="validFrom != null">
        #{validFrom,jdbcType=DATE},
      </if>
      <if test="validTill != null">
        #{validTill,jdbcType=DATE},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="deletedAt != null">
        #{deletedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeWaitList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_wait_list
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appointmentId != null">
        appointment_id = #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="datePreference != null">
        date_preference = #{datePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.DatePreferenceHandler},
      </if>
      <if test="timePreference != null">
        time_preference = #{timePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.TimePreferenceHandler},
      </if>
      <if test="staffPreference != null">
        staff_preference = #{staffPreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StaffPreferenceHandler},
      </if>
      <if test="validFrom != null">
        valid_from = #{validFrom,jdbcType=DATE},
      </if>
      <if test="validTill != null">
        valid_till = #{validTill,jdbcType=DATE},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="deletedAt != null">
        deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeWaitList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_wait_list
    set business_id = #{businessId,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      appointment_id = #{appointmentId,jdbcType=BIGINT},
      date_preference = #{datePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.DatePreferenceHandler},
      time_preference = #{timePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.TimePreferenceHandler},
      staff_preference = #{staffPreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StaffPreferenceHandler},
      valid_from = #{validFrom,jdbcType=DATE},
      valid_till = #{validTill,jdbcType=DATE},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      deleted_at = #{deletedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_wait_list
    where id = #{id,jdbcType=BIGINT} and business_id = #{businessId,jdbcType=BIGINT} and deleted_at is null
  </select>

  <select id="selectByAppointmentId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_wait_list
    where appointment_id = #{appointmentId,jdbcType=BIGINT} and business_id = #{businessId,jdbcType=BIGINT} and deleted_at is null
  </select>

  <select id="selectByAppointmentIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_wait_list
    where business_id = #{businessId,jdbcType=BIGINT}
    and appointment_id in
    <foreach collection="appointmentIds" item="appointmentId" open="(" close=")" separator=",">
      #{appointmentId,jdbcType=BIGINT}
    </foreach>
    and deleted_at is null
  </select>
  <select id="selectByAppointmentIdsV2" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_wait_list
    where appointment_id in
    <foreach collection="appointmentIds" item="appointmentId" open="(" close=")" separator=",">
      #{appointmentId,jdbcType=BIGINT}
    </foreach>
    and deleted_at is null
  </select>

  <select id="query" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_wait_list
    where deleted_at is null
    and company_id = #{companyId,jdbcType=BIGINT}
    <if test="businessId != null">
      and business_id = #{businessId,jdbcType=BIGINT}
    </if>
    <if test="validTillGte != null">
      and valid_till >= #{validTillGte,jdbcType=DATE}
    </if>
    <if test="validTillLt != null">
      and valid_till &lt; #{validTillLt,jdbcType=DATE}
    </if>
    <if test="createGte != null">
      and created_at >= #{createGte,jdbcType=TIMESTAMP}
    </if>
    <if test="createLte != null">
      and created_at &lt;= #{createLte,jdbcType=TIMESTAMP}
    </if>
    <if test="order != null">
      order by ${order}
    </if>
  </select>

  <update id="updateById" parameterType="com.moego.server.grooming.mapperbean.MoeWaitList">
    update moe_wait_list
    <set>
      <if test="datePreference != null">
        date_preference = #{datePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.DatePreferenceHandler},
      </if>
      <if test="timePreference != null">
        time_preference = #{timePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.TimePreferenceHandler},
      </if>
      <if test="staffPreference != null">
        staff_preference = #{staffPreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StaffPreferenceHandler},
      </if>
      <if test="validFrom != null">
        valid_from = #{validFrom,jdbcType=DATE},
      </if>
      <if test="validTill != null">
        valid_till = #{validTill,jdbcType=DATE},
      </if>
      updated_by =  #{updatedBy,jdbcType=BIGINT},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
    </set>
    where id = #{id,jdbcType=BIGINT} and business_id = #{businessId,jdbcType=BIGINT} and deleted_at is null
  </update>

  <update id="deleteById">
    update moe_wait_list
    set deleted_at = #{deletedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT} and business_id = #{businessId,jdbcType=INTEGER}
  </update>

  <update id="deleteByAppointmentId">
    update moe_wait_list
    set deleted_at = #{deletedAt,jdbcType=TIMESTAMP}
    where appointment_id in
    <foreach collection="appointmentIds" item="appointmentId" open="(" close=")" separator=",">
      #{appointmentId,jdbcType=BIGINT}
    </foreach>
    <if test="companyId != null">
      and company_id = #{companyId,jdbcType=BIGINT}
    </if>
    <if test="businessId != null">
      and business_id = #{businessId,jdbcType=INTEGER}
    </if>
    and deleted_at is null
  </update>

  <insert id="batchInsertRecords" useGeneratedKeys="true">
    insert into moe_wait_list (business_id, company_id, appointment_id,
    date_preference,
    time_preference,
    staff_preference,
    valid_from, valid_till, created_at,
    created_by, updated_at, updated_by)
    values
    <foreach collection="records" item="record" separator=",">
      (#{record.businessId,jdbcType=BIGINT}, #{record.companyId,jdbcType=BIGINT}, #{record.appointmentId,jdbcType=BIGINT},
      #{record.datePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.DatePreferenceHandler},
      #{record.timePreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.TimePreferenceHandler},
      #{record.staffPreference,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StaffPreferenceHandler},
      #{record.validFrom,jdbcType=DATE}, #{record.validTill,jdbcType=DATE}, #{record.createdAt,jdbcType=TIMESTAMP},
      #{record.createdBy,jdbcType=BIGINT}, #{record.updatedAt,jdbcType=TIMESTAMP}, #{record.updatedBy,jdbcType=BIGINT})
    </foreach>
  </insert>
</mapper>
