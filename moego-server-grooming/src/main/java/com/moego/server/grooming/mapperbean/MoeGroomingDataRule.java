package com.moego.server.grooming.mapperbean;

public class MoeGroomingDataRule {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.client_full_name
     *
     * @mbg.generated
     */
    private Byte clientFullName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.pet_name_breed
     *
     * @mbg.generated
     */
    private Byte petNameBreed;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.service_name
     *
     * @mbg.generated
     */
    private Byte serviceName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.service_price
     *
     * @mbg.generated
     */
    private Byte servicePrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.client_full_address
     *
     * @mbg.generated
     */
    private Byte clientFullAddress;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.city
     *
     * @mbg.generated
     */
    private Byte city;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.zipcode
     *
     * @mbg.generated
     */
    private Byte zipcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.ticket_comments
     *
     * @mbg.generated
     */
    private Byte ticketComments;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.alert_notes
     *
     * @mbg.generated
     */
    private Byte alertNotes;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.client_phone_number
     *
     * @mbg.generated
     */
    private Byte clientPhoneNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.monthly_view_customization
     *
     * @mbg.generated
     */
    private String monthlyViewCustomization;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_data_rule.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.id
     *
     * @return the value of moe_grooming_data_rule.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.id
     *
     * @param id the value for moe_grooming_data_rule.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.business_id
     *
     * @return the value of moe_grooming_data_rule.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.business_id
     *
     * @param businessId the value for moe_grooming_data_rule.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.client_full_name
     *
     * @return the value of moe_grooming_data_rule.client_full_name
     *
     * @mbg.generated
     */
    public Byte getClientFullName() {
        return clientFullName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.client_full_name
     *
     * @param clientFullName the value for moe_grooming_data_rule.client_full_name
     *
     * @mbg.generated
     */
    public void setClientFullName(Byte clientFullName) {
        this.clientFullName = clientFullName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.pet_name_breed
     *
     * @return the value of moe_grooming_data_rule.pet_name_breed
     *
     * @mbg.generated
     */
    public Byte getPetNameBreed() {
        return petNameBreed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.pet_name_breed
     *
     * @param petNameBreed the value for moe_grooming_data_rule.pet_name_breed
     *
     * @mbg.generated
     */
    public void setPetNameBreed(Byte petNameBreed) {
        this.petNameBreed = petNameBreed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.service_name
     *
     * @return the value of moe_grooming_data_rule.service_name
     *
     * @mbg.generated
     */
    public Byte getServiceName() {
        return serviceName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.service_name
     *
     * @param serviceName the value for moe_grooming_data_rule.service_name
     *
     * @mbg.generated
     */
    public void setServiceName(Byte serviceName) {
        this.serviceName = serviceName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.service_price
     *
     * @return the value of moe_grooming_data_rule.service_price
     *
     * @mbg.generated
     */
    public Byte getServicePrice() {
        return servicePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.service_price
     *
     * @param servicePrice the value for moe_grooming_data_rule.service_price
     *
     * @mbg.generated
     */
    public void setServicePrice(Byte servicePrice) {
        this.servicePrice = servicePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.client_full_address
     *
     * @return the value of moe_grooming_data_rule.client_full_address
     *
     * @mbg.generated
     */
    public Byte getClientFullAddress() {
        return clientFullAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.client_full_address
     *
     * @param clientFullAddress the value for moe_grooming_data_rule.client_full_address
     *
     * @mbg.generated
     */
    public void setClientFullAddress(Byte clientFullAddress) {
        this.clientFullAddress = clientFullAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.city
     *
     * @return the value of moe_grooming_data_rule.city
     *
     * @mbg.generated
     */
    public Byte getCity() {
        return city;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.city
     *
     * @param city the value for moe_grooming_data_rule.city
     *
     * @mbg.generated
     */
    public void setCity(Byte city) {
        this.city = city;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.zipcode
     *
     * @return the value of moe_grooming_data_rule.zipcode
     *
     * @mbg.generated
     */
    public Byte getZipcode() {
        return zipcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.zipcode
     *
     * @param zipcode the value for moe_grooming_data_rule.zipcode
     *
     * @mbg.generated
     */
    public void setZipcode(Byte zipcode) {
        this.zipcode = zipcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.ticket_comments
     *
     * @return the value of moe_grooming_data_rule.ticket_comments
     *
     * @mbg.generated
     */
    public Byte getTicketComments() {
        return ticketComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.ticket_comments
     *
     * @param ticketComments the value for moe_grooming_data_rule.ticket_comments
     *
     * @mbg.generated
     */
    public void setTicketComments(Byte ticketComments) {
        this.ticketComments = ticketComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.alert_notes
     *
     * @return the value of moe_grooming_data_rule.alert_notes
     *
     * @mbg.generated
     */
    public Byte getAlertNotes() {
        return alertNotes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.alert_notes
     *
     * @param alertNotes the value for moe_grooming_data_rule.alert_notes
     *
     * @mbg.generated
     */
    public void setAlertNotes(Byte alertNotes) {
        this.alertNotes = alertNotes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.client_phone_number
     *
     * @return the value of moe_grooming_data_rule.client_phone_number
     *
     * @mbg.generated
     */
    public Byte getClientPhoneNumber() {
        return clientPhoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.client_phone_number
     *
     * @param clientPhoneNumber the value for moe_grooming_data_rule.client_phone_number
     *
     * @mbg.generated
     */
    public void setClientPhoneNumber(Byte clientPhoneNumber) {
        this.clientPhoneNumber = clientPhoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.monthly_view_customization
     *
     * @return the value of moe_grooming_data_rule.monthly_view_customization
     *
     * @mbg.generated
     */
    public String getMonthlyViewCustomization() {
        return monthlyViewCustomization;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.monthly_view_customization
     *
     * @param monthlyViewCustomization the value for moe_grooming_data_rule.monthly_view_customization
     *
     * @mbg.generated
     */
    public void setMonthlyViewCustomization(String monthlyViewCustomization) {
        this.monthlyViewCustomization = monthlyViewCustomization == null ? null : monthlyViewCustomization.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.create_time
     *
     * @return the value of moe_grooming_data_rule.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.create_time
     *
     * @param createTime the value for moe_grooming_data_rule.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_data_rule.update_time
     *
     * @return the value of moe_grooming_data_rule.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_data_rule.update_time
     *
     * @param updateTime the value for moe_grooming_data_rule.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
