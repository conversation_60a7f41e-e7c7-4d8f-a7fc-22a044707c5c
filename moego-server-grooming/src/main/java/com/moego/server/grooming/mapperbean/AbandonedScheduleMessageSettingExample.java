package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AbandonedScheduleMessageSettingExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public AbandonedScheduleMessageSettingExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andClientTypesIsNull() {
            addCriterion("client_types is null");
            return (Criteria) this;
        }

        public Criteria andClientTypesIsNotNull() {
            addCriterion("client_types is not null");
            return (Criteria) this;
        }

        public Criteria andClientTypesEqualTo(String value) {
            addCriterion("client_types =", value, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesNotEqualTo(String value) {
            addCriterion("client_types <>", value, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesGreaterThan(String value) {
            addCriterion("client_types >", value, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesGreaterThanOrEqualTo(String value) {
            addCriterion("client_types >=", value, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesLessThan(String value) {
            addCriterion("client_types <", value, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesLessThanOrEqualTo(String value) {
            addCriterion("client_types <=", value, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesLike(String value) {
            addCriterion("client_types like", value, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesNotLike(String value) {
            addCriterion("client_types not like", value, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesIn(List<String> values) {
            addCriterion("client_types in", values, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesNotIn(List<String> values) {
            addCriterion("client_types not in", values, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesBetween(String value1, String value2) {
            addCriterion("client_types between", value1, value2, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andClientTypesNotBetween(String value1, String value2) {
            addCriterion("client_types not between", value1, value2, "clientTypes");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsIsNull() {
            addCriterion("abandoned_steps is null");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsIsNotNull() {
            addCriterion("abandoned_steps is not null");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsEqualTo(String value) {
            addCriterion("abandoned_steps =", value, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsNotEqualTo(String value) {
            addCriterion("abandoned_steps <>", value, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsGreaterThan(String value) {
            addCriterion("abandoned_steps >", value, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsGreaterThanOrEqualTo(String value) {
            addCriterion("abandoned_steps >=", value, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsLessThan(String value) {
            addCriterion("abandoned_steps <", value, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsLessThanOrEqualTo(String value) {
            addCriterion("abandoned_steps <=", value, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsLike(String value) {
            addCriterion("abandoned_steps like", value, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsNotLike(String value) {
            addCriterion("abandoned_steps not like", value, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsIn(List<String> values) {
            addCriterion("abandoned_steps in", values, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsNotIn(List<String> values) {
            addCriterion("abandoned_steps not in", values, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsBetween(String value1, String value2) {
            addCriterion("abandoned_steps between", value1, value2, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andAbandonedStepsNotBetween(String value1, String value2) {
            addCriterion("abandoned_steps not between", value1, value2, "abandonedSteps");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeIsNull() {
            addCriterion("send_out_type is null");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeIsNotNull() {
            addCriterion("send_out_type is not null");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeEqualTo(String value) {
            addCriterion("send_out_type =", value, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeNotEqualTo(String value) {
            addCriterion("send_out_type <>", value, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeGreaterThan(String value) {
            addCriterion("send_out_type >", value, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeGreaterThanOrEqualTo(String value) {
            addCriterion("send_out_type >=", value, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeLessThan(String value) {
            addCriterion("send_out_type <", value, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeLessThanOrEqualTo(String value) {
            addCriterion("send_out_type <=", value, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeLike(String value) {
            addCriterion("send_out_type like", value, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeNotLike(String value) {
            addCriterion("send_out_type not like", value, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeIn(List<String> values) {
            addCriterion("send_out_type in", values, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeNotIn(List<String> values) {
            addCriterion("send_out_type not in", values, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeBetween(String value1, String value2) {
            addCriterion("send_out_type between", value1, value2, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andSendOutTypeNotBetween(String value1, String value2) {
            addCriterion("send_out_type not between", value1, value2, "sendOutType");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysIsNull() {
            addCriterion("on_type_days is null");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysIsNotNull() {
            addCriterion("on_type_days is not null");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysEqualTo(String value) {
            addCriterion("on_type_days =", value, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysNotEqualTo(String value) {
            addCriterion("on_type_days <>", value, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysGreaterThan(String value) {
            addCriterion("on_type_days >", value, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysGreaterThanOrEqualTo(String value) {
            addCriterion("on_type_days >=", value, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysLessThan(String value) {
            addCriterion("on_type_days <", value, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysLessThanOrEqualTo(String value) {
            addCriterion("on_type_days <=", value, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysLike(String value) {
            addCriterion("on_type_days like", value, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysNotLike(String value) {
            addCriterion("on_type_days not like", value, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysIn(List<String> values) {
            addCriterion("on_type_days in", values, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysNotIn(List<String> values) {
            addCriterion("on_type_days not in", values, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysBetween(String value1, String value2) {
            addCriterion("on_type_days between", value1, value2, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeDaysNotBetween(String value1, String value2) {
            addCriterion("on_type_days not between", value1, value2, "onTypeDays");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteIsNull() {
            addCriterion("on_type_minute is null");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteIsNotNull() {
            addCriterion("on_type_minute is not null");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteEqualTo(Integer value) {
            addCriterion("on_type_minute =", value, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteNotEqualTo(Integer value) {
            addCriterion("on_type_minute <>", value, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteGreaterThan(Integer value) {
            addCriterion("on_type_minute >", value, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteGreaterThanOrEqualTo(Integer value) {
            addCriterion("on_type_minute >=", value, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteLessThan(Integer value) {
            addCriterion("on_type_minute <", value, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteLessThanOrEqualTo(Integer value) {
            addCriterion("on_type_minute <=", value, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteIn(List<Integer> values) {
            addCriterion("on_type_minute in", values, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteNotIn(List<Integer> values) {
            addCriterion("on_type_minute not in", values, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteBetween(Integer value1, Integer value2) {
            addCriterion("on_type_minute between", value1, value2, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andOnTypeMinuteNotBetween(Integer value1, Integer value2) {
            addCriterion("on_type_minute not between", value1, value2, "onTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteIsNull() {
            addCriterion("wait_for_type_minute is null");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteIsNotNull() {
            addCriterion("wait_for_type_minute is not null");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteEqualTo(Integer value) {
            addCriterion("wait_for_type_minute =", value, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteNotEqualTo(Integer value) {
            addCriterion("wait_for_type_minute <>", value, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteGreaterThan(Integer value) {
            addCriterion("wait_for_type_minute >", value, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteGreaterThanOrEqualTo(Integer value) {
            addCriterion("wait_for_type_minute >=", value, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteLessThan(Integer value) {
            addCriterion("wait_for_type_minute <", value, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteLessThanOrEqualTo(Integer value) {
            addCriterion("wait_for_type_minute <=", value, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteIn(List<Integer> values) {
            addCriterion("wait_for_type_minute in", values, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteNotIn(List<Integer> values) {
            addCriterion("wait_for_type_minute not in", values, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteBetween(Integer value1, Integer value2) {
            addCriterion("wait_for_type_minute between", value1, value2, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andWaitForTypeMinuteNotBetween(Integer value1, Integer value2) {
            addCriterion("wait_for_type_minute not between", value1, value2, "waitForTypeMinute");
            return (Criteria) this;
        }

        public Criteria andMessageIsNull() {
            addCriterion("message is null");
            return (Criteria) this;
        }

        public Criteria andMessageIsNotNull() {
            addCriterion("message is not null");
            return (Criteria) this;
        }

        public Criteria andMessageEqualTo(String value) {
            addCriterion("message =", value, "message");
            return (Criteria) this;
        }

        public Criteria andMessageNotEqualTo(String value) {
            addCriterion("message <>", value, "message");
            return (Criteria) this;
        }

        public Criteria andMessageGreaterThan(String value) {
            addCriterion("message >", value, "message");
            return (Criteria) this;
        }

        public Criteria andMessageGreaterThanOrEqualTo(String value) {
            addCriterion("message >=", value, "message");
            return (Criteria) this;
        }

        public Criteria andMessageLessThan(String value) {
            addCriterion("message <", value, "message");
            return (Criteria) this;
        }

        public Criteria andMessageLessThanOrEqualTo(String value) {
            addCriterion("message <=", value, "message");
            return (Criteria) this;
        }

        public Criteria andMessageLike(String value) {
            addCriterion("message like", value, "message");
            return (Criteria) this;
        }

        public Criteria andMessageNotLike(String value) {
            addCriterion("message not like", value, "message");
            return (Criteria) this;
        }

        public Criteria andMessageIn(List<String> values) {
            addCriterion("message in", values, "message");
            return (Criteria) this;
        }

        public Criteria andMessageNotIn(List<String> values) {
            addCriterion("message not in", values, "message");
            return (Criteria) this;
        }

        public Criteria andMessageBetween(String value1, String value2) {
            addCriterion("message between", value1, value2, "message");
            return (Criteria) this;
        }

        public Criteria andMessageNotBetween(String value1, String value2) {
            addCriterion("message not between", value1, value2, "message");
            return (Criteria) this;
        }

        public Criteria andIsEnabledIsNull() {
            addCriterion("is_enabled is null");
            return (Criteria) this;
        }

        public Criteria andIsEnabledIsNotNull() {
            addCriterion("is_enabled is not null");
            return (Criteria) this;
        }

        public Criteria andIsEnabledEqualTo(Boolean value) {
            addCriterion("is_enabled =", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledNotEqualTo(Boolean value) {
            addCriterion("is_enabled <>", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledGreaterThan(Boolean value) {
            addCriterion("is_enabled >", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_enabled >=", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledLessThan(Boolean value) {
            addCriterion("is_enabled <", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledLessThanOrEqualTo(Boolean value) {
            addCriterion("is_enabled <=", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledIn(List<Boolean> values) {
            addCriterion("is_enabled in", values, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledNotIn(List<Boolean> values) {
            addCriterion("is_enabled not in", values, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledBetween(Boolean value1, Boolean value2) {
            addCriterion("is_enabled between", value1, value2, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_enabled not between", value1, value2, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtIsNull() {
            addCriterion("enabled_at is null");
            return (Criteria) this;
        }

        public Criteria andEnabledAtIsNotNull() {
            addCriterion("enabled_at is not null");
            return (Criteria) this;
        }

        public Criteria andEnabledAtEqualTo(Date value) {
            addCriterion("enabled_at =", value, "enabledAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtNotEqualTo(Date value) {
            addCriterion("enabled_at <>", value, "enabledAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtGreaterThan(Date value) {
            addCriterion("enabled_at >", value, "enabledAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtGreaterThanOrEqualTo(Date value) {
            addCriterion("enabled_at >=", value, "enabledAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtLessThan(Date value) {
            addCriterion("enabled_at <", value, "enabledAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtLessThanOrEqualTo(Date value) {
            addCriterion("enabled_at <=", value, "enabledAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtIn(List<Date> values) {
            addCriterion("enabled_at in", values, "enabledAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtNotIn(List<Date> values) {
            addCriterion("enabled_at not in", values, "enabledAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtBetween(Date value1, Date value2) {
            addCriterion("enabled_at between", value1, value2, "enabledAt");
            return (Criteria) this;
        }

        public Criteria andEnabledAtNotBetween(Date value1, Date value2) {
            addCriterion("enabled_at not between", value1, value2, "enabledAt");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
