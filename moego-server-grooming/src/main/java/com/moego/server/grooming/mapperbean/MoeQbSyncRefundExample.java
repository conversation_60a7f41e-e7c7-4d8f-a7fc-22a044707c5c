package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.List;

public class MoeQbSyncRefundExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public MoeQbSyncRefundExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andConnectIdIsNull() {
            addCriterion("connect_id is null");
            return (Criteria) this;
        }

        public Criteria andConnectIdIsNotNull() {
            addCriterion("connect_id is not null");
            return (Criteria) this;
        }

        public Criteria andConnectIdEqualTo(Integer value) {
            addCriterion("connect_id =", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotEqualTo(Integer value) {
            addCriterion("connect_id <>", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdGreaterThan(Integer value) {
            addCriterion("connect_id >", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("connect_id >=", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdLessThan(Integer value) {
            addCriterion("connect_id <", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdLessThanOrEqualTo(Integer value) {
            addCriterion("connect_id <=", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdIn(List<Integer> values) {
            addCriterion("connect_id in", values, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotIn(List<Integer> values) {
            addCriterion("connect_id not in", values, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdBetween(Integer value1, Integer value2) {
            addCriterion("connect_id between", value1, value2, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotBetween(Integer value1, Integer value2) {
            addCriterion("connect_id not between", value1, value2, "connectId");
            return (Criteria) this;
        }

        public Criteria andRealmIdIsNull() {
            addCriterion("realm_id is null");
            return (Criteria) this;
        }

        public Criteria andRealmIdIsNotNull() {
            addCriterion("realm_id is not null");
            return (Criteria) this;
        }

        public Criteria andRealmIdEqualTo(String value) {
            addCriterion("realm_id =", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotEqualTo(String value) {
            addCriterion("realm_id <>", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdGreaterThan(String value) {
            addCriterion("realm_id >", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdGreaterThanOrEqualTo(String value) {
            addCriterion("realm_id >=", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdLessThan(String value) {
            addCriterion("realm_id <", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdLessThanOrEqualTo(String value) {
            addCriterion("realm_id <=", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdLike(String value) {
            addCriterion("realm_id like", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotLike(String value) {
            addCriterion("realm_id not like", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdIn(List<String> values) {
            addCriterion("realm_id in", values, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotIn(List<String> values) {
            addCriterion("realm_id not in", values, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdBetween(String value1, String value2) {
            addCriterion("realm_id between", value1, value2, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotBetween(String value1, String value2) {
            addCriterion("realm_id not between", value1, value2, "realmId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdIsNull() {
            addCriterion("payment_id is null");
            return (Criteria) this;
        }

        public Criteria andPaymentIdIsNotNull() {
            addCriterion("payment_id is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentIdEqualTo(Integer value) {
            addCriterion("payment_id =", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotEqualTo(Integer value) {
            addCriterion("payment_id <>", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdGreaterThan(Integer value) {
            addCriterion("payment_id >", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_id >=", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdLessThan(Integer value) {
            addCriterion("payment_id <", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdLessThanOrEqualTo(Integer value) {
            addCriterion("payment_id <=", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdIn(List<Integer> values) {
            addCriterion("payment_id in", values, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotIn(List<Integer> values) {
            addCriterion("payment_id not in", values, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdBetween(Integer value1, Integer value2) {
            addCriterion("payment_id between", value1, value2, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_id not between", value1, value2, "paymentId");
            return (Criteria) this;
        }

        public Criteria andRefundIdIsNull() {
            addCriterion("refund_id is null");
            return (Criteria) this;
        }

        public Criteria andRefundIdIsNotNull() {
            addCriterion("refund_id is not null");
            return (Criteria) this;
        }

        public Criteria andRefundIdEqualTo(Integer value) {
            addCriterion("refund_id =", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdNotEqualTo(Integer value) {
            addCriterion("refund_id <>", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdGreaterThan(Integer value) {
            addCriterion("refund_id >", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("refund_id >=", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdLessThan(Integer value) {
            addCriterion("refund_id <", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdLessThanOrEqualTo(Integer value) {
            addCriterion("refund_id <=", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdIn(List<Integer> values) {
            addCriterion("refund_id in", values, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdNotIn(List<Integer> values) {
            addCriterion("refund_id not in", values, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdBetween(Integer value1, Integer value2) {
            addCriterion("refund_id between", value1, value2, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdNotBetween(Integer value1, Integer value2) {
            addCriterion("refund_id not between", value1, value2, "refundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdIsNull() {
            addCriterion("qb_refund_id is null");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdIsNotNull() {
            addCriterion("qb_refund_id is not null");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdEqualTo(String value) {
            addCriterion("qb_refund_id =", value, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdNotEqualTo(String value) {
            addCriterion("qb_refund_id <>", value, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdGreaterThan(String value) {
            addCriterion("qb_refund_id >", value, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdGreaterThanOrEqualTo(String value) {
            addCriterion("qb_refund_id >=", value, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdLessThan(String value) {
            addCriterion("qb_refund_id <", value, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdLessThanOrEqualTo(String value) {
            addCriterion("qb_refund_id <=", value, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdLike(String value) {
            addCriterion("qb_refund_id like", value, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdNotLike(String value) {
            addCriterion("qb_refund_id not like", value, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdIn(List<String> values) {
            addCriterion("qb_refund_id in", values, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdNotIn(List<String> values) {
            addCriterion("qb_refund_id not in", values, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdBetween(String value1, String value2) {
            addCriterion("qb_refund_id between", value1, value2, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andQbRefundIdNotBetween(String value1, String value2) {
            addCriterion("qb_refund_id not between", value1, value2, "qbRefundId");
            return (Criteria) this;
        }

        public Criteria andRefundNameIsNull() {
            addCriterion("refund_name is null");
            return (Criteria) this;
        }

        public Criteria andRefundNameIsNotNull() {
            addCriterion("refund_name is not null");
            return (Criteria) this;
        }

        public Criteria andRefundNameEqualTo(String value) {
            addCriterion("refund_name =", value, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameNotEqualTo(String value) {
            addCriterion("refund_name <>", value, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameGreaterThan(String value) {
            addCriterion("refund_name >", value, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameGreaterThanOrEqualTo(String value) {
            addCriterion("refund_name >=", value, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameLessThan(String value) {
            addCriterion("refund_name <", value, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameLessThanOrEqualTo(String value) {
            addCriterion("refund_name <=", value, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameLike(String value) {
            addCriterion("refund_name like", value, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameNotLike(String value) {
            addCriterion("refund_name not like", value, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameIn(List<String> values) {
            addCriterion("refund_name in", values, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameNotIn(List<String> values) {
            addCriterion("refund_name not in", values, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameBetween(String value1, String value2) {
            addCriterion("refund_name between", value1, value2, "refundName");
            return (Criteria) this;
        }

        public Criteria andRefundNameNotBetween(String value1, String value2) {
            addCriterion("refund_name not between", value1, value2, "refundName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
