package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_addon_applicable_service
 */
public class MoeGroomingAddonApplicableService {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_addon_applicable_service.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_addon_applicable_service.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   moe_grooming_service.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_addon_applicable_service.addon_id
     *
     * @mbg.generated
     */
    private Long addonId;

    /**
     * Database Column Remarks:
     *   1 - grooming, 2 - boarding, 3 - daycare
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_addon_applicable_service.service_item_type
     *
     * @mbg.generated
     */
    private Integer serviceItemType;

    /**
     * Database Column Remarks:
     *   false for all service in service_item_type, true for specific service
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_addon_applicable_service.available_for_all_services
     *
     * @mbg.generated
     */
    private Boolean availableForAllServices;

    /**
     * Database Column Remarks:
     *   customized applicable service list, only when service_filter is true
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_addon_applicable_service.available_service_id_list
     *
     * @mbg.generated
     */
    private String availableServiceIdList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_addon_applicable_service.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_addon_applicable_service.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_addon_applicable_service.id
     *
     * @return the value of moe_grooming_addon_applicable_service.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_addon_applicable_service.id
     *
     * @param id the value for moe_grooming_addon_applicable_service.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_addon_applicable_service.company_id
     *
     * @return the value of moe_grooming_addon_applicable_service.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_addon_applicable_service.company_id
     *
     * @param companyId the value for moe_grooming_addon_applicable_service.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_addon_applicable_service.addon_id
     *
     * @return the value of moe_grooming_addon_applicable_service.addon_id
     *
     * @mbg.generated
     */
    public Long getAddonId() {
        return addonId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_addon_applicable_service.addon_id
     *
     * @param addonId the value for moe_grooming_addon_applicable_service.addon_id
     *
     * @mbg.generated
     */
    public void setAddonId(Long addonId) {
        this.addonId = addonId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_addon_applicable_service.service_item_type
     *
     * @return the value of moe_grooming_addon_applicable_service.service_item_type
     *
     * @mbg.generated
     */
    public Integer getServiceItemType() {
        return serviceItemType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_addon_applicable_service.service_item_type
     *
     * @param serviceItemType the value for moe_grooming_addon_applicable_service.service_item_type
     *
     * @mbg.generated
     */
    public void setServiceItemType(Integer serviceItemType) {
        this.serviceItemType = serviceItemType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_addon_applicable_service.available_for_all_services
     *
     * @return the value of moe_grooming_addon_applicable_service.available_for_all_services
     *
     * @mbg.generated
     */
    public Boolean getAvailableForAllServices() {
        return availableForAllServices;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_addon_applicable_service.available_for_all_services
     *
     * @param availableForAllServices the value for moe_grooming_addon_applicable_service.available_for_all_services
     *
     * @mbg.generated
     */
    public void setAvailableForAllServices(Boolean availableForAllServices) {
        this.availableForAllServices = availableForAllServices;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_addon_applicable_service.available_service_id_list
     *
     * @return the value of moe_grooming_addon_applicable_service.available_service_id_list
     *
     * @mbg.generated
     */
    public String getAvailableServiceIdList() {
        return availableServiceIdList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_addon_applicable_service.available_service_id_list
     *
     * @param availableServiceIdList the value for moe_grooming_addon_applicable_service.available_service_id_list
     *
     * @mbg.generated
     */
    public void setAvailableServiceIdList(String availableServiceIdList) {
        this.availableServiceIdList = availableServiceIdList == null ? null : availableServiceIdList.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_addon_applicable_service.created_at
     *
     * @return the value of moe_grooming_addon_applicable_service.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_addon_applicable_service.created_at
     *
     * @param createdAt the value for moe_grooming_addon_applicable_service.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_addon_applicable_service.updated_at
     *
     * @return the value of moe_grooming_addon_applicable_service.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_addon_applicable_service.updated_at
     *
     * @param updatedAt the value for moe_grooming_addon_applicable_service.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
