package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_package
 */
public class MoeGroomingPackage {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商品服务packageid
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.cart_package_id
     *
     * @mbg.generated
     */
    private Integer cartPackageId;

    /**
     * Database Column Remarks:
     *   商家id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   顾客id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   package员工id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   package支付信息id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.retail_invoice_item_id
     *
     * @mbg.generated
     */
    private Integer retailInvoiceItemId;

    /**
     * Database Column Remarks:
     *   package卡号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.confirmation_id
     *
     * @mbg.generated
     */
    private String confirmationId;

    /**
     * Database Column Remarks:
     *   package名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.package_name
     *
     * @mbg.generated
     */
    private String packageName;

    /**
     * Database Column Remarks:
     *   packagedesc
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.package_desc
     *
     * @mbg.generated
     */
    private String packageDesc;

    /**
     * Database Column Remarks:
     *   package_price
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.package_price
     *
     * @mbg.generated
     */
    private BigDecimal packagePrice;

    /**
     * Database Column Remarks:
     *   购买时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.purchase_time
     *
     * @mbg.generated
     */
    private Long purchaseTime;

    /**
     * Database Column Remarks:
     *   有效期开始时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.start_time
     *
     * @mbg.generated
     */
    private Long startTime;

    /**
     * Database Column Remarks:
     *   过期时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.end_time
     *
     * @mbg.generated
     */
    private Long endTime;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   package status: 1-normal, 2-deleted
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   过期日期，大于 expiration_date 意味着过期，用一个魔法值 9999-01-01 代表永不过期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package.expiration_date
     *
     * @mbg.generated
     */
    private String expirationDate;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.id
     *
     * @return the value of moe_grooming_package.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.id
     *
     * @param id the value for moe_grooming_package.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.cart_package_id
     *
     * @return the value of moe_grooming_package.cart_package_id
     *
     * @mbg.generated
     */
    public Integer getCartPackageId() {
        return cartPackageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.cart_package_id
     *
     * @param cartPackageId the value for moe_grooming_package.cart_package_id
     *
     * @mbg.generated
     */
    public void setCartPackageId(Integer cartPackageId) {
        this.cartPackageId = cartPackageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.business_id
     *
     * @return the value of moe_grooming_package.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.business_id
     *
     * @param businessId the value for moe_grooming_package.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.company_id
     *
     * @return the value of moe_grooming_package.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.company_id
     *
     * @param companyId the value for moe_grooming_package.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.customer_id
     *
     * @return the value of moe_grooming_package.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.customer_id
     *
     * @param customerId the value for moe_grooming_package.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.staff_id
     *
     * @return the value of moe_grooming_package.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.staff_id
     *
     * @param staffId the value for moe_grooming_package.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.retail_invoice_item_id
     *
     * @return the value of moe_grooming_package.retail_invoice_item_id
     *
     * @mbg.generated
     */
    public Integer getRetailInvoiceItemId() {
        return retailInvoiceItemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.retail_invoice_item_id
     *
     * @param retailInvoiceItemId the value for moe_grooming_package.retail_invoice_item_id
     *
     * @mbg.generated
     */
    public void setRetailInvoiceItemId(Integer retailInvoiceItemId) {
        this.retailInvoiceItemId = retailInvoiceItemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.confirmation_id
     *
     * @return the value of moe_grooming_package.confirmation_id
     *
     * @mbg.generated
     */
    public String getConfirmationId() {
        return confirmationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.confirmation_id
     *
     * @param confirmationId the value for moe_grooming_package.confirmation_id
     *
     * @mbg.generated
     */
    public void setConfirmationId(String confirmationId) {
        this.confirmationId = confirmationId == null ? null : confirmationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.package_name
     *
     * @return the value of moe_grooming_package.package_name
     *
     * @mbg.generated
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.package_name
     *
     * @param packageName the value for moe_grooming_package.package_name
     *
     * @mbg.generated
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.package_desc
     *
     * @return the value of moe_grooming_package.package_desc
     *
     * @mbg.generated
     */
    public String getPackageDesc() {
        return packageDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.package_desc
     *
     * @param packageDesc the value for moe_grooming_package.package_desc
     *
     * @mbg.generated
     */
    public void setPackageDesc(String packageDesc) {
        this.packageDesc = packageDesc == null ? null : packageDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.package_price
     *
     * @return the value of moe_grooming_package.package_price
     *
     * @mbg.generated
     */
    public BigDecimal getPackagePrice() {
        return packagePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.package_price
     *
     * @param packagePrice the value for moe_grooming_package.package_price
     *
     * @mbg.generated
     */
    public void setPackagePrice(BigDecimal packagePrice) {
        this.packagePrice = packagePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.purchase_time
     *
     * @return the value of moe_grooming_package.purchase_time
     *
     * @mbg.generated
     */
    public Long getPurchaseTime() {
        return purchaseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.purchase_time
     *
     * @param purchaseTime the value for moe_grooming_package.purchase_time
     *
     * @mbg.generated
     */
    public void setPurchaseTime(Long purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.start_time
     *
     * @return the value of moe_grooming_package.start_time
     *
     * @mbg.generated
     */
    public Long getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.start_time
     *
     * @param startTime the value for moe_grooming_package.start_time
     *
     * @mbg.generated
     */
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.end_time
     *
     * @return the value of moe_grooming_package.end_time
     *
     * @mbg.generated
     */
    public Long getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.end_time
     *
     * @param endTime the value for moe_grooming_package.end_time
     *
     * @mbg.generated
     */
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.create_time
     *
     * @return the value of moe_grooming_package.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.create_time
     *
     * @param createTime the value for moe_grooming_package.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.update_time
     *
     * @return the value of moe_grooming_package.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.update_time
     *
     * @param updateTime the value for moe_grooming_package.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.status
     *
     * @return the value of moe_grooming_package.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.status
     *
     * @param status the value for moe_grooming_package.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package.expiration_date
     *
     * @return the value of moe_grooming_package.expiration_date
     *
     * @mbg.generated
     */
    public String getExpirationDate() {
        return expirationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package.expiration_date
     *
     * @param expirationDate the value for moe_grooming_package.expiration_date
     *
     * @mbg.generated
     */
    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate == null ? null : expirationDate.trim();
    }
}
