package com.moego.server.grooming.eventbus;

import com.google.protobuf.Timestamp;
import com.moego.common.enums.RepeatModifyTypeEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.appointment.CancelTypeEnum;
import com.moego.idl.models.event_bus.v1.AppointmentCanceledEvent;
import com.moego.idl.models.event_bus.v1.AppointmentCreatedEvent;
import com.moego.idl.models.event_bus.v1.AppointmentDeletedEvent;
import com.moego.idl.models.event_bus.v1.AppointmentFinishedEvent;
import com.moego.idl.models.event_bus.v1.AppointmentUpdatedEvent;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.idl.models.event_bus.v1.EventType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.lib.event_bus.producer.Producer;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.params.CancelParams;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@AllArgsConstructor
public class AppointmentProducer {
    private final AppointmentMapperProxy moeGroomingAppointmentMapper;
    private final Producer producer;
    private final CompanyHelper companyHelper;
    public static final String APPOINTMENT_EVENT_TOPIC = "moego.erp.appointment";

    public void pushAppointmentCreatedEvent(Integer appointmentId) {
        ThreadPool.execute(() -> {
            var appt = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
            producer.send(APPOINTMENT_EVENT_TOPIC, buildAppointmentCreatedEvent(appt));
        });
    }

    public void pushAppointmentCancelledEvent(CancelParams cancelParams, MoeGroomingAppointment appointment) {
        ThreadPool.execute(() -> {
            producer.send(APPOINTMENT_EVENT_TOPIC, buildAppointmentCancelledEvent(cancelParams, appointment));
        });
    }

    public void pushAppointmentFinishedEvent(Integer appointmentId) {
        ThreadPool.execute(() -> {
            var appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
            String timeZoneName = companyHelper.getCompanyTimeZoneName(appointment.getCompanyId());
            producer.send(APPOINTMENT_EVENT_TOPIC, buildAppointmentFinishedEvent(appointment, timeZoneName));
        });
    }

    public void pushAppointmentDeletedEvent(Integer appointmentId) {
        var appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
        producer.send(APPOINTMENT_EVENT_TOPIC, buildAppointmentDeletedEvent(appointment));
    }

    private static EventRecord buildAppointmentDeletedEvent(MoeGroomingAppointment appointment) {
        return EventRecord.<EventData>builder()
                .id(String.valueOf(appointment.getId()))
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(appointment.getCompanyId())
                                .setBusinessId(appointment.getBusinessId())
                                .build())
                        .setAppointmentDeletedEvent(
                                AppointmentDeletedEvent.newBuilder().setId(appointment.getId()))
                        .build())
                .type(EventType.APPOINTMENT_DELETED)
                .build();
    }

    public void pushAppointmentUpdatedEvent(Integer appointmentId) {
        var appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
        producer.send(APPOINTMENT_EVENT_TOPIC, buildAppointmentUpdatedEvent(appointment));
    }

    private EventRecord buildAppointmentUpdatedEvent(MoeGroomingAppointment appointment) {
        return EventRecord.<EventData>builder()
                .id(String.valueOf(appointment.getId()))
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(appointment.getCompanyId())
                                .setBusinessId(appointment.getBusinessId())
                                .build())
                        .setAppointmentUpdatedEvent(
                                AppointmentUpdatedEvent.newBuilder().setId(appointment.getId()))
                        .build())
                .type(EventType.APPOINTMENT_UPDATED)
                .build();
    }

    private EventRecord buildAppointmentCreatedEvent(MoeGroomingAppointment appointment) {
        var eventBuilder = AppointmentCreatedEvent.newBuilder();
        if (StringUtils.hasText(appointment.getAppointmentDate())) {
            String timeZoneName = companyHelper.getCompanyTimeZoneName(appointment.getCompanyId());
            eventBuilder
                    .setStartTime(getTimestamp(
                            timeZoneName, appointment.getAppointmentDate(), appointment.getAppointmentStartTime()))
                    .setEndTime(getTimestamp(
                            timeZoneName, appointment.getAppointmentEndDate(), appointment.getAppointmentEndTime()));
        }
        return EventRecord.builder()
                .id(appointment.getId().toString())
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(appointment.getCompanyId())
                                .setBusinessId(appointment.getBusinessId())
                                .build())
                        .setAppointmentCreatedEvent(eventBuilder
                                .setId(appointment.getId())
                                .setCustomerId(appointment.getCustomerId())
                                .addAllServiceItemTypes(getServiceItemTypeList(appointment.getServiceTypeInclude()))
                                .setCustomerId(appointment.getCustomerId()))
                        .build())
                .type(EventType.APPOINTMENT_CREATED)
                .build();
    }

    public EventRecord<EventData> buildAppointmentCancelledEvent(
            CancelParams cancelParams, MoeGroomingAppointment appointment) {
        var appointmentCanceledEventBuilder = AppointmentCanceledEvent.newBuilder();
        if (StringUtils.hasText(appointment.getAppointmentDate())) {
            String timeZoneName = companyHelper.getCompanyTimeZoneName(appointment.getCompanyId());
            appointmentCanceledEventBuilder
                    .setStartTime(getTimestamp(
                            timeZoneName, appointment.getAppointmentDate(), appointment.getAppointmentStartTime()))
                    .setEndTime(getTimestamp(
                            timeZoneName, appointment.getAppointmentEndDate(), appointment.getAppointmentEndTime()));
        }
        var isBatchCancelRepeat = false;
        var repeatType = cancelParams.getRepeatType();
        if (repeatType != null && !Objects.equals(RepeatModifyTypeEnum.ONLY_THIS.getRepeatType(), repeatType)) {
            isBatchCancelRepeat = true;
        }
        return EventRecord.<EventData>builder()
                .id(appointment.getId().toString())
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(appointment.getCompanyId())
                                .setBusinessId(appointment.getBusinessId())
                                .build())
                        .setAppointmentCanceledEvent(appointmentCanceledEventBuilder
                                .setId(appointment.getId())
                                .setCancelledType(toCancelledType(cancelParams.getCancelByType()))
                                .setCancelledReason(Optional.ofNullable(cancelParams.getCancelReason())
                                        .orElse(""))
                                .setOperatorId(cancelParams.getAccountId())
                                .addAllServiceItemTypes(getServiceItemTypeList(appointment.getServiceTypeInclude()))
                                .setIsRepeatBatchCancel(isBatchCancelRepeat)
                                .setCustomerId(appointment.getCustomerId())
                                .setAutoRefundOrder(Boolean.TRUE.equals(cancelParams.getAutoRefundOrder()))
                                .setRefundDeposit(!cancelParams.getChargeNoShowFee()))
                        .build())
                .type(EventType.APPOINTMENT_CANCELED)
                .build();
    }

    private static AppointmentCanceledEvent.CancelledType toCancelledType(Byte cancelByType) {
        if (Objects.isNull(cancelByType)) {
            return AppointmentCanceledEvent.CancelledType.CANCELLED_BY_SYSTEM;
        }

        switch (CancelTypeEnum.fromValue(cancelByType)) {
            case CANCEL_BY_BUSINESS -> {
                return AppointmentCanceledEvent.CancelledType.CANCELLED_BY_BUSINESS;
            }
            case CANCEL_BY_CLIENT, CANCEL_BY_CLIENT_PORTAL -> {
                return AppointmentCanceledEvent.CancelledType.CANCELLED_BY_CUSTOMER;
            }
            default -> {
                return AppointmentCanceledEvent.CancelledType.CANCELLED_BY_SYSTEM;
            }
        }
    }

    private static EventRecord buildAppointmentFinishedEvent(MoeGroomingAppointment entity, String timeZoneName) {
        return EventRecord.builder()
                .id(entity.getId().toString())
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(entity.getCompanyId())
                                .setBusinessId(entity.getBusinessId())
                                .build())
                        .setAppointmentFinishedEvent(AppointmentFinishedEvent.newBuilder()
                                .setId(entity.getId())
                                .setCustomerId(entity.getCustomerId())
                                .addAllServiceItemTypes(getServiceItemTypeList(entity.getServiceTypeInclude()))
                                .setStartTime(getTimestamp(
                                        timeZoneName, entity.getAppointmentDate(), entity.getAppointmentStartTime()))
                                .setEndTime(getTimestamp(
                                        timeZoneName, entity.getAppointmentEndDate(), entity.getAppointmentEndTime()))
                                .build())
                        .build())
                .type(EventType.APPOINTMENT_FINISHED)
                .build();
    }

    private static List<ServiceItemType> getServiceItemTypeList(Integer serviceItemTypeInclude) {
        return ServiceItemEnum.convertBitValueList(serviceItemTypeInclude).stream()
                .map(type -> ServiceItemType.forNumber(type.getServiceItem()))
                .toList();
    }

    private static Timestamp getTimestamp(String timeZoneName, String date, Integer minutes) {
        long seconds = LocalDate.parse(date)
                .atStartOfDay(ZoneId.of(timeZoneName))
                .plusMinutes(minutes)
                .toEpochSecond();
        return Timestamp.newBuilder().setSeconds(seconds).build();
    }
}
