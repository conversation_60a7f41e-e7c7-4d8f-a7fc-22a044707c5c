package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_connect
 */
public class MoeQbConnect {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   realmId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.realm_id
     *
     * @mbg.generated
     */
    private String realmId;

    /**
     * Database Column Remarks:
     *   quickbooks access token
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.access_token
     *
     * @mbg.generated
     */
    private String accessToken;

    /**
     * Database Column Remarks:
     *   quickbooks refresh token
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.refresh_token
     *
     * @mbg.generated
     */
    private String refreshToken;

    /**
     * Database Column Remarks:
     *   connect status 0-未连接   1-可正常同步 2-用户主动取消 3-授权异常失效
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.connect_status
     *
     * @mbg.generated
     */
    private Byte connectStatus;

    /**
     * Database Column Remarks:
     *   access token 过期时间戳(提前10分钟更新)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.token_expired_time
     *
     * @mbg.generated
     */
    private Long tokenExpiredTime;

    /**
     * Database Column Remarks:
     *   connect 的email
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.connect_email
     *
     * @mbg.generated
     */
    private String connectEmail;

    /**
     * Database Column Remarks:
     *   connect company name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.connect_company_name
     *
     * @mbg.generated
     */
    private String connectCompanyName;

    /**
     * Database Column Remarks:
     *   获取到的userinfo
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.connect_sub
     *
     * @mbg.generated
     */
    private String connectSub;

    /**
     * Database Column Remarks:
     *   银行账本名
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.account_name
     *
     * @mbg.generated
     */
    private String accountName;

    /**
     * Database Column Remarks:
     *   银行账本id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.account_id
     *
     * @mbg.generated
     */
    private String accountId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   qb sync oauth url callback state, bizIds list
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_connect.state
     *
     * @mbg.generated
     */
    private String state;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.id
     *
     * @return the value of moe_qb_connect.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.id
     *
     * @param id the value for moe_qb_connect.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.business_id
     *
     * @return the value of moe_qb_connect.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.business_id
     *
     * @param businessId the value for moe_qb_connect.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.realm_id
     *
     * @return the value of moe_qb_connect.realm_id
     *
     * @mbg.generated
     */
    public String getRealmId() {
        return realmId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.realm_id
     *
     * @param realmId the value for moe_qb_connect.realm_id
     *
     * @mbg.generated
     */
    public void setRealmId(String realmId) {
        this.realmId = realmId == null ? null : realmId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.access_token
     *
     * @return the value of moe_qb_connect.access_token
     *
     * @mbg.generated
     */
    public String getAccessToken() {
        return accessToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.access_token
     *
     * @param accessToken the value for moe_qb_connect.access_token
     *
     * @mbg.generated
     */
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken == null ? null : accessToken.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.refresh_token
     *
     * @return the value of moe_qb_connect.refresh_token
     *
     * @mbg.generated
     */
    public String getRefreshToken() {
        return refreshToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.refresh_token
     *
     * @param refreshToken the value for moe_qb_connect.refresh_token
     *
     * @mbg.generated
     */
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken == null ? null : refreshToken.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.connect_status
     *
     * @return the value of moe_qb_connect.connect_status
     *
     * @mbg.generated
     */
    public Byte getConnectStatus() {
        return connectStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.connect_status
     *
     * @param connectStatus the value for moe_qb_connect.connect_status
     *
     * @mbg.generated
     */
    public void setConnectStatus(Byte connectStatus) {
        this.connectStatus = connectStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.token_expired_time
     *
     * @return the value of moe_qb_connect.token_expired_time
     *
     * @mbg.generated
     */
    public Long getTokenExpiredTime() {
        return tokenExpiredTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.token_expired_time
     *
     * @param tokenExpiredTime the value for moe_qb_connect.token_expired_time
     *
     * @mbg.generated
     */
    public void setTokenExpiredTime(Long tokenExpiredTime) {
        this.tokenExpiredTime = tokenExpiredTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.connect_email
     *
     * @return the value of moe_qb_connect.connect_email
     *
     * @mbg.generated
     */
    public String getConnectEmail() {
        return connectEmail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.connect_email
     *
     * @param connectEmail the value for moe_qb_connect.connect_email
     *
     * @mbg.generated
     */
    public void setConnectEmail(String connectEmail) {
        this.connectEmail = connectEmail == null ? null : connectEmail.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.connect_company_name
     *
     * @return the value of moe_qb_connect.connect_company_name
     *
     * @mbg.generated
     */
    public String getConnectCompanyName() {
        return connectCompanyName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.connect_company_name
     *
     * @param connectCompanyName the value for moe_qb_connect.connect_company_name
     *
     * @mbg.generated
     */
    public void setConnectCompanyName(String connectCompanyName) {
        this.connectCompanyName = connectCompanyName == null ? null : connectCompanyName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.connect_sub
     *
     * @return the value of moe_qb_connect.connect_sub
     *
     * @mbg.generated
     */
    public String getConnectSub() {
        return connectSub;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.connect_sub
     *
     * @param connectSub the value for moe_qb_connect.connect_sub
     *
     * @mbg.generated
     */
    public void setConnectSub(String connectSub) {
        this.connectSub = connectSub == null ? null : connectSub.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.account_name
     *
     * @return the value of moe_qb_connect.account_name
     *
     * @mbg.generated
     */
    public String getAccountName() {
        return accountName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.account_name
     *
     * @param accountName the value for moe_qb_connect.account_name
     *
     * @mbg.generated
     */
    public void setAccountName(String accountName) {
        this.accountName = accountName == null ? null : accountName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.account_id
     *
     * @return the value of moe_qb_connect.account_id
     *
     * @mbg.generated
     */
    public String getAccountId() {
        return accountId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.account_id
     *
     * @param accountId the value for moe_qb_connect.account_id
     *
     * @mbg.generated
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId == null ? null : accountId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.create_time
     *
     * @return the value of moe_qb_connect.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.create_time
     *
     * @param createTime the value for moe_qb_connect.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.update_time
     *
     * @return the value of moe_qb_connect.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.update_time
     *
     * @param updateTime the value for moe_qb_connect.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.company_id
     *
     * @return the value of moe_qb_connect.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.company_id
     *
     * @param companyId the value for moe_qb_connect.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_connect.state
     *
     * @return the value of moe_qb_connect.state
     *
     * @mbg.generated
     */
    public String getState() {
        return state;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_connect.state
     *
     * @param state the value for moe_qb_connect.state
     *
     * @mbg.generated
     */
    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }
}
