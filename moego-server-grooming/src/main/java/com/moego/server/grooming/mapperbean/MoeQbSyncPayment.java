package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_sync_payment
 */
public class MoeQbSyncPayment {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.connect_id
     *
     * @mbg.generated
     */
    private Integer connectId;

    /**
     * Database Column Remarks:
     *   realmId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.realm_id
     *
     * @mbg.generated
     */
    private String realmId;

    /**
     * Database Column Remarks:
     *   invoice id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.invoice_id
     *
     * @mbg.generated
     */
    private Integer invoiceId;

    /**
     * Database Column Remarks:
     *   payment id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.payment_id
     *
     * @mbg.generated
     */
    private Integer paymentId;

    /**
     * Database Column Remarks:
     *   支付金额
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.paid_amount
     *
     * @mbg.generated
     */
    private BigDecimal paidAmount;

    /**
     * Database Column Remarks:
     *   quickbookds invoice id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.qb_invoice_id
     *
     * @mbg.generated
     */
    private String qbInvoiceId;

    /**
     * Database Column Remarks:
     *   quickbookds payment id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.qb_payment_id
     *
     * @mbg.generated
     */
    private String qbPaymentId;

    /**
     * Database Column Remarks:
     *   qb payment 状态 1 正常  2已删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.qb_payment_status
     *
     * @mbg.generated
     */
    private Byte qbPaymentStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.id
     *
     * @return the value of moe_qb_sync_payment.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.id
     *
     * @param id the value for moe_qb_sync_payment.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.business_id
     *
     * @return the value of moe_qb_sync_payment.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.business_id
     *
     * @param businessId the value for moe_qb_sync_payment.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.connect_id
     *
     * @return the value of moe_qb_sync_payment.connect_id
     *
     * @mbg.generated
     */
    public Integer getConnectId() {
        return connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.connect_id
     *
     * @param connectId the value for moe_qb_sync_payment.connect_id
     *
     * @mbg.generated
     */
    public void setConnectId(Integer connectId) {
        this.connectId = connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.realm_id
     *
     * @return the value of moe_qb_sync_payment.realm_id
     *
     * @mbg.generated
     */
    public String getRealmId() {
        return realmId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.realm_id
     *
     * @param realmId the value for moe_qb_sync_payment.realm_id
     *
     * @mbg.generated
     */
    public void setRealmId(String realmId) {
        this.realmId = realmId == null ? null : realmId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.invoice_id
     *
     * @return the value of moe_qb_sync_payment.invoice_id
     *
     * @mbg.generated
     */
    public Integer getInvoiceId() {
        return invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.invoice_id
     *
     * @param invoiceId the value for moe_qb_sync_payment.invoice_id
     *
     * @mbg.generated
     */
    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.payment_id
     *
     * @return the value of moe_qb_sync_payment.payment_id
     *
     * @mbg.generated
     */
    public Integer getPaymentId() {
        return paymentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.payment_id
     *
     * @param paymentId the value for moe_qb_sync_payment.payment_id
     *
     * @mbg.generated
     */
    public void setPaymentId(Integer paymentId) {
        this.paymentId = paymentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.paid_amount
     *
     * @return the value of moe_qb_sync_payment.paid_amount
     *
     * @mbg.generated
     */
    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.paid_amount
     *
     * @param paidAmount the value for moe_qb_sync_payment.paid_amount
     *
     * @mbg.generated
     */
    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.qb_invoice_id
     *
     * @return the value of moe_qb_sync_payment.qb_invoice_id
     *
     * @mbg.generated
     */
    public String getQbInvoiceId() {
        return qbInvoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.qb_invoice_id
     *
     * @param qbInvoiceId the value for moe_qb_sync_payment.qb_invoice_id
     *
     * @mbg.generated
     */
    public void setQbInvoiceId(String qbInvoiceId) {
        this.qbInvoiceId = qbInvoiceId == null ? null : qbInvoiceId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.qb_payment_id
     *
     * @return the value of moe_qb_sync_payment.qb_payment_id
     *
     * @mbg.generated
     */
    public String getQbPaymentId() {
        return qbPaymentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.qb_payment_id
     *
     * @param qbPaymentId the value for moe_qb_sync_payment.qb_payment_id
     *
     * @mbg.generated
     */
    public void setQbPaymentId(String qbPaymentId) {
        this.qbPaymentId = qbPaymentId == null ? null : qbPaymentId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.qb_payment_status
     *
     * @return the value of moe_qb_sync_payment.qb_payment_status
     *
     * @mbg.generated
     */
    public Byte getQbPaymentStatus() {
        return qbPaymentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.qb_payment_status
     *
     * @param qbPaymentStatus the value for moe_qb_sync_payment.qb_payment_status
     *
     * @mbg.generated
     */
    public void setQbPaymentStatus(Byte qbPaymentStatus) {
        this.qbPaymentStatus = qbPaymentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.create_time
     *
     * @return the value of moe_qb_sync_payment.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.create_time
     *
     * @param createTime the value for moe_qb_sync_payment.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.update_time
     *
     * @return the value of moe_qb_sync_payment.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.update_time
     *
     * @param updateTime the value for moe_qb_sync_payment.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment.company_id
     *
     * @return the value of moe_qb_sync_payment.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment.company_id
     *
     * @param companyId the value for moe_qb_sync_payment.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
