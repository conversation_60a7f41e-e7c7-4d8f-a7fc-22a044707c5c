package com.moego.server.grooming.service.utils;

import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.lib.utils.model.Pair;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.dto.AppointmentServiceInfo;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.StaffConflictDTO;
import com.moego.server.grooming.params.PetDetailParams;
import com.moego.server.grooming.params.SaveRepeatAppointmentParams;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

public class AppointmentUtil {

    public static boolean isHistory(
            String appointmentDate, Integer appointmentEndTime, BusinessDateTimeDTO businessDateTime) {
        if (appointmentDate.compareTo(businessDateTime.getCurrentDate()) < 0) {
            return true;
        }
        return appointmentDate.compareTo(businessDateTime.getCurrentDate()) == 0
                && appointmentEndTime <= businessDateTime.getCurrentMinutes();
    }

    public static boolean isUpcoming(
            String appointmentDate, Integer appointmentEndTime, BusinessDateTimeDTO businessDateTime) {
        if (appointmentDate.compareTo(businessDateTime.getCurrentDate()) > 0) {
            return true;
        }
        return appointmentDate.compareTo(businessDateTime.getCurrentDate()) == 0
                && appointmentEndTime > businessDateTime.getCurrentMinutes();
    }

    public static boolean isBlock(Integer isBlock) {
        return Objects.equals(isBlock, GroomingAppointmentEnum.IS_BLOCK_TRUE);
    }

    /**
     * 判断是否和 working hour 冲突
     *
     * @param compareStart  compareStart
     * @param compareEnd    compareEnd
     * @param timeRangeList one day's working hour time range list
     * @return is conflict: true/false
     */
    public static boolean checkWorkingHourConflict(
            Integer compareStart, Integer compareEnd, List<TimeRangeDto> timeRangeList) {
        if (CollectionUtils.isEmpty(timeRangeList)) {
            // 无 working hour，冲突
            return true;
        }
        boolean isInWorkTime = false;
        for (TimeRangeDto timeRange : timeRangeList) {
            Integer startTime = timeRange.getStartTime();
            Integer endTime = timeRange.getEndTime();
            // 比对是否在工作时间范围内
            if (compareStart >= startTime && compareStart < endTime && compareEnd <= endTime) {
                isInWorkTime = true;
                break;
            }
        }
        return !isInWorkTime;
    }

    /**
     * 判断是否和 Appointment/Block 冲突
     * TODO StaffConflictDTO 需要考虑 Operation 时间，老逻辑没有考虑，这里暂时记个 todo 待改
     *
     * @param compareStart     compareStart
     * @param compareEnd       compareEnd
     * @param appointmentBlock appointment or block detail
     * @return is conflict: true/false
     */
    public static boolean checkAppointmentOrBlockConflict(
            Integer compareStart, Integer compareEnd, StaffConflictDTO appointmentBlock) {
        int start = appointmentBlock.getStartTime().intValue();
        int end = appointmentBlock.getStartTime().intValue() + appointmentBlock.getServiceTime();
        // 开始时间 < 已有预约/block 开始时间时，结束时间 > 已有预约/block 开始时间则冲突
        if (compareStart <= start) {
            return compareEnd > start;
        } else {
            // 开始时间 > 已有预约/block 开始时间时，结束时间 < 已有预约/block 结束时间则冲突
            return compareStart < end;
        }
    }

    /**
     * compareStart 到 compareEnd 与 start 到 end 不发生重合，说明不发生冲突
     *
     * @param compareStart compareStart
     * @param compareEnd   compareEnd
     * @param start        start
     * @param end          end
     */
    public static boolean isNotConflict(int compareStart, int compareEnd, int start, int end) {
        // before start
        if (compareStart <= compareEnd && compareEnd <= start) {
            return true;
        }
        // after end
        return end <= compareStart && compareStart <= compareEnd;
    }

    /**
     * 判断是否是 reschedule：更新了 appointment 日期、时间或 pet service 的 staff 或时间（非判断是否需要更新）
     *
     * @param params      更新参数
     * @param appointment 原 appointment
     * @return true/false
     */
    public static boolean needRescheduleAppointment(
            SaveRepeatAppointmentParams params, AppointmentWithPetDetailsDto appointment) {
        if (appointment == null || params == null) {
            return false;
        }
        if (params.getAppointmentDateString() != null
                && !params.getAppointmentDateString().equals(appointment.getAppointmentDate())) {
            return true;
        }
        if (params.getAppointmentStartTime() != null
                && !params.getAppointmentStartTime().equals(appointment.getAppointmentStartTime())) {
            return true;
        }
        if (params.getAllPetsStartAtSameTime() != null
                && !params.getAllPetsStartAtSameTime()
                        .equals(AppointmentUtil.isAllPetsStartAtSameTime(appointment.getServices()))) {
            return true;
        }

        if (!CollectionUtils.isEmpty(params.getServiceList())) {
            if (params.getServiceList().size() != appointment.getServices().size()) {
                return true;
            }
            Map<Pair<Integer, Integer>, PetDetailParams> serviceMap = params.getServiceList().stream()
                    .collect(Collectors.toMap(
                            service -> Pair.of(service.getPetId(), service.getServiceId()), Function.identity()));

            for (int i = 0; i < appointment.getServices().size(); i++) {
                var service = appointment.getServices().get(i);
                var updateService = serviceMap.get(Pair.of(service.getPetId(), service.getServiceId()));
                if (updateService == null) {
                    return true;
                }
                if (updateService.getStaffId() != null
                        && !Objects.equals(updateService.getStaffId(), service.getStaffId())) {
                    return true;
                }
                if (updateService.getServiceTime() != null
                        && !Objects.equals(service.getServiceTime(), updateService.getServiceTime())) {
                    return true;
                }
                if (updateService.getStartTime() != null
                        && !Objects.equals(updateService.getStartTime(), service.getStartTime())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断是否所有宠物都是同一时间开始
     *
     * @param petDetailList 预约的 pet detail 列表
     * @return true/false
     */
    public static boolean isAllPetsStartAtSameTime(List<AppointmentServiceInfo> petDetailList) {
        if (CollectionUtils.isEmpty(petDetailList)) {
            return false;
        }
        // single pet 的 startAtSameTime 开关判断为 false
        if (isSinglePet(petDetailList)) {
            return false;
        }

        return petDetailList.stream().collect(Collectors.groupingBy(AppointmentServiceInfo::getPetId)).values().stream()
                        .map(petServiceList -> petServiceList.stream()
                                .map(AppointmentServiceInfo::getStartTime)
                                .min(Integer::compareTo)
                                .orElse(0))
                        .collect(Collectors.toSet())
                        .size()
                == 1;
    }

    public static boolean isSinglePet(List<AppointmentServiceInfo> petDetailList) {
        if (CollectionUtils.isEmpty(petDetailList)) {
            return false;
        }
        return petDetailList.stream()
                        .map(AppointmentServiceInfo::getPetId)
                        .distinct()
                        .count()
                == 1;
    }
}
