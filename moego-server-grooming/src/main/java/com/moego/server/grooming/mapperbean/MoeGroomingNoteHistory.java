package com.moego.server.grooming.mapperbean;

public class MoeGroomingNoteHistory {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note_history.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note_history.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note_history.grooming_note_id
     *
     * @mbg.generated
     */
    private Integer groomingNoteId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note_history.create_by
     *
     * @mbg.generated
     */
    private Integer createBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note_history.update_by
     *
     * @mbg.generated
     */
    private Integer updateBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note_history.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note_history.old_note
     *
     * @mbg.generated
     */
    private String oldNote;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note_history.new_note
     *
     * @mbg.generated
     */
    private String newNote;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note_history.id
     *
     * @return the value of moe_grooming_note_history.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note_history.id
     *
     * @param id the value for moe_grooming_note_history.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note_history.customer_id
     *
     * @return the value of moe_grooming_note_history.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note_history.customer_id
     *
     * @param customerId the value for moe_grooming_note_history.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note_history.grooming_note_id
     *
     * @return the value of moe_grooming_note_history.grooming_note_id
     *
     * @mbg.generated
     */
    public Integer getGroomingNoteId() {
        return groomingNoteId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note_history.grooming_note_id
     *
     * @param groomingNoteId the value for moe_grooming_note_history.grooming_note_id
     *
     * @mbg.generated
     */
    public void setGroomingNoteId(Integer groomingNoteId) {
        this.groomingNoteId = groomingNoteId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note_history.create_by
     *
     * @return the value of moe_grooming_note_history.create_by
     *
     * @mbg.generated
     */
    public Integer getCreateBy() {
        return createBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note_history.create_by
     *
     * @param createBy the value for moe_grooming_note_history.create_by
     *
     * @mbg.generated
     */
    public void setCreateBy(Integer createBy) {
        this.createBy = createBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note_history.update_by
     *
     * @return the value of moe_grooming_note_history.update_by
     *
     * @mbg.generated
     */
    public Integer getUpdateBy() {
        return updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note_history.update_by
     *
     * @param updateBy the value for moe_grooming_note_history.update_by
     *
     * @mbg.generated
     */
    public void setUpdateBy(Integer updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note_history.create_time
     *
     * @return the value of moe_grooming_note_history.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note_history.create_time
     *
     * @param createTime the value for moe_grooming_note_history.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note_history.old_note
     *
     * @return the value of moe_grooming_note_history.old_note
     *
     * @mbg.generated
     */
    public String getOldNote() {
        return oldNote;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note_history.old_note
     *
     * @param oldNote the value for moe_grooming_note_history.old_note
     *
     * @mbg.generated
     */
    public void setOldNote(String oldNote) {
        this.oldNote = oldNote == null ? null : oldNote.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note_history.new_note
     *
     * @return the value of moe_grooming_note_history.new_note
     *
     * @mbg.generated
     */
    public String getNewNote() {
        return newNote;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note_history.new_note
     *
     * @param newNote the value for moe_grooming_note_history.new_note
     *
     * @mbg.generated
     */
    public void setNewNote(String newNote) {
        this.newNote = newNote == null ? null : newNote.trim();
    }
}
