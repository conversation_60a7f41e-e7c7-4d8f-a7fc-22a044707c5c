package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EmployeeReportCalculateDTO {

    private BigDecimal serviceExpectedRevenue; // service 应收总收入，包含 tax、discount
    private BigDecimal productExpectedRevenue; // product 应收总收入，包含 tax、discount
    private BigDecimal serviceChargeExpectedRevenue; // service charge 应收总收入，包含 tax、discount

    private BigDecimal serviceUnpaidAmount;
    private BigDecimal productUnpaidAmount;
    private BigDecimal serviceChargeUnpaidAmount;

    private BigDecimal servicePaidAmount;
    private BigDecimal productPaidAmount;
    private BigDecimal serviceChargePaidAmount;
    private BigDecimal servicePaidExcludeConFee;
    private BigDecimal productPaidExcludeConFee;
    private BigDecimal serviceChargePaidExcludeConFee;

    private BigDecimal serviceRefundedAmount;
    private BigDecimal productRefundedAmount;
    private BigDecimal serviceChargeRefundedAmount;
    // 订单是否已支付，是否已finish
    private boolean aptPaid;
    private boolean aptFinish;
    // tipsMap, discountMap, taxMap，以 petDetailId 为 key，对应金额为 value
    private Map<Integer, Map<Integer, BigDecimal>> tipsMap;
    private Map<Integer, BigDecimal> discountMap;
    private Map<Integer, BigDecimal> taxMap;
    // new invoice 后，这个字段的含义更正: 预约所有 invoice 的 tips 总和
    private BigDecimal orderTotalTips;

    // 分配到 staff 的金额
    private BigDecimal staffServicePaidAmount;
    private BigDecimal staffServicePaidExcludeConFee;
    private BigDecimal staffServiceRefundedAmount;
    private BigDecimal staffServiceUnpaidAmount;
    private BigDecimal staffServiceExpectedRevenue;
}
