package com.moego.server.grooming.service.utils;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapstruct.AutoAssignConverter;
import com.moego.server.grooming.service.AutoAssignService;
import com.moego.server.grooming.service.client.ClientApptUtils;
import com.moego.server.grooming.web.vo.client.ApptDetailVO;
import com.moego.server.grooming.web.vo.client.ClientApptVO;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ClientApptHelper {

    private final AutoAssignService autoAssignService;

    /**
     * Convert appointment to {@link ClientApptVO}.
     *
     * <p> 如果设置该 appointment 是 auto-assign 的, 则把 start time 和 end time 设置为 0
     *
     * @param appt appointment
     * @return {@link ClientApptVO}
     */
    @Nullable
    public ClientApptVO convertAppt(MoeGroomingAppointment appt) {
        ClientApptVO apptVO = ClientApptUtils.convert(appt);
        if (apptVO != null) {
            AutoAssign autoAssign = autoAssignService.getAutoAssign(appt.getId());
            if (autoAssign != null) {
                apptVO.setAutoAssign(AutoAssignConverter.INSTANCE.entityToDTO(autoAssign));
                if (Objects.equals(appt.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)
                        && autoAssign.getAppointmentTime() != null) {
                    // OB requests 里 auto assign time 的 appointments 需要把时间设置为 0
                    apptVO.setNoStartTime(true);
                    apptVO.setApptStartTime(0);
                    apptVO.setApptEndTime(0);
                }
            }
        }
        return apptVO;
    }

    /**
     * Convert appointment list to {@link ClientApptVO}s.
     *
     * @param appointmentList appointment list
     * @return {@link ClientApptVO}s
     * @see #convertAppt(MoeGroomingAppointment)
     */
    public List<ClientApptVO> convertAppts(List<MoeGroomingAppointment> appointmentList) {
        return appointmentList.parallelStream().map(this::convertAppt).toList();
    }

    /**
     * Convert appointment to {@link ApptDetailVO}.
     *
     * <p> 如果设置该 appointment 是 auto-assign 的, 则把 start time 和 end time 设置为 0
     *
     * @param appointment appointment
     * @return {@link ApptDetailVO}
     */
    @Nullable
    public ApptDetailVO convertApptDetail(MoeGroomingAppointment appointment) {
        ApptDetailVO vo = ClientApptUtils.convertDetail(appointment);
        if (vo != null) {
            AutoAssign autoAssign = autoAssignService.getAutoAssign(appointment.getId());
            if (autoAssign != null) {
                vo.setAutoAssign(AutoAssignConverter.INSTANCE.entityToDTO(autoAssign));
                if (Objects.equals(appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)
                        && autoAssign.getAppointmentTime() != null) {
                    // OB requests 里 auto assign time 的 appointments 需要把时间设置为 0
                    vo.setNoStartTime(true);
                    vo.setApptStartTime(0);
                    vo.setApptEndTime(0);
                }
            }
        }
        return vo;
    }
}
