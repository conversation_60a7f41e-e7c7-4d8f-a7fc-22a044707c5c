package com.moego.server.grooming.service.client;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.service.AutoAssignService;
import com.moego.server.grooming.service.CompanyGroomingServiceQueryService;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import com.moego.server.grooming.web.vo.client.ClientApptItemVO;
import com.moego.server.grooming.web.vo.client.ServiceDetailVO;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
@Slf4j
@Service
@AllArgsConstructor
public class ClientApptPetDetailService {

    private final PetDetailMapperProxy petDetailMapper;
    private final CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    private final IPetClient petClient;
    private final IBusinessStaffClient staffClient;
    private final AutoAssignService autoAssignService;

    public List<ClientApptItemVO> listApptPetDetail(MoeGroomingAppointment appointment) {
        List<OBClientApptDTO.OBClientApptPetDetailDTO> apptDetailDTOList =
                petDetailMapper.getApptPetDetail(appointment.getId());
        if (CollectionUtils.isEmpty(apptDetailDTOList)) {
            return Collections.emptyList();
        }

        // Pet detail
        List<Integer> petIdList = apptDetailDTOList.stream()
                .map(OBClientApptDTO.OBClientApptPetDetailDTO::getPetId)
                .distinct()
                .collect(Collectors.toList());
        List<CustomerPetDetailDTO> petDetailDTOList = petClient.getCustomerPetListByIdList(petIdList);
        Map<Integer, CustomerPetDetailDTO> petDetailDTOMap = petDetailDTOList.stream()
                .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity()));
        // Service detail
        List<Integer> serviceIdList = apptDetailDTOList.stream()
                .map(OBClientApptDTO.OBClientApptPetDetailDTO::getServiceId)
                .distinct()
                .collect(Collectors.toList());
        List<MoeGroomingService> serviceList =
                companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                        appointment.getBusinessId(), serviceIdList);
        Map<Integer, MoeGroomingService> serviceMap =
                serviceList.stream().collect(Collectors.toMap(MoeGroomingService::getId, Function.identity()));
        // Staff detail
        List<Integer> staffIdList = apptDetailDTOList.stream()
                .map(OBClientApptDTO.OBClientApptPetDetailDTO::getStaffId)
                .distinct()
                .collect(Collectors.toList());
        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(appointment.getBusinessId());
        staffIdListParams.setStaffIdList(staffIdList);
        List<MoeStaffDto> staffDtoList = staffClient.getStaffList(staffIdListParams);
        Map<Integer, MoeStaffDto> staffDtoMap =
                staffDtoList.stream().collect(Collectors.toMap(MoeStaffDto::getId, Function.identity()));

        // OB requests 里 auto assign staff 的 appointments 需要把 staffId 设置为 null
        AutoAssign autoAssign = autoAssignService.getAutoAssign(appointment.getId());
        boolean isNeedHideStaff = autoAssign != null
                && Objects.equals(appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)
                && autoAssign.getStaffId() != null;

        return apptDetailDTOList.stream()
                .map(apptPetDetailDTO -> {
                    ClientApptItemVO apptItemVO = new ClientApptItemVO();
                    CustomerPetDetailDTO petDetailDTO = petDetailDTOMap.get(apptPetDetailDTO.getPetId());
                    if (Objects.nonNull(petDetailDTO)) {
                        apptItemVO.setPetDetail(ClientApptUtils.convert(petDetailDTO));
                    }
                    ServiceDetailVO serviceDetailVO = new ServiceDetailVO()
                            .setServiceId(apptPetDetailDTO.getServiceId())
                            .setServiceTime(apptPetDetailDTO.getServiceTime())
                            .setServicePrice(apptPetDetailDTO.getServicePrice());
                    apptItemVO.setServiceDetail(serviceDetailVO);
                    MoeGroomingService service = serviceMap.get(apptPetDetailDTO.getServiceId());
                    if (Objects.nonNull(service)) {
                        serviceDetailVO.setServiceName(service.getName()).setServiceType(service.getType());
                    }
                    MoeStaffDto staffDto = staffDtoMap.get(apptPetDetailDTO.getStaffId());
                    if (Objects.nonNull(staffDto) && !isNeedHideStaff) {
                        apptItemVO.setStaffDetail(ClientApptUtils.convert(staffDto));
                    }
                    return apptItemVO;
                })
                .collect(Collectors.toList());
    }
}
