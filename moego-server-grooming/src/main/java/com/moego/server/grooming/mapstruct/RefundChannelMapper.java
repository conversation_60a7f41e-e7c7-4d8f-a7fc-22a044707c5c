package com.moego.server.grooming.mapstruct;

import com.moego.idl.models.order.v1.RefundChannel;
import com.moego.idl.models.order.v1.RefundChannelResponse;
import com.moego.server.payment.dto.CanRefundChannel;
import com.moego.server.payment.dto.RefundChannelDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {ChannelMapper.class})
public interface RefundChannelMapper {

    RefundChannelMapper INSTANCE = Mappers.getMapper(RefundChannelMapper.class);

    @Mapping(target = "refundAmount", expression = "java(java.math.BigDecimal.valueOf(response.getRefundAmount()))")
    @Mapping(target = "channelList", source = "channelListList")
    @Mapping(target = "invoiceId", expression = "java(Math.toIntExact(response.getInvoiceId()))")
    RefundChannelDTO toDTO(RefundChannelResponse response);

    List<CanRefundChannel> toListDto(List<RefundChannel> channelList);
}
