package com.moego.server.grooming.service;

import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest;
import com.moego.server.grooming.mapper.EvaluationServiceDetailMapper;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class EvaluationServiceDetailService {
    @Autowired
    private EvaluationServiceDetailMapper evaluationServiceDetailMapper;

    @Autowired
    private EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationServiceBlockingStub;

    public List<EvaluationServiceDetail> queryByGroomingIds(List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return List.of();
        }
        return evaluationServiceDetailMapper.queryByAppointmentIds(
                groomingIds.stream().map(Integer::longValue).toList());
    }

    public List<EvaluationBriefView> getEvaluationServiceList(List<Long> evaluationServiceIds) {
        if (CollectionUtils.isEmpty(evaluationServiceIds)) {
            return List.of();
        }
        return evaluationServiceBlockingStub
                .getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(evaluationServiceIds)
                        .build())
                .getEvaluationsList();
    }

    public Map<Long, EvaluationBriefView> getEvaluationServiceMap(List<Long> evaluationServiceIds) {
        return getEvaluationServiceList(evaluationServiceIds).stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, e -> e, (k1, k2) -> k1));
    }
}
