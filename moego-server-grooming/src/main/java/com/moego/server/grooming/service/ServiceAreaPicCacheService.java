package com.moego.server.grooming.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.mapper.ServiceAreaPicCacheMapper;
import com.moego.server.grooming.mapperbean.ServiceAreaPicCache;
import com.moego.server.grooming.mapperbean.ServiceAreaPicCacheExample;
import jakarta.annotation.Nullable;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ServiceAreaPicCacheService {

    private final ServiceAreaPicCacheMapper serviceAreaPicCacheMapper;

    /**
     * Create a {@link ServiceAreaPicCache}.
     *
     * @param serviceAreaPicCache {@link ServiceAreaPicCache}
     * @return id
     */
    public int insert(ServiceAreaPicCache serviceAreaPicCache) {
        return serviceAreaPicCacheMapper.insertSelective(serviceAreaPicCache);
    }

    /**
     * Update {@link ServiceAreaPicCache} by business id.
     *
     * @param serviceAreaPicCache {@link ServiceAreaPicCache}
     * @return affect count
     */
    public int updateByBusinessId(ServiceAreaPicCache serviceAreaPicCache) {
        if (serviceAreaPicCache.getBusinessId() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business id can not be null");
        }
        ServiceAreaPicCacheExample example = new ServiceAreaPicCacheExample();
        example.createCriteria().andBusinessIdEqualTo(serviceAreaPicCache.getBusinessId());
        return serviceAreaPicCacheMapper.updateByExampleSelective(serviceAreaPicCache, example);
    }

    /**
     * Get {@link ServiceAreaPicCache} by business id.
     *
     * @param businessId business id
     * @return {@link ServiceAreaPicCache}, maybe null
     */
    @Nullable
    public ServiceAreaPicCache getByBusinessId(Integer businessId) {
        ServiceAreaPicCacheExample example = new ServiceAreaPicCacheExample();
        example.createCriteria().andBusinessIdEqualTo(businessId);
        List<ServiceAreaPicCache> entities = serviceAreaPicCacheMapper.selectByExample(example);
        if (ObjectUtils.isEmpty(entities)) {
            return null;
        }
        return entities.get(0);
    }

    /**
     * Delete by business id.
     *
     * @param businessId business id
     * @return affected count
     */
    public int deleteByBusinessId(Integer businessId) {
        ServiceAreaPicCacheExample example = new ServiceAreaPicCacheExample();
        example.createCriteria().andBusinessIdEqualTo(businessId);
        return serviceAreaPicCacheMapper.deleteByExample(example);
    }
}
