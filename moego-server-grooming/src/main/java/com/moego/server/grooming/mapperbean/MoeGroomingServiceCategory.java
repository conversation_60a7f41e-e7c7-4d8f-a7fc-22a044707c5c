package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_service_category
 */
public class MoeGroomingServiceCategory {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   类型名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     * Database Column Remarks:
     *   数据类型：1-主服务(service)；2-额外服务(addons)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     * Database Column Remarks:
     *   排序值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     * Database Column Remarks:
     *   1 正常  2删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   1 - grooming, 2 - boarding, 3 - daycare
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_category.service_item_type
     *
     * @mbg.generated
     */
    private Integer serviceItemType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.id
     *
     * @return the value of moe_grooming_service_category.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.id
     *
     * @param id the value for moe_grooming_service_category.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.business_id
     *
     * @return the value of moe_grooming_service_category.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.business_id
     *
     * @param businessId the value for moe_grooming_service_category.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.name
     *
     * @return the value of moe_grooming_service_category.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.name
     *
     * @param name the value for moe_grooming_service_category.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.type
     *
     * @return the value of moe_grooming_service_category.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.type
     *
     * @param type the value for moe_grooming_service_category.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.sort
     *
     * @return the value of moe_grooming_service_category.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.sort
     *
     * @param sort the value for moe_grooming_service_category.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.status
     *
     * @return the value of moe_grooming_service_category.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.status
     *
     * @param status the value for moe_grooming_service_category.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.create_time
     *
     * @return the value of moe_grooming_service_category.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.create_time
     *
     * @param createTime the value for moe_grooming_service_category.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.update_time
     *
     * @return the value of moe_grooming_service_category.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.update_time
     *
     * @param updateTime the value for moe_grooming_service_category.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.company_id
     *
     * @return the value of moe_grooming_service_category.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.company_id
     *
     * @param companyId the value for moe_grooming_service_category.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_category.service_item_type
     *
     * @return the value of moe_grooming_service_category.service_item_type
     *
     * @mbg.generated
     */
    public Integer getServiceItemType() {
        return serviceItemType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_category.service_item_type
     *
     * @param serviceItemType the value for moe_grooming_service_category.service_item_type
     *
     * @mbg.generated
     */
    public void setServiceItemType(Integer serviceItemType) {
        this.serviceItemType = serviceItemType;
    }
}
