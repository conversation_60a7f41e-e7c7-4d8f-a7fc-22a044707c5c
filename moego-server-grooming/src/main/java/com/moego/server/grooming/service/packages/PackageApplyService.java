package com.moego.server.grooming.service.packages;

import com.moego.common.constant.CommonConstant;
import com.moego.server.grooming.mapper.MoeGroomingInvoiceApplyPackageMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackageExample;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PackageApplyService {

    private final MoeGroomingInvoiceApplyPackageMapper invoiceApplyPackageMapper;

    public void replaceApplyPackages(int orderId, List<MoeGroomingInvoiceApplyPackage> applyRecords) {
        if (applyRecords.isEmpty()) {
            return;
        }

        // 先删除原有的 apply 记录
        var updateBean = new MoeGroomingInvoiceApplyPackage();
        updateBean.setStatus(CommonConstant.DELETED);
        var exm = new MoeGroomingInvoiceApplyPackageExample();
        exm.createCriteria().andInvoiceIdEqualTo(orderId);
        invoiceApplyPackageMapper.updateByExampleSelective(updateBean, exm);

        // 插入新的 apply 记录
        for (var e : applyRecords) {
            invoiceApplyPackageMapper.insertSelective(e);
        }
    }
}
