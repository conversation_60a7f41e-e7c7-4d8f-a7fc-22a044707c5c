package com.moego.server.grooming.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.summingInt;
import static java.util.stream.Collectors.toMap;

import com.moego.common.constant.CommonConstant;
import com.moego.common.distributed.LockManager;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.grooming.constant.PackageConstant;
import com.moego.server.grooming.dto.GroomingPackageDTO;
import com.moego.server.grooming.dto.GroomingPackageHistoryDTO;
import com.moego.server.grooming.dto.GroomingPackageInfoDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceInfoDTO;
import com.moego.server.grooming.dto.PackageServiceDTO;
import com.moego.server.grooming.enums.PackageActivityEnum;
import com.moego.server.grooming.eventbus.PackageProducer;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingInvoiceApplyPackageMapper;
import com.moego.server.grooming.mapper.MoeGroomingPackageHistoryMapper;
import com.moego.server.grooming.mapper.MoeGroomingPackageMapper;
import com.moego.server.grooming.mapper.MoeGroomingPackageServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackageExample;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.mapperbean.MoeGroomingPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageExample;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageHistoryExample;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageService;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageServiceExample;
import com.moego.server.grooming.mapstruct.GroomingPackageServiceConverter;
import com.moego.server.grooming.params.CreateCustomerPackageResult;
import com.moego.server.grooming.params.PackageUsedParams;
import com.moego.server.grooming.params.PurchasedPackage;
import com.moego.server.grooming.service.params.QueryCustomerPackageInfoParam;
import com.moego.server.retail.client.IRetailClient;
import com.moego.server.retail.dto.PackageInfoDto;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class MoePackageService {

    @Autowired
    private MoeGroomingPackageMapper moeGroomingPackageMapper;

    @Autowired
    private MoeGroomingPackageServiceMapper packageServiceMapper;

    @Autowired
    private MoeGroomingInvoiceApplyPackageMapper invoiceApplyPackageMapper;

    @Autowired
    private MoeGroomingPackageHistoryMapper moeGroomingPackageHistoryMapper;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private MoePackageServiceInfoService moePackageServiceInfoService;

    @Autowired
    private PackageProducer packageProducer;

    @Autowired
    private IRetailClient iRetailClient;

    @Autowired
    private LockManager moegoLockManager;

    @Autowired
    private ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;

    @Autowired
    private IBusinessBusinessService businessApi;

    private static final byte REMOVED = 2;

    @Autowired
    private MoeGroomingInvoiceApplyPackageMapper moeGroomingInvoiceApplyPackageMapper;

    // 0203 @Transactional
    public CreateCustomerPackageResult createPurchasedPackage(List<PurchasedPackage> purchasedPackages) {
        Map<Long, Long> packageIdToCustomerPackageIdMap = new HashMap<>();
        // cart_package_id 和 retail_invoice_item_id 在数据层是唯一存在的
        // 最理想是通过表的唯一索引限制，但是存量有 150 多条数据无法加唯一索引
        for (PurchasedPackage purchasedPackage : purchasedPackages) {
            var companyId = purchasedPackage.getCompanyId();
            var retailInvoiceItemId = purchasedPackage.getRetailInvoiceItemId();
            var cardPackageId = purchasedPackage.getPackageId();
            var isExist = queryPurchasePackage(companyId, retailInvoiceItemId, cardPackageId);
            if (isExist) {
                continue;
            }
            // get lock
            var lockKey = moegoLockManager.getResourceKey(
                    LockManager.GROOMING_PACKAGE_ADD, retailInvoiceItemId + "_" + cardPackageId);
            String randomValue = CommonUtil.getUuid();
            try {
                if (moegoLockManager.lockWithRetry(lockKey, randomValue)) {
                    isExist = queryPurchasePackage(companyId, retailInvoiceItemId, cardPackageId);
                    if (isExist) {
                        continue;
                    }
                    // 插入记录
                    packageIdToCustomerPackageIdMap.put(
                            purchasedPackage.getPackageId().longValue(), addMoeGroomingPackage(purchasedPackage));
                } else {
                    log.error("get lock failed, key: {}, value: {}", lockKey, randomValue);
                }
            } finally {
                moegoLockManager.unlock(lockKey, randomValue);
            }
        }
        return CreateCustomerPackageResult.builder()
                .packageIdToCustomerPackageIdMap(packageIdToCustomerPackageIdMap)
                .build();
    }

    private boolean queryPurchasePackage(Long companyId, Integer retailInvoiceItemId, Integer packageId) {
        MoeGroomingPackageExample example = new MoeGroomingPackageExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andRetailInvoiceItemIdEqualTo(retailInvoiceItemId)
                .andCartPackageIdEqualTo(packageId);
        return moeGroomingPackageMapper.countByExample(example) > 0;
    }

    // 0203 @Transactional
    public long addMoeGroomingPackage(PurchasedPackage purchasedPackage) {

        var moeGroomingPackage = buildGroomingPackage(purchasedPackage);

        moeGroomingPackageMapper.insertSelective(moeGroomingPackage);

        if (purchasedPackage.getItems() != null) {
            addPackageServicesForItems(moeGroomingPackage.getId(), purchasedPackage.getItems());
        } else {
            moePackageServiceInfoService.addMoePackageService(
                    moeGroomingPackage.getId(), purchasedPackage.getServices());
        }

        ActivityLogRecorder.record(
                Action.CREATE, ResourceType.CUSTOMER_PACKAGE, moeGroomingPackage.getId(), purchasedPackage);

        return moeGroomingPackage.getId();
    }

    private void addPackageServicesForItems(int groomingPackageId, List<PackageInfoDto.Item> items) {
        for (var item : items) {

            var insertBean = new MoeGroomingPackageService();
            insertBean.setPackageId(groomingPackageId);
            insertBean.setTotalQuantity(item.getQuantity());
            insertBean.setRemainingQuantity(item.getQuantity());
            insertBean.setServices(item.getServices());

            // 数据库没有默认值，手动设置 :)
            insertBean.setServiceId(0);
            insertBean.setServiceUnitPrice(BigDecimal.ZERO);

            packageServiceMapper.insertSelective(insertBean);
        }
    }

    private static MoeGroomingPackage buildGroomingPackage(PurchasedPackage purchasedPackage) {

        MoeGroomingPackage moeGroomingPackage = new MoeGroomingPackage();

        moeGroomingPackage.setCompanyId(purchasedPackage.getCompanyId());
        moeGroomingPackage.setBusinessId(purchasedPackage.getBusinessId());
        moeGroomingPackage.setCartPackageId(purchasedPackage.getPackageId());
        moeGroomingPackage.setConfirmationId(purchasedPackage.getConfirmationId());
        moeGroomingPackage.setCustomerId(purchasedPackage.getCustomerId());
        moeGroomingPackage.setStaffId(purchasedPackage.getStaffId());
        moeGroomingPackage.setPackageDesc(purchasedPackage.getPackageDesc());
        moeGroomingPackage.setPackageName(purchasedPackage.getPackageName());
        moeGroomingPackage.setPackagePrice(purchasedPackage.getPackagePrice());
        moeGroomingPackage.setPurchaseTime(purchasedPackage.getPurchaseTime());
        moeGroomingPackage.setRetailInvoiceItemId(purchasedPackage.getRetailInvoiceItemId());

        if (StringUtils.hasText(purchasedPackage.getExpirationDate())) {
            // 新 package，有效时间是一段时长
            moeGroomingPackage.setStartTime(Instant.now().getEpochSecond());
            moeGroomingPackage.setEndTime(LocalDate.parse(purchasedPackage.getExpirationDate())
                    .atStartOfDay(ZoneId.systemDefault())
                    .toEpochSecond());
            moeGroomingPackage.setExpirationDate(purchasedPackage.getExpirationDate());
        } else if (isNormal(purchasedPackage.getEndTime())) {
            // 旧 package，有效时间是一段时间范围
            moeGroomingPackage.setStartTime(purchasedPackage.getStartTime());
            moeGroomingPackage.setEndTime(purchasedPackage.getEndTime());
            moeGroomingPackage.setExpirationDate(Instant.ofEpochSecond(purchasedPackage.getEndTime())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate()
                    .toString());
        } else {
            throw bizException(Code.CODE_PARAMS_ERROR, "endTime or expirationDate is required");
        }

        moeGroomingPackage.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingPackage.setUpdateTime(CommonUtil.get10Timestamp());
        return moeGroomingPackage;
    }

    public ResponseResult<List<GroomingPackageDTO>> queryCustomerPackageList(
            Long companyId, List<Integer> businessIds, Integer customerId) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return ResponseResult.success(Collections.emptyList());
        }

        List<GroomingPackageDTO> groomingPackages =
                moeGroomingPackageMapper.queryCustomerPackageListByBusinessIds(businessIds, customerId);

        if (CollectionUtils.isEmpty(groomingPackages)) {
            return ResponseResult.success(groomingPackages);
        }

        Set<Integer> packageIdSet = moeGroomingPackageHistoryMapper
                .queryPackageHistoryByPackageIdList(
                        groomingPackages.stream().map(GroomingPackageDTO::getId).collect(Collectors.toList()))
                .stream()
                .map(GroomingPackageHistoryDTO::getPackageId)
                .collect(Collectors.toSet());

        var example = new MoeGroomingInvoiceApplyPackageExample();
        example.createCriteria()
                .andPackageIdIn(
                        groomingPackages.stream().map(GroomingPackageDTO::getId).collect(Collectors.toList()))
                .andStatusEqualTo(CommonConstant.NORMAL);
        var appliedPackageIds = moeGroomingInvoiceApplyPackageMapper.selectByExample(example).stream()
                .map(MoeGroomingInvoiceApplyPackage::getPackageId)
                .collect(Collectors.toSet());

        // get total reminder
        var packageIdList =
                groomingPackages.stream().map(GroomingPackageDTO::getId).toList();
        Map<Integer, Integer> packageServiceMapByGroomingPackageIds =
                getPackageServiceMapByGroomingPackageIds(packageIdList).entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                                .mapToInt(GroomingPackageServiceDTO::getRemainingQuantity)
                                .sum()));

        // 设置 package 是否被使用 & total reminder
        groomingPackages.forEach(groomingPackage -> {
            groomingPackage.setApplied(appliedPackageIds.contains(groomingPackage.getId()));
            groomingPackage.setUsed(packageIdSet.contains(groomingPackage.getId()));
            groomingPackage.setTotalRemainingQuantity(
                    packageServiceMapByGroomingPackageIds.getOrDefault(groomingPackage.getId(), 0));
        });

        return ResponseResult.success(groomingPackages);
    }

    public List<GroomingPackageDTO> queryCustomerPackages(
            Long companyId, List<Integer> customerIds, Integer businessId, String currentDate) {
        return moeGroomingPackageMapper.queryCustomerPackageListByFilter(
                companyId, customerIds, businessId, currentDate);
    }

    public List<GroomingPackageServiceDTO> queryPackageServices(List<Integer> packageIds) {
        return getPackageServiceMapByGroomingPackageIds(packageIds).values().stream()
                .flatMap(List::stream)
                .toList();
    }

    public MoeGroomingPackage queryGroomingPackage(Integer businessId, Integer packageId) {
        MoeGroomingPackage groomingPackage = moeGroomingPackageMapper.selectByPrimaryKey(packageId);
        if (businessId == null) {
            return groomingPackage;
        }
        if (groomingPackage != null && businessId.equals(groomingPackage.getBusinessId())) {
            return groomingPackage;
        }

        return null;
    }

    public boolean updatePackage(MoeGroomingPackage moeGroomingPackage) {
        if (moeGroomingPackage.getId() == null || moeGroomingPackage.getId() == 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "package id is required");
        }

        MoeGroomingPackage moeGroomingPackageOld =
                moeGroomingPackageMapper.selectByPrimaryKey(moeGroomingPackage.getId());
        if (moeGroomingPackageOld == null) {
            throw ExceptionUtil.bizException(Code.CODE_PACKAGE_NOT_FOUND, "package not found");
        }

        moeGroomingPackageMapper.updateByPrimaryKeySelective(moeGroomingPackage);
        return true;
    }

    public void insertPackageHistory(MoeGroomingPackageHistory moeGroomingPackageHistory) {
        moeGroomingPackageHistoryMapper.insertSelective(moeGroomingPackageHistory);
    }

    public boolean updatePackageService(Long staffId, PackageServiceDTO packageServiceDTO) {
        if (packageServiceDTO.getId() == null || packageServiceDTO.getId() == 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "package service id is required");
        }

        MoeGroomingPackageService moeGroomingPackageService =
                packageServiceMapper.selectByPrimaryKey(packageServiceDTO.getId());
        if (moeGroomingPackageService == null) {
            throw ExceptionUtil.bizException(Code.CODE_PACKAGE_NOT_FOUND, "package service not found");
        }

        // 计算package service quantity数量差
        var quantityDiff = packageServiceDTO.getQuantity() - moeGroomingPackageService.getRemainingQuantity();
        if (quantityDiff == 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "manual change quantity diff must be greater than 0");
        }
        moeGroomingPackageService.setRemainingQuantity(packageServiceDTO.getQuantity());
        packageServiceMapper.updateByPrimaryKeySelective(moeGroomingPackageService);

        // add package service history
        MoeGroomingPackageHistory moeGroomingPackageHistory = new MoeGroomingPackageHistory();
        moeGroomingPackageHistory.setPackageId(moeGroomingPackageService.getPackageId());
        moeGroomingPackageHistory.setPackageServiceId(moeGroomingPackageService.getId());
        moeGroomingPackageHistory.setServiceId(moeGroomingPackageService.getServiceId());
        moeGroomingPackageHistory.setQuantity(quantityDiff);
        moeGroomingPackageHistory.setUseTime(CommonUtil.get10Timestamp());
        moeGroomingPackageHistory.setActivityType(PackageActivityEnum.MANUAL_CHANGE_REMAINING);
        moeGroomingPackageHistory.setStatus(CommonConstant.NORMAL);
        moeGroomingPackageHistory.setStaffId(staffId.intValue());
        moeGroomingPackageHistory.setInvoiceId(0);
        moeGroomingPackageHistoryMapper.insertSelective(moeGroomingPackageHistory);

        return true;
    }

    public GroomingPackageInfoDTO queryCustomerPackageInfoById(Integer id) {
        GroomingPackageInfoDTO groomingPackageInfoDTO = new GroomingPackageInfoDTO();

        List<GroomingPackageServiceDTO> groomingPackageServices =
                getPackageServiceMapByGroomingPackageIds(List.of(id)).getOrDefault(id, List.of());

        List<GroomingPackageHistoryDTO> groomingPackageHistories =
                moeGroomingPackageHistoryMapper.queryPackageHistoryByPackageId(id);

        groomingPackageInfoDTO.setPackageServices(groomingPackageServices);
        groomingPackageInfoDTO.setPackageHistories(groomingPackageHistories);
        return groomingPackageInfoDTO;
    }

    // 0203 @Transactional
    public ResponseResult<GroomingPackageInfoDTO> queryCustomerPackageInfo(QueryCustomerPackageInfoParam params) {
        GroomingPackageInfoDTO groomingPackageInfoDTO = new GroomingPackageInfoDTO();

        List<GroomingPackageServiceDTO> groomingPackageServices = getPackageServiceMapByGroomingPackageIds(
                        List.of(params.getId()))
                .getOrDefault(params.getId(), List.of());

        var packageHistoryCount = moeGroomingPackageHistoryMapper.getPackageHistoryCount(params.getId());

        // 分页默认值
        if (params.getHistoryPageNum() == null) params.setHistoryPageNum(1);
        if (params.getHistoryPageSize() == null) params.setHistoryPageSize(20);
        List<GroomingPackageHistoryDTO> groomingPackageHistories = moeGroomingPackageHistoryMapper.queryPackageHistory(
                params.getId(),
                CommonUtil.getLimitOffset(params.getHistoryPageNum(), params.getHistoryPageSize()),
                params.getHistoryPageSize());

        groomingPackageInfoDTO.setPackageHistoryCount(packageHistoryCount);
        groomingPackageInfoDTO.setPackageServices(groomingPackageServices);
        groomingPackageInfoDTO.setPackageHistories(groomingPackageHistories);
        return ResponseResult.success(groomingPackageInfoDTO);
    }

    private Map<Integer, List<GroomingPackageServiceDTO>> getPackageServiceMapByGroomingPackageIds(
            List<Integer> groomingPackageIds) {

        var packageServices = listPackageServiceByGroomingPackageIds(groomingPackageIds);

        var serviceIds = packageServices.stream()
                .flatMap(e -> e.getServices().stream())
                .map(PackageInfoDto.Service::getServiceId)
                .map(Integer::longValue)
                .collect(Collectors.toSet());

        var serviceIdToService = serviceStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(serviceIds)
                        .build())
                .getServicesList()
                .stream()
                .collect(toMap(ServiceBriefView::getId, identity(), (o, n) -> o));

        return packageServices.stream()
                .map(entity -> {
                    var dto = GroomingPackageServiceConverter.INSTANCE.entityToDTO(entity);
                    if (dto.getServices() != null) {
                        for (var s : dto.getServices()) {
                            var service =
                                    serviceIdToService.get(s.getServiceId().longValue());
                            if (service != null) {
                                s.setName(service.getName()); // 使用最新的 service name
                            }
                        }
                    }
                    return dto;
                })
                .collect(Collectors.groupingBy(GroomingPackageServiceDTO::getPackageId));
    }

    private List<MoeGroomingPackageService> listPackageServiceByGroomingPackageIds(List<Integer> groomingPackageIds) {
        var example = new MoeGroomingPackageServiceExample();
        example.createCriteria().andPackageIdIn(groomingPackageIds);
        return packageServiceMapper.selectByExample(example);
    }

    /**
     * 查询包含 serviceIds 的 packages
     *
     * @param tokenBusinessId
     * @param serviceIds
     * @param customerId
     * @return
     * @deprecated by Freeman, 方法的返回值已经不符合现在 package 模型定义
     */
    @Deprecated(since = "2024/10/28")
    public List<GroomingPackageServiceInfoDTO> queryCustomerPackageByServiceIds(
            Integer tokenBusinessId, List<Integer> serviceIds, Integer customerId) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Collections.emptyList();
        }
        // 获取当前日期查询未过期 package
        return moeGroomingPackageMapper.queryCustomerPackageByServiceIds(
                tokenBusinessId, serviceIds, customerId, LocalDate.now().toString());
    }

    /**
     * List all available packages for a customer
     *
     * @param businessId businessId
     * @param customerId customerId
     * @return Available packages
     */
    public List<MoeGroomingPackage> listAvailablePackage(int businessId, int customerId) {

        var example = new MoeGroomingPackageExample();
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andCustomerIdEqualTo(customerId)
                .andStatusEqualTo(CommonConstant.NORMAL)
                .andExpirationDateGreaterThanOrEqualTo(
                        mustGetBusinessTime(businessId).getCurrentDate());

        return moeGroomingPackageMapper.selectByExample(example);
    }

    private BusinessDateTimeDTO mustGetBusinessTime(int businessId) {
        return businessApi.getBusinessDateTime(businessId);
    }

    public List<MoeGroomingPackageHistory> checkUsePackageForGrooming(List<PackageUsedParams> packageUsedParams) {
        List<MoeGroomingPackageHistory> histories = new ArrayList<>();

        for (PackageUsedParams packageUsedParam : packageUsedParams) {
            MoeGroomingPackageService moeGroomingPackageService = packageServiceMapper.queryPackageServiceByProp(
                    packageUsedParam.getBusinessId(),
                    packageUsedParam.getCustomerId(),
                    packageUsedParam.getPackageId(),
                    packageUsedParam.getPackageServiceId());
            if (moeGroomingPackageService == null) {
                continue;
            }
            Integer remainingQuantity = moeGroomingPackageService.getRemainingQuantity();
            if (remainingQuantity < packageUsedParam.getQuantity()) {
                continue;
            }

            // 增加使用历史记录
            MoeGroomingPackageHistory moeGroomingPackageHistory = new MoeGroomingPackageHistory();
            moeGroomingPackageHistory.setInvoiceId(packageUsedParam.getInvoiceId());
            moeGroomingPackageHistory.setPackageId(packageUsedParam.getPackageId());
            moeGroomingPackageHistory.setQuantity(packageUsedParam.getQuantity());
            moeGroomingPackageHistory.setServiceId(packageUsedParam.getServiceId());
            moeGroomingPackageHistory.setGroomingId(packageUsedParam.getGroomingId());
            moeGroomingPackageHistory.setUseTime(CommonUtil.get10Timestamp());

            MoeGroomingAppointment moeGroomingAppointment =
                    moeGroomingAppointmentMapper.selectByPrimaryKey(packageUsedParam.getGroomingId());
            if (moeGroomingAppointment == null) {
                continue;
            }

            moeGroomingPackageHistory.setAppointmentDate(moeGroomingAppointment.getAppointmentDate());

            histories.add(moeGroomingPackageHistory);
        }
        return histories;
    }

    // 0203 @Transactional
    public ResponseResult<Integer> usePackageForGrooming(List<PackageUsedParams> packageUsedParams) {
        List<MoeGroomingPackageService> updates = new ArrayList<>();
        List<Integer> historiesIds = new ArrayList<>();
        long useTime = CommonUtil.get10Timestamp();
        for (PackageUsedParams packageUsedParam : packageUsedParams) {
            if (packageUsedParam.getQuantity() < 1) {
                continue;
            }
            MoeGroomingPackageService moeGroomingPackageService = packageServiceMapper.queryPackageServiceByProp(
                    packageUsedParam.getBusinessId(),
                    packageUsedParam.getCustomerId(),
                    packageUsedParam.getPackageId(),
                    packageUsedParam.getPackageServiceId());
            if (moeGroomingPackageService == null) {
                log.error(
                        "package or package services not exists, packageId: {}, packageServiceId: {}",
                        packageUsedParam.getPackageId(),
                        packageUsedParam.getPackageServiceId());
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "package or package services not exists");
            }
            if (moeGroomingPackageService.getRemainingQuantity() < packageUsedParam.getQuantity()) {
                log.error(
                        "package service quantity not enough, invoiceId: {}, packageServiceId: {}, total: {}, remain: {}, expected: {}",
                        packageUsedParam.getInvoiceId(),
                        packageUsedParam.getPackageServiceId(),
                        moeGroomingPackageService.getTotalQuantity(),
                        moeGroomingPackageService.getRemainingQuantity(),
                        packageUsedParam.getQuantity());
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "package service quantity not enough");
            }

            MoeGroomingAppointment moeGroomingAppointment =
                    moeGroomingAppointmentMapper.selectByPrimaryKey(packageUsedParam.getGroomingId());
            if (moeGroomingAppointment == null) {
                log.error("the pay appointment not exist, groomingId: {}", packageUsedParam.getGroomingId());
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "the pay appointment not exist");
            }

            moeGroomingPackageService = new MoeGroomingPackageService();
            moeGroomingPackageService.setId(packageUsedParam.getPackageServiceId());
            moeGroomingPackageService.setRemainingQuantity(packageUsedParam.getQuantity());
            updates.add(moeGroomingPackageService);

            // 增加使用历史记录
            MoeGroomingPackageHistory moeGroomingPackageHistory = new MoeGroomingPackageHistory();
            moeGroomingPackageHistory.setInvoiceId(packageUsedParam.getInvoiceId());
            moeGroomingPackageHistory.setPackageId(packageUsedParam.getPackageId());
            moeGroomingPackageHistory.setPackageServiceId(packageUsedParam.getPackageServiceId());
            moeGroomingPackageHistory.setQuantity(packageUsedParam.getQuantity());
            moeGroomingPackageHistory.setServiceId(packageUsedParam.getServiceId());
            moeGroomingPackageHistory.setGroomingId(packageUsedParam.getGroomingId());
            moeGroomingPackageHistory.setUseTime(useTime);
            moeGroomingPackageHistory.setAppointmentDate(moeGroomingAppointment.getAppointmentDate());
            moeGroomingPackageHistoryMapper.insertSelective(moeGroomingPackageHistory);
            historiesIds.add(moeGroomingPackageHistory.getId());
        }
        if (!updates.isEmpty()) {
            packageServiceMapper.batchUpdateMoeGroomingPackageService(updates);
        }
        // 推送 package 消费事件
        packageProducer.pushPackageRedeemedEvent(historiesIds, useTime);
        return ResponseResult.success(updates.size());
    }

    public Boolean deleteCustomerPackage(Integer businessId, Integer staffId, Integer id) {
        MoeGroomingPackage groomingPackage = moeGroomingPackageMapper.selectByPrimaryKey(id);
        if (Objects.isNull(groomingPackage) || !Objects.equals(businessId, groomingPackage.getBusinessId())) {
            return Boolean.FALSE;
        }

        MoeGroomingPackage moeGroomingPackage = new MoeGroomingPackage();
        moeGroomingPackage.setId(id);
        moeGroomingPackage.setStatus(REMOVED);
        moeGroomingPackage.setUpdateTime(CommonUtil.get10Timestamp());
        moeGroomingPackageMapper.updateByPrimaryKeySelective(moeGroomingPackage);

        ActivityLogRecorder.record(Action.DELETE, ResourceType.CUSTOMER_PACKAGE, id, moeGroomingPackage);

        return iRetailClient.removeInvoice(groomingPackage.getRetailInvoiceItemId(), staffId);
    }

    /**
     * 移除订单的 package 使用记录
     * 2023-02-14: 从 OrderService#removePackage 迁移过来，优化部分代码
     *
     * @param deletingList 需要移除的 package apply 记录
     */
    public void invalidApplyPackageRecord(List<MoeGroomingInvoiceApplyPackage> deletingList) {
        for (MoeGroomingInvoiceApplyPackage deleting : deletingList) {
            if (deleting.getQuantity() == 0) {
                deleteApplyRecord(deleting.getInvoiceId(), deleting.getPackageServiceId(), deleting.getServiceId());
            } else {
                invoiceApplyPackageMapper.updateByPrimaryKeySelective(deleting);
            }
        }
    }

    private void deleteApplyRecord(Integer invoiceId, Integer packageServiceId, Integer serviceId) {
        var e = new MoeGroomingInvoiceApplyPackageExample();
        e.createCriteria()
                .andInvoiceIdEqualTo(invoiceId)
                .andPackageServiceIdEqualTo(packageServiceId)
                .andServiceIdEqualTo(serviceId);
        invoiceApplyPackageMapper.deleteByExample(e);
    }

    /**
     * 失效 package history 记录，退回 package 数量
     * 2023-02-14: 增加失效 History 记录处理 (https://moego.atlassian.net/browse/ERP-3060)
     *
     * @param invoiceId
     */
    @Transactional
    public void invalidPackageUsedHistory(Integer invoiceId, Integer packageServiceId, Integer quantity) {
        // 查询订单的 package 使用记录
        List<GroomingPackageHistoryDTO> packageHistoryList =
                moeGroomingPackageHistoryMapper.queryPackageHistoryByInvoiceId(invoiceId);
        if (CollectionUtils.isEmpty(packageHistoryList)) {
            return;
        }
        // 查询关联的 package service
        MoeGroomingPackageService packageService = packageServiceMapper.selectByPrimaryKey(packageServiceId);
        if (Objects.isNull(packageService)) {
            return;
        }
        List<Integer> invalidIds = new ArrayList<>();
        Integer returnQuantity = 0;
        for (GroomingPackageHistoryDTO packageHistory : packageHistoryList) {
            if (Objects.equals(packageHistory.getPackageId(), packageService.getPackageId())
                    && Objects.equals(packageHistory.getServiceId(), packageService.getServiceId())) {
                invalidIds.add(packageHistory.getId());
                returnQuantity += packageHistory.getQuantity();
            }
        }

        if (Objects.nonNull(quantity)) {
            returnQuantity = Math.min(returnQuantity, quantity);
        }

        // 增加判空，避免数据库报错
        if (CollectionUtils.isEmpty(invalidIds)) {
            return;
        }
        // 失效 history 记录，返回 package_service quantity
        moeGroomingPackageHistoryMapper.batchInvalidGroomingPackageHistories(invalidIds);
        packageServiceMapper.updateQuantityById(packageServiceId, returnQuantity);
    }

    /**
     * 失效 package 使用记录，并返回 package service 数量
     * 目前只有 cancelled 预约时调用
     *
     * @param invoiceIds 需要删除的 invoiceIds
     */
    @Transactional
    public void invalidPackageUsedHistory(List<Integer> invoiceIds) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return;
        }

        // 删除 apply 记录
        deleteApplyRecords(invoiceIds);

        // 删除 redeem 记录
        deleteRedeemRecords(invoiceIds);
    }

    private void deleteApplyRecords(List<Integer> orderIds) {
        if (orderIds.isEmpty()) {
            return;
        }

        var updateBean = new MoeGroomingInvoiceApplyPackage();
        updateBean.setStatus(CommonConstant.DELETED);

        var e = new MoeGroomingInvoiceApplyPackageExample();
        e.createCriteria().andInvoiceIdIn(orderIds);
        invoiceApplyPackageMapper.updateByExampleSelective(updateBean, e);
    }

    /**
     * 删除 order 的 package redeem 记录。
     *
     * <p>
     * 这个操作会将 package service 的剩余数量回滚。
     *
     * @param orderIds order ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteRedeemRecords(List<Integer> orderIds) {
        if (orderIds.isEmpty()) {
            return;
        }

        // 回滚 package service 剩余数量
        revertPackageRemainingQuantity(orderIds);

        // 删除 redeem 记录
        doDeleteRedeemRecords(orderIds);
    }

    private void revertPackageRemainingQuantity(List<Integer> orderIds) {
        if (ObjectUtils.isEmpty(orderIds)) {
            return;
        }

        var e = new MoeGroomingPackageHistoryExample();
        e.createCriteria().andInvoiceIdIn(orderIds).andStatusEqualTo(CommonConstant.NORMAL);
        var packageServiceIdToConsumeCount = moeGroomingPackageHistoryMapper.selectByExample(e).stream()
                .collect(groupingBy(
                        MoeGroomingPackageHistory::getPackageServiceId,
                        summingInt(MoeGroomingPackageHistory::getQuantity)));
        if (packageServiceIdToConsumeCount.isEmpty()) {
            return;
        }

        var example = new MoeGroomingPackageServiceExample();
        example.createCriteria().andIdIn(List.copyOf(packageServiceIdToConsumeCount.keySet()));
        var packageServices = packageServiceMapper.selectByExample(example);

        for (var packageService : packageServices) {
            var consumeCount = packageServiceIdToConsumeCount.get(packageService.getId());
            if (consumeCount == null) {
                continue;
            }
            var update = new MoeGroomingPackageService();
            update.setId(packageService.getId());
            update.setRemainingQuantity(
                    Math.min(packageService.getTotalQuantity(), packageService.getRemainingQuantity() + consumeCount));
            packageServiceMapper.updateByPrimaryKeySelective(update);
        }
    }

    private void doDeleteRedeemRecords(List<Integer> orderIds) {
        if (ObjectUtils.isEmpty(orderIds)) {
            return;
        }

        var updateBean = new MoeGroomingPackageHistory();
        updateBean.setStatus(CommonConstant.DELETED);

        var example = new MoeGroomingPackageHistoryExample();
        example.createCriteria().andInvoiceIdIn(orderIds);
        moeGroomingPackageHistoryMapper.updateByExampleSelective(updateBean, example);
    }

    /**
     * 编辑预约时更新 package 使用记录
     */
    public void updateOrderApplyPackageRecord(Integer invoiceId, List<MoeGroomingInvoiceItem> updateItems) {
        // 查询当前 invoice 是否有 apply package 记录
        List<MoeGroomingInvoiceApplyPackage> applyRecords = invoiceApplyPackageMapper.selectByInvoiceId(invoiceId);
        if (CollectionUtils.isEmpty(applyRecords)) {
            return;
        }

        Map<Integer, MoeGroomingInvoiceItem> invoiceItemMap = updateItems.stream()
                .filter(item -> item.getId() != null && !item.getIsDeleted())
                .collect(toMap(MoeGroomingInvoiceItem::getId, identity()));

        List<MoeGroomingInvoiceApplyPackage> updateRecords = new ArrayList<>();
        // 从 invoiceItems 取对应使用的 apply 记录
        for (MoeGroomingInvoiceApplyPackage applyRecord : applyRecords) {
            MoeGroomingInvoiceItem item = invoiceItemMap.get(applyRecord.getInvoiceItemId());
            if (item != null) {
                // purchased quantity 更新
                if (!Objects.equals(applyRecord.getQuantity(), item.getPurchasedQuantity())) {
                    MoeGroomingInvoiceApplyPackage updateRecord = new MoeGroomingInvoiceApplyPackage();
                    updateRecord.setId(applyRecord.getId());
                    updateRecord.setQuantity(item.getPurchasedQuantity());
                    updateRecord.setUpdateTime(DateUtil.get10Timestamp());
                    updateRecords.add(updateRecord);
                }
            } else {
                // 当前 apply record 关联的 item 被删除了，失效当前 apply record
                MoeGroomingInvoiceApplyPackage updateRecord = new MoeGroomingInvoiceApplyPackage();
                updateRecord.setId(applyRecord.getId());
                updateRecord.setStatus(PackageConstant.APPLY_RECORD_STATUS_INVALID);
                updateRecord.setUpdateTime(DateUtil.get10Timestamp());
                updateRecords.add(updateRecord);
            }
        }

        if (!CollectionUtils.isEmpty(updateRecords)) {
            invoiceApplyPackageMapper.batchUpdateAppliedRecord(updateRecords);
        }
    }
}
