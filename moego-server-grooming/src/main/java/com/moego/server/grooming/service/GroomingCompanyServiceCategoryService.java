package com.moego.server.grooming.service;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.server.grooming.mapper.MoeGroomingServiceCategoryMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory;
import com.moego.server.grooming.service.dto.ServiceCategoryListDto;
import com.moego.server.grooming.service.dto.ServiceCategoryUpdateDto;
import com.moego.server.grooming.web.params.ServiceCategoryBatchUpdateParams;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroomingCompanyServiceCategoryService {

    @Autowired
    private MoeGroomingServiceCategoryMapper groomingServiceCategoryMapper;

    @Autowired
    private MoeGroomingServiceMapper groomingServiceMapper;

    public Boolean checkCategoryNameIsExist(Long companyId, String name, Byte type, ServiceItemType serviceItemType) {
        return checkCategoryNameIsExist(companyId, name, type, null, serviceItemType);
    }

    public Boolean checkCategoryNameIsExist(
            Long companyId, String name, Byte type, Integer updateCategoryId, ServiceItemType serviceItemType) {
        return groomingServiceCategoryMapper.getCategoryCountWithNameByCompanyId(
                        companyId,
                        name,
                        type,
                        updateCategoryId,
                        serviceItemType == null ? ServiceItemType.GROOMING.getNumber() : serviceItemType.getNumber())
                > 0;
    }

    public List<ServiceCategoryListDto> getEditServiceCategoryForCompany(
            Long companyId, Byte type, ServiceItemType serviceItemType) {
        List<MoeGroomingServiceCategory> serviceCategoryList = groomingServiceCategoryMapper.selectByCompanyId(
                companyId,
                type,
                serviceItemType == null ? ServiceItemType.GROOMING.getNumber() : serviceItemType.getNumber());
        List<ServiceCategoryListDto> categoryList = new ArrayList<>();
        for (MoeGroomingServiceCategory serviceCategory : serviceCategoryList) {
            ServiceCategoryListDto categoryDto = new ServiceCategoryListDto();
            BeanUtils.copyProperties(serviceCategory, categoryDto);
            categoryDto.setCategoryId(serviceCategory.getId());
            categoryDto.setType(serviceCategory.getType().intValue());
            categoryList.add(categoryDto);
        }
        return categoryList;
    }

    @Transactional
    public void updateServiceCategoryBatch(Long companyId, ServiceCategoryBatchUpdateParams batchUpdateParams) {
        // get categoryId list
        List<Integer> categoryIdList = new java.util.ArrayList<>(batchUpdateParams.getCategorySaveList().stream()
                .map(ServiceCategoryUpdateDto::getCategoryId)
                .toList());
        categoryIdList.removeIf(Objects::isNull);
        List<Integer> validCategoryIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(categoryIdList)) {
            List<MoeGroomingServiceCategory> categoryList =
                    groomingServiceCategoryMapper.selectCategoryByCidCategoryIds(companyId, categoryIdList);
            // 有效校验
            validCategoryIdList =
                    categoryList.stream().map(MoeGroomingServiceCategory::getId).toList();
        }

        // need deleted category id list
        List<MoeGroomingServiceCategory> existCategoryList = groomingServiceCategoryMapper.selectByCompanyId(
                companyId, batchUpdateParams.getType(), batchUpdateParams.getServiceItemType());
        List<Integer> existCategoryIds = existCategoryList.stream()
                .map(MoeGroomingServiceCategory::getId)
                .toList();
        List<Integer> needDeleteCategoryList = new ArrayList<>(existCategoryIds);
        needDeleteCategoryList.removeAll(validCategoryIdList);

        // 不允许同时编辑多个 type 的数据
        // category name check
        var type = batchUpdateParams.getType();
        List<Integer> needUpdateNameIds = new ArrayList<>();
        List<String> needCreateNames = new ArrayList<>();
        List<String> needUpdateNames = new ArrayList<>();
        for (var categoryParam : batchUpdateParams.getCategorySaveList()) {
            if (categoryParam.getCategoryId() != null) {
                needUpdateNameIds.add(categoryParam.getCategoryId());
                needUpdateNames.add(categoryParam.getName());
            } else {
                needCreateNames.add(categoryParam.getName());
            }
        }
        // update name 允许重名
        List<String> updateNameDistinctList =
                needUpdateNames.stream().distinct().toList();
        // create name 不允许重名
        List<String> createNameDistinctList =
                needCreateNames.stream().distinct().toList();
        if (createNameDistinctList.size() < needCreateNames.size()) {
            throw bizException(Code.CODE_SERVICE_CATEGORY_NAME_IS_EXIST);
        }
        List<String> allName = new ArrayList<>();
        allName.addAll(updateNameDistinctList);
        allName.addAll(createNameDistinctList);
        List<String> allNameDistinctList = allName.stream().distinct().toList();
        // create name 不允许和 update 重名
        if (allNameDistinctList.size() < allName.size()) {
            throw bizException(Code.CODE_SERVICE_CATEGORY_NAME_IS_EXIST);
        }
        // new update name 不允许和 exist name 重名
        needUpdateNameIds.addAll(needDeleteCategoryList);
        if (!CollectionUtils.isEmpty(allName)
                && groomingServiceCategoryMapper.batchCountCategoryName(
                                companyId, type, allName, needUpdateNameIds, batchUpdateParams.getServiceItemType())
                        > 0) {
            throw bizException(Code.CODE_SERVICE_CATEGORY_NAME_IS_EXIST);
        }
        int sortIndex = 0;
        for (int i = batchUpdateParams.getCategorySaveList().size(); 0 < i; --i) {
            var categoryParam = batchUpdateParams.getCategorySaveList().get(i - 1);
            Integer categoryId = categoryParam.getCategoryId();
            var category = new MoeGroomingServiceCategory();
            category.setName(categoryParam.getName());
            category.setSort(sortIndex);
            category.setUpdateTime(CommonUtil.get10Timestamp());
            // insert
            if (categoryId == null) {
                category.setType(categoryParam.getType());
                category.setCompanyId(companyId);
                category.setCreateTime(category.getUpdateTime());
                category.setServiceItemType(batchUpdateParams.getServiceItemType());
                groomingServiceCategoryMapper.insertSelective(category);
            } else {
                // update
                if (!validCategoryIdList.contains(categoryId)) {
                    continue;
                }
                category.setId(categoryId);
                groomingServiceCategoryMapper.updateByPrimaryKeySelective(category);
            }
            ++sortIndex;
        }
        for (Integer categoryId : needDeleteCategoryList) {
            MoeGroomingServiceCategory serviceCategory = new MoeGroomingServiceCategory();
            serviceCategory.setId(categoryId);
            serviceCategory.setCompanyId(companyId);
            serviceCategory.setStatus(DeleteStatusEnum.STATUS_DELETE);
            serviceCategory.setUpdateTime(DateUtil.get10Timestamp());
            groomingServiceCategoryMapper.updateByPrimaryKeySelectiveWithCid(serviceCategory);
            // 删除category后，要将当前的绑定了当前category，移到默认组下(既category_id=0)
            groomingServiceMapper.setServiceCategoryDefaultForCid(categoryId, companyId);
        }
    }
}
