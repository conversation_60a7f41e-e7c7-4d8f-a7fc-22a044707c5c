package com.moego.server.grooming.service.intuit.helper;

import com.intuit.ipp.data.PaymentMethod;
import com.intuit.ipp.data.ReferenceType;

public class PaymentMethodHelper {

    public static ReferenceType getPaymentMethodRef(PaymentMethod paymentMethod) {
        ReferenceType paymentMethodRef = new ReferenceType();
        paymentMethodRef.setValue(paymentMethod.getId());
        return paymentMethodRef;
    }

    public static PaymentMethod getPaymentMethodByName(String method) {
        PaymentMethod paymentMethod = new PaymentMethod();
        paymentMethod.setName(method);
        //        paymentMethod.setType(PaymentMethodEnum.OTHER.name());
        return paymentMethod;
    }

    public static PaymentMethod getPaymentMethodById(String qbPaymentMethodId) {
        PaymentMethod paymentMethod = new PaymentMethod();
        paymentMethod.setId(qbPaymentMethodId);
        return paymentMethod;
    }
}
