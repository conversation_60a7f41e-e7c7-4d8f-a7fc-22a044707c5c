<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.AppointmentPetMedicationMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.AppointmentPetMedication">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="appointment_id" jdbcType="BIGINT" property="appointmentId" />
    <result column="pet_detail_id" jdbcType="BIGINT" property="petDetailId" />
    <result column="pet_id" jdbcType="BIGINT" property="petId" />
    <result column="medication_amount" jdbcType="VARCHAR" property="medicationAmount" />
    <result column="medication_unit" jdbcType="VARCHAR" property="medicationUnit" />
    <result column="medication_name" jdbcType="VARCHAR" property="medicationName" />
    <result column="medication_note" jdbcType="VARCHAR" property="medicationNote" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt" />
    <result column="date_type" jdbcType="INTEGER" property="dateType" typeHandler="com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler" />
    <result column="specific_dates" jdbcType="VARCHAR" property="specificDates" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.dateTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.dateTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, appointment_id, pet_detail_id, pet_id, medication_amount, medication_unit, 
    medication_name, medication_note, created_at, updated_at, deleted_at, date_type, 
    specific_dates
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetMedicationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from appointment_pet_medication
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from appointment_pet_medication
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from appointment_pet_medication
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetMedicationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from appointment_pet_medication
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetMedication">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_pet_medication (company_id, appointment_id, pet_detail_id, 
      pet_id, medication_amount, medication_unit, 
      medication_name, medication_note, created_at, 
      updated_at, deleted_at, date_type, 
      specific_dates)
    values (#{companyId,jdbcType=BIGINT}, #{appointmentId,jdbcType=BIGINT}, #{petDetailId,jdbcType=BIGINT}, 
      #{petId,jdbcType=BIGINT}, #{medicationAmount,jdbcType=VARCHAR}, #{medicationUnit,jdbcType=VARCHAR}, 
      #{medicationName,jdbcType=VARCHAR}, #{medicationNote,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{deletedAt,jdbcType=TIMESTAMP}, #{dateType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler}, 
      #{specificDates,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetMedication">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_pet_medication
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="appointmentId != null">
        appointment_id,
      </if>
      <if test="petDetailId != null">
        pet_detail_id,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="medicationAmount != null">
        medication_amount,
      </if>
      <if test="medicationUnit != null">
        medication_unit,
      </if>
      <if test="medicationName != null">
        medication_name,
      </if>
      <if test="medicationNote != null">
        medication_note,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="deletedAt != null">
        deleted_at,
      </if>
      <if test="dateType != null">
        date_type,
      </if>
      <if test="specificDates != null">
        specific_dates,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appointmentId != null">
        #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="petDetailId != null">
        #{petDetailId,jdbcType=BIGINT},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=BIGINT},
      </if>
      <if test="medicationAmount != null">
        #{medicationAmount,jdbcType=VARCHAR},
      </if>
      <if test="medicationUnit != null">
        #{medicationUnit,jdbcType=VARCHAR},
      </if>
      <if test="medicationName != null">
        #{medicationName,jdbcType=VARCHAR},
      </if>
      <if test="medicationNote != null">
        #{medicationNote,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedAt != null">
        #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="dateType != null">
        #{dateType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler},
      </if>
      <if test="specificDates != null">
        #{specificDates,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetMedicationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from appointment_pet_medication
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_medication
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.appointmentId != null">
        appointment_id = #{record.appointmentId,jdbcType=BIGINT},
      </if>
      <if test="record.petDetailId != null">
        pet_detail_id = #{record.petDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.petId != null">
        pet_id = #{record.petId,jdbcType=BIGINT},
      </if>
      <if test="record.medicationAmount != null">
        medication_amount = #{record.medicationAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.medicationUnit != null">
        medication_unit = #{record.medicationUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.medicationName != null">
        medication_name = #{record.medicationName,jdbcType=VARCHAR},
      </if>
      <if test="record.medicationNote != null">
        medication_note = #{record.medicationNote,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deletedAt != null">
        deleted_at = #{record.deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dateType != null">
        date_type = #{record.dateType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler},
      </if>
      <if test="record.specificDates != null">
        specific_dates = #{record.specificDates,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_medication
    set id = #{record.id,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      appointment_id = #{record.appointmentId,jdbcType=BIGINT},
      pet_detail_id = #{record.petDetailId,jdbcType=BIGINT},
      pet_id = #{record.petId,jdbcType=BIGINT},
      medication_amount = #{record.medicationAmount,jdbcType=VARCHAR},
      medication_unit = #{record.medicationUnit,jdbcType=VARCHAR},
      medication_name = #{record.medicationName,jdbcType=VARCHAR},
      medication_note = #{record.medicationNote,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      deleted_at = #{record.deletedAt,jdbcType=TIMESTAMP},
      date_type = #{record.dateType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler},
      specific_dates = #{record.specificDates,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetMedication">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_medication
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appointmentId != null">
        appointment_id = #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="petDetailId != null">
        pet_detail_id = #{petDetailId,jdbcType=BIGINT},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=BIGINT},
      </if>
      <if test="medicationAmount != null">
        medication_amount = #{medicationAmount,jdbcType=VARCHAR},
      </if>
      <if test="medicationUnit != null">
        medication_unit = #{medicationUnit,jdbcType=VARCHAR},
      </if>
      <if test="medicationName != null">
        medication_name = #{medicationName,jdbcType=VARCHAR},
      </if>
      <if test="medicationNote != null">
        medication_note = #{medicationNote,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedAt != null">
        deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="dateType != null">
        date_type = #{dateType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler},
      </if>
      <if test="specificDates != null">
        specific_dates = #{specificDates,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetMedication">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_medication
    set company_id = #{companyId,jdbcType=BIGINT},
      appointment_id = #{appointmentId,jdbcType=BIGINT},
      pet_detail_id = #{petDetailId,jdbcType=BIGINT},
      pet_id = #{petId,jdbcType=BIGINT},
      medication_amount = #{medicationAmount,jdbcType=VARCHAR},
      medication_unit = #{medicationUnit,jdbcType=VARCHAR},
      medication_name = #{medicationName,jdbcType=VARCHAR},
      medication_note = #{medicationNote,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      date_type = #{dateType,jdbcType=INTEGER,typeHandler=com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler},
      specific_dates = #{specificDates,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>