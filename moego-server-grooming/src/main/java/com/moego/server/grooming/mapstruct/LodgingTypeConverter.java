package com.moego.server.grooming.mapstruct;

import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.server.grooming.dto.LodgingTypeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LodgingTypeConverter {
    LodgingTypeConverter INSTANCE = Mappers.getMapper(LodgingTypeConverter.class);

    LodgingTypeDTO toLodgingTypeDTO(LodgingTypeModel lodgingTypeModel);
}
