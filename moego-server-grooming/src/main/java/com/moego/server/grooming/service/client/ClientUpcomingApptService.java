package com.moego.server.grooming.service.client;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.BusinessCustomerConst;
import com.moego.common.enums.ClientApptConst;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.params.PageQuery;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerShareInfoDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.dto.client.ListClientApptDTO;
import com.moego.server.grooming.service.dto.client.ListUpcomingApptDTO;
import com.moego.server.grooming.web.vo.client.ClientApptDetailVO;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@Slf4j
@Service
@AllArgsConstructor
public class ClientUpcomingApptService implements IBaseClientApptService {

    private final AppointmentMapperProxy appointmentMapper;

    private final ClientApptService clientApptService;

    private final ICustomerCustomerClient customerClient;

    private final IBusinessBusinessClient businessClient;

    @Override
    public Page<MoeGroomingAppointment> getApptList(ListClientApptDTO listClientApptDTO, PageQuery pageQuery) {
        if (Objects.isNull(listClientApptDTO.getIsAppointmentOnly())) {
            listClientApptDTO.setIsAppointmentOnly(Boolean.TRUE);
        }
        List<BaseBusinessCustomerIdDTO> customerIdDTOList = listClientApptDTO.getCustomerIdDTOList();
        // Get customer share setting
        Map<Integer, CustomerShareInfoDTO> shareInfoDTOMap = getCustomerShareInfo(customerIdDTOList);
        // Get upcoming appt list
        List<ListUpcomingApptDTO> upcomingApptDTOList =
                buildUpcomingApptParams(customerIdDTOList, listClientApptDTO, shareInfoDTOMap);
        return PageHelper.startPage(pageQuery.getPageNum(), pageQuery.getPageSize())
                .doSelectPage(() -> appointmentMapper.listUpcomingAppt(upcomingApptDTOList, pageQuery));
    }

    public Map<Integer, CustomerShareInfoDTO> getCustomerShareInfo(List<BaseBusinessCustomerIdDTO> customerIdDTOList) {
        List<Integer> customerIdList = customerIdDTOList.stream()
                .map(BaseBusinessCustomerIdDTO::getCustomerId)
                .collect(Collectors.toList());
        CustomerIdListParams customerIdListParams = new CustomerIdListParams();
        customerIdListParams.setIdList(customerIdList);
        return customerClient.listCustomerShareInfo(customerIdListParams);
    }

    public List<ListUpcomingApptDTO> buildUpcomingApptParams(
            List<BaseBusinessCustomerIdDTO> customerIdDTOList,
            ListClientApptDTO listClientApptDTO,
            Map<Integer, CustomerShareInfoDTO> shareInfoDTOMap) {
        return customerIdDTOList.stream()
                .filter(customerIdDTO ->
                        listClientApptDTO.getBusinessDateTimeDTOMap().containsKey(customerIdDTO.getBusinessId()))
                .map(customerIdDTO -> {
                    ListUpcomingApptDTO upcomingApptDTO = new ListUpcomingApptDTO();
                    upcomingApptDTO.setBusinessId(customerIdDTO.getBusinessId());
                    upcomingApptDTO.setCustomerId(customerIdDTO.getCustomerId());
                    upcomingApptDTO.setCompanyId(customerIdDTO.getCompanyId());
                    upcomingApptDTO.setIsWaitingList(GroomingAppointmentEnum.NOT_WAITING_LIST);
                    upcomingApptDTO.setIsDeprecate(GroomingAppointmentEnum.IS_DEPRECATE_FALSE.byteValue());
                    upcomingApptDTO.setIsBlock(ClientApptConst.IS_BLOCK_FALSE);
                    if (BooleanUtils.isTrue(listClientApptDTO.getIsAppointmentOnly())) {
                        upcomingApptDTO.setBookOnlineStatus(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB);
                    }
                    BusinessDateTimeDTO dateTimeDTO =
                            listClientApptDTO.getBusinessDateTimeDTOMap().get(customerIdDTO.getBusinessId());
                    upcomingApptDTO.setCurrentDate(dateTimeDTO.getCurrentDate());
                    upcomingApptDTO.setCurrentMinutes(dateTimeDTO.getCurrentMinutes());
                    CustomerShareInfoDTO shareInfoDTO = shareInfoDTOMap.get(customerIdDTO.getCustomerId());
                    if (Objects.isNull(shareInfoDTO)) {
                        upcomingApptDTO.setStatusList(AppointmentStatusSet.ACTIVE_STATUS_SET);
                        return upcomingApptDTO;
                    }
                    // Share all status
                    if (Objects.equals(
                            shareInfoDTO.getShareApptStatus(), BusinessCustomerConst.SHARE_APPT_STATUS_ALL)) {
                        upcomingApptDTO.setStatusList(AppointmentStatusSet.ACTIVE_STATUS_SET);
                    } else {
                        upcomingApptDTO.setStatus(shareInfoDTO.getShareApptStatus());
                    }
                    Byte shareRangeType = shareInfoDTO.getShareRangeType();
                    upcomingApptDTO.setShareRangeType(shareRangeType);
                    if (Objects.equals(shareRangeType, BusinessCustomerConst.SHARE_RANGE_TYPE_NEXT_DAYS)) {
                        upcomingApptDTO.setEndDate(dateTimeDTO
                                .getLocalDateTime()
                                .plusDays(shareInfoDTO.getInNDays())
                                .toLocalDate()
                                .toString());
                    } else if (Objects.equals(shareRangeType, BusinessCustomerConst.SHARE_RANGE_TYPE_NEXT_APPT)) {
                        upcomingApptDTO.setLimit(shareInfoDTO.getLimitN());
                    } else if (Objects.equals(shareRangeType, BusinessCustomerConst.SHARE_RANGE_TYPE_MANUALLY)) {
                        shareInfoDTO.setShareApptIdList(shareInfoDTO.getShareApptIdList());
                    } else {
                        log.error("Invalid shareRangeType:{}", shareRangeType);
                    }
                    return upcomingApptDTO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Byte getApptType() {
        return ClientApptConst.APPT_TYPE_UPCOMING;
    }

    public ClientApptDetailVO getComingNextAppt(ListClientApptDTO listClientApptDTO, PageQuery pageQuery) {
        List<MoeGroomingAppointment> apptList = getApptList(listClientApptDTO, pageQuery);
        if (CollectionUtils.isEmpty(apptList)) {
            return new ClientApptDetailVO();
        }
        String bookingId = String.valueOf(apptList.get(0).getId());
        return clientApptService.getApptDetail(bookingId, listClientApptDTO.getCustomerIdDTOList());
    }

    /**
     * contains booking request and scheduled appointment
     *
     * @param linkCustomers link customers
     * @return appointment id
     */
    public Integer getComingNextApptId(List<BaseBusinessCustomerIdDTO> linkCustomers) {
        ListClientApptDTO listClientApptDTO = new ListClientApptDTO();
        listClientApptDTO.setCustomerIdDTOList(linkCustomers);
        Map<Integer, BusinessDateTimeDTO> businessDateTimeDTOMap =
                businessClient.listBusinessDateTime(linkCustomers.stream()
                        .map(BaseBusinessCustomerIdDTO::getBusinessId)
                        .toList());
        listClientApptDTO.setBusinessDateTimeDTOMap(businessDateTimeDTOMap);
        PageQuery pageQuery = new PageQuery()
                .setPageNum(1)
                .setPageSize(1)
                .setSortList(Arrays.asList(
                        new PageQuery.SortQuery().setSortBy("appointment_date").setOrder(PageQuery.OrderEnum.asc),
                        new PageQuery.SortQuery()
                                .setSortBy("appointment_start_time")
                                .setOrder(PageQuery.OrderEnum.asc)));
        listClientApptDTO.setIsAppointmentOnly(Boolean.FALSE);
        List<MoeGroomingAppointment> apptList = getApptList(listClientApptDTO, pageQuery);
        return Optional.ofNullable(apptList)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .map(list -> list.get(0).getId())
                .orElse(null);
    }
}
