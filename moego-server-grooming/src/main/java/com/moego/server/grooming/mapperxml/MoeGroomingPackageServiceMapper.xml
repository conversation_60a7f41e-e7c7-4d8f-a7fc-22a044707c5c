<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingPackageServiceMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingPackageService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="package_id" jdbcType="INTEGER" property="packageId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity" />
    <result column="remaining_quantity" jdbcType="INTEGER" property="remainingQuantity" />
    <result column="service_unit_price" jdbcType="DECIMAL" property="serviceUnitPrice" />
    <result column="services" jdbcType="CHAR" property="services" typeHandler="com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.servicesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.servicesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, package_id, service_id, total_quantity, remaining_quantity, service_unit_price,
    services
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageServiceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_package_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_package_service
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_package_service
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_package_service (package_id, service_id, total_quantity,
      remaining_quantity, service_unit_price, services
      )
    values (#{packageId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER}, #{totalQuantity,jdbcType=INTEGER},
      #{remainingQuantity,jdbcType=INTEGER}, #{serviceUnitPrice,jdbcType=DECIMAL}, #{services,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_package_service
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="packageId != null">
        package_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="totalQuantity != null">
        total_quantity,
      </if>
      <if test="remainingQuantity != null">
        remaining_quantity,
      </if>
      <if test="serviceUnitPrice != null">
        service_unit_price,
      </if>
      <if test="services != null">
        services,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="packageId != null">
        #{packageId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="totalQuantity != null">
        #{totalQuantity,jdbcType=INTEGER},
      </if>
      <if test="remainingQuantity != null">
        #{remainingQuantity,jdbcType=INTEGER},
      </if>
      <if test="serviceUnitPrice != null">
        #{serviceUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="services != null">
        #{services,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageServiceExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_package_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package_service
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.packageId != null">
        package_id = #{record.packageId,jdbcType=INTEGER},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=INTEGER},
      </if>
      <if test="record.totalQuantity != null">
        total_quantity = #{record.totalQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.remainingQuantity != null">
        remaining_quantity = #{record.remainingQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.serviceUnitPrice != null">
        service_unit_price = #{record.serviceUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.services != null">
        services = #{record.services,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package_service
    set id = #{record.id,jdbcType=INTEGER},
      package_id = #{record.packageId,jdbcType=INTEGER},
      service_id = #{record.serviceId,jdbcType=INTEGER},
      total_quantity = #{record.totalQuantity,jdbcType=INTEGER},
      remaining_quantity = #{record.remainingQuantity,jdbcType=INTEGER},
      service_unit_price = #{record.serviceUnitPrice,jdbcType=DECIMAL},
      services = #{record.services,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package_service
    <set>
      <if test="packageId != null">
        package_id = #{packageId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="totalQuantity != null">
        total_quantity = #{totalQuantity,jdbcType=INTEGER},
      </if>
      <if test="remainingQuantity != null">
        remaining_quantity = #{remainingQuantity,jdbcType=INTEGER},
      </if>
      <if test="serviceUnitPrice != null">
        service_unit_price = #{serviceUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="services != null">
        services = #{services,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package_service
    set package_id = #{packageId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      total_quantity = #{totalQuantity,jdbcType=INTEGER},
      remaining_quantity = #{remainingQuantity,jdbcType=INTEGER},
      service_unit_price = #{serviceUnitPrice,jdbcType=DECIMAL},
      services = #{services,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchUpdateMoeGroomingPackageService" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageService">
    <foreach collection="list" item="item" separator=";">
      UPDATE moe_grooming_package_service
      SET remaining_quantity = remaining_quantity - #{item.remainingQuantity}
      WHERE id = #{item.id}
      AND remaining_quantity &gt;= #{item.remainingQuantity}
    </foreach>
  </update>

  <update id="updateQuantityById">
    UPDATE moe_grooming_package_service
    SET remaining_quantity = remaining_quantity + #{quantity}
    WHERE id = #{id}
      AND total_quantity &gt;= remaining_quantity + #{quantity}
  </update>

  <update id="batchUpdateQuantityByIds">
    UPDATE moe_grooming_package_service
    <set>
      <trim prefix="remaining_quantity = CASE id" suffix="END," suffixOverrides=",">
        <foreach collection="updateList" item="item">
          WHEN #{item.packageServiceId} THEN remaining_quantity + #{item.quantity}
        </foreach>
      </trim>
    </set>
    WHERE id IN
    <foreach close=")" collection="updateList" item="item" open="(" separator=",">
      #{item.packageServiceId}
    </foreach>
  </update>

  <select id="queryPackageServiceByProp" resultMap="BaseResultMap">
        SELECT
          s.id,
          s.package_id,
          s.service_id,
          s.total_quantity,
          s.remaining_quantity,
          s.service_unit_price

          from moe_grooming_package p
          LEFT join moe_grooming_package_service s on p.id = s.package_id

          WHERE p.business_id = #{businessId}
          AND p.customer_id = #{customerId}
          and p.id = #{packageId}
          and s.id = #{packageServiceId}
          and p.status = 1
  </select>
</mapper>
