package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeQbSyncReceiptExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public MoeQbSyncReceiptExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andConnectIdIsNull() {
            addCriterion("connect_id is null");
            return (Criteria) this;
        }

        public Criteria andConnectIdIsNotNull() {
            addCriterion("connect_id is not null");
            return (Criteria) this;
        }

        public Criteria andConnectIdEqualTo(Integer value) {
            addCriterion("connect_id =", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotEqualTo(Integer value) {
            addCriterion("connect_id <>", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdGreaterThan(Integer value) {
            addCriterion("connect_id >", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("connect_id >=", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdLessThan(Integer value) {
            addCriterion("connect_id <", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdLessThanOrEqualTo(Integer value) {
            addCriterion("connect_id <=", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdIn(List<Integer> values) {
            addCriterion("connect_id in", values, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotIn(List<Integer> values) {
            addCriterion("connect_id not in", values, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdBetween(Integer value1, Integer value2) {
            addCriterion("connect_id between", value1, value2, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotBetween(Integer value1, Integer value2) {
            addCriterion("connect_id not between", value1, value2, "connectId");
            return (Criteria) this;
        }

        public Criteria andRealmIdIsNull() {
            addCriterion("realm_id is null");
            return (Criteria) this;
        }

        public Criteria andRealmIdIsNotNull() {
            addCriterion("realm_id is not null");
            return (Criteria) this;
        }

        public Criteria andRealmIdEqualTo(String value) {
            addCriterion("realm_id =", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotEqualTo(String value) {
            addCriterion("realm_id <>", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdGreaterThan(String value) {
            addCriterion("realm_id >", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdGreaterThanOrEqualTo(String value) {
            addCriterion("realm_id >=", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdLessThan(String value) {
            addCriterion("realm_id <", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdLessThanOrEqualTo(String value) {
            addCriterion("realm_id <=", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdLike(String value) {
            addCriterion("realm_id like", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotLike(String value) {
            addCriterion("realm_id not like", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdIn(List<String> values) {
            addCriterion("realm_id in", values, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotIn(List<String> values) {
            addCriterion("realm_id not in", values, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdBetween(String value1, String value2) {
            addCriterion("realm_id between", value1, value2, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotBetween(String value1, String value2) {
            addCriterion("realm_id not between", value1, value2, "realmId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdIsNull() {
            addCriterion("payment_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdIsNotNull() {
            addCriterion("payment_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdEqualTo(Integer value) {
            addCriterion("payment_detail_id =", value, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdNotEqualTo(Integer value) {
            addCriterion("payment_detail_id <>", value, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdGreaterThan(Integer value) {
            addCriterion("payment_detail_id >", value, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_detail_id >=", value, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdLessThan(Integer value) {
            addCriterion("payment_detail_id <", value, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdLessThanOrEqualTo(Integer value) {
            addCriterion("payment_detail_id <=", value, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdIn(List<Integer> values) {
            addCriterion("payment_detail_id in", values, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdNotIn(List<Integer> values) {
            addCriterion("payment_detail_id not in", values, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdBetween(Integer value1, Integer value2) {
            addCriterion("payment_detail_id between", value1, value2, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andPaymentDetailIdNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_detail_id not between", value1, value2, "paymentDetailId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdIsNull() {
            addCriterion("qb_receipt_id is null");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdIsNotNull() {
            addCriterion("qb_receipt_id is not null");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdEqualTo(String value) {
            addCriterion("qb_receipt_id =", value, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdNotEqualTo(String value) {
            addCriterion("qb_receipt_id <>", value, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdGreaterThan(String value) {
            addCriterion("qb_receipt_id >", value, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdGreaterThanOrEqualTo(String value) {
            addCriterion("qb_receipt_id >=", value, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdLessThan(String value) {
            addCriterion("qb_receipt_id <", value, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdLessThanOrEqualTo(String value) {
            addCriterion("qb_receipt_id <=", value, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdLike(String value) {
            addCriterion("qb_receipt_id like", value, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdNotLike(String value) {
            addCriterion("qb_receipt_id not like", value, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdIn(List<String> values) {
            addCriterion("qb_receipt_id in", values, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdNotIn(List<String> values) {
            addCriterion("qb_receipt_id not in", values, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdBetween(String value1, String value2) {
            addCriterion("qb_receipt_id between", value1, value2, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptIdNotBetween(String value1, String value2) {
            addCriterion("qb_receipt_id not between", value1, value2, "qbReceiptId");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusIsNull() {
            addCriterion("qb_receipt_status is null");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusIsNotNull() {
            addCriterion("qb_receipt_status is not null");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusEqualTo(Byte value) {
            addCriterion("qb_receipt_status =", value, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusNotEqualTo(Byte value) {
            addCriterion("qb_receipt_status <>", value, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusGreaterThan(Byte value) {
            addCriterion("qb_receipt_status >", value, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("qb_receipt_status >=", value, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusLessThan(Byte value) {
            addCriterion("qb_receipt_status <", value, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusLessThanOrEqualTo(Byte value) {
            addCriterion("qb_receipt_status <=", value, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusIn(List<Byte> values) {
            addCriterion("qb_receipt_status in", values, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusNotIn(List<Byte> values) {
            addCriterion("qb_receipt_status not in", values, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusBetween(Byte value1, Byte value2) {
            addCriterion("qb_receipt_status between", value1, value2, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andQbReceiptStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("qb_receipt_status not between", value1, value2, "qbReceiptStatus");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeIsNull() {
            addCriterion("receipt_type is null");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeIsNotNull() {
            addCriterion("receipt_type is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeEqualTo(Byte value) {
            addCriterion("receipt_type =", value, "receiptType");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeNotEqualTo(Byte value) {
            addCriterion("receipt_type <>", value, "receiptType");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeGreaterThan(Byte value) {
            addCriterion("receipt_type >", value, "receiptType");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("receipt_type >=", value, "receiptType");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeLessThan(Byte value) {
            addCriterion("receipt_type <", value, "receiptType");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeLessThanOrEqualTo(Byte value) {
            addCriterion("receipt_type <=", value, "receiptType");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeIn(List<Byte> values) {
            addCriterion("receipt_type in", values, "receiptType");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeNotIn(List<Byte> values) {
            addCriterion("receipt_type not in", values, "receiptType");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeBetween(Byte value1, Byte value2) {
            addCriterion("receipt_type between", value1, value2, "receiptType");
            return (Criteria) this;
        }

        public Criteria andReceiptTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("receipt_type not between", value1, value2, "receiptType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
