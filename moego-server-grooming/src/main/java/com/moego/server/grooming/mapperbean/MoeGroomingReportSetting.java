package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_report_setting
 */
public class MoeGroomingReportSetting {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_setting.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_setting.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   sending type, 1-automatically, 2-manually
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_setting.sending_type
     *
     * @mbg.generated
     */
    private Integer sendingType;

    /**
     * Database Column Remarks:
     *   sending method, 0b01-email, 0b10-sms, 0b11=3-email&sms
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_setting.sending_method
     *
     * @mbg.generated
     */
    private Byte sendingMethod;

    /**
     * Database Column Remarks:
     *   template last publish time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_setting.template_publish_time
     *
     * @mbg.generated
     */
    private Date templatePublishTime;

    /**
     * Database Column Remarks:
     *   last update staff id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_setting.update_by
     *
     * @mbg.generated
     */
    private Integer updateBy;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_setting.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_setting.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_setting.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_setting.id
     *
     * @return the value of moe_grooming_report_setting.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_setting.id
     *
     * @param id the value for moe_grooming_report_setting.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_setting.business_id
     *
     * @return the value of moe_grooming_report_setting.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_setting.business_id
     *
     * @param businessId the value for moe_grooming_report_setting.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_setting.sending_type
     *
     * @return the value of moe_grooming_report_setting.sending_type
     *
     * @mbg.generated
     */
    public Integer getSendingType() {
        return sendingType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_setting.sending_type
     *
     * @param sendingType the value for moe_grooming_report_setting.sending_type
     *
     * @mbg.generated
     */
    public void setSendingType(Integer sendingType) {
        this.sendingType = sendingType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_setting.sending_method
     *
     * @return the value of moe_grooming_report_setting.sending_method
     *
     * @mbg.generated
     */
    public Byte getSendingMethod() {
        return sendingMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_setting.sending_method
     *
     * @param sendingMethod the value for moe_grooming_report_setting.sending_method
     *
     * @mbg.generated
     */
    public void setSendingMethod(Byte sendingMethod) {
        this.sendingMethod = sendingMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_setting.template_publish_time
     *
     * @return the value of moe_grooming_report_setting.template_publish_time
     *
     * @mbg.generated
     */
    public Date getTemplatePublishTime() {
        return templatePublishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_setting.template_publish_time
     *
     * @param templatePublishTime the value for moe_grooming_report_setting.template_publish_time
     *
     * @mbg.generated
     */
    public void setTemplatePublishTime(Date templatePublishTime) {
        this.templatePublishTime = templatePublishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_setting.update_by
     *
     * @return the value of moe_grooming_report_setting.update_by
     *
     * @mbg.generated
     */
    public Integer getUpdateBy() {
        return updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_setting.update_by
     *
     * @param updateBy the value for moe_grooming_report_setting.update_by
     *
     * @mbg.generated
     */
    public void setUpdateBy(Integer updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_setting.create_time
     *
     * @return the value of moe_grooming_report_setting.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_setting.create_time
     *
     * @param createTime the value for moe_grooming_report_setting.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_setting.update_time
     *
     * @return the value of moe_grooming_report_setting.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_setting.update_time
     *
     * @param updateTime the value for moe_grooming_report_setting.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_setting.company_id
     *
     * @return the value of moe_grooming_report_setting.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_setting.company_id
     *
     * @param companyId the value for moe_grooming_report_setting.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
