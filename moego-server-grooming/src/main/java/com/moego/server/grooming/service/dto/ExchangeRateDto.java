package com.moego.server.grooming.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 4/27/21 3:37 PM
 */
@Data
@Schema(description = "货币汇率返回结果")
public class ExchangeRateDto {

    @Schema(description = "货币汇率当前时间戳")
    private Integer timestamp;

    @Schema(description = "货币汇率当前基准")
    private String base;

    @Schema(description = "货币汇率当前日期")
    private String date;

    @Schema(
            description = "货币汇率结果，如：{\"AUD\":1.292316,\"EUR\":0.828905,\"GBP\":0.720665,\"USD\":1,\"CAD\":1.240376,"
                    + "\"NZD\":1.388655,\"ZAR\":14.404399,\"BRL\":5.4526}")
    private Map<String, Object> rates;
}
