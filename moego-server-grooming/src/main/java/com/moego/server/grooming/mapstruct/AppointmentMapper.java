package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.AppointmentDTO;
import com.moego.server.grooming.dto.AppointmentDetailClientPortalDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.web.vo.ob.OBRequestDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface AppointmentMapper {
    AppointmentMapper INSTANCE = Mappers.getMapper(AppointmentMapper.class);

    AppointmentDTO entity2DTO(MoeGroomingAppointment entity);

    @Mapping(target = "apptId", source = "id")
    @Mapping(target = "additionalNote", ignore = true)
    @Mapping(target = "staff", ignore = true)
    @Mapping(target = "customer", ignore = true)
    @Mapping(target = "address", ignore = true)
    @Mapping(target = "pets", ignore = true)
    @Mapping(target = "services", ignore = true)
    @Mapping(target = "prepay", ignore = true)
    @Mapping(target = "hasRequestUpdate", ignore = true)
    @Mapping(target = "autoAssign", ignore = true)
    @Mapping(target = "discountCode", ignore = true)
    OBRequestDetailVO entity2OBRequestDetailVO(MoeGroomingAppointment entity);

    @Mapping(target = "paymentStatus", source = "isPaid")
    @Mapping(target = "petDetails", ignore = true)
    AppointmentDetailClientPortalDTO entityToDto(MoeGroomingAppointment entity);
}
