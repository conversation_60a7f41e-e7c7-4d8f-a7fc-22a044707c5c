package com.moego.server.grooming.service;

import static java.util.Comparator.comparing;

import com.moego.server.grooming.mapper.ObConfigClientReviewMapper;
import com.moego.server.grooming.mapperbean.ObConfigClientReview;
import com.moego.server.grooming.mapperbean.ObConfigClientReviewExample;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OBConfigClientReviewService {

    private final ObConfigClientReviewMapper obConfigClientReviewMapper;

    /**
     * List {@link ObConfigClientReview}s by business id.
     *
     * @param businessId businessId
     * @return {@link ObConfigClientReview}s
     */
    public List<ObConfigClientReview> list(Integer businessId) {
        ObConfigClientReviewExample example = new ObConfigClientReviewExample();
        example.createCriteria().andBusinessIdEqualTo(businessId);
        return obConfigClientReviewMapper.selectByExample(example).stream()
                .sorted(comparing(ObConfigClientReview::getSort))
                .toList();
    }

    /**
     * Reset {@link ObConfigClientReview}s by business id.
     *
     * @param businessId    businessId
     * @param clientReviews clientReviews
     * @return affected rows
     */
    @Transactional(rollbackFor = Exception.class)
    public int reset(Integer businessId, List<ObConfigClientReview> clientReviews) {
        ObConfigClientReviewExample example = new ObConfigClientReviewExample();
        example.createCriteria().andBusinessIdEqualTo(businessId);
        obConfigClientReviewMapper.deleteByExample(example);

        return !ObjectUtils.isEmpty(clientReviews) ? obConfigClientReviewMapper.batchInsertSelective(clientReviews) : 0;
    }
}
