package com.moego.server.grooming.service.intuit.qbo;

import com.google.protobuf.Timestamp;
import com.intuit.ipp.core.Context;
import com.intuit.ipp.core.IEntity;
import com.intuit.ipp.core.ServiceType;
import com.intuit.ipp.data.Error;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.security.OAuth2Authorizer;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.services.QueryResult;
import com.intuit.oauth2.data.BearerTokenResponse;
import com.intuit.oauth2.exception.OAuthException;
import com.moego.common.enums.QuickBooksConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.ratelimit.v1.AllowRequest;
import com.moego.idl.service.ratelimit.v1.RateLimitServiceGrpc;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.mapper.MoeQbConnectMapper;
import com.moego.server.grooming.mapperbean.MoeQbConnect;
import com.moego.server.grooming.service.QuickBooksService;
import com.moego.server.grooming.service.dto.QBBusinessSettingDto;
import com.moego.server.grooming.service.intuit.QuickBooksContext;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BusinessDataServiceFactory {

    @Autowired
    private OAuth2PlatformClientFactory factory;

    @Autowired
    private QuickBooksService quickBooksService;

    @Autowired
    private MoeQbConnectMapper qbConnectMapper;

    @Autowired
    private RateLimitServiceGrpc.RateLimitServiceBlockingStub rateLimitServiceBlockingStub;

    /**
     * rate limiter 请求方法, 当达到 upper 的时候, 限流服务会返回最小等待时间
     *
     * @param hits
     */
    private void rateLimiter(Integer hits) {
        var business = QuickBooksContext.getBusinessId();
        if (Objects.isNull(business)) {
            // 线程无business id 则有可能是接口调用, 接口调用时从AuthContext拿
            business = AuthContext.get().getBusinessId();
            // 如果还拿不到那就报错了
            if (Objects.isNull(business)) {
                log.error("rate limiter error, business id is null");
                // 业务id为空, 无法进行限流, 这里抛出异常可能不是很完备, 先改成返回
                return;
            }
        }

        var limit = 0;
        // 申请3次, 从实现逻辑来说3次都申请不到就是服务异常了
        Map<String, String> resources = Map.of("BusinessID", business.toString(), "API", "QuickBooksService");
        while (limit < 3) {
            var resp = rateLimitServiceBlockingStub.allow(AllowRequest.newBuilder()
                    .setDomain("QuickBooksService")
                    .setTimestamp(Timestamp.newBuilder()
                            .setSeconds(Instant.now().getEpochSecond())
                            .setNanos(Instant.now().getNano())
                            .build())
                    .setHits(hits > 0 ? hits : 1)
                    .putAllResources(resources)
                    .build());

            if (resp.getAllow()) break;
            try {
                // sleep 时长为预期 + 1s
                limit++;
                log.info("rate limiter, retry after {}s", resp.getRetryAfter().getSeconds() + 1);
                Thread.sleep((resp.getRetryAfter().getSeconds() + 1) * 1000L);
            } catch (InterruptedException e) {
                log.error("rate limiter error", e);
                throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, e.getMessage());
            }
        }
    }

    /**
     * 用户刷新token并记录在表内
     *
     * @param connect
     */
    public void refreshAccessToken(MoeQbConnect connect) {
        MoeQbConnect qbConnect = new MoeQbConnect();
        qbConnect.setUpdateTime(DateUtil.get10Timestamp());
        qbConnect.setId(connect.getId());
        try {
            // 刷新token
            BearerTokenResponse bearerTokenResponse =
                    factory.getOAuth2PlatformClient().refreshToken(connect.getRefreshToken());
            // 保存刷新后的token
            qbConnect.setRefreshToken(bearerTokenResponse.getRefreshToken());
            qbConnect.setAccessToken(bearerTokenResponse.getAccessToken());
            qbConnect.setTokenExpiredTime(qbConnect.getUpdateTime() + bearerTokenResponse.getExpiresIn());
            qbConnectMapper.updateByPrimaryKeySelective(qbConnect);
            // 更新accessToken后，刷新dataService
        } catch (OAuthException e) {
            log.error("qb refresh token error, message:" + e.getMessage());
            qbConnect.setConnectStatus(QuickBooksConst.CONNECT_STATUS_AUTH_ERROR);
            qbConnectMapper.updateByPrimaryKeySelective(qbConnect);
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_REFRESH_TOKEN_ERROR);
        }
    }

    /**
     * 检查access token是否过期
     *
     * @param connect
     * @return
     */
    public Boolean checkRefreshAccessToken(MoeQbConnect connect) {
        if (DateUtil.get10Timestamp() > connect.getTokenExpiredTime() - QuickBooksConst.EXPIRT_TIME_BEFORE_SECOND
                || !StringUtils.hasLength(connect.getAccessToken())) {
            refreshAccessToken(connect);
            BeanUtils.copyProperties(qbConnectMapper.selectByPrimaryKey(connect.getId()), connect);
            return true;
        }
        return false;
    }

    /**
     * new一个DataService
     *
     * @param connect
     * @return
     */
    public DataService getNewDataService(MoeQbConnect connect) {
        try {
            if (!QuickBooksConst.CONNECT_STATUS_DEFAULT.equals(connect.getConnectStatus())
                    && !QuickBooksConst.CONNECT_STATUS_NORMAL.equals(connect.getConnectStatus())) {
                throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, "connect status error");
            }
            checkRefreshAccessToken(connect);
            // 刷新后，connect值可能会变，需要重新判断
            if (!QuickBooksConst.CONNECT_STATUS_DEFAULT.equals(connect.getConnectStatus())
                    && !QuickBooksConst.CONNECT_STATUS_NORMAL.equals(connect.getConnectStatus())) {
                throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, "connect status error");
            }
            factory.updateConfigUrl();
            OAuth2Authorizer oauth = new OAuth2Authorizer(connect.getAccessToken());
            Context context = new Context(oauth, ServiceType.QBO, connect.getRealmId());
            return new DataService(context);
        } catch (FMSException e) {
            log.error("initDataService error, message:" + e.getMessage());
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, e.getMessage());
        }
    }

    public Context getContext(MoeQbConnect connect) {
        try {
            if (!QuickBooksConst.CONNECT_STATUS_DEFAULT.equals(connect.getConnectStatus())
                    && !QuickBooksConst.CONNECT_STATUS_NORMAL.equals(connect.getConnectStatus())) {
                throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, "connect status error");
            }
            checkRefreshAccessToken(connect);
            // 刷新后，connect值可能会变，需要重新判断
            if (!QuickBooksConst.CONNECT_STATUS_DEFAULT.equals(connect.getConnectStatus())
                    && !QuickBooksConst.CONNECT_STATUS_NORMAL.equals(connect.getConnectStatus())) {
                throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, "connect status error");
            }
            factory.updateConfigUrl();
            OAuth2Authorizer oauth = new OAuth2Authorizer(connect.getAccessToken());
            return new Context(oauth, ServiceType.QBO, connect.getRealmId());
        } catch (FMSException e) {
            log.error("initDataService error, message:" + e.getMessage());
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, e.getMessage());
        }
    }

    public DataService getNewDataService(Integer businessId) {
        QBBusinessSettingDto settingDto = quickBooksService.getQbConnectSettingMustAvailable(businessId);
        return getNewDataService(settingDto.getQbConnect());
    }

    public DataService getDataService(Integer businessId) {
        factory.updateConfigUrl();
        return getNewDataService(businessId);
    }

    /**
     * 获取getConnect
     *
     * @return
     */
    public MoeQbConnect getConnect(Integer businessId) {
        QBBusinessSettingDto settingDto = quickBooksService.getQbConnectSettingMustAvailable(businessId);
        return qbConnectMapper.selectByPrimaryKey(settingDto.getConnectId());
    }

    /**
     * 获取getConnectId
     *
     * @return
     */
    public Integer getConnectId(Integer businessId) {
        QBBusinessSettingDto settingDto = quickBooksService.getQbConnectSettingMustAvailable(businessId);
        return settingDto.getConnectId();
    }

    /**
     * 获取getConnectRealmId
     *
     * @return
     */
    public String getConnectRealmId(Integer businessId) {
        QBBusinessSettingDto settingDto = quickBooksService.getQbConnectSettingMustAvailable(businessId);
        return qbConnectMapper.selectByPrimaryKey(settingDto.getConnectId()).getRealmId();
    }

    public QueryResult executeQuery(MoeQbConnect connect, String queryStr) {
        try {
            DataService dataService = getNewDataService(connect);
            return executeQuery(dataService, queryStr);
        } catch (CommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("executeQuery sql:{}, connect:{}", queryStr, connect, e);
            throw e;
        }
    }

    public QueryResult executeQuery(DataService dataService, String queryStr) {
        try {
            return dataService.executeQuery(queryStr);
        } catch (FMSException e) {
            log.error("executeQuery sql:{}", queryStr, e);
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, e.getMessage());
        }
    }

    public <T extends IEntity> T dataServiceAdd(DataService service, T t) {
        try {
            // rate limiter
            rateLimiter(1);
            return service.add(t);
        } catch (FMSException e) {
            List<Error> list = e.getErrorList();
            list.forEach(error -> log.error("Error while calling entity add:: " + error.getMessage()));
            if (QuickBooksConst.QB_RETURN_ERROR_CODE_DUPLICATE_NAME_EXISTS.equals(
                    e.getErrorList().get(0).getCode())) {
                throw new CommonException(
                        ResponseCodeEnum.QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS,
                        getErrorMessageDetail(e.getErrorList().get(0).getDetail()));
            }
            if (QuickBooksConst.QB_RETURN_ERROR_CODE_EMAIL_ERROR.equals(
                    e.getErrorList().get(0).getCode())) {
                throw new CommonException(ResponseCodeEnum.QUICKBOOKS_DATA_DUPLICATE_EMAIL_FORMAT_ERROR);
            }
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, e.getMessage());
        }
    }

    public <T extends IEntity> T dataServiceUpdate(DataService service, T t) {
        try {
            // rate limiter
            rateLimiter(1);
            return service.update(t);
        } catch (FMSException e) {
            List<Error> list = e.getErrorList();
            list.forEach(error -> log.error("Error while calling entity dataServiceUpdate:: " + error.getMessage()));
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, e.getMessage());
        }
    }

    public <T extends IEntity> T dataServiceDelete(DataService service, T t) {
        try {
            // rate limiter
            rateLimiter(1);
            return service.delete(t);
        } catch (FMSException e) {
            List<Error> list = e.getErrorList();
            list.forEach(error -> log.error("Error while calling entity dataServiceDelete:: " + error.getMessage()));
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, e.getMessage());
        }
    }

    /**
     * e.g.:The name supplied already exists. : Id=35
     *
     * @param errorMessage
     * @return
     */
    private String getErrorMessageDetail(String errorMessage) {
        if (StringUtils.isEmpty(errorMessage)) {
            return null;
        }
        String[] splitArr = errorMessage.split("=");
        if (splitArr.length == 2 && CommonUtil.isNumeric(splitArr[1])) {
            return splitArr[1];
        }
        return null;
    }

    public <T extends IEntity> T dataServiceFindById(DataService service, T t) {
        try {
            // rate limiter
            rateLimiter(1);
            return service.findById(t);
        } catch (FMSException e) {
            // 在qb那边删除后，会抛出 object not found的异常
            if (QuickBooksConst.QB_RETURN_ERROR_CODE_OBJECT_NOT_FOUND.equals(
                    e.getErrorList().get(0).getCode())) {
                return null;
            }
            List<Error> list = e.getErrorList();
            list.forEach(error -> log.error("Error while dataServiceFindById:: " + error.getMessage()));
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, e.getMessage());
        }
    }

    public <T extends IEntity> List<T> dataServiceFindAll(DataService service, T t) {
        try {
            rateLimiter(1);
            return service.findAll(t);
        } catch (FMSException e) {
            List<Error> list = e.getErrorList();
            list.forEach(error -> log.error("Error while dataServiceFindAll:: " + error.getMessage()));
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_UNEXPECTED_EXCEPTION, e.getMessage());
        }
    }
}
