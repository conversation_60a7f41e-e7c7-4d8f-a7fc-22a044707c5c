package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeBookOnlineAbandonRecordExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public MoeBookOnlineAbandonRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> specificDatesCriteria;

        protected List<Criterion> allCriteria;

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
            specificDatesCriteria = new ArrayList<>();
        }

        public List<Criterion> getSpecificDatesCriteria() {
            return specificDatesCriteria;
        }

        protected void addSpecificDatesCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            specificDatesCriteria.add(new Criterion(
                    condition, value, "com.moego.server.grooming.mapper.typehandler.StringListTypeHandler"));
            allCriteria = null;
        }

        protected void addSpecificDatesCriterion(
                String condition, List<String> value1, List<String> value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            specificDatesCriteria.add(new Criterion(
                    condition, value1, value2, "com.moego.server.grooming.mapper.typehandler.StringListTypeHandler"));
            allCriteria = null;
        }

        public boolean isValid() {
            return criteria.size() > 0 || specificDatesCriteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            if (allCriteria == null) {
                allCriteria = new ArrayList<>();
                allCriteria.addAll(criteria);
                allCriteria.addAll(specificDatesCriteria);
            }
            return allCriteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
            allCriteria = null;
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdIsNull() {
            addCriterion("booking_flow_id is null");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdIsNotNull() {
            addCriterion("booking_flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdEqualTo(String value) {
            addCriterion("booking_flow_id =", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdNotEqualTo(String value) {
            addCriterion("booking_flow_id <>", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdGreaterThan(String value) {
            addCriterion("booking_flow_id >", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("booking_flow_id >=", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdLessThan(String value) {
            addCriterion("booking_flow_id <", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdLessThanOrEqualTo(String value) {
            addCriterion("booking_flow_id <=", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdLike(String value) {
            addCriterion("booking_flow_id like", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdNotLike(String value) {
            addCriterion("booking_flow_id not like", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdIn(List<String> values) {
            addCriterion("booking_flow_id in", values, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdNotIn(List<String> values) {
            addCriterion("booking_flow_id not in", values, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdBetween(String value1, String value2) {
            addCriterion("booking_flow_id between", value1, value2, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdNotBetween(String value1, String value2) {
            addCriterion("booking_flow_id not between", value1, value2, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andRefererIsNull() {
            addCriterion("referer is null");
            return (Criteria) this;
        }

        public Criteria andRefererIsNotNull() {
            addCriterion("referer is not null");
            return (Criteria) this;
        }

        public Criteria andRefererEqualTo(String value) {
            addCriterion("referer =", value, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererNotEqualTo(String value) {
            addCriterion("referer <>", value, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererGreaterThan(String value) {
            addCriterion("referer >", value, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererGreaterThanOrEqualTo(String value) {
            addCriterion("referer >=", value, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererLessThan(String value) {
            addCriterion("referer <", value, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererLessThanOrEqualTo(String value) {
            addCriterion("referer <=", value, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererLike(String value) {
            addCriterion("referer like", value, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererNotLike(String value) {
            addCriterion("referer not like", value, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererIn(List<String> values) {
            addCriterion("referer in", values, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererNotIn(List<String> values) {
            addCriterion("referer not in", values, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererBetween(String value1, String value2) {
            addCriterion("referer between", value1, value2, "referer");
            return (Criteria) this;
        }

        public Criteria andRefererNotBetween(String value1, String value2) {
            addCriterion("referer not between", value1, value2, "referer");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNull() {
            addCriterion("phone_number is null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNotNull() {
            addCriterion("phone_number is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberEqualTo(String value) {
            addCriterion("phone_number =", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotEqualTo(String value) {
            addCriterion("phone_number <>", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThan(String value) {
            addCriterion("phone_number >", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThanOrEqualTo(String value) {
            addCriterion("phone_number >=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThan(String value) {
            addCriterion("phone_number <", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThanOrEqualTo(String value) {
            addCriterion("phone_number <=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLike(String value) {
            addCriterion("phone_number like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotLike(String value) {
            addCriterion("phone_number not like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIn(List<String> values) {
            addCriterion("phone_number in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotIn(List<String> values) {
            addCriterion("phone_number not in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberBetween(String value1, String value2) {
            addCriterion("phone_number between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotBetween(String value1, String value2) {
            addCriterion("phone_number not between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNull() {
            addCriterion("first_name is null");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNotNull() {
            addCriterion("first_name is not null");
            return (Criteria) this;
        }

        public Criteria andFirstNameEqualTo(String value) {
            addCriterion("first_name =", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotEqualTo(String value) {
            addCriterion("first_name <>", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThan(String value) {
            addCriterion("first_name >", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThanOrEqualTo(String value) {
            addCriterion("first_name >=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThan(String value) {
            addCriterion("first_name <", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThanOrEqualTo(String value) {
            addCriterion("first_name <=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLike(String value) {
            addCriterion("first_name like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotLike(String value) {
            addCriterion("first_name not like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameIn(List<String> values) {
            addCriterion("first_name in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotIn(List<String> values) {
            addCriterion("first_name not in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameBetween(String value1, String value2) {
            addCriterion("first_name between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotBetween(String value1, String value2) {
            addCriterion("first_name not between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNull() {
            addCriterion("last_name is null");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNotNull() {
            addCriterion("last_name is not null");
            return (Criteria) this;
        }

        public Criteria andLastNameEqualTo(String value) {
            addCriterion("last_name =", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotEqualTo(String value) {
            addCriterion("last_name <>", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThan(String value) {
            addCriterion("last_name >", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("last_name >=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThan(String value) {
            addCriterion("last_name <", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThanOrEqualTo(String value) {
            addCriterion("last_name <=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLike(String value) {
            addCriterion("last_name like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotLike(String value) {
            addCriterion("last_name not like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameIn(List<String> values) {
            addCriterion("last_name in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotIn(List<String> values) {
            addCriterion("last_name not in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameBetween(String value1, String value2) {
            addCriterion("last_name between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotBetween(String value1, String value2) {
            addCriterion("last_name not between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdIsNull() {
            addCriterion("referral_source_id is null");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdIsNotNull() {
            addCriterion("referral_source_id is not null");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdEqualTo(Integer value) {
            addCriterion("referral_source_id =", value, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdNotEqualTo(Integer value) {
            addCriterion("referral_source_id <>", value, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdGreaterThan(Integer value) {
            addCriterion("referral_source_id >", value, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("referral_source_id >=", value, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdLessThan(Integer value) {
            addCriterion("referral_source_id <", value, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdLessThanOrEqualTo(Integer value) {
            addCriterion("referral_source_id <=", value, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdIn(List<Integer> values) {
            addCriterion("referral_source_id in", values, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdNotIn(List<Integer> values) {
            addCriterion("referral_source_id not in", values, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdBetween(Integer value1, Integer value2) {
            addCriterion("referral_source_id between", value1, value2, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andReferralSourceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("referral_source_id not between", value1, value2, "referralSourceId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdIsNull() {
            addCriterion("preferred_groomer_id is null");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdIsNotNull() {
            addCriterion("preferred_groomer_id is not null");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdEqualTo(Integer value) {
            addCriterion("preferred_groomer_id =", value, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdNotEqualTo(Integer value) {
            addCriterion("preferred_groomer_id <>", value, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdGreaterThan(Integer value) {
            addCriterion("preferred_groomer_id >", value, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("preferred_groomer_id >=", value, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdLessThan(Integer value) {
            addCriterion("preferred_groomer_id <", value, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("preferred_groomer_id <=", value, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdIn(List<Integer> values) {
            addCriterion("preferred_groomer_id in", values, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdNotIn(List<Integer> values) {
            addCriterion("preferred_groomer_id not in", values, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdBetween(Integer value1, Integer value2) {
            addCriterion("preferred_groomer_id between", value1, value2, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredGroomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("preferred_groomer_id not between", value1, value2, "preferredGroomerId");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayIsNull() {
            addCriterion("preferred_frequency_day is null");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayIsNotNull() {
            addCriterion("preferred_frequency_day is not null");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayEqualTo(Integer value) {
            addCriterion("preferred_frequency_day =", value, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayNotEqualTo(Integer value) {
            addCriterion("preferred_frequency_day <>", value, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayGreaterThan(Integer value) {
            addCriterion("preferred_frequency_day >", value, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayGreaterThanOrEqualTo(Integer value) {
            addCriterion("preferred_frequency_day >=", value, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayLessThan(Integer value) {
            addCriterion("preferred_frequency_day <", value, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayLessThanOrEqualTo(Integer value) {
            addCriterion("preferred_frequency_day <=", value, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayIn(List<Integer> values) {
            addCriterion("preferred_frequency_day in", values, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayNotIn(List<Integer> values) {
            addCriterion("preferred_frequency_day not in", values, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayBetween(Integer value1, Integer value2) {
            addCriterion("preferred_frequency_day between", value1, value2, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyDayNotBetween(Integer value1, Integer value2) {
            addCriterion("preferred_frequency_day not between", value1, value2, "preferredFrequencyDay");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeIsNull() {
            addCriterion("preferred_frequency_type is null");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeIsNotNull() {
            addCriterion("preferred_frequency_type is not null");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeEqualTo(Byte value) {
            addCriterion("preferred_frequency_type =", value, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeNotEqualTo(Byte value) {
            addCriterion("preferred_frequency_type <>", value, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeGreaterThan(Byte value) {
            addCriterion("preferred_frequency_type >", value, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("preferred_frequency_type >=", value, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeLessThan(Byte value) {
            addCriterion("preferred_frequency_type <", value, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeLessThanOrEqualTo(Byte value) {
            addCriterion("preferred_frequency_type <=", value, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeIn(List<Byte> values) {
            addCriterion("preferred_frequency_type in", values, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeNotIn(List<Byte> values) {
            addCriterion("preferred_frequency_type not in", values, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeBetween(Byte value1, Byte value2) {
            addCriterion("preferred_frequency_type between", value1, value2, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredFrequencyTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("preferred_frequency_type not between", value1, value2, "preferredFrequencyType");
            return (Criteria) this;
        }

        public Criteria andPreferredDayIsNull() {
            addCriterion("preferred_day is null");
            return (Criteria) this;
        }

        public Criteria andPreferredDayIsNotNull() {
            addCriterion("preferred_day is not null");
            return (Criteria) this;
        }

        public Criteria andPreferredDayEqualTo(String value) {
            addCriterion("preferred_day =", value, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayNotEqualTo(String value) {
            addCriterion("preferred_day <>", value, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayGreaterThan(String value) {
            addCriterion("preferred_day >", value, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayGreaterThanOrEqualTo(String value) {
            addCriterion("preferred_day >=", value, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayLessThan(String value) {
            addCriterion("preferred_day <", value, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayLessThanOrEqualTo(String value) {
            addCriterion("preferred_day <=", value, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayLike(String value) {
            addCriterion("preferred_day like", value, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayNotLike(String value) {
            addCriterion("preferred_day not like", value, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayIn(List<String> values) {
            addCriterion("preferred_day in", values, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayNotIn(List<String> values) {
            addCriterion("preferred_day not in", values, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayBetween(String value1, String value2) {
            addCriterion("preferred_day between", value1, value2, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredDayNotBetween(String value1, String value2) {
            addCriterion("preferred_day not between", value1, value2, "preferredDay");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeIsNull() {
            addCriterion("preferred_time is null");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeIsNotNull() {
            addCriterion("preferred_time is not null");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeEqualTo(String value) {
            addCriterion("preferred_time =", value, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeNotEqualTo(String value) {
            addCriterion("preferred_time <>", value, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeGreaterThan(String value) {
            addCriterion("preferred_time >", value, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeGreaterThanOrEqualTo(String value) {
            addCriterion("preferred_time >=", value, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeLessThan(String value) {
            addCriterion("preferred_time <", value, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeLessThanOrEqualTo(String value) {
            addCriterion("preferred_time <=", value, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeLike(String value) {
            addCriterion("preferred_time like", value, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeNotLike(String value) {
            addCriterion("preferred_time not like", value, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeIn(List<String> values) {
            addCriterion("preferred_time in", values, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeNotIn(List<String> values) {
            addCriterion("preferred_time not in", values, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeBetween(String value1, String value2) {
            addCriterion("preferred_time between", value1, value2, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andPreferredTimeNotBetween(String value1, String value2) {
            addCriterion("preferred_time not between", value1, value2, "preferredTime");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersIsNull() {
            addCriterion("customer_question_answers is null");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersIsNotNull() {
            addCriterion("customer_question_answers is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersEqualTo(String value) {
            addCriterion("customer_question_answers =", value, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersNotEqualTo(String value) {
            addCriterion("customer_question_answers <>", value, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersGreaterThan(String value) {
            addCriterion("customer_question_answers >", value, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersGreaterThanOrEqualTo(String value) {
            addCriterion("customer_question_answers >=", value, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersLessThan(String value) {
            addCriterion("customer_question_answers <", value, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersLessThanOrEqualTo(String value) {
            addCriterion("customer_question_answers <=", value, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersLike(String value) {
            addCriterion("customer_question_answers like", value, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersNotLike(String value) {
            addCriterion("customer_question_answers not like", value, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersIn(List<String> values) {
            addCriterion("customer_question_answers in", values, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersNotIn(List<String> values) {
            addCriterion("customer_question_answers not in", values, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersBetween(String value1, String value2) {
            addCriterion("customer_question_answers between", value1, value2, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCustomerQuestionAnswersNotBetween(String value1, String value2) {
            addCriterion("customer_question_answers not between", value1, value2, "customerQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andAddressIdIsNull() {
            addCriterion("address_id is null");
            return (Criteria) this;
        }

        public Criteria andAddressIdIsNotNull() {
            addCriterion("address_id is not null");
            return (Criteria) this;
        }

        public Criteria andAddressIdEqualTo(Integer value) {
            addCriterion("address_id =", value, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddressIdNotEqualTo(Integer value) {
            addCriterion("address_id <>", value, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddressIdGreaterThan(Integer value) {
            addCriterion("address_id >", value, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddressIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("address_id >=", value, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddressIdLessThan(Integer value) {
            addCriterion("address_id <", value, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddressIdLessThanOrEqualTo(Integer value) {
            addCriterion("address_id <=", value, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddressIdIn(List<Integer> values) {
            addCriterion("address_id in", values, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddressIdNotIn(List<Integer> values) {
            addCriterion("address_id not in", values, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddressIdBetween(Integer value1, Integer value2) {
            addCriterion("address_id between", value1, value2, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddressIdNotBetween(Integer value1, Integer value2) {
            addCriterion("address_id not between", value1, value2, "addressId");
            return (Criteria) this;
        }

        public Criteria andAddress1IsNull() {
            addCriterion("address1 is null");
            return (Criteria) this;
        }

        public Criteria andAddress1IsNotNull() {
            addCriterion("address1 is not null");
            return (Criteria) this;
        }

        public Criteria andAddress1EqualTo(String value) {
            addCriterion("address1 =", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1NotEqualTo(String value) {
            addCriterion("address1 <>", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1GreaterThan(String value) {
            addCriterion("address1 >", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1GreaterThanOrEqualTo(String value) {
            addCriterion("address1 >=", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1LessThan(String value) {
            addCriterion("address1 <", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1LessThanOrEqualTo(String value) {
            addCriterion("address1 <=", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1Like(String value) {
            addCriterion("address1 like", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1NotLike(String value) {
            addCriterion("address1 not like", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1In(List<String> values) {
            addCriterion("address1 in", values, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1NotIn(List<String> values) {
            addCriterion("address1 not in", values, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1Between(String value1, String value2) {
            addCriterion("address1 between", value1, value2, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1NotBetween(String value1, String value2) {
            addCriterion("address1 not between", value1, value2, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress2IsNull() {
            addCriterion("address2 is null");
            return (Criteria) this;
        }

        public Criteria andAddress2IsNotNull() {
            addCriterion("address2 is not null");
            return (Criteria) this;
        }

        public Criteria andAddress2EqualTo(String value) {
            addCriterion("address2 =", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2NotEqualTo(String value) {
            addCriterion("address2 <>", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2GreaterThan(String value) {
            addCriterion("address2 >", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2GreaterThanOrEqualTo(String value) {
            addCriterion("address2 >=", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2LessThan(String value) {
            addCriterion("address2 <", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2LessThanOrEqualTo(String value) {
            addCriterion("address2 <=", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2Like(String value) {
            addCriterion("address2 like", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2NotLike(String value) {
            addCriterion("address2 not like", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2In(List<String> values) {
            addCriterion("address2 in", values, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2NotIn(List<String> values) {
            addCriterion("address2 not in", values, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2Between(String value1, String value2) {
            addCriterion("address2 between", value1, value2, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2NotBetween(String value1, String value2) {
            addCriterion("address2 not between", value1, value2, "address2");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("state is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("state is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("state =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("state <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("state >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("state >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("state <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("state <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("state like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("state not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("state in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("state not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("state between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("state not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andZipcodeIsNull() {
            addCriterion("zipcode is null");
            return (Criteria) this;
        }

        public Criteria andZipcodeIsNotNull() {
            addCriterion("zipcode is not null");
            return (Criteria) this;
        }

        public Criteria andZipcodeEqualTo(String value) {
            addCriterion("zipcode =", value, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeNotEqualTo(String value) {
            addCriterion("zipcode <>", value, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeGreaterThan(String value) {
            addCriterion("zipcode >", value, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeGreaterThanOrEqualTo(String value) {
            addCriterion("zipcode >=", value, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeLessThan(String value) {
            addCriterion("zipcode <", value, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeLessThanOrEqualTo(String value) {
            addCriterion("zipcode <=", value, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeLike(String value) {
            addCriterion("zipcode like", value, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeNotLike(String value) {
            addCriterion("zipcode not like", value, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeIn(List<String> values) {
            addCriterion("zipcode in", values, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeNotIn(List<String> values) {
            addCriterion("zipcode not in", values, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeBetween(String value1, String value2) {
            addCriterion("zipcode between", value1, value2, "zipcode");
            return (Criteria) this;
        }

        public Criteria andZipcodeNotBetween(String value1, String value2) {
            addCriterion("zipcode not between", value1, value2, "zipcode");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andLatIsNull() {
            addCriterion("lat is null");
            return (Criteria) this;
        }

        public Criteria andLatIsNotNull() {
            addCriterion("lat is not null");
            return (Criteria) this;
        }

        public Criteria andLatEqualTo(String value) {
            addCriterion("lat =", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotEqualTo(String value) {
            addCriterion("lat <>", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatGreaterThan(String value) {
            addCriterion("lat >", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatGreaterThanOrEqualTo(String value) {
            addCriterion("lat >=", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatLessThan(String value) {
            addCriterion("lat <", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatLessThanOrEqualTo(String value) {
            addCriterion("lat <=", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatLike(String value) {
            addCriterion("lat like", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotLike(String value) {
            addCriterion("lat not like", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatIn(List<String> values) {
            addCriterion("lat in", values, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotIn(List<String> values) {
            addCriterion("lat not in", values, "lat");
            return (Criteria) this;
        }

        public Criteria andLatBetween(String value1, String value2) {
            addCriterion("lat between", value1, value2, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotBetween(String value1, String value2) {
            addCriterion("lat not between", value1, value2, "lat");
            return (Criteria) this;
        }

        public Criteria andLngIsNull() {
            addCriterion("lng is null");
            return (Criteria) this;
        }

        public Criteria andLngIsNotNull() {
            addCriterion("lng is not null");
            return (Criteria) this;
        }

        public Criteria andLngEqualTo(String value) {
            addCriterion("lng =", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotEqualTo(String value) {
            addCriterion("lng <>", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngGreaterThan(String value) {
            addCriterion("lng >", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngGreaterThanOrEqualTo(String value) {
            addCriterion("lng >=", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngLessThan(String value) {
            addCriterion("lng <", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngLessThanOrEqualTo(String value) {
            addCriterion("lng <=", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngLike(String value) {
            addCriterion("lng like", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotLike(String value) {
            addCriterion("lng not like", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngIn(List<String> values) {
            addCriterion("lng in", values, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotIn(List<String> values) {
            addCriterion("lng not in", values, "lng");
            return (Criteria) this;
        }

        public Criteria andLngBetween(String value1, String value2) {
            addCriterion("lng between", value1, value2, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotBetween(String value1, String value2) {
            addCriterion("lng not between", value1, value2, "lng");
            return (Criteria) this;
        }

        public Criteria andAbandonStepIsNull() {
            addCriterion("abandon_step is null");
            return (Criteria) this;
        }

        public Criteria andAbandonStepIsNotNull() {
            addCriterion("abandon_step is not null");
            return (Criteria) this;
        }

        public Criteria andAbandonStepEqualTo(String value) {
            addCriterion("abandon_step =", value, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepNotEqualTo(String value) {
            addCriterion("abandon_step <>", value, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepGreaterThan(String value) {
            addCriterion("abandon_step >", value, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepGreaterThanOrEqualTo(String value) {
            addCriterion("abandon_step >=", value, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepLessThan(String value) {
            addCriterion("abandon_step <", value, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepLessThanOrEqualTo(String value) {
            addCriterion("abandon_step <=", value, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepLike(String value) {
            addCriterion("abandon_step like", value, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepNotLike(String value) {
            addCriterion("abandon_step not like", value, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepIn(List<String> values) {
            addCriterion("abandon_step in", values, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepNotIn(List<String> values) {
            addCriterion("abandon_step not in", values, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepBetween(String value1, String value2) {
            addCriterion("abandon_step between", value1, value2, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonStepNotBetween(String value1, String value2) {
            addCriterion("abandon_step not between", value1, value2, "abandonStep");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeIsNull() {
            addCriterion("abandon_time is null");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeIsNotNull() {
            addCriterion("abandon_time is not null");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeEqualTo(Long value) {
            addCriterion("abandon_time =", value, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeNotEqualTo(Long value) {
            addCriterion("abandon_time <>", value, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeGreaterThan(Long value) {
            addCriterion("abandon_time >", value, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("abandon_time >=", value, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeLessThan(Long value) {
            addCriterion("abandon_time <", value, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeLessThanOrEqualTo(Long value) {
            addCriterion("abandon_time <=", value, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeIn(List<Long> values) {
            addCriterion("abandon_time in", values, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeNotIn(List<Long> values) {
            addCriterion("abandon_time not in", values, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeBetween(Long value1, Long value2) {
            addCriterion("abandon_time between", value1, value2, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonTimeNotBetween(Long value1, Long value2) {
            addCriterion("abandon_time not between", value1, value2, "abandonTime");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusIsNull() {
            addCriterion("abandon_status is null");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusIsNotNull() {
            addCriterion("abandon_status is not null");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusEqualTo(String value) {
            addCriterion("abandon_status =", value, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusNotEqualTo(String value) {
            addCriterion("abandon_status <>", value, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusGreaterThan(String value) {
            addCriterion("abandon_status >", value, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusGreaterThanOrEqualTo(String value) {
            addCriterion("abandon_status >=", value, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusLessThan(String value) {
            addCriterion("abandon_status <", value, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusLessThanOrEqualTo(String value) {
            addCriterion("abandon_status <=", value, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusLike(String value) {
            addCriterion("abandon_status like", value, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusNotLike(String value) {
            addCriterion("abandon_status not like", value, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusIn(List<String> values) {
            addCriterion("abandon_status in", values, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusNotIn(List<String> values) {
            addCriterion("abandon_status not in", values, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusBetween(String value1, String value2) {
            addCriterion("abandon_status between", value1, value2, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andAbandonStatusNotBetween(String value1, String value2) {
            addCriterion("abandon_status not between", value1, value2, "abandonStatus");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeIsNull() {
            addCriterion("last_texted_time is null");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeIsNotNull() {
            addCriterion("last_texted_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeEqualTo(Long value) {
            addCriterion("last_texted_time =", value, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeNotEqualTo(Long value) {
            addCriterion("last_texted_time <>", value, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeGreaterThan(Long value) {
            addCriterion("last_texted_time >", value, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("last_texted_time >=", value, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeLessThan(Long value) {
            addCriterion("last_texted_time <", value, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeLessThanOrEqualTo(Long value) {
            addCriterion("last_texted_time <=", value, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeIn(List<Long> values) {
            addCriterion("last_texted_time in", values, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeNotIn(List<Long> values) {
            addCriterion("last_texted_time not in", values, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeBetween(Long value1, Long value2) {
            addCriterion("last_texted_time between", value1, value2, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastTextedTimeNotBetween(Long value1, Long value2) {
            addCriterion("last_texted_time not between", value1, value2, "lastTextedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeIsNull() {
            addCriterion("last_emailed_time is null");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeIsNotNull() {
            addCriterion("last_emailed_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeEqualTo(Long value) {
            addCriterion("last_emailed_time =", value, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeNotEqualTo(Long value) {
            addCriterion("last_emailed_time <>", value, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeGreaterThan(Long value) {
            addCriterion("last_emailed_time >", value, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("last_emailed_time >=", value, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeLessThan(Long value) {
            addCriterion("last_emailed_time <", value, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeLessThanOrEqualTo(Long value) {
            addCriterion("last_emailed_time <=", value, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeIn(List<Long> values) {
            addCriterion("last_emailed_time in", values, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeNotIn(List<Long> values) {
            addCriterion("last_emailed_time not in", values, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeBetween(Long value1, Long value2) {
            addCriterion("last_emailed_time between", value1, value2, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andLastEmailedTimeNotBetween(Long value1, Long value2) {
            addCriterion("last_emailed_time not between", value1, value2, "lastEmailedTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeIsNull() {
            addCriterion("recovery_type is null");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeIsNotNull() {
            addCriterion("recovery_type is not null");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeEqualTo(Long value) {
            addCriterion("recovery_type =", value, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeNotEqualTo(Long value) {
            addCriterion("recovery_type <>", value, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeGreaterThan(Long value) {
            addCriterion("recovery_type >", value, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("recovery_type >=", value, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeLessThan(Long value) {
            addCriterion("recovery_type <", value, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeLessThanOrEqualTo(Long value) {
            addCriterion("recovery_type <=", value, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeIn(List<Long> values) {
            addCriterion("recovery_type in", values, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeNotIn(List<Long> values) {
            addCriterion("recovery_type not in", values, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeBetween(Long value1, Long value2) {
            addCriterion("recovery_type between", value1, value2, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTypeNotBetween(Long value1, Long value2) {
            addCriterion("recovery_type not between", value1, value2, "recoveryType");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeIsNull() {
            addCriterion("recovery_time is null");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeIsNotNull() {
            addCriterion("recovery_time is not null");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeEqualTo(Long value) {
            addCriterion("recovery_time =", value, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeNotEqualTo(Long value) {
            addCriterion("recovery_time <>", value, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeGreaterThan(Long value) {
            addCriterion("recovery_time >", value, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("recovery_time >=", value, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeLessThan(Long value) {
            addCriterion("recovery_time <", value, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeLessThanOrEqualTo(Long value) {
            addCriterion("recovery_time <=", value, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeIn(List<Long> values) {
            addCriterion("recovery_time in", values, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeNotIn(List<Long> values) {
            addCriterion("recovery_time not in", values, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeBetween(Long value1, Long value2) {
            addCriterion("recovery_time between", value1, value2, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andRecoveryTimeNotBetween(Long value1, Long value2) {
            addCriterion("recovery_time not between", value1, value2, "recoveryTime");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNull() {
            addCriterion("staff_id is null");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNotNull() {
            addCriterion("staff_id is not null");
            return (Criteria) this;
        }

        public Criteria andStaffIdEqualTo(Integer value) {
            addCriterion("staff_id =", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotEqualTo(Integer value) {
            addCriterion("staff_id <>", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThan(Integer value) {
            addCriterion("staff_id >", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("staff_id >=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThan(Integer value) {
            addCriterion("staff_id <", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThanOrEqualTo(Integer value) {
            addCriterion("staff_id <=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIn(List<Integer> values) {
            addCriterion("staff_id in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotIn(List<Integer> values) {
            addCriterion("staff_id not in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdBetween(Integer value1, Integer value2) {
            addCriterion("staff_id between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotBetween(Integer value1, Integer value2) {
            addCriterion("staff_id not between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdIsNull() {
            addCriterion("appointment_id is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdIsNotNull() {
            addCriterion("appointment_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdEqualTo(Integer value) {
            addCriterion("appointment_id =", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdNotEqualTo(Integer value) {
            addCriterion("appointment_id <>", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdGreaterThan(Integer value) {
            addCriterion("appointment_id >", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("appointment_id >=", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdLessThan(Integer value) {
            addCriterion("appointment_id <", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdLessThanOrEqualTo(Integer value) {
            addCriterion("appointment_id <=", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdIn(List<Integer> values) {
            addCriterion("appointment_id in", values, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdNotIn(List<Integer> values) {
            addCriterion("appointment_id not in", values, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdBetween(Integer value1, Integer value2) {
            addCriterion("appointment_id between", value1, value2, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("appointment_id not between", value1, value2, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andLeadTypeIsNull() {
            addCriterion("lead_type is null");
            return (Criteria) this;
        }

        public Criteria andLeadTypeIsNotNull() {
            addCriterion("lead_type is not null");
            return (Criteria) this;
        }

        public Criteria andLeadTypeEqualTo(String value) {
            addCriterion("lead_type =", value, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeNotEqualTo(String value) {
            addCriterion("lead_type <>", value, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeGreaterThan(String value) {
            addCriterion("lead_type >", value, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeGreaterThanOrEqualTo(String value) {
            addCriterion("lead_type >=", value, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeLessThan(String value) {
            addCriterion("lead_type <", value, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeLessThanOrEqualTo(String value) {
            addCriterion("lead_type <=", value, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeLike(String value) {
            addCriterion("lead_type like", value, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeNotLike(String value) {
            addCriterion("lead_type not like", value, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeIn(List<String> values) {
            addCriterion("lead_type in", values, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeNotIn(List<String> values) {
            addCriterion("lead_type not in", values, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeBetween(String value1, String value2) {
            addCriterion("lead_type between", value1, value2, "leadType");
            return (Criteria) this;
        }

        public Criteria andLeadTypeNotBetween(String value1, String value2) {
            addCriterion("lead_type not between", value1, value2, "leadType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateIsNull() {
            addCriterion("appointment_date is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateIsNotNull() {
            addCriterion("appointment_date is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateEqualTo(String value) {
            addCriterion("appointment_date =", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateNotEqualTo(String value) {
            addCriterion("appointment_date <>", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateGreaterThan(String value) {
            addCriterion("appointment_date >", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_date >=", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateLessThan(String value) {
            addCriterion("appointment_date <", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateLessThanOrEqualTo(String value) {
            addCriterion("appointment_date <=", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateLike(String value) {
            addCriterion("appointment_date like", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateNotLike(String value) {
            addCriterion("appointment_date not like", value, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateIn(List<String> values) {
            addCriterion("appointment_date in", values, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateNotIn(List<String> values) {
            addCriterion("appointment_date not in", values, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateBetween(String value1, String value2) {
            addCriterion("appointment_date between", value1, value2, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentDateNotBetween(String value1, String value2) {
            addCriterion("appointment_date not between", value1, value2, "appointmentDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeIsNull() {
            addCriterion("appointment_start_time is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeIsNotNull() {
            addCriterion("appointment_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeEqualTo(Integer value) {
            addCriterion("appointment_start_time =", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeNotEqualTo(Integer value) {
            addCriterion("appointment_start_time <>", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeGreaterThan(Integer value) {
            addCriterion("appointment_start_time >", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("appointment_start_time >=", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeLessThan(Integer value) {
            addCriterion("appointment_start_time <", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeLessThanOrEqualTo(Integer value) {
            addCriterion("appointment_start_time <=", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeIn(List<Integer> values) {
            addCriterion("appointment_start_time in", values, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeNotIn(List<Integer> values) {
            addCriterion("appointment_start_time not in", values, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeBetween(Integer value1, Integer value2) {
            addCriterion("appointment_start_time between", value1, value2, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("appointment_start_time not between", value1, value2, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoIsNull() {
            addCriterion("agreement_info is null");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoIsNotNull() {
            addCriterion("agreement_info is not null");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoEqualTo(String value) {
            addCriterion("agreement_info =", value, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoNotEqualTo(String value) {
            addCriterion("agreement_info <>", value, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoGreaterThan(String value) {
            addCriterion("agreement_info >", value, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoGreaterThanOrEqualTo(String value) {
            addCriterion("agreement_info >=", value, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoLessThan(String value) {
            addCriterion("agreement_info <", value, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoLessThanOrEqualTo(String value) {
            addCriterion("agreement_info <=", value, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoLike(String value) {
            addCriterion("agreement_info like", value, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoNotLike(String value) {
            addCriterion("agreement_info not like", value, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoIn(List<String> values) {
            addCriterion("agreement_info in", values, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoNotIn(List<String> values) {
            addCriterion("agreement_info not in", values, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoBetween(String value1, String value2) {
            addCriterion("agreement_info between", value1, value2, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andAgreementInfoNotBetween(String value1, String value2) {
            addCriterion("agreement_info not between", value1, value2, "agreementInfo");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeIsNull() {
            addCriterion("delete_type is null");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeIsNotNull() {
            addCriterion("delete_type is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeEqualTo(Byte value) {
            addCriterion("delete_type =", value, "deleteType");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeNotEqualTo(Byte value) {
            addCriterion("delete_type <>", value, "deleteType");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeGreaterThan(Byte value) {
            addCriterion("delete_type >", value, "deleteType");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("delete_type >=", value, "deleteType");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeLessThan(Byte value) {
            addCriterion("delete_type <", value, "deleteType");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeLessThanOrEqualTo(Byte value) {
            addCriterion("delete_type <=", value, "deleteType");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeIn(List<Byte> values) {
            addCriterion("delete_type in", values, "deleteType");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeNotIn(List<Byte> values) {
            addCriterion("delete_type not in", values, "deleteType");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeBetween(Byte value1, Byte value2) {
            addCriterion("delete_type between", value1, value2, "deleteType");
            return (Criteria) this;
        }

        public Criteria andDeleteTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("delete_type not between", value1, value2, "deleteType");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingIsNull() {
            addCriterion("use_payment_seg_setting is null");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingIsNotNull() {
            addCriterion("use_payment_seg_setting is not null");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingEqualTo(Boolean value) {
            addCriterion("use_payment_seg_setting =", value, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingNotEqualTo(Boolean value) {
            addCriterion("use_payment_seg_setting <>", value, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingGreaterThan(Boolean value) {
            addCriterion("use_payment_seg_setting >", value, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingGreaterThanOrEqualTo(Boolean value) {
            addCriterion("use_payment_seg_setting >=", value, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingLessThan(Boolean value) {
            addCriterion("use_payment_seg_setting <", value, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingLessThanOrEqualTo(Boolean value) {
            addCriterion("use_payment_seg_setting <=", value, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingIn(List<Boolean> values) {
            addCriterion("use_payment_seg_setting in", values, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingNotIn(List<Boolean> values) {
            addCriterion("use_payment_seg_setting not in", values, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingBetween(Boolean value1, Boolean value2) {
            addCriterion("use_payment_seg_setting between", value1, value2, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andUsePaymentSegSettingNotBetween(Boolean value1, Boolean value2) {
            addCriterion("use_payment_seg_setting not between", value1, value2, "usePaymentSegSetting");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleIsNull() {
            addCriterion("payment_seg_setting_rule is null");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleIsNotNull() {
            addCriterion("payment_seg_setting_rule is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleEqualTo(String value) {
            addCriterion("payment_seg_setting_rule =", value, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleNotEqualTo(String value) {
            addCriterion("payment_seg_setting_rule <>", value, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleGreaterThan(String value) {
            addCriterion("payment_seg_setting_rule >", value, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleGreaterThanOrEqualTo(String value) {
            addCriterion("payment_seg_setting_rule >=", value, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleLessThan(String value) {
            addCriterion("payment_seg_setting_rule <", value, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleLessThanOrEqualTo(String value) {
            addCriterion("payment_seg_setting_rule <=", value, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleLike(String value) {
            addCriterion("payment_seg_setting_rule like", value, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleNotLike(String value) {
            addCriterion("payment_seg_setting_rule not like", value, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleIn(List<String> values) {
            addCriterion("payment_seg_setting_rule in", values, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleNotIn(List<String> values) {
            addCriterion("payment_seg_setting_rule not in", values, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleBetween(String value1, String value2) {
            addCriterion("payment_seg_setting_rule between", value1, value2, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andPaymentSegSettingRuleNotBetween(String value1, String value2) {
            addCriterion("payment_seg_setting_rule not between", value1, value2, "paymentSegSettingRule");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageIsNull() {
            addCriterion("is_send_schedule_message is null");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageIsNotNull() {
            addCriterion("is_send_schedule_message is not null");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageEqualTo(Boolean value) {
            addCriterion("is_send_schedule_message =", value, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageNotEqualTo(Boolean value) {
            addCriterion("is_send_schedule_message <>", value, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageGreaterThan(Boolean value) {
            addCriterion("is_send_schedule_message >", value, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_send_schedule_message >=", value, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageLessThan(Boolean value) {
            addCriterion("is_send_schedule_message <", value, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageLessThanOrEqualTo(Boolean value) {
            addCriterion("is_send_schedule_message <=", value, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageIn(List<Boolean> values) {
            addCriterion("is_send_schedule_message in", values, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageNotIn(List<Boolean> values) {
            addCriterion("is_send_schedule_message not in", values, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageBetween(Boolean value1, Boolean value2) {
            addCriterion("is_send_schedule_message between", value1, value2, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andIsSendScheduleMessageNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_send_schedule_message not between", value1, value2, "isSendScheduleMessage");
            return (Criteria) this;
        }

        public Criteria andCareTypeIsNull() {
            addCriterion("care_type is null");
            return (Criteria) this;
        }

        public Criteria andCareTypeIsNotNull() {
            addCriterion("care_type is not null");
            return (Criteria) this;
        }

        public Criteria andCareTypeEqualTo(String value) {
            addCriterion("care_type =", value, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeNotEqualTo(String value) {
            addCriterion("care_type <>", value, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeGreaterThan(String value) {
            addCriterion("care_type >", value, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeGreaterThanOrEqualTo(String value) {
            addCriterion("care_type >=", value, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeLessThan(String value) {
            addCriterion("care_type <", value, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeLessThanOrEqualTo(String value) {
            addCriterion("care_type <=", value, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeLike(String value) {
            addCriterion("care_type like", value, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeNotLike(String value) {
            addCriterion("care_type not like", value, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeIn(List<String> values) {
            addCriterion("care_type in", values, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeNotIn(List<String> values) {
            addCriterion("care_type not in", values, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeBetween(String value1, String value2) {
            addCriterion("care_type between", value1, value2, "careType");
            return (Criteria) this;
        }

        public Criteria andCareTypeNotBetween(String value1, String value2) {
            addCriterion("care_type not between", value1, value2, "careType");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesIsNull() {
            addCriterion("specific_dates is null");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesIsNotNull() {
            addCriterion("specific_dates is not null");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesEqualTo(List<String> value) {
            addSpecificDatesCriterion("specific_dates =", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotEqualTo(List<String> value) {
            addSpecificDatesCriterion("specific_dates <>", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesGreaterThan(List<String> value) {
            addSpecificDatesCriterion("specific_dates >", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesGreaterThanOrEqualTo(List<String> value) {
            addSpecificDatesCriterion("specific_dates >=", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesLessThan(List<String> value) {
            addSpecificDatesCriterion("specific_dates <", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesLessThanOrEqualTo(List<String> value) {
            addSpecificDatesCriterion("specific_dates <=", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesLike(List<String> value) {
            addSpecificDatesCriterion("specific_dates like", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotLike(List<String> value) {
            addSpecificDatesCriterion("specific_dates not like", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesIn(List<List<String>> values) {
            addSpecificDatesCriterion("specific_dates in", values, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotIn(List<List<String>> values) {
            addSpecificDatesCriterion("specific_dates not in", values, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesBetween(List<String> value1, List<String> value2) {
            addSpecificDatesCriterion("specific_dates between", value1, value2, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotBetween(List<String> value1, List<String> value2) {
            addSpecificDatesCriterion("specific_dates not between", value1, value2, "specificDates");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateIsNull() {
            addCriterion("appointment_end_date is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateIsNotNull() {
            addCriterion("appointment_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateEqualTo(String value) {
            addCriterion("appointment_end_date =", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateNotEqualTo(String value) {
            addCriterion("appointment_end_date <>", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateGreaterThan(String value) {
            addCriterion("appointment_end_date >", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_end_date >=", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateLessThan(String value) {
            addCriterion("appointment_end_date <", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateLessThanOrEqualTo(String value) {
            addCriterion("appointment_end_date <=", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateLike(String value) {
            addCriterion("appointment_end_date like", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateNotLike(String value) {
            addCriterion("appointment_end_date not like", value, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateIn(List<String> values) {
            addCriterion("appointment_end_date in", values, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateNotIn(List<String> values) {
            addCriterion("appointment_end_date not in", values, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateBetween(String value1, String value2) {
            addCriterion("appointment_end_date between", value1, value2, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndDateNotBetween(String value1, String value2) {
            addCriterion("appointment_end_date not between", value1, value2, "appointmentEndDate");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeIsNull() {
            addCriterion("appointment_end_time is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeIsNotNull() {
            addCriterion("appointment_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeEqualTo(Integer value) {
            addCriterion("appointment_end_time =", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeNotEqualTo(Integer value) {
            addCriterion("appointment_end_time <>", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeGreaterThan(Integer value) {
            addCriterion("appointment_end_time >", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("appointment_end_time >=", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeLessThan(Integer value) {
            addCriterion("appointment_end_time <", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeLessThanOrEqualTo(Integer value) {
            addCriterion("appointment_end_time <=", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeIn(List<Integer> values) {
            addCriterion("appointment_end_time in", values, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeNotIn(List<Integer> values) {
            addCriterion("appointment_end_time not in", values, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeBetween(Integer value1, Integer value2) {
            addCriterion("appointment_end_time between", value1, value2, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("appointment_end_time not between", value1, value2, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentIsNull() {
            addCriterion("is_notification_sent is null");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentIsNotNull() {
            addCriterion("is_notification_sent is not null");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentEqualTo(Boolean value) {
            addCriterion("is_notification_sent =", value, "isNotificationSent");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentNotEqualTo(Boolean value) {
            addCriterion("is_notification_sent <>", value, "isNotificationSent");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentGreaterThan(Boolean value) {
            addCriterion("is_notification_sent >", value, "isNotificationSent");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_notification_sent >=", value, "isNotificationSent");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentLessThan(Boolean value) {
            addCriterion("is_notification_sent <", value, "isNotificationSent");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentLessThanOrEqualTo(Boolean value) {
            addCriterion("is_notification_sent <=", value, "isNotificationSent");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentIn(List<Boolean> values) {
            addCriterion("is_notification_sent in", values, "isNotificationSent");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentNotIn(List<Boolean> values) {
            addCriterion("is_notification_sent not in", values, "isNotificationSent");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentBetween(Boolean value1, Boolean value2) {
            addCriterion("is_notification_sent between", value1, value2, "isNotificationSent");
            return (Criteria) this;
        }

        public Criteria andIsNotificationSentNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_notification_sent not between", value1, value2, "isNotificationSent");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
