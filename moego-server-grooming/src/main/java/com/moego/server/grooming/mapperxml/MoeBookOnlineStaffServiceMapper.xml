<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineStaffServiceMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, staff_id, service_id, status, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_book_online_staff_service
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_staff_service
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_staff_service (business_id, staff_id, service_id,
      status, create_time, update_time,
      company_id)
    values (#{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER},
      #{status,jdbcType=BIT}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},
      #{companyId,jdbcType=BIGINT})
  </insert>

  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_staff_service
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_staff_service
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_staff_service
    set business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      status = #{status,jdbcType=BIT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectByBusinessId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_book_online_staff_service
        where business_id = #{businessId,jdbcType=INTEGER}
        <if test="serviceId != null">
            and service_id = #{serviceId,jdbcType=INTEGER}
        </if>
    </select>
    <select id="selectByCompanyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_book_online_staff_service
        where company_id = #{companyId,jdbcType=INTEGER}
        <if test="serviceId != null">
            and service_id = #{serviceId,jdbcType=INTEGER}
        </if>
    </select>
    <delete id="deleteByServiceId">
        delete from moe_book_online_staff_service
        where business_id = #{businessId,jdbcType=INTEGER}
        and service_id = #{serviceId,jdbcType=INTEGER}
    </delete>
    <insert id="batchInsertSelective">
        insert into moe_book_online_staff_service (business_id, staff_id, service_id,create_time, update_time, company_id
        )
        values
        <foreach collection="records" index="index" item="record" separator=",">
        (#{record.businessId,jdbcType=INTEGER}, #{record.staffId,jdbcType=INTEGER}, #{record.serviceId,jdbcType=INTEGER},
        #{record.createTime,jdbcType=BIGINT}, #{record.updateTime,jdbcType=BIGINT}, #{record.companyId,jdbcType=BIGINT}
        )
        </foreach>
    </insert>
  <select id="getServiceListByStaffIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_book_online_staff_service
    where business_id = #{businessId,jdbcType=INTEGER}
    <if test="staffIdList != null and staffIdList.size &gt; 0">
      AND `staff_id` IN
      <foreach close=")" collection="staffIdList" item="staffId" open="(" separator=",">
        #{staffId,jdbcType=INTEGER}
      </foreach>
    </if>
  </select>
  <select id="getServiceListByServiceIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_book_online_staff_service
    where business_id = #{businessId,jdbcType=INTEGER}
    <if test="serviceIdList != null and serviceIdList.size &gt; 0">
      AND `service_id` IN
      <foreach close=")" collection="serviceIdList" item="serviceId" open="(" separator=",">
        #{serviceId,jdbcType=INTEGER}
      </foreach>
    </if>
  </select>
</mapper>
