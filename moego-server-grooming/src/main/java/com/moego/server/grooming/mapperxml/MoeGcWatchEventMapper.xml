<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGcWatchEventMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGcWatchEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="gc_calendar_id" jdbcType="INTEGER" property="gcCalendarId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="channel_uuid" jdbcType="VARCHAR" property="channelUuid" />
    <result column="token_expired_time" jdbcType="BIGINT" property="tokenExpiredTime" />
    <result column="last_notify_time" jdbcType="BIGINT" property="lastNotifyTime" />
    <result column="last_sync_time" jdbcType="BIGINT" property="lastSyncTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, gc_calendar_id, resource_id, channel_uuid, token_expired_time, last_notify_time, 
    last_sync_time, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_gc_watch_event
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_gc_watch_event
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGcWatchEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_watch_event (business_id, gc_calendar_id, resource_id, 
      channel_uuid, token_expired_time, last_notify_time, 
      last_sync_time, create_time, update_time, 
      company_id)
    values (#{businessId,jdbcType=INTEGER}, #{gcCalendarId,jdbcType=INTEGER}, #{resourceId,jdbcType=VARCHAR}, 
      #{channelUuid,jdbcType=VARCHAR}, #{tokenExpiredTime,jdbcType=BIGINT}, #{lastNotifyTime,jdbcType=BIGINT}, 
      #{lastSyncTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, 
      #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcWatchEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_watch_event
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="gcCalendarId != null">
        gc_calendar_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="channelUuid != null">
        channel_uuid,
      </if>
      <if test="tokenExpiredTime != null">
        token_expired_time,
      </if>
      <if test="lastNotifyTime != null">
        last_notify_time,
      </if>
      <if test="lastSyncTime != null">
        last_sync_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="gcCalendarId != null">
        #{gcCalendarId,jdbcType=INTEGER},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="channelUuid != null">
        #{channelUuid,jdbcType=VARCHAR},
      </if>
      <if test="tokenExpiredTime != null">
        #{tokenExpiredTime,jdbcType=BIGINT},
      </if>
      <if test="lastNotifyTime != null">
        #{lastNotifyTime,jdbcType=BIGINT},
      </if>
      <if test="lastSyncTime != null">
        #{lastSyncTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcWatchEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_watch_event
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="gcCalendarId != null">
        gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="channelUuid != null">
        channel_uuid = #{channelUuid,jdbcType=VARCHAR},
      </if>
      <if test="tokenExpiredTime != null">
        token_expired_time = #{tokenExpiredTime,jdbcType=BIGINT},
      </if>
      <if test="lastNotifyTime != null">
        last_notify_time = #{lastNotifyTime,jdbcType=BIGINT},
      </if>
      <if test="lastSyncTime != null">
        last_sync_time = #{lastSyncTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGcWatchEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_watch_event
    set business_id = #{businessId,jdbcType=INTEGER},
      gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER},
      resource_id = #{resourceId,jdbcType=VARCHAR},
      channel_uuid = #{channelUuid,jdbcType=VARCHAR},
      token_expired_time = #{tokenExpiredTime,jdbcType=BIGINT},
      last_notify_time = #{lastNotifyTime,jdbcType=BIGINT},
      last_sync_time = #{lastSyncTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectByChannelUuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_watch_event
        where channel_uuid = #{channelUuid} limit 1
    </select>
    <select id="selectByGcCalendarId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_watch_event
        where gc_calendar_id = #{gcCalendarId} order by id desc limit 1
    </select>
</mapper>