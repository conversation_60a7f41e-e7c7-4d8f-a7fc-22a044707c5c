package com.moego.server.grooming.mapstruct;

import com.moego.idl.models.appointment.v1.ApplyServiceChargeModel;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.server.grooming.dto.ApplyServiceChargeDTO;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ServiceChargeMapper {

    ServiceChargeMapper INSTANCE = Mappers.getMapper(ServiceChargeMapper.class);

    @Named("toDate")
    default Date toDate(com.google.protobuf.Timestamp value) {
        return new Date(value.getSeconds() * 1000);
    }

    default ServiceChargeDTO modelToDTO(ServiceCharge model) {

        var serviceChargeDTO = new ServiceChargeDTO();

        if (model.hasCreatedAt()) {
            serviceChargeDTO.setCreatedAt(toDate(model.getCreatedAt()));
        }
        if (model.hasUpdatedAt()) {
            serviceChargeDTO.setUpdatedAt(toDate(model.getUpdatedAt()));
        }
        serviceChargeDTO.setId(model.getId());
        serviceChargeDTO.setBusinessId(model.getBusinessId());
        serviceChargeDTO.setName(model.getName());
        serviceChargeDTO.setDescription(model.getDescription());
        serviceChargeDTO.setPrice(BigDecimal.valueOf(model.getPrice()));
        serviceChargeDTO.setTaxId(model.getTaxId());
        serviceChargeDTO.setIsMandatory(model.getIsMandatory());
        serviceChargeDTO.setIsActive(model.getIsActive());
        serviceChargeDTO.setIsDeleted(model.getIsDeleted());
        serviceChargeDTO.setCreatedBy(model.getCreatedBy());
        serviceChargeDTO.setUpdatedBy(model.getUpdatedBy());
        serviceChargeDTO.setServiceItemTypes(model.getServiceItemTypesList());

        return serviceChargeDTO;
    }

    ApplyServiceChargeDTO toApplyServiceChargeDTO(ServiceChargeDTO serviceChargeDTO);

    default ApplyServiceChargeDTO toApplyServiceChargeDTO(
            ServiceChargeDTO serviceChargeDTO, ApplyServiceChargeModel applyServiceChargeModel) {
        if (Objects.isNull(serviceChargeDTO) || Objects.isNull(applyServiceChargeModel)) {
            return null;
        }
        ApplyServiceChargeDTO applyServiceChargeDTO = toApplyServiceChargeDTO(serviceChargeDTO);
        applyServiceChargeDTO.setApplyQuantity(applyServiceChargeModel.getApplyQuantity());

        return applyServiceChargeDTO;
    }
}
