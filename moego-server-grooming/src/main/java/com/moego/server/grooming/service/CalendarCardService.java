package com.moego.server.grooming.service;

import com.moego.common.enums.AppointmentEventEnum;
import com.moego.common.enums.ClientApptConst;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.agreement.v1.AgreementServiceGrpc;
import com.moego.idl.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest;
import com.moego.idl.service.agreement.v1.BatchGetAgreementUnsignedAppointmentResponse;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.api.IBusinessServiceAreaService;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.api.IPetVaccineService;
import com.moego.server.customer.client.ICustomerGroomingClient;
import com.moego.server.customer.client.IPetCodeClient;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.customer.dto.GroomingQueryDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.customer.params.GroomingCustomerInfoParams;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO;
import com.moego.server.grooming.dto.calendarcard.CalendarCardDTO;
import com.moego.server.grooming.dto.calendarcard.ClientDTO;
import com.moego.server.grooming.dto.calendarcard.DraggableInfoDTO;
import com.moego.server.grooming.dto.calendarcard.MonthlyViewDTO;
import com.moego.server.grooming.dto.calendarcard.PetInfoDTO;
import com.moego.server.grooming.dto.calendarcard.ServiceInfoDTO;
import com.moego.server.grooming.dto.calendarcard.VaccineAlertDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.BookingTypeEnum;
import com.moego.server.grooming.enums.PaymentStatusEnum;
import com.moego.server.grooming.enums.calendar.CardTypeEnum;
import com.moego.server.grooming.listener.event.UpdateCustomerEvent;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperationExample;
import com.moego.server.grooming.mapstruct.AutoAssignConverter;
import com.moego.server.grooming.mapstruct.CalendarCardConverter;
import com.moego.server.grooming.service.storage.AppointmentStorageService;
import com.moego.server.grooming.web.params.calendar.CardRescheduleParams;
import com.moego.server.grooming.web.params.calendar.QueryCardListParams;
import com.moego.server.payment.client.IPaymentPreAuthClient;
import com.moego.server.payment.dto.PreAuthDTO;
import com.moego.server.payment.params.BatchQueryPreAuthParams;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CalendarCardService {

    @Autowired
    private AppointmentMapperProxy appointmentMapper;

    @Autowired
    private PetDetailMapperProxy petDetailMapper;

    @Autowired
    private MoeGroomingServiceOperationMapper serviceOperationMapper;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private IPetCodeClient iPetCodeClient;

    @Autowired
    private IPetVaccineService iPetVaccineService;

    @Autowired
    private ICustomerGroomingClient iCustomerGroomingClient;

    @Autowired
    private IBusinessServiceAreaService iBusinessServiceAreaService;

    @Autowired
    private AppointmentStorageService appointmentStorageService;

    @Autowired
    private MoeGroomingNoteService moeGroomingNoteService;

    @Autowired
    private MoeBookOnlineDepositService moeBookOnlineDepositService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private ICustomerCustomerService iCustomerCustomerService;

    @Autowired
    private IPaymentPreAuthClient preAuthClient;

    @Autowired
    private AgreementServiceGrpc.AgreementServiceBlockingStub agreementServiceBlockingStub;

    @Autowired
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private AutoAssignService autoAssignService;

    @Autowired
    private GroomingApptAsyncService asyncService;

    @Autowired
    private ActiveMQService activeMQService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private PermissionHelper permissionHelper;

    public Boolean reschedule(CardRescheduleParams params) {
        // 查询 appointment
        MoeGroomingAppointment appointment = appointmentMapper.selectByPrimaryKey(params.appointmentId());
        if (appointment == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not found");
        }

        boolean result = reschedule(appointment, params, true);
        // repeat 预约更新 apply to upcoming/all
        if (params.repeatType() != null && appointment.getRepeatId() != null) {
            ThreadPool.execute(() -> {
                String appointmentDate = appointment.getAppointmentDate();
                Long daysDiff = DateUtil.getDaysDiffByTwoDate(appointmentDate, params.startDate());
                // 异步修改 upcoming/all
                List<MoeGroomingAppointment> updateAppointmentList =
                        switch (params.repeatType()) {
                            case THIS_AND_FOLLOWING -> appointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), appointment.getAppointmentDate());
                            case ALL -> appointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), null);
                            default -> List.of();
                        };
                updateAppointmentList.stream()
                        .filter(repeatAppointment -> !Objects.equals(repeatAppointment.getId(), appointment.getId()))
                        .forEach(repeatAppointment -> {
                            // 更新请求参数
                            String updateDate =
                                    DateUtil.getStrDateByDaysDiff(repeatAppointment.getAppointmentDate(), daysDiff);
                            CardRescheduleParams updateParams = params.toBuilder()
                                    .appointmentId(repeatAppointment.getId())
                                    .startDate(updateDate)
                                    .build();
                            reschedule(repeatAppointment, updateParams, false);
                        });
            });
        }
        return result;
    }

    private boolean reschedule(MoeGroomingAppointment appointment, CardRescheduleParams params, boolean needNotify) {
        // 查询 service
        List<GroomingPetDetailDTO> groomingServices =
                petDetailMapper.queryPetDetailByGroomingId(params.appointmentId());

        // 查询 operation
        List<MoeGroomingServiceOperation> serviceOperations =
                serviceOperationMapper.selectByBusinessIdAndGroomingId(params.businessId(), params.appointmentId());

        if (Objects.isNull(params.endTime())) {
            if (!CardTypeEnum.APPOINTMENT.equals(params.cardType()) && !CardTypeEnum.BLOCK.equals(params.cardType())) {
                // 仅当 Appointment 和 Block 类型的 card reschedule 时，可以留空 endTime
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "endTime is null");
            }
            params = params.toBuilder()
                    .endTime(appointment.getAppointmentEndTime()
                            - appointment.getAppointmentStartTime()
                            + params.startTime())
                    .build();
        }

        Boolean result = updateByCardType(params, appointment, groomingServices, serviceOperations);
        if (result) {
            orderService.updateOrderByGroomingId(
                    appointment.getCompanyId(),
                    appointment.getBusinessId(),
                    appointment.getId(),
                    params.tokenStaffId());

            MoeGroomingAppointment newGroomingAppointment =
                    appointmentMapper.selectByPrimaryKey(params.appointmentId());
            asyncService.syncApptInfoToCalender(newGroomingAppointment); // appt reschedule
            if (needNotify) {
                asyncService.apptUpdateNotify(newGroomingAppointment, appointment, params.tokenStaffId());
            }
            activeMQService.publishAppointmentEventV2(
                    null, newGroomingAppointment, null, AppointmentEventEnum.MODIFY_SINGLE);
            publisher.publishEvent(new UpdateCustomerEvent(this)
                    .setBusinessId(params.businessId())
                    .setCustomerId(appointment.getCustomerId()));
        }
        return result;
    }

    private Boolean updateByCardType(
            CardRescheduleParams params,
            MoeGroomingAppointment existingAppointment,
            List<GroomingPetDetailDTO> existGroomingServices,
            List<MoeGroomingServiceOperation> existServiceOperations) {
        return switch (params.cardType()) {
            case APPOINTMENT -> rescheduleAppointment(
                    params, existingAppointment, existGroomingServices, existServiceOperations);
            case SERVICE -> rescheduleService(params, existingAppointment, existGroomingServices);
            case OPERATION -> rescheduleOperation(
                    params, existingAppointment, existGroomingServices, existServiceOperations);
            case BLOCK -> rescheduleBlock(params, existingAppointment);
        };
    }

    public List<CalendarCardDTO> queryCalendarCardByDate(QueryCardListParams params) {
        List<MoeGroomingAppointment> appointments = queryAppointments(params);
        if (CollectionUtils.isEmpty(appointments)) {
            return new ArrayList<>();
        }

        List<GroomingPetDetailDTO> petDetailServices = petDetailMapper.queryHasStaffPetDetailByGroomingIds(
                appointments.stream().map(MoeGroomingAppointment::getId).toList(),
                params.startDate(),
                params.endDate());
        if (CollectionUtils.isEmpty(petDetailServices)) {
            return List.of();
        }

        List<MoeGroomingServiceOperation> operations = queryOperations(
                params,
                petDetailServices.stream()
                        .filter(GroomingPetDetailDTO::getEnableOperation)
                        .map(GroomingPetDetailDTO::getId)
                        .toList());

        List<CalendarCardDTO> cardList = buildCalendarCards(appointments, petDetailServices, operations);

        List<Integer> calenderStaffList = iBusinessStaffClient.showCalenderStaff(params.businessId(), params.staffId());
        Set<Integer> availableStaffIds = new HashSet<>(calenderStaffList);

        // 过滤在 start date 和 end date 范围内的 card
        // show calender staff 过滤
        cardList = cardList.stream()
                .filter(card -> card.getDate().compareTo(params.startDate()) >= 0
                        && card.getDate().compareTo(params.endDate()) <= 0)
                .filter(card -> availableStaffIds.contains(card.getStaffId().intValue()))
                .collect(Collectors.toList());
        if (params.filterNoStaff()) {
            cardList.removeIf(card -> Objects.isNull(card.getStaffId()));
        }
        cardList.sort(Comparator.comparing(CalendarCardDTO::getDate).thenComparing(CalendarCardDTO::getStartTime));

        setCustomerInfo(
                cardList.stream()
                        .filter(card -> !card.getCardType().equals(CardTypeEnum.BLOCK))
                        .collect(Collectors.toList()),
                params.staffId(),
                params.businessId());
        setPetInfo(cardList.stream()
                .filter(card -> !card.getCardType().equals(CardTypeEnum.BLOCK))
                .collect(Collectors.toList()));
        setTicketComment(cardList);
        setAlertNote(cardList);
        setPrepayStatus(params.businessId(), cardList);
        setAgreementInfo(
                params.businessId().longValue(),
                cardList.stream()
                        .filter(card -> !card.getCardType().equals(CardTypeEnum.BLOCK))
                        .collect(Collectors.toList()));
        setPreAuthInfo(cardList);
        return cardList;
    }

    public List<MonthlyViewDTO> queryAppointmentForMonthlyView(QueryCardListParams params) {
        List<MoeGroomingAppointment> appointments = queryAppointments(params);
        if (CollectionUtils.isEmpty(appointments)) {
            return new ArrayList<>();
        }

        List<GroomingPetDetailDTO> petDetailServices = petDetailMapper.queryHasStaffPetDetailByGroomingIds(
                appointments.stream().map(MoeGroomingAppointment::getId).toList(),
                params.startDate(),
                params.endDate());

        List<MoeGroomingServiceOperation> operations = queryOperations(
                params,
                petDetailServices.stream()
                        .filter(GroomingPetDetailDTO::getEnableOperation)
                        .map(GroomingPetDetailDTO::getId)
                        .toList());

        List<CalendarCardDTO> cardList = buildCalendarCards(appointments, petDetailServices, operations);

        List<Integer> calenderStaffList = iBusinessStaffClient.showCalenderStaff(params.businessId(), params.staffId());
        Set<Integer> availableStaffIds = new HashSet<>(calenderStaffList);

        // 过滤在 start date 和 end date 范围内的 card
        // show calender staff 过滤
        cardList = cardList.stream()
                .filter(card -> card.getDate().compareTo(params.startDate()) >= 0
                        && card.getDate().compareTo(params.endDate()) <= 0)
                .filter(card -> availableStaffIds.contains(card.getStaffId().intValue()))
                .toList();

        List<MonthlyViewDTO> monthlyViewDTOS = cardList.stream()
                .map(card -> {
                    MonthlyViewDTO monthlyViewDTO = new MonthlyViewDTO();
                    monthlyViewDTO.setCardId(card.getCardId());
                    monthlyViewDTO.setAppointmentId(card.getAppointmentId());
                    monthlyViewDTO.setAppointmentDate(card.getDate());
                    monthlyViewDTO.setStartTime(card.getStartTime());
                    monthlyViewDTO.setColorCode(card.getColorCode());
                    monthlyViewDTO.setStaffId(card.getStaffId().intValue());
                    monthlyViewDTO.setCustomerId(
                            card.getClientInfo().getClientId().intValue());
                    monthlyViewDTO.setAppointmentStatus(card.getAppointmentStatus());
                    monthlyViewDTO.setBookingType(card.getBookingType());
                    monthlyViewDTO.setIsBlock(Objects.equals(card.getCardType(), CardTypeEnum.BLOCK));
                    monthlyViewDTO.setRepeatId(card.getRepeatId().intValue());
                    monthlyViewDTO.setEndTime(card.getEndTime());
                    if (!CollectionUtils.isEmpty(card.getPetList())
                            && !CollectionUtils.isEmpty(card.getPetList().get(0).getServiceList())) {
                        monthlyViewDTO.setServiceColorCode(
                                card.getPetList().get(0).getServiceList().get(0).getColorCode());
                        monthlyViewDTO.setPetDetailId(
                                card.getPetList().get(0).getServiceList().get(0).getPetDetailId());
                    }
                    monthlyViewDTO.setId(card.getId());
                    monthlyViewDTO.setCardType(card.getCardType());
                    monthlyViewDTO.setPetDetailsIds(card.getPetDetailIds());
                    monthlyViewDTO.setIsAutoAccept(card.getIsAutoAccept());
                    monthlyViewDTO.setAutoAssign(card.getAutoAssign());
                    return monthlyViewDTO;
                })
                .collect(Collectors.toList());
        setCustomerName(monthlyViewDTOS.stream()
                .filter(monthlyViewDTO -> !monthlyViewDTO.getIsBlock())
                .toList());
        setBlockDesc(monthlyViewDTOS.stream().filter(MonthlyViewDTO::getIsBlock).toList());
        if (params.filterNoStaff()) {
            monthlyViewDTOS.removeIf(dto -> Objects.isNull(dto.getStaffId()));
        }
        monthlyViewDTOS.sort(
                Comparator.comparing(MonthlyViewDTO::getAppointmentDate).thenComparing(MonthlyViewDTO::getStartTime));
        return monthlyViewDTOS;
    }

    private void setCustomerName(List<MonthlyViewDTO> monthlyViewDTOS) {
        if (CollectionUtils.isEmpty(monthlyViewDTOS)) {
            return;
        }
        List<Integer> customerIds = monthlyViewDTOS.stream()
                .map(MonthlyViewDTO::getCustomerId)
                .distinct()
                .toList();
        List<MoeBusinessCustomerDTO> customerList =
                iCustomerCustomerService.queryCustomerList(new CustomerIdListParams(customerIds));
        if (CollectionUtils.isEmpty(customerList)) {
            return;
        }
        Map<Integer, MoeBusinessCustomerDTO> customerInfoMap = customerList.stream()
                .collect(Collectors.toMap(MoeBusinessCustomerDTO::getCustomerId, Function.identity()));
        monthlyViewDTOS.forEach(monthlyViewDTO -> {
            if (!customerInfoMap.containsKey(monthlyViewDTO.getCustomerId())) {
                return;
            }
            monthlyViewDTO.setCustomerFirstName(
                    customerInfoMap.get(monthlyViewDTO.getCustomerId()).getFirstName());
            monthlyViewDTO.setCustomerLastName(
                    customerInfoMap.get(monthlyViewDTO.getCustomerId()).getLastName());
            monthlyViewDTO.setCustomerColor(
                    customerInfoMap.get(monthlyViewDTO.getCustomerId()).getClientColor());
        });
    }

    private void setBlockDesc(List<MonthlyViewDTO> monthlyViewDTOS) {
        if (CollectionUtils.isEmpty(monthlyViewDTOS)) {
            return;
        }
        List<Integer> appointmentIds = monthlyViewDTOS.stream()
                .map(MonthlyViewDTO::getAppointmentId)
                .map(Long::intValue)
                .distinct()
                .toList();
        List<MoeGroomingNote> moeGroomingNotes = moeGroomingNoteService.getNoteListByGroomingIdListAndType(
                appointmentIds, GroomingAppointmentEnum.NOTE_COMMENT.intValue());
        if (CollectionUtils.isEmpty(moeGroomingNotes)) {
            return;
        }
        Map<Integer, MoeGroomingNote> moeGroomingNoteMap = moeGroomingNotes.stream()
                .collect(Collectors.toMap(MoeGroomingNote::getGroomingId, Function.identity()));
        monthlyViewDTOS.forEach(monthlyViewDTO -> {
            if (!moeGroomingNoteMap.containsKey(
                    monthlyViewDTO.getAppointmentId().intValue())) {
                return;
            }
            monthlyViewDTO.setDesc(moeGroomingNoteMap
                    .get(monthlyViewDTO.getAppointmentId().intValue())
                    .getNote());
        });
    }

    private void setPreAuthInfo(List<CalendarCardDTO> cardList) {
        List<Integer> appointmentIdList = cardList.stream()
                .map(CalendarCardDTO::getAppointmentId)
                .map(Long::intValue)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return;
        }
        // 获取preauth状态
        List<PreAuthDTO> preAuthDTOS = preAuthClient.batchQueryByTicketIds(BatchQueryPreAuthParams.builder()
                .businessId(AuthContext.get().getBusinessId())
                .ticketIds(appointmentIdList)
                .build());
        if (!CollectionUtils.isEmpty(preAuthDTOS)) {
            Map<Integer, PreAuthDTO> preauthMap = preAuthDTOS.stream()
                    .collect(Collectors.toMap(PreAuthDTO::getTicketId, note -> note, (note1, note2) -> note2));
            for (CalendarCardDTO cardDTO : cardList) {
                int ticketId = Math.toIntExact(cardDTO.getAppointmentId());
                if (preauthMap.containsKey(ticketId)) {
                    cardDTO.setPreAuthInfo(preauthMap.get(ticketId));
                }
            }
        }
    }

    // 将 Appointment、Service、Operation 三个表的数据合并到 CalendarCardDTO 中
    private List<CalendarCardDTO> buildCalendarCards(
            List<MoeGroomingAppointment> appointments,
            List<GroomingPetDetailDTO> petDetailServices,
            List<MoeGroomingServiceOperation> operations) {
        Map<Integer, List<GroomingPetDetailDTO>> petDetailByGroomingIdMap =
                petDetailServices.stream().collect(Collectors.groupingBy(GroomingPetDetailDTO::getGroomingId));
        Map<Integer, List<MoeGroomingServiceOperation>> operationByServiceIdMap =
                operations.stream().collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));
        Map</*appointmentId*/ Integer, AutoAssign> apptIdToAutoAssign =
                autoAssignService
                        .listAutoAssign(appointments.stream()
                                .map(MoeGroomingAppointment::getId)
                                .toList())
                        .stream()
                        .collect(Collectors.toMap(AutoAssign::getAppointmentId, Function.identity()));
        List<CalendarCardDTO> result = new ArrayList<>();
        appointments.forEach(a -> {
            List<CalendarCardDTO> cardList = new ArrayList<>();
            // 过滤有 staff id 和 start date 的 grooming service
            List<GroomingPetDetailDTO> petDetailList =
                    Optional.ofNullable(petDetailByGroomingIdMap.get(a.getId())).orElse(List.of()).stream()
                            .filter(p -> Objects.nonNull(p.getStaffId()) && Objects.nonNull(p.getStartDate()))
                            .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(petDetailList)) {
                log.error("petDetailList is empty, appointmentId: {}", a.getId());
                return;
            }

            Map<Integer, Long> earliestStartTimesByPetId = petDetailList.stream()
                    .collect(Collectors.groupingBy(
                            GroomingPetDetailDTO::getPetId,
                            Collectors.mapping(GroomingPetDetailDTO::getStartTime, Collectors.minBy(Long::compareTo))))
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey, e -> e.getValue().orElse(0L)));
            petDetailList.sort(
                    Comparator.comparing((GroomingPetDetailDTO p) -> earliestStartTimesByPetId.get(p.getPetId()))
                            .thenComparing(GroomingPetDetailDTO::getPetId)
                            .thenComparing(GroomingPetDetailDTO::getStaffId)
                            .thenComparing(GroomingPetDetailDTO::getStartTime)
                            .thenComparing(GroomingPetDetailDTO::getEndTime));
            AtomicReference<CalendarCardDTO> lastCard = new AtomicReference<>();
            petDetailList.forEach(p -> {
                /*
                针对 block 类型，只会存在一条 petDetail 记录，直接返回即可
                 */
                if (Objects.equals(a.getIsBlock().byteValue(), ClientApptConst.IS_BLOCK_TRUE)) {
                    CalendarCardDTO card = new CalendarCardDTO(
                            a.getId().longValue(),
                            CardTypeEnum.BLOCK,
                            p.getStartDate(),
                            p.getEndDate(),
                            a.getAppointmentStartTime().longValue(),
                            a.getAppointmentEndTime().longValue(),
                            p.getStaffId().longValue(),
                            a.getColorCode(),
                            BookingTypeEnum.fromValue(a.getBookOnlineStatus()));
                    cardList.add(card);
                    return;
                }
                /*
                以下情况需要合并卡片：
                1. 前一张卡片类型是 service
                2. 当前 service 没有 operation
                3. 当前 service 的 startTime 与前一张卡片的 endTime 相同
                4. 当前 service 的 staffId 与前一张卡片的 staffId 相同
                 */
                if (!Objects.isNull(lastCard.get())
                        && lastCard.get().getCardType().equals(CardTypeEnum.SERVICE)
                        && lastCard.get().getEndTime().equals(p.getStartTime())
                        && !operationByServiceIdMap.containsKey(p.getId())
                        && lastCard.get().getStaffId().equals(p.getStaffId().longValue())) {
                    // 合并卡片，将 endDate & endTime 延长，并且将当前 service 添加到 lastCard 中
                    lastCard.get().setEndDate(p.getEndDate());
                    lastCard.get().setEndTime(p.getEndTime());
                    lastCard.get().getPetList().stream()
                            .filter(pet -> pet.getPetId().equals(p.getPetId()))
                            .findFirst()
                            .ifPresentOrElse(
                                    pet -> {
                                        // pet 已存在，只需要添加 service
                                        pet.addService(new ServiceInfoDTO(
                                                p.getId(),
                                                p.getServiceId().longValue(),
                                                p.getServiceName(),
                                                p.getColorCode(),
                                                p.getServiceTime(),
                                                p.getServicePrice()));
                                    },
                                    () -> {
                                        // pet 不存在，需要新建 pet
                                        PetInfoDTO petInfoDTO = new PetInfoDTO(p.getPetId(), p.getPetName());
                                        petInfoDTO.addService(new ServiceInfoDTO(
                                                p.getId(),
                                                p.getServiceId().longValue(),
                                                p.getServiceName(),
                                                p.getColorCode(),
                                                p.getServiceTime(),
                                                p.getServicePrice()));
                                        lastCard.get().addPet(petInfoDTO);
                                    });

                } else {
                    // 新建下一张卡片
                    if (operationByServiceIdMap.containsKey(p.getId())) {
                        var staffIds = operationByServiceIdMap.get(p.getId()).stream()
                                .map(MoeGroomingServiceOperation::getStaffId)
                                .collect(Collectors.toSet());
                        // 有 operation
                        operationByServiceIdMap.get(p.getId()).forEach(o -> {
                            CalendarCardDTO card = new CalendarCardDTO(
                                    o.getId(),
                                    CardTypeEnum.OPERATION,
                                    p.getStartDate(),
                                    p.getEndDate(),
                                    o.getStartTime().longValue(),
                                    o.getStartTime().longValue()
                                            + o.getDuration().longValue(),
                                    o.getStaffId().longValue(),
                                    a.getColorCode(),
                                    BookingTypeEnum.fromValue(a.getBookOnlineStatus()));
                            card.setDraggableInfo(new DraggableInfoDTO(
                                    true,
                                    a.getAppointmentDate(),
                                    a.getAppointmentEndDate(),
                                    staffIds.stream()
                                            .filter(staffId -> !staffId.equals(o.getStaffId()))
                                            .map(Integer::longValue)
                                            .toList()));
                            ServiceInfoDTO serviceInfoDTO = new ServiceInfoDTO(
                                    p.getId(),
                                    p.getServiceId().longValue(),
                                    p.getServiceName(),
                                    p.getColorCode(),
                                    p.getServiceTime(),
                                    p.getServicePrice());
                            PetInfoDTO petInfoDTO = new PetInfoDTO(p.getPetId(), p.getPetName());
                            petInfoDTO.addService(serviceInfoDTO);
                            card.addPet(petInfoDTO);
                            cardList.add(card);
                            lastCard.set(card);
                        });
                    } else {
                        CalendarCardDTO card = new CalendarCardDTO(
                                p.getId().longValue(),
                                CardTypeEnum.SERVICE,
                                p.getStartDate(),
                                p.getEndDate(),
                                p.getStartTime(),
                                p.getEndTime(),
                                p.getStaffId().longValue(),
                                a.getColorCode(),
                                BookingTypeEnum.fromValue(a.getBookOnlineStatus()));
                        card.setDraggableInfo(new DraggableInfoDTO(
                                true, a.getAppointmentDate(), a.getAppointmentEndDate(), Collections.emptyList()));
                        ServiceInfoDTO serviceInfoDTO = new ServiceInfoDTO(
                                p.getId(),
                                p.getServiceId().longValue(),
                                p.getServiceName(),
                                p.getColorCode(),
                                p.getServiceTime(),
                                p.getServicePrice());
                        PetInfoDTO petInfoDTO = new PetInfoDTO(p.getPetId(), p.getPetName());
                        petInfoDTO.addService(serviceInfoDTO);
                        card.addPet(petInfoDTO);
                        cardList.add(card);
                        lastCard.set(card);
                    }
                }
            });
            cardList.forEach(card -> {
                card.setAppointmentId(a.getId().longValue());
                card.setAppointmentStatus(moeGroomingAppointmentService.getCompatibleAppointmentStatus(
                        a.getStatus(), a.getCheckInTime()));
                card.setPaymentStatus(PaymentStatusEnum.fromValue(a.getIsPaid()));
                card.setRepeatId(a.getRepeatId().longValue());
                card.setClientInfo(ClientDTO.builder()
                        .clientId(a.getCustomerId().longValue())
                        .build());
                card.setPetDetailIds(card.getPetList().stream()
                        .flatMap(pet -> pet.getServiceList().stream())
                        .map(ServiceInfoDTO::getPetDetailId)
                        .distinct()
                        .toList());
                card.setEstimatedTotalPrice(card.getPetList().stream()
                        .flatMap(pet -> pet.getServiceList().stream())
                        .map(ServiceInfoDTO::getServicePrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                card.setIsAutoAccept(a.getIsAutoAccept());
                card.setAutoAssign(AutoAssignConverter.INSTANCE.entityToDTO(apptIdToAutoAssign.get(a.getId())));
            });

            // 如果只有一个卡片，并且不是 block，那类型就是 appointment
            /* 类型是 appointment 的条件：
            1. 只有一张卡片
            2. 不是 block
            3. 只包含 Grooming 类型的服务
             */
            if (cardList.size() == 1
                    && !CardTypeEnum.BLOCK.equals(cardList.get(0).getCardType())
                    && isGroomingOnlyService(a.getServiceTypeInclude())) {
                cardList.get(0).setCardType(CardTypeEnum.APPOINTMENT);
                cardList.get(0).setId(a.getId().longValue());
                cardList.get(0)
                        .setDraggableInfo(new DraggableInfoDTO(
                                true, "", "", Collections.emptyList())); // 如果是完整的 appointment 卡片，可以随意拖拽修改日期、时间、staff
            }
            result.addAll(cardList);
        });
        // 如果存在跨天的卡片，需要按天将其拆分
        return afterCalendarCardOperation(result);
    }

    /**
     * 1.日历卡片按天拆分
     * 2.添加卡片唯一键
     *
     * @param cards 日历卡片
     * @return 拆分后的日历卡片
     */
    private List<CalendarCardDTO> afterCalendarCardOperation(List<CalendarCardDTO> cards) {
        return cards.stream()
                .map(card -> {
                    if (card.getDate().equals(card.getEndDate())) {
                        return List.of(card);
                    }
                    List<CalendarCardDTO> splitCards = new ArrayList<>();
                    for (String currentDate = card.getDate();
                            currentDate.compareTo(card.getEndDate()) <= 0;
                            currentDate = DateUtil.getStrDateByDaysDiff(currentDate, -1L)) {
                        CalendarCardDTO splitCard = CalendarCardConverter.INSTANCE.copy(card);
                        splitCard.setDate(currentDate);
                        splitCard.setEndDate(currentDate);
                        if (currentDate.compareTo(card.getEndDate()) == 0) {
                            splitCard.setEndTime(card.getEndTime());
                        } else {
                            splitCard.setEndTime(24 * 60 - 1L);
                        }
                        if (currentDate.compareTo(card.getDate()) > 0) {
                            splitCard.setStartTime(0L);
                        }
                        splitCards.add(splitCard);
                    }
                    return splitCards;
                })
                .flatMap(List::stream)
                .peek(card -> card.setCardId(buildCardId(card)))
                .toList();
    }

    private String buildCardId(CalendarCardDTO card) {
        return String.format("%s%s%s", card.getCardType(), card.getDate(), card.getId())
                .replace("-", "");
    }

    // 根据 appointment 中的 serviceTypeInclude 字段，判断 appointment 是否是 grooming only
    private boolean isGroomingOnlyService(Integer serviceTypeInclude) {
        return !ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)
                && !ServiceItemEnum.DAYCARE.isIncludedIn(serviceTypeInclude);
    }

    private List<MoeGroomingAppointment> queryAppointments(QueryCardListParams params) {
        MoeGroomingAppointmentExample apptExample = new MoeGroomingAppointmentExample();
        // 这里的 60 天，是预约的最长时间限制，用来优化查询效率，避免索引失效
        MoeGroomingAppointmentExample.Criteria apptcriteria = apptExample
                .createCriteria()
                .andBusinessIdEqualTo(params.businessId())
                .andAppointmentDateBetween(DateUtil.getStrDateByDaysDiff(params.endDate(), 60L), params.endDate())
                .andAppointmentEndDateBetween(
                        params.startDate(), DateUtil.getStrDateByDaysDiff(params.startDate(), -60L))
                .andIsDeprecateEqualTo(0)
                .andStatusIn(Stream.of(AppointmentStatusSet.ACTIVE_STATUS_SET.stream()
                                .map(AppointmentStatusEnum::getValue)
                                .toArray(Byte[]::new))
                        .toList());
        if (Objects.nonNull(params.isWaitingList())) {
            apptcriteria.andIsWaitingListEqualTo(params.isWaitingList() ? (byte) 1 : (byte) 0);
        }
        if (params.filterNoStartTime()) {
            apptcriteria.andNoStartTimeEqualTo(false);
        }
        // DONE: date 区间与筛选区间有交集就返回
        return appointmentMapper.selectByExample(apptExample);
    }

    private List<MoeGroomingServiceOperation> queryOperations(
            QueryCardListParams params, List<Integer> petDetailServiceIds) {
        if (Objects.isNull(petDetailServiceIds) || petDetailServiceIds.isEmpty()) {
            return Collections.emptyList();
        }
        MoeGroomingServiceOperationExample operationExample = new MoeGroomingServiceOperationExample();
        operationExample
                .createCriteria()
                .andBusinessIdEqualTo(params.businessId())
                .andGroomingServiceIdIn(petDetailServiceIds);
        return serviceOperationMapper.selectByExample(operationExample);
    }

    private void setCustomerInfo(List<CalendarCardDTO> cardList, Integer staffId, Integer businessId) {
        List<GroomingQueryDto> ticketInfo = new ArrayList<>(cardList.stream()
                .map(card -> {
                    GroomingQueryDto groomingQueryDto = new GroomingQueryDto();
                    groomingQueryDto.setCustomerId(
                            card.getClientInfo().getClientId().intValue());
                    groomingQueryDto.setGroomingId(card.getAppointmentId().intValue());
                    return groomingQueryDto;
                })
                .collect(Collectors.toMap(GroomingQueryDto::getCustomerId, e -> e, (e1, e2) -> e1))
                .values());
        if (ticketInfo.isEmpty()) {
            return;
        }
        GroomingCustomerInfoParams groomingCustomerInfoParams = new GroomingCustomerInfoParams();
        groomingCustomerInfoParams.setTokenStaffId(staffId);
        groomingCustomerInfoParams.setTicketInfo(ticketInfo);

        List<GroomingCalenderCustomerInfo> groomingCalenderCustomerInfoList =
                iCustomerGroomingClient.getGroomingCalenderCustomerInfo(groomingCustomerInfoParams);
        Map<Integer, GroomingCalenderCustomerInfo> customerInfoMap = groomingCalenderCustomerInfoList.stream()
                .collect(Collectors.toMap(GroomingCalenderCustomerInfo::getCustomerId, Function.identity()));
        Map<Long, List<CertainAreaDTO>> clientCertainAreaInfo =
                getClientCertainAreaInfo(businessId.longValue(), groomingCalenderCustomerInfoList);

        List<Integer> customerIdList = cardList.stream()
                .map(card -> card.getClientInfo().getClientId().intValue())
                .distinct()
                .toList();
        Set<Integer> notNewCustomerIdList = appointmentMapper.findNotNewCustomerIdList(businessId, customerIdList);

        cardList.forEach(card -> {
            GroomingCalenderCustomerInfo customerInfo =
                    customerInfoMap.get(card.getClientInfo().getClientId().intValue());
            card.setClientInfo(card.getClientInfo().toBuilder()
                    .customerFirstName(customerInfo.getCustomerFirstName())
                    .customerLastName(customerInfo.getCustomerLastName())
                    .clientColor(customerInfo.getClientColor())
                    .areas(clientCertainAreaInfo.getOrDefault(
                            customerInfo.getCustomerId().longValue(), Collections.emptyList()))
                    .fullAddress(CommonUtil.getFullAddress(
                            customerInfo.getAddress1(),
                            customerInfo.getAddress2(),
                            customerInfo.getCity(),
                            customerInfo.getState(),
                            customerInfo.getCountry(),
                            customerInfo.getZipcode()))
                    .address1(customerInfo.getAddress1())
                    .address2(customerInfo.getAddress2())
                    .country(customerInfo.getCountry())
                    .state(customerInfo.getState())
                    .city(customerInfo.getCity())
                    .zipcode(customerInfo.getZipcode())
                    .lat(customerInfo.getLat())
                    .lng(customerInfo.getLng())
                    .isNewClient(!notNewCustomerIdList.contains(customerInfo.getCustomerId()))
                    .build());
        });
    }

    private Map<Long, List<CertainAreaDTO>> getClientCertainAreaInfo(
            Long businessId, List<GroomingCalenderCustomerInfo> customerInfoList) {
        if (customerInfoList.isEmpty()) {
            return Collections.emptyMap();
        }
        List<GetAreasByLocationParams> locationParams = customerInfoList.stream()
                .filter(customerInfo ->
                        !Strings.isEmpty(customerInfo.getLat()) && !Strings.isEmpty(customerInfo.getLng()))
                .map(customerInfo -> new GetAreasByLocationParams(
                        customerInfo.getCustomerId().longValue(),
                        customerInfo.getLat(),
                        customerInfo.getLng(),
                        customerInfo.getZipcode()))
                .toList();
        return iBusinessServiceAreaService.getAreasByLocation(
                new BatchGetAreasByLocationParams(businessId, null, locationParams));
    }

    private void setPetInfo(List<CalendarCardDTO> cardList) {
        List<Integer> petIdList = cardList.stream()
                .flatMap(card -> card.getPetList().stream())
                .map(PetInfoDTO::getPetId)
                .distinct()
                .toList();
        if (petIdList.isEmpty()) {
            return;
        }
        // 查询宠物的基础信息
        List<CustomerPetPetCodeDTO> petCodeInfoList =
                iPetCodeClient.getCustomerPetPetCodeListByIdList(false, petIdList);
        Map<Integer, CustomerPetPetCodeDTO> petCodeInfoMap = petCodeInfoList.stream()
                .collect(Collectors.toMap(CustomerPetPetCodeDTO::getPetId, Function.identity()));

        // 查询宠物疫苗情况
        Map<Integer, List<VaccineBindingRecordDto>> vaccinePetInfoDtoMap =
                iPetVaccineService.getVaccineInfoByPetIdList(petIdList);

        cardList.forEach(card -> {
            card.getPetList().forEach(pet -> {
                if (!petCodeInfoMap.containsKey(pet.getPetId())) {
                    log.error("petId {} not found in petInfoMap", pet.getPetId());
                    return;
                }
                CustomerPetPetCodeDTO petInfo = petCodeInfoMap.get(pet.getPetId());
                pet.setPetName(petInfo.getPetName());
                pet.setPetBreedName(petInfo.getBreed());
                pet.setPetCodeList(petInfo.getMoePetCodeInfos());
                if (petInfo.getExpiryNotification() != null && petInfo.getExpiryNotification() == 1) {
                    if (vaccinePetInfoDtoMap.containsKey(pet.getPetId())) {
                        vaccinePetInfoDtoMap.get(pet.getPetId()).forEach(vaccineBindingRecordDto -> {
                            pet.getVaccineAlerts()
                                    .add(new VaccineAlertDTO(
                                            vaccineBindingRecordDto.getVaccineId(),
                                            vaccineBindingRecordDto.getVaccineName(),
                                            vaccineBindingRecordDto.getExpirationDate()));
                        });
                    }
                }
            });
        });
    }

    private void setTicketComment(List<CalendarCardDTO> cardList) {
        List<Integer> appointmentIdList = cardList.stream()
                .map(CalendarCardDTO::getAppointmentId)
                .map(Long::intValue)
                .distinct()
                .toList();

        List<MoeGroomingNote> moeGroomingNoteList = moeGroomingNoteService.getNoteListByGroomingIdListAndType(
                appointmentIdList, GroomingAppointmentEnum.NOTE_COMMENT.intValue());
        Map<Integer, String> moeGroomingNoteMap = moeGroomingNoteList.stream()
                .collect(Collectors.toMap(MoeGroomingNote::getGroomingId, MoeGroomingNote::getNote, (e1, e2) -> e1));

        cardList.forEach(card -> card.setTicketComments(
                moeGroomingNoteMap.get(card.getAppointmentId().intValue())));
    }

    private void setAlertNote(List<CalendarCardDTO> cardList) {
        List<Integer> appointmentIdList = cardList.stream()
                .map(CalendarCardDTO::getAppointmentId)
                .map(Long::intValue)
                .distinct()
                .toList();

        List<MoeGroomingNote> moeGroomingNoteList = moeGroomingNoteService.getNoteListByGroomingIdListAndType(
                appointmentIdList, GroomingAppointmentEnum.NOTE_ALERT.intValue());
        Map<Integer, String> moeGroomingNoteMap = moeGroomingNoteList.stream()
                .collect(Collectors.toMap(MoeGroomingNote::getGroomingId, MoeGroomingNote::getNote, (e1, e2) -> e1));

        cardList.forEach(card -> card.setAlertNotes(
                moeGroomingNoteMap.get(card.getAppointmentId().intValue())));
    }

    private void setAgreementInfo(Long businessId, List<CalendarCardDTO> cardList) {
        List<BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentId> customerWithAppointmentIds =
                cardList.stream()
                        .map(card -> BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentId.newBuilder()
                                .setCustomerId(card.getClientInfo().getClientId())
                                .setAppointmentId(card.getAppointmentId())
                                .build())
                        .distinct()
                        .toList();
        if (CollectionUtils.isEmpty(customerWithAppointmentIds)) {
            return;
        }
        BatchGetAgreementUnsignedAppointmentResponse response =
                agreementServiceBlockingStub.batchGetAgreementUnsignedAppointment(
                        BatchGetAgreementUnsignedAppointmentRequest.newBuilder()
                                .setBusinessId(businessId)
                                .addAllCustomerWithAppointmentId(customerWithAppointmentIds)
                                .build());
        List<Long> unsignedAppointmentIds = response.getAppointmentIdList();
        cardList.forEach(card -> card.setRequiredSign(unsignedAppointmentIds.contains(card.getAppointmentId())));
    }

    private void setPrepayStatus(Integer businessId, List<CalendarCardDTO> calendarCardDTOS) {
        List<Integer> appointmentIdList = calendarCardDTOS.stream()
                .map(CalendarCardDTO::getAppointmentId)
                .map(Long::intValue)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return;
        }

        List<MoeGroomingInvoice> invoices =
                orderService.getListByGroomingIds(null, appointmentIdList, OrderSourceType.APPOINTMENT.getSource());
        Map<Integer, MoeGroomingInvoice> invoiceMap = invoices.stream()
                .collect(Collectors.toMap(MoeGroomingInvoice::getGroomingId, Function.identity(), (i1, i2) -> i1));

        List<MoeBookOnlineDeposit> deposits =
                moeBookOnlineDepositService.getOBDepositByGroomingIds(businessId, new HashSet<>(appointmentIdList));
        Map<Integer, MoeBookOnlineDeposit> depositMap = deposits.stream()
                .collect(Collectors.toMap(MoeBookOnlineDeposit::getGroomingId, Function.identity(), (i1, i2) -> i1));

        calendarCardDTOS.forEach(card -> {
            card.setPaidAmount(invoiceMap
                    .getOrDefault(card.getAppointmentId().intValue(), new MoeGroomingInvoice())
                    .getPaidAmount());
            if (depositMap.containsKey(card.getAppointmentId().intValue())) {
                MoeBookOnlineDeposit deposit =
                        depositMap.get(card.getAppointmentId().intValue());
                if (DepositPaymentTypeEnum.PrePay.equals(deposit.getDepositType())) {
                    card.setPrepayStatus(deposit.getStatus());
                    card.setPrepaidAmount(deposit.getAmount());
                }
            }
        });
    }

    /*
    appointment 的时间更新，需要更新 appointment 的时间，同时更新 service 的时间
     */
    private Boolean rescheduleAppointment(
            CardRescheduleParams params,
            MoeGroomingAppointment existingAppointment,
            List<GroomingPetDetailDTO> existGroomingServices,
            List<MoeGroomingServiceOperation> existServiceOperations) {
        // 更新 appointment 的时间
        MoeGroomingAppointment appointmentToBeUpdated = new MoeGroomingAppointment();
        appointmentToBeUpdated.setId(params.appointmentId());
        appointmentToBeUpdated.setAppointmentDate(params.startDate());
        appointmentToBeUpdated.setAppointmentEndDate(
                params.startDate()); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
        appointmentToBeUpdated.setAppointmentStartTime(params.startTime());
        appointmentToBeUpdated.setAppointmentEndTime(params.endTime());
        appointmentToBeUpdated.setUpdatedById(params.tokenStaffId().longValue());
        // 保存旧的日期时间
        appointmentToBeUpdated.setOldAppointmentDate(existingAppointment.getAppointmentDate());
        appointmentToBeUpdated.setOldAppointmentStartTime(existingAppointment.getAppointmentStartTime());
        appointmentToBeUpdated.setOldAppointmentEndTime(existingAppointment.getOldAppointmentEndTime());

        ActivityLogRecorder.record(
                AppointmentAction.RESCHEDULE,
                ResourceType.APPOINTMENT,
                params.appointmentId(),
                new ChangeTimeLogDTO(
                        convertDateAndMinutesToTimestamp(
                                existingAppointment.getAppointmentDate(),
                                existingAppointment.getAppointmentStartTime(),
                                params.businessId()),
                        convertDateAndMinutesToTimestamp(params.startDate(), params.startTime(), params.businessId()),
                        AppointmentUpdatedBy.BY_BUSINESS));

        // 更新 service 的时间，同时将 Service 的操作人改成新的操作人
        List<MoeGroomingPetDetail> petDetailListToUpdate = new ArrayList<>();
        int diff = params.startTime() - existingAppointment.getAppointmentStartTime();
        if (existGroomingServices.size() == 1) {
            // 仅有一个 service 的 appointment，支持改变预约时长
            MoeGroomingPetDetail groomingServiceToBeUpdated = new MoeGroomingPetDetail();
            groomingServiceToBeUpdated.setId(existGroomingServices.get(0).getId());
            groomingServiceToBeUpdated.setStartTime(params.startTime().longValue());
            groomingServiceToBeUpdated.setEndTime(params.endTime().longValue());
            groomingServiceToBeUpdated.setStartDate(params.startDate());
            groomingServiceToBeUpdated.setEndDate(params.startDate());
            groomingServiceToBeUpdated.setServiceTime(params.endTime() - params.startTime());
            groomingServiceToBeUpdated.setStaffId(params.staffId());
            petDetailListToUpdate.add(groomingServiceToBeUpdated);
        } else {
            existGroomingServices.forEach(groomingService -> {
                MoeGroomingPetDetail groomingServiceToBeUpdated = new MoeGroomingPetDetail();
                groomingServiceToBeUpdated.setId(groomingService.getId());
                groomingServiceToBeUpdated.setStartTime(groomingService.getStartTime() + diff);
                groomingServiceToBeUpdated.setEndTime(
                        groomingServiceToBeUpdated.getStartTime() + groomingService.getServiceTime());
                groomingServiceToBeUpdated.setStartDate(params.startDate());
                groomingServiceToBeUpdated.setEndDate(params.startDate());
                groomingServiceToBeUpdated.setStaffId(params.staffId());
                petDetailListToUpdate.add(groomingServiceToBeUpdated);
            });
        }

        // 更新 operation 的时间
        List<MoeGroomingServiceOperation> operationListToUpdate = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(existServiceOperations)) {
            existServiceOperations.forEach(serviceOperation -> {
                MoeGroomingServiceOperation operationToBeUpdated = new MoeGroomingServiceOperation();
                operationToBeUpdated.setId(serviceOperation.getId());
                operationToBeUpdated.setStartTime(serviceOperation.getStartTime() + diff);
                operationListToUpdate.add(operationToBeUpdated);
            });
        }

        appointmentStorageService.updateAppointment(
                appointmentToBeUpdated, petDetailListToUpdate, operationListToUpdate);

        return true;
    }

    /*
    service 的时间更新，需要更新 service 的时间，同时更新 operation 的时间
     */
    private Boolean rescheduleService(
            CardRescheduleParams params,
            MoeGroomingAppointment existingAppointment,
            List<GroomingPetDetailDTO> existGroomingServices) {
        if ((Objects.compare(params.startDate(), existingAppointment.getAppointmentDate(), Comparator.naturalOrder())
                        < 0)
                || (Objects.compare(
                                params.startDate(),
                                existingAppointment.getAppointmentEndDate(),
                                Comparator.naturalOrder())
                        > 0)) {
            log.error("cannot reschedule service to a date out of appointment date");
            return false;
        }
        CardRescheduleParams updateParams;
        // 检查更新参数，如果更新 repeat，params.id 是第一个预约的 petDetailId，更新后续预约时，需要根据 petId 和 serviceId 匹配
        Integer petDetailId = params.id().intValue();
        Optional<GroomingPetDetailDTO> matchPetDetail = existGroomingServices.stream()
                .filter(service -> Objects.equals(service.getId(), petDetailId))
                .findFirst();
        if (matchPetDetail.isEmpty()) {
            MoeGroomingPetDetail originModifiedPetDetail = petDetailMapper.selectByPrimaryKey(petDetailId);
            matchPetDetail = existGroomingServices.stream()
                    .filter(service -> Objects.equals(service.getPetId(), originModifiedPetDetail.getPetId())
                            && Objects.equals(service.getServiceId(), originModifiedPetDetail.getServiceId()))
                    .findFirst();
            if (matchPetDetail.isEmpty()) {
                // 找不到匹配的 pet detail，跳过更新
                log.error(
                        "pet detail not found, params.id: {}, params.appointmentId: {}",
                        params.id(),
                        params.appointmentId());
                return false;
            }
            MoeGroomingPetDetailExample example = new MoeGroomingPetDetailExample();
            example.createCriteria()
                    .andIdIn(params.petDetailIds())
                    .andStatusEqualTo((byte) 1)
                    .andServiceItemTypeEqualTo(ServiceItemType.GROOMING_VALUE);
            List<MoeGroomingPetDetail> petDetails = petDetailMapper.selectByExample(example);
            List<Integer> updatePetDetailIds = new ArrayList<>();
            for (MoeGroomingPetDetail petDetail : petDetails) {
                existGroomingServices.stream()
                        .filter(service -> Objects.equals(service.getPetId(), petDetail.getPetId())
                                && Objects.equals(service.getServiceId(), petDetail.getServiceId()))
                        .findFirst()
                        .ifPresent(service -> updatePetDetailIds.add(service.getId()));
            }
            updateParams = params.toBuilder()
                    .id(matchPetDetail.get().getId().longValue())
                    .petDetailIds(updatePetDetailIds)
                    .build();
        } else {
            updateParams = params; // 无需更新参数
        }

        List<MoeGroomingPetDetail> petDetailListToUpdate = new ArrayList<>();
        if (CollectionUtils.isEmpty(updateParams.petDetailIds())
                || updateParams.petDetailIds().size() == 1) {
            petDetailListToUpdate.add(buildPetDetailToUpdate(updateParams));
        } else {
            petDetailListToUpdate.addAll(buildPetDetailToUpdate(updateParams, existGroomingServices));
        }

        // 判断是否需要更新 appointment 的时间
        MoeGroomingAppointment appointmentToBeUpdated = null;
        int minStartTime = Math.min(
                updateParams.startTime(),
                existGroomingServices.stream()
                        .filter(service -> !updateParams.petDetailIds().contains(service.getId()))
                        .map(GroomingPetDetailDTO::getStartTime)
                        .map(Long::intValue)
                        .min(Integer::compareTo)
                        .orElse(existingAppointment.getAppointmentStartTime()));
        int maxEndTime = Math.max(
                updateParams.endTime(),
                existGroomingServices.stream()
                        .filter(service -> !updateParams.petDetailIds().contains(service.getId()))
                        .map(GroomingPetDetailDTO::getEndTime)
                        .map(Long::intValue)
                        .max(Integer::compareTo)
                        .orElse(existingAppointment.getAppointmentEndTime()));
        if (minStartTime != existingAppointment.getAppointmentStartTime()
                || maxEndTime != existingAppointment.getAppointmentEndTime()) {
            appointmentToBeUpdated = new MoeGroomingAppointment();
            appointmentToBeUpdated.setId(updateParams.appointmentId());
            appointmentToBeUpdated.setAppointmentStartTime(minStartTime);
            appointmentToBeUpdated.setAppointmentEndTime(maxEndTime);
            appointmentToBeUpdated.setUpdatedById(updateParams.tokenStaffId().longValue());
            ActivityLogRecorder.record(
                    AppointmentAction.RESCHEDULE,
                    ResourceType.APPOINTMENT,
                    params.appointmentId(),
                    new ChangeTimeLogDTO(
                            convertDateAndMinutesToTimestamp(
                                    existingAppointment.getAppointmentDate(),
                                    existingAppointment.getAppointmentStartTime(),
                                    params.businessId()),
                            convertDateAndMinutesToTimestamp(params.startDate(), minStartTime, params.businessId()),
                            AppointmentUpdatedBy.BY_BUSINESS));
        }
        appointmentStorageService.updateAppointment(appointmentToBeUpdated, petDetailListToUpdate, null);
        return true;
    }

    private MoeGroomingPetDetail buildPetDetailToUpdate(CardRescheduleParams params) {
        // 更新 service 时间和操作人
        MoeGroomingPetDetail groomingServiceToBeUpdated = new MoeGroomingPetDetail();
        groomingServiceToBeUpdated.setId(params.id().intValue());
        groomingServiceToBeUpdated.setStartTime(params.startTime().longValue());
        groomingServiceToBeUpdated.setEndTime(params.endTime().longValue());
        groomingServiceToBeUpdated.setServiceTime(params.endTime() - params.startTime());
        groomingServiceToBeUpdated.setStaffId(params.staffId());
        return groomingServiceToBeUpdated;
    }

    private List<MoeGroomingPetDetail> buildPetDetailToUpdate(
            CardRescheduleParams params, List<GroomingPetDetailDTO> existGroomingServices) {
        int diff = params.startTime()
                - existGroomingServices.stream()
                        .filter(service -> service.getId().equals(params.id().intValue()))
                        .map(GroomingPetDetailDTO::getStartTime)
                        .findFirst()
                        .orElse(0L)
                        .intValue();
        Map<Integer, GroomingPetDetailDTO> petDetailMap = existGroomingServices.stream()
                .collect(Collectors.toMap(GroomingPetDetailDTO::getId, Function.identity()));
        List<MoeGroomingPetDetail> petDetailListToUpdate = new ArrayList<>();
        params.petDetailIds().forEach(id -> {
            if (!petDetailMap.containsKey(id)) {
                log.error("petDetailId {} not found in petDetailMap", id);
                throw ExceptionUtil.bizException(Code.CODE_SERVICE_NOT_FOUND);
            }
            MoeGroomingPetDetail groomingServiceToBeUpdated = new MoeGroomingPetDetail();
            groomingServiceToBeUpdated.setId(id);
            groomingServiceToBeUpdated.setStartTime(petDetailMap.get(id).getStartTime() + diff);
            groomingServiceToBeUpdated.setEndTime(petDetailMap.get(id).getEndTime() + diff);
            groomingServiceToBeUpdated.setStaffId(params.staffId());
            petDetailListToUpdate.add(groomingServiceToBeUpdated);
        });
        return petDetailListToUpdate;
    }

    /*
    operation 的时间更新，需要更新 service 的时间，同时更新其他 staff 的 operation 的时间，保证 operation 间相对的时间关系保持不变
     */
    private Boolean rescheduleOperation(
            CardRescheduleParams params,
            MoeGroomingAppointment existingAppointment,
            List<GroomingPetDetailDTO> existGroomingServices,
            List<MoeGroomingServiceOperation> existServiceOperations) {
        if (Objects.compare(params.startDate(), existingAppointment.getAppointmentDate(), Comparator.naturalOrder()) < 0
                || Objects.compare(
                                params.startDate(),
                                existingAppointment.getAppointmentEndDate(),
                                Comparator.naturalOrder())
                        > 0) {
            log.error("cannot reschedule operation to a date out of appointment date");
            return false;
        }
        // 先更新 operation 的时间，需要保持相对的时间关系不变
        List<MoeGroomingServiceOperation> operationListToUpdate = new ArrayList<>();
        MoeGroomingServiceOperation operationToUpdate;
        GroomingPetDetailDTO serviceToUpdate;
        Optional<MoeGroomingServiceOperation> matchOperation = existServiceOperations.stream()
                .filter(operation -> operation.getId().equals(params.id()))
                .findFirst();
        if (matchOperation.isEmpty()) {
            MoeGroomingServiceOperation originModifyOperation = serviceOperationMapper.selectByPrimaryKey(params.id());
            if (originModifyOperation == null) {
                log.error("operation not found, params:{}", JsonUtil.toJson(params));
                return false;
            }
            MoeGroomingPetDetail originModifyPetDetail =
                    petDetailMapper.selectByPrimaryKey(originModifyOperation.getGroomingServiceId());
            if (originModifyPetDetail == null) {
                log.error("pet detail not found, params:{}", JsonUtil.toJson(params));
                return false;
            }

            Optional<GroomingPetDetailDTO> serviceOptional = existGroomingServices.stream()
                    .filter(service -> service.getPetId().equals(originModifyPetDetail.getPetId())
                            && service.getServiceId().equals(originModifyPetDetail.getServiceId()))
                    .findFirst();
            if (serviceOptional.isEmpty()) {
                log.error("service not found in existGroomingServices, params:{}", JsonUtil.toJson(params));
                return false;
            }
            serviceToUpdate = serviceOptional.get();

            Optional<MoeGroomingServiceOperation> operationOptional = existServiceOperations.stream()
                    .filter(operation -> operation.getGroomingServiceId().equals(serviceToUpdate.getId()))
                    .findFirst();
            if (operationOptional.isEmpty()) {
                log.error("operation not found in existServiceOperations, params:{}", JsonUtil.toJson(params));
                return false;
            }
            operationToUpdate = operationOptional.get();
        } else {
            operationToUpdate = matchOperation.get();
            // 更新 service 的时间
            serviceToUpdate = existGroomingServices.stream()
                    .filter(service -> service.getId().equals(operationToUpdate.getGroomingServiceId()))
                    .findFirst()
                    .orElseThrow(() -> {
                        log.error(
                                "serviceId {} not found in existGroomingServices",
                                operationToUpdate.getGroomingServiceId());
                        return ExceptionUtil.bizException(Code.CODE_SERVICE_NOT_FOUND);
                    });
        }

        // 如果更新的是 main staff 的 operation，需要更新 pet detail 的 staffId 为新 staffId
        Integer mainStaffId = serviceToUpdate.getStaffId();
        if (Objects.equals(operationToUpdate.getStaffId(), serviceToUpdate.getStaffId())) {
            mainStaffId = params.staffId();
        }

        Integer diff = params.startTime() - operationToUpdate.getStartTime();
        existServiceOperations.stream()
                .filter(serviceOperation ->
                        serviceOperation.getGroomingServiceId().equals(serviceToUpdate.getId()))
                .forEach(serviceOperation -> {
                    serviceOperation.setStartTime(serviceOperation.getStartTime() + diff);
                    if (serviceOperation.getId().equals(params.id()) && Objects.nonNull(params.staffId())) {
                        serviceOperation.setStaffId(params.staffId());
                    }
                    operationListToUpdate.add(serviceOperation);
                });

        MoeGroomingPetDetail groomingServiceToBeUpdated = new MoeGroomingPetDetail();
        groomingServiceToBeUpdated.setId(serviceToUpdate.getId());
        groomingServiceToBeUpdated.setStartTime(serviceToUpdate.getStartTime() + diff);
        groomingServiceToBeUpdated.setEndTime(serviceToUpdate.getEndTime() + diff);
        groomingServiceToBeUpdated.setStaffId(mainStaffId);
        groomingServiceToBeUpdated.setStartDate(params.startDate());
        groomingServiceToBeUpdated.setEndDate(params.startDate());
        List<MoeGroomingPetDetail> petDetailListToUpdate = Collections.singletonList(groomingServiceToBeUpdated);

        // 判断是否需要更新 appointment 的时间
        MoeGroomingAppointment appointmentToBeUpdated = null;
        serviceToUpdate.setStartTime(serviceToUpdate.getStartTime() + diff);
        serviceToUpdate.setEndTime(serviceToUpdate.getEndTime() + diff);
        int minStartTime = existGroomingServices.stream()
                .map(GroomingPetDetailDTO::getStartTime)
                .min(Long::compareTo)
                .orElse(existingAppointment.getAppointmentEndTime().longValue())
                .intValue();
        int maxEndTime = existGroomingServices.stream()
                .map(GroomingPetDetailDTO::getEndTime)
                .max(Long::compareTo)
                .orElse(existingAppointment.getAppointmentEndTime().longValue())
                .intValue();
        if (minStartTime != existingAppointment.getAppointmentStartTime()
                || maxEndTime != existingAppointment.getAppointmentEndTime()) {
            appointmentToBeUpdated = new MoeGroomingAppointment();
            appointmentToBeUpdated.setId(params.appointmentId());
            appointmentToBeUpdated.setAppointmentStartTime(minStartTime);
            appointmentToBeUpdated.setAppointmentEndTime(maxEndTime);
            appointmentToBeUpdated.setUpdatedById(params.tokenStaffId().longValue());
            ActivityLogRecorder.record(
                    AppointmentAction.RESCHEDULE,
                    ResourceType.APPOINTMENT,
                    params.appointmentId(),
                    new ChangeTimeLogDTO(
                            convertDateAndMinutesToTimestamp(
                                    existingAppointment.getAppointmentDate(),
                                    existingAppointment.getAppointmentStartTime(),
                                    params.businessId()),
                            convertDateAndMinutesToTimestamp(params.startDate(), minStartTime, params.businessId()),
                            AppointmentUpdatedBy.BY_BUSINESS));
        }
        appointmentStorageService.updateAppointment(
                appointmentToBeUpdated, petDetailListToUpdate, operationListToUpdate);

        return true;
    }

    /*
    block 的时间更新，直接更新即可
     */
    private Boolean rescheduleBlock(CardRescheduleParams params, MoeGroomingAppointment existingAppointment) {
        // repeat type 的判断放在前面了，这里只需要更新单个 block 的时间即可
        //        List<Integer> appointmentIdsToUpdate;
        //        if (Objects.isNull(params.repeatType()) || params.repeatType().equals(RepeatModifyTypeEnum.ONLY_THIS))
        // {
        //            appointmentIdsToUpdate = Collections.singletonList(params.appointmentId());
        //        } else if (params.repeatType().equals(RepeatModifyTypeEnum.THIS_AND_FOLLOWING)) {
        //            appointmentIdsToUpdate = appointmentMapper.selectBlockByRepeatType(
        //                    params.businessId(), existingAppointment.getRepeatId(),
        // existingAppointment.getAppointmentDate());
        //        } else if (params.repeatType().equals(RepeatModifyTypeEnum.ALL)) {
        //            appointmentIdsToUpdate = appointmentMapper.selectBlockByRepeatType(
        //                    params.businessId(), existingAppointment.getRepeatId(), null);
        //        } else {
        //            log.error("repeatType {} not supported", params.repeatType());
        //            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        //        }

        appointmentStorageService.updateBlockTime(
                params.appointmentId(), params.staffId(), params.startDate(), params.startTime(), params.endTime());

        //        if (Objects.nonNull(params.startDate())) {
        //            // 时间修改仅对当前的 block 生效，同一个 repeat 集合里的其他 block 不受影响
        //            MoeGroomingAppointment appointmentToBeUpdated = new MoeGroomingAppointment();
        //            appointmentToBeUpdated.setId(existingAppointment.getId());
        //            appointmentToBeUpdated.setAppointmentDate(params.startDate());
        //            appointmentMapper.updateByPrimaryKeySelective(appointmentToBeUpdated);
        //        }

        return true;
    }

    private Long convertDateAndMinutesToTimestamp(String date, Integer minutes, Integer businessId) {
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(businessId).build());
        if (businessInfo == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business not found");
        }

        return DateUtil.timestamp(date, businessInfo.getTimezoneName()) / 1000 + 60L * minutes;
    }
}
