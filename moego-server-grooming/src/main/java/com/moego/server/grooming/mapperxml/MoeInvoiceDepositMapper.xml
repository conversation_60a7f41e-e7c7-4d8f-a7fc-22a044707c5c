<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeInvoiceDepositMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeInvoiceDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="de_guid" jdbcType="VARCHAR" property="deGuid" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, invoice_id, business_id, staff_id, amount, create_time, update_time, status, 
    de_guid, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_invoice_deposit
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_invoice_deposit
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeInvoiceDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_invoice_deposit (invoice_id, business_id, staff_id, 
      amount, create_time, update_time, 
      status, de_guid, company_id
      )
    values (#{invoiceId,jdbcType=INTEGER}, #{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, 
      #{amount,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{deGuid,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeInvoiceDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_invoice_deposit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deGuid != null">
        de_guid,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="deGuid != null">
        #{deGuid,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeInvoiceDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_invoice_deposit
    <set>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="deGuid != null">
        de_guid = #{deGuid,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeInvoiceDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_invoice_deposit
    set invoice_id = #{invoiceId,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      de_guid = #{deGuid,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectByDeGuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_invoice_deposit
        where de_guid = #{deGuid,jdbcType=VARCHAR}
    </select>
    <select id="selectByInvoiceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_invoice_deposit
        where invoice_id = #{invoiceId,jdbcType=INTEGER} and status &gt; 0
        limit 1;
    </select>
</mapper>