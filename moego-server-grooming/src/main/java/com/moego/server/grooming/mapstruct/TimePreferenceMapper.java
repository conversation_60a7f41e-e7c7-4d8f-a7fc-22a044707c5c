package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.waitlist.TimePreferenceDTO;
import com.moego.server.grooming.mapper.po.TimePreferencePO;
import com.moego.server.grooming.web.params.waitlist.TimePreference;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TimePreferenceMapper {
    TimePreferenceMapper INSTANCE = Mappers.getMapper(TimePreferenceMapper.class);

    TimePreferenceDTO toDto(TimePreferencePO po);

    TimePreferencePO toEntity(TimePreference param);
}
