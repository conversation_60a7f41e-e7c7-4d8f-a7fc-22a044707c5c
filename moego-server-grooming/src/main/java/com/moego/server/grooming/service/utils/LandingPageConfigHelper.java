package com.moego.server.grooming.service.utils;

import com.moego.common.enums.BusinessConst;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class LandingPageConfigHelper {

    private final IBusinessBusinessService businessApi;

    /**
     * Get component to enabled map, populate default value if not exist.
     *
     * @param landingPageConfig landing page config
     * @return component to enabled map
     */
    public Map<String, Boolean> getComponentEnabledMap(MoeBookOnlineLandingPageConfig landingPageConfig) {
        Map<String, Boolean> componentToEnabled =
                new HashMap<>(JsonUtil.toBean(landingPageConfig.getPageComponents(), new TypeRef<>() {}));
        Arrays.stream(LandingPageComponentEnum.values())
                .forEach(it -> componentToEnabled.putIfAbsent(it.getComponent(), it.isEnable()));

        MoeBusinessDto business = businessApi.getBusinessInfo(new InfoIdParams(landingPageConfig.getBusinessId()));
        if (Objects.equals(business.getAppType(), BusinessConst.APP_TYPE_SALON)) {
            componentToEnabled.remove(LandingPageComponentEnum.SERVICE_AREA.getComponent());
        }

        return componentToEnabled;
    }
}
