package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_gc_setting
 */
public class MoeGcSetting {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.google_auth_id
     *
     * @mbg.generated
     */
    private Integer googleAuthId;

    /**
     * Database Column Remarks:
     *   1 import and export   2 export only  3 import only
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.sync_type
     *
     * @mbg.generated
     */
    private Byte syncType;

    /**
     * Database Column Remarks:
     *   1 有效 2弃用
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   calendar name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.calendar_name
     *
     * @mbg.generated
     */
    private String calendarName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   exported event title
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.event_title
     *
     * @mbg.generated
     */
    private String eventTitle;

    /**
     * Database Column Remarks:
     *   exported event description
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_setting.event_description
     *
     * @mbg.generated
     */
    private String eventDescription;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.id
     *
     * @return the value of moe_gc_setting.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.id
     *
     * @param id the value for moe_gc_setting.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.business_id
     *
     * @return the value of moe_gc_setting.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.business_id
     *
     * @param businessId the value for moe_gc_setting.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.staff_id
     *
     * @return the value of moe_gc_setting.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.staff_id
     *
     * @param staffId the value for moe_gc_setting.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.google_auth_id
     *
     * @return the value of moe_gc_setting.google_auth_id
     *
     * @mbg.generated
     */
    public Integer getGoogleAuthId() {
        return googleAuthId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.google_auth_id
     *
     * @param googleAuthId the value for moe_gc_setting.google_auth_id
     *
     * @mbg.generated
     */
    public void setGoogleAuthId(Integer googleAuthId) {
        this.googleAuthId = googleAuthId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.sync_type
     *
     * @return the value of moe_gc_setting.sync_type
     *
     * @mbg.generated
     */
    public Byte getSyncType() {
        return syncType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.sync_type
     *
     * @param syncType the value for moe_gc_setting.sync_type
     *
     * @mbg.generated
     */
    public void setSyncType(Byte syncType) {
        this.syncType = syncType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.status
     *
     * @return the value of moe_gc_setting.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.status
     *
     * @param status the value for moe_gc_setting.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.create_time
     *
     * @return the value of moe_gc_setting.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.create_time
     *
     * @param createTime the value for moe_gc_setting.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.update_time
     *
     * @return the value of moe_gc_setting.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.update_time
     *
     * @param updateTime the value for moe_gc_setting.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.calendar_name
     *
     * @return the value of moe_gc_setting.calendar_name
     *
     * @mbg.generated
     */
    public String getCalendarName() {
        return calendarName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.calendar_name
     *
     * @param calendarName the value for moe_gc_setting.calendar_name
     *
     * @mbg.generated
     */
    public void setCalendarName(String calendarName) {
        this.calendarName = calendarName == null ? null : calendarName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.company_id
     *
     * @return the value of moe_gc_setting.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.company_id
     *
     * @param companyId the value for moe_gc_setting.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.event_title
     *
     * @return the value of moe_gc_setting.event_title
     *
     * @mbg.generated
     */
    public String getEventTitle() {
        return eventTitle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.event_title
     *
     * @param eventTitle the value for moe_gc_setting.event_title
     *
     * @mbg.generated
     */
    public void setEventTitle(String eventTitle) {
        this.eventTitle = eventTitle == null ? null : eventTitle.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_setting.event_description
     *
     * @return the value of moe_gc_setting.event_description
     *
     * @mbg.generated
     */
    public String getEventDescription() {
        return eventDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_setting.event_description
     *
     * @param eventDescription the value for moe_gc_setting.event_description
     *
     * @mbg.generated
     */
    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription == null ? null : eventDescription.trim();
    }
}
