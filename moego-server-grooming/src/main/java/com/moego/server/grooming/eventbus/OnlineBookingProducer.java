package com.moego.server.grooming.eventbus;

import com.google.protobuf.Timestamp;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.idl.models.event_bus.v1.EventType;
import com.moego.idl.models.event_bus.v1.OnlineBookingAbandonedEvent;
import com.moego.idl.models.event_bus.v1.OnlineBookingAcceptedEvent;
import com.moego.idl.models.event_bus.v1.OnlineBookingSubmittedEvent;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.lib.event_bus.producer.Producer;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@AllArgsConstructor
public class OnlineBookingProducer {
    private final AppointmentMapperProxy moeGroomingAppointmentMapper;
    private final Producer producer;
    private final CompanyHelper companyHelper;
    private static final String ONLINE_BOOKING_EVENT_TOPIC = "moego.erp.online_booking";

    public void pushOnlineBookingSubmittedEvent(Integer appointmentId) {
        ThreadPool.execute(() -> {
            var appt = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
            producer.send(ONLINE_BOOKING_EVENT_TOPIC, buildOnlineBookingSubmittedEvent(appt));
        });
    }

    public void pushOnlineBookingAcceptedEvent(MoeGroomingAppointment entity) {
        ThreadPool.execute(() -> {
            var timeZoneName = companyHelper.getCompanyTimeZoneName(entity.getCompanyId());
            producer.send(ONLINE_BOOKING_EVENT_TOPIC, buildOnlineBookingAcceptedEvent(entity, timeZoneName));
        });
    }

    public void pushOnlineBookingAbandonedEvent(MoeBookOnlineAbandonRecord abandonRecord) {
        producer.send(ONLINE_BOOKING_EVENT_TOPIC, buildOnlineBookingAbandonedEvent(abandonRecord));
    }

    private EventRecord buildOnlineBookingSubmittedEvent(MoeGroomingAppointment entity) {
        var eventBuilder = OnlineBookingSubmittedEvent.newBuilder();
        if (StringUtils.hasText(entity.getAppointmentDate()) && !entity.getNoStartTime()) {
            var timeZoneName = companyHelper.getCompanyTimeZoneName(entity.getCompanyId());
            eventBuilder.setStartTime(
                    getTimestamp(timeZoneName, entity.getAppointmentDate(), entity.getAppointmentStartTime()));
        }
        return EventRecord.builder()
                .id(entity.getId().toString())
                .time(Instant.ofEpochSecond(entity.getCreateTime()))
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(entity.getCompanyId())
                                .setBusinessId(entity.getBusinessId())
                                .build())
                        .setOnlineBookingSubmittedEvent(eventBuilder
                                .setId(entity.getId())
                                .setCustomerId(entity.getCustomerId())
                                .addAllServiceItemTypes(
                                        ServiceItemEnum.convertBitValueList(entity.getServiceTypeInclude()).stream()
                                                .map(type -> ServiceItemType.forNumber(type.getServiceItem()))
                                                .toList())
                                .build())
                        .build())
                .type(EventType.ONLINE_BOOKING_SUBMITTED)
                .build();
    }

    private static EventRecord buildOnlineBookingAcceptedEvent(MoeGroomingAppointment entity, String timeZoneName) {
        return EventRecord.builder()
                .id(entity.getId().toString())
                .time(Instant.ofEpochSecond(entity.getCreateTime()))
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(entity.getCompanyId())
                                .setBusinessId(entity.getBusinessId())
                                .build())
                        .setOnlineBookingAcceptedEvent(OnlineBookingAcceptedEvent.newBuilder()
                                .setId(entity.getId())
                                .setCustomerId(entity.getCustomerId())
                                .setStartTime(getTimestamp(
                                        timeZoneName, entity.getAppointmentDate(), entity.getAppointmentStartTime()))
                                .addAllServiceItemTypes(convertServiceItemTypes(entity.getServiceTypeInclude()))
                                .build())
                        .build())
                .type(EventType.ONLINE_BOOKING_ACCEPTED)
                .build();
    }

    private static EventRecord buildOnlineBookingAbandonedEvent(MoeBookOnlineAbandonRecord abandonRecord) {
        var eventRecordBuilder = EventRecord.builder();
        eventRecordBuilder.time(Instant.now());
        var eventBuilder = OnlineBookingAbandonedEvent.newBuilder();
        if (abandonRecord.getCustomerId() != null) {
            eventRecordBuilder.id(abandonRecord.getCustomerId().toString());
            eventBuilder.setCustomerId(abandonRecord.getCustomerId());
        }
        if (abandonRecord.getCareType() != null) {
            eventBuilder.addServiceItemTypes(ServiceItemType.valueOf(abandonRecord.getCareType()));
        }

        eventRecordBuilder.detail(EventData.newBuilder()
                .setTenant(Tenant.newBuilder()
                        .setCompanyId(abandonRecord.getCompanyId())
                        .setBusinessId(abandonRecord.getBusinessId())
                        .build())
                .setOnlineBookingAbandonedEvent(eventBuilder.build())
                .build());
        return eventRecordBuilder.type(EventType.ONLINE_BOOKING_ABANDONED).build();
    }

    private static Timestamp getTimestamp(String timeZoneName, String date, Integer minutes) {
        long seconds = LocalDate.parse(date)
                .atStartOfDay(ZoneId.of(timeZoneName))
                .plusMinutes(minutes)
                .toEpochSecond();
        return Timestamp.newBuilder().setSeconds(seconds).build();
    }

    private static List<ServiceItemType> convertServiceItemTypes(Integer serviceTypeInclude) {
        return ServiceItemEnum.convertBitValueList(serviceTypeInclude).stream()
                .map(type -> ServiceItemType.forNumber(type.getServiceItem()))
                .toList();
    }
}
