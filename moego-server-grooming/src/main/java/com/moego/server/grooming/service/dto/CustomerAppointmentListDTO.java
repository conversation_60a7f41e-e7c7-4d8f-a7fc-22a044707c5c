package com.moego.server.grooming.service.dto;

import com.github.pagehelper.PageInfo;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.grooming.dto.CustomerGrooming;
import com.moego.server.grooming.dto.EvaluationServiceDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerAppointmentListDTO extends PageInfo<CustomerGrooming> {

    public CustomerAppointmentListDTO(List<CustomerGrooming> list) {
        super(list);
    }

    private List<CustomerPetDetailDTO> petList;
    private List<CustomerAppointmentListStaffDTO> staffList;
    private List<MoeGroomingServiceDTO> serviceList;
    private List<EvaluationServiceDTO> evaluationServiceList;
}
