<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.AppointmentPetFeedingMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.AppointmentPetFeeding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="appointment_id" jdbcType="BIGINT" property="appointmentId" />
    <result column="pet_detail_id" jdbcType="BIGINT" property="petDetailId" />
    <result column="pet_id" jdbcType="BIGINT" property="petId" />
    <result column="feeding_amount" jdbcType="VARCHAR" property="feedingAmount" />
    <result column="feeding_unit" jdbcType="VARCHAR" property="feedingUnit" />
    <result column="feeding_type" jdbcType="VARCHAR" property="feedingType" />
    <result column="feeding_source" jdbcType="VARCHAR" property="feedingSource" />
    <result column="feeding_instruction" jdbcType="VARCHAR" property="feedingInstruction" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt" />
    <result column="feeding_note" jdbcType="VARCHAR" property="feedingNote" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, appointment_id, pet_detail_id, pet_id, feeding_amount, feeding_unit, 
    feeding_type, feeding_source, feeding_instruction, created_at, updated_at, deleted_at, 
    feeding_note
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetFeedingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from appointment_pet_feeding
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from appointment_pet_feeding
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from appointment_pet_feeding
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetFeedingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from appointment_pet_feeding
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetFeeding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_pet_feeding (company_id, appointment_id, pet_detail_id, 
      pet_id, feeding_amount, feeding_unit, 
      feeding_type, feeding_source, feeding_instruction, 
      created_at, updated_at, deleted_at, 
      feeding_note)
    values (#{companyId,jdbcType=BIGINT}, #{appointmentId,jdbcType=BIGINT}, #{petDetailId,jdbcType=BIGINT}, 
      #{petId,jdbcType=BIGINT}, #{feedingAmount,jdbcType=VARCHAR}, #{feedingUnit,jdbcType=VARCHAR}, 
      #{feedingType,jdbcType=VARCHAR}, #{feedingSource,jdbcType=VARCHAR}, #{feedingInstruction,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{deletedAt,jdbcType=TIMESTAMP}, 
      #{feedingNote,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetFeeding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_pet_feeding
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="appointmentId != null">
        appointment_id,
      </if>
      <if test="petDetailId != null">
        pet_detail_id,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="feedingAmount != null">
        feeding_amount,
      </if>
      <if test="feedingUnit != null">
        feeding_unit,
      </if>
      <if test="feedingType != null">
        feeding_type,
      </if>
      <if test="feedingSource != null">
        feeding_source,
      </if>
      <if test="feedingInstruction != null">
        feeding_instruction,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="deletedAt != null">
        deleted_at,
      </if>
      <if test="feedingNote != null">
        feeding_note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appointmentId != null">
        #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="petDetailId != null">
        #{petDetailId,jdbcType=BIGINT},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=BIGINT},
      </if>
      <if test="feedingAmount != null">
        #{feedingAmount,jdbcType=VARCHAR},
      </if>
      <if test="feedingUnit != null">
        #{feedingUnit,jdbcType=VARCHAR},
      </if>
      <if test="feedingType != null">
        #{feedingType,jdbcType=VARCHAR},
      </if>
      <if test="feedingSource != null">
        #{feedingSource,jdbcType=VARCHAR},
      </if>
      <if test="feedingInstruction != null">
        #{feedingInstruction,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedAt != null">
        #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="feedingNote != null">
        #{feedingNote,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetFeedingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from appointment_pet_feeding
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_feeding
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.appointmentId != null">
        appointment_id = #{record.appointmentId,jdbcType=BIGINT},
      </if>
      <if test="record.petDetailId != null">
        pet_detail_id = #{record.petDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.petId != null">
        pet_id = #{record.petId,jdbcType=BIGINT},
      </if>
      <if test="record.feedingAmount != null">
        feeding_amount = #{record.feedingAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.feedingUnit != null">
        feeding_unit = #{record.feedingUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.feedingType != null">
        feeding_type = #{record.feedingType,jdbcType=VARCHAR},
      </if>
      <if test="record.feedingSource != null">
        feeding_source = #{record.feedingSource,jdbcType=VARCHAR},
      </if>
      <if test="record.feedingInstruction != null">
        feeding_instruction = #{record.feedingInstruction,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deletedAt != null">
        deleted_at = #{record.deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.feedingNote != null">
        feeding_note = #{record.feedingNote,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_feeding
    set id = #{record.id,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      appointment_id = #{record.appointmentId,jdbcType=BIGINT},
      pet_detail_id = #{record.petDetailId,jdbcType=BIGINT},
      pet_id = #{record.petId,jdbcType=BIGINT},
      feeding_amount = #{record.feedingAmount,jdbcType=VARCHAR},
      feeding_unit = #{record.feedingUnit,jdbcType=VARCHAR},
      feeding_type = #{record.feedingType,jdbcType=VARCHAR},
      feeding_source = #{record.feedingSource,jdbcType=VARCHAR},
      feeding_instruction = #{record.feedingInstruction,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      deleted_at = #{record.deletedAt,jdbcType=TIMESTAMP},
      feeding_note = #{record.feedingNote,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetFeeding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_feeding
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appointmentId != null">
        appointment_id = #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="petDetailId != null">
        pet_detail_id = #{petDetailId,jdbcType=BIGINT},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=BIGINT},
      </if>
      <if test="feedingAmount != null">
        feeding_amount = #{feedingAmount,jdbcType=VARCHAR},
      </if>
      <if test="feedingUnit != null">
        feeding_unit = #{feedingUnit,jdbcType=VARCHAR},
      </if>
      <if test="feedingType != null">
        feeding_type = #{feedingType,jdbcType=VARCHAR},
      </if>
      <if test="feedingSource != null">
        feeding_source = #{feedingSource,jdbcType=VARCHAR},
      </if>
      <if test="feedingInstruction != null">
        feeding_instruction = #{feedingInstruction,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedAt != null">
        deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="feedingNote != null">
        feeding_note = #{feedingNote,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetFeeding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_feeding
    set company_id = #{companyId,jdbcType=BIGINT},
      appointment_id = #{appointmentId,jdbcType=BIGINT},
      pet_detail_id = #{petDetailId,jdbcType=BIGINT},
      pet_id = #{petId,jdbcType=BIGINT},
      feeding_amount = #{feedingAmount,jdbcType=VARCHAR},
      feeding_unit = #{feedingUnit,jdbcType=VARCHAR},
      feeding_type = #{feedingType,jdbcType=VARCHAR},
      feeding_source = #{feedingSource,jdbcType=VARCHAR},
      feeding_instruction = #{feedingInstruction,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      feeding_note = #{feedingNote,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>