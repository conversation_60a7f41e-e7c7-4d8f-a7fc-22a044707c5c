package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_invoice_deposit
 */
public class MoeInvoiceDeposit {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.invoice_id
     *
     * @mbg.generated
     */
    private Integer invoiceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.amount
     *
     * @mbg.generated
     */
    private BigDecimal amount;

    /**
     * Database Column Remarks:
     *   åˆ›å»ºæ—¶é—´
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   ä¿®æ”¹æ—¶é—´
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   0 delete 1 normal  2 paid
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.de_guid
     *
     * @mbg.generated
     */
    private String deGuid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_invoice_deposit.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.id
     *
     * @return the value of moe_invoice_deposit.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.id
     *
     * @param id the value for moe_invoice_deposit.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.invoice_id
     *
     * @return the value of moe_invoice_deposit.invoice_id
     *
     * @mbg.generated
     */
    public Integer getInvoiceId() {
        return invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.invoice_id
     *
     * @param invoiceId the value for moe_invoice_deposit.invoice_id
     *
     * @mbg.generated
     */
    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.business_id
     *
     * @return the value of moe_invoice_deposit.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.business_id
     *
     * @param businessId the value for moe_invoice_deposit.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.staff_id
     *
     * @return the value of moe_invoice_deposit.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.staff_id
     *
     * @param staffId the value for moe_invoice_deposit.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.amount
     *
     * @return the value of moe_invoice_deposit.amount
     *
     * @mbg.generated
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.amount
     *
     * @param amount the value for moe_invoice_deposit.amount
     *
     * @mbg.generated
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.create_time
     *
     * @return the value of moe_invoice_deposit.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.create_time
     *
     * @param createTime the value for moe_invoice_deposit.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.update_time
     *
     * @return the value of moe_invoice_deposit.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.update_time
     *
     * @param updateTime the value for moe_invoice_deposit.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.status
     *
     * @return the value of moe_invoice_deposit.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.status
     *
     * @param status the value for moe_invoice_deposit.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.de_guid
     *
     * @return the value of moe_invoice_deposit.de_guid
     *
     * @mbg.generated
     */
    public String getDeGuid() {
        return deGuid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.de_guid
     *
     * @param deGuid the value for moe_invoice_deposit.de_guid
     *
     * @mbg.generated
     */
    public void setDeGuid(String deGuid) {
        this.deGuid = deGuid == null ? null : deGuid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_invoice_deposit.company_id
     *
     * @return the value of moe_invoice_deposit.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_invoice_deposit.company_id
     *
     * @param companyId the value for moe_invoice_deposit.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
