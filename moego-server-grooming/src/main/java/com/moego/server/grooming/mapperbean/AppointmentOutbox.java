package com.moego.server.grooming.mapperbean;

import com.moego.idl.models.appointment.v1.OutboxSendStatus;
import com.moego.idl.models.event_bus.v1.EventType;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table appointment_outbox
 */
public class AppointmentOutbox {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_outbox.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   topic name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_outbox.topic
     *
     * @mbg.generated
     */
    private String topic;

    /**
     * Database Column Remarks:
     *   id for event record
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_outbox.event_id
     *
     * @mbg.generated
     */
    private String eventId;

    /**
     * Database Column Remarks:
     *   time for event record
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_outbox.event_time
     *
     * @mbg.generated
     */
    private LocalDateTime eventTime;

    /**
     * Database Column Remarks:
     *   key for event record
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_outbox.event_key
     *
     * @mbg.generated
     */
    private String eventKey;

    /**
     * Database Column Remarks:
     *   defined in com.moego.idl.models.event_bus.v1.EventType
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_outbox.event_type
     *
     * @mbg.generated
     */
    private EventType eventType;

    /**
     * Database Column Remarks:
     *   message push status.1 pending 2 sent 3 failed
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_outbox.status
     *
     * @mbg.generated
     */
    private OutboxSendStatus status;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_outbox.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     * Database Column Remarks:
     *   detail for event record
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_outbox.event_detail
     *
     * @mbg.generated
     */
    private String eventDetail;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_outbox.id
     *
     * @return the value of appointment_outbox.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_outbox.id
     *
     * @param id the value for appointment_outbox.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_outbox.topic
     *
     * @return the value of appointment_outbox.topic
     *
     * @mbg.generated
     */
    public String getTopic() {
        return topic;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_outbox.topic
     *
     * @param topic the value for appointment_outbox.topic
     *
     * @mbg.generated
     */
    public void setTopic(String topic) {
        this.topic = topic == null ? null : topic.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_outbox.event_id
     *
     * @return the value of appointment_outbox.event_id
     *
     * @mbg.generated
     */
    public String getEventId() {
        return eventId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_outbox.event_id
     *
     * @param eventId the value for appointment_outbox.event_id
     *
     * @mbg.generated
     */
    public void setEventId(String eventId) {
        this.eventId = eventId == null ? null : eventId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_outbox.event_time
     *
     * @return the value of appointment_outbox.event_time
     *
     * @mbg.generated
     */
    public LocalDateTime getEventTime() {
        return eventTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_outbox.event_time
     *
     * @param eventTime the value for appointment_outbox.event_time
     *
     * @mbg.generated
     */
    public void setEventTime(LocalDateTime eventTime) {
        this.eventTime = eventTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_outbox.event_key
     *
     * @return the value of appointment_outbox.event_key
     *
     * @mbg.generated
     */
    public String getEventKey() {
        return eventKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_outbox.event_key
     *
     * @param eventKey the value for appointment_outbox.event_key
     *
     * @mbg.generated
     */
    public void setEventKey(String eventKey) {
        this.eventKey = eventKey == null ? null : eventKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_outbox.event_type
     *
     * @return the value of appointment_outbox.event_type
     *
     * @mbg.generated
     */
    public EventType getEventType() {
        return eventType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_outbox.event_type
     *
     * @param eventType the value for appointment_outbox.event_type
     *
     * @mbg.generated
     */
    public void setEventType(EventType eventType) {
        this.eventType = eventType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_outbox.status
     *
     * @return the value of appointment_outbox.status
     *
     * @mbg.generated
     */
    public OutboxSendStatus getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_outbox.status
     *
     * @param status the value for appointment_outbox.status
     *
     * @mbg.generated
     */
    public void setStatus(OutboxSendStatus status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_outbox.created_at
     *
     * @return the value of appointment_outbox.created_at
     *
     * @mbg.generated
     */
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_outbox.created_at
     *
     * @param createdAt the value for appointment_outbox.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_outbox.event_detail
     *
     * @return the value of appointment_outbox.event_detail
     *
     * @mbg.generated
     */
    public String getEventDetail() {
        return eventDetail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_outbox.event_detail
     *
     * @param eventDetail the value for appointment_outbox.event_detail
     *
     * @mbg.generated
     */
    public void setEventDetail(String eventDetail) {
        this.eventDetail = eventDetail == null ? null : eventDetail.trim();
    }
}
