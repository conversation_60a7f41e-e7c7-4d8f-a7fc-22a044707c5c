package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import lombok.Data;
import org.apache.pulsar.shade.io.swagger.annotations.ApiModelProperty;

@Data
public class ServiceLocationOverrideDto {
    private Integer businessId;

    @ApiModelProperty("为 null 也是有效值，代表不覆盖，非 null 的有效值代表覆盖，price 和 duration 同理")
    private Integer taxId;

    private BigDecimal price;
    private Integer duration;
}
