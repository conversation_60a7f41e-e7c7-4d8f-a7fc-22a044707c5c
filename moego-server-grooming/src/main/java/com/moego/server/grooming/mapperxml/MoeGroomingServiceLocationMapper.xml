<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingServiceLocationMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="tax_id" jdbcType="INTEGER" property="taxId" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, business_id, service_id, tax_id, price, duration, is_deleted, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_grooming_service_location
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_service_location
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_location (company_id, business_id, service_id, 
      tax_id, price, duration, 
      is_deleted, create_time, update_time
      )
    values (#{companyId,jdbcType=BIGINT}, #{businessId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER}, 
      #{taxId,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, #{duration,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_location
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="taxId != null">
        tax_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="taxId != null">
        #{taxId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_location
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_location
    set company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      tax_id = #{taxId,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      duration = #{duration,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByServiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_location
    where company_id = #{companyId,jdbcType=BIGINT}
    and service_id = #{serviceId,jdbcType=INTEGER}
  </select>
  <select id="selectNoDeletedByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_location
    where company_id = #{companyId,jdbcType=BIGINT}
    and is_deleted = 0
  </select>
  <select id="selectWithDeletedByBidServiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from moe_grooming_service_location
    where company_id = #{companyId,jdbcType=BIGINT}
    and business_id = #{businessId,jdbcType=INTEGER}
    and service_id = #{serviceId,jdbcType=INTEGER}
  </select>
  <update id="updateSetDeletedByUniqueIndex">
    update moe_grooming_service_location
    set is_deleted = 1
    where company_id = #{companyId,jdbcType=BIGINT}
      and business_id = #{businessId,jdbcType=INTEGER}
      and service_id = #{serviceId,jdbcType=INTEGER}
  </update>

  <select id="selectNoDeletedByBusinessId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from moe_grooming_service_location
    where company_id = #{companyId,jdbcType=BIGINT}
    and business_id = #{businessId,jdbcType=INTEGER}
    and is_deleted = 0
  </select>
  <select id="selectNoDeletedByBusinessIdSids" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from moe_grooming_service_location
    where company_id = #{companyId,jdbcType=BIGINT}
    and business_id = #{businessId,jdbcType=INTEGER}
    and service_id in
    <foreach collection="serviceIds" item="serviceId" open="(" close=")" separator=",">
      #{serviceId}
    </foreach>
    and is_deleted = 0
  </select>
</mapper>