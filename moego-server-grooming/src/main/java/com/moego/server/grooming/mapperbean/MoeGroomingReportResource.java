package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_report_resource
 */
public class MoeGroomingReportResource {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_resource.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_resource.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   report id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_resource.report_id
     *
     * @mbg.generated
     */
    private Integer reportId;

    /**
     * Database Column Remarks:
     *   theme code
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_resource.theme_code
     *
     * @mbg.generated
     */
    private String themeCode;

    /**
     * Database Column Remarks:
     *   GR link cover url
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_resource.cover_url
     *
     * @mbg.generated
     */
    private String coverUrl;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_resource.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_resource.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_resource.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_resource.id
     *
     * @return the value of moe_grooming_report_resource.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_resource.id
     *
     * @param id the value for moe_grooming_report_resource.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_resource.business_id
     *
     * @return the value of moe_grooming_report_resource.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_resource.business_id
     *
     * @param businessId the value for moe_grooming_report_resource.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_resource.report_id
     *
     * @return the value of moe_grooming_report_resource.report_id
     *
     * @mbg.generated
     */
    public Integer getReportId() {
        return reportId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_resource.report_id
     *
     * @param reportId the value for moe_grooming_report_resource.report_id
     *
     * @mbg.generated
     */
    public void setReportId(Integer reportId) {
        this.reportId = reportId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_resource.theme_code
     *
     * @return the value of moe_grooming_report_resource.theme_code
     *
     * @mbg.generated
     */
    public String getThemeCode() {
        return themeCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_resource.theme_code
     *
     * @param themeCode the value for moe_grooming_report_resource.theme_code
     *
     * @mbg.generated
     */
    public void setThemeCode(String themeCode) {
        this.themeCode = themeCode == null ? null : themeCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_resource.cover_url
     *
     * @return the value of moe_grooming_report_resource.cover_url
     *
     * @mbg.generated
     */
    public String getCoverUrl() {
        return coverUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_resource.cover_url
     *
     * @param coverUrl the value for moe_grooming_report_resource.cover_url
     *
     * @mbg.generated
     */
    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl == null ? null : coverUrl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_resource.create_time
     *
     * @return the value of moe_grooming_report_resource.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_resource.create_time
     *
     * @param createTime the value for moe_grooming_report_resource.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_resource.update_time
     *
     * @return the value of moe_grooming_report_resource.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_resource.update_time
     *
     * @param updateTime the value for moe_grooming_report_resource.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_resource.company_id
     *
     * @return the value of moe_grooming_report_resource.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_resource.company_id
     *
     * @param companyId the value for moe_grooming_report_resource.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
