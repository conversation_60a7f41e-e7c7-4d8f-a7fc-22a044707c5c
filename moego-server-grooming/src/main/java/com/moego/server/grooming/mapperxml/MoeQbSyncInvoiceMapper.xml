<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeQbSyncInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeQbSyncInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="connect_id" jdbcType="INTEGER" property="connectId" />
    <result column="realm_id" jdbcType="VARCHAR" property="realmId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="qb_invoice_id" jdbcType="VARCHAR" property="qbInvoiceId" />
    <result column="qb_invoice_status" jdbcType="TINYINT" property="qbInvoiceStatus" />
    <result column="invoice_type" jdbcType="VARCHAR" property="invoiceType" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, connect_id, realm_id, grooming_id, invoice_id, qb_invoice_id, qb_invoice_status,
    invoice_type, total_amount, paid_amount, pay_status, update_time, create_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_qb_sync_invoice
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_sync_invoice
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_invoice (business_id, connect_id, realm_id,
      grooming_id, invoice_id, qb_invoice_id,
      qb_invoice_status, invoice_type, total_amount,
      paid_amount, pay_status, update_time,
      create_time, company_id)
    values (#{businessId,jdbcType=INTEGER}, #{connectId,jdbcType=INTEGER}, #{realmId,jdbcType=VARCHAR},
      #{groomingId,jdbcType=INTEGER}, #{invoiceId,jdbcType=INTEGER}, #{qbInvoiceId,jdbcType=VARCHAR},
      #{qbInvoiceStatus,jdbcType=TINYINT}, #{invoiceType,jdbcType=VARCHAR}, #{totalAmount,jdbcType=DECIMAL},
      #{paidAmount,jdbcType=DECIMAL}, #{payStatus,jdbcType=TINYINT}, #{updateTime,jdbcType=BIGINT},
      #{createTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="connectId != null">
        connect_id,
      </if>
      <if test="realmId != null">
        realm_id,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="qbInvoiceId != null">
        qb_invoice_id,
      </if>
      <if test="qbInvoiceStatus != null">
        qb_invoice_status,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="qbInvoiceId != null">
        #{qbInvoiceId,jdbcType=VARCHAR},
      </if>
      <if test="qbInvoiceStatus != null">
        #{qbInvoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_invoice
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        connect_id = #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        realm_id = #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="qbInvoiceId != null">
        qb_invoice_id = #{qbInvoiceId,jdbcType=VARCHAR},
      </if>
      <if test="qbInvoiceStatus != null">
        qb_invoice_status = #{qbInvoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_invoice
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      invoice_id = #{invoiceId,jdbcType=INTEGER},
      qb_invoice_id = #{qbInvoiceId,jdbcType=VARCHAR},
      qb_invoice_status = #{qbInvoiceStatus,jdbcType=TINYINT},
      invoice_type = #{invoiceType,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      paid_amount = #{paidAmount,jdbcType=DECIMAL},
      pay_status = #{payStatus,jdbcType=TINYINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByGroomingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_qb_sync_invoice
        where business_id = #{businessId} and
        realm_id = #{realmId} and
        grooming_id = #{groomingId}
        order by id desc limit 1
    </select>
    <select id="selectByInvoiceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_qb_sync_invoice
        where business_id = #{businessId} and
        invoice_id = #{invoiceId}
        order by id desc limit 1
    </select>

  <select id="selectByBusinessId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_qb_sync_invoice
    where business_id = #{businessId}
    and qb_invoice_status = 1
  </select>
    <select id="listInvoice" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
          from moe_qb_sync_invoice
          where business_id = #{params.businessId, jdbcType=INTEGER}
          <if test="params.invoiceId != null">
            and invoice_id = #{params.invoiceId,jdbcType=INTEGER}
          </if>
          <if test="params.startTime != null">
            and update_time &gt;= #{params.startTime, jdbcType=BIGINT}
          </if>
          <if test="params.endTime != null">
            and update_time &lt;= #{params.endTime, jdbcType=BIGINT}
          </if>
        order by update_time desc
        limit #{limit}
        offset #{offset}
    </select>

    <select id="countInvoice" resultType="java.lang.Integer">
        select count(*)
        from moe_qb_sync_invoice
        where business_id = #{businessId}
        <if test="invoiceId != null">
            and invoice_id = #{invoiceId}
        </if>
        <if test="startTime != null">
            and update_time &gt;= #{startTime, jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            and update_time &lt;= #{endTime, jdbcType=BIGINT}
        </if>
    </select>
</mapper>
