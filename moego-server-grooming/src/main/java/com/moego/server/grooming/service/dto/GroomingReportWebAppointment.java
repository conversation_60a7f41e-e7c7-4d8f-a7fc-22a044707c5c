package com.moego.server.grooming.service.dto;

import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.service.dto.report.ReportBaseAmountDTO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class GroomingReportWebAppointment extends ReportBaseAmountDTO {

    private List<ReportWebApptPetDetail> petDetails;

    private List<EvaluationServiceDetailDTO> evaluationDetails;

    private List<AppointmentNote> appointmentNotes;

    private Map<Integer, MoeGroomingInvoice> invoiceMap;
    private Map<Integer, List<ReportWebApptPetDetail>> invoiceIdToPetDetailsMap;

    private List<MoeGroomingInvoiceItem> invoiceItems;

    private List<String> serviceChargeList;

    private Integer id;

    private String orderId;

    private Integer businessId;

    private Integer customerId;

    private String appointmentDate;

    private Integer appointmentStartTime;

    private Integer appointmentEndTime;

    private Byte isWaitingList;

    private Integer moveWaitingBy;

    private Long confirmedTime;

    private Long checkInTime;

    private Long checkOutTime;

    private Long canceledTime;

    private Byte status;

    private Byte bookOnlineStatus;

    private Integer repeatId;

    private Byte isPaid;

    private String colorCode;

    private Byte noShow;

    private Long noShowBy;

    private Byte isPustNotification;

    private Byte cancelByType;

    private Integer cancelBy;

    private Byte confirmByType;

    private Integer confirmBy;

    private Integer createdById;

    private Byte outOfArea;

    private Long createTime;

    private Long updateTime;

    private Integer source;

    private Integer isBlock;

    private Integer isDeprecate;

    private BigDecimal noShowFee;

    private String oldAppointmentDate;

    private Integer oldAppointmentStartTime;

    private Integer oldAppointmentEndTime;

    private String type;

    private BigDecimal discountRate;

    private String discountType;

    private BigDecimal discountedSubTotalAmount;

    private BigDecimal tipsRate;

    private String tipsType;

    private Integer invoiceStatus;

    private Integer createBy;

    private Integer updateBy;

    private Integer serviceTypeInclude;
}
