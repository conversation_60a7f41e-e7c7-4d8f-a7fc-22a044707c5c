package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_service
 */
public class MoeGroomingService {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   类型id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.category_id
     *
     * @mbg.generated
     */
    private Integer categoryId;

    /**
     * Database Column Remarks:
     *   服务名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     * Database Column Remarks:
     *   描述
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     * Database Column Remarks:
     *   数据类型：1-主服务(service)；2-额外服务(addons)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     * Database Column Remarks:
     *   税费id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.tax_id
     *
     * @mbg.generated
     */
    private Integer taxId;

    /**
     * Database Column Remarks:
     *   服务价格
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.price
     *
     * @mbg.generated
     */
    private BigDecimal price;

    /**
     * Database Column Remarks:
     *   服务时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.duration
     *
     * @mbg.generated
     */
    private Integer duration;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.inactive
     *
     * @mbg.generated
     */
    private Byte inactive;

    /**
     * Database Column Remarks:
     *   排序值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     * Database Column Remarks:
     *   color code
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.color_code
     *
     * @mbg.generated
     */
    private String colorCode;

    /**
     * Database Column Remarks:
     *   1 正常 2删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   booking online 是否显示价格
     *   0 do not show price
     *   1 show fixd service price
     *   2 show price with "starting at"
     *   3 show price sa "Varies"
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.show_base_price
     *
     * @mbg.generated
     */
    private Byte showBasePrice;

    /**
     * Database Column Remarks:
     *   0-not  1-yes
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.book_online_available
     *
     * @mbg.generated
     */
    private Byte bookOnlineAvailable;

    /**
     * Database Column Remarks:
     *   是否全选staff
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.is_all_staff
     *
     * @mbg.generated
     */
    private Byte isAllStaff;

    /**
     * Database Column Remarks:
     *   filtered by breed, 0-all breed, 1-filter by selected breeds, see table moe_grooming_service_type_breed_binding
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.breed_filter
     *
     * @mbg.generated
     */
    private Byte breedFilter;

    /**
     * Database Column Remarks:
     *   filtered by weight, 0-all weight, 1-filter by weight range
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.weight_filter
     *
     * @mbg.generated
     */
    private Byte weightFilter;

    /**
     * Database Column Remarks:
     *   weight filter down limit
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.weight_down_limit
     *
     * @mbg.generated
     */
    private BigDecimal weightDownLimit;

    /**
     * Database Column Remarks:
     *   weight filter up limit
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.weight_up_limit
     *
     * @mbg.generated
     */
    private BigDecimal weightUpLimit;

    /**
     * Database Column Remarks:
     *   filtered by coat, 0-all coat, 1-filter by selected coat
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.coat_filter
     *
     * @mbg.generated
     */
    private Byte coatFilter;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.is_all_location
     *
     * @mbg.generated
     */
    private Byte isAllLocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.images
     *
     * @mbg.generated
     */
    private String images;

    /**
     * Database Column Remarks:
     *   1 - grooming, 2 - boarding, 3 - daycare
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.service_item_type
     *
     * @mbg.generated
     */
    private Integer serviceItemType;

    /**
     * Database Column Remarks:
     *   1 - per session, 2 - per night, 3 - per hour
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.price_unit
     *
     * @mbg.generated
     */
    private Integer priceUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.add_to_commission
     *
     * @mbg.generated
     */
    private Boolean addToCommission;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.can_tip
     *
     * @mbg.generated
     */
    private Boolean canTip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.require_dedicated_staff
     *
     * @mbg.generated
     */
    private Boolean requireDedicatedStaff;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.require_dedicated_lodging
     *
     * @mbg.generated
     */
    private Boolean requireDedicatedLodging;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.lodging_filter
     *
     * @mbg.generated
     */
    private Boolean lodgingFilter;

    /**
     * Database Column Remarks:
     *   allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.allowed_lodging_list
     *
     * @mbg.generated
     */
    private String allowedLodgingList;

    /**
     * Database Column Remarks:
     *   only for add-on
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.service_filter
     *
     * @mbg.generated
     */
    private Boolean serviceFilter;

    /**
     * Database Column Remarks:
     *   allowed pet size list, only when service_filter is true
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.allowed_pet_size_list
     *
     * @mbg.generated
     */
    private String allowedPetSizeList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.pet_size_filter
     *
     * @mbg.generated
     */
    private Boolean petSizeFilter;

    /**
     * Database Column Remarks:
     *   max duration in minutes, only for daycare
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service.max_duration
     *
     * @mbg.generated
     */
    private Integer maxDuration;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.id
     *
     * @return the value of moe_grooming_service.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.id
     *
     * @param id the value for moe_grooming_service.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.business_id
     *
     * @return the value of moe_grooming_service.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.business_id
     *
     * @param businessId the value for moe_grooming_service.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.category_id
     *
     * @return the value of moe_grooming_service.category_id
     *
     * @mbg.generated
     */
    public Integer getCategoryId() {
        return categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.category_id
     *
     * @param categoryId the value for moe_grooming_service.category_id
     *
     * @mbg.generated
     */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.name
     *
     * @return the value of moe_grooming_service.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.name
     *
     * @param name the value for moe_grooming_service.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.description
     *
     * @return the value of moe_grooming_service.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.description
     *
     * @param description the value for moe_grooming_service.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.type
     *
     * @return the value of moe_grooming_service.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.type
     *
     * @param type the value for moe_grooming_service.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.tax_id
     *
     * @return the value of moe_grooming_service.tax_id
     *
     * @mbg.generated
     */
    public Integer getTaxId() {
        return taxId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.tax_id
     *
     * @param taxId the value for moe_grooming_service.tax_id
     *
     * @mbg.generated
     */
    public void setTaxId(Integer taxId) {
        this.taxId = taxId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.price
     *
     * @return the value of moe_grooming_service.price
     *
     * @mbg.generated
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.price
     *
     * @param price the value for moe_grooming_service.price
     *
     * @mbg.generated
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.duration
     *
     * @return the value of moe_grooming_service.duration
     *
     * @mbg.generated
     */
    public Integer getDuration() {
        return duration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.duration
     *
     * @param duration the value for moe_grooming_service.duration
     *
     * @mbg.generated
     */
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.inactive
     *
     * @return the value of moe_grooming_service.inactive
     *
     * @mbg.generated
     */
    public Byte getInactive() {
        return inactive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.inactive
     *
     * @param inactive the value for moe_grooming_service.inactive
     *
     * @mbg.generated
     */
    public void setInactive(Byte inactive) {
        this.inactive = inactive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.sort
     *
     * @return the value of moe_grooming_service.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.sort
     *
     * @param sort the value for moe_grooming_service.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.color_code
     *
     * @return the value of moe_grooming_service.color_code
     *
     * @mbg.generated
     */
    public String getColorCode() {
        return colorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.color_code
     *
     * @param colorCode the value for moe_grooming_service.color_code
     *
     * @mbg.generated
     */
    public void setColorCode(String colorCode) {
        this.colorCode = colorCode == null ? null : colorCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.status
     *
     * @return the value of moe_grooming_service.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.status
     *
     * @param status the value for moe_grooming_service.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.create_time
     *
     * @return the value of moe_grooming_service.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.create_time
     *
     * @param createTime the value for moe_grooming_service.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.update_time
     *
     * @return the value of moe_grooming_service.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.update_time
     *
     * @param updateTime the value for moe_grooming_service.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.show_base_price
     *
     * @return the value of moe_grooming_service.show_base_price
     *
     * @mbg.generated
     */
    public Byte getShowBasePrice() {
        return showBasePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.show_base_price
     *
     * @param showBasePrice the value for moe_grooming_service.show_base_price
     *
     * @mbg.generated
     */
    public void setShowBasePrice(Byte showBasePrice) {
        this.showBasePrice = showBasePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.book_online_available
     *
     * @return the value of moe_grooming_service.book_online_available
     *
     * @mbg.generated
     */
    public Byte getBookOnlineAvailable() {
        return bookOnlineAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.book_online_available
     *
     * @param bookOnlineAvailable the value for moe_grooming_service.book_online_available
     *
     * @mbg.generated
     */
    public void setBookOnlineAvailable(Byte bookOnlineAvailable) {
        this.bookOnlineAvailable = bookOnlineAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.is_all_staff
     *
     * @return the value of moe_grooming_service.is_all_staff
     *
     * @mbg.generated
     */
    public Byte getIsAllStaff() {
        return isAllStaff;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.is_all_staff
     *
     * @param isAllStaff the value for moe_grooming_service.is_all_staff
     *
     * @mbg.generated
     */
    public void setIsAllStaff(Byte isAllStaff) {
        this.isAllStaff = isAllStaff;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.breed_filter
     *
     * @return the value of moe_grooming_service.breed_filter
     *
     * @mbg.generated
     */
    public Byte getBreedFilter() {
        return breedFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.breed_filter
     *
     * @param breedFilter the value for moe_grooming_service.breed_filter
     *
     * @mbg.generated
     */
    public void setBreedFilter(Byte breedFilter) {
        this.breedFilter = breedFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.weight_filter
     *
     * @return the value of moe_grooming_service.weight_filter
     *
     * @mbg.generated
     */
    public Byte getWeightFilter() {
        return weightFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.weight_filter
     *
     * @param weightFilter the value for moe_grooming_service.weight_filter
     *
     * @mbg.generated
     */
    public void setWeightFilter(Byte weightFilter) {
        this.weightFilter = weightFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.weight_down_limit
     *
     * @return the value of moe_grooming_service.weight_down_limit
     *
     * @mbg.generated
     */
    public BigDecimal getWeightDownLimit() {
        return weightDownLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.weight_down_limit
     *
     * @param weightDownLimit the value for moe_grooming_service.weight_down_limit
     *
     * @mbg.generated
     */
    public void setWeightDownLimit(BigDecimal weightDownLimit) {
        this.weightDownLimit = weightDownLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.weight_up_limit
     *
     * @return the value of moe_grooming_service.weight_up_limit
     *
     * @mbg.generated
     */
    public BigDecimal getWeightUpLimit() {
        return weightUpLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.weight_up_limit
     *
     * @param weightUpLimit the value for moe_grooming_service.weight_up_limit
     *
     * @mbg.generated
     */
    public void setWeightUpLimit(BigDecimal weightUpLimit) {
        this.weightUpLimit = weightUpLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.coat_filter
     *
     * @return the value of moe_grooming_service.coat_filter
     *
     * @mbg.generated
     */
    public Byte getCoatFilter() {
        return coatFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.coat_filter
     *
     * @param coatFilter the value for moe_grooming_service.coat_filter
     *
     * @mbg.generated
     */
    public void setCoatFilter(Byte coatFilter) {
        this.coatFilter = coatFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.company_id
     *
     * @return the value of moe_grooming_service.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.company_id
     *
     * @param companyId the value for moe_grooming_service.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.is_all_location
     *
     * @return the value of moe_grooming_service.is_all_location
     *
     * @mbg.generated
     */
    public Byte getIsAllLocation() {
        return isAllLocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.is_all_location
     *
     * @param isAllLocation the value for moe_grooming_service.is_all_location
     *
     * @mbg.generated
     */
    public void setIsAllLocation(Byte isAllLocation) {
        this.isAllLocation = isAllLocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.images
     *
     * @return the value of moe_grooming_service.images
     *
     * @mbg.generated
     */
    public String getImages() {
        return images;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.images
     *
     * @param images the value for moe_grooming_service.images
     *
     * @mbg.generated
     */
    public void setImages(String images) {
        this.images = images == null ? null : images.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.service_item_type
     *
     * @return the value of moe_grooming_service.service_item_type
     *
     * @mbg.generated
     */
    public Integer getServiceItemType() {
        return serviceItemType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.service_item_type
     *
     * @param serviceItemType the value for moe_grooming_service.service_item_type
     *
     * @mbg.generated
     */
    public void setServiceItemType(Integer serviceItemType) {
        this.serviceItemType = serviceItemType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.price_unit
     *
     * @return the value of moe_grooming_service.price_unit
     *
     * @mbg.generated
     */
    public Integer getPriceUnit() {
        return priceUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.price_unit
     *
     * @param priceUnit the value for moe_grooming_service.price_unit
     *
     * @mbg.generated
     */
    public void setPriceUnit(Integer priceUnit) {
        this.priceUnit = priceUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.add_to_commission
     *
     * @return the value of moe_grooming_service.add_to_commission
     *
     * @mbg.generated
     */
    public Boolean getAddToCommission() {
        return addToCommission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.add_to_commission
     *
     * @param addToCommission the value for moe_grooming_service.add_to_commission
     *
     * @mbg.generated
     */
    public void setAddToCommission(Boolean addToCommission) {
        this.addToCommission = addToCommission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.can_tip
     *
     * @return the value of moe_grooming_service.can_tip
     *
     * @mbg.generated
     */
    public Boolean getCanTip() {
        return canTip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.can_tip
     *
     * @param canTip the value for moe_grooming_service.can_tip
     *
     * @mbg.generated
     */
    public void setCanTip(Boolean canTip) {
        this.canTip = canTip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.require_dedicated_staff
     *
     * @return the value of moe_grooming_service.require_dedicated_staff
     *
     * @mbg.generated
     */
    public Boolean getRequireDedicatedStaff() {
        return requireDedicatedStaff;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.require_dedicated_staff
     *
     * @param requireDedicatedStaff the value for moe_grooming_service.require_dedicated_staff
     *
     * @mbg.generated
     */
    public void setRequireDedicatedStaff(Boolean requireDedicatedStaff) {
        this.requireDedicatedStaff = requireDedicatedStaff;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.require_dedicated_lodging
     *
     * @return the value of moe_grooming_service.require_dedicated_lodging
     *
     * @mbg.generated
     */
    public Boolean getRequireDedicatedLodging() {
        return requireDedicatedLodging;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.require_dedicated_lodging
     *
     * @param requireDedicatedLodging the value for moe_grooming_service.require_dedicated_lodging
     *
     * @mbg.generated
     */
    public void setRequireDedicatedLodging(Boolean requireDedicatedLodging) {
        this.requireDedicatedLodging = requireDedicatedLodging;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.lodging_filter
     *
     * @return the value of moe_grooming_service.lodging_filter
     *
     * @mbg.generated
     */
    public Boolean getLodgingFilter() {
        return lodgingFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.lodging_filter
     *
     * @param lodgingFilter the value for moe_grooming_service.lodging_filter
     *
     * @mbg.generated
     */
    public void setLodgingFilter(Boolean lodgingFilter) {
        this.lodgingFilter = lodgingFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.allowed_lodging_list
     *
     * @return the value of moe_grooming_service.allowed_lodging_list
     *
     * @mbg.generated
     */
    public String getAllowedLodgingList() {
        return allowedLodgingList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.allowed_lodging_list
     *
     * @param allowedLodgingList the value for moe_grooming_service.allowed_lodging_list
     *
     * @mbg.generated
     */
    public void setAllowedLodgingList(String allowedLodgingList) {
        this.allowedLodgingList = allowedLodgingList == null ? null : allowedLodgingList.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.service_filter
     *
     * @return the value of moe_grooming_service.service_filter
     *
     * @mbg.generated
     */
    public Boolean getServiceFilter() {
        return serviceFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.service_filter
     *
     * @param serviceFilter the value for moe_grooming_service.service_filter
     *
     * @mbg.generated
     */
    public void setServiceFilter(Boolean serviceFilter) {
        this.serviceFilter = serviceFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.allowed_pet_size_list
     *
     * @return the value of moe_grooming_service.allowed_pet_size_list
     *
     * @mbg.generated
     */
    public String getAllowedPetSizeList() {
        return allowedPetSizeList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.allowed_pet_size_list
     *
     * @param allowedPetSizeList the value for moe_grooming_service.allowed_pet_size_list
     *
     * @mbg.generated
     */
    public void setAllowedPetSizeList(String allowedPetSizeList) {
        this.allowedPetSizeList = allowedPetSizeList == null ? null : allowedPetSizeList.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.pet_size_filter
     *
     * @return the value of moe_grooming_service.pet_size_filter
     *
     * @mbg.generated
     */
    public Boolean getPetSizeFilter() {
        return petSizeFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.pet_size_filter
     *
     * @param petSizeFilter the value for moe_grooming_service.pet_size_filter
     *
     * @mbg.generated
     */
    public void setPetSizeFilter(Boolean petSizeFilter) {
        this.petSizeFilter = petSizeFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service.max_duration
     *
     * @return the value of moe_grooming_service.max_duration
     *
     * @mbg.generated
     */
    public Integer getMaxDuration() {
        return maxDuration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service.max_duration
     *
     * @param maxDuration the value for moe_grooming_service.max_duration
     *
     * @mbg.generated
     */
    public void setMaxDuration(Integer maxDuration) {
        this.maxDuration = maxDuration;
    }
}
