package com.moego.server.grooming.service.storage;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.server.grooming.enums.PetDetailStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperationExample;
import com.moego.server.grooming.service.MoePetDetailService;
import com.moego.server.grooming.service.WaitListService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 这个类用于处理 appointment 的数据库写入逻辑，主要是解决事务性写入的问题
 */
@Service
public class AppointmentStorageService {
    @Autowired
    private MoePetDetailService moePetDetailService;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private PetDetailMapperProxy moeGroomingPetDetailMapper;

    @Autowired
    private MoeGroomingServiceOperationMapper moeGroomingServiceOperationMapper;

    @Autowired
    private WaitListService waitListService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Transactional
    public void updatePetDetails(
            MoeGroomingAppointment appointment,
            List<MoeGroomingPetDetail> petDetails,
            Map<Pair<Integer, Integer>, List<MoeGroomingServiceOperation>> serviceOperations) {
        // Step 1，删除旧的 pet detail & operation
        moeGroomingPetDetailMapper.deleteByAppointmentId(appointment.getId());
        moeGroomingServiceOperationMapper.deleteByGroomingId(appointment.getId());

        // Step 2，插入新的 pet detail & operation
        petDetails.forEach(moeGroomingPetDetailMapper::insertSelective);
        Map<Pair<Integer, Integer>, Integer> petDetailIdMap = petDetails.stream()
                .map(petDetail -> Pair.of(Pair.of(petDetail.getPetId(), petDetail.getServiceId()), petDetail.getId()))
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue));

        if (!serviceOperations.isEmpty()) {
            serviceOperations.forEach((k, v) -> v.forEach(serviceOperation -> {
                serviceOperation.setBusinessId(appointment.getBusinessId());
                serviceOperation.setCompanyId(appointment.getCompanyId());
                serviceOperation.setGroomingId(appointment.getId());
                serviceOperation.setGroomingServiceId(petDetailIdMap.get(k));
            }));
            moeGroomingServiceOperationMapper.batchInsert(
                    serviceOperations.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        }

        // Step 3, 修改 appointment
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointment);
    }

    @Transactional
    public void addAppointmentPet(
            MoeGroomingAppointment appointment,
            List<MoeGroomingPetDetail> newPetDetails,
            Map<Pair<Integer, Integer>, List<MoeGroomingServiceOperation>> serviceOperations) {
        newPetDetails.forEach(moeGroomingPetDetailMapper::insertSelective);
        Map<Pair<Integer, Integer>, Integer> petDetailIdMap = newPetDetails.stream()
                .map(petDetail -> Pair.of(Pair.of(petDetail.getPetId(), petDetail.getServiceId()), petDetail.getId()))
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue));

        if (!serviceOperations.isEmpty()) {
            serviceOperations.forEach((k, v) -> v.forEach(serviceOperation -> {
                serviceOperation.setBusinessId(appointment.getBusinessId());
                serviceOperation.setCompanyId(appointment.getCompanyId());
                serviceOperation.setGroomingId(appointment.getId());
                serviceOperation.setGroomingServiceId(petDetailIdMap.get(k));
            }));
            moeGroomingServiceOperationMapper.batchInsert(
                    serviceOperations.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        }

        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointment);
    }

    @Transactional
    public void editAppointmentPet(
            MoeGroomingAppointment appointment,
            Integer originPetId,
            List<MoeGroomingPetDetail> newPetDetails,
            Map<Pair<Integer, Integer>, List<MoeGroomingServiceOperation>> newServiceOperations,
            List<MoeGroomingPetDetail> petDetailsToUpdate,
            List<MoeGroomingServiceOperation> operationsToUpdate) {
        // 涉及到了 Pet 切换，将旧的数据删除
        MoeGroomingPetDetailExample example = new MoeGroomingPetDetailExample();
        example.createCriteria().andGroomingIdEqualTo(appointment.getId()).andPetIdEqualTo(originPetId);
        MoeGroomingPetDetail petDetailToUpdate = new MoeGroomingPetDetail();
        petDetailToUpdate.setStatus(
                PetDetailStatusEnum.DELETED_DUE_TO_MODIFY.getValue().byteValue());
        petDetailToUpdate.setUpdateTime(CommonUtil.get10Timestamp());
        moeGroomingPetDetailMapper.updateByExampleSelective(petDetailToUpdate, example);

        MoeGroomingServiceOperationExample operationExample = new MoeGroomingServiceOperationExample();
        operationExample
                .createCriteria()
                .andGroomingIdEqualTo(appointment.getId())
                .andPetIdEqualTo(originPetId);
        moeGroomingServiceOperationMapper.deleteByExample(operationExample);

        newPetDetails.forEach(moeGroomingPetDetailMapper::insertSelective);
        Map<Pair<Integer, Integer>, Integer> petDetailIdMap = newPetDetails.stream()
                .map(petDetail -> Pair.of(Pair.of(petDetail.getPetId(), petDetail.getServiceId()), petDetail.getId()))
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue));

        if (!newServiceOperations.isEmpty()) {
            newServiceOperations.forEach((k, v) -> v.forEach(serviceOperation -> {
                serviceOperation.setBusinessId(appointment.getBusinessId());
                serviceOperation.setCompanyId(appointment.getCompanyId());
                serviceOperation.setGroomingId(appointment.getId());
                serviceOperation.setGroomingServiceId(petDetailIdMap.get(k));
            }));
            moeGroomingServiceOperationMapper.batchInsert(
                    newServiceOperations.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        }

        petDetailsToUpdate.forEach(moeGroomingPetDetailMapper::updateByPrimaryKeySelective);
        operationsToUpdate.forEach(moeGroomingServiceOperationMapper::updateByPrimaryKeySelective);

        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointment);
    }

    @Transactional
    public void insertAppointment(
            MoeGroomingAppointment appointment,
            List<MoeGroomingPetDetail> petDetails,
            Map<Pair<Integer, Integer>, List<MoeGroomingServiceOperation>> serviceOperations) {
        // Step 1，插入 appointment
        moeGroomingAppointmentMapper.insertSelective(appointment);

        // Step 2，插入 pet detail
        petDetails.forEach(petDetail -> {
            petDetail.setGroomingId(appointment.getId());
            petDetail.setStartDate(appointment.getAppointmentDate());
            petDetail.setEndDate(appointment.getAppointmentDate());
            moeGroomingPetDetailMapper.insertSelective(petDetail);
        });

        // Step 3，插入 operation
        if (!serviceOperations.isEmpty()) {
            Map<Pair<Integer, Integer>, Integer> petDetailIdMap = petDetails.stream()
                    .collect(java.util.stream.Collectors.toMap(
                            petDetail -> Pair.of(petDetail.getPetId(), petDetail.getServiceId()),
                            MoeGroomingPetDetail::getId));

            serviceOperations.forEach((k, v) -> v.forEach(serviceOperation -> {
                serviceOperation.setBusinessId(appointment.getBusinessId());
                serviceOperation.setCompanyId(appointment.getCompanyId());
                serviceOperation.setGroomingId(appointment.getId());
                serviceOperation.setGroomingServiceId(petDetailIdMap.get(k));
            }));
            moeGroomingServiceOperationMapper.batchInsert(
                    serviceOperations.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        }
    }

    @Transactional
    public void deleteAppointmentList(Integer businessId, List<Integer> appointmentIds, String timeZone) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return;
        }
        moeGroomingAppointmentMapper.deleteAppointments(appointmentIds, businessId);
        moePetDetailService.deleteByAppointIds(appointmentIds);
        waitListService.deleteByAppointmentId(
                null,
                businessId.longValue(),
                appointmentIds.stream().map(Integer::longValue).toList(),
                timeZone);
    }

    @Transactional
    public void updateBlockTime(
            Integer appointmentId, Integer staffId, String appointmentDate, Integer startTime, Integer endTime) {
        MoeGroomingAppointment appointmentToUpdate = new MoeGroomingAppointment();
        appointmentToUpdate.setAppointmentDate(appointmentDate);
        appointmentToUpdate.setAppointmentEndDate(
                appointmentDate); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
        appointmentToUpdate.setAppointmentStartTime(startTime);
        appointmentToUpdate.setAppointmentEndTime(endTime);
        appointmentToUpdate.setUpdateTime(DateUtil.get10Timestamp());
        MoeGroomingAppointmentExample example = new MoeGroomingAppointmentExample();
        example.createCriteria()
                .andIsBlockEqualTo(GroomingAppointmentEnum.IS_BLOCK_TRUE)
                .andIdEqualTo(appointmentId);
        moeGroomingAppointmentMapper.updateByExampleSelective(appointmentToUpdate, example);

        MoeGroomingPetDetail petDetailToUpdate = new MoeGroomingPetDetail();
        petDetailToUpdate.setStartTime(startTime.longValue());
        petDetailToUpdate.setEndTime(endTime.longValue());
        petDetailToUpdate.setServiceTime(endTime - startTime);
        petDetailToUpdate.setStartDate(appointmentDate);
        petDetailToUpdate.setEndDate(appointmentDate);
        petDetailToUpdate.setStaffId(staffId);
        petDetailToUpdate.setUpdateTime(DateUtil.get10Timestamp());
        MoeGroomingPetDetailExample petDetailExample = new MoeGroomingPetDetailExample();
        petDetailExample.createCriteria().andGroomingIdEqualTo(appointmentId);
        moeGroomingPetDetailMapper.updateByExampleSelective(petDetailToUpdate, petDetailExample);
    }

    @Transactional
    public void updateMultiPetStartTime(
            MoeGroomingAppointment appointment,
            List<MoeGroomingPetDetail> petDetails,
            List<MoeGroomingServiceOperation> serviceOperations) {
        MoeGroomingAppointment appointmentToUpdate = new MoeGroomingAppointment();
        appointmentToUpdate.setId(appointment.getId());
        appointmentToUpdate.setAppointmentEndTime(appointment.getAppointmentEndTime());
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointmentToUpdate);

        petDetails.stream()
                .map(p -> {
                    MoeGroomingPetDetail petDetailToUpdate = new MoeGroomingPetDetail();
                    petDetailToUpdate.setId(p.getId());
                    petDetailToUpdate.setStartTime(p.getStartTime());
                    petDetailToUpdate.setEndTime(p.getEndTime());
                    return petDetailToUpdate;
                })
                .forEach(moeGroomingPetDetailMapper::updateByPrimaryKeySelective);

        if (CollectionUtils.isNotEmpty(serviceOperations)) {
            serviceOperations.stream()
                    .map(s -> {
                        MoeGroomingServiceOperation serviceOperationToUpdate = new MoeGroomingServiceOperation();
                        serviceOperationToUpdate.setId(s.getId());
                        serviceOperationToUpdate.setStartTime(s.getStartTime());
                        return serviceOperationToUpdate;
                    })
                    .forEach(moeGroomingServiceOperationMapper::updateByPrimaryKeySelective);
        }
    }

    @Transactional
    public void updateAppointment(
            MoeGroomingAppointment appointmentToUpdate,
            List<MoeGroomingPetDetail> petDetailListToUpdate,
            List<MoeGroomingServiceOperation> serviceOperationListToUpdate) {
        // Step 1，更新 appointment
        if (Objects.nonNull(appointmentToUpdate)) {
            moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointmentToUpdate);
        }

        // Step 2，更新 pet detail
        if (CollectionUtils.isNotEmpty(petDetailListToUpdate)) {
            petDetailListToUpdate.forEach(moeGroomingPetDetailMapper::updateByPrimaryKeySelective);
        }

        // Step 3，更新 operation
        if (CollectionUtils.isNotEmpty(serviceOperationListToUpdate)) {
            serviceOperationListToUpdate.forEach(moeGroomingServiceOperationMapper::updateByPrimaryKeySelective);
        }
    }
}
