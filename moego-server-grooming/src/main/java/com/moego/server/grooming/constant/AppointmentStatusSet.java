package com.moego.server.grooming.constant;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import java.util.List;

public interface AppointmentStatusSet {
    List<AppointmentStatusEnum> ACTIVE_STATUS_SET = List.of(
            AppointmentStatusEnum.UNCONFIRMED,
            AppointmentStatusEnum.CONFIRMED,
            AppointmentStatusEnum.READY,
            AppointmentStatusEnum.CHECK_IN,
            AppointmentStatusEnum.FINISHED);

    List<AppointmentStatusEnum> IN_PROGRESS_STATUS_SET = List.of(
            AppointmentStatusEnum.UNCONFIRMED,
            AppointmentStatusEnum.CONFIRMED,
            AppointmentStatusEnum.READY,
            AppointmentStatusEnum.CHECK_IN);
}
