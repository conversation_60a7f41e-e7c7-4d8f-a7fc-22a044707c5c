<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeQbConnectMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeQbConnect">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="realm_id" jdbcType="VARCHAR" property="realmId" />
    <result column="access_token" jdbcType="VARCHAR" property="accessToken" />
    <result column="refresh_token" jdbcType="VARCHAR" property="refreshToken" />
    <result column="connect_status" jdbcType="TINYINT" property="connectStatus" />
    <result column="token_expired_time" jdbcType="BIGINT" property="tokenExpiredTime" />
    <result column="connect_email" jdbcType="VARCHAR" property="connectEmail" />
    <result column="connect_company_name" jdbcType="VARCHAR" property="connectCompanyName" />
    <result column="connect_sub" jdbcType="VARCHAR" property="connectSub" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="state" jdbcType="VARCHAR" property="state" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, realm_id, access_token, refresh_token, connect_status, token_expired_time, 
    connect_email, connect_company_name, connect_sub, account_name, account_id, create_time, 
    update_time, company_id, state
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbConnectExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_qb_connect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_qb_connect
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_connect
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbConnectExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_connect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeQbConnect">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_connect (business_id, realm_id, access_token, 
      refresh_token, connect_status, token_expired_time, 
      connect_email, connect_company_name, connect_sub, 
      account_name, account_id, create_time, 
      update_time, company_id, state
      )
    values (#{businessId,jdbcType=INTEGER}, #{realmId,jdbcType=VARCHAR}, #{accessToken,jdbcType=VARCHAR}, 
      #{refreshToken,jdbcType=VARCHAR}, #{connectStatus,jdbcType=TINYINT}, #{tokenExpiredTime,jdbcType=BIGINT}, 
      #{connectEmail,jdbcType=VARCHAR}, #{connectCompanyName,jdbcType=VARCHAR}, #{connectSub,jdbcType=VARCHAR}, 
      #{accountName,jdbcType=VARCHAR}, #{accountId,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{state,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbConnect">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_connect
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="realmId != null">
        realm_id,
      </if>
      <if test="accessToken != null">
        access_token,
      </if>
      <if test="refreshToken != null">
        refresh_token,
      </if>
      <if test="connectStatus != null">
        connect_status,
      </if>
      <if test="tokenExpiredTime != null">
        token_expired_time,
      </if>
      <if test="connectEmail != null">
        connect_email,
      </if>
      <if test="connectCompanyName != null">
        connect_company_name,
      </if>
      <if test="connectSub != null">
        connect_sub,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="state != null">
        state,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="refreshToken != null">
        #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="connectStatus != null">
        #{connectStatus,jdbcType=TINYINT},
      </if>
      <if test="tokenExpiredTime != null">
        #{tokenExpiredTime,jdbcType=BIGINT},
      </if>
      <if test="connectEmail != null">
        #{connectEmail,jdbcType=VARCHAR},
      </if>
      <if test="connectCompanyName != null">
        #{connectCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="connectSub != null">
        #{connectSub,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbConnectExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_qb_connect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_connect
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.realmId != null">
        realm_id = #{record.realmId,jdbcType=VARCHAR},
      </if>
      <if test="record.accessToken != null">
        access_token = #{record.accessToken,jdbcType=VARCHAR},
      </if>
      <if test="record.refreshToken != null">
        refresh_token = #{record.refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="record.connectStatus != null">
        connect_status = #{record.connectStatus,jdbcType=TINYINT},
      </if>
      <if test="record.tokenExpiredTime != null">
        token_expired_time = #{record.tokenExpiredTime,jdbcType=BIGINT},
      </if>
      <if test="record.connectEmail != null">
        connect_email = #{record.connectEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.connectCompanyName != null">
        connect_company_name = #{record.connectCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.connectSub != null">
        connect_sub = #{record.connectSub,jdbcType=VARCHAR},
      </if>
      <if test="record.accountName != null">
        account_name = #{record.accountName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_connect
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      realm_id = #{record.realmId,jdbcType=VARCHAR},
      access_token = #{record.accessToken,jdbcType=VARCHAR},
      refresh_token = #{record.refreshToken,jdbcType=VARCHAR},
      connect_status = #{record.connectStatus,jdbcType=TINYINT},
      token_expired_time = #{record.tokenExpiredTime,jdbcType=BIGINT},
      connect_email = #{record.connectEmail,jdbcType=VARCHAR},
      connect_company_name = #{record.connectCompanyName,jdbcType=VARCHAR},
      connect_sub = #{record.connectSub,jdbcType=VARCHAR},
      account_name = #{record.accountName,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      state = #{record.state,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbConnect">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_connect
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        realm_id = #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        access_token = #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="refreshToken != null">
        refresh_token = #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="connectStatus != null">
        connect_status = #{connectStatus,jdbcType=TINYINT},
      </if>
      <if test="tokenExpiredTime != null">
        token_expired_time = #{tokenExpiredTime,jdbcType=BIGINT},
      </if>
      <if test="connectEmail != null">
        connect_email = #{connectEmail,jdbcType=VARCHAR},
      </if>
      <if test="connectCompanyName != null">
        connect_company_name = #{connectCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="connectSub != null">
        connect_sub = #{connectSub,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeQbConnect">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_connect
    set business_id = #{businessId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      access_token = #{accessToken,jdbcType=VARCHAR},
      refresh_token = #{refreshToken,jdbcType=VARCHAR},
      connect_status = #{connectStatus,jdbcType=TINYINT},
      token_expired_time = #{tokenExpiredTime,jdbcType=BIGINT},
      connect_email = #{connectEmail,jdbcType=VARCHAR},
      connect_company_name = #{connectCompanyName,jdbcType=VARCHAR},
      connect_sub = #{connectSub,jdbcType=VARCHAR},
      account_name = #{accountName,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      state = #{state,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>


    <select id="selectByBusinessId" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from moe_qb_connect
        where business_id = #{businessId,jdbcType=INTEGER}
        order by id desc
    </select>

    <select id="selectByRealmId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_qb_connect
        where id != #{connectId,jdbcType=INTEGER}
        and realm_id = #{realmId}
        and connect_status = 1
        order by id desc
    </select>

    <update id="updateCancelByRealmId">
        update moe_qb_connect
        set
        connect_status = 2,
        update_time = #{updateTime,jdbcType=BIGINT}
        where realm_id = #{realmId}
        and connect_status = 1
    </update>
</mapper>