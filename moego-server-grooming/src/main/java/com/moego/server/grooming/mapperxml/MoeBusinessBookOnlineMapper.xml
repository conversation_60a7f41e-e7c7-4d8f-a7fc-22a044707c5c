<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBusinessBookOnline">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="is_enable" jdbcType="TINYINT" property="isEnable" />
    <result column="max_available_dist" jdbcType="INTEGER" property="maxAvailableDist" />
    <result column="max_available_time" jdbcType="INTEGER" property="maxAvailableTime" />
    <result column="soonest_available" jdbcType="INTEGER" property="soonestAvailable" />
    <result column="farest_available" jdbcType="INTEGER" property="farestAvailable" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="is_require_agreement" jdbcType="TINYINT" property="isRequireAgreement" />
    <result column="zip_code" jdbcType="VARCHAR" property="zipCode" />
    <result column="place_name" jdbcType="VARCHAR" property="placeName" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="state_abbreviation" jdbcType="VARCHAR" property="stateAbbreviation" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="is_need_address" jdbcType="TINYINT" property="isNeedAddress" />
    <result column="is_need_select_time" jdbcType="TINYINT" property="isNeedSelectTime" />
    <result column="fake_it" jdbcType="TINYINT" property="fakeIt" />
    <result column="enable_no_show_fee" jdbcType="TINYINT" property="enableNoShowFee" />
    <result column="no_show_fee" jdbcType="DECIMAL" property="noShowFee" />
    <result column="appointment_interval" jdbcType="INTEGER" property="appointmentInterval" />
    <result column="timeslot_mins" jdbcType="INTEGER" property="timeslotMins" />
    <result column="timeslot_format" jdbcType="TINYINT" property="timeslotFormat" />
    <result column="accept_client" jdbcType="TINYINT" property="acceptClient" />
    <result column="auto_move_wait" jdbcType="TINYINT" property="autoMoveWait" />
    <result column="service_area_enable" jdbcType="TINYINT" property="serviceAreaEnable" />
    <result column="weight_limit_notify" jdbcType="TINYINT" property="weightLimitNotify" />
    <result column="weight_limit" jdbcType="INTEGER" property="weightLimit" />
    <result column="over_limit_tips" jdbcType="VARCHAR" property="overLimitTips" />
    <result column="need_within_area" jdbcType="TINYINT" property="needWithinArea" />
    <result column="is_by_zipcode" jdbcType="TINYINT" property="isByZipcode" />
    <result column="is_by_radius" jdbcType="TINYINT" property="isByRadius" />
    <result column="setting_location" jdbcType="VARCHAR" property="settingLocation" />
    <result column="setting_lat" jdbcType="VARCHAR" property="settingLat" />
    <result column="setting_lng" jdbcType="VARCHAR" property="settingLng" />
    <result column="is_check_existing_client" jdbcType="TINYINT" property="isCheckExistingClient" />
    <result column="is_redirect" jdbcType="TINYINT" property="isRedirect" />
    <result column="auto_accept" jdbcType="TINYINT" property="autoAccept" />
    <result column="show_one_available_time" jdbcType="TINYINT" property="showOneAvailableTime" />
    <result column="smart_schedule_enable" jdbcType="TINYINT" property="smartScheduleEnable" />
    <result column="smart_schedule_max_dist" jdbcType="INTEGER" property="smartScheduleMaxDist" />
    <result column="smart_schedule_max_time" jdbcType="INTEGER" property="smartScheduleMaxTime" />
    <result column="zip_codes" jdbcType="VARCHAR" property="zipCodes" />
    <result column="service_areas" jdbcType="CHAR" property="serviceAreas" typeHandler="com.moego.server.grooming.mapper.typehandler.IntListTypeHandler" />
    <result column="book_online_name" jdbcType="VARCHAR" property="bookOnlineName" />
    <result column="allowed_simplify_submit" jdbcType="TINYINT" property="allowedSimplifySubmit" />
    <result column="available_time_type" jdbcType="TINYINT" property="availableTimeType" />
    <result column="by_slot_timeslot_format" jdbcType="TINYINT" property="bySlotTimeslotFormat" />
    <result column="by_slot_timeslot_mins" jdbcType="INTEGER" property="bySlotTimeslotMins" />
    <result column="by_slot_soonest_available" jdbcType="INTEGER" property="bySlotSoonestAvailable" />
    <result column="by_slot_farthest_available" jdbcType="INTEGER" property="bySlotFarthestAvailable" />
    <result column="service_filter" jdbcType="TINYINT" property="serviceFilter" />
    <result column="is_show_categories" jdbcType="BIT" property="isShowCategories" />
    <result column="prepay_type" jdbcType="TINYINT" property="prepayType" />
    <result column="prepay_tip_enable" jdbcType="TINYINT" property="prepayTipEnable" />
    <result column="deposit_type" jdbcType="TINYINT" property="depositType" />
    <result column="deposit_percentage" jdbcType="INTEGER" property="depositPercentage" />
    <result column="deposit_amount" jdbcType="DECIMAL" property="depositAmount" />
    <result column="use_version" jdbcType="TINYINT" property="useVersion" />
    <result column="pre_auth_tip_enable" jdbcType="TINYINT" property="preAuthTipEnable" />
    <result column="auto_refund_deposit" jdbcType="TINYINT" property="autoRefundDeposit" />
    <result column="request_submitted_auto_type" jdbcType="VARCHAR" property="requestSubmittedAutoType" />
    <result column="display_staff_selection_page" jdbcType="BIT" property="displayStaffSelectionPage" />
    <result column="arrival_window_before_min" jdbcType="INTEGER" property="arrivalWindowBeforeMin" />
    <result column="arrival_window_after_min" jdbcType="INTEGER" property="arrivalWindowAfterMin" />
    <result column="booking_range_start_offset" jdbcType="INTEGER" property="bookingRangeStartOffset" />
    <result column="booking_range_end_type" jdbcType="TINYINT" property="bookingRangeEndType" />
    <result column="booking_range_end_offset" jdbcType="INTEGER" property="bookingRangeEndOffset" />
    <result column="booking_range_end_date" jdbcType="VARCHAR" property="bookingRangeEndDate" />
    <result column="is_need_send_renew_notification" jdbcType="BIT" property="isNeedSendRenewNotification" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="group_payment_type" jdbcType="TINYINT" property="groupPaymentType" />
    <result column="group_filter_rule" jdbcType="CHAR" property="groupFilterRule" />
    <result column="group_prepay_type" jdbcType="TINYINT" property="groupPrepayType" />
    <result column="group_prepay_tip_enable" jdbcType="TINYINT" property="groupPrepayTipEnable" />
    <result column="group_deposit_type" jdbcType="TINYINT" property="groupDepositType" />
    <result column="group_deposit_percentage" jdbcType="INTEGER" property="groupDepositPercentage" />
    <result column="group_deposit_amount" jdbcType="DECIMAL" property="groupDepositAmount" />
    <result column="group_pre_auth_tip_enable" jdbcType="TINYINT" property="groupPreAuthTipEnable" />
    <result column="group_accept_client" jdbcType="TINYINT" property="groupAcceptClient" />
    <result column="new_client_flow_type" jdbcType="INTEGER" property="newClientFlowType" />
    <result column="available_time_sync" jdbcType="TINYINT" property="availableTimeSync" />
    <result column="payment_option_map" jdbcType="CHAR" property="paymentOptionMap" typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler" />
    <result column="by_slot_show_one_available_time" jdbcType="BIT" property="bySlotShowOneAvailableTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeBusinessBookOnline">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="cancellation_policy" jdbcType="LONGVARCHAR" property="cancellationPolicy" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="prepay_policy" jdbcType="LONGVARCHAR" property="prepayPolicy" />
    <result column="pre_auth_policy" jdbcType="LONGVARCHAR" property="preAuthPolicy" />
    <result column="group_pre_auth_policy" jdbcType="LONGVARCHAR" property="groupPreAuthPolicy" />
    <result column="group_cancellation_policy" jdbcType="LONGVARCHAR" property="groupCancellationPolicy" />
    <result column="group_prepay_policy" jdbcType="LONGVARCHAR" property="groupPrepayPolicy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.serviceAreasCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.paymentOptionMapCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.serviceAreasCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.paymentOptionMapCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, is_enable, max_available_dist, max_available_time, soonest_available,
    farest_available, create_time, update_time, is_require_agreement, zip_code, place_name,
    state, state_abbreviation, county, is_need_address, is_need_select_time, fake_it,
    enable_no_show_fee, no_show_fee, appointment_interval, timeslot_mins, timeslot_format,
    accept_client, auto_move_wait, service_area_enable, weight_limit_notify, weight_limit,
    over_limit_tips, need_within_area, is_by_zipcode, is_by_radius, setting_location,
    setting_lat, setting_lng, is_check_existing_client, is_redirect, auto_accept, show_one_available_time,
    smart_schedule_enable, smart_schedule_max_dist, smart_schedule_max_time, zip_codes,
    service_areas, book_online_name, allowed_simplify_submit, available_time_type, by_slot_timeslot_format,
    by_slot_timeslot_mins, by_slot_soonest_available, by_slot_farthest_available, service_filter,
    is_show_categories, prepay_type, prepay_tip_enable, deposit_type, deposit_percentage,
    deposit_amount, use_version, pre_auth_tip_enable, auto_refund_deposit, request_submitted_auto_type,
    display_staff_selection_page, arrival_window_before_min, arrival_window_after_min,
    booking_range_start_offset, booking_range_end_type, booking_range_end_offset, booking_range_end_date,
    is_need_send_renew_notification, company_id, group_payment_type, group_filter_rule,
    group_prepay_type, group_prepay_tip_enable, group_deposit_type, group_deposit_percentage,
    group_deposit_amount, group_pre_auth_tip_enable, group_accept_client, new_client_flow_type,
    available_time_sync, payment_option_map, by_slot_show_one_available_time
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    cancellation_policy, description, prepay_policy, pre_auth_policy, group_pre_auth_policy,
    group_cancellation_policy, group_prepay_policy
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBusinessBookOnlineExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_business_book_online
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBusinessBookOnlineExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_business_book_online
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_business_book_online
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business_book_online
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBusinessBookOnlineExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business_book_online
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBusinessBookOnline">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_book_online (business_id, is_enable, max_available_dist,
      max_available_time, soonest_available, farest_available,
      create_time, update_time, is_require_agreement,
      zip_code, place_name, state,
      state_abbreviation, county, is_need_address,
      is_need_select_time, fake_it, enable_no_show_fee,
      no_show_fee, appointment_interval, timeslot_mins,
      timeslot_format, accept_client, auto_move_wait,
      service_area_enable, weight_limit_notify, weight_limit,
      over_limit_tips, need_within_area, is_by_zipcode,
      is_by_radius, setting_location, setting_lat,
      setting_lng, is_check_existing_client, is_redirect,
      auto_accept, show_one_available_time, smart_schedule_enable,
      smart_schedule_max_dist, smart_schedule_max_time,
      zip_codes, service_areas,
      book_online_name, allowed_simplify_submit,
      available_time_type, by_slot_timeslot_format,
      by_slot_timeslot_mins, by_slot_soonest_available,
      by_slot_farthest_available, service_filter,
      is_show_categories, prepay_type, prepay_tip_enable,
      deposit_type, deposit_percentage, deposit_amount,
      use_version, pre_auth_tip_enable, auto_refund_deposit,
      request_submitted_auto_type, display_staff_selection_page,
      arrival_window_before_min, arrival_window_after_min,
      booking_range_start_offset, booking_range_end_type,
      booking_range_end_offset, booking_range_end_date,
      is_need_send_renew_notification, company_id, group_payment_type,
      group_filter_rule, group_prepay_type, group_prepay_tip_enable,
      group_deposit_type, group_deposit_percentage,
      group_deposit_amount, group_pre_auth_tip_enable,
      group_accept_client, new_client_flow_type, available_time_sync,
      payment_option_map,
      by_slot_show_one_available_time, cancellation_policy,
      description, prepay_policy, pre_auth_policy,
      group_pre_auth_policy, group_cancellation_policy,
      group_prepay_policy)
    values (#{businessId,jdbcType=INTEGER}, #{isEnable,jdbcType=TINYINT}, #{maxAvailableDist,jdbcType=INTEGER},
      #{maxAvailableTime,jdbcType=INTEGER}, #{soonestAvailable,jdbcType=INTEGER}, #{farestAvailable,jdbcType=INTEGER},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{isRequireAgreement,jdbcType=TINYINT},
      #{zipCode,jdbcType=VARCHAR}, #{placeName,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR},
      #{stateAbbreviation,jdbcType=VARCHAR}, #{county,jdbcType=VARCHAR}, #{isNeedAddress,jdbcType=TINYINT},
      #{isNeedSelectTime,jdbcType=TINYINT}, #{fakeIt,jdbcType=TINYINT}, #{enableNoShowFee,jdbcType=TINYINT},
      #{noShowFee,jdbcType=DECIMAL}, #{appointmentInterval,jdbcType=INTEGER}, #{timeslotMins,jdbcType=INTEGER},
      #{timeslotFormat,jdbcType=TINYINT}, #{acceptClient,jdbcType=TINYINT}, #{autoMoveWait,jdbcType=TINYINT},
      #{serviceAreaEnable,jdbcType=TINYINT}, #{weightLimitNotify,jdbcType=TINYINT}, #{weightLimit,jdbcType=INTEGER},
      #{overLimitTips,jdbcType=VARCHAR}, #{needWithinArea,jdbcType=TINYINT}, #{isByZipcode,jdbcType=TINYINT},
      #{isByRadius,jdbcType=TINYINT}, #{settingLocation,jdbcType=VARCHAR}, #{settingLat,jdbcType=VARCHAR},
      #{settingLng,jdbcType=VARCHAR}, #{isCheckExistingClient,jdbcType=TINYINT}, #{isRedirect,jdbcType=TINYINT},
      #{autoAccept,jdbcType=TINYINT}, #{showOneAvailableTime,jdbcType=TINYINT}, #{smartScheduleEnable,jdbcType=TINYINT},
      #{smartScheduleMaxDist,jdbcType=INTEGER}, #{smartScheduleMaxTime,jdbcType=INTEGER},
      #{zipCodes,jdbcType=VARCHAR}, #{serviceAreas,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler},
      #{bookOnlineName,jdbcType=VARCHAR}, #{allowedSimplifySubmit,jdbcType=TINYINT},
      #{availableTimeType,jdbcType=TINYINT}, #{bySlotTimeslotFormat,jdbcType=TINYINT},
      #{bySlotTimeslotMins,jdbcType=INTEGER}, #{bySlotSoonestAvailable,jdbcType=INTEGER},
      #{bySlotFarthestAvailable,jdbcType=INTEGER}, #{serviceFilter,jdbcType=TINYINT},
      #{isShowCategories,jdbcType=BIT}, #{prepayType,jdbcType=TINYINT}, #{prepayTipEnable,jdbcType=TINYINT},
      #{depositType,jdbcType=TINYINT}, #{depositPercentage,jdbcType=INTEGER}, #{depositAmount,jdbcType=DECIMAL},
      #{useVersion,jdbcType=TINYINT}, #{preAuthTipEnable,jdbcType=TINYINT}, #{autoRefundDeposit,jdbcType=TINYINT},
      #{requestSubmittedAutoType,jdbcType=VARCHAR}, #{displayStaffSelectionPage,jdbcType=BIT},
      #{arrivalWindowBeforeMin,jdbcType=INTEGER}, #{arrivalWindowAfterMin,jdbcType=INTEGER},
      #{bookingRangeStartOffset,jdbcType=INTEGER}, #{bookingRangeEndType,jdbcType=TINYINT},
      #{bookingRangeEndOffset,jdbcType=INTEGER}, #{bookingRangeEndDate,jdbcType=VARCHAR},
      #{isNeedSendRenewNotification,jdbcType=BIT}, #{companyId,jdbcType=BIGINT}, #{groupPaymentType,jdbcType=TINYINT},
      #{groupFilterRule,jdbcType=CHAR}, #{groupPrepayType,jdbcType=TINYINT}, #{groupPrepayTipEnable,jdbcType=TINYINT},
      #{groupDepositType,jdbcType=TINYINT}, #{groupDepositPercentage,jdbcType=INTEGER},
      #{groupDepositAmount,jdbcType=DECIMAL}, #{groupPreAuthTipEnable,jdbcType=TINYINT},
      #{groupAcceptClient,jdbcType=TINYINT}, #{newClientFlowType,jdbcType=INTEGER}, #{availableTimeSync,jdbcType=TINYINT},
      #{paymentOptionMap,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler},
      #{bySlotShowOneAvailableTime,jdbcType=BIT}, #{cancellationPolicy,jdbcType=LONGVARCHAR},
      #{description,jdbcType=LONGVARCHAR}, #{prepayPolicy,jdbcType=LONGVARCHAR}, #{preAuthPolicy,jdbcType=LONGVARCHAR},
      #{groupPreAuthPolicy,jdbcType=LONGVARCHAR}, #{groupCancellationPolicy,jdbcType=LONGVARCHAR},
      #{groupPrepayPolicy,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBusinessBookOnline">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_book_online
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="isEnable != null">
        is_enable,
      </if>
      <if test="maxAvailableDist != null">
        max_available_dist,
      </if>
      <if test="maxAvailableTime != null">
        max_available_time,
      </if>
      <if test="soonestAvailable != null">
        soonest_available,
      </if>
      <if test="farestAvailable != null">
        farest_available,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isRequireAgreement != null">
        is_require_agreement,
      </if>
      <if test="zipCode != null">
        zip_code,
      </if>
      <if test="placeName != null">
        place_name,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="stateAbbreviation != null">
        state_abbreviation,
      </if>
      <if test="county != null">
        county,
      </if>
      <if test="isNeedAddress != null">
        is_need_address,
      </if>
      <if test="isNeedSelectTime != null">
        is_need_select_time,
      </if>
      <if test="fakeIt != null">
        fake_it,
      </if>
      <if test="enableNoShowFee != null">
        enable_no_show_fee,
      </if>
      <if test="noShowFee != null">
        no_show_fee,
      </if>
      <if test="appointmentInterval != null">
        appointment_interval,
      </if>
      <if test="timeslotMins != null">
        timeslot_mins,
      </if>
      <if test="timeslotFormat != null">
        timeslot_format,
      </if>
      <if test="acceptClient != null">
        accept_client,
      </if>
      <if test="autoMoveWait != null">
        auto_move_wait,
      </if>
      <if test="serviceAreaEnable != null">
        service_area_enable,
      </if>
      <if test="weightLimitNotify != null">
        weight_limit_notify,
      </if>
      <if test="weightLimit != null">
        weight_limit,
      </if>
      <if test="overLimitTips != null">
        over_limit_tips,
      </if>
      <if test="needWithinArea != null">
        need_within_area,
      </if>
      <if test="isByZipcode != null">
        is_by_zipcode,
      </if>
      <if test="isByRadius != null">
        is_by_radius,
      </if>
      <if test="settingLocation != null">
        setting_location,
      </if>
      <if test="settingLat != null">
        setting_lat,
      </if>
      <if test="settingLng != null">
        setting_lng,
      </if>
      <if test="isCheckExistingClient != null">
        is_check_existing_client,
      </if>
      <if test="isRedirect != null">
        is_redirect,
      </if>
      <if test="autoAccept != null">
        auto_accept,
      </if>
      <if test="showOneAvailableTime != null">
        show_one_available_time,
      </if>
      <if test="smartScheduleEnable != null">
        smart_schedule_enable,
      </if>
      <if test="smartScheduleMaxDist != null">
        smart_schedule_max_dist,
      </if>
      <if test="smartScheduleMaxTime != null">
        smart_schedule_max_time,
      </if>
      <if test="zipCodes != null">
        zip_codes,
      </if>
      <if test="serviceAreas != null">
        service_areas,
      </if>
      <if test="bookOnlineName != null">
        book_online_name,
      </if>
      <if test="allowedSimplifySubmit != null">
        allowed_simplify_submit,
      </if>
      <if test="availableTimeType != null">
        available_time_type,
      </if>
      <if test="bySlotTimeslotFormat != null">
        by_slot_timeslot_format,
      </if>
      <if test="bySlotTimeslotMins != null">
        by_slot_timeslot_mins,
      </if>
      <if test="bySlotSoonestAvailable != null">
        by_slot_soonest_available,
      </if>
      <if test="bySlotFarthestAvailable != null">
        by_slot_farthest_available,
      </if>
      <if test="serviceFilter != null">
        service_filter,
      </if>
      <if test="isShowCategories != null">
        is_show_categories,
      </if>
      <if test="prepayType != null">
        prepay_type,
      </if>
      <if test="prepayTipEnable != null">
        prepay_tip_enable,
      </if>
      <if test="depositType != null">
        deposit_type,
      </if>
      <if test="depositPercentage != null">
        deposit_percentage,
      </if>
      <if test="depositAmount != null">
        deposit_amount,
      </if>
      <if test="useVersion != null">
        use_version,
      </if>
      <if test="preAuthTipEnable != null">
        pre_auth_tip_enable,
      </if>
      <if test="autoRefundDeposit != null">
        auto_refund_deposit,
      </if>
      <if test="requestSubmittedAutoType != null">
        request_submitted_auto_type,
      </if>
      <if test="displayStaffSelectionPage != null">
        display_staff_selection_page,
      </if>
      <if test="arrivalWindowBeforeMin != null">
        arrival_window_before_min,
      </if>
      <if test="arrivalWindowAfterMin != null">
        arrival_window_after_min,
      </if>
      <if test="bookingRangeStartOffset != null">
        booking_range_start_offset,
      </if>
      <if test="bookingRangeEndType != null">
        booking_range_end_type,
      </if>
      <if test="bookingRangeEndOffset != null">
        booking_range_end_offset,
      </if>
      <if test="bookingRangeEndDate != null">
        booking_range_end_date,
      </if>
      <if test="isNeedSendRenewNotification != null">
        is_need_send_renew_notification,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="groupPaymentType != null">
        group_payment_type,
      </if>
      <if test="groupFilterRule != null">
        group_filter_rule,
      </if>
      <if test="groupPrepayType != null">
        group_prepay_type,
      </if>
      <if test="groupPrepayTipEnable != null">
        group_prepay_tip_enable,
      </if>
      <if test="groupDepositType != null">
        group_deposit_type,
      </if>
      <if test="groupDepositPercentage != null">
        group_deposit_percentage,
      </if>
      <if test="groupDepositAmount != null">
        group_deposit_amount,
      </if>
      <if test="groupPreAuthTipEnable != null">
        group_pre_auth_tip_enable,
      </if>
      <if test="groupAcceptClient != null">
        group_accept_client,
      </if>
      <if test="newClientFlowType != null">
        new_client_flow_type,
      </if>
      <if test="availableTimeSync != null">
        available_time_sync,
      </if>
      <if test="paymentOptionMap != null">
        payment_option_map,
      </if>
      <if test="bySlotShowOneAvailableTime != null">
        by_slot_show_one_available_time,
      </if>
      <if test="cancellationPolicy != null">
        cancellation_policy,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="prepayPolicy != null">
        prepay_policy,
      </if>
      <if test="preAuthPolicy != null">
        pre_auth_policy,
      </if>
      <if test="groupPreAuthPolicy != null">
        group_pre_auth_policy,
      </if>
      <if test="groupCancellationPolicy != null">
        group_cancellation_policy,
      </if>
      <if test="groupPrepayPolicy != null">
        group_prepay_policy,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=TINYINT},
      </if>
      <if test="maxAvailableDist != null">
        #{maxAvailableDist,jdbcType=INTEGER},
      </if>
      <if test="maxAvailableTime != null">
        #{maxAvailableTime,jdbcType=INTEGER},
      </if>
      <if test="soonestAvailable != null">
        #{soonestAvailable,jdbcType=INTEGER},
      </if>
      <if test="farestAvailable != null">
        #{farestAvailable,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="isRequireAgreement != null">
        #{isRequireAgreement,jdbcType=TINYINT},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="placeName != null">
        #{placeName,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="stateAbbreviation != null">
        #{stateAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        #{county,jdbcType=VARCHAR},
      </if>
      <if test="isNeedAddress != null">
        #{isNeedAddress,jdbcType=TINYINT},
      </if>
      <if test="isNeedSelectTime != null">
        #{isNeedSelectTime,jdbcType=TINYINT},
      </if>
      <if test="fakeIt != null">
        #{fakeIt,jdbcType=TINYINT},
      </if>
      <if test="enableNoShowFee != null">
        #{enableNoShowFee,jdbcType=TINYINT},
      </if>
      <if test="noShowFee != null">
        #{noShowFee,jdbcType=DECIMAL},
      </if>
      <if test="appointmentInterval != null">
        #{appointmentInterval,jdbcType=INTEGER},
      </if>
      <if test="timeslotMins != null">
        #{timeslotMins,jdbcType=INTEGER},
      </if>
      <if test="timeslotFormat != null">
        #{timeslotFormat,jdbcType=TINYINT},
      </if>
      <if test="acceptClient != null">
        #{acceptClient,jdbcType=TINYINT},
      </if>
      <if test="autoMoveWait != null">
        #{autoMoveWait,jdbcType=TINYINT},
      </if>
      <if test="serviceAreaEnable != null">
        #{serviceAreaEnable,jdbcType=TINYINT},
      </if>
      <if test="weightLimitNotify != null">
        #{weightLimitNotify,jdbcType=TINYINT},
      </if>
      <if test="weightLimit != null">
        #{weightLimit,jdbcType=INTEGER},
      </if>
      <if test="overLimitTips != null">
        #{overLimitTips,jdbcType=VARCHAR},
      </if>
      <if test="needWithinArea != null">
        #{needWithinArea,jdbcType=TINYINT},
      </if>
      <if test="isByZipcode != null">
        #{isByZipcode,jdbcType=TINYINT},
      </if>
      <if test="isByRadius != null">
        #{isByRadius,jdbcType=TINYINT},
      </if>
      <if test="settingLocation != null">
        #{settingLocation,jdbcType=VARCHAR},
      </if>
      <if test="settingLat != null">
        #{settingLat,jdbcType=VARCHAR},
      </if>
      <if test="settingLng != null">
        #{settingLng,jdbcType=VARCHAR},
      </if>
      <if test="isCheckExistingClient != null">
        #{isCheckExistingClient,jdbcType=TINYINT},
      </if>
      <if test="isRedirect != null">
        #{isRedirect,jdbcType=TINYINT},
      </if>
      <if test="autoAccept != null">
        #{autoAccept,jdbcType=TINYINT},
      </if>
      <if test="showOneAvailableTime != null">
        #{showOneAvailableTime,jdbcType=TINYINT},
      </if>
      <if test="smartScheduleEnable != null">
        #{smartScheduleEnable,jdbcType=TINYINT},
      </if>
      <if test="smartScheduleMaxDist != null">
        #{smartScheduleMaxDist,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleMaxTime != null">
        #{smartScheduleMaxTime,jdbcType=INTEGER},
      </if>
      <if test="zipCodes != null">
        #{zipCodes,jdbcType=VARCHAR},
      </if>
      <if test="serviceAreas != null">
        #{serviceAreas,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler},
      </if>
      <if test="bookOnlineName != null">
        #{bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="allowedSimplifySubmit != null">
        #{allowedSimplifySubmit,jdbcType=TINYINT},
      </if>
      <if test="availableTimeType != null">
        #{availableTimeType,jdbcType=TINYINT},
      </if>
      <if test="bySlotTimeslotFormat != null">
        #{bySlotTimeslotFormat,jdbcType=TINYINT},
      </if>
      <if test="bySlotTimeslotMins != null">
        #{bySlotTimeslotMins,jdbcType=INTEGER},
      </if>
      <if test="bySlotSoonestAvailable != null">
        #{bySlotSoonestAvailable,jdbcType=INTEGER},
      </if>
      <if test="bySlotFarthestAvailable != null">
        #{bySlotFarthestAvailable,jdbcType=INTEGER},
      </if>
      <if test="serviceFilter != null">
        #{serviceFilter,jdbcType=TINYINT},
      </if>
      <if test="isShowCategories != null">
        #{isShowCategories,jdbcType=BIT},
      </if>
      <if test="prepayType != null">
        #{prepayType,jdbcType=TINYINT},
      </if>
      <if test="prepayTipEnable != null">
        #{prepayTipEnable,jdbcType=TINYINT},
      </if>
      <if test="depositType != null">
        #{depositType,jdbcType=TINYINT},
      </if>
      <if test="depositPercentage != null">
        #{depositPercentage,jdbcType=INTEGER},
      </if>
      <if test="depositAmount != null">
        #{depositAmount,jdbcType=DECIMAL},
      </if>
      <if test="useVersion != null">
        #{useVersion,jdbcType=TINYINT},
      </if>
      <if test="preAuthTipEnable != null">
        #{preAuthTipEnable,jdbcType=TINYINT},
      </if>
      <if test="autoRefundDeposit != null">
        #{autoRefundDeposit,jdbcType=TINYINT},
      </if>
      <if test="requestSubmittedAutoType != null">
        #{requestSubmittedAutoType,jdbcType=VARCHAR},
      </if>
      <if test="displayStaffSelectionPage != null">
        #{displayStaffSelectionPage,jdbcType=BIT},
      </if>
      <if test="arrivalWindowBeforeMin != null">
        #{arrivalWindowBeforeMin,jdbcType=INTEGER},
      </if>
      <if test="arrivalWindowAfterMin != null">
        #{arrivalWindowAfterMin,jdbcType=INTEGER},
      </if>
      <if test="bookingRangeStartOffset != null">
        #{bookingRangeStartOffset,jdbcType=INTEGER},
      </if>
      <if test="bookingRangeEndType != null">
        #{bookingRangeEndType,jdbcType=TINYINT},
      </if>
      <if test="bookingRangeEndOffset != null">
        #{bookingRangeEndOffset,jdbcType=INTEGER},
      </if>
      <if test="bookingRangeEndDate != null">
        #{bookingRangeEndDate,jdbcType=VARCHAR},
      </if>
      <if test="isNeedSendRenewNotification != null">
        #{isNeedSendRenewNotification,jdbcType=BIT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="groupPaymentType != null">
        #{groupPaymentType,jdbcType=TINYINT},
      </if>
      <if test="groupFilterRule != null">
        #{groupFilterRule,jdbcType=CHAR},
      </if>
      <if test="groupPrepayType != null">
        #{groupPrepayType,jdbcType=TINYINT},
      </if>
      <if test="groupPrepayTipEnable != null">
        #{groupPrepayTipEnable,jdbcType=TINYINT},
      </if>
      <if test="groupDepositType != null">
        #{groupDepositType,jdbcType=TINYINT},
      </if>
      <if test="groupDepositPercentage != null">
        #{groupDepositPercentage,jdbcType=INTEGER},
      </if>
      <if test="groupDepositAmount != null">
        #{groupDepositAmount,jdbcType=DECIMAL},
      </if>
      <if test="groupPreAuthTipEnable != null">
        #{groupPreAuthTipEnable,jdbcType=TINYINT},
      </if>
      <if test="groupAcceptClient != null">
        #{groupAcceptClient,jdbcType=TINYINT},
      </if>
      <if test="newClientFlowType != null">
        #{newClientFlowType,jdbcType=INTEGER},
      </if>
      <if test="availableTimeSync != null">
        #{availableTimeSync,jdbcType=TINYINT},
      </if>
      <if test="paymentOptionMap != null">
        #{paymentOptionMap,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler},
      </if>
      <if test="bySlotShowOneAvailableTime != null">
        #{bySlotShowOneAvailableTime,jdbcType=BIT},
      </if>
      <if test="cancellationPolicy != null">
        #{cancellationPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="prepayPolicy != null">
        #{prepayPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="preAuthPolicy != null">
        #{preAuthPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="groupPreAuthPolicy != null">
        #{groupPreAuthPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="groupCancellationPolicy != null">
        #{groupCancellationPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="groupPrepayPolicy != null">
        #{groupPrepayPolicy,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBusinessBookOnlineExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_business_book_online
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_book_online
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.isEnable != null">
        is_enable = #{record.isEnable,jdbcType=TINYINT},
      </if>
      <if test="record.maxAvailableDist != null">
        max_available_dist = #{record.maxAvailableDist,jdbcType=INTEGER},
      </if>
      <if test="record.maxAvailableTime != null">
        max_available_time = #{record.maxAvailableTime,jdbcType=INTEGER},
      </if>
      <if test="record.soonestAvailable != null">
        soonest_available = #{record.soonestAvailable,jdbcType=INTEGER},
      </if>
      <if test="record.farestAvailable != null">
        farest_available = #{record.farestAvailable,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.isRequireAgreement != null">
        is_require_agreement = #{record.isRequireAgreement,jdbcType=TINYINT},
      </if>
      <if test="record.zipCode != null">
        zip_code = #{record.zipCode,jdbcType=VARCHAR},
      </if>
      <if test="record.placeName != null">
        place_name = #{record.placeName,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=VARCHAR},
      </if>
      <if test="record.stateAbbreviation != null">
        state_abbreviation = #{record.stateAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="record.county != null">
        county = #{record.county,jdbcType=VARCHAR},
      </if>
      <if test="record.isNeedAddress != null">
        is_need_address = #{record.isNeedAddress,jdbcType=TINYINT},
      </if>
      <if test="record.isNeedSelectTime != null">
        is_need_select_time = #{record.isNeedSelectTime,jdbcType=TINYINT},
      </if>
      <if test="record.fakeIt != null">
        fake_it = #{record.fakeIt,jdbcType=TINYINT},
      </if>
      <if test="record.enableNoShowFee != null">
        enable_no_show_fee = #{record.enableNoShowFee,jdbcType=TINYINT},
      </if>
      <if test="record.noShowFee != null">
        no_show_fee = #{record.noShowFee,jdbcType=DECIMAL},
      </if>
      <if test="record.appointmentInterval != null">
        appointment_interval = #{record.appointmentInterval,jdbcType=INTEGER},
      </if>
      <if test="record.timeslotMins != null">
        timeslot_mins = #{record.timeslotMins,jdbcType=INTEGER},
      </if>
      <if test="record.timeslotFormat != null">
        timeslot_format = #{record.timeslotFormat,jdbcType=TINYINT},
      </if>
      <if test="record.acceptClient != null">
        accept_client = #{record.acceptClient,jdbcType=TINYINT},
      </if>
      <if test="record.autoMoveWait != null">
        auto_move_wait = #{record.autoMoveWait,jdbcType=TINYINT},
      </if>
      <if test="record.serviceAreaEnable != null">
        service_area_enable = #{record.serviceAreaEnable,jdbcType=TINYINT},
      </if>
      <if test="record.weightLimitNotify != null">
        weight_limit_notify = #{record.weightLimitNotify,jdbcType=TINYINT},
      </if>
      <if test="record.weightLimit != null">
        weight_limit = #{record.weightLimit,jdbcType=INTEGER},
      </if>
      <if test="record.overLimitTips != null">
        over_limit_tips = #{record.overLimitTips,jdbcType=VARCHAR},
      </if>
      <if test="record.needWithinArea != null">
        need_within_area = #{record.needWithinArea,jdbcType=TINYINT},
      </if>
      <if test="record.isByZipcode != null">
        is_by_zipcode = #{record.isByZipcode,jdbcType=TINYINT},
      </if>
      <if test="record.isByRadius != null">
        is_by_radius = #{record.isByRadius,jdbcType=TINYINT},
      </if>
      <if test="record.settingLocation != null">
        setting_location = #{record.settingLocation,jdbcType=VARCHAR},
      </if>
      <if test="record.settingLat != null">
        setting_lat = #{record.settingLat,jdbcType=VARCHAR},
      </if>
      <if test="record.settingLng != null">
        setting_lng = #{record.settingLng,jdbcType=VARCHAR},
      </if>
      <if test="record.isCheckExistingClient != null">
        is_check_existing_client = #{record.isCheckExistingClient,jdbcType=TINYINT},
      </if>
      <if test="record.isRedirect != null">
        is_redirect = #{record.isRedirect,jdbcType=TINYINT},
      </if>
      <if test="record.autoAccept != null">
        auto_accept = #{record.autoAccept,jdbcType=TINYINT},
      </if>
      <if test="record.showOneAvailableTime != null">
        show_one_available_time = #{record.showOneAvailableTime,jdbcType=TINYINT},
      </if>
      <if test="record.smartScheduleEnable != null">
        smart_schedule_enable = #{record.smartScheduleEnable,jdbcType=TINYINT},
      </if>
      <if test="record.smartScheduleMaxDist != null">
        smart_schedule_max_dist = #{record.smartScheduleMaxDist,jdbcType=INTEGER},
      </if>
      <if test="record.smartScheduleMaxTime != null">
        smart_schedule_max_time = #{record.smartScheduleMaxTime,jdbcType=INTEGER},
      </if>
      <if test="record.zipCodes != null">
        zip_codes = #{record.zipCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceAreas != null">
        service_areas = #{record.serviceAreas,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler},
      </if>
      <if test="record.bookOnlineName != null">
        book_online_name = #{record.bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="record.allowedSimplifySubmit != null">
        allowed_simplify_submit = #{record.allowedSimplifySubmit,jdbcType=TINYINT},
      </if>
      <if test="record.availableTimeType != null">
        available_time_type = #{record.availableTimeType,jdbcType=TINYINT},
      </if>
      <if test="record.bySlotTimeslotFormat != null">
        by_slot_timeslot_format = #{record.bySlotTimeslotFormat,jdbcType=TINYINT},
      </if>
      <if test="record.bySlotTimeslotMins != null">
        by_slot_timeslot_mins = #{record.bySlotTimeslotMins,jdbcType=INTEGER},
      </if>
      <if test="record.bySlotSoonestAvailable != null">
        by_slot_soonest_available = #{record.bySlotSoonestAvailable,jdbcType=INTEGER},
      </if>
      <if test="record.bySlotFarthestAvailable != null">
        by_slot_farthest_available = #{record.bySlotFarthestAvailable,jdbcType=INTEGER},
      </if>
      <if test="record.serviceFilter != null">
        service_filter = #{record.serviceFilter,jdbcType=TINYINT},
      </if>
      <if test="record.isShowCategories != null">
        is_show_categories = #{record.isShowCategories,jdbcType=BIT},
      </if>
      <if test="record.prepayType != null">
        prepay_type = #{record.prepayType,jdbcType=TINYINT},
      </if>
      <if test="record.prepayTipEnable != null">
        prepay_tip_enable = #{record.prepayTipEnable,jdbcType=TINYINT},
      </if>
      <if test="record.depositType != null">
        deposit_type = #{record.depositType,jdbcType=TINYINT},
      </if>
      <if test="record.depositPercentage != null">
        deposit_percentage = #{record.depositPercentage,jdbcType=INTEGER},
      </if>
      <if test="record.depositAmount != null">
        deposit_amount = #{record.depositAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.useVersion != null">
        use_version = #{record.useVersion,jdbcType=TINYINT},
      </if>
      <if test="record.preAuthTipEnable != null">
        pre_auth_tip_enable = #{record.preAuthTipEnable,jdbcType=TINYINT},
      </if>
      <if test="record.autoRefundDeposit != null">
        auto_refund_deposit = #{record.autoRefundDeposit,jdbcType=TINYINT},
      </if>
      <if test="record.requestSubmittedAutoType != null">
        request_submitted_auto_type = #{record.requestSubmittedAutoType,jdbcType=VARCHAR},
      </if>
      <if test="record.displayStaffSelectionPage != null">
        display_staff_selection_page = #{record.displayStaffSelectionPage,jdbcType=BIT},
      </if>
      <if test="record.arrivalWindowBeforeMin != null">
        arrival_window_before_min = #{record.arrivalWindowBeforeMin,jdbcType=INTEGER},
      </if>
      <if test="record.arrivalWindowAfterMin != null">
        arrival_window_after_min = #{record.arrivalWindowAfterMin,jdbcType=INTEGER},
      </if>
      <if test="record.bookingRangeStartOffset != null">
        booking_range_start_offset = #{record.bookingRangeStartOffset,jdbcType=INTEGER},
      </if>
      <if test="record.bookingRangeEndType != null">
        booking_range_end_type = #{record.bookingRangeEndType,jdbcType=TINYINT},
      </if>
      <if test="record.bookingRangeEndOffset != null">
        booking_range_end_offset = #{record.bookingRangeEndOffset,jdbcType=INTEGER},
      </if>
      <if test="record.bookingRangeEndDate != null">
        booking_range_end_date = #{record.bookingRangeEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.isNeedSendRenewNotification != null">
        is_need_send_renew_notification = #{record.isNeedSendRenewNotification,jdbcType=BIT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.groupPaymentType != null">
        group_payment_type = #{record.groupPaymentType,jdbcType=TINYINT},
      </if>
      <if test="record.groupFilterRule != null">
        group_filter_rule = #{record.groupFilterRule,jdbcType=CHAR},
      </if>
      <if test="record.groupPrepayType != null">
        group_prepay_type = #{record.groupPrepayType,jdbcType=TINYINT},
      </if>
      <if test="record.groupPrepayTipEnable != null">
        group_prepay_tip_enable = #{record.groupPrepayTipEnable,jdbcType=TINYINT},
      </if>
      <if test="record.groupDepositType != null">
        group_deposit_type = #{record.groupDepositType,jdbcType=TINYINT},
      </if>
      <if test="record.groupDepositPercentage != null">
        group_deposit_percentage = #{record.groupDepositPercentage,jdbcType=INTEGER},
      </if>
      <if test="record.groupDepositAmount != null">
        group_deposit_amount = #{record.groupDepositAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.groupPreAuthTipEnable != null">
        group_pre_auth_tip_enable = #{record.groupPreAuthTipEnable,jdbcType=TINYINT},
      </if>
      <if test="record.groupAcceptClient != null">
        group_accept_client = #{record.groupAcceptClient,jdbcType=TINYINT},
      </if>
      <if test="record.newClientFlowType != null">
        new_client_flow_type = #{record.newClientFlowType,jdbcType=INTEGER},
      </if>
      <if test="record.availableTimeSync != null">
        available_time_sync = #{record.availableTimeSync,jdbcType=TINYINT},
      </if>
      <if test="record.paymentOptionMap != null">
        payment_option_map = #{record.paymentOptionMap,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler},
      </if>
      <if test="record.bySlotShowOneAvailableTime != null">
        by_slot_show_one_available_time = #{record.bySlotShowOneAvailableTime,jdbcType=BIT},
      </if>
      <if test="record.cancellationPolicy != null">
        cancellation_policy = #{record.cancellationPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.prepayPolicy != null">
        prepay_policy = #{record.prepayPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.preAuthPolicy != null">
        pre_auth_policy = #{record.preAuthPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.groupPreAuthPolicy != null">
        group_pre_auth_policy = #{record.groupPreAuthPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.groupCancellationPolicy != null">
        group_cancellation_policy = #{record.groupCancellationPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.groupPrepayPolicy != null">
        group_prepay_policy = #{record.groupPrepayPolicy,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_book_online
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      is_enable = #{record.isEnable,jdbcType=TINYINT},
      max_available_dist = #{record.maxAvailableDist,jdbcType=INTEGER},
      max_available_time = #{record.maxAvailableTime,jdbcType=INTEGER},
      soonest_available = #{record.soonestAvailable,jdbcType=INTEGER},
      farest_available = #{record.farestAvailable,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      is_require_agreement = #{record.isRequireAgreement,jdbcType=TINYINT},
      zip_code = #{record.zipCode,jdbcType=VARCHAR},
      place_name = #{record.placeName,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=VARCHAR},
      state_abbreviation = #{record.stateAbbreviation,jdbcType=VARCHAR},
      county = #{record.county,jdbcType=VARCHAR},
      is_need_address = #{record.isNeedAddress,jdbcType=TINYINT},
      is_need_select_time = #{record.isNeedSelectTime,jdbcType=TINYINT},
      fake_it = #{record.fakeIt,jdbcType=TINYINT},
      enable_no_show_fee = #{record.enableNoShowFee,jdbcType=TINYINT},
      no_show_fee = #{record.noShowFee,jdbcType=DECIMAL},
      appointment_interval = #{record.appointmentInterval,jdbcType=INTEGER},
      timeslot_mins = #{record.timeslotMins,jdbcType=INTEGER},
      timeslot_format = #{record.timeslotFormat,jdbcType=TINYINT},
      accept_client = #{record.acceptClient,jdbcType=TINYINT},
      auto_move_wait = #{record.autoMoveWait,jdbcType=TINYINT},
      service_area_enable = #{record.serviceAreaEnable,jdbcType=TINYINT},
      weight_limit_notify = #{record.weightLimitNotify,jdbcType=TINYINT},
      weight_limit = #{record.weightLimit,jdbcType=INTEGER},
      over_limit_tips = #{record.overLimitTips,jdbcType=VARCHAR},
      need_within_area = #{record.needWithinArea,jdbcType=TINYINT},
      is_by_zipcode = #{record.isByZipcode,jdbcType=TINYINT},
      is_by_radius = #{record.isByRadius,jdbcType=TINYINT},
      setting_location = #{record.settingLocation,jdbcType=VARCHAR},
      setting_lat = #{record.settingLat,jdbcType=VARCHAR},
      setting_lng = #{record.settingLng,jdbcType=VARCHAR},
      is_check_existing_client = #{record.isCheckExistingClient,jdbcType=TINYINT},
      is_redirect = #{record.isRedirect,jdbcType=TINYINT},
      auto_accept = #{record.autoAccept,jdbcType=TINYINT},
      show_one_available_time = #{record.showOneAvailableTime,jdbcType=TINYINT},
      smart_schedule_enable = #{record.smartScheduleEnable,jdbcType=TINYINT},
      smart_schedule_max_dist = #{record.smartScheduleMaxDist,jdbcType=INTEGER},
      smart_schedule_max_time = #{record.smartScheduleMaxTime,jdbcType=INTEGER},
      zip_codes = #{record.zipCodes,jdbcType=VARCHAR},
      service_areas = #{record.serviceAreas,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler},
      book_online_name = #{record.bookOnlineName,jdbcType=VARCHAR},
      allowed_simplify_submit = #{record.allowedSimplifySubmit,jdbcType=TINYINT},
      available_time_type = #{record.availableTimeType,jdbcType=TINYINT},
      by_slot_timeslot_format = #{record.bySlotTimeslotFormat,jdbcType=TINYINT},
      by_slot_timeslot_mins = #{record.bySlotTimeslotMins,jdbcType=INTEGER},
      by_slot_soonest_available = #{record.bySlotSoonestAvailable,jdbcType=INTEGER},
      by_slot_farthest_available = #{record.bySlotFarthestAvailable,jdbcType=INTEGER},
      service_filter = #{record.serviceFilter,jdbcType=TINYINT},
      is_show_categories = #{record.isShowCategories,jdbcType=BIT},
      prepay_type = #{record.prepayType,jdbcType=TINYINT},
      prepay_tip_enable = #{record.prepayTipEnable,jdbcType=TINYINT},
      deposit_type = #{record.depositType,jdbcType=TINYINT},
      deposit_percentage = #{record.depositPercentage,jdbcType=INTEGER},
      deposit_amount = #{record.depositAmount,jdbcType=DECIMAL},
      use_version = #{record.useVersion,jdbcType=TINYINT},
      pre_auth_tip_enable = #{record.preAuthTipEnable,jdbcType=TINYINT},
      auto_refund_deposit = #{record.autoRefundDeposit,jdbcType=TINYINT},
      request_submitted_auto_type = #{record.requestSubmittedAutoType,jdbcType=VARCHAR},
      display_staff_selection_page = #{record.displayStaffSelectionPage,jdbcType=BIT},
      arrival_window_before_min = #{record.arrivalWindowBeforeMin,jdbcType=INTEGER},
      arrival_window_after_min = #{record.arrivalWindowAfterMin,jdbcType=INTEGER},
      booking_range_start_offset = #{record.bookingRangeStartOffset,jdbcType=INTEGER},
      booking_range_end_type = #{record.bookingRangeEndType,jdbcType=TINYINT},
      booking_range_end_offset = #{record.bookingRangeEndOffset,jdbcType=INTEGER},
      booking_range_end_date = #{record.bookingRangeEndDate,jdbcType=VARCHAR},
      is_need_send_renew_notification = #{record.isNeedSendRenewNotification,jdbcType=BIT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      group_payment_type = #{record.groupPaymentType,jdbcType=TINYINT},
      group_filter_rule = #{record.groupFilterRule,jdbcType=CHAR},
      group_prepay_type = #{record.groupPrepayType,jdbcType=TINYINT},
      group_prepay_tip_enable = #{record.groupPrepayTipEnable,jdbcType=TINYINT},
      group_deposit_type = #{record.groupDepositType,jdbcType=TINYINT},
      group_deposit_percentage = #{record.groupDepositPercentage,jdbcType=INTEGER},
      group_deposit_amount = #{record.groupDepositAmount,jdbcType=DECIMAL},
      group_pre_auth_tip_enable = #{record.groupPreAuthTipEnable,jdbcType=TINYINT},
      group_accept_client = #{record.groupAcceptClient,jdbcType=TINYINT},
      new_client_flow_type = #{record.newClientFlowType,jdbcType=INTEGER},
      available_time_sync = #{record.availableTimeSync,jdbcType=TINYINT},
      payment_option_map = #{record.paymentOptionMap,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler},
      by_slot_show_one_available_time = #{record.bySlotShowOneAvailableTime,jdbcType=BIT},
      cancellation_policy = #{record.cancellationPolicy,jdbcType=LONGVARCHAR},
      description = #{record.description,jdbcType=LONGVARCHAR},
      prepay_policy = #{record.prepayPolicy,jdbcType=LONGVARCHAR},
      pre_auth_policy = #{record.preAuthPolicy,jdbcType=LONGVARCHAR},
      group_pre_auth_policy = #{record.groupPreAuthPolicy,jdbcType=LONGVARCHAR},
      group_cancellation_policy = #{record.groupCancellationPolicy,jdbcType=LONGVARCHAR},
      group_prepay_policy = #{record.groupPrepayPolicy,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_book_online
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      is_enable = #{record.isEnable,jdbcType=TINYINT},
      max_available_dist = #{record.maxAvailableDist,jdbcType=INTEGER},
      max_available_time = #{record.maxAvailableTime,jdbcType=INTEGER},
      soonest_available = #{record.soonestAvailable,jdbcType=INTEGER},
      farest_available = #{record.farestAvailable,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      is_require_agreement = #{record.isRequireAgreement,jdbcType=TINYINT},
      zip_code = #{record.zipCode,jdbcType=VARCHAR},
      place_name = #{record.placeName,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=VARCHAR},
      state_abbreviation = #{record.stateAbbreviation,jdbcType=VARCHAR},
      county = #{record.county,jdbcType=VARCHAR},
      is_need_address = #{record.isNeedAddress,jdbcType=TINYINT},
      is_need_select_time = #{record.isNeedSelectTime,jdbcType=TINYINT},
      fake_it = #{record.fakeIt,jdbcType=TINYINT},
      enable_no_show_fee = #{record.enableNoShowFee,jdbcType=TINYINT},
      no_show_fee = #{record.noShowFee,jdbcType=DECIMAL},
      appointment_interval = #{record.appointmentInterval,jdbcType=INTEGER},
      timeslot_mins = #{record.timeslotMins,jdbcType=INTEGER},
      timeslot_format = #{record.timeslotFormat,jdbcType=TINYINT},
      accept_client = #{record.acceptClient,jdbcType=TINYINT},
      auto_move_wait = #{record.autoMoveWait,jdbcType=TINYINT},
      service_area_enable = #{record.serviceAreaEnable,jdbcType=TINYINT},
      weight_limit_notify = #{record.weightLimitNotify,jdbcType=TINYINT},
      weight_limit = #{record.weightLimit,jdbcType=INTEGER},
      over_limit_tips = #{record.overLimitTips,jdbcType=VARCHAR},
      need_within_area = #{record.needWithinArea,jdbcType=TINYINT},
      is_by_zipcode = #{record.isByZipcode,jdbcType=TINYINT},
      is_by_radius = #{record.isByRadius,jdbcType=TINYINT},
      setting_location = #{record.settingLocation,jdbcType=VARCHAR},
      setting_lat = #{record.settingLat,jdbcType=VARCHAR},
      setting_lng = #{record.settingLng,jdbcType=VARCHAR},
      is_check_existing_client = #{record.isCheckExistingClient,jdbcType=TINYINT},
      is_redirect = #{record.isRedirect,jdbcType=TINYINT},
      auto_accept = #{record.autoAccept,jdbcType=TINYINT},
      show_one_available_time = #{record.showOneAvailableTime,jdbcType=TINYINT},
      smart_schedule_enable = #{record.smartScheduleEnable,jdbcType=TINYINT},
      smart_schedule_max_dist = #{record.smartScheduleMaxDist,jdbcType=INTEGER},
      smart_schedule_max_time = #{record.smartScheduleMaxTime,jdbcType=INTEGER},
      zip_codes = #{record.zipCodes,jdbcType=VARCHAR},
      service_areas = #{record.serviceAreas,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler},
      book_online_name = #{record.bookOnlineName,jdbcType=VARCHAR},
      allowed_simplify_submit = #{record.allowedSimplifySubmit,jdbcType=TINYINT},
      available_time_type = #{record.availableTimeType,jdbcType=TINYINT},
      by_slot_timeslot_format = #{record.bySlotTimeslotFormat,jdbcType=TINYINT},
      by_slot_timeslot_mins = #{record.bySlotTimeslotMins,jdbcType=INTEGER},
      by_slot_soonest_available = #{record.bySlotSoonestAvailable,jdbcType=INTEGER},
      by_slot_farthest_available = #{record.bySlotFarthestAvailable,jdbcType=INTEGER},
      service_filter = #{record.serviceFilter,jdbcType=TINYINT},
      is_show_categories = #{record.isShowCategories,jdbcType=BIT},
      prepay_type = #{record.prepayType,jdbcType=TINYINT},
      prepay_tip_enable = #{record.prepayTipEnable,jdbcType=TINYINT},
      deposit_type = #{record.depositType,jdbcType=TINYINT},
      deposit_percentage = #{record.depositPercentage,jdbcType=INTEGER},
      deposit_amount = #{record.depositAmount,jdbcType=DECIMAL},
      use_version = #{record.useVersion,jdbcType=TINYINT},
      pre_auth_tip_enable = #{record.preAuthTipEnable,jdbcType=TINYINT},
      auto_refund_deposit = #{record.autoRefundDeposit,jdbcType=TINYINT},
      request_submitted_auto_type = #{record.requestSubmittedAutoType,jdbcType=VARCHAR},
      display_staff_selection_page = #{record.displayStaffSelectionPage,jdbcType=BIT},
      arrival_window_before_min = #{record.arrivalWindowBeforeMin,jdbcType=INTEGER},
      arrival_window_after_min = #{record.arrivalWindowAfterMin,jdbcType=INTEGER},
      booking_range_start_offset = #{record.bookingRangeStartOffset,jdbcType=INTEGER},
      booking_range_end_type = #{record.bookingRangeEndType,jdbcType=TINYINT},
      booking_range_end_offset = #{record.bookingRangeEndOffset,jdbcType=INTEGER},
      booking_range_end_date = #{record.bookingRangeEndDate,jdbcType=VARCHAR},
      is_need_send_renew_notification = #{record.isNeedSendRenewNotification,jdbcType=BIT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      group_payment_type = #{record.groupPaymentType,jdbcType=TINYINT},
      group_filter_rule = #{record.groupFilterRule,jdbcType=CHAR},
      group_prepay_type = #{record.groupPrepayType,jdbcType=TINYINT},
      group_prepay_tip_enable = #{record.groupPrepayTipEnable,jdbcType=TINYINT},
      group_deposit_type = #{record.groupDepositType,jdbcType=TINYINT},
      group_deposit_percentage = #{record.groupDepositPercentage,jdbcType=INTEGER},
      group_deposit_amount = #{record.groupDepositAmount,jdbcType=DECIMAL},
      group_pre_auth_tip_enable = #{record.groupPreAuthTipEnable,jdbcType=TINYINT},
      group_accept_client = #{record.groupAcceptClient,jdbcType=TINYINT},
      new_client_flow_type = #{record.newClientFlowType,jdbcType=INTEGER},
      available_time_sync = #{record.availableTimeSync,jdbcType=TINYINT},
      payment_option_map = #{record.paymentOptionMap,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler},
      by_slot_show_one_available_time = #{record.bySlotShowOneAvailableTime,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBusinessBookOnline">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_book_online
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=TINYINT},
      </if>
      <if test="maxAvailableDist != null">
        max_available_dist = #{maxAvailableDist,jdbcType=INTEGER},
      </if>
      <if test="maxAvailableTime != null">
        max_available_time = #{maxAvailableTime,jdbcType=INTEGER},
      </if>
      <if test="soonestAvailable != null">
        soonest_available = #{soonestAvailable,jdbcType=INTEGER},
      </if>
      <if test="farestAvailable != null">
        farest_available = #{farestAvailable,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="isRequireAgreement != null">
        is_require_agreement = #{isRequireAgreement,jdbcType=TINYINT},
      </if>
      <if test="zipCode != null">
        zip_code = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="placeName != null">
        place_name = #{placeName,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=VARCHAR},
      </if>
      <if test="stateAbbreviation != null">
        state_abbreviation = #{stateAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        county = #{county,jdbcType=VARCHAR},
      </if>
      <if test="isNeedAddress != null">
        is_need_address = #{isNeedAddress,jdbcType=TINYINT},
      </if>
      <if test="isNeedSelectTime != null">
        is_need_select_time = #{isNeedSelectTime,jdbcType=TINYINT},
      </if>
      <if test="fakeIt != null">
        fake_it = #{fakeIt,jdbcType=TINYINT},
      </if>
      <if test="enableNoShowFee != null">
        enable_no_show_fee = #{enableNoShowFee,jdbcType=TINYINT},
      </if>
      <if test="noShowFee != null">
        no_show_fee = #{noShowFee,jdbcType=DECIMAL},
      </if>
      <if test="appointmentInterval != null">
        appointment_interval = #{appointmentInterval,jdbcType=INTEGER},
      </if>
      <if test="timeslotMins != null">
        timeslot_mins = #{timeslotMins,jdbcType=INTEGER},
      </if>
      <if test="timeslotFormat != null">
        timeslot_format = #{timeslotFormat,jdbcType=TINYINT},
      </if>
      <if test="acceptClient != null">
        accept_client = #{acceptClient,jdbcType=TINYINT},
      </if>
      <if test="autoMoveWait != null">
        auto_move_wait = #{autoMoveWait,jdbcType=TINYINT},
      </if>
      <if test="serviceAreaEnable != null">
        service_area_enable = #{serviceAreaEnable,jdbcType=TINYINT},
      </if>
      <if test="weightLimitNotify != null">
        weight_limit_notify = #{weightLimitNotify,jdbcType=TINYINT},
      </if>
      <if test="weightLimit != null">
        weight_limit = #{weightLimit,jdbcType=INTEGER},
      </if>
      <if test="overLimitTips != null">
        over_limit_tips = #{overLimitTips,jdbcType=VARCHAR},
      </if>
      <if test="needWithinArea != null">
        need_within_area = #{needWithinArea,jdbcType=TINYINT},
      </if>
      <if test="isByZipcode != null">
        is_by_zipcode = #{isByZipcode,jdbcType=TINYINT},
      </if>
      <if test="isByRadius != null">
        is_by_radius = #{isByRadius,jdbcType=TINYINT},
      </if>
      <if test="settingLocation != null">
        setting_location = #{settingLocation,jdbcType=VARCHAR},
      </if>
      <if test="settingLat != null">
        setting_lat = #{settingLat,jdbcType=VARCHAR},
      </if>
      <if test="settingLng != null">
        setting_lng = #{settingLng,jdbcType=VARCHAR},
      </if>
      <if test="isCheckExistingClient != null">
        is_check_existing_client = #{isCheckExistingClient,jdbcType=TINYINT},
      </if>
      <if test="isRedirect != null">
        is_redirect = #{isRedirect,jdbcType=TINYINT},
      </if>
      <if test="autoAccept != null">
        auto_accept = #{autoAccept,jdbcType=TINYINT},
      </if>
      <if test="showOneAvailableTime != null">
        show_one_available_time = #{showOneAvailableTime,jdbcType=TINYINT},
      </if>
      <if test="smartScheduleEnable != null">
        smart_schedule_enable = #{smartScheduleEnable,jdbcType=TINYINT},
      </if>
      <if test="smartScheduleMaxDist != null">
        smart_schedule_max_dist = #{smartScheduleMaxDist,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleMaxTime != null">
        smart_schedule_max_time = #{smartScheduleMaxTime,jdbcType=INTEGER},
      </if>
      <if test="zipCodes != null">
        zip_codes = #{zipCodes,jdbcType=VARCHAR},
      </if>
      <if test="serviceAreas != null">
        service_areas = #{serviceAreas,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler},
      </if>
      <if test="bookOnlineName != null">
        book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="allowedSimplifySubmit != null">
        allowed_simplify_submit = #{allowedSimplifySubmit,jdbcType=TINYINT},
      </if>
      <if test="availableTimeType != null">
        available_time_type = #{availableTimeType,jdbcType=TINYINT},
      </if>
      <if test="bySlotTimeslotFormat != null">
        by_slot_timeslot_format = #{bySlotTimeslotFormat,jdbcType=TINYINT},
      </if>
      <if test="bySlotTimeslotMins != null">
        by_slot_timeslot_mins = #{bySlotTimeslotMins,jdbcType=INTEGER},
      </if>
      <if test="bySlotSoonestAvailable != null">
        by_slot_soonest_available = #{bySlotSoonestAvailable,jdbcType=INTEGER},
      </if>
      <if test="bySlotFarthestAvailable != null">
        by_slot_farthest_available = #{bySlotFarthestAvailable,jdbcType=INTEGER},
      </if>
      <if test="serviceFilter != null">
        service_filter = #{serviceFilter,jdbcType=TINYINT},
      </if>
      <if test="isShowCategories != null">
        is_show_categories = #{isShowCategories,jdbcType=BIT},
      </if>
      <if test="prepayType != null">
        prepay_type = #{prepayType,jdbcType=TINYINT},
      </if>
      <if test="prepayTipEnable != null">
        prepay_tip_enable = #{prepayTipEnable,jdbcType=TINYINT},
      </if>
      <if test="depositType != null">
        deposit_type = #{depositType,jdbcType=TINYINT},
      </if>
      <if test="depositPercentage != null">
        deposit_percentage = #{depositPercentage,jdbcType=INTEGER},
      </if>
      <if test="depositAmount != null">
        deposit_amount = #{depositAmount,jdbcType=DECIMAL},
      </if>
      <if test="useVersion != null">
        use_version = #{useVersion,jdbcType=TINYINT},
      </if>
      <if test="preAuthTipEnable != null">
        pre_auth_tip_enable = #{preAuthTipEnable,jdbcType=TINYINT},
      </if>
      <if test="autoRefundDeposit != null">
        auto_refund_deposit = #{autoRefundDeposit,jdbcType=TINYINT},
      </if>
      <if test="requestSubmittedAutoType != null">
        request_submitted_auto_type = #{requestSubmittedAutoType,jdbcType=VARCHAR},
      </if>
      <if test="displayStaffSelectionPage != null">
        display_staff_selection_page = #{displayStaffSelectionPage,jdbcType=BIT},
      </if>
      <if test="arrivalWindowBeforeMin != null">
        arrival_window_before_min = #{arrivalWindowBeforeMin,jdbcType=INTEGER},
      </if>
      <if test="arrivalWindowAfterMin != null">
        arrival_window_after_min = #{arrivalWindowAfterMin,jdbcType=INTEGER},
      </if>
      <if test="bookingRangeStartOffset != null">
        booking_range_start_offset = #{bookingRangeStartOffset,jdbcType=INTEGER},
      </if>
      <if test="bookingRangeEndType != null">
        booking_range_end_type = #{bookingRangeEndType,jdbcType=TINYINT},
      </if>
      <if test="bookingRangeEndOffset != null">
        booking_range_end_offset = #{bookingRangeEndOffset,jdbcType=INTEGER},
      </if>
      <if test="bookingRangeEndDate != null">
        booking_range_end_date = #{bookingRangeEndDate,jdbcType=VARCHAR},
      </if>
      <if test="isNeedSendRenewNotification != null">
        is_need_send_renew_notification = #{isNeedSendRenewNotification,jdbcType=BIT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="groupPaymentType != null">
        group_payment_type = #{groupPaymentType,jdbcType=TINYINT},
      </if>
      <if test="groupFilterRule != null">
        group_filter_rule = #{groupFilterRule,jdbcType=CHAR},
      </if>
      <if test="groupPrepayType != null">
        group_prepay_type = #{groupPrepayType,jdbcType=TINYINT},
      </if>
      <if test="groupPrepayTipEnable != null">
        group_prepay_tip_enable = #{groupPrepayTipEnable,jdbcType=TINYINT},
      </if>
      <if test="groupDepositType != null">
        group_deposit_type = #{groupDepositType,jdbcType=TINYINT},
      </if>
      <if test="groupDepositPercentage != null">
        group_deposit_percentage = #{groupDepositPercentage,jdbcType=INTEGER},
      </if>
      <if test="groupDepositAmount != null">
        group_deposit_amount = #{groupDepositAmount,jdbcType=DECIMAL},
      </if>
      <if test="groupPreAuthTipEnable != null">
        group_pre_auth_tip_enable = #{groupPreAuthTipEnable,jdbcType=TINYINT},
      </if>
      <if test="groupAcceptClient != null">
        group_accept_client = #{groupAcceptClient,jdbcType=TINYINT},
      </if>
      <if test="newClientFlowType != null">
        new_client_flow_type = #{newClientFlowType,jdbcType=INTEGER},
      </if>
      <if test="availableTimeSync != null">
        available_time_sync = #{availableTimeSync,jdbcType=TINYINT},
      </if>
      <if test="paymentOptionMap != null">
        payment_option_map = #{paymentOptionMap,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler},
      </if>
      <if test="bySlotShowOneAvailableTime != null">
        by_slot_show_one_available_time = #{bySlotShowOneAvailableTime,jdbcType=BIT},
      </if>
      <if test="cancellationPolicy != null">
        cancellation_policy = #{cancellationPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="prepayPolicy != null">
        prepay_policy = #{prepayPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="preAuthPolicy != null">
        pre_auth_policy = #{preAuthPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="groupPreAuthPolicy != null">
        group_pre_auth_policy = #{groupPreAuthPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="groupCancellationPolicy != null">
        group_cancellation_policy = #{groupCancellationPolicy,jdbcType=LONGVARCHAR},
      </if>
      <if test="groupPrepayPolicy != null">
        group_prepay_policy = #{groupPrepayPolicy,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBusinessBookOnline">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_book_online
    set business_id = #{businessId,jdbcType=INTEGER},
      is_enable = #{isEnable,jdbcType=TINYINT},
      max_available_dist = #{maxAvailableDist,jdbcType=INTEGER},
      max_available_time = #{maxAvailableTime,jdbcType=INTEGER},
      soonest_available = #{soonestAvailable,jdbcType=INTEGER},
      farest_available = #{farestAvailable,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      is_require_agreement = #{isRequireAgreement,jdbcType=TINYINT},
      zip_code = #{zipCode,jdbcType=VARCHAR},
      place_name = #{placeName,jdbcType=VARCHAR},
      state = #{state,jdbcType=VARCHAR},
      state_abbreviation = #{stateAbbreviation,jdbcType=VARCHAR},
      county = #{county,jdbcType=VARCHAR},
      is_need_address = #{isNeedAddress,jdbcType=TINYINT},
      is_need_select_time = #{isNeedSelectTime,jdbcType=TINYINT},
      fake_it = #{fakeIt,jdbcType=TINYINT},
      enable_no_show_fee = #{enableNoShowFee,jdbcType=TINYINT},
      no_show_fee = #{noShowFee,jdbcType=DECIMAL},
      appointment_interval = #{appointmentInterval,jdbcType=INTEGER},
      timeslot_mins = #{timeslotMins,jdbcType=INTEGER},
      timeslot_format = #{timeslotFormat,jdbcType=TINYINT},
      accept_client = #{acceptClient,jdbcType=TINYINT},
      auto_move_wait = #{autoMoveWait,jdbcType=TINYINT},
      service_area_enable = #{serviceAreaEnable,jdbcType=TINYINT},
      weight_limit_notify = #{weightLimitNotify,jdbcType=TINYINT},
      weight_limit = #{weightLimit,jdbcType=INTEGER},
      over_limit_tips = #{overLimitTips,jdbcType=VARCHAR},
      need_within_area = #{needWithinArea,jdbcType=TINYINT},
      is_by_zipcode = #{isByZipcode,jdbcType=TINYINT},
      is_by_radius = #{isByRadius,jdbcType=TINYINT},
      setting_location = #{settingLocation,jdbcType=VARCHAR},
      setting_lat = #{settingLat,jdbcType=VARCHAR},
      setting_lng = #{settingLng,jdbcType=VARCHAR},
      is_check_existing_client = #{isCheckExistingClient,jdbcType=TINYINT},
      is_redirect = #{isRedirect,jdbcType=TINYINT},
      auto_accept = #{autoAccept,jdbcType=TINYINT},
      show_one_available_time = #{showOneAvailableTime,jdbcType=TINYINT},
      smart_schedule_enable = #{smartScheduleEnable,jdbcType=TINYINT},
      smart_schedule_max_dist = #{smartScheduleMaxDist,jdbcType=INTEGER},
      smart_schedule_max_time = #{smartScheduleMaxTime,jdbcType=INTEGER},
      zip_codes = #{zipCodes,jdbcType=VARCHAR},
      service_areas = #{serviceAreas,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler},
      book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
      allowed_simplify_submit = #{allowedSimplifySubmit,jdbcType=TINYINT},
      available_time_type = #{availableTimeType,jdbcType=TINYINT},
      by_slot_timeslot_format = #{bySlotTimeslotFormat,jdbcType=TINYINT},
      by_slot_timeslot_mins = #{bySlotTimeslotMins,jdbcType=INTEGER},
      by_slot_soonest_available = #{bySlotSoonestAvailable,jdbcType=INTEGER},
      by_slot_farthest_available = #{bySlotFarthestAvailable,jdbcType=INTEGER},
      service_filter = #{serviceFilter,jdbcType=TINYINT},
      is_show_categories = #{isShowCategories,jdbcType=BIT},
      prepay_type = #{prepayType,jdbcType=TINYINT},
      prepay_tip_enable = #{prepayTipEnable,jdbcType=TINYINT},
      deposit_type = #{depositType,jdbcType=TINYINT},
      deposit_percentage = #{depositPercentage,jdbcType=INTEGER},
      deposit_amount = #{depositAmount,jdbcType=DECIMAL},
      use_version = #{useVersion,jdbcType=TINYINT},
      pre_auth_tip_enable = #{preAuthTipEnable,jdbcType=TINYINT},
      auto_refund_deposit = #{autoRefundDeposit,jdbcType=TINYINT},
      request_submitted_auto_type = #{requestSubmittedAutoType,jdbcType=VARCHAR},
      display_staff_selection_page = #{displayStaffSelectionPage,jdbcType=BIT},
      arrival_window_before_min = #{arrivalWindowBeforeMin,jdbcType=INTEGER},
      arrival_window_after_min = #{arrivalWindowAfterMin,jdbcType=INTEGER},
      booking_range_start_offset = #{bookingRangeStartOffset,jdbcType=INTEGER},
      booking_range_end_type = #{bookingRangeEndType,jdbcType=TINYINT},
      booking_range_end_offset = #{bookingRangeEndOffset,jdbcType=INTEGER},
      booking_range_end_date = #{bookingRangeEndDate,jdbcType=VARCHAR},
      is_need_send_renew_notification = #{isNeedSendRenewNotification,jdbcType=BIT},
      company_id = #{companyId,jdbcType=BIGINT},
      group_payment_type = #{groupPaymentType,jdbcType=TINYINT},
      group_filter_rule = #{groupFilterRule,jdbcType=CHAR},
      group_prepay_type = #{groupPrepayType,jdbcType=TINYINT},
      group_prepay_tip_enable = #{groupPrepayTipEnable,jdbcType=TINYINT},
      group_deposit_type = #{groupDepositType,jdbcType=TINYINT},
      group_deposit_percentage = #{groupDepositPercentage,jdbcType=INTEGER},
      group_deposit_amount = #{groupDepositAmount,jdbcType=DECIMAL},
      group_pre_auth_tip_enable = #{groupPreAuthTipEnable,jdbcType=TINYINT},
      group_accept_client = #{groupAcceptClient,jdbcType=TINYINT},
      new_client_flow_type = #{newClientFlowType,jdbcType=INTEGER},
      available_time_sync = #{availableTimeSync,jdbcType=TINYINT},
      payment_option_map = #{paymentOptionMap,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler},
      by_slot_show_one_available_time = #{bySlotShowOneAvailableTime,jdbcType=BIT},
      cancellation_policy = #{cancellationPolicy,jdbcType=LONGVARCHAR},
      description = #{description,jdbcType=LONGVARCHAR},
      prepay_policy = #{prepayPolicy,jdbcType=LONGVARCHAR},
      pre_auth_policy = #{preAuthPolicy,jdbcType=LONGVARCHAR},
      group_pre_auth_policy = #{groupPreAuthPolicy,jdbcType=LONGVARCHAR},
      group_cancellation_policy = #{groupCancellationPolicy,jdbcType=LONGVARCHAR},
      group_prepay_policy = #{groupPrepayPolicy,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBusinessBookOnline">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_book_online
    set business_id = #{businessId,jdbcType=INTEGER},
      is_enable = #{isEnable,jdbcType=TINYINT},
      max_available_dist = #{maxAvailableDist,jdbcType=INTEGER},
      max_available_time = #{maxAvailableTime,jdbcType=INTEGER},
      soonest_available = #{soonestAvailable,jdbcType=INTEGER},
      farest_available = #{farestAvailable,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      is_require_agreement = #{isRequireAgreement,jdbcType=TINYINT},
      zip_code = #{zipCode,jdbcType=VARCHAR},
      place_name = #{placeName,jdbcType=VARCHAR},
      state = #{state,jdbcType=VARCHAR},
      state_abbreviation = #{stateAbbreviation,jdbcType=VARCHAR},
      county = #{county,jdbcType=VARCHAR},
      is_need_address = #{isNeedAddress,jdbcType=TINYINT},
      is_need_select_time = #{isNeedSelectTime,jdbcType=TINYINT},
      fake_it = #{fakeIt,jdbcType=TINYINT},
      enable_no_show_fee = #{enableNoShowFee,jdbcType=TINYINT},
      no_show_fee = #{noShowFee,jdbcType=DECIMAL},
      appointment_interval = #{appointmentInterval,jdbcType=INTEGER},
      timeslot_mins = #{timeslotMins,jdbcType=INTEGER},
      timeslot_format = #{timeslotFormat,jdbcType=TINYINT},
      accept_client = #{acceptClient,jdbcType=TINYINT},
      auto_move_wait = #{autoMoveWait,jdbcType=TINYINT},
      service_area_enable = #{serviceAreaEnable,jdbcType=TINYINT},
      weight_limit_notify = #{weightLimitNotify,jdbcType=TINYINT},
      weight_limit = #{weightLimit,jdbcType=INTEGER},
      over_limit_tips = #{overLimitTips,jdbcType=VARCHAR},
      need_within_area = #{needWithinArea,jdbcType=TINYINT},
      is_by_zipcode = #{isByZipcode,jdbcType=TINYINT},
      is_by_radius = #{isByRadius,jdbcType=TINYINT},
      setting_location = #{settingLocation,jdbcType=VARCHAR},
      setting_lat = #{settingLat,jdbcType=VARCHAR},
      setting_lng = #{settingLng,jdbcType=VARCHAR},
      is_check_existing_client = #{isCheckExistingClient,jdbcType=TINYINT},
      is_redirect = #{isRedirect,jdbcType=TINYINT},
      auto_accept = #{autoAccept,jdbcType=TINYINT},
      show_one_available_time = #{showOneAvailableTime,jdbcType=TINYINT},
      smart_schedule_enable = #{smartScheduleEnable,jdbcType=TINYINT},
      smart_schedule_max_dist = #{smartScheduleMaxDist,jdbcType=INTEGER},
      smart_schedule_max_time = #{smartScheduleMaxTime,jdbcType=INTEGER},
      zip_codes = #{zipCodes,jdbcType=VARCHAR},
      service_areas = #{serviceAreas,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler},
      book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
      allowed_simplify_submit = #{allowedSimplifySubmit,jdbcType=TINYINT},
      available_time_type = #{availableTimeType,jdbcType=TINYINT},
      by_slot_timeslot_format = #{bySlotTimeslotFormat,jdbcType=TINYINT},
      by_slot_timeslot_mins = #{bySlotTimeslotMins,jdbcType=INTEGER},
      by_slot_soonest_available = #{bySlotSoonestAvailable,jdbcType=INTEGER},
      by_slot_farthest_available = #{bySlotFarthestAvailable,jdbcType=INTEGER},
      service_filter = #{serviceFilter,jdbcType=TINYINT},
      is_show_categories = #{isShowCategories,jdbcType=BIT},
      prepay_type = #{prepayType,jdbcType=TINYINT},
      prepay_tip_enable = #{prepayTipEnable,jdbcType=TINYINT},
      deposit_type = #{depositType,jdbcType=TINYINT},
      deposit_percentage = #{depositPercentage,jdbcType=INTEGER},
      deposit_amount = #{depositAmount,jdbcType=DECIMAL},
      use_version = #{useVersion,jdbcType=TINYINT},
      pre_auth_tip_enable = #{preAuthTipEnable,jdbcType=TINYINT},
      auto_refund_deposit = #{autoRefundDeposit,jdbcType=TINYINT},
      request_submitted_auto_type = #{requestSubmittedAutoType,jdbcType=VARCHAR},
      display_staff_selection_page = #{displayStaffSelectionPage,jdbcType=BIT},
      arrival_window_before_min = #{arrivalWindowBeforeMin,jdbcType=INTEGER},
      arrival_window_after_min = #{arrivalWindowAfterMin,jdbcType=INTEGER},
      booking_range_start_offset = #{bookingRangeStartOffset,jdbcType=INTEGER},
      booking_range_end_type = #{bookingRangeEndType,jdbcType=TINYINT},
      booking_range_end_offset = #{bookingRangeEndOffset,jdbcType=INTEGER},
      booking_range_end_date = #{bookingRangeEndDate,jdbcType=VARCHAR},
      is_need_send_renew_notification = #{isNeedSendRenewNotification,jdbcType=BIT},
      company_id = #{companyId,jdbcType=BIGINT},
      group_payment_type = #{groupPaymentType,jdbcType=TINYINT},
      group_filter_rule = #{groupFilterRule,jdbcType=CHAR},
      group_prepay_type = #{groupPrepayType,jdbcType=TINYINT},
      group_prepay_tip_enable = #{groupPrepayTipEnable,jdbcType=TINYINT},
      group_deposit_type = #{groupDepositType,jdbcType=TINYINT},
      group_deposit_percentage = #{groupDepositPercentage,jdbcType=INTEGER},
      group_deposit_amount = #{groupDepositAmount,jdbcType=DECIMAL},
      group_pre_auth_tip_enable = #{groupPreAuthTipEnable,jdbcType=TINYINT},
      group_accept_client = #{groupAcceptClient,jdbcType=TINYINT},
      new_client_flow_type = #{newClientFlowType,jdbcType=INTEGER},
      available_time_sync = #{availableTimeSync,jdbcType=TINYINT},
      payment_option_map = #{paymentOptionMap,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler},
      by_slot_show_one_available_time = #{bySlotShowOneAvailableTime,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <sql id="Payment_Group_Setting">
    (group_accept_client, group_filter_rule,
    group_payment_type, group_prepay_type, group_prepay_tip_enable, group_deposit_type,
    group_deposit_percentage, group_deposit_amount, group_pre_auth_tip_enable,group_prepay_policy,
    group_pre_auth_policy, group_cancellation_policy)
  </sql>
    <select id="selectByBusinessId" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from moe_business_book_online
        where business_id = #{businessId,jdbcType=INTEGER}
    </select>
  <select id="getBusinessBookOnlineList" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_business_book_online
    where business_id in
    <foreach close=")" collection="businessIdList" item="businessId" open="(" separator=",">
      #{businessId,jdbcType=INTEGER}
    </foreach>
  </select>
    <select id="selectByBookOnlineName" resultType="com.moego.server.grooming.mapper.po.BusinessCompanyPO">
        select business_id, company_id
        from moe_business_book_online
        where book_online_name = #{bookOnlineName,jdbcType=INTEGER} limit 1
    </select>
    <update id="updateInfoByPrimaryIdOrBusinessId">
        update moe_business_book_online
        <set>
            <if test="businessId != null">
                business_id = #{businessId,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null">
                is_enable = #{isEnable,jdbcType=TINYINT},
            </if>
            <if test="bookOnlineName != null">
                book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
            </if>
            <if test="maxAvailableDist != null">
                max_available_dist = #{maxAvailableDist,jdbcType=INTEGER},
            </if>
            <if test="maxAvailableTime != null">
                max_available_time = #{maxAvailableTime,jdbcType=INTEGER},
            </if>
            <if test="soonestAvailable != null">
                soonest_available = #{soonestAvailable,jdbcType=INTEGER},
            </if>
            <if test="farestAvailable != null">
                farest_available = #{farestAvailable,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="zipCode != null">
                zip_code = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="placeName != null">
                place_name = #{placeName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=VARCHAR},
            </if>
            <if test="stateAbbreviation != null">
                state_abbreviation = #{stateAbbreviation,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                county = #{county,jdbcType=VARCHAR},
            </if>
            <if test="isNeedAddress != null">
                is_need_address = #{isNeedAddress,jdbcType=TINYINT},
            </if>
            <if test="isNeedSelectTime != null">
                is_need_select_time = #{isNeedSelectTime,jdbcType=TINYINT},
            </if>
            <if test="fakeIt != null">
                fake_it = #{fakeIt,jdbcType=TINYINT},
            </if>
            <if test="enableNoShowFee != null">
                enable_no_show_fee = #{enableNoShowFee,jdbcType=TINYINT},
            </if>
            <if test="noShowFee != null">
                no_show_fee = #{noShowFee,jdbcType=DECIMAL},
            </if>
            <if test="appointmentInterval != null">
                appointment_interval = #{appointmentInterval,jdbcType=INTEGER},
            </if>
            <if test="timeslotMins != null">
                timeslot_mins = #{timeslotMins,jdbcType=INTEGER},
            </if>
            <if test="timeslotFormat != null">
                timeslot_format = #{timeslotFormat,jdbcType=TINYINT},
            </if>
            <if test="acceptClient != null">
                accept_client = #{acceptClient,jdbcType=TINYINT},
            </if>
            <if test="autoMoveWait != null">
                auto_move_wait = #{autoMoveWait,jdbcType=TINYINT},
            </if>
            <if test="serviceAreaEnable != null">
                service_area_enable = #{serviceAreaEnable,jdbcType=TINYINT},
            </if>
            <if test="weightLimitNotify != null">
                weight_limit_notify = #{weightLimitNotify,jdbcType=TINYINT},
            </if>
            <if test="weightLimit != null">
                weight_limit = #{weightLimit,jdbcType=INTEGER},
            </if>
            <if test="overLimitTips != null">
                over_limit_tips = #{overLimitTips,jdbcType=VARCHAR},
            </if>
            <if test="needWithinArea != null">
                need_within_area = #{needWithinArea,jdbcType=TINYINT},
            </if>
            <if test="isByZipcode != null">
                is_by_zipcode = #{isByZipcode,jdbcType=TINYINT},
            </if>
            <if test="isByRadius != null">
                is_by_radius = #{isByRadius,jdbcType=TINYINT},
            </if>
            <if test="settingLocation != null">
                setting_location = #{settingLocation,jdbcType=VARCHAR},
            </if>
            <if test="settingLat != null">
                setting_lat = #{settingLat,jdbcType=VARCHAR},
            </if>
            <if test="settingLng != null">
                setting_lng = #{settingLng,jdbcType=VARCHAR},
            </if>
            <if test="isCheckExistingClient != null">
                is_check_existing_client = #{isCheckExistingClient,jdbcType=TINYINT},
            </if>
            <if test="isRedirect != null">
                is_redirect = #{isRedirect,jdbcType=TINYINT},
            </if>
            <if test="autoAccept != null">
                auto_accept = #{autoAccept,jdbcType=TINYINT},
            </if>
            <if test="showOneAvailableTime != null">
                show_one_available_time = #{showOneAvailableTime,jdbcType=TINYINT},
            </if>
            <if test="bySlotShowOneAvailableTime != null">
              by_slot_show_one_available_time = #{bySlotShowOneAvailableTime,jdbcType=BIT},
            </if>
            <if test="smartScheduleEnable != null">
                smart_schedule_enable = #{smartScheduleEnable,jdbcType=TINYINT},
            </if>
            <if test="smartScheduleMaxDist != null">
                smart_schedule_max_dist = #{smartScheduleMaxDist,jdbcType=INTEGER},
            </if>
            <if test="smartScheduleMaxTime != null">
                smart_schedule_max_time = #{smartScheduleMaxTime,jdbcType=INTEGER},
            </if>
            <if test="zipCodes != null">
                zip_codes = #{zipCodes,jdbcType=VARCHAR},
            </if>
            <if test="serviceAreas != null">
              service_areas = #{serviceAreas,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.IntListTypeHandler},
            </if>
            <if test="cancellationPolicy != null">
                cancellation_policy = #{cancellationPolicy,jdbcType=LONGVARCHAR},
            </if>
            <if test="description != null">
                description =  #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="allowedSimplifySubmit != null">
                allowed_simplify_submit = #{allowedSimplifySubmit,jdbcType=TINYINT},
            </if>
            <if test="availableTimeType != null">
                available_time_type = #{availableTimeType,jdbcType=TINYINT},
            </if>
            <if test="availableTimeSync != null">
                available_time_sync = #{availableTimeSync,jdbcType=TINYINT},
            </if>
            <if test="bySlotTimeslotFormat != null">
                by_slot_timeslot_format = #{bySlotTimeslotFormat,jdbcType=TINYINT},
            </if>
            <if test="bySlotTimeslotMins != null">
                by_slot_timeslot_mins = #{bySlotTimeslotMins,jdbcType=INTEGER},
            </if>
            <if test="bySlotSoonestAvailable != null">
                by_slot_soonest_available = #{bySlotSoonestAvailable,jdbcType=INTEGER},
            </if>
            <if test="bySlotFarthestAvailable != null">
                by_slot_farthest_available = #{bySlotFarthestAvailable,jdbcType=INTEGER},
            </if>
            <if test="serviceFilter != null">
                service_filter = #{serviceFilter,jdbcType=TINYINT},
            </if>
            <if test="prepayType != null">
                prepay_type = #{prepayType,jdbcType=TINYINT},
            </if>
            <if test="prepayTipEnable != null">
                prepay_tip_enable = #{prepayTipEnable,jdbcType=TINYINT},
            </if>
            <if test="depositType != null">
                deposit_type = #{depositType,jdbcType=TINYINT},
            </if>
            <if test="depositPercentage != null">
                deposit_percentage = #{depositPercentage,jdbcType=INTEGER},
            </if>
            <if test="depositAmount != null">
                deposit_amount = #{depositAmount,jdbcType=DECIMAL},
            </if>
            <if test="prepayPolicy != null">
                prepay_policy = #{prepayPolicy,jdbcType=LONGVARCHAR},
            </if>
            <if test="useVersion != null">
                use_version = #{useVersion,jdbcType=TINYINT},
            </if>
            <if test="preAuthTipEnable != null">
                pre_auth_tip_enable = #{preAuthTipEnable,jdbcType=TINYINT},
            </if>
            <if test="preAuthPolicy != null">
                pre_auth_policy = #{preAuthPolicy,jdbcType=LONGVARCHAR},
            </if>
            <if test="requestSubmittedAutoType != null">
                request_submitted_auto_type = #{requestSubmittedAutoType,jdbcType=VARCHAR},
            </if>
            <if test="displayStaffSelectionPage != null">
                display_staff_selection_page = #{displayStaffSelectionPage},
            </if>
            <if test="arrivalWindowBeforeMin != null">
                arrival_window_before_min = #{arrivalWindowBeforeMin},
            </if>
            <if test="arrivalWindowAfterMin != null">
                arrival_window_after_min = #{arrivalWindowAfterMin},
            </if>
            <if test="bookingRangeStartOffset != null">
                booking_range_start_offset = #{bookingRangeStartOffset},
            </if>
            <if test="bookingRangeEndType != null">
                booking_range_end_type = #{bookingRangeEndType},
            </if>
            <if test="bookingRangeEndOffset != null">
                booking_range_end_offset = #{bookingRangeEndOffset},
            </if>
            <if test="bookingRangeEndDate != null">
                booking_range_end_date = #{bookingRangeEndDate}
            </if>
            <if test="newClientFlowType != null">
              new_client_flow_type = #{newClientFlowType}
            </if>
            <if test="isShowCategories != null">
              is_show_categories = #{isShowCategories,jdbcType=TINYINT},
            </if>

        </set>
        where 1=1
        <if test="id != null and id != 0">
            and id = #{id,jdbcType=INTEGER}
        </if>
        <if test="businessId != null and businessId != 0">
            and business_id = #{businessId,jdbcType=INTEGER}
        </if>
    </update>
    <update id="updateInfoByBusinessId">
      update moe_business_book_online
      <set>
        <if test="isEnable != null">
          is_enable = #{isEnable,jdbcType=TINYINT},
        </if>
        <if test="bookOnlineName != null">
          book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
        </if>
        <if test="maxAvailableDist != null">
          max_available_dist = #{maxAvailableDist,jdbcType=INTEGER},
        </if>
        <if test="maxAvailableTime != null">
          max_available_time = #{maxAvailableTime,jdbcType=INTEGER},
        </if>
        <if test="soonestAvailable != null">
          soonest_available = #{soonestAvailable,jdbcType=INTEGER},
        </if>
        <if test="farestAvailable != null">
          farest_available = #{farestAvailable,jdbcType=INTEGER},
        </if>
        <if test="createTime != null">
          create_time = #{createTime,jdbcType=BIGINT},
        </if>
        <if test="updateTime != null">
          update_time = #{updateTime,jdbcType=BIGINT},
        </if>
        <if test="zipCode != null">
          zip_code = #{zipCode,jdbcType=VARCHAR},
        </if>
        <if test="placeName != null">
          place_name = #{placeName,jdbcType=VARCHAR},
        </if>
        <if test="state != null">
          state = #{state,jdbcType=VARCHAR},
        </if>
        <if test="stateAbbreviation != null">
          state_abbreviation = #{stateAbbreviation,jdbcType=VARCHAR},
        </if>
        <if test="county != null">
          county = #{county,jdbcType=VARCHAR},
        </if>
        <if test="isNeedAddress != null">
          is_need_address = #{isNeedAddress,jdbcType=TINYINT},
        </if>
        <if test="isNeedSelectTime != null">
          is_need_select_time = #{isNeedSelectTime,jdbcType=TINYINT},
        </if>
        <if test="fakeIt != null">
          fake_it = #{fakeIt,jdbcType=TINYINT},
        </if>
        <if test="enableNoShowFee != null">
          enable_no_show_fee = #{enableNoShowFee,jdbcType=TINYINT},
        </if>
        <if test="noShowFee != null">
          no_show_fee = #{noShowFee,jdbcType=DECIMAL},
        </if>
        <if test="appointmentInterval != null">
          appointment_interval = #{appointmentInterval,jdbcType=INTEGER},
        </if>
        <if test="timeslotMins != null">
          timeslot_mins = #{timeslotMins,jdbcType=INTEGER},
        </if>
        <if test="timeslotFormat != null">
          timeslot_format = #{timeslotFormat,jdbcType=TINYINT},
        </if>
        <if test="acceptClient != null">
          accept_client = #{acceptClient,jdbcType=TINYINT},
        </if>
        <if test="autoMoveWait != null">
          auto_move_wait = #{autoMoveWait,jdbcType=TINYINT},
        </if>
        <if test="serviceAreaEnable != null">
          service_area_enable = #{serviceAreaEnable,jdbcType=TINYINT},
        </if>
        <if test="weightLimitNotify != null">
          weight_limit_notify = #{weightLimitNotify,jdbcType=TINYINT},
        </if>
        <if test="weightLimit != null">
          weight_limit = #{weightLimit,jdbcType=INTEGER},
        </if>
        <if test="overLimitTips != null">
          over_limit_tips = #{overLimitTips,jdbcType=VARCHAR},
        </if>
        <if test="needWithinArea != null">
          need_within_area = #{needWithinArea,jdbcType=TINYINT},
        </if>
        <if test="isByZipcode != null">
          is_by_zipcode = #{isByZipcode,jdbcType=TINYINT},
        </if>
        <if test="isByRadius != null">
          is_by_radius = #{isByRadius,jdbcType=TINYINT},
        </if>
        <if test="settingLocation != null">
          setting_location = #{settingLocation,jdbcType=VARCHAR},
        </if>
        <if test="settingLat != null">
          setting_lat = #{settingLat,jdbcType=VARCHAR},
        </if>
        <if test="settingLng != null">
          setting_lng = #{settingLng,jdbcType=VARCHAR},
        </if>
        <if test="isCheckExistingClient != null">
          is_check_existing_client = #{isCheckExistingClient,jdbcType=TINYINT},
        </if>
        <if test="isRedirect != null">
          is_redirect = #{isRedirect,jdbcType=TINYINT},
        </if>
        <if test="autoAccept != null">
          auto_accept = #{autoAccept,jdbcType=TINYINT},
        </if>
        <if test="showOneAvailableTime != null">
          show_one_available_time = #{showOneAvailableTime,jdbcType=TINYINT},
        </if>
        <if test="bySlotShowOneAvailableTime != null">
          by_slot_show_one_available_time = #{bySlotShowOneAvailableTime,jdbcType=BIT},
        </if>
        <if test="smartScheduleEnable != null">
          smart_schedule_enable = #{smartScheduleEnable,jdbcType=TINYINT},
        </if>
        <if test="smartScheduleMaxDist != null">
          smart_schedule_max_dist = #{smartScheduleMaxDist,jdbcType=INTEGER},
        </if>
        <if test="smartScheduleMaxTime != null">
          smart_schedule_max_time = #{smartScheduleMaxTime,jdbcType=INTEGER},
        </if>
        <if test="zipCodes != null">
          zip_codes = #{zipCodes,jdbcType=VARCHAR},
        </if>
        <if test="cancellationPolicy != null">
          cancellation_policy = #{cancellationPolicy,jdbcType=LONGVARCHAR},
        </if>
        <if test="description != null">
          description = #{description,jdbcType=LONGVARCHAR},
        </if>
        <if test="allowedSimplifySubmit != null">
          allowed_simplify_submit = #{allowedSimplifySubmit,jdbcType=TINYINT},
        </if>
        <if test="availableTimeType != null">
          available_time_type = #{availableTimeType,jdbcType=TINYINT},
        </if>
        <if test="bySlotTimeslotFormat != null">
          by_slot_timeslot_format = #{bySlotTimeslotFormat,jdbcType=TINYINT},
        </if>
        <if test="bySlotTimeslotMins != null">
          by_slot_timeslot_mins = #{bySlotTimeslotMins,jdbcType=INTEGER},
        </if>
        <if test="bySlotSoonestAvailable != null">
          by_slot_soonest_available = #{bySlotSoonestAvailable,jdbcType=INTEGER},
        </if>
        <if test="bySlotFarthestAvailable != null">
          by_slot_farthest_available = #{bySlotFarthestAvailable,jdbcType=INTEGER},
        </if>
        <if test="serviceFilter != null">
          service_filter = #{serviceFilter,jdbcType=TINYINT},
        </if>
        <if test="prepayType != null">
          prepay_type = #{prepayType,jdbcType=TINYINT},
        </if>
        <if test="prepayTipEnable != null">
          prepay_tip_enable = #{prepayTipEnable,jdbcType=TINYINT},
        </if>
        <if test="depositType != null">
          deposit_type = #{depositType,jdbcType=TINYINT},
        </if>
        <if test="depositPercentage != null">
          deposit_percentage = #{depositPercentage,jdbcType=INTEGER},
        </if>
        <if test="depositAmount != null">
          deposit_amount = #{depositAmount,jdbcType=DECIMAL},
        </if>
        <if test="prepayPolicy != null">
          prepay_policy = #{prepayPolicy,jdbcType=LONGVARCHAR},
        </if>
        <if test="useVersion != null">
          use_version = #{useVersion,jdbcType=TINYINT},
        </if>
        <if test="requestSubmittedAutoType != null">
          request_submitted_auto_type = #{requestSubmittedAutoType,jdbcType=VARCHAR},
        </if>
      </set>
      where business_id = #{businessId,jdbcType=INTEGER}
    </update>


  <select id="listAllBookOnlineName" resultType="com.moego.server.grooming.dto.ob.BookOnlineConfigDTO">
    select
        business_id businessId,
        book_online_name bookOnlineName,
        is_enable isEnable,
        use_version useVersion,
        company_id companyId
    FROM
        moe_business_book_online
  </select>

  <delete id="deletePaymentGroupSetting">
    update moe_business_book_online set
      group_accept_client=null, group_filter_rule=null, group_payment_type=null
    where business_id=#{businessId}
  </delete>

  <select id="listOnlineBookingV3Legacies" resultMap="BaseResultMap">
    select
        mbbo.*
    from
        moe_business_book_online mbbo
            left join moe_book_online_landing_page_config mbolpc on mbbo.business_id = mbolpc.business_id
    where mbbo.use_version = 2 and mbolpc.business_id is null
  </select>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from
        moe_business_book_online
  </select>
  <select id="selectByIsEnable"
          resultType="com.moego.server.grooming.mapper.po.BusinessCompanyPO">
    select business_id, company_id
    from moe_business_book_online
    where is_enable = 1
    and company_id != 0
  </select>
  <select id="selectAllCompanyIdBusinessId"
          resultType="com.moego.server.grooming.mapper.po.BusinessCompanyPO">
    select business_id, company_id
    from moe_business_book_online
    where company_id != 0
  </select>
</mapper>
