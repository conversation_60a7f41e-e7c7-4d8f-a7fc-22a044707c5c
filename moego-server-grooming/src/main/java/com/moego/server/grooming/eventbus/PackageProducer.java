package com.moego.server.grooming.eventbus;

import com.google.protobuf.util.Timestamps;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.idl.models.event_bus.v1.EventType;
import com.moego.idl.models.event_bus.v1.PackageRedeemedEvent;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.lib.event_bus.producer.Producer;
import com.moego.server.grooming.mapper.MoeGroomingPackageHistoryMapper;
import com.moego.server.grooming.mapper.MoeGroomingPackageMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageHistoryExample;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@AllArgsConstructor
public class PackageProducer {
    private final Producer producer;
    private static final String PACKAGE_EVENT_TOPIC = "moego.erp.package";
    private final MoeGroomingPackageMapper moeGroomingPackageMapper;
    private final MoeGroomingPackageHistoryMapper moeGroomingPackageHistoryMapper;

    public void pushPackageRedeemedEvent(List<Integer> historiesIds, long useTime) {
        if (CollectionUtils.isEmpty(historiesIds)) {
            return;
        }
        ThreadPool.execute(() -> {
            producer.send(PACKAGE_EVENT_TOPIC, buildPackageRedeemedEvent(historiesIds, useTime));
        });
    }

    private EventRecord buildPackageRedeemedEvent(List<Integer> historiesIds, long useTime) {
        var example = new MoeGroomingPackageHistoryExample();
        example.createCriteria().andIdIn(historiesIds);
        var historyList = moeGroomingPackageHistoryMapper.selectByExample(example);
        var beanPackageInfo =
                moeGroomingPackageMapper.selectByPrimaryKey(historyList.get(0).getPackageId());
        Map<Integer, List<Integer>> phIdsByServiceIdMap = historyList.stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingPackageHistory::getPackageId,
                        Collectors.mapping(MoeGroomingPackageHistory::getId, Collectors.toList())));

        List<PackageRedeemedEvent.GroomingPackageHistory> recordList = new ArrayList<>();
        for (var mapEntry : phIdsByServiceIdMap.entrySet()) {
            long GroomingPackageId = mapEntry.getKey();
            recordList.add(PackageRedeemedEvent.GroomingPackageHistory.newBuilder()
                    .setGroomingPackageId(GroomingPackageId)
                    .addAllPackageHistoryId(
                            mapEntry.getValue().stream().map(Integer::longValue).collect(Collectors.toList()))
                    .build());
        }
        return EventRecord.builder()
                .id(String.valueOf(beanPackageInfo.getCustomerId()))
                .time(Instant.ofEpochSecond(useTime))
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(beanPackageInfo.getCompanyId())
                                .setBusinessId(beanPackageInfo.getBusinessId())
                                .build())
                        .setPackageRedeemedEvent(PackageRedeemedEvent.newBuilder()
                                .setCustomerId(beanPackageInfo.getCustomerId())
                                .addAllGroomingPackages(recordList)
                                .setUsedTime(Timestamps.fromSeconds(useTime))
                                .build())
                        .build())
                .type(EventType.PACKAGE_REDEEMED)
                .build();
    }
}
