package com.moego.server.grooming.mapstruct;

import static java.util.stream.Collectors.toSet;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.grooming.dto.AbandonRecordDTO;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.param.SearchAbandonedRecordParam;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.web.params.SearchAbandonedClientParam;
import com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO;
import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Mapper(
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        imports = {OBStepEnum.class, List.class, ServiceItemEnum.class})
public interface AbandonRecordMapper {
    AbandonRecordMapper INSTANCE = Mappers.getMapper(AbandonRecordMapper.class);

    @Mapping(target = "abandonStep", expression = "java(OBStepEnum.valueOf(abandonRecord.getAbandonStep()))")
    @Mapping(target = "customer", expression = "java(entity2ClientDetailVO(abandonRecord))")
    @Mapping(target = "preference", expression = "java(entity2PreferenceVO(abandonRecord))")
    @Mapping(target = "address", expression = "java(entity2AddressDetailVO(abandonRecord))")
    @Mapping(target = "careType", expression = "java(ServiceItemEnum.valueOf(abandonRecord.getCareType()))")
    AbandonClientRecordVO entity2VO(MoeBookOnlineAbandonRecord abandonRecord);

    AbandonRecordDTO entity2DTO(MoeBookOnlineAbandonRecord entity);

    MoeBookOnlineAbandonRecord dtoToEntity(AbandonRecordDTO dto);

    @Mapping(target = "questionAnswerList", expression = "java(List.of())")
    AbandonClientRecordVO.ClientDetailVO entity2ClientDetailVO(MoeBookOnlineAbandonRecord abandonRecord);

    @Mapping(target = "preferredDay", expression = "java(string2List(abandonRecord.getPreferredDay()))")
    @Mapping(target = "preferredTime", expression = "java(string2List(abandonRecord.getPreferredTime()))")
    AbandonClientRecordVO.ClientPreferenceVO entity2PreferenceVO(MoeBookOnlineAbandonRecord abandonRecord);

    default List<Integer> string2List(String str) {
        return StringUtils.hasText(str) ? JsonUtil.toList(str, Integer.class) : List.of();
    }

    AbandonClientRecordVO.AddressDetailVO entity2AddressDetailVO(MoeBookOnlineAbandonRecord abandonRecord);

    @Mapping(target = "customerId", source = "id")
    @Mapping(target = "questionAnswerList", expression = "java(List.of())")
    AbandonClientRecordVO.ClientDetailVO dto2ClientDetailVO(CustomerInfoDto customerInfoDto);

    @Mapping(target = "addressId", source = "customerAddressId")
    AbandonClientRecordVO.AddressDetailVO dto2AddressDetailVO(CustomerAddressDto customerAddressDto);

    default SearchAbandonedRecordParam filter2param(SearchAbandonedClientParam.Filter filter) {
        SearchAbandonedRecordParam p = new SearchAbandonedRecordParam();
        // Abandoned client type
        p.setLeadType(Optional.ofNullable(filter.leadType())
                .map(leadTypes -> leadTypes.stream()
                        .filter(leadType -> leadType != SearchAbandonedClientParam.LeadType.NON_CLIENT)
                        .map(SearchAbandonedClientParam.LeadType::getValue)
                        .collect(toSet()))
                .orElse(null));
        p.setNotAssociated(Optional.ofNullable(filter.leadType())
                .map(leadTypes -> leadTypes.stream()
                        .anyMatch(leadType -> leadType == SearchAbandonedClientParam.LeadType.NON_CLIENT))
                .orElse(false));
        // Abandoned step
        p.setAbandonStep(Optional.ofNullable(filter.abandonedStep())
                .filter(steps -> !CollectionUtils.isEmpty(steps))
                .orElse(new HashSet<>(OBStepEnum.listRecoverableSteps()))
                .stream()
                .map(OBStepEnum::name)
                .collect(toSet()));
        // Abandoned status
        p.setAbandonStatuses(Optional.ofNullable(filter.abandonStatus())
                .map(set -> set.stream()
                        .map(SearchAbandonedClientParam.AbandonStatus::name)
                        .map(String::toLowerCase)
                        .collect(toSet()))
                .orElse(null));
        // Abandoned date
        p.setStartTimeSec(Optional.ofNullable(filter.timeRange())
                .map(SearchAbandonedClientParam.TimeRange::startTimeSec)
                .orElse(null));
        p.setEndTimeSec(Optional.ofNullable(filter.timeRange())
                .map(SearchAbandonedClientParam.TimeRange::endTimeSec)
                .orElse(null));
        // Last contact time
        p.setLastContactStartTimeSec(Optional.ofNullable(filter.lastContactWithInMins())
                .map(mins -> Instant.now().minusSeconds(mins * 60).getEpochSecond())
                .orElse(null));
        p.setLastContactEndTimeSec(Optional.ofNullable(filter.lastContactBeforeMins())
                .map(mins -> Instant.now().minusSeconds(mins * 60).getEpochSecond())
                .orElse(null));
        // Include/exclude booking flow ids
        p.setIncludeBookingFlowIds(filter.includeBookingFlowIds());
        p.setExcludeBookingFlowIds(filter.excludeBookingFlowIds());
        // Selected care types, default to grooming
        p.setCareTypes(Optional.ofNullable(filter.careTypes()).orElse(Set.of(ServiceItemEnum.GROOMING)).stream()
                .map(ServiceItemEnum::name)
                .collect(toSet()));
        return p;
    }
}
