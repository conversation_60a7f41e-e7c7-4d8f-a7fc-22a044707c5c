<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeQbSyncServiceMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeQbSyncService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="connect_id" jdbcType="INTEGER" property="connectId" />
    <result column="realm_id" jdbcType="VARCHAR" property="realmId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="qb_service_id" jdbcType="VARCHAR" property="qbServiceId" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeQbSyncService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="service_description" jdbcType="LONGVARCHAR" property="serviceDescription" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, connect_id, realm_id, service_id, qb_service_id, service_name, update_time, 
    create_time, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    service_description
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_qb_sync_service
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_sync_service
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_service (business_id, connect_id, realm_id, 
      service_id, qb_service_id, service_name, 
      update_time, create_time, company_id, 
      service_description)
    values (#{businessId,jdbcType=INTEGER}, #{connectId,jdbcType=INTEGER}, #{realmId,jdbcType=VARCHAR}, 
      #{serviceId,jdbcType=INTEGER}, #{qbServiceId,jdbcType=VARCHAR}, #{serviceName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, 
      #{serviceDescription,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_service
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="connectId != null">
        connect_id,
      </if>
      <if test="realmId != null">
        realm_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="qbServiceId != null">
        qb_service_id,
      </if>
      <if test="serviceName != null">
        service_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="serviceDescription != null">
        service_description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="qbServiceId != null">
        #{qbServiceId,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceDescription != null">
        #{serviceDescription,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_service
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        connect_id = #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        realm_id = #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="qbServiceId != null">
        qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        service_name = #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceDescription != null">
        service_description = #{serviceDescription,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_service
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      service_id = #{serviceId,jdbcType=INTEGER},
      qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      service_name = #{serviceName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      service_description = #{serviceDescription,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_service
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      service_id = #{serviceId,jdbcType=INTEGER},
      qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      service_name = #{serviceName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>


    <select id="selectByBusinessIdRealmIdServiceIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_qb_sync_service
        where
        business_id = #{businessId} and
        realm_id = #{realmId} and
        service_id in
        <foreach close=")" collection="serviceIdList" item="serviceId" open="(" separator=",">
            #{serviceId}
        </foreach>
    </select>
    <select id="selectByBusinessIdRealmIdItemName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_qb_sync_service
        where
        business_id = #{businessId} and
        realm_id = #{realmId} and
        service_id = 0 and
        service_name = #{itemName}
        order by id desc limit 1
    </select>
    <update id="updateByBusinessIdRealmIdServiceId" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncService">
        update moe_qb_sync_service
        set
        qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
        service_name = #{serviceName,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=BIGINT},
        connect_id = #{connectId,jdbcType=INTEGER}
        <if test="serviceDescription != null">
            ,service_description = #{serviceDescription,jdbcType=LONGVARCHAR}
        </if>
        where
        business_id = #{businessId,jdbcType=INTEGER} and
        realm_id = #{realmId,jdbcType=VARCHAR} and
        service_id = #{serviceId,jdbcType=INTEGER}
    </update>
</mapper>