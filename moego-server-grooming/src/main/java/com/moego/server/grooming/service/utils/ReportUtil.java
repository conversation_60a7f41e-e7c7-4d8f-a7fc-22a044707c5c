package com.moego.server.grooming.service.utils;

import static com.moego.common.enums.ServiceEnum.TYPE_ADD_ONS;
import static com.moego.server.grooming.service.utils.OrderUtil.buildItemKey;
import static com.moego.server.grooming.service.utils.OrderUtil.buildItemKeyForOldOrder;
import static java.math.RoundingMode.HALF_UP;

import com.google.type.Decimal;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.utils.AmountUtils;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.server.customer.dto.GroomingCalenderPetInfo;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapstruct.PetDetailConverter;
import com.moego.server.grooming.service.dto.EmployeeReportCalculateDTO;
import com.moego.server.grooming.service.dto.GroomingReportApptDetail;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.report.CollectedAmountCollection;
import com.moego.server.grooming.service.params.CalCollectedAmountParams;
import com.moego.server.grooming.utils.PetDetailUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

public class ReportUtil {

    public static BigDecimal getProductAmount(List<MoeGroomingInvoiceItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return BigDecimal.ZERO;
        }
        return items.stream()
                .filter(item -> Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_PRODUCT.getType()))
                .map(item -> item.getTotalSalePrice().add(item.getTaxAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Extract and return service / add on names from pet details
     *
     * @param type, 1 - return service names, 2 - return add on names
     */
    public static List<String> getServices(List<ReportWebApptPetDetail> petDetails, Integer type, Integer staffId) {
        return petDetails.stream()
                .filter(p -> {
                    if (!Objects.equals(type, p.getServiceType())) {
                        return false;
                    }
                    if (!CollectionUtils.isEmpty(p.getOperationList())) {
                        return p.getOperationList().stream()
                                .map(GroomingServiceOperationDTO::getStaffId)
                                .anyMatch(staffId::equals);
                    }
                    return staffId.equals(p.getStaffId());
                })
                .map(ReportWebApptPetDetail::getServiceName)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取product item name
     */
    public static List<String> getStaffProducts(Integer staffId, List<MoeGroomingInvoiceItem> items) {
        return items.stream()
                .filter(item -> Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_PRODUCT.getType())
                        && Objects.equals(item.getStaffId(), staffId))
                .map(MoeGroomingInvoiceItem::getServiceName)
                .distinct()
                .collect(Collectors.toList());
    }

    public static boolean isAddon(Integer serviceType) {
        return Objects.equals(Integer.valueOf(TYPE_ADD_ONS), serviceType);
    }

    public static List<Integer> getReportApptPetIds(List<GroomingReportApptDetail> appointments) {
        List<Integer> pIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(appointments)) {
            return pIds;
        }
        appointments.forEach(a -> {
            if (!CollectionUtils.isEmpty(a.getPetDetails())) {
                a.getPetDetails().forEach(p -> pIds.add(p.getPetId()));
            }
            if (!CollectionUtils.isEmpty(a.getEvaluationDetails())) {
                a.getEvaluationDetails().forEach(e -> pIds.add(e.getPetId().intValue()));
            }
        });

        return pIds.stream().distinct().collect(Collectors.toList());
    }

    public static List<Integer> getReportWebApptPetIds(List<GroomingReportWebAppointment> appointments) {
        List<Integer> pIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(appointments)) {
            return pIds;
        }
        appointments.forEach(a -> {
            if (!CollectionUtils.isEmpty(a.getPetDetails())) {
                a.getPetDetails().forEach(p -> pIds.add(p.getPetId()));
            }
            if (!CollectionUtils.isEmpty(a.getEvaluationDetails())) {
                a.getEvaluationDetails().forEach(e -> pIds.add(e.getPetId().intValue()));
            }
        });

        return pIds.stream().distinct().collect(Collectors.toList());
    }

    public static List<Integer> getReportApptStaffIds(List<GroomingReportApptDetail> appointments) {
        List<Integer> staffIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(appointments)) {
            return staffIds;
        }
        appointments.forEach(a -> {
            if (!CollectionUtils.isEmpty(a.getPetDetails())) {
                a.getPetDetails().forEach(p -> {
                    staffIds.add(p.getStaffId());
                    if (!CollectionUtils.isEmpty(p.getOperationList())) {
                        p.getOperationList().forEach(o -> staffIds.add(o.getStaffId()));
                    }
                });
            }
        });
        return staffIds.stream().distinct().collect(Collectors.toList());
    }

    public static List<Integer> getReportWebApptStaffIds(List<GroomingReportWebAppointment> appointments) {
        List<Integer> staffIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(appointments)) {
            return staffIds;
        }
        appointments.forEach(a -> {
            if (!CollectionUtils.isEmpty(a.getPetDetails())) {
                a.getPetDetails().forEach(p -> {
                    staffIds.add(p.getStaffId());
                    if (!CollectionUtils.isEmpty(p.getOperationList())) {
                        p.getOperationList().forEach(o -> staffIds.add(o.getStaffId()));
                    }
                });
            }
        });
        return staffIds.stream().distinct().collect(Collectors.toList());
    }

    public static String buildPetAndServiceStr(
            GroomingReportApptDetail appointment, Map<Integer, GroomingCalenderPetInfo> petInfoMap) {
        // 获取petId -> services map
        Map<Integer, List<String>> petIdServices = new HashMap<>();
        if (!CollectionUtils.isEmpty(appointment.getPetDetails())) {
            for (ReportWebApptPetDetail p : appointment.getPetDetails()) {
                String serviceName = p.getServiceName();
                petIdServices
                        .computeIfAbsent(p.getPetId(), pId -> new ArrayList<>())
                        .add(serviceName);
            }
        }
        if (!CollectionUtils.isEmpty(appointment.getEvaluationDetails())) {
            for (EvaluationServiceDetailDTO p : appointment.getEvaluationDetails()) {
                String serviceName = p.getServiceName();
                petIdServices
                        .computeIfAbsent(p.getPetId().intValue(), pId -> new ArrayList<>())
                        .add(serviceName);
            }
        }
        // 处理 petAndService
        StringBuilder petAndServiceSb = new StringBuilder();
        petIdServices.forEach((k, v) -> {
            GroomingCalenderPetInfo petInfo = petInfoMap.get(k);
            String petName = petInfo == null ? "" : petInfo.getPetName();
            petAndServiceSb.append(petName).append("(");
            // 获取服务描述
            StringBuilder serviceSb = new StringBuilder();
            v.forEach(s -> serviceSb.append(s).append(", "));
            petAndServiceSb
                    .append(serviceSb.substring(0, serviceSb.length() - 2))
                    .append(")")
                    .append(", ");
        });
        String petAndServices = "";
        if (petAndServiceSb.length() > 2) {
            petAndServices = petAndServiceSb.substring(0, petAndServiceSb.length() - 2);
        }
        return petAndServices;
    }

    public static List<BigDecimal> allocateAmountByPrice(BigDecimal totalAmount, List<BigDecimal> prices) {
        if (CollectionUtils.isEmpty(prices)) {
            return new ArrayList<>();
        }
        BigDecimal total = prices.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal allocatedAmount = BigDecimal.ZERO;
        List<BigDecimal> amounts = new ArrayList<>();
        for (int i = 0; i < prices.size(); i++) {
            BigDecimal price = prices.get(i);
            if (i == prices.size() - 1) {
                amounts.add(totalAmount.subtract(allocatedAmount));
            } else {
                BigDecimal amount = BigDecimal.ZERO;
                if (total.compareTo(BigDecimal.ZERO) > 0) {
                    amount = totalAmount.multiply(price).divide(total, 2, HALF_UP);
                }
                amounts.add(amount);
                allocatedAmount = allocatedAmount.add(amount);
            }
        }
        return amounts;
    }

    public static String buildStaffName(StaffModel staff) {
        if (staff == null) {
            return "";
        }
        return (staff.getFirstName() + " " + staff.getLastName()).trim();
    }

    public static EmployeeReportCalculateDTO allocateAmount(
            GroomingReportWebAppointment apt,
            List<MoeGroomingInvoiceItem> invoiceItems,
            Map<Integer, BigDecimal> staffTipMap) {
        // 区分 service、product、service-charge 的 discount 和 tax
        BigDecimal serviceTotalDiscount = BigDecimal.ZERO;
        BigDecimal serviceTotalTax = BigDecimal.ZERO;
        BigDecimal productTotalDiscount = BigDecimal.ZERO;
        BigDecimal productTotalTax = BigDecimal.ZERO;
        BigDecimal serviceChargeDiscount = BigDecimal.ZERO;
        BigDecimal serviceChargeTax = BigDecimal.ZERO;

        for (MoeGroomingInvoiceItem item : invoiceItems) {
            if (Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE.getType())) {
                serviceTotalDiscount = AmountUtils.sum(serviceTotalDiscount, item.getDiscountAmount());
                serviceTotalTax = AmountUtils.sum(serviceTotalTax, item.getTaxAmount());
            } else if (Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_PRODUCT.getType())) {
                productTotalDiscount = AmountUtils.sum(productTotalDiscount, item.getDiscountAmount());
                productTotalTax = AmountUtils.sum(productTotalTax, item.getTaxAmount());
            } else if (Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE_CHARGE.getType())) {
                serviceChargeDiscount = AmountUtils.sum(serviceChargeDiscount, item.getDiscountAmount());
                serviceChargeTax = AmountUtils.sum(serviceChargeTax, item.getTaxAmount());
            }
        }

        // service应收总金额
        BigDecimal totalServiceRevenue = getPetDetailsTotalPrice(apt.getPetDetails())
                .add(apt.getTipsAmount())
                .add(serviceTotalTax)
                .subtract(serviceTotalDiscount);
        // 未收、已收、退款金额
        BigDecimal unpaidAmount = apt.getRemainAmount();
        BigDecimal refundedAmount = apt.getRefundedAmount();
        BigDecimal paidAmount = apt.getPaidAmount();
        BigDecimal totalAmount = apt.getTotalAmount();
        BigDecimal servicePaidExcludeConFee = AmountUtils.subtract(paidAmount, apt.getConvenienceFee());
        BigDecimal productPaidExcludeConFee = BigDecimal.ZERO;
        BigDecimal serviceChargePaidExcludeConFee = BigDecimal.ZERO;
        BigDecimal servicePaidAmount = paidAmount;
        BigDecimal productPaidAmount = BigDecimal.ZERO;
        BigDecimal serviceChargePaidAmount = BigDecimal.ZERO;
        BigDecimal serviceUnpaidAmount = unpaidAmount;
        BigDecimal productUnpaidAmount = BigDecimal.ZERO;
        BigDecimal serviceChargeUnpaidAmount = BigDecimal.ZERO;
        BigDecimal serviceRefundedAmount = refundedAmount;
        BigDecimal productRefundedAmount = BigDecimal.ZERO;
        BigDecimal serviceChargeRefundedAmount = BigDecimal.ZERO;
        // 计算product收入以及去掉paidAmount中product所占的部分
        if (!AmountUtils.isNullOrZero(totalAmount)
                && apt.getTotalProductSale() != null
                && apt.getTotalServiceSale() != null
                && apt.getTotalServiceChargeSale() != null) {
            BigDecimal paidAmountExcludeConFee = AmountUtils.subtract(paidAmount, apt.getConvenienceFee());
            BigDecimal totalAmountExcludeConFee = AmountUtils.subtract(totalAmount, apt.getConvenienceFee());

            if (!AmountUtils.isNullOrZero(totalAmountExcludeConFee)) {
                servicePaidExcludeConFee = paidAmountExcludeConFee
                        .multiply(apt.getTotalServiceSale())
                        .divide(totalAmountExcludeConFee, 2, HALF_UP);
                serviceChargePaidExcludeConFee = paidAmountExcludeConFee
                        .multiply(apt.getTotalServiceChargeSale())
                        .divide(totalAmountExcludeConFee, 2, HALF_UP);
                productPaidExcludeConFee = AmountUtils.subtract(
                        paidAmountExcludeConFee, servicePaidExcludeConFee, serviceChargePaidExcludeConFee);
                servicePaidAmount =
                        paidAmount.multiply(apt.getTotalServiceSale()).divide(totalAmountExcludeConFee, 2, HALF_UP);
                serviceChargePaidAmount = paidAmount
                        .multiply(apt.getTotalServiceChargeSale())
                        .divide(totalAmountExcludeConFee, 2, HALF_UP);
                productPaidAmount = AmountUtils.subtract(paidAmount, servicePaidAmount, serviceChargePaidAmount);

                serviceUnpaidAmount =
                        unpaidAmount.multiply(apt.getTotalServiceSale()).divide(totalAmountExcludeConFee, 2, HALF_UP);
                serviceChargeUnpaidAmount = unpaidAmount
                        .multiply(apt.getTotalServiceChargeSale())
                        .divide(totalAmountExcludeConFee, 2, HALF_UP);
                productUnpaidAmount =
                        AmountUtils.subtract(unpaidAmount, serviceUnpaidAmount, serviceChargeUnpaidAmount);

                serviceRefundedAmount =
                        refundedAmount.multiply(apt.getTotalServiceSale()).divide(totalAmountExcludeConFee, 2, HALF_UP);
                serviceChargeRefundedAmount = refundedAmount
                        .multiply(apt.getTotalServiceChargeSale())
                        .divide(totalAmountExcludeConFee, 2, HALF_UP);
                productRefundedAmount =
                        AmountUtils.subtract(refundedAmount, serviceRefundedAmount, serviceChargeRefundedAmount);
            }
        }

        var apptTotalTips = CollectionUtils.isEmpty(apt.getInvoiceMap())
                ? apt.getTipsAmount()
                : apt.getInvoiceMap().values().stream()
                        .map(MoeGroomingInvoice::getTipsAmount)
                        .filter(CommonUtil::isNormal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

        return new EmployeeReportCalculateDTO()
                // service
                .setServiceExpectedRevenue(totalServiceRevenue)
                .setServicePaidAmount(servicePaidAmount)
                .setServicePaidExcludeConFee(servicePaidExcludeConFee)
                .setServiceRefundedAmount(serviceRefundedAmount)
                .setServiceUnpaidAmount(serviceUnpaidAmount)
                // product
                .setProductExpectedRevenue(apt.getTotalProductSale())
                .setProductPaidAmount(productPaidAmount)
                .setProductPaidExcludeConFee(productPaidExcludeConFee)
                .setProductUnpaidAmount(productUnpaidAmount)
                .setProductRefundedAmount(productRefundedAmount)
                // service charge
                .setServiceChargeExpectedRevenue(apt.getTotalServiceChargePrice())
                .setServiceChargePaidAmount(serviceChargePaidAmount)
                .setServiceChargePaidExcludeConFee(serviceChargePaidExcludeConFee)
                .setServiceChargeUnpaidAmount(serviceChargeUnpaidAmount)
                .setServiceChargeRefundedAmount(serviceChargeRefundedAmount)
                // apt info
                .setAptPaid(!GroomingAppointmentEnum.NOT_PAY.equals(apt.getIsPaid())) // 预约是否支付
                .setAptFinish(AppointmentStatusEnum.FINISHED.getValue().equals(apt.getStatus()))
                .setTipsMap(calculateTips(staffTipMap, apt.getPetDetails()))
                .setDiscountMap(calculateDiscount(apt.getPetDetails(), invoiceItems))
                .setTaxMap(calculateTax(apt.getPetDetails(), invoiceItems))
                .setOrderTotalTips(apptTotalTips);
    }

    private static BigDecimal getPetDetailsTotalPrice(List<ReportWebApptPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return BigDecimal.ZERO;
        }
        var moeGroomingPetDetails = PetDetailConverter.INSTANCE.toMoeGroomingPetDetails(petDetails);
        var petServiceMap = moeGroomingPetDetails.stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingPetDetail::getPetId,
                        Collectors.toMap(MoeGroomingPetDetail::getServiceId, Function.identity(), (p1, p2) -> p1)));
        return moeGroomingPetDetails.stream()
                .map(pd ->
                        pd.getServicePrice().multiply(BigDecimal.valueOf(PetDetailUtil.getQuantity(pd, petServiceMap))))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算每个 petDetail 的 tips，把 tipsAmount 分配到每个 petDetail 中，按 service 金额所占比例分配
     * 由于 report 这里是按 pet detail 分配，如果一个 staff 有多个服务，这里还需要按照 service 比例分配
     */
    public static Map<Integer, Map<Integer, BigDecimal>> calculateTips(
            Map<Integer, BigDecimal> staffTipMap, List<ReportWebApptPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(staffTipMap) || CollectionUtils.isEmpty(petDetails)) {
            return Map.of();
        }
        // 每个 staff 的 petDetail 列表
        Map<Integer, List<ReportWebApptPetDetail>> staffPetDetailMap = new HashMap<>();
        petDetails.forEach(petDetail -> {
            // / 如果有 operation，则按照 operation 分配 service
            if (!CollectionUtils.isEmpty(petDetail.getOperationList())) {
                petDetail.getOperationList().forEach(operation -> staffPetDetailMap
                        .computeIfAbsent(operation.getStaffId(), k -> new ArrayList<>())
                        .add(petDetail));
            } else {
                staffPetDetailMap
                        .computeIfAbsent(petDetail.getStaffId(), k -> new ArrayList<>())
                        .add(petDetail);
            }
        });

        // 每个 petDetail 分配到的 tips
        Map<Integer, Map<Integer, BigDecimal>> tipsMap = new HashMap<>();

        // 遍历每个 staff
        for (var entry : staffPetDetailMap.entrySet()) {
            Integer staffId = entry.getKey();
            // 当前 staff 的 petDetail 列表
            List<ReportWebApptPetDetail> staffPetDetails = staffPetDetailMap.get(staffId);
            // 当前 staff 的总服务金额，用于按比例分配
            BigDecimal totalServiceAmount = staffPetDetails.stream()
                    .map(petDetail -> {
                        // 如果有 operation，则使用 operation 的价格
                        if (!CollectionUtils.isEmpty(petDetail.getOperationList())) {
                            return petDetail.getOperationList().stream()
                                    .filter(operation -> Objects.equals(staffId, operation.getStaffId()))
                                    .map(GroomingServiceOperationDTO::getPrice)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        return petDetail.getServicePrice();
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 当前 staff 的 tips
            BigDecimal staffTips = staffTipMap.getOrDefault(staffId, BigDecimal.ZERO);
            BigDecimal tempTotalTips = BigDecimal.ZERO;
            // 遍历当前 staff 的 petDetail，按照 service 金额比例分配 tips
            for (int i = 0, size = staffPetDetails.size(); i < size; i++) {
                ReportWebApptPetDetail petDetail = staffPetDetails.get(i);
                BigDecimal servicePrice = petDetail.getServicePrice();
                if (!CollectionUtils.isEmpty(petDetail.getOperationList())) {
                    servicePrice = petDetail.getOperationList().stream()
                            .filter(operation -> Objects.equals(staffId, operation.getStaffId()))
                            .map(GroomingServiceOperationDTO::getPrice)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                // tips: 按service价格占比分配
                BigDecimal tips;
                if (i != size - 1) {
                    if (totalServiceAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        // 总的service价格是0时，平分tipsAmount
                        tips = staffTips.divide(BigDecimal.valueOf(petDetails.size()), 2, HALF_UP);
                    } else {
                        tips = servicePrice.multiply(staffTips).divide(totalServiceAmount, 2, HALF_UP);
                    }
                    tempTotalTips = tempTotalTips.add(tips);
                } else {
                    // 最后一个服务不按比例分配，用总数减去之前的总和以保证各个服务相加等于总数
                    tips = staffTips.subtract(tempTotalTips);
                }
                Map<Integer, BigDecimal> petDetailTipsMap = tipsMap.getOrDefault(petDetail.getId(), new HashMap<>());
                petDetailTipsMap.put(
                        staffId,
                        petDetailTipsMap.getOrDefault(staffId, BigDecimal.ZERO).add(tips));
                tipsMap.put(petDetail.getId(), petDetailTipsMap);
            }
        }
        return tipsMap;
    }

    public static Map<Integer, BigDecimal> calculateDiscount(
            List<ReportWebApptPetDetail> petDetails, List<MoeGroomingInvoiceItem> invoiceItems) {
        Map<Integer, BigDecimal> petDetailIdToDiscount = new HashMap<>();
        if (CollectionUtils.isEmpty(petDetails) || CollectionUtils.isEmpty(invoiceItems)) {
            return petDetailIdToDiscount;
        }
        // 以 serviceId_servicePrice_pet_id 为key，相同的service有可能不同价格，不区分出来可能会导致计算结果被覆盖
        Map<String, List<ReportWebApptPetDetail>> itemKeyToPetDetails =
                petDetails.stream().collect(Collectors.groupingBy(OrderUtil::buildItemKey));
        Map<String, List<ReportWebApptPetDetail>> oldItemKeyToPetDetails =
                petDetails.stream().collect(Collectors.groupingBy(OrderUtil::buildItemKeyForOldOrder));

        invoiceItems.forEach(item -> {
            BigDecimal tempTotalDiscount = BigDecimal.ZERO;
            Integer quantity = item.getQuantity();
            // 非 service 没有 petDetail
            if (!Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE.getType())) {
                return;
            }
            List<ReportWebApptPetDetail> pdList;
            if (item.getPetId() == 0) {
                pdList = oldItemKeyToPetDetails.get(buildItemKeyForOldOrder(item));
            } else {
                pdList = itemKeyToPetDetails.get(buildItemKey(item));
            }
            if (CollectionUtils.isEmpty(pdList)) {
                return;
            }
            for (int i = 0, size = pdList.size(); i < size; i++) {
                ReportWebApptPetDetail pd = pdList.get(i);
                BigDecimal discount;
                if (i == size - 1) {
                    discount = item.getDiscountAmount().subtract(tempTotalDiscount);
                } else {
                    discount = item.getDiscountAmount().divide(BigDecimal.valueOf(quantity), 2, HALF_UP);
                    tempTotalDiscount = tempTotalDiscount.add(discount);
                }
                petDetailIdToDiscount.put(pd.getId(), discount);
            }
        });
        return petDetailIdToDiscount;
    }

    /**
     * 计算每个 petDetail 的 tax，把 taxAmount 分配到每个 petDetail 中，按 service 金额所占比例分配
     */
    public static Map<Integer, BigDecimal> calculateTax(
            List<ReportWebApptPetDetail> petDetails, List<MoeGroomingInvoiceItem> invoiceItems) {
        Map<Integer, BigDecimal> petDetailIdToTax = new HashMap<>();
        if (CollectionUtils.isEmpty(petDetails) || CollectionUtils.isEmpty(invoiceItems)) {
            return petDetailIdToTax;
        }
        // 以 serviceId_servicePrice_pet_id 为key，相同的service有可能不同价格，不区分出来可能会导致计算结果被覆盖
        Map<String, List<ReportWebApptPetDetail>> itemKeyToPetDetails =
                petDetails.stream().collect(Collectors.groupingBy(OrderUtil::buildItemKey));
        Map<String, List<ReportWebApptPetDetail>> oldItemKeyToPetDetails =
                petDetails.stream().collect(Collectors.groupingBy(OrderUtil::buildItemKeyForOldOrder));
        invoiceItems.forEach(item -> {
            BigDecimal tempTotalTax = BigDecimal.ZERO;
            Integer quantity = item.getQuantity();
            // 非 service 没有 petDetail
            if (!Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE.getType())) {
                return;
            }
            List<ReportWebApptPetDetail> pdList;
            if (item.getPetId() == 0) {
                pdList = oldItemKeyToPetDetails.get(buildItemKeyForOldOrder(item));
            } else {
                pdList = itemKeyToPetDetails.get(buildItemKey(item));
            }
            if (CollectionUtils.isEmpty(pdList)) {
                return;
            }
            for (int i = 0, size = pdList.size(); i < size; i++) {
                ReportWebApptPetDetail pd = pdList.get(i);
                BigDecimal tax;
                if (i == size - 1) {
                    tax = item.getTaxAmount().subtract(tempTotalTax);
                } else {
                    tax = item.getTaxAmount().divide(BigDecimal.valueOf(quantity), 2, HALF_UP);
                    tempTotalTax = tempTotalTax.add(tax);
                }
                petDetailIdToTax.put(pd.getId(), tax);
            }
        });
        return petDetailIdToTax;
    }

    public static CollectedAmountCollection calculateCollectedAmount(CalCollectedAmountParams params) {
        BigDecimal collectedRevenue = params.collectedRevenue() != null ? params.collectedRevenue() : BigDecimal.ZERO;
        BigDecimal expectedTax = params.expectedTax();
        BigDecimal expectedTips = params.expectedTips();
        BigDecimal expectedDiscount = params.expectedDiscount();

        BigDecimal collectedTax = BigDecimal.ZERO;
        BigDecimal collectedTips = BigDecimal.ZERO;
        BigDecimal collectedDiscount = expectedDiscount != null ? expectedDiscount : BigDecimal.ZERO;
        BigDecimal collectedItemPrice;
        BigDecimal netSaleRevenue;
        // 分配顺序: tax > tips > service，collectedRevenue 为负数时不分配
        if (!AmountUtils.isNullOrZero(collectedRevenue)) {
            BigDecimal remainRev = collectedRevenue;
            // 分配 tax
            if (AmountUtils.isGreaterThanZero(expectedTax)) {
                // 如果已支付金额 >= expectedTax, earnedTax = expectedTax, 否则 earnedTax = remainRev
                if (remainRev.compareTo(expectedTax) >= 0) {
                    collectedTax = expectedTax;
                } else {
                    collectedTax = remainRev;
                }
            } else {
                // 负数金额处理: 直接设置为 expectedTax
                collectedTax = expectedTax;
            }
            // 减掉分配的 tax
            remainRev = remainRev.subtract(collectedTax);
            // remainRev 还有剩余时，继续分配 tips
            if (AmountUtils.isGreaterThanZero(expectedTips)) {
                // 如果remainRev >= expectedTips, earnedTips = expectedTips，否则 earnedTips = remainRev
                if (remainRev.compareTo(expectedTips) >= 0) {
                    collectedTips = expectedTips;
                } else {
                    collectedTips = remainRev;
                }
            } else {
                // 负数金额处理: 直接设置为 expectedTips
                collectedTips = expectedTips;
            }
        }
        // net sale = paidAmount - refund - tax - tips
        netSaleRevenue = collectedRevenue.subtract(collectedTax).subtract(collectedTips);
        // collected item price = paidAmount - refund - tax - tips + discount
        collectedItemPrice = netSaleRevenue.add(collectedDiscount);

        return new CollectedAmountCollection(
                collectedTax, collectedTips, collectedDiscount, collectedItemPrice, netSaleRevenue);
    }

    public static boolean isDateBetween(String date, String startDate, String endDate) {
        if (!StringUtils.hasText(date)) {
            return false;
        }
        return (date.compareTo(startDate) >= 0) && (date.compareTo(endDate) <= 0);
    }

    public static BigDecimal toBigDecimal(Decimal decimal) {
        if (decimal == null || decimal.getValue().isEmpty()) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(decimal.getValue());
    }
}
