package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.AutoAssignDTO;
import com.moego.server.grooming.mapperbean.AutoAssign;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface AutoAssignConverter {

    AutoAssignConverter INSTANCE = Mappers.getMapper(AutoAssignConverter.class);

    AutoAssignDTO entityToDTO(AutoAssign entity);
}
