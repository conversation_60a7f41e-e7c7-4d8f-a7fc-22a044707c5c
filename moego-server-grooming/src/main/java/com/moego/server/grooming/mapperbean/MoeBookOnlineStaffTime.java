package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_staff_time
 */
public class MoeBookOnlineStaffTime {
    /**
     * Database Column Remarks:
     *   商家ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_staff_time.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_staff_time.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   staffId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_staff_time.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   1normal   2
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_staff_time.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_staff_time.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_staff_time.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_staff_time.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   时间间隔的json字符串
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_staff_time.staff_times
     *
     * @mbg.generated
     */
    private String staffTimes;

    /**
     * Database Column Remarks:
     *   time slot配置的json字符串
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_staff_time.staff_slots
     *
     * @mbg.generated
     */
    private String staffSlots;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_staff_time.id
     *
     * @return the value of moe_book_online_staff_time.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_staff_time.id
     *
     * @param id the value for moe_book_online_staff_time.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_staff_time.business_id
     *
     * @return the value of moe_book_online_staff_time.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_staff_time.business_id
     *
     * @param businessId the value for moe_book_online_staff_time.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_staff_time.staff_id
     *
     * @return the value of moe_book_online_staff_time.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_staff_time.staff_id
     *
     * @param staffId the value for moe_book_online_staff_time.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_staff_time.status
     *
     * @return the value of moe_book_online_staff_time.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_staff_time.status
     *
     * @param status the value for moe_book_online_staff_time.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_staff_time.create_time
     *
     * @return the value of moe_book_online_staff_time.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_staff_time.create_time
     *
     * @param createTime the value for moe_book_online_staff_time.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_staff_time.update_time
     *
     * @return the value of moe_book_online_staff_time.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_staff_time.update_time
     *
     * @param updateTime the value for moe_book_online_staff_time.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_staff_time.company_id
     *
     * @return the value of moe_book_online_staff_time.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_staff_time.company_id
     *
     * @param companyId the value for moe_book_online_staff_time.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_staff_time.staff_times
     *
     * @return the value of moe_book_online_staff_time.staff_times
     *
     * @mbg.generated
     */
    public String getStaffTimes() {
        return staffTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_staff_time.staff_times
     *
     * @param staffTimes the value for moe_book_online_staff_time.staff_times
     *
     * @mbg.generated
     */
    public void setStaffTimes(String staffTimes) {
        this.staffTimes = staffTimes == null ? null : staffTimes.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_staff_time.staff_slots
     *
     * @return the value of moe_book_online_staff_time.staff_slots
     *
     * @mbg.generated
     */
    public String getStaffSlots() {
        return staffSlots;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_staff_time.staff_slots
     *
     * @param staffSlots the value for moe_book_online_staff_time.staff_slots
     *
     * @mbg.generated
     */
    public void setStaffSlots(String staffSlots) {
        this.staffSlots = staffSlots == null ? null : staffSlots.trim();
    }
}
