package com.moego.server.grooming.service;

import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.notification.v1.AppPushDef;
import com.moego.idl.models.notification.v1.AskForReviewDef;
import com.moego.idl.models.notification.v1.NotificationExtraDef;
import com.moego.idl.models.notification.v1.NotificationMethod;
import com.moego.idl.models.notification.v1.NotificationSource;
import com.moego.idl.models.notification.v1.NotificationType;
import com.moego.idl.models.notification.v1.PushTokenSource;
import com.moego.idl.models.notification.v1.ReadyToPickUpDef;
import com.moego.idl.service.account.v1.AccountServiceGrpc.AccountServiceBlockingStub;
import com.moego.idl.service.account.v1.GetAccountRequest;
import com.moego.idl.service.notification.v1.CreateInboxNotificationRequest;
import com.moego.idl.service.notification.v1.NotificationServiceGrpc;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.grooming.dto.AppointmentServiceInfo;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.enums.PetDetailStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.params.AppointmentParams;
import com.moego.server.grooming.params.PetDetailParams;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.params.OnlineBookWaitingNotifyParams;
import com.moego.server.message.params.notification.NotificationApptAssignedParams;
import com.moego.server.message.params.notification.NotificationApptRescheduledParams;
import com.moego.server.message.params.notification.ob.NotificationOBRequestRescheduledParams;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @create: 2023/5/6 18:31
 * @author: channy.shu
 **/
@Component
@Async
@RequiredArgsConstructor
@Slf4j
public class GroomingApptAsyncService {

    private final INotificationClient iNotificationClient;
    private final PetDetailMapperProxy moeGroomingPetDetailMapper;
    private final GroomingServiceOperationService groomingServiceOperationService;
    private final CalendarSyncService calendarSyncService;
    private final QuickBooksService quickBooksService;
    private final AppointmentMapperProxy appointmentMapper;
    private final MoeAppointmentQueryService appointmentQueryService;
    private final IBusinessBusinessClient businessClient;
    private final ICustomerCustomerClient customerClient;
    private final IPetClient petClient;
    private final IBusinessStaffClient staffClient;
    private final NotificationServiceGrpc.NotificationServiceBlockingStub notificationServiceBlockingStub;
    private final BrandedAppNotificationService brandedAppNotificationService;
    private final AccountServiceBlockingStub accountService;
    private final AppointmentMapperProxy moeGroomingAppointmentMapper;

    public void apptUpdateNotify(
            MoeGroomingAppointment finalMoeGroomingAppointment, MoeGroomingAppointment temp, Integer tokenStaffId) {
        // fixme 这里看起来有 bug，beforeUpdateStaffIdList 和 staffIdList 是一样的
        // 获取更新预约前的staffIdList，一会判断发送什么通知
        List<Integer> beforeUpdateStaffIdList =
                moeGroomingPetDetailMapper.queryStaffIdByGroomingId(finalMoeGroomingAppointment.getId());
        beforeUpdateStaffIdList.addAll(
                groomingServiceOperationService.queryStaffIdByGroomingId(finalMoeGroomingAppointment.getId()));
        // waitingListBookNow 发送的都是 创建通知 否则就需要特殊判断
        boolean isWaitingListBooKNow = BooleanEnum.VALUE_FALSE.equals(finalMoeGroomingAppointment.getIsWaitingList())
                && BooleanEnum.VALUE_TRUE.equals(temp.getIsWaitingList());
        // 只有更新appointmentDate和startTime后，才会发送rescheduled通知
        boolean isUpdateDate = !finalMoeGroomingAppointment.getAppointmentDate().equals(temp.getAppointmentDate());
        if (!finalMoeGroomingAppointment.getAppointmentStartTime().equals(temp.getAppointmentStartTime())) {
            isUpdateDate = true;
        }
        // 获取发送的staffIdList
        Set<Integer> staffIdList =
                appointmentQueryService.getAppointmentRelatedStaffIds(finalMoeGroomingAppointment.getId());
        // ob pending send reschedule notification
        if (Objects.nonNull(finalMoeGroomingAppointment.getBookOnlineStatus())
                && Objects.equals(finalMoeGroomingAppointment.getBookOnlineStatus(), ServiceEnum.OB_NOT_CONFIRM)) {
            Set<Integer> staffList = new HashSet<>(beforeUpdateStaffIdList);
            staffList.addAll(staffIdList);

            // 调用通知发送
            NotificationOBRequestRescheduledParams obRequestRescheduledParams =
                    new NotificationOBRequestRescheduledParams();
            obRequestRescheduledParams.setBusinessId(temp.getBusinessId());
            obRequestRescheduledParams.setStaffIdList(staffList);
            obRequestRescheduledParams.setTokenStaffId(tokenStaffId);

            // 给前端的数据体
            NotificationExtraApptCommonDto apptRescheduledDto = new NotificationExtraApptCommonDto();
            apptRescheduledDto.setAppointmentDate(finalMoeGroomingAppointment.getAppointmentDate());
            apptRescheduledDto.setAppointmentStartTime(finalMoeGroomingAppointment.getAppointmentStartTime());
            apptRescheduledDto.setNoStartTime(finalMoeGroomingAppointment.getNoStartTime());
            apptRescheduledDto.setAppointmentEndTime(finalMoeGroomingAppointment.getAppointmentEndTime());
            apptRescheduledDto.setCustomerId(temp.getCustomerId());
            apptRescheduledDto.setGroomingId(temp.getId());
            // customer firstName 和 lastName 在notification模块组装
            obRequestRescheduledParams.setWebPushDto(apptRescheduledDto);
            iNotificationClient.sendNotificationOBRequestRescheduled(obRequestRescheduledParams);
            return;
        }
        if (ServiceEnum.OB_NOT_CONFIRM.equals(temp.getBookOnlineStatus())) {
            // 发送ob确认的通知
            OnlineBookWaitingNotifyParams onlineBookWaitingNotifyParams = new OnlineBookWaitingNotifyParams();
            onlineBookWaitingNotifyParams.setCompanyId(temp.getCompanyId());
            onlineBookWaitingNotifyParams.setGroomingId(temp.getId());
            onlineBookWaitingNotifyParams.setBusinessId(temp.getBusinessId());
            // send accept notify
            onlineBookWaitingNotifyParams.setType(OnlineBookWaitingNotifyParams.TYPE_ACCEPT);
            iNotificationClient.bookOnlineNotify(onlineBookWaitingNotifyParams);

            // 发送给staff的创建预约通知
            NotificationApptAssignedParams apptAssignedParams = new NotificationApptAssignedParams();
            apptAssignedParams.setBusinessId(finalMoeGroomingAppointment.getBusinessId());
            apptAssignedParams.setStaffIdList(new HashSet<>(staffIdList));
            apptAssignedParams.setTokenStaffId(tokenStaffId);

            // 给前端的数据体
            NotificationExtraApptCommonDto apptAssignedDto = new NotificationExtraApptCommonDto();
            apptAssignedDto.setAppointmentDate(finalMoeGroomingAppointment.getAppointmentDate());
            apptAssignedDto.setAppointmentStartTime(finalMoeGroomingAppointment.getAppointmentStartTime());
            apptAssignedDto.setNoStartTime(finalMoeGroomingAppointment.getNoStartTime());
            apptAssignedDto.setAppointmentEndTime(finalMoeGroomingAppointment.getAppointmentEndTime());
            apptAssignedDto.setCustomerId(finalMoeGroomingAppointment.getCustomerId());
            apptAssignedDto.setGroomingId(finalMoeGroomingAppointment.getId());
            // customer firstName 和 lastName 在notification模块组装
            apptAssignedParams.setWebPushDto(apptAssignedDto);
            iNotificationClient.sendNotificationApptAssigned(apptAssignedParams);
        } else {
            // staffIdList 现在有的staffIdList
            // beforeUpdateStaffIdList 更新前的staffIdList
            Set<Integer> notifyUpdateStaffIdList = new HashSet<>();
            Set<Integer> notifyCreateStaffIdList = new HashSet<>();
            // waitingListBookNow 发送的都是 创建通知 否则就需要特殊判断
            if (isWaitingListBooKNow) {
                notifyCreateStaffIdList.addAll(staffIdList);
            } else {
                for (Integer staffId : staffIdList) {
                    if (beforeUpdateStaffIdList.contains(staffId)) {
                        notifyUpdateStaffIdList.add(staffId);
                    } else {
                        notifyCreateStaffIdList.add(staffId);
                    }
                }
            }
            if (isUpdateDate && notifyUpdateStaffIdList.size() > 0) {
                // 调用通知发送
                NotificationApptRescheduledParams apptRescheduledParams = new NotificationApptRescheduledParams();
                apptRescheduledParams.setBusinessId(temp.getBusinessId());
                apptRescheduledParams.setStaffIdList(notifyUpdateStaffIdList);
                apptRescheduledParams.setTokenStaffId(tokenStaffId);

                // 给前端的数据体
                NotificationExtraApptCommonDto apptRescheduledDto = new NotificationExtraApptCommonDto();
                apptRescheduledDto.setAppointmentDate(finalMoeGroomingAppointment.getAppointmentDate());
                apptRescheduledDto.setAppointmentStartTime(finalMoeGroomingAppointment.getAppointmentStartTime());
                apptRescheduledDto.setNoStartTime(finalMoeGroomingAppointment.getNoStartTime());
                apptRescheduledDto.setAppointmentEndTime(finalMoeGroomingAppointment.getAppointmentEndTime());
                apptRescheduledDto.setCustomerId(temp.getCustomerId());
                apptRescheduledDto.setGroomingId(temp.getId());
                // customer firstName 和 lastName 在notification模块组装
                apptRescheduledParams.setWebPushDto(apptRescheduledDto);
                iNotificationClient.sendNotificationApptRescheduled(apptRescheduledParams);
            }
            if (notifyCreateStaffIdList.size() > 0) {
                // 发送给staff的创建预约通知
                NotificationApptAssignedParams apptAssignedParams = new NotificationApptAssignedParams();
                apptAssignedParams.setBusinessId(temp.getBusinessId());
                apptAssignedParams.setStaffIdList(notifyCreateStaffIdList);
                apptAssignedParams.setTokenStaffId(tokenStaffId);
                // 给前端的数据体
                NotificationExtraApptCommonDto apptAssignedDto = new NotificationExtraApptCommonDto();
                apptAssignedDto.setAppointmentDate(finalMoeGroomingAppointment.getAppointmentDate());
                apptAssignedDto.setAppointmentStartTime(finalMoeGroomingAppointment.getAppointmentStartTime());
                apptAssignedDto.setNoStartTime(finalMoeGroomingAppointment.getNoStartTime());
                apptAssignedDto.setAppointmentEndTime(finalMoeGroomingAppointment.getAppointmentEndTime());
                apptAssignedDto.setCustomerId(temp.getCustomerId());
                apptAssignedDto.setGroomingId(temp.getId());
                // customer firstName 和 lastName 在notification模块组装
                apptAssignedParams.setWebPushDto(apptAssignedDto);
                iNotificationClient.sendNotificationApptAssigned(apptAssignedParams);
            }
        }
    }

    public void syncApptInfoToCalender(MoeGroomingAppointment moeGroomingAppointment) {
        syncApptInfoToCalender(moeGroomingAppointment, false);
    }

    public void syncApptInfoToCalender(MoeGroomingAppointment moeGroomingAppointment, Boolean isDelay) {
        if (isDelay == null) {
            isDelay = false;
        }
        log.info(String.format("syncApptInfoToCalender id:%s isDelay:%s", moeGroomingAppointment.getId(), isDelay));
        try {
            // update appt
            calendarSyncService.checkBusinessHaveGoogleCalendarSync( // for multi reason
                    moeGroomingAppointment.getBusinessId(),
                    moeGroomingAppointment.getId(),
                    moeGroomingAppointment.getAppointmentDate(),
                    isDelay);
            quickBooksService.addRedisSyncGroomingData(
                    moeGroomingAppointment.getBusinessId(),
                    moeGroomingAppointment.getId(),
                    moeGroomingAppointment.getAppointmentDate());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Deprecated // use apptAddNotifyV2
    public void apptAddNotify(AppointmentParams appointment, MoeGroomingAppointment moeGroomingAppointment) {
        boolean isAllowSendNotify = false;
        // repeat新增时，repeatId不为空
        if (appointment.getRepeatId() != null && appointment.getRepeatId() != 0) {
            // 是repeat的第一个appt，则允许发送通知
            if (Boolean.TRUE.equals(appointment.getIsRepeatFirstAppt())) {
                isAllowSendNotify = true;
            }
        } else if (!ServiceEnum.OB_NOT_CONFIRM.equals(moeGroomingAppointment.getBookOnlineStatus())) {
            // 不是repeat，且不是ob的普通预约，才允许发送通知
            isAllowSendNotify = true;
        }
        if (isAllowSendNotify) {
            // 调用通知发送
            NotificationApptAssignedParams apptAssignedParams = new NotificationApptAssignedParams();
            apptAssignedParams.setBusinessId(moeGroomingAppointment.getBusinessId());
            // 获取发送的staffIdList
            Set<Integer> staffIdList = new HashSet<>();
            for (PetDetailParams petService : appointment.getPetServices()) {
                staffIdList.add(petService.getStaffId());
                if (!CollectionUtils.isEmpty(petService.getOperationList())) {
                    petService.getOperationList().stream()
                            .map(GroomingServiceOperationDTO::getStaffId)
                            .forEach(staffIdList::add);
                }
            }
            apptAssignedParams.setStaffIdList(staffIdList);
            apptAssignedParams.setTokenStaffId(appointment.getCreatedById());

            // 给前端的数据体
            NotificationExtraApptCommonDto apptAssignedDto = new NotificationExtraApptCommonDto();
            apptAssignedDto.setAppointmentDate(moeGroomingAppointment.getAppointmentDate());
            apptAssignedDto.setAppointmentStartTime(moeGroomingAppointment.getAppointmentStartTime());
            apptAssignedDto.setNoStartTime(moeGroomingAppointment.getNoStartTime());
            apptAssignedDto.setAppointmentEndTime(moeGroomingAppointment.getAppointmentEndTime());
            apptAssignedDto.setCustomerId(moeGroomingAppointment.getCustomerId());
            apptAssignedDto.setGroomingId(moeGroomingAppointment.getId());
            // customer firstName 和 lastName 在notification模块组装
            apptAssignedParams.setWebPushDto(apptAssignedDto);
            iNotificationClient.sendNotificationApptAssigned(apptAssignedParams);
        }
    }

    public void apptAddNotifyV2(Integer appointmentId) {
        var moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
        if (moeGroomingAppointment == null) {
            return;
        }
        MoeGroomingPetDetailExample e = new MoeGroomingPetDetailExample();
        e.createCriteria()
                .andGroomingIdEqualTo(appointmentId)
                .andStaffIdGreaterThan(0)
                .andStatusEqualTo(PetDetailStatusEnum.NORMAL.getValue().byteValue());
        var staffIdList = moeGroomingPetDetailMapper.selectByExample(e).stream()
                .map(MoeGroomingPetDetail::getStaffId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(staffIdList)) {
            return;
        }
        apptAddNotifyV2(staffIdList, moeGroomingAppointment);
    }

    public void apptAddNotifyV2(Set<Integer> staffIdList, MoeGroomingAppointment moeGroomingAppointment) {
        NotificationApptAssignedParams apptAssignedParams = new NotificationApptAssignedParams();
        apptAssignedParams.setBusinessId(moeGroomingAppointment.getBusinessId());
        apptAssignedParams.setStaffIdList(staffIdList);
        apptAssignedParams.setTokenStaffId(moeGroomingAppointment.getCreatedById());

        NotificationExtraApptCommonDto apptAssignedDto = new NotificationExtraApptCommonDto();
        apptAssignedDto.setAppointmentDate(moeGroomingAppointment.getAppointmentDate());
        apptAssignedDto.setAppointmentStartTime(moeGroomingAppointment.getAppointmentStartTime());
        apptAssignedDto.setAppointmentEndTime(moeGroomingAppointment.getAppointmentEndTime());
        apptAssignedDto.setCustomerId(moeGroomingAppointment.getCustomerId());
        apptAssignedDto.setGroomingId(moeGroomingAppointment.getId());
        apptAssignedParams.setWebPushDto(apptAssignedDto);
        iNotificationClient.sendNotificationApptAssigned(apptAssignedParams);
    }

    public void asyncNotificationAppClientReady(Integer appointmentId) {
        MoeGroomingAppointment appointment = appointmentMapper.selectByPrimaryKey(appointmentId);
        Long linkAccountId = customerClient.getLinkAccountId(appointment.getCustomerId());
        if (Objects.isNull(linkAccountId)) {
            return;
        }
        if (isFromBrandedApp(linkAccountId)) {
            brandedAppNotificationService.pushNotification(
                    appointmentId, NotificationType.NOTIFICATION_TYPE_READY_TO_PICK_UP);
            return;
        }
        MoeBusinessDto businessInfo = businessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(appointment.getBusinessId()).build());
        List<MoeGroomingPetDetail> petDetails =
                moeGroomingPetDetailMapper.queryPetDetailCountByGroomingId(appointmentId);
        List<Integer> petIds = petDetails.stream()
                .map(MoeGroomingPetDetail::getPetId)
                .distinct()
                .toList();
        List<CustomerPetDetailDTO> pets = petClient.getCustomerPetListByIdList(petIds);
        CreateInboxNotificationRequest request = CreateInboxNotificationRequest.newBuilder()
                .setSource(NotificationSource.NOTIFICATION_SOURCE_PLATFORM)
                .setMethod(NotificationMethod.NOTIFICATION_METHOD_PET_PARENT_APP)
                .setType(NotificationType.NOTIFICATION_TYPE_READY_TO_PICK_UP)
                .setSenderId(0L)
                .setReceiverId(linkAccountId)
                .setExtra(NotificationExtraDef.newBuilder()
                        .setReadyToPickUp(ReadyToPickUpDef.newBuilder()
                                .setAppointmentId(appointmentId)
                                .setBusinessName(businessInfo.getBusinessName())
                                .setPetName(getPetName(pets))
                                .setVerb(getVerb(pets))
                                .setBusinessId(businessInfo.getId())
                                .build())
                        .build())
                .setAppPush(AppPushDef.newBuilder()
                        .setSource(PushTokenSource.PUSH_TOKEN_SOURCE_CLIENT)
                        .setFromTemplate(true)
                        .build())
                .build();
        notificationServiceBlockingStub.createInboxNotification(request);
    }

    private String getPetName(List<CustomerPetDetailDTO> pets) {
        return pets.stream().map(CustomerPetDetailDTO::getPetName).collect(Collectors.joining(" & "));
    }

    private String getVerb(List<CustomerPetDetailDTO> pets) {
        return pets.size() == 1 ? "is" : "are";
    }

    private String getStaffName(List<MoeStaffDto> staffs) {
        return staffs.stream().map(MoeStaffDto::getFirstName).collect(Collectors.joining(" & "));
    }

    private boolean isFromBrandedApp(long accountId) {
        var account = accountService.getAccount(
                GetAccountRequest.newBuilder().setId(accountId).build());
        return !Objects.equals(account.getNamespace().getType(), AccountNamespaceType.MOEGO);
    }

    public void asyncNotificationAppClientFinished(Integer appointmentId) {
        AppointmentWithPetDetailsDto appointment = appointmentMapper.getAppointmentWithPetDetails(appointmentId, false);
        Long linkAccountId = customerClient.getLinkAccountId(appointment.getCustomerId());
        if (Objects.isNull(linkAccountId)) {
            return;
        }
        if (isFromBrandedApp(linkAccountId)) {
            brandedAppNotificationService.pushNotification(
                    appointmentId, NotificationType.NOTIFICATION_TYPE_ASK_FOR_REVIEW);
            return;
        }
        MoeBusinessDto businessInfo = businessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(appointment.getBusinessId()).build());
        List<Integer> staffIds = appointment.getServices().stream()
                .map(AppointmentServiceInfo::getStaffId)
                .filter(Objects::nonNull)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        StaffIdListParams params = new StaffIdListParams();
        params.setBusinessId(appointment.getBusinessId());
        params.setStaffIdList(staffIds);
        List<MoeStaffDto> staffs = staffClient.getStaffList(params);
        CreateInboxNotificationRequest request = CreateInboxNotificationRequest.newBuilder()
                .setSource(NotificationSource.NOTIFICATION_SOURCE_PLATFORM)
                .setMethod(NotificationMethod.NOTIFICATION_METHOD_PET_PARENT_APP)
                .setType(NotificationType.NOTIFICATION_TYPE_ASK_FOR_REVIEW)
                .setSenderId(0L)
                .setReceiverId(linkAccountId)
                .setExtra(NotificationExtraDef.newBuilder()
                        .setAskForReview(AskForReviewDef.newBuilder()
                                .setAppointmentId(appointmentId)
                                .setBusinessName(businessInfo.getBusinessName())
                                .setBusinessId(businessInfo.getId())
                                .setAppointmentStaffName(getStaffName(staffs))
                                .build()))
                .setAppPush(AppPushDef.newBuilder()
                        .setSource(PushTokenSource.PUSH_TOKEN_SOURCE_CLIENT)
                        .setFromTemplate(true)
                        .build())
                .build();
        notificationServiceBlockingStub.createInboxNotification(request);
    }
}
