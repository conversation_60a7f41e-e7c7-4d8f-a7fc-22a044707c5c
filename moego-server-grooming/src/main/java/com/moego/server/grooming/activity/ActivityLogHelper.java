package com.moego.server.grooming.activity;

import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.server.grooming.activity.dto.ApplyPackageLogDTO;
import com.moego.server.grooming.activity.dto.AppointmentRepeatParamsLogDTO;
import com.moego.server.grooming.activity.dto.UpdateDiscountLogDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage;
import java.util.List;

public class ActivityLogHelper {

    public static AppointmentRepeatParamsLogDTO getAppointmentRepeatParamsLogDTO(
            Integer appointmentId, Integer repeatApplyType, Object params) {
        return new AppointmentRepeatParamsLogDTO(appointmentId, repeatApplyType, params);
    }

    public static ApplyPackageLogDTO getApplyPackageLogDTO(List<MoeGroomingInvoiceApplyPackage> applyPackageList) {
        return new ApplyPackageLogDTO(applyPackageList);
    }

    public static UpdateDiscountLogDTO getUpdateDiscountLogDTO(
            List<OrderLineDiscountModel> orderLineDiscountModelList) {
        return new UpdateDiscountLogDTO(orderLineDiscountModelList);
    }
}
