<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingReportMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="pet_id" jdbcType="INTEGER" property="petId" />
    <result column="pet_type_id" jdbcType="INTEGER" property="petTypeId" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="template_publish_time" jdbcType="TIMESTAMP" property="templatePublishTime" />
    <result column="template_json" jdbcType="CHAR" property="templateJson" />
    <result column="content_json" jdbcType="CHAR" property="contentJson" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="submitted_time" jdbcType="TIMESTAMP" property="submittedTime" />
    <result column="link_opened_count" jdbcType="INTEGER" property="linkOpenedCount" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="theme_code" jdbcType="VARCHAR" property="themeCode" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, customer_id, grooming_id, pet_id, pet_type_id, uuid, template_publish_time,
    template_json, content_json, status, submitted_time, link_opened_count, update_by,
    create_time, update_time, theme_code, company_id
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_report
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_report
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_report (business_id, customer_id, grooming_id,
      pet_id, pet_type_id, uuid,
      template_publish_time, template_json, content_json,
      status, submitted_time, link_opened_count,
      update_by, create_time, update_time,
      theme_code, company_id)
    values (#{businessId,jdbcType=INTEGER}, #{customerId,jdbcType=INTEGER}, #{groomingId,jdbcType=INTEGER},
      #{petId,jdbcType=INTEGER}, #{petTypeId,jdbcType=INTEGER}, #{uuid,jdbcType=VARCHAR},
      #{templatePublishTime,jdbcType=TIMESTAMP}, #{templateJson,jdbcType=CHAR}, #{contentJson,jdbcType=CHAR},
      #{status,jdbcType=VARCHAR}, #{submittedTime,jdbcType=TIMESTAMP}, #{linkOpenedCount,jdbcType=INTEGER},
      #{updateBy,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{themeCode,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="petTypeId != null">
        pet_type_id,
      </if>
      <if test="uuid != null">
        uuid,
      </if>
      <if test="templatePublishTime != null">
        template_publish_time,
      </if>
      <if test="templateJson != null">
        template_json,
      </if>
      <if test="contentJson != null">
        content_json,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="submittedTime != null">
        submitted_time,
      </if>
      <if test="linkOpenedCount != null">
        link_opened_count,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="themeCode != null">
        theme_code,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=INTEGER},
      </if>
      <if test="petTypeId != null">
        #{petTypeId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="templatePublishTime != null">
        #{templatePublishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="templateJson != null">
        #{templateJson,jdbcType=CHAR},
      </if>
      <if test="contentJson != null">
        #{contentJson,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="submittedTime != null">
        #{submittedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="linkOpenedCount != null">
        #{linkOpenedCount,jdbcType=INTEGER},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="themeCode != null">
        #{themeCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.groomingId != null">
        grooming_id = #{record.groomingId,jdbcType=INTEGER},
      </if>
      <if test="record.petId != null">
        pet_id = #{record.petId,jdbcType=INTEGER},
      </if>
      <if test="record.petTypeId != null">
        pet_type_id = #{record.petTypeId,jdbcType=INTEGER},
      </if>
      <if test="record.uuid != null">
        uuid = #{record.uuid,jdbcType=VARCHAR},
      </if>
      <if test="record.templatePublishTime != null">
        template_publish_time = #{record.templatePublishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.templateJson != null">
        template_json = #{record.templateJson,jdbcType=CHAR},
      </if>
      <if test="record.contentJson != null">
        content_json = #{record.contentJson,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.submittedTime != null">
        submitted_time = #{record.submittedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.linkOpenedCount != null">
        link_opened_count = #{record.linkOpenedCount,jdbcType=INTEGER},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.themeCode != null">
        theme_code = #{record.themeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      grooming_id = #{record.groomingId,jdbcType=INTEGER},
      pet_id = #{record.petId,jdbcType=INTEGER},
      pet_type_id = #{record.petTypeId,jdbcType=INTEGER},
      uuid = #{record.uuid,jdbcType=VARCHAR},
      template_publish_time = #{record.templatePublishTime,jdbcType=TIMESTAMP},
      template_json = #{record.templateJson,jdbcType=CHAR},
      content_json = #{record.contentJson,jdbcType=CHAR},
      status = #{record.status,jdbcType=VARCHAR},
      submitted_time = #{record.submittedTime,jdbcType=TIMESTAMP},
      link_opened_count = #{record.linkOpenedCount,jdbcType=INTEGER},
      update_by = #{record.updateBy,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      theme_code = #{record.themeCode,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=INTEGER},
      </if>
      <if test="petTypeId != null">
        pet_type_id = #{petTypeId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        uuid = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="templatePublishTime != null">
        template_publish_time = #{templatePublishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="templateJson != null">
        template_json = #{templateJson,jdbcType=CHAR},
      </if>
      <if test="contentJson != null">
        content_json = #{contentJson,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="submittedTime != null">
        submitted_time = #{submittedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="linkOpenedCount != null">
        link_opened_count = #{linkOpenedCount,jdbcType=INTEGER},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="themeCode != null">
        theme_code = #{themeCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report
    set business_id = #{businessId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      pet_id = #{petId,jdbcType=INTEGER},
      pet_type_id = #{petTypeId,jdbcType=INTEGER},
      uuid = #{uuid,jdbcType=VARCHAR},
      template_publish_time = #{templatePublishTime,jdbcType=TIMESTAMP},
      template_json = #{templateJson,jdbcType=CHAR},
      content_json = #{contentJson,jdbcType=CHAR},
      status = #{status,jdbcType=VARCHAR},
      submitted_time = #{submittedTime,jdbcType=TIMESTAMP},
      link_opened_count = #{linkOpenedCount,jdbcType=INTEGER},
      update_by = #{updateBy,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      theme_code = #{themeCode,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="initGroomingReports" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReport">
    insert into moe_grooming_report (business_id, customer_id, grooming_id, pet_id,
                                     pet_type_id, uuid, template_publish_time, status,
                                     submitted_time, update_by, template_json, content_json,
    company_id, theme_code
    )
    values
    <foreach collection="groomingReportList" index="index" item="groomingReport" separator=",">
      (#{groomingReport.businessId,jdbcType=INTEGER}, #{groomingReport.customerId,jdbcType=INTEGER},
       #{groomingReport.groomingId,jdbcType=INTEGER}, #{groomingReport.petId,jdbcType=INTEGER},
       #{groomingReport.petTypeId,jdbcType=INTEGER}, #{groomingReport.uuid,jdbcType=VARCHAR},
       #{groomingReport.templatePublishTime,jdbcType=TIMESTAMP}, #{groomingReport.status,jdbcType=VARCHAR},
       #{groomingReport.submittedTime,jdbcType=TIMESTAMP}, #{groomingReport.updateBy,jdbcType=INTEGER},
       #{groomingReport.templateJson,jdbcType=LONGVARCHAR}, #{groomingReport.contentJson,jdbcType=LONGVARCHAR},
      #{groomingReport.companyId,jdbcType=BIGINT}, #{groomingReport.themeCode,jdbcType=VARCHAR}
      )
    </foreach>
    on duplicate key update
        pet_type_id = values(pet_type_id),
        uuid = values(uuid),
        template_publish_time = values(template_publish_time),
        status = values(status),
        submitted_time = values(submitted_time),
        update_by = values(update_by),
        template_json = values(template_json),
        content_json = values(content_json),
        theme_code = values(theme_code),
        update_time = now()
  </insert>

  <select id="selectBusinessDefaultReport" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_report
    where business_id = #{businessId,jdbcType=INTEGER}
        and grooming_id = 0
        and pet_id = 0
  </select>

  <select id="selectByGroomingIdAndPetId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_report
    where business_id = #{businessId,jdbcType=INTEGER}
    and grooming_id = #{groomingId,jdbcType=INTEGER}
    and pet_id = #{petId,jdbcType=INTEGER}
  </select>

  <select id="selectByGroomingId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_report
    where business_id = #{businessId,jdbcType=INTEGER}
        and grooming_id = #{groomingId,jdbcType=INTEGER}
    order by id desc
  </select>

  <select id="queryList" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    content_json
    from moe_grooming_report
    where business_id = #{businessId,jdbcType=INTEGER}
    <if test="groomingIdList != null">
      and grooming_id in
      <foreach close=")" collection="groomingIdList" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="statuses != null">
      and status in
      <foreach close=")" collection="statuses" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="selectByUuid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_report
    where uuid = #{uuid}
  </select>

  <select id="selectByBusinessIdAndIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_report
    where id in
    <foreach close=")" collection="ids" item="id" open="(" separator=",">
      #{id}
    </foreach>
    and business_id = #{businessId}
  </select>

  <update id="batchUpdateGroomingReportStatus">
    update moe_grooming_report
    <set>
      <if test="status">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=INTEGER}
      </if>
    </set>
    where business_id = #{businessId,jdbcType=INTEGER}
    AND id in
    <foreach close=")" collection="ids" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </update>

  <update id="addUpGroomingReportOpenedCount">
    update moe_grooming_report
    set link_opened_count = link_opened_count + 1
    where uuid = #{uuid}
  </update>

</mapper>
