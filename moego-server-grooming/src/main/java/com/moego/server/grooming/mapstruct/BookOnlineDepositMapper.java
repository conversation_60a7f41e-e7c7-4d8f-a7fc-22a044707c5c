package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.api.IBookOnlineDepositService;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BookOnlineDepositMapper {
    BookOnlineDepositMapper INSTANCE = Mappers.getMapper(BookOnlineDepositMapper.class);

    BookOnlineDepositDTO entityToDto(MoeBookOnlineDeposit entity);

    MoeBookOnlineDeposit insertParamToEntity(IBookOnlineDepositService.InsertParam insertParam);

    MoeBookOnlineDeposit updateParamToEntity(IBookOnlineDepositService.UpdateParam updateParam);
}
