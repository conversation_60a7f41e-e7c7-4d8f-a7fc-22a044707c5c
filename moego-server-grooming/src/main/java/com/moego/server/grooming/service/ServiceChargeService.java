package com.moego.server.grooming.service;

import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.service.order.v1.GetServiceChargeListInput;
import com.moego.idl.service.order.v1.GetServiceChargeListOutput;
import com.moego.idl.service.order.v1.QueryServiceChargeListInput;
import com.moego.idl.service.order.v1.ServiceChargeServiceGrpc;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import com.moego.server.grooming.mapstruct.ServiceChargeMapper;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class ServiceChargeService {

    @Autowired
    private ServiceChargeServiceGrpc.ServiceChargeServiceBlockingStub serviceChargeClient;

    @Autowired
    private MigrateHelper migrateHelper;

    /**
     * 查询 serviceChargeItem 信息 List
     *
     * @param businessId       businessId
     * @param serviceChargeIds serviceChargeIds
     * @return List<ServiceChargeDTO>
     */
    public List<ServiceChargeDTO> getServiceChargeList(Integer businessId, List<? extends Number> serviceChargeIds) {
        if (CollectionUtils.isEmpty(serviceChargeIds)) {
            return List.of();
        }
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);

        List<Long> serviceChargeIdList =
                serviceChargeIds.stream().map(Number::longValue).distinct().toList();
        QueryServiceChargeListInput input = QueryServiceChargeListInput.newBuilder()
                .setBusinessId(businessId)
                .setCompanyId(migrateInfo.companyId())
                .addAllServiceChargeId(serviceChargeIdList)
                .build();
        return serviceChargeClient.queryServiceChargeList(input).getServiceChargeList().stream()
                .map(ServiceChargeMapper.INSTANCE::modelToDTO)
                .toList();
    }

    /**
     * 查询 serviceChargeItem 信息 Map
     *
     * @param businessId       businessId
     * @param serviceChargeIds serviceChargeIds
     * @return key-serviceChargeId, value-ServiceChargeDTO
     */
    public Map<Long, ServiceChargeDTO> getServiceChargeMap(
            Integer businessId, List<? extends Number> serviceChargeIds) {
        return getServiceChargeList(businessId, serviceChargeIds).stream()
                .collect(Collectors.toMap(ServiceChargeDTO::getId, Function.identity()));
    }

    /**
     * 获取 mandatory 的 service charge 及 金额
     *
     * @param businessId businessId
     * @return service charge list
     */
    public List<ServiceChargeDTO> getMandatoryServiceChargeList(Integer businessId) {
        // 查询 mandatory 的 service charge 及 金额
        GetServiceChargeListOutput output =
                serviceChargeClient.getServiceChargeList(GetServiceChargeListInput.newBuilder()
                        .setBusinessId(businessId)
                        .setIsActive(true)
                        .setIsMandatory(true)
                        .setIncludedDeleted(false)
                        .build());

        if (output.getServiceChargeCount() == 0) {
            return Collections.emptyList();
        }

        return output.getServiceChargeList().stream()
                .filter(e -> e.getAutoApplyStatus() == ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED)
                .map(ServiceChargeMapper.INSTANCE::modelToDTO)
                .toList();
    }
}
