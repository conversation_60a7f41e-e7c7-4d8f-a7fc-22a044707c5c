<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineLandingPageConfigMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="amenities" jdbcType="CHAR" property="amenities" />
    <result column="showcase_before_image" jdbcType="VARCHAR" property="showcaseBeforeImage" />
    <result column="showcase_after_image" jdbcType="VARCHAR" property="showcaseAfterImage" />
    <result column="theme_color" jdbcType="VARCHAR" property="themeColor" />
    <result column="url_domain_name" jdbcType="VARCHAR" property="urlDomainName" />
    <result column="is_published" jdbcType="BIT" property="isPublished" />
    <result column="ga_measurement_id" jdbcType="VARCHAR" property="gaMeasurementId" />
    <result column="page_components" jdbcType="CHAR" property="pageComponents" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="is_display_client_review_showcase_photo" jdbcType="BIT" property="isDisplayClientReviewShowcasePhoto" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="about_us" jdbcType="LONGVARCHAR" property="aboutUs" />
    <result column="welcome_page_message" jdbcType="LONGVARCHAR" property="welcomePageMessage" />
    <result column="thank_you_page_url" jdbcType="LONGVARCHAR" property="thankYouPageUrl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, amenities, showcase_before_image, showcase_after_image, theme_color,
    url_domain_name, is_published, ga_measurement_id, page_components, create_time, update_time,
    company_id, is_display_client_review_showcase_photo
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    about_us, welcome_page_message, thank_you_page_url
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfigExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_landing_page_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_book_online_landing_page_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_landing_page_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_landing_page_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfigExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_landing_page_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_landing_page_config (business_id, amenities, showcase_before_image,
      showcase_after_image, theme_color, url_domain_name,
      is_published, ga_measurement_id, page_components,
      create_time, update_time, company_id,
      is_display_client_review_showcase_photo, about_us,
      welcome_page_message, thank_you_page_url
      )
    values (#{businessId,jdbcType=INTEGER}, #{amenities,jdbcType=CHAR}, #{showcaseBeforeImage,jdbcType=VARCHAR},
      #{showcaseAfterImage,jdbcType=VARCHAR}, #{themeColor,jdbcType=VARCHAR}, #{urlDomainName,jdbcType=VARCHAR},
      #{isPublished,jdbcType=BIT}, #{gaMeasurementId,jdbcType=VARCHAR}, #{pageComponents,jdbcType=CHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT},
      #{isDisplayClientReviewShowcasePhoto,jdbcType=BIT}, #{aboutUs,jdbcType=LONGVARCHAR},
      #{welcomePageMessage,jdbcType=LONGVARCHAR}, #{thankYouPageUrl,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_landing_page_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="amenities != null">
        amenities,
      </if>
      <if test="showcaseBeforeImage != null">
        showcase_before_image,
      </if>
      <if test="showcaseAfterImage != null">
        showcase_after_image,
      </if>
      <if test="themeColor != null">
        theme_color,
      </if>
      <if test="urlDomainName != null">
        url_domain_name,
      </if>
      <if test="isPublished != null">
        is_published,
      </if>
      <if test="gaMeasurementId != null">
        ga_measurement_id,
      </if>
      <if test="pageComponents != null">
        page_components,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="isDisplayClientReviewShowcasePhoto != null">
        is_display_client_review_showcase_photo,
      </if>
      <if test="aboutUs != null">
        about_us,
      </if>
      <if test="welcomePageMessage != null">
        welcome_page_message,
      </if>
      <if test="thankYouPageUrl != null">
        thank_you_page_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="amenities != null">
        #{amenities,jdbcType=CHAR},
      </if>
      <if test="showcaseBeforeImage != null">
        #{showcaseBeforeImage,jdbcType=VARCHAR},
      </if>
      <if test="showcaseAfterImage != null">
        #{showcaseAfterImage,jdbcType=VARCHAR},
      </if>
      <if test="themeColor != null">
        #{themeColor,jdbcType=VARCHAR},
      </if>
      <if test="urlDomainName != null">
        #{urlDomainName,jdbcType=VARCHAR},
      </if>
      <if test="isPublished != null">
        #{isPublished,jdbcType=BIT},
      </if>
      <if test="gaMeasurementId != null">
        #{gaMeasurementId,jdbcType=VARCHAR},
      </if>
      <if test="pageComponents != null">
        #{pageComponents,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="isDisplayClientReviewShowcasePhoto != null">
        #{isDisplayClientReviewShowcasePhoto,jdbcType=BIT},
      </if>
      <if test="aboutUs != null">
        #{aboutUs,jdbcType=LONGVARCHAR},
      </if>
      <if test="welcomePageMessage != null">
        #{welcomePageMessage,jdbcType=LONGVARCHAR},
      </if>
      <if test="thankYouPageUrl != null">
        #{thankYouPageUrl,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_book_online_landing_page_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_landing_page_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.amenities != null">
        amenities = #{record.amenities,jdbcType=CHAR},
      </if>
      <if test="record.showcaseBeforeImage != null">
        showcase_before_image = #{record.showcaseBeforeImage,jdbcType=VARCHAR},
      </if>
      <if test="record.showcaseAfterImage != null">
        showcase_after_image = #{record.showcaseAfterImage,jdbcType=VARCHAR},
      </if>
      <if test="record.themeColor != null">
        theme_color = #{record.themeColor,jdbcType=VARCHAR},
      </if>
      <if test="record.urlDomainName != null">
        url_domain_name = #{record.urlDomainName,jdbcType=VARCHAR},
      </if>
      <if test="record.isPublished != null">
        is_published = #{record.isPublished,jdbcType=BIT},
      </if>
      <if test="record.gaMeasurementId != null">
        ga_measurement_id = #{record.gaMeasurementId,jdbcType=VARCHAR},
      </if>
      <if test="record.pageComponents != null">
        page_components = #{record.pageComponents,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.isDisplayClientReviewShowcasePhoto != null">
        is_display_client_review_showcase_photo = #{record.isDisplayClientReviewShowcasePhoto,jdbcType=BIT},
      </if>
      <if test="record.aboutUs != null">
        about_us = #{record.aboutUs,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.welcomePageMessage != null">
        welcome_page_message = #{record.welcomePageMessage,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.thankYouPageUrl != null">
        thank_you_page_url = #{record.thankYouPageUrl,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_landing_page_config
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      amenities = #{record.amenities,jdbcType=CHAR},
      showcase_before_image = #{record.showcaseBeforeImage,jdbcType=VARCHAR},
      showcase_after_image = #{record.showcaseAfterImage,jdbcType=VARCHAR},
      theme_color = #{record.themeColor,jdbcType=VARCHAR},
      url_domain_name = #{record.urlDomainName,jdbcType=VARCHAR},
      is_published = #{record.isPublished,jdbcType=BIT},
      ga_measurement_id = #{record.gaMeasurementId,jdbcType=VARCHAR},
      page_components = #{record.pageComponents,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=BIGINT},
      is_display_client_review_showcase_photo = #{record.isDisplayClientReviewShowcasePhoto,jdbcType=BIT},
      about_us = #{record.aboutUs,jdbcType=LONGVARCHAR},
      welcome_page_message = #{record.welcomePageMessage,jdbcType=LONGVARCHAR},
      thank_you_page_url = #{record.thankYouPageUrl,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_landing_page_config
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      amenities = #{record.amenities,jdbcType=CHAR},
      showcase_before_image = #{record.showcaseBeforeImage,jdbcType=VARCHAR},
      showcase_after_image = #{record.showcaseAfterImage,jdbcType=VARCHAR},
      theme_color = #{record.themeColor,jdbcType=VARCHAR},
      url_domain_name = #{record.urlDomainName,jdbcType=VARCHAR},
      is_published = #{record.isPublished,jdbcType=BIT},
      ga_measurement_id = #{record.gaMeasurementId,jdbcType=VARCHAR},
      page_components = #{record.pageComponents,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=BIGINT},
      is_display_client_review_showcase_photo = #{record.isDisplayClientReviewShowcasePhoto,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_landing_page_config
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="amenities != null">
        amenities = #{amenities,jdbcType=CHAR},
      </if>
      <if test="showcaseBeforeImage != null">
        showcase_before_image = #{showcaseBeforeImage,jdbcType=VARCHAR},
      </if>
      <if test="showcaseAfterImage != null">
        showcase_after_image = #{showcaseAfterImage,jdbcType=VARCHAR},
      </if>
      <if test="themeColor != null">
        theme_color = #{themeColor,jdbcType=VARCHAR},
      </if>
      <if test="urlDomainName != null">
        url_domain_name = #{urlDomainName,jdbcType=VARCHAR},
      </if>
      <if test="isPublished != null">
        is_published = #{isPublished,jdbcType=BIT},
      </if>
      <if test="gaMeasurementId != null">
        ga_measurement_id = #{gaMeasurementId,jdbcType=VARCHAR},
      </if>
      <if test="pageComponents != null">
        page_components = #{pageComponents,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="isDisplayClientReviewShowcasePhoto != null">
        is_display_client_review_showcase_photo = #{isDisplayClientReviewShowcasePhoto,jdbcType=BIT},
      </if>
      <if test="aboutUs != null">
        about_us = #{aboutUs,jdbcType=LONGVARCHAR},
      </if>
      <if test="welcomePageMessage != null">
        welcome_page_message = #{welcomePageMessage,jdbcType=LONGVARCHAR},
      </if>
      <if test="thankYouPageUrl != null">
        thank_you_page_url = #{thankYouPageUrl,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_landing_page_config
    set business_id = #{businessId,jdbcType=INTEGER},
      amenities = #{amenities,jdbcType=CHAR},
      showcase_before_image = #{showcaseBeforeImage,jdbcType=VARCHAR},
      showcase_after_image = #{showcaseAfterImage,jdbcType=VARCHAR},
      theme_color = #{themeColor,jdbcType=VARCHAR},
      url_domain_name = #{urlDomainName,jdbcType=VARCHAR},
      is_published = #{isPublished,jdbcType=BIT},
      ga_measurement_id = #{gaMeasurementId,jdbcType=VARCHAR},
      page_components = #{pageComponents,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      is_display_client_review_showcase_photo = #{isDisplayClientReviewShowcasePhoto,jdbcType=BIT},
      about_us = #{aboutUs,jdbcType=LONGVARCHAR},
      welcome_page_message = #{welcomePageMessage,jdbcType=LONGVARCHAR},
      thank_you_page_url = #{thankYouPageUrl,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_landing_page_config
    set business_id = #{businessId,jdbcType=INTEGER},
      amenities = #{amenities,jdbcType=CHAR},
      showcase_before_image = #{showcaseBeforeImage,jdbcType=VARCHAR},
      showcase_after_image = #{showcaseAfterImage,jdbcType=VARCHAR},
      theme_color = #{themeColor,jdbcType=VARCHAR},
      url_domain_name = #{urlDomainName,jdbcType=VARCHAR},
      is_published = #{isPublished,jdbcType=BIT},
      ga_measurement_id = #{gaMeasurementId,jdbcType=VARCHAR},
      page_components = #{pageComponents,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      is_display_client_review_showcase_photo = #{isDisplayClientReviewShowcasePhoto,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByBusinessId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_landing_page_config
    where business_id = #{businessId,jdbcType=INTEGER}
  </select>

  <update id="updateByBusinessIdSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig">
    update moe_book_online_landing_page_config
    <set>
      <if test="businessId != null">
        update_time = sysdate(),
      </if>
      <if test="amenities != null">
        amenities = #{amenities,jdbcType=CHAR},
      </if>
      <if test="showcaseBeforeImage != null">
        showcase_before_image = #{showcaseBeforeImage,jdbcType=VARCHAR},
      </if>
      <if test="showcaseAfterImage != null">
        showcase_after_image = #{showcaseAfterImage,jdbcType=VARCHAR},
      </if>
      <if test="themeColor != null">
        theme_color = #{themeColor,jdbcType=VARCHAR},
      </if>
      <if test="urlDomainName != null">
        url_domain_name = #{urlDomainName,jdbcType=VARCHAR},
      </if>
      <if test="isPublished != null">
        is_published = #{isPublished,jdbcType=BIT},
      </if>
      <if test="gaMeasurementId != null">
        ga_measurement_id = #{gaMeasurementId,jdbcType=VARCHAR},
      </if>
      <if test="pageComponents != null">
        page_components = json_merge_patch(page_components, #{pageComponents,jdbcType=CHAR}),
      </if>
      <if test="aboutUs != null">
        about_us = #{aboutUs,jdbcType=LONGVARCHAR},
      </if>
      <if test="welcomePageMessage != null">
        welcome_page_message = #{welcomePageMessage,jdbcType=LONGVARCHAR},
      </if>
      <if test="thankYouPageUrl != null">
        thank_you_page_url = #{thankYouPageUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="isDisplayClientReviewShowcasePhoto != null">
        is_display_client_review_showcase_photo = #{isDisplayClientReviewShowcasePhoto}
      </if>
    </set>
    where business_id = #{businessId,jdbcType=INTEGER}
  </update>

  <select id="selectByDomainName" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_landing_page_config
    where url_domain_name = #{domain,jdbcType=VARCHAR}
  </select>

  <select id="listByBusinessId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_landing_page_config
    where business_id in
          <foreach close=")" collection="businessIdList" item="businessId" open="(" separator=",">
            #{businessId,jdbcType=INTEGER}
          </foreach>
  </select>
</mapper>
