package com.moego.server.grooming.service.remote;

import static com.moego.idl.models.errors.v1.Code.CODE_PARAMS_ERROR;

import com.moego.idl.models.smart_scheduler.v1.BusinessSettingOverrideModel;
import com.moego.idl.service.smart_scheduler.v1.GetSmartScheduleSettingRequest;
import com.moego.idl.service.smart_scheduler.v1.GetSmartScheduleSettingResponse;
import com.moego.idl.service.smart_scheduler.v1.SmartScheduleSettingServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SmartSchedulerService {
    private final SmartScheduleSettingServiceGrpc.SmartScheduleSettingServiceBlockingStub
            smartSchedulerServiceBlockingStub;

    public Integer getSmartScheduleBufferTime(int companyId, int businessId) {
        if (companyId <= 0 || businessId <= 0) {
            throw ExceptionUtil.bizException(CODE_PARAMS_ERROR, "companyId/businessId is invalid");
        }

        GetSmartScheduleSettingResponse smartScheduleSetting = getSmartSchedulerSetting(companyId);

        for (BusinessSettingOverrideModel businessSettingOverrideModel :
                smartScheduleSetting.getBusinessOverrideListList()) {
            if (businessId == businessSettingOverrideModel.getBusinessId()
                    && businessSettingOverrideModel.hasBufferTime()) {
                return businessSettingOverrideModel.getBufferTime();
            }
        }

        return smartScheduleSetting.getSmartScheduleSetting().getBufferTime();
    }

    public GetSmartScheduleSettingResponse getSmartSchedulerSetting(Integer companyId) {
        if (companyId <= 0) {
            throw ExceptionUtil.bizException(CODE_PARAMS_ERROR, "companyId/businessId is invalid");
        }

        return smartSchedulerServiceBlockingStub.getSmartScheduleSetting(GetSmartScheduleSettingRequest.newBuilder()
                .setTokenCompanyId(companyId)
                .build());
    }
}
