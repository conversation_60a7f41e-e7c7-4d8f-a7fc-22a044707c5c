package com.moego.server.grooming.service.report;

import static com.moego.server.grooming.service.utils.ReportBeanUtil.setInvoiceAmount;

import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.utils.AmountUtils;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.server.grooming.dto.CustomerGrooming;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import com.moego.server.grooming.dto.report.ReportWebAppointment;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.service.GroomingServiceOperationService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.ServiceChargeService;
import com.moego.server.grooming.service.dto.GroomingReportApptDetail;
import com.moego.server.grooming.service.dto.GroomingReportApptInvoice;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportAppointmentDAO;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.report.ReportBaseAmountDTO;
import com.moego.server.grooming.service.utils.OrderUtil;
import com.moego.server.payment.client.IPaymentRefundClient;
import com.moego.server.payment.dto.RefundDTO;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class ReportOrderService {

    @Autowired
    private AppointmentMapperProxy appointmentMapper;

    @Autowired
    private MoeGroomingServiceOperationMapper serviceOperationMapper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private GroomingServiceOperationService groomingServiceOperationService;

    @Autowired
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Autowired
    private ServiceChargeService serviceChargeService;

    @Autowired
    private IPaymentRefundClient iPaymentRefundClient;

    @Autowired
    private NewOrderHelper newOrderHelper;

    public List<CustomerGrooming> queryGroomingCustomerAppointment(
            Integer businessId, List<Integer> appointmentIds, Integer orderType) {
        List<CustomerGrooming> appointments =
                appointmentMapper.queryGroomingCustomerAppointment(appointmentIds, orderType);

        List<Integer> groomingIds =
                appointments.stream().map(CustomerGrooming::getId).collect(Collectors.toList());

        // 查询multi staffIds
        Map<Integer, List<Long>> multiStaffsMap = serviceOperationMapper.selectByGroomingIdList(groomingIds).stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingServiceOperation::getGroomingServiceId,
                        Collectors.mapping(operation -> operation.getStaffId().longValue(), Collectors.toList())));

        // DONE new order flow 一个 appointment 对应多个 order
        var groomingIdToInvoices = getInvoiceDetails(businessId, groomingIds);
        var groomingIdToNewOrder = newOrderHelper.listNewOrder(
                groomingIds.stream().map(Integer::longValue).toList());

        appointments.forEach(appointment -> {
            var invoices = groomingIdToInvoices.getOrDefault(appointment.getId(), List.of());
            var isNewOrder = groomingIdToNewOrder.get(appointment.getId().longValue());
            if (isNewOrder) {
                appointment.setAppointmentOrderId(getOriginOrderId(invoices));
                appointment.setPaidAmount(calculateAmount(invoices, MoeGroomingInvoice::getPaidAmount));
                appointment.setRefundAmount(calculateAmount(invoices, MoeGroomingInvoice::getRefundedAmount));
            } else {
                invoices.stream()
                        .filter(i -> Objects.equals(i.getOrderType(), OrderModel.OrderType.ORIGIN.name()))
                        .findFirst()
                        .ifPresent(invoice -> {
                            appointment.setAppointmentOrderId(invoice.getId().longValue());
                            appointment.setPaymentStatus(invoice.getPaymentStatus());
                            appointment.setInvoiceId(invoice.getId());
                            appointment.setPaidAmount(invoice.getPaidAmount());
                            appointment.setRefundAmount(invoice.getRefundedAmount());
                        });
            }

            appointment.getPetServiceList().forEach(petService -> {
                List<Long> staffIds =
                        new ArrayList<>(multiStaffsMap.getOrDefault(petService.getPetDetailId(), List.of()));
                if (petService.getStaffId() != null) {
                    staffIds.add(petService.getStaffId().longValue());
                }
                petService.setStaffIds(staffIds.stream().distinct().toList());
            });
        });

        /*
         * 兼容代码 by ZhangDong
         * 因为 appointment 新增了两个状态字段，但是前端存在版本碎片问题，无法正确解析，因此需要增加兼容逻辑
         * status: 旧字段，状态枚举为 1 - 4，旧版本前端会读该字段
         * appointmentStatus：新字段，状态枚举为 1 - 6，新版本前端会读该字段
         */
        appointments.forEach(a -> {
            Byte status = a.getStatus().byteValue();
            a.setStatus(moeGroomingAppointmentService.getCompatibleStatus(status));
            a.setAppointmentStatus(
                    moeGroomingAppointmentService.getCompatibleAppointmentStatus(status, a.getCheckInTime()));
        });
        // ------------------------------

        appointments.forEach(
                a -> a.setServiceItems(ServiceItemEnum.convertBitValueList(a.getServiceTypeInclude()).stream()
                        .map(ServiceItemEnum::getServiceItem)
                        .distinct()
                        .toList()));
        return appointments;
    }

    public List<GroomingReportApptDetail> queryBusinessApptsAndInvoiceWithDate(
            Integer businessId, String startDate, String endDate) {
        List<GroomingReportApptDetail> appointments =
                queryBusinessApptsWithDate(List.of(businessId), startDate, endDate);

        List<Integer> groomingIds =
                appointments.stream().map(GroomingReportApptDetail::getId).collect(Collectors.toList());
        Map<Integer, List<MoeGroomingInvoice>> invoiceMap = getGroomingIdInvoiceListMap(businessId, groomingIds);

        // inner join，去掉没找到invoice的
        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId()));
        return fillReportApptInvoiceInfo(businessId, appointments, invoiceMap, false);
    }

    @Nullable
    private static Long getOriginOrderId(List<MoeGroomingInvoice> invoices) {
        return invoices.stream()
                .filter(i -> Objects.equals(i.getOrderType(), OrderModel.OrderType.ORIGIN.name()))
                .findFirst()
                .map(MoeGroomingInvoice::getId)
                .map(Integer::longValue)
                .orElse(null);
    }

    private static BigDecimal calculateAmount(
            List<MoeGroomingInvoice> invoices, Function<MoeGroomingInvoice, BigDecimal> function) {
        return invoices.stream().map(function).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public List<GroomingReportApptDetail> queryBusinessApptsWithDate(
            List<Integer> businessIds, String startDate, String endDate) {
        List<GroomingReportApptDetail> appointments =
                appointmentMapper.queryBusinessApptsAndInvoiceWithDate(businessIds, startDate, endDate);
        Map<Integer, List<GroomingReportApptDetail>> apptMap =
                appointments.stream().collect(Collectors.groupingBy(GroomingReportApptDetail::getBusinessId));
        for (Map.Entry<Integer, List<GroomingReportApptDetail>> entry : apptMap.entrySet()) {
            setOperationListForApptDetail(entry.getKey(), entry.getValue());
        }
        return appointments;
    }

    public List<ReportAppointmentDAO> queryUnpaidApptsWithAmount(Integer businessId, String startDate, String endDate) {
        List<ReportAppointmentDAO> appointments =
                appointmentMapper.queryUnpaidApptsWithAmount(businessId, startDate, endDate);

        List<Integer> groomingIds =
                appointments.stream().map(ReportAppointmentDAO::getId).collect(Collectors.toList());
        Map<Integer, MoeGroomingInvoice> invoiceMap = getGroomingIdInvoiceMap(businessId, groomingIds, null);

        // inner join，去掉没找到invoice的，同时过滤 remainAmount = 0
        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId())
                || AmountUtils.isNullOrZero(invoiceMap.get(appointment.getId()).getRemainAmount()));
        appointments.forEach(appointment -> {
            MoeGroomingInvoice invoice = invoiceMap.get(appointment.getId());
            appointment.setRemainAmount(invoice.getRemainAmount());
        });

        return appointments;
    }

    public Integer queryUnpaidApptsCountWithAmount(Integer businessId, String startDate, String endDate) {
        //         return appointmentMapper.queryUnpaidApptsCountWithAmount(businessId, startDate, endDate);
        // Order 服务分离后，只查 appointment 表没办法查出准确的 unpaid appts 数量，所以这里修改为查询 unpaid appt 列表的数量，查询有 limit 500
        return queryUnpaidApptsWithAmount(businessId, startDate, endDate).size();
    }

    public List<GroomingReportWebAppointment> queryWebReportUnpaidAppts(
            Integer businessId, String startDate, String endDate) {
        List<GroomingReportWebAppointment> appointments =
                appointmentMapper.queryWebReportUnpaidAppts(businessId, startDate, endDate);

        setOperationList(businessId, appointments);

        List<Integer> groomingIds =
                appointments.stream().map(GroomingReportWebAppointment::getId).collect(Collectors.toList());
        Map<Integer, MoeGroomingInvoice> invoiceMap = getGroomingIdInvoiceMap(businessId, groomingIds, null);

        // inner join，去掉没找到invoice的，同时过滤 remainAmount = 0
        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId())
                || AmountUtils.isNullOrZero(invoiceMap.get(appointment.getId()).getRemainAmount()));
        appointments.forEach(appointment -> {
            MoeGroomingInvoice invoice = invoiceMap.get(appointment.getId());
            appointment.setInvoiceId(invoice.getId());
            appointment.setTotalAmount(invoice.getTotalAmount());
            appointment.setPaidAmount(invoice.getPaidAmount());
            appointment.setRemainAmount(invoice.getRemainAmount());
        });

        return appointments;
    }

    public List<ReportAppointmentDAO> queryCancelledApptsWithNoShowInfo(
            Integer businessId, String startDate, String endDate) {
        List<ReportAppointmentDAO> appointments =
                appointmentMapper.queryCancelledApptsWithNoShowInfo(businessId, startDate, endDate);

        List<Integer> groomingIds =
                appointments.stream().map(ReportAppointmentDAO::getId).collect(Collectors.toList());
        Map<Integer, MoeGroomingInvoice> invoiceMap = getGroomingIdInvoiceMap(businessId, groomingIds, null);

        // inner join，去掉没找到invoice的
        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId()));
        appointments.forEach(appointment -> {
            MoeGroomingInvoice invoice = invoiceMap.get(appointment.getId());
            appointment.setInvoiceStatus(invoice.getStatus());
            appointment.setRemainAmount(invoice.getRemainAmount());
        });

        return appointments;
    }

    public List<ReportAppointmentDAO> queryNoShowApptsWithNoShowInfo(
            Integer businessId, String startDate, String endDate) {
        List<ReportAppointmentDAO> appointments =
                appointmentMapper.queryNoShowApptsWithNoShowInfo(businessId, startDate, endDate);

        List<Integer> groomingIds =
                appointments.stream().map(ReportAppointmentDAO::getId).collect(Collectors.toList());
        Map<Integer, MoeGroomingInvoice> invoiceMap = getGroomingIdInvoiceMap(businessId, groomingIds, null);

        // inner join，去掉没找到invoice的
        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId()));
        appointments.forEach(appointment -> {
            MoeGroomingInvoice invoice = invoiceMap.get(appointment.getId());
            appointment.setInvoiceStatus(invoice.getStatus());
            appointment.setRemainAmount(invoice.getRemainAmount());
        });

        return appointments;
    }

    public List<GroomingReportWebAppointment> queryWebReportApptDetails(
            Integer businessId, List<GroomingReportWebAppointment> appointments) {
        List<Integer> groomingIds =
                appointments.stream().map(GroomingReportWebAppointment::getId).collect(Collectors.toList());
        Map<Integer, MoeGroomingInvoice> invoiceMap = getGroomingIdInvoiceMap(businessId, groomingIds, null);

        // 查询 Service charge 信息
        Map<Integer, List<MoeGroomingInvoiceItem>> serviceChargeItemMap =
                getInvoiceIdServiceChargeItemMap(businessId, new ArrayList<>(invoiceMap.values()));
        List<Long> serviceChargeIds = serviceChargeItemMap.values().stream()
                .flatMap(Collection::stream)
                .map(MoeGroomingInvoiceItem::getServiceId)
                .map(Integer::longValue)
                .distinct()
                .toList();
        Map<Long, ServiceChargeDTO> serviceChargeMap =
                serviceChargeService.getServiceChargeMap(businessId, serviceChargeIds);

        appointments.forEach(appointment -> {
            MoeGroomingInvoice invoice = invoiceMap.get(appointment.getId());
            if (invoice == null) return;
            setInvoiceAmount(appointment, invoice);

            List<MoeGroomingInvoiceItem> serviceChargeItems = serviceChargeItemMap.get(invoice.getId());
            if (CollectionUtils.isEmpty(serviceChargeItems)) return;
            appointment.setServiceChargeList(getServiceChargeName(serviceChargeItems, serviceChargeMap));
        });

        return appointments;
    }

    public void queryWaitListReportOrderInfo(Integer businessId, List<ReportWebAppointment> reportAppointments) {
        List<Integer> groomingIds = reportAppointments.stream()
                .map(ReportWebAppointment::getBookingId)
                .toList();
        if (CollectionUtils.isEmpty(groomingIds)) {
            return;
        }
        Map<Integer, MoeGroomingInvoice> invoiceMap = getGroomingIdInvoiceMap(businessId, groomingIds, null);
        // 查询 Service charge 信息
        Map<Integer, List<MoeGroomingInvoiceItem>> serviceChargeItemMap =
                getInvoiceIdServiceChargeItemMap(businessId, new ArrayList<>(invoiceMap.values()));
        List<Long> serviceChargeIds = serviceChargeItemMap.values().stream()
                .flatMap(Collection::stream)
                .map(MoeGroomingInvoiceItem::getServiceId)
                .map(Integer::longValue)
                .distinct()
                .toList();
        Map<Long, ServiceChargeDTO> serviceChargeMap =
                serviceChargeService.getServiceChargeMap(businessId, serviceChargeIds);
        reportAppointments.forEach(appointment -> {
            MoeGroomingInvoice invoice = invoiceMap.get(appointment.getBookingId());
            if (invoice == null) return;
            List<MoeGroomingInvoiceItem> serviceChargeItems = serviceChargeItemMap.get(invoice.getId());
            if (CollectionUtils.isEmpty(serviceChargeItems)) return;
            appointment.setServiceCharges(getServiceChargeName(serviceChargeItems, serviceChargeMap));
        });
    }

    public List<GroomingReportApptDetail> queryGroomReportApptDetail(
            Integer businessId, List<GroomingReportApptDetail> appointments) {
        List<Integer> groomingIds =
                appointments.stream().map(GroomingReportApptDetail::getId).collect(Collectors.toList());
        Map<Integer, List<MoeGroomingInvoice>> invoiceMap = getGroomingIdInvoiceListMap(businessId, groomingIds);

        List<MoeGroomingInvoice> invoices =
                invoiceMap.values().stream().flatMap(Collection::stream).toList();
        // 查询 product/service-charge items，用于统计 product/service-charge 的价格
        Map<Integer, List<MoeGroomingInvoiceItem>> productItemMap = getInvoiceIdProductItemMap(businessId, invoices);
        Map<Integer, List<MoeGroomingInvoiceItem>> serviceChargeItemMap =
                getInvoiceIdServiceChargeItemMap(businessId, invoices);
        List<Long> serviceChargeIds = serviceChargeItemMap.values().stream()
                .flatMap(Collection::stream)
                .map(MoeGroomingInvoiceItem::getServiceId)
                .map(Integer::longValue)
                .distinct()
                .toList();
        Map<Long, ServiceChargeDTO> serviceChargeMap =
                serviceChargeService.getServiceChargeMap(businessId, serviceChargeIds);

        appointments.forEach(appointment -> {
            List<MoeGroomingInvoice> invoicesForApt =
                    invoiceMap.getOrDefault(appointment.getId(), Collections.emptyList());
            appointment.setInvoices(invoicesForApt.stream()
                    .map(invoice -> {
                        Integer invoiceId = invoice.getId();
                        GroomingReportApptInvoice reportInvoice = new GroomingReportApptInvoice();
                        BeanUtils.copyProperties(invoice, reportInvoice);
                        reportInvoice.setInvoiceId(invoiceId);
                        setAmountByItemType(
                                reportInvoice,
                                invoice,
                                productItemMap.get(invoiceId),
                                serviceChargeItemMap.get(invoiceId));
                        // product item staffId
                        if (productItemMap.containsKey(invoiceId)) {
                            reportInvoice.setSellProductStaffIds(productItemMap.get(invoiceId).stream()
                                    .map(MoeGroomingInvoiceItem::getStaffId)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList()));
                        }
                        // service charge item name list
                        reportInvoice.setServiceChargeList(
                                getServiceChargeName(serviceChargeItemMap.get(invoiceId), serviceChargeMap));
                        return reportInvoice;
                    })
                    .collect(Collectors.toList()));
        });

        return appointments;
    }

    /**
     * 设置 service/product/service-charge 相关金额字段
     *
     * @param dto                report invoice 对象
     * @param invoice            invoice 对象
     * @param productItems       product items
     * @param serviceChargeItems service charge items
     */
    private void setAmountByItemType(
            ReportBaseAmountDTO dto,
            MoeGroomingInvoice invoice,
            List<MoeGroomingInvoiceItem> productItems,
            List<MoeGroomingInvoiceItem> serviceChargeItems) {
        BigDecimal totalProductPrice = BigDecimal.ZERO;
        BigDecimal totalProductSale = BigDecimal.ZERO;
        BigDecimal totalProductTax = BigDecimal.ZERO;
        BigDecimal totalProductDiscount = BigDecimal.ZERO;
        BigDecimal totalServiceChargePrice = BigDecimal.ZERO;
        BigDecimal totalServiceChargeSale = BigDecimal.ZERO;
        BigDecimal totalServiceChargeTax = BigDecimal.ZERO;
        BigDecimal totalServiceChargeDiscount = BigDecimal.ZERO;

        if (!CollectionUtils.isEmpty(productItems)) {
            for (MoeGroomingInvoiceItem item : productItems) {
                totalProductPrice = AmountUtils.sum(totalProductPrice, item.getTotalListPrice());
                totalProductSale = AmountUtils.sum(
                        totalProductSale, item.getTotalSalePrice().add(item.getTaxAmount()));
                totalProductTax = AmountUtils.sum(totalProductTax, item.getTaxAmount());
                totalProductDiscount = AmountUtils.sum(totalProductDiscount, item.getDiscountAmount());
            }
        }

        if (!CollectionUtils.isEmpty(serviceChargeItems)) {
            for (MoeGroomingInvoiceItem item : serviceChargeItems) {
                totalServiceChargePrice = AmountUtils.sum(totalServiceChargePrice, item.getTotalListPrice());
                totalServiceChargeSale = AmountUtils.sum(
                        totalServiceChargeSale, item.getTotalSalePrice().add(item.getTaxAmount()));
                totalServiceChargeTax = AmountUtils.sum(totalServiceChargeTax, item.getTaxAmount());
                totalServiceChargeDiscount = AmountUtils.sum(totalServiceChargeDiscount, item.getDiscountAmount());
            }
        }

        dto.setTotalProductPrice(totalProductPrice);
        dto.setTotalServiceChargePrice(totalServiceChargePrice);
        dto.setTotalServicePrice(
                AmountUtils.subtract(invoice.getSubTotalAmount(), totalProductPrice, totalServiceChargePrice));
        dto.setTotalProductSale(totalProductSale);
        dto.setTotalServiceChargeSale(totalServiceChargeSale);
        dto.setTotalServiceSale(AmountUtils.subtract(
                invoice.getTotalAmount(), totalProductSale, totalServiceChargeSale, invoice.getConvenienceFee()));
        dto.setProductTaxAmount(totalProductTax);
        dto.setServiceChargeTaxAmount(totalServiceChargeTax);
        dto.setServiceTaxAmount(AmountUtils.subtract(invoice.getTaxAmount(), totalProductTax, totalServiceChargeTax));
        dto.setProductDiscountAmount(totalProductDiscount);
        dto.setServiceChargeDiscountAmount(totalServiceChargeDiscount);
        dto.setServiceDiscountAmount(
                AmountUtils.subtract(invoice.getDiscountAmount(), totalProductDiscount, totalServiceChargeDiscount));
    }

    public List<String> getServiceChargeName(
            List<MoeGroomingInvoiceItem> serviceChargeItems, Map<Long, ServiceChargeDTO> serviceChargeMap) {
        if (CollectionUtils.isEmpty(serviceChargeItems)) {
            return Collections.emptyList();
        }
        return serviceChargeItems.stream()
                .map(item -> {
                    if (serviceChargeMap.containsKey(item.getServiceId().longValue())) {
                        item.setServiceName(serviceChargeMap
                                .get(item.getServiceId().longValue())
                                .getName());
                    }
                    return item.getServiceName();
                })
                .distinct()
                .toList();
    }

    public List<GroomingReportWebAppointment> queryWebReportApptEmployee(
            Integer businessId, String startDate, String endDate, Integer staffId) {
        List<GroomingReportWebAppointment> appointments =
                appointmentMapper.queryWebReportApptEmployee(businessId, startDate, endDate, staffId);

        setOperationListFilterStaff(businessId, appointments, staffId);

        List<Integer> groomingIds =
                appointments.stream().map(GroomingReportWebAppointment::getId).collect(Collectors.toList());

        Map<Integer, MoeGroomingInvoice> invoiceMap = getGroomingIdInvoiceMap(businessId, groomingIds, null);
        List<MoeGroomingInvoice> invoices = new ArrayList<>(invoiceMap.values());
        // 查询 product/service-charge items，用于统计 product/service-charge 的价格
        Map<Integer, List<MoeGroomingInvoiceItem>> productItemMap = getInvoiceIdProductItemMap(businessId, invoices);
        Map<Integer, List<MoeGroomingInvoiceItem>> serviceChargeItemMap =
                getInvoiceIdServiceChargeItemMap(businessId, invoices);

        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId()));
        appointments.forEach(appointment -> {
            MoeGroomingInvoice invoice = invoiceMap.get(appointment.getId());
            setInvoiceAmount(appointment, invoice);
            setAmountByItemType(
                    appointment,
                    invoice,
                    productItemMap.get(invoice.getId()),
                    serviceChargeItemMap.get(invoice.getId()));
        });

        return appointments;
    }

    public void setOperationList(Integer businessId, List<GroomingReportWebAppointment> appointments) {
        List<Integer> groomingIds = appointments.stream()
                .map(GroomingReportWebAppointment::getId)
                .distinct()
                .toList();

        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingIdList(businessId, groomingIds);
        if (CollectionUtils.isEmpty(operationMap)) {
            return;
        }

        appointments.stream()
                .map(GroomingReportWebAppointment::getPetDetails)
                .flatMap(Collection::stream)
                .filter(reportWebApptPetDetail -> operationMap.containsKey(reportWebApptPetDetail.getId()))
                .forEach(reportWebApptPetDetail -> {
                    List<GroomingServiceOperationDTO> operationList = operationMap.get(reportWebApptPetDetail.getId());
                    reportWebApptPetDetail.setOperationList(operationList);
                });
    }

    public void setOperationListForApptDetail(Integer businessId, List<GroomingReportApptDetail> appointments) {
        List<Integer> groomingIds = appointments.stream()
                .map(GroomingReportApptDetail::getId)
                .distinct()
                .toList();

        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingIdList(businessId, groomingIds);
        if (CollectionUtils.isEmpty(operationMap)) {
            return;
        }

        appointments.stream()
                .map(GroomingReportApptDetail::getPetDetails)
                .flatMap(Collection::stream)
                .filter(reportWebApptPetDetail -> operationMap.containsKey(reportWebApptPetDetail.getId()))
                .forEach(reportWebApptPetDetail -> {
                    List<GroomingServiceOperationDTO> operationList = operationMap.get(reportWebApptPetDetail.getId());
                    reportWebApptPetDetail.setOperationList(operationList);
                });
    }

    public void setOperationListFilterStaff(
            Integer businessId, List<GroomingReportWebAppointment> appointments, Integer staffId) {
        List<Integer> groomingIds = appointments.stream()
                .map(GroomingReportWebAppointment::getId)
                .distinct()
                .toList();

        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingIdList(businessId, groomingIds);
        appointments.stream()
                .map(GroomingReportWebAppointment::getPetDetails)
                .flatMap(Collection::stream)
                .filter(reportWebApptPetDetail -> operationMap.containsKey(reportWebApptPetDetail.getId()))
                .forEach(reportWebApptPetDetail -> {
                    List<GroomingServiceOperationDTO> operationList = operationMap.get(reportWebApptPetDetail.getId());
                    if (Objects.nonNull(staffId)) {
                        operationList.removeIf(operation -> !staffId.equals(operation.getStaffId()));
                    }
                    reportWebApptPetDetail.setOperationList(operationList);
                });
    }

    /**
     * 设置 invoice 信息
     *
     * @param appointments
     * @param invoiceMap 非必传参数，如果传入则不再查询
     * @return
     */
    public List<GroomingReportApptDetail> fillReportApptInvoiceInfo(
            Integer businessId,
            List<GroomingReportApptDetail> appointments,
            Map<Integer, List<MoeGroomingInvoice>> invoiceMap,
            boolean queryProductInfo) {
        if (invoiceMap == null) {
            List<Integer> appointmentIds =
                    appointments.stream().map(GroomingReportApptDetail::getId).toList();
            invoiceMap = getGroomingIdInvoiceListMap(businessId, appointmentIds);
        }
        Map<Integer, List<MoeGroomingInvoiceItem>> productItemMap = Collections.emptyMap();
        Map<Integer, List<MoeGroomingInvoiceItem>> serviceChargeItemMap = Collections.emptyMap();
        if (queryProductInfo) {
            List<MoeGroomingInvoice> invoices =
                    invoiceMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            productItemMap = getInvoiceIdProductItemMap(businessId, invoices);
            serviceChargeItemMap = getInvoiceIdServiceChargeItemMap(businessId, invoices);
        }

        Map<Integer, List<MoeGroomingInvoiceItem>> finalProductItemMap = productItemMap;
        Map<Integer, List<MoeGroomingInvoiceItem>> finalServiceChargeItemMap = serviceChargeItemMap;
        for (GroomingReportApptDetail appointment : appointments) {
            List<MoeGroomingInvoice> invoices = invoiceMap.getOrDefault(appointment.getId(), Collections.emptyList());
            appointment.setInvoices(invoices.stream()
                    .map(invoice -> {
                        GroomingReportApptInvoice reportInvoice = new GroomingReportApptInvoice();
                        BeanUtils.copyProperties(invoice, reportInvoice);
                        reportInvoice.setInvoiceId(invoice.getId());
                        reportInvoice.setInvoiceType(invoice.getType());
                        // 填充 product/service-charge 金额字段
                        setAmountByItemType(
                                reportInvoice,
                                invoice,
                                finalProductItemMap.get(invoice.getId()),
                                finalServiceChargeItemMap.get(invoice.getId()));
                        return reportInvoice;
                    })
                    .collect(Collectors.toList()));
        }
        return appointments;
    }

    @Deprecated
    public void fillReportWebApptInvoiceInfo(Integer businessId, List<GroomingReportWebAppointment> appointments) {
        List<Integer> appointmentIds =
                appointments.stream().map(GroomingReportWebAppointment::getId).toList();
        Map<Integer, MoeGroomingInvoice> invoiceMap = getGroomingIdInvoiceMap(businessId, appointmentIds, null);
        List<MoeGroomingInvoice> invoices = new ArrayList<>(invoiceMap.values());
        // 查询 product/service-charge items，用于统计 product/service-charge 的价格
        Map<Integer, List<MoeGroomingInvoiceItem>> productItemMap = getInvoiceIdProductItemMap(businessId, invoices);
        Map<Integer, List<MoeGroomingInvoiceItem>> serviceChargeItemMap =
                getInvoiceIdServiceChargeItemMap(businessId, invoices);

        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId()));
        appointments.forEach(appointment -> {
            MoeGroomingInvoice invoice = invoiceMap.get(appointment.getId());
            setInvoiceAmount(appointment, invoice);
            setAmountByItemType(
                    appointment,
                    invoice,
                    productItemMap.get(invoice.getId()),
                    serviceChargeItemMap.get(invoice.getId()));
        });
    }

    public void fillInvoiceInfoForPayroll(Integer businessId, List<GroomingReportWebAppointment> appointments) {
        List<Integer> appointmentIds =
                appointments.stream().map(GroomingReportWebAppointment::getId).toList();
        Map<Integer, List<MoeGroomingInvoice>> invoiceMap = getInvoiceDetails(businessId, appointmentIds);

        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId()));
        appointments.forEach(appointment -> {
            var invoices = invoiceMap.get(appointment.getId());
            Optional<MoeGroomingInvoice> originInvoiceOption = invoices.stream()
                    .filter(OrderUtil::isOriginOrder)
                    .filter(invoice -> !Objects.equals(invoice.getType(), OrderSourceType.NO_SHOW.getSource()))
                    .findFirst();
            if (originInvoiceOption.isEmpty()) {
                return;
            }
            resetInvoice(appointment, originInvoiceOption.get());
            appointment.setInvoiceMap(
                    invoices.stream().collect(Collectors.toMap(MoeGroomingInvoice::getId, Function.identity())));
            appointment.setInvoiceIdToPetDetailsMap(getInvoiceIdToPetDetailsMap(invoices, appointment.getPetDetails()));
        });
    }

    public void resetInvoice(GroomingReportWebAppointment appointment, MoeGroomingInvoice invoice) {
        setInvoiceAmount(appointment, invoice);
        appointment.setInvoiceItems(invoice.getItems());

        List<MoeGroomingInvoiceItem> productItems = new ArrayList<>();
        List<MoeGroomingInvoiceItem> serviceChargeItems = new ArrayList<>();
        for (MoeGroomingInvoiceItem item : invoice.getItems()) {
            if (invoice.getHasProduct() && Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_PRODUCT.getType())) {
                productItems.add(item);
            }
            if (invoice.getHasServiceCharge()
                    && Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE_CHARGE.getType())) {
                serviceChargeItems.add(item);
            }
        }
        setAmountByItemType(appointment, invoice, productItems, serviceChargeItems);
    }

    private Map<Integer, List<ReportWebApptPetDetail>> getInvoiceIdToPetDetailsMap(
            List<MoeGroomingInvoice> invoices, List<ReportWebApptPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(invoices) || CollectionUtils.isEmpty(petDetails)) {
            return Collections.emptyMap();
        }
        // 只有一个 invoice 时，所有 petDetail 都映射到 origin invoice
        if (invoices.size() == 1) {
            return Map.of(invoices.get(0).getId(), petDetails);
        }
        var itemKeyToPetDetails = petDetails.stream().collect(Collectors.groupingBy(OrderUtil::buildItemKey));
        var oldItemKeyToPetDetails =
                petDetails.stream().collect(Collectors.groupingBy(OrderUtil::buildItemKeyForOldOrder));

        Map<Integer, List<ReportWebApptPetDetail>> invoiceToPetDetails = new HashMap<>();
        for (var invoice : invoices) {
            for (var item : invoice.getItems()) {
                if (!Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE.getType())) {
                    continue;
                }
                // pet id 不为 null 且大于 0 是新订单，否则是老订单
                if (CommonUtil.isNormal(item.getPetId())
                        && itemKeyToPetDetails.containsKey(OrderUtil.buildItemKey(item))) {
                    // item 匹配到 petDetails, 则把 petDetails 分组到当前 invoice 中
                    invoiceToPetDetails
                            .computeIfAbsent(invoice.getId(), invoiceId -> new ArrayList<>())
                            .addAll(itemKeyToPetDetails.get(OrderUtil.buildItemKey(item)));
                } else if (oldItemKeyToPetDetails.containsKey(OrderUtil.buildItemKeyForOldOrder(item))) {
                    invoiceToPetDetails
                            .computeIfAbsent(invoice.getId(), invoiceId -> new ArrayList<>())
                            .addAll(oldItemKeyToPetDetails.get(OrderUtil.buildItemKeyForOldOrder(item)));
                }
            }
        }
        return invoiceToPetDetails;
    }

    public List<GroomingReportWebAppointment> queryAppointmentWithInvoice(
            Integer businessId, List<GroomingReportWebAppointment> appointments) {
        List<Integer> groomingIds =
                appointments.stream().map(GroomingReportWebAppointment::getId).collect(Collectors.toList());
        Map<Integer, MoeGroomingInvoice> invoiceMap = getGroomingIdInvoiceMap(businessId, groomingIds, null);

        List<MoeGroomingInvoice> invoices = new ArrayList<>(invoiceMap.values());
        // 查询 product/service-charge items，用于统计 product/service-charge 的价格
        Map<Integer, List<MoeGroomingInvoiceItem>> productItemMap = getInvoiceIdProductItemMap(businessId, invoices);
        Map<Integer, List<MoeGroomingInvoiceItem>> serviceChargeItemMap =
                getInvoiceIdServiceChargeItemMap(businessId, invoices);

        appointments.removeIf(appointment -> !invoiceMap.containsKey(appointment.getId()));
        appointments.forEach(appointment -> {
            MoeGroomingInvoice invoice = invoiceMap.get(appointment.getId());
            setInvoiceAmount(appointment, invoice);
            setAmountByItemType(
                    appointment,
                    invoice,
                    productItemMap.get(invoice.getId()),
                    serviceChargeItemMap.get(invoice.getId()));
        });

        return appointments;
    }

    public Map<Integer, MoeGroomingInvoice> getGroomingIdInvoiceMap(
            Integer businessId, List<Integer> groomingIds, String type) {
        List<MoeGroomingInvoice> invoices = orderService.getListByGroomingIds(businessId, groomingIds, type);
        return invoices.stream()
                // 这个场景下只处理 origin order
                .filter(OrderUtil::isOriginOrder)
                .collect(Collectors.toMap(
                        MoeGroomingInvoice::getGroomingId, invoice -> invoice, (invoice1, invoice2) -> invoice2));
    }

    public Map<Integer, List<MoeGroomingInvoice>> getInvoiceDetails(Integer businessId, List<Integer> groomingIds) {
        List<MoeGroomingInvoice> invoices = orderService.getInvoicesByGroomingIds(businessId, groomingIds);
        return invoices.stream().collect(Collectors.groupingBy(MoeGroomingInvoice::getGroomingId));
    }

    /**
     * 查询 groomingIds 对应的 invoice 列表，包括 appointment/noshow
     *
     * @param businessId  businessId
     * @param groomingIds groomingIds
     * @return key-groomingId, value-invoiceList
     */
    private Map<Integer, List<MoeGroomingInvoice>> getGroomingIdInvoiceListMap(
            Integer businessId, List<Integer> groomingIds) {
        return orderService.getListByGroomingIds(businessId, groomingIds, null).stream()
                .collect(Collectors.groupingBy(MoeGroomingInvoice::getGroomingId));
    }

    /**
     * 查询 invoiceIds 对应的 productItem 列表
     *
     * @param businessId businessId
     * @param invoices   invoices
     * @return key-invoiceId, value-productItems
     */
    private Map<Integer, List<MoeGroomingInvoiceItem>> getInvoiceIdProductItemMap(
            Integer businessId, List<MoeGroomingInvoice> invoices) {
        List<Integer> invoiceIdsWithProduct = invoices.stream()
                .filter(invoice -> Objects.equals(invoice.getType(), OrderSourceType.APPOINTMENT.getSource())
                        && Boolean.TRUE.equals(invoice.getHasProduct()))
                .map(MoeGroomingInvoice::getId)
                .distinct()
                .toList();
        return orderService
                .getInvoiceItemByInvoiceIds(
                        businessId, invoiceIdsWithProduct, OrderItemType.ITEM_TYPE_PRODUCT.getType())
                .stream()
                .collect(Collectors.groupingBy(MoeGroomingInvoiceItem::getInvoiceId));
    }

    /**
     * 查询 invoiceIds 对应的 serviceChargeItem 列表
     *
     * @param businessId businessId
     * @param invoices   invoices
     * @return key-invoiceId, value-serviceChargeItems
     */
    private Map<Integer, List<MoeGroomingInvoiceItem>> getInvoiceIdServiceChargeItemMap(
            Integer businessId, List<MoeGroomingInvoice> invoices) {
        List<Integer> invoiceIdsWithServiceCharge = invoices.stream()
                .filter(invoice -> Objects.equals(invoice.getType(), OrderSourceType.APPOINTMENT.getSource())
                        && Boolean.TRUE.equals(invoice.getHasServiceCharge()))
                .map(MoeGroomingInvoice::getId)
                .distinct()
                .toList();
        return orderService
                .getInvoiceItemByInvoiceIds(
                        businessId, invoiceIdsWithServiceCharge, OrderItemType.ITEM_TYPE_SERVICE_CHARGE.getType())
                .stream()
                .collect(Collectors.groupingBy(MoeGroomingInvoiceItem::getInvoiceId));
    }

    public Map<Integer, BigDecimal> getRefundMap(Set<Integer> invoiceIds) {
        List<RefundDTO> refunds =
                iPaymentRefundClient.getRefunds(PaymentMethodEnum.MODULE_GROOMING, new ArrayList<>(invoiceIds));

        if (CollectionUtils.isEmpty(refunds)) {
            return Collections.emptyMap();
        }

        Map<Integer, BigDecimal> refundMap = new HashMap<>();
        refunds.forEach(refund -> {
            if (!Objects.equals(refund.getStatus(), PaymentStatusEnum.FAILED)) {
                BigDecimal totalAmount = refundMap.getOrDefault(refund.getInvoiceId(), BigDecimal.ZERO);
                totalAmount = totalAmount.add(refund.getAmount());
                refundMap.put(refund.getInvoiceId(), totalAmount);
            }
        });
        return refundMap;
    }

    /**
     * 获取appointments的invoice items, 用于计算tax
     *
     * @param appointments
     * @return
     */
    public Map<Integer, List<MoeGroomingInvoiceItem>> getInvoiceItemMap(
            List<GroomingReportWebAppointment> appointments) {
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyMap();
        }
        // 获取服务的invoice item
        Map<Integer, GroomingReportWebAppointment> invoiceIdMap = new HashMap<>();
        appointments.forEach(a -> invoiceIdMap.put(a.getInvoiceId(), a));
        List<MoeGroomingInvoiceItem> invoiceItems = orderService.getInvoiceItemByInvoiceIds(
                appointments.get(0).getBusinessId(), new ArrayList<>(invoiceIdMap.keySet()));

        Map<Integer, List<MoeGroomingInvoiceItem>> invoiceItemMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(invoiceItems)) {
            invoiceItems.forEach(item -> {
                GroomingReportWebAppointment a = invoiceIdMap.get(item.getInvoiceId());
                List<MoeGroomingInvoiceItem> items = invoiceItemMap.computeIfAbsent(a.getId(), i -> new ArrayList<>());
                items.add(item);
            });
        }
        return invoiceItemMap;
    }
}
