package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.params.appointment.OperationParams;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ServiceOperationMapper {
    ServiceOperationMapper INSTANCE = Mappers.getMapper(ServiceOperationMapper.class);

    @Mapping(target = "companyId", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "petId", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "groomingServiceId", ignore = true)
    @Mapping(target = "groomingId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "comment", ignore = true)
    @Mapping(target = "businessId", ignore = true)
    MoeGroomingServiceOperation vo2Entity(OperationParams vo);
}
