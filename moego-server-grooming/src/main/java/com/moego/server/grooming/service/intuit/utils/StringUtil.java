package com.moego.server.grooming.service.intuit.utils;

import org.springframework.util.StringUtils;

/**
 * 字符串工具类
 */
public class StringUtil {

    /**
     * <a href="https://support.chargebee.com/support/solutions/articles/260046">原问题解决方案</a>
     * 替换掉不合法的字符
     * 把 tabs, newlines or ':' 替换 成 下划线 '_'
     *
     * @param str 原字符串
     * @return 替换后的字符串
     */
    public static String replaceInvalidString(String str) {
        if (!StringUtils.hasText(str)) {
            return str;
        }
        return str.replaceAll("[\\t\\n:]", "_");
    }
}
