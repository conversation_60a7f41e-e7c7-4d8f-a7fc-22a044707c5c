package com.moego.server.grooming.mapperbean;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import java.math.BigDecimal;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_appointment
 */
public class MoeGroomingAppointment {
    /**
     * Database Column Remarks:
     *   预约订单id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   订单流水号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.order_id
     *
     * @mbg.generated
     */
    private String orderId;

    /**
     * Database Column Remarks:
     *   店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   客户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   预约日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.appointment_date
     *
     * @mbg.generated
     */
    private String appointmentDate;

    /**
     * Database Column Remarks:
     *   预约服务开始时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.appointment_start_time
     *
     * @mbg.generated
     */
    private Integer appointmentStartTime;

    /**
     * Database Column Remarks:
     *   预约服务结束时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.appointment_end_time
     *
     * @mbg.generated
     */
    private Integer appointmentEndTime;

    /**
     * Database Column Remarks:
     *   是否在waiting list内  0否  1是
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.is_waiting_list
     *
     * @mbg.generated
     */
    private Byte isWaitingList;

    /**
     * Database Column Remarks:
     *   把订单移动到waiting list的员工
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.move_waiting_by
     *
     * @mbg.generated
     */
    private Integer moveWaitingBy;

    /**
     * Database Column Remarks:
     *   订单确认时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.confirmed_time
     *
     * @mbg.generated
     */
    private Long confirmedTime;

    /**
     * Database Column Remarks:
     *   真实服务开始时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.check_in_time
     *
     * @mbg.generated
     */
    private Long checkInTime;

    /**
     * Database Column Remarks:
     *   真实服务结束时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.check_out_time
     *
     * @mbg.generated
     */
    private Long checkOutTime;

    /**
     * Database Column Remarks:
     *   取消时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.canceled_time
     *
     * @mbg.generated
     */
    private Long canceledTime;

    /**
     * Database Column Remarks:
     *   1未确认 2确认 3已完成 4已取消
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   是否为block 1-是 2-否
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.is_block
     *
     * @mbg.generated
     */
    private Integer isBlock;

    /**
     * Database Column Remarks:
     *   1 未确认 2 confirm 3 waitinglist 4 cancle
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.book_online_status
     *
     * @mbg.generated
     */
    private Byte bookOnlineStatus;

    /**
     * Database Column Remarks:
     *   客户地址id  （mm_customer_address   id）
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.customer_address_id
     *
     * @mbg.generated
     */
    private Integer customerAddressId;

    /**
     * Database Column Remarks:
     *   重复预约id（mm_repeat_appointment）为0则表示普通订单
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.repeat_id
     *
     * @mbg.generated
     */
    private Integer repeatId;

    /**
     * Database Column Remarks:
     *   是否已完全支付 1是 2否( 部分支付为2)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.is_paid
     *
     * @mbg.generated
     */
    private Byte isPaid;

    /**
     * Database Column Remarks:
     *   订单颜色
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.color_code
     *
     * @mbg.generated
     */
    private String colorCode;

    /**
     * Database Column Remarks:
     *   用户爽约 1-爽约 2-没有爽约
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.no_show
     *
     * @mbg.generated
     */
    private Byte noShow;

    /**
     * Database Column Remarks:
     *   noShow费用，扣费后同步
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.no_show_fee
     *
     * @mbg.generated
     */
    private BigDecimal noShowFee;

    /**
     * Database Column Remarks:
     *   是否推送过通知 1-是 2-否
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.is_pust_notification
     *
     * @mbg.generated
     */
    private Byte isPustNotification;

    /**
     * Database Column Remarks:
     *   0-by business, 1-by customer reply msg, 2-by delete pet
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.cancel_by_type
     *
     * @mbg.generated
     */
    private Byte cancelByType;

    /**
     * Database Column Remarks:
     *   预约取消员工 business_account_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.cancel_by
     *
     * @mbg.generated
     */
    private Integer cancelBy;

    /**
     * Database Column Remarks:
     *   0-by business, 1-by customer reply msg
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.confirm_by_type
     *
     * @mbg.generated
     */
    private Byte confirmByType;

    /**
     * Database Column Remarks:
     *   预约确定员工 business_account_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.confirm_by
     *
     * @mbg.generated
     */
    private Integer confirmBy;

    /**
     * Database Column Remarks:
     *   预约创建员工
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.created_by_id
     *
     * @mbg.generated
     */
    private Integer createdById;

    /**
     * Database Column Remarks:
     *   提交数据时，是否超出服务区域
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.out_of_area
     *
     * @mbg.generated
     */
    private Byte outOfArea;

    /**
     * Database Column Remarks:
     *   是否弃用 默认0 未弃用 1弃用  修改repeat规则后弃用的旧数据
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.is_deprecate
     *
     * @mbg.generated
     */
    private Integer isDeprecate;

    /**
     * Database Column Remarks:
     *   订单创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   最后修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   22168-book-online 22018-web,17216-android,17802-ios
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.source
     *
     * @mbg.generated
     */
    private Integer source;

    /**
     * Database Column Remarks:
     *   reschedule前的预约日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.old_appointment_date
     *
     * @mbg.generated
     */
    private String oldAppointmentDate;

    /**
     * Database Column Remarks:
     *   reschedule前的预约服务开始时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.old_appointment_start_time
     *
     * @mbg.generated
     */
    private Integer oldAppointmentStartTime;

    /**
     * Database Column Remarks:
     *   reschedule前的预约服务结束时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.old_appointment_end_time
     *
     * @mbg.generated
     */
    private Integer oldAppointmentEndTime;

    /**
     * Database Column Remarks:
     *   旧版本的apptId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.old_appt_id
     *
     * @mbg.generated
     */
    private Integer oldApptId;

    /**
     * Database Column Remarks:
     *   repeat 类型，1-普通 repeat，2-Smart schedule repeat
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.schedule_type
     *
     * @mbg.generated
     */
    private Byte scheduleType;

    /**
     * Database Column Remarks:
     *   This field indicates the third-party platform through which the appointment was initiated and entered into our system. Possible values: GOOGLE, INSTAGRAM, FACEBOOK, and other relevant platforms.
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.source_platform
     *
     * @mbg.generated
     */
    private String sourcePlatform;

    /**
     * Database Column Remarks:
     *   设置为 ready for pickup 的时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.ready_time
     *
     * @mbg.generated
     */
    private Long readyTime;

    /**
     * Database Column Remarks:
     *   发送 ready for pickup 通知的状态, 0 - 未发送, 1 - 已发送, 2 - 发送失败
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.pickup_notification_send_status
     *
     * @mbg.generated
     */
    private Integer pickupNotificationSendStatus;

    /**
     * Database Column Remarks:
     *   发送 ready for pickup 通知失败的原因
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.pickup_notification_failed_reason
     *
     * @mbg.generated
     */
    private String pickupNotificationFailedReason;

    /**
     * Database Column Remarks:
     *   checkin 之前的状态, 0 - unknown, 1 - unconfirmed, 2 - confirmed, 3 - finished, 4 - cancelled, 5 - ready, 6 - checkin
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.status_before_checkin
     *
     * @mbg.generated
     */
    private AppointmentStatusEnum statusBeforeCheckin;

    /**
     * Database Column Remarks:
     *   ready for pickup 之前的状态, 0 - unknown, 1 - unconfirmed, 2 - confirmed, 3 - finished, 4 - cancelled, 5 - ready, 6 - checkin
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.status_before_ready
     *
     * @mbg.generated
     */
    private AppointmentStatusEnum statusBeforeReady;

    /**
     * Database Column Remarks:
     *   finish 之前的状态, 0 - unknown, 1 - unconfirmed, 2 - confirmed, 3 - finished, 4 - cancelled, 5 - ready, 6 - checkin
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.status_before_finish
     *
     * @mbg.generated
     */
    private AppointmentStatusEnum statusBeforeFinish;

    /**
     * Database Column Remarks:
     *   当前 appointment 是否包含时间，新增这个字段的原因是因为 start_time 字段默认值 0 不能区分是 0 时还是没有 time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.no_start_time
     *
     * @mbg.generated
     */
    private Boolean noStartTime;

    /**
     * Database Column Remarks:
     *   更新人 id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.updated_by_id
     *
     * @mbg.generated
     */
    private Long updatedById;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   Is auto accept OB appointment? Default value is false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.is_auto_accept
     *
     * @mbg.generated
     */
    private Boolean isAutoAccept;

    /**
     * Database Column Remarks:
     *   0有 appt 无 waitlist 1无 appt 有 waitlist 2有 appt 有 waitlist
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.wait_list_status
     *
     * @mbg.generated
     */
    private WaitListStatusEnum waitListStatus;

    /**
     * Database Column Remarks:
     *   结束日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.appointment_end_date
     *
     * @mbg.generated
     */
    private String appointmentEndDate;

    /**
     * Database Column Remarks:
     *   包含的服务类型，用位图表示，二进制从低到高依次为 grooming, boarding, daycare, evaluation
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.service_type_include
     *
     * @mbg.generated
     */
    private Integer serviceTypeInclude;

    /**
     * Database Column Remarks:
     *   staff id who marked the appointment as no-show
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_appointment.no_show_by
     *
     * @mbg.generated
     */
    private Long noShowBy;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.id
     *
     * @return the value of moe_grooming_appointment.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.id
     *
     * @param id the value for moe_grooming_appointment.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.order_id
     *
     * @return the value of moe_grooming_appointment.order_id
     *
     * @mbg.generated
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.order_id
     *
     * @param orderId the value for moe_grooming_appointment.order_id
     *
     * @mbg.generated
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.business_id
     *
     * @return the value of moe_grooming_appointment.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.business_id
     *
     * @param businessId the value for moe_grooming_appointment.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.customer_id
     *
     * @return the value of moe_grooming_appointment.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.customer_id
     *
     * @param customerId the value for moe_grooming_appointment.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.appointment_date
     *
     * @return the value of moe_grooming_appointment.appointment_date
     *
     * @mbg.generated
     */
    public String getAppointmentDate() {
        return appointmentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.appointment_date
     *
     * @param appointmentDate the value for moe_grooming_appointment.appointment_date
     *
     * @mbg.generated
     */
    public void setAppointmentDate(String appointmentDate) {
        this.appointmentDate = appointmentDate == null ? null : appointmentDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.appointment_start_time
     *
     * @return the value of moe_grooming_appointment.appointment_start_time
     *
     * @mbg.generated
     */
    public Integer getAppointmentStartTime() {
        return appointmentStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.appointment_start_time
     *
     * @param appointmentStartTime the value for moe_grooming_appointment.appointment_start_time
     *
     * @mbg.generated
     */
    public void setAppointmentStartTime(Integer appointmentStartTime) {
        this.appointmentStartTime = appointmentStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.appointment_end_time
     *
     * @return the value of moe_grooming_appointment.appointment_end_time
     *
     * @mbg.generated
     */
    public Integer getAppointmentEndTime() {
        return appointmentEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.appointment_end_time
     *
     * @param appointmentEndTime the value for moe_grooming_appointment.appointment_end_time
     *
     * @mbg.generated
     */
    public void setAppointmentEndTime(Integer appointmentEndTime) {
        this.appointmentEndTime = appointmentEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.is_waiting_list
     *
     * @return the value of moe_grooming_appointment.is_waiting_list
     *
     * @mbg.generated
     */
    public Byte getIsWaitingList() {
        return isWaitingList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.is_waiting_list
     *
     * @param isWaitingList the value for moe_grooming_appointment.is_waiting_list
     *
     * @mbg.generated
     */
    public void setIsWaitingList(Byte isWaitingList) {
        this.isWaitingList = isWaitingList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.move_waiting_by
     *
     * @return the value of moe_grooming_appointment.move_waiting_by
     *
     * @mbg.generated
     */
    public Integer getMoveWaitingBy() {
        return moveWaitingBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.move_waiting_by
     *
     * @param moveWaitingBy the value for moe_grooming_appointment.move_waiting_by
     *
     * @mbg.generated
     */
    public void setMoveWaitingBy(Integer moveWaitingBy) {
        this.moveWaitingBy = moveWaitingBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.confirmed_time
     *
     * @return the value of moe_grooming_appointment.confirmed_time
     *
     * @mbg.generated
     */
    public Long getConfirmedTime() {
        return confirmedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.confirmed_time
     *
     * @param confirmedTime the value for moe_grooming_appointment.confirmed_time
     *
     * @mbg.generated
     */
    public void setConfirmedTime(Long confirmedTime) {
        this.confirmedTime = confirmedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.check_in_time
     *
     * @return the value of moe_grooming_appointment.check_in_time
     *
     * @mbg.generated
     */
    public Long getCheckInTime() {
        return checkInTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.check_in_time
     *
     * @param checkInTime the value for moe_grooming_appointment.check_in_time
     *
     * @mbg.generated
     */
    public void setCheckInTime(Long checkInTime) {
        this.checkInTime = checkInTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.check_out_time
     *
     * @return the value of moe_grooming_appointment.check_out_time
     *
     * @mbg.generated
     */
    public Long getCheckOutTime() {
        return checkOutTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.check_out_time
     *
     * @param checkOutTime the value for moe_grooming_appointment.check_out_time
     *
     * @mbg.generated
     */
    public void setCheckOutTime(Long checkOutTime) {
        this.checkOutTime = checkOutTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.canceled_time
     *
     * @return the value of moe_grooming_appointment.canceled_time
     *
     * @mbg.generated
     */
    public Long getCanceledTime() {
        return canceledTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.canceled_time
     *
     * @param canceledTime the value for moe_grooming_appointment.canceled_time
     *
     * @mbg.generated
     */
    public void setCanceledTime(Long canceledTime) {
        this.canceledTime = canceledTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.status
     *
     * @return the value of moe_grooming_appointment.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.status
     *
     * @param status the value for moe_grooming_appointment.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.is_block
     *
     * @return the value of moe_grooming_appointment.is_block
     *
     * @mbg.generated
     */
    public Integer getIsBlock() {
        return isBlock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.is_block
     *
     * @param isBlock the value for moe_grooming_appointment.is_block
     *
     * @mbg.generated
     */
    public void setIsBlock(Integer isBlock) {
        this.isBlock = isBlock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.book_online_status
     *
     * @return the value of moe_grooming_appointment.book_online_status
     *
     * @mbg.generated
     */
    public Byte getBookOnlineStatus() {
        return bookOnlineStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.book_online_status
     *
     * @param bookOnlineStatus the value for moe_grooming_appointment.book_online_status
     *
     * @mbg.generated
     */
    public void setBookOnlineStatus(Byte bookOnlineStatus) {
        this.bookOnlineStatus = bookOnlineStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.customer_address_id
     *
     * @return the value of moe_grooming_appointment.customer_address_id
     *
     * @mbg.generated
     */
    public Integer getCustomerAddressId() {
        return customerAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.customer_address_id
     *
     * @param customerAddressId the value for moe_grooming_appointment.customer_address_id
     *
     * @mbg.generated
     */
    public void setCustomerAddressId(Integer customerAddressId) {
        this.customerAddressId = customerAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.repeat_id
     *
     * @return the value of moe_grooming_appointment.repeat_id
     *
     * @mbg.generated
     */
    public Integer getRepeatId() {
        return repeatId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.repeat_id
     *
     * @param repeatId the value for moe_grooming_appointment.repeat_id
     *
     * @mbg.generated
     */
    public void setRepeatId(Integer repeatId) {
        this.repeatId = repeatId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.is_paid
     *
     * @return the value of moe_grooming_appointment.is_paid
     *
     * @mbg.generated
     */
    public Byte getIsPaid() {
        return isPaid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.is_paid
     *
     * @param isPaid the value for moe_grooming_appointment.is_paid
     *
     * @mbg.generated
     */
    public void setIsPaid(Byte isPaid) {
        this.isPaid = isPaid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.color_code
     *
     * @return the value of moe_grooming_appointment.color_code
     *
     * @mbg.generated
     */
    public String getColorCode() {
        return colorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.color_code
     *
     * @param colorCode the value for moe_grooming_appointment.color_code
     *
     * @mbg.generated
     */
    public void setColorCode(String colorCode) {
        this.colorCode = colorCode == null ? null : colorCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.no_show
     *
     * @return the value of moe_grooming_appointment.no_show
     *
     * @mbg.generated
     */
    public Byte getNoShow() {
        return noShow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.no_show
     *
     * @param noShow the value for moe_grooming_appointment.no_show
     *
     * @mbg.generated
     */
    public void setNoShow(Byte noShow) {
        this.noShow = noShow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.no_show_fee
     *
     * @return the value of moe_grooming_appointment.no_show_fee
     *
     * @mbg.generated
     */
    public BigDecimal getNoShowFee() {
        return noShowFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.no_show_fee
     *
     * @param noShowFee the value for moe_grooming_appointment.no_show_fee
     *
     * @mbg.generated
     */
    public void setNoShowFee(BigDecimal noShowFee) {
        this.noShowFee = noShowFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.is_pust_notification
     *
     * @return the value of moe_grooming_appointment.is_pust_notification
     *
     * @mbg.generated
     */
    public Byte getIsPustNotification() {
        return isPustNotification;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.is_pust_notification
     *
     * @param isPustNotification the value for moe_grooming_appointment.is_pust_notification
     *
     * @mbg.generated
     */
    public void setIsPustNotification(Byte isPustNotification) {
        this.isPustNotification = isPustNotification;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.cancel_by_type
     *
     * @return the value of moe_grooming_appointment.cancel_by_type
     *
     * @mbg.generated
     */
    public Byte getCancelByType() {
        return cancelByType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.cancel_by_type
     *
     * @param cancelByType the value for moe_grooming_appointment.cancel_by_type
     *
     * @mbg.generated
     */
    public void setCancelByType(Byte cancelByType) {
        this.cancelByType = cancelByType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.cancel_by
     *
     * @return the value of moe_grooming_appointment.cancel_by
     *
     * @mbg.generated
     */
    public Integer getCancelBy() {
        return cancelBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.cancel_by
     *
     * @param cancelBy the value for moe_grooming_appointment.cancel_by
     *
     * @mbg.generated
     */
    public void setCancelBy(Integer cancelBy) {
        this.cancelBy = cancelBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.confirm_by_type
     *
     * @return the value of moe_grooming_appointment.confirm_by_type
     *
     * @mbg.generated
     */
    public Byte getConfirmByType() {
        return confirmByType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.confirm_by_type
     *
     * @param confirmByType the value for moe_grooming_appointment.confirm_by_type
     *
     * @mbg.generated
     */
    public void setConfirmByType(Byte confirmByType) {
        this.confirmByType = confirmByType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.confirm_by
     *
     * @return the value of moe_grooming_appointment.confirm_by
     *
     * @mbg.generated
     */
    public Integer getConfirmBy() {
        return confirmBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.confirm_by
     *
     * @param confirmBy the value for moe_grooming_appointment.confirm_by
     *
     * @mbg.generated
     */
    public void setConfirmBy(Integer confirmBy) {
        this.confirmBy = confirmBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.created_by_id
     *
     * @return the value of moe_grooming_appointment.created_by_id
     *
     * @mbg.generated
     */
    public Integer getCreatedById() {
        return createdById;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.created_by_id
     *
     * @param createdById the value for moe_grooming_appointment.created_by_id
     *
     * @mbg.generated
     */
    public void setCreatedById(Integer createdById) {
        this.createdById = createdById;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.out_of_area
     *
     * @return the value of moe_grooming_appointment.out_of_area
     *
     * @mbg.generated
     */
    public Byte getOutOfArea() {
        return outOfArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.out_of_area
     *
     * @param outOfArea the value for moe_grooming_appointment.out_of_area
     *
     * @mbg.generated
     */
    public void setOutOfArea(Byte outOfArea) {
        this.outOfArea = outOfArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.is_deprecate
     *
     * @return the value of moe_grooming_appointment.is_deprecate
     *
     * @mbg.generated
     */
    public Integer getIsDeprecate() {
        return isDeprecate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.is_deprecate
     *
     * @param isDeprecate the value for moe_grooming_appointment.is_deprecate
     *
     * @mbg.generated
     */
    public void setIsDeprecate(Integer isDeprecate) {
        this.isDeprecate = isDeprecate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.create_time
     *
     * @return the value of moe_grooming_appointment.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.create_time
     *
     * @param createTime the value for moe_grooming_appointment.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.update_time
     *
     * @return the value of moe_grooming_appointment.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.update_time
     *
     * @param updateTime the value for moe_grooming_appointment.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.source
     *
     * @return the value of moe_grooming_appointment.source
     *
     * @mbg.generated
     */
    public Integer getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.source
     *
     * @param source the value for moe_grooming_appointment.source
     *
     * @mbg.generated
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.old_appointment_date
     *
     * @return the value of moe_grooming_appointment.old_appointment_date
     *
     * @mbg.generated
     */
    public String getOldAppointmentDate() {
        return oldAppointmentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.old_appointment_date
     *
     * @param oldAppointmentDate the value for moe_grooming_appointment.old_appointment_date
     *
     * @mbg.generated
     */
    public void setOldAppointmentDate(String oldAppointmentDate) {
        this.oldAppointmentDate = oldAppointmentDate == null ? null : oldAppointmentDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.old_appointment_start_time
     *
     * @return the value of moe_grooming_appointment.old_appointment_start_time
     *
     * @mbg.generated
     */
    public Integer getOldAppointmentStartTime() {
        return oldAppointmentStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.old_appointment_start_time
     *
     * @param oldAppointmentStartTime the value for moe_grooming_appointment.old_appointment_start_time
     *
     * @mbg.generated
     */
    public void setOldAppointmentStartTime(Integer oldAppointmentStartTime) {
        this.oldAppointmentStartTime = oldAppointmentStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.old_appointment_end_time
     *
     * @return the value of moe_grooming_appointment.old_appointment_end_time
     *
     * @mbg.generated
     */
    public Integer getOldAppointmentEndTime() {
        return oldAppointmentEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.old_appointment_end_time
     *
     * @param oldAppointmentEndTime the value for moe_grooming_appointment.old_appointment_end_time
     *
     * @mbg.generated
     */
    public void setOldAppointmentEndTime(Integer oldAppointmentEndTime) {
        this.oldAppointmentEndTime = oldAppointmentEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.old_appt_id
     *
     * @return the value of moe_grooming_appointment.old_appt_id
     *
     * @mbg.generated
     */
    public Integer getOldApptId() {
        return oldApptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.old_appt_id
     *
     * @param oldApptId the value for moe_grooming_appointment.old_appt_id
     *
     * @mbg.generated
     */
    public void setOldApptId(Integer oldApptId) {
        this.oldApptId = oldApptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.schedule_type
     *
     * @return the value of moe_grooming_appointment.schedule_type
     *
     * @mbg.generated
     */
    public Byte getScheduleType() {
        return scheduleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.schedule_type
     *
     * @param scheduleType the value for moe_grooming_appointment.schedule_type
     *
     * @mbg.generated
     */
    public void setScheduleType(Byte scheduleType) {
        this.scheduleType = scheduleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.source_platform
     *
     * @return the value of moe_grooming_appointment.source_platform
     *
     * @mbg.generated
     */
    public String getSourcePlatform() {
        return sourcePlatform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.source_platform
     *
     * @param sourcePlatform the value for moe_grooming_appointment.source_platform
     *
     * @mbg.generated
     */
    public void setSourcePlatform(String sourcePlatform) {
        this.sourcePlatform = sourcePlatform == null ? null : sourcePlatform.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.ready_time
     *
     * @return the value of moe_grooming_appointment.ready_time
     *
     * @mbg.generated
     */
    public Long getReadyTime() {
        return readyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.ready_time
     *
     * @param readyTime the value for moe_grooming_appointment.ready_time
     *
     * @mbg.generated
     */
    public void setReadyTime(Long readyTime) {
        this.readyTime = readyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.pickup_notification_send_status
     *
     * @return the value of moe_grooming_appointment.pickup_notification_send_status
     *
     * @mbg.generated
     */
    public Integer getPickupNotificationSendStatus() {
        return pickupNotificationSendStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.pickup_notification_send_status
     *
     * @param pickupNotificationSendStatus the value for moe_grooming_appointment.pickup_notification_send_status
     *
     * @mbg.generated
     */
    public void setPickupNotificationSendStatus(Integer pickupNotificationSendStatus) {
        this.pickupNotificationSendStatus = pickupNotificationSendStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.pickup_notification_failed_reason
     *
     * @return the value of moe_grooming_appointment.pickup_notification_failed_reason
     *
     * @mbg.generated
     */
    public String getPickupNotificationFailedReason() {
        return pickupNotificationFailedReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.pickup_notification_failed_reason
     *
     * @param pickupNotificationFailedReason the value for moe_grooming_appointment.pickup_notification_failed_reason
     *
     * @mbg.generated
     */
    public void setPickupNotificationFailedReason(String pickupNotificationFailedReason) {
        this.pickupNotificationFailedReason =
                pickupNotificationFailedReason == null ? null : pickupNotificationFailedReason.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.status_before_checkin
     *
     * @return the value of moe_grooming_appointment.status_before_checkin
     *
     * @mbg.generated
     */
    public AppointmentStatusEnum getStatusBeforeCheckin() {
        return statusBeforeCheckin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.status_before_checkin
     *
     * @param statusBeforeCheckin the value for moe_grooming_appointment.status_before_checkin
     *
     * @mbg.generated
     */
    public void setStatusBeforeCheckin(AppointmentStatusEnum statusBeforeCheckin) {
        this.statusBeforeCheckin = statusBeforeCheckin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.status_before_ready
     *
     * @return the value of moe_grooming_appointment.status_before_ready
     *
     * @mbg.generated
     */
    public AppointmentStatusEnum getStatusBeforeReady() {
        return statusBeforeReady;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.status_before_ready
     *
     * @param statusBeforeReady the value for moe_grooming_appointment.status_before_ready
     *
     * @mbg.generated
     */
    public void setStatusBeforeReady(AppointmentStatusEnum statusBeforeReady) {
        this.statusBeforeReady = statusBeforeReady;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.status_before_finish
     *
     * @return the value of moe_grooming_appointment.status_before_finish
     *
     * @mbg.generated
     */
    public AppointmentStatusEnum getStatusBeforeFinish() {
        return statusBeforeFinish;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.status_before_finish
     *
     * @param statusBeforeFinish the value for moe_grooming_appointment.status_before_finish
     *
     * @mbg.generated
     */
    public void setStatusBeforeFinish(AppointmentStatusEnum statusBeforeFinish) {
        this.statusBeforeFinish = statusBeforeFinish;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.no_start_time
     *
     * @return the value of moe_grooming_appointment.no_start_time
     *
     * @mbg.generated
     */
    public Boolean getNoStartTime() {
        return noStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.no_start_time
     *
     * @param noStartTime the value for moe_grooming_appointment.no_start_time
     *
     * @mbg.generated
     */
    public void setNoStartTime(Boolean noStartTime) {
        this.noStartTime = noStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.updated_by_id
     *
     * @return the value of moe_grooming_appointment.updated_by_id
     *
     * @mbg.generated
     */
    public Long getUpdatedById() {
        return updatedById;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.updated_by_id
     *
     * @param updatedById the value for moe_grooming_appointment.updated_by_id
     *
     * @mbg.generated
     */
    public void setUpdatedById(Long updatedById) {
        this.updatedById = updatedById;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.company_id
     *
     * @return the value of moe_grooming_appointment.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.company_id
     *
     * @param companyId the value for moe_grooming_appointment.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.is_auto_accept
     *
     * @return the value of moe_grooming_appointment.is_auto_accept
     *
     * @mbg.generated
     */
    public Boolean getIsAutoAccept() {
        return isAutoAccept;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.is_auto_accept
     *
     * @param isAutoAccept the value for moe_grooming_appointment.is_auto_accept
     *
     * @mbg.generated
     */
    public void setIsAutoAccept(Boolean isAutoAccept) {
        this.isAutoAccept = isAutoAccept;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.wait_list_status
     *
     * @return the value of moe_grooming_appointment.wait_list_status
     *
     * @mbg.generated
     */
    public WaitListStatusEnum getWaitListStatus() {
        return waitListStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.wait_list_status
     *
     * @param waitListStatus the value for moe_grooming_appointment.wait_list_status
     *
     * @mbg.generated
     */
    public void setWaitListStatus(WaitListStatusEnum waitListStatus) {
        this.waitListStatus = waitListStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.appointment_end_date
     *
     * @return the value of moe_grooming_appointment.appointment_end_date
     *
     * @mbg.generated
     */
    public String getAppointmentEndDate() {
        return appointmentEndDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.appointment_end_date
     *
     * @param appointmentEndDate the value for moe_grooming_appointment.appointment_end_date
     *
     * @mbg.generated
     */
    public void setAppointmentEndDate(String appointmentEndDate) {
        this.appointmentEndDate = appointmentEndDate == null ? null : appointmentEndDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.service_type_include
     *
     * @return the value of moe_grooming_appointment.service_type_include
     *
     * @mbg.generated
     */
    public Integer getServiceTypeInclude() {
        return serviceTypeInclude;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.service_type_include
     *
     * @param serviceTypeInclude the value for moe_grooming_appointment.service_type_include
     *
     * @mbg.generated
     */
    public void setServiceTypeInclude(Integer serviceTypeInclude) {
        this.serviceTypeInclude = serviceTypeInclude;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_appointment.no_show_by
     *
     * @return the value of moe_grooming_appointment.no_show_by
     *
     * @mbg.generated
     */
    public Long getNoShowBy() {
        return noShowBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_appointment.no_show_by
     *
     * @param noShowBy the value for moe_grooming_appointment.no_show_by
     *
     * @mbg.generated
     */
    public void setNoShowBy(Long noShowBy) {
        this.noShowBy = noShowBy;
    }
}
