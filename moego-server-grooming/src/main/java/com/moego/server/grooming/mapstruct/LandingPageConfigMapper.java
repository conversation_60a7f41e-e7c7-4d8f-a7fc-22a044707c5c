package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.LandingPageConfigDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface LandingPageConfigMapper {
    LandingPageConfigMapper INSTANCE = Mappers.getMapper(LandingPageConfigMapper.class);

    LandingPageConfigDTO entity2DTO(MoeBookOnlineLandingPageConfig entity);
}
