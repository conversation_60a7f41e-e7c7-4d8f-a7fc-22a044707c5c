package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AppointmentPetFeedingExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public AppointmentPetFeedingExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdIsNull() {
            addCriterion("appointment_id is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdIsNotNull() {
            addCriterion("appointment_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdEqualTo(Long value) {
            addCriterion("appointment_id =", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdNotEqualTo(Long value) {
            addCriterion("appointment_id <>", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdGreaterThan(Long value) {
            addCriterion("appointment_id >", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("appointment_id >=", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdLessThan(Long value) {
            addCriterion("appointment_id <", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdLessThanOrEqualTo(Long value) {
            addCriterion("appointment_id <=", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdIn(List<Long> values) {
            addCriterion("appointment_id in", values, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdNotIn(List<Long> values) {
            addCriterion("appointment_id not in", values, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdBetween(Long value1, Long value2) {
            addCriterion("appointment_id between", value1, value2, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdNotBetween(Long value1, Long value2) {
            addCriterion("appointment_id not between", value1, value2, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdIsNull() {
            addCriterion("pet_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdIsNotNull() {
            addCriterion("pet_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdEqualTo(Long value) {
            addCriterion("pet_detail_id =", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdNotEqualTo(Long value) {
            addCriterion("pet_detail_id <>", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdGreaterThan(Long value) {
            addCriterion("pet_detail_id >", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pet_detail_id >=", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdLessThan(Long value) {
            addCriterion("pet_detail_id <", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("pet_detail_id <=", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdIn(List<Long> values) {
            addCriterion("pet_detail_id in", values, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdNotIn(List<Long> values) {
            addCriterion("pet_detail_id not in", values, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdBetween(Long value1, Long value2) {
            addCriterion("pet_detail_id between", value1, value2, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("pet_detail_id not between", value1, value2, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNull() {
            addCriterion("pet_id is null");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNotNull() {
            addCriterion("pet_id is not null");
            return (Criteria) this;
        }

        public Criteria andPetIdEqualTo(Long value) {
            addCriterion("pet_id =", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotEqualTo(Long value) {
            addCriterion("pet_id <>", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThan(Long value) {
            addCriterion("pet_id >", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pet_id >=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThan(Long value) {
            addCriterion("pet_id <", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThanOrEqualTo(Long value) {
            addCriterion("pet_id <=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdIn(List<Long> values) {
            addCriterion("pet_id in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotIn(List<Long> values) {
            addCriterion("pet_id not in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdBetween(Long value1, Long value2) {
            addCriterion("pet_id between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotBetween(Long value1, Long value2) {
            addCriterion("pet_id not between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountIsNull() {
            addCriterion("feeding_amount is null");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountIsNotNull() {
            addCriterion("feeding_amount is not null");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountEqualTo(String value) {
            addCriterion("feeding_amount =", value, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountNotEqualTo(String value) {
            addCriterion("feeding_amount <>", value, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountGreaterThan(String value) {
            addCriterion("feeding_amount >", value, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountGreaterThanOrEqualTo(String value) {
            addCriterion("feeding_amount >=", value, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountLessThan(String value) {
            addCriterion("feeding_amount <", value, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountLessThanOrEqualTo(String value) {
            addCriterion("feeding_amount <=", value, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountLike(String value) {
            addCriterion("feeding_amount like", value, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountNotLike(String value) {
            addCriterion("feeding_amount not like", value, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountIn(List<String> values) {
            addCriterion("feeding_amount in", values, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountNotIn(List<String> values) {
            addCriterion("feeding_amount not in", values, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountBetween(String value1, String value2) {
            addCriterion("feeding_amount between", value1, value2, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingAmountNotBetween(String value1, String value2) {
            addCriterion("feeding_amount not between", value1, value2, "feedingAmount");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitIsNull() {
            addCriterion("feeding_unit is null");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitIsNotNull() {
            addCriterion("feeding_unit is not null");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitEqualTo(String value) {
            addCriterion("feeding_unit =", value, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitNotEqualTo(String value) {
            addCriterion("feeding_unit <>", value, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitGreaterThan(String value) {
            addCriterion("feeding_unit >", value, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitGreaterThanOrEqualTo(String value) {
            addCriterion("feeding_unit >=", value, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitLessThan(String value) {
            addCriterion("feeding_unit <", value, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitLessThanOrEqualTo(String value) {
            addCriterion("feeding_unit <=", value, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitLike(String value) {
            addCriterion("feeding_unit like", value, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitNotLike(String value) {
            addCriterion("feeding_unit not like", value, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitIn(List<String> values) {
            addCriterion("feeding_unit in", values, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitNotIn(List<String> values) {
            addCriterion("feeding_unit not in", values, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitBetween(String value1, String value2) {
            addCriterion("feeding_unit between", value1, value2, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingUnitNotBetween(String value1, String value2) {
            addCriterion("feeding_unit not between", value1, value2, "feedingUnit");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeIsNull() {
            addCriterion("feeding_type is null");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeIsNotNull() {
            addCriterion("feeding_type is not null");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeEqualTo(String value) {
            addCriterion("feeding_type =", value, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeNotEqualTo(String value) {
            addCriterion("feeding_type <>", value, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeGreaterThan(String value) {
            addCriterion("feeding_type >", value, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeGreaterThanOrEqualTo(String value) {
            addCriterion("feeding_type >=", value, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeLessThan(String value) {
            addCriterion("feeding_type <", value, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeLessThanOrEqualTo(String value) {
            addCriterion("feeding_type <=", value, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeLike(String value) {
            addCriterion("feeding_type like", value, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeNotLike(String value) {
            addCriterion("feeding_type not like", value, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeIn(List<String> values) {
            addCriterion("feeding_type in", values, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeNotIn(List<String> values) {
            addCriterion("feeding_type not in", values, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeBetween(String value1, String value2) {
            addCriterion("feeding_type between", value1, value2, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingTypeNotBetween(String value1, String value2) {
            addCriterion("feeding_type not between", value1, value2, "feedingType");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceIsNull() {
            addCriterion("feeding_source is null");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceIsNotNull() {
            addCriterion("feeding_source is not null");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceEqualTo(String value) {
            addCriterion("feeding_source =", value, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceNotEqualTo(String value) {
            addCriterion("feeding_source <>", value, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceGreaterThan(String value) {
            addCriterion("feeding_source >", value, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceGreaterThanOrEqualTo(String value) {
            addCriterion("feeding_source >=", value, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceLessThan(String value) {
            addCriterion("feeding_source <", value, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceLessThanOrEqualTo(String value) {
            addCriterion("feeding_source <=", value, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceLike(String value) {
            addCriterion("feeding_source like", value, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceNotLike(String value) {
            addCriterion("feeding_source not like", value, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceIn(List<String> values) {
            addCriterion("feeding_source in", values, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceNotIn(List<String> values) {
            addCriterion("feeding_source not in", values, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceBetween(String value1, String value2) {
            addCriterion("feeding_source between", value1, value2, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingSourceNotBetween(String value1, String value2) {
            addCriterion("feeding_source not between", value1, value2, "feedingSource");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionIsNull() {
            addCriterion("feeding_instruction is null");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionIsNotNull() {
            addCriterion("feeding_instruction is not null");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionEqualTo(String value) {
            addCriterion("feeding_instruction =", value, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionNotEqualTo(String value) {
            addCriterion("feeding_instruction <>", value, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionGreaterThan(String value) {
            addCriterion("feeding_instruction >", value, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionGreaterThanOrEqualTo(String value) {
            addCriterion("feeding_instruction >=", value, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionLessThan(String value) {
            addCriterion("feeding_instruction <", value, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionLessThanOrEqualTo(String value) {
            addCriterion("feeding_instruction <=", value, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionLike(String value) {
            addCriterion("feeding_instruction like", value, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionNotLike(String value) {
            addCriterion("feeding_instruction not like", value, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionIn(List<String> values) {
            addCriterion("feeding_instruction in", values, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionNotIn(List<String> values) {
            addCriterion("feeding_instruction not in", values, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionBetween(String value1, String value2) {
            addCriterion("feeding_instruction between", value1, value2, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andFeedingInstructionNotBetween(String value1, String value2) {
            addCriterion("feeding_instruction not between", value1, value2, "feedingInstruction");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtIsNull() {
            addCriterion("deleted_at is null");
            return (Criteria) this;
        }

        public Criteria andDeletedAtIsNotNull() {
            addCriterion("deleted_at is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedAtEqualTo(Date value) {
            addCriterion("deleted_at =", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtNotEqualTo(Date value) {
            addCriterion("deleted_at <>", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtGreaterThan(Date value) {
            addCriterion("deleted_at >", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("deleted_at >=", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtLessThan(Date value) {
            addCriterion("deleted_at <", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtLessThanOrEqualTo(Date value) {
            addCriterion("deleted_at <=", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtIn(List<Date> values) {
            addCriterion("deleted_at in", values, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtNotIn(List<Date> values) {
            addCriterion("deleted_at not in", values, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtBetween(Date value1, Date value2) {
            addCriterion("deleted_at between", value1, value2, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtNotBetween(Date value1, Date value2) {
            addCriterion("deleted_at not between", value1, value2, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteIsNull() {
            addCriterion("feeding_note is null");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteIsNotNull() {
            addCriterion("feeding_note is not null");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteEqualTo(String value) {
            addCriterion("feeding_note =", value, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteNotEqualTo(String value) {
            addCriterion("feeding_note <>", value, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteGreaterThan(String value) {
            addCriterion("feeding_note >", value, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteGreaterThanOrEqualTo(String value) {
            addCriterion("feeding_note >=", value, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteLessThan(String value) {
            addCriterion("feeding_note <", value, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteLessThanOrEqualTo(String value) {
            addCriterion("feeding_note <=", value, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteLike(String value) {
            addCriterion("feeding_note like", value, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteNotLike(String value) {
            addCriterion("feeding_note not like", value, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteIn(List<String> values) {
            addCriterion("feeding_note in", values, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteNotIn(List<String> values) {
            addCriterion("feeding_note not in", values, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteBetween(String value1, String value2) {
            addCriterion("feeding_note between", value1, value2, "feedingNote");
            return (Criteria) this;
        }

        public Criteria andFeedingNoteNotBetween(String value1, String value2) {
            addCriterion("feeding_note not between", value1, value2, "feedingNote");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
