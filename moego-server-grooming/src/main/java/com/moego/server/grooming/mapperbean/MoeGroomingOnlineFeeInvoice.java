package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_online_fee_invoice
 */
public class MoeGroomingOnlineFeeInvoice {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_online_fee_invoice.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_online_fee_invoice.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   invoice id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_online_fee_invoice.invoice_id
     *
     * @mbg.generated
     */
    private Integer invoiceId;

    /**
     * Database Column Remarks:
     *   online link type: 1-invoice, 2-deposit
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_online_fee_invoice.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     * Database Column Remarks:
     *   required convenience fee pay by client, 0-no, 1-yes
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_online_fee_invoice.required_fee
     *
     * @mbg.generated
     */
    private Byte requiredFee;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_online_fee_invoice.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_online_fee_invoice.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_online_fee_invoice.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_online_fee_invoice.id
     *
     * @return the value of moe_grooming_online_fee_invoice.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_online_fee_invoice.id
     *
     * @param id the value for moe_grooming_online_fee_invoice.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_online_fee_invoice.business_id
     *
     * @return the value of moe_grooming_online_fee_invoice.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_online_fee_invoice.business_id
     *
     * @param businessId the value for moe_grooming_online_fee_invoice.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_online_fee_invoice.invoice_id
     *
     * @return the value of moe_grooming_online_fee_invoice.invoice_id
     *
     * @mbg.generated
     */
    public Integer getInvoiceId() {
        return invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_online_fee_invoice.invoice_id
     *
     * @param invoiceId the value for moe_grooming_online_fee_invoice.invoice_id
     *
     * @mbg.generated
     */
    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_online_fee_invoice.type
     *
     * @return the value of moe_grooming_online_fee_invoice.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_online_fee_invoice.type
     *
     * @param type the value for moe_grooming_online_fee_invoice.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_online_fee_invoice.required_fee
     *
     * @return the value of moe_grooming_online_fee_invoice.required_fee
     *
     * @mbg.generated
     */
    public Byte getRequiredFee() {
        return requiredFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_online_fee_invoice.required_fee
     *
     * @param requiredFee the value for moe_grooming_online_fee_invoice.required_fee
     *
     * @mbg.generated
     */
    public void setRequiredFee(Byte requiredFee) {
        this.requiredFee = requiredFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_online_fee_invoice.create_time
     *
     * @return the value of moe_grooming_online_fee_invoice.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_online_fee_invoice.create_time
     *
     * @param createTime the value for moe_grooming_online_fee_invoice.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_online_fee_invoice.update_time
     *
     * @return the value of moe_grooming_online_fee_invoice.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_online_fee_invoice.update_time
     *
     * @param updateTime the value for moe_grooming_online_fee_invoice.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_online_fee_invoice.company_id
     *
     * @return the value of moe_grooming_online_fee_invoice.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_online_fee_invoice.company_id
     *
     * @param companyId the value for moe_grooming_online_fee_invoice.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
