<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingRepeatMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingRepeat">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="repeat_type" jdbcType="TINYINT" property="repeatType" />
    <result column="repeat_every" jdbcType="INTEGER" property="repeatEvery" />
    <result column="repeat_by" jdbcType="VARCHAR" property="repeatBy" />
    <result column="starts_on" jdbcType="DATE" property="startsOn" />
    <result column="times" jdbcType="INTEGER" property="times" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="end_on" jdbcType="CHAR" property="endOn" />
    <result column="is_notice" jdbcType="TINYINT" property="isNotice" />
    <result column="set_end_on" jdbcType="DATE" property="setEndOn" />
    <result column="repeat_every_type" jdbcType="TINYINT" property="repeatEveryType" />
    <result column="month_day" jdbcType="TINYINT" property="monthDay" />
    <result column="month_week_times" jdbcType="TINYINT" property="monthWeekTimes" />
    <result column="month_week_day" jdbcType="TINYINT" property="monthWeekDay" />
    <result column="type" jdbcType="CHAR" property="type" />
    <result column="ss_flag" jdbcType="TINYINT" property="ssFlag" />
    <result column="ss_before_days" jdbcType="INTEGER" property="ssBeforeDays" />
    <result column="ss_after_days" jdbcType="INTEGER" property="ssAfterDays" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="repeat_by_days" jdbcType="CHAR" property="repeatByDays" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, customer_id, business_id, staff_id, repeat_type, repeat_every, repeat_by, starts_on,
    times, create_time, update_time, status, end_on, is_notice, set_end_on, repeat_every_type,
    month_day, month_week_times, month_week_day, type, ss_flag, ss_before_days, ss_after_days,
    company_id, repeat_by_days
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingRepeatExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_repeat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_repeat
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_repeat
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingRepeatExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_repeat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingRepeat">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_repeat (customer_id, business_id, staff_id,
      repeat_type, repeat_every, repeat_by,
      starts_on, times, create_time,
      update_time, status, end_on,
      is_notice, set_end_on, repeat_every_type,
      month_day, month_week_times, month_week_day,
      type, ss_flag, ss_before_days,
      ss_after_days, company_id, repeat_by_days
      )
    values (#{customerId,jdbcType=INTEGER}, #{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER},
      #{repeatType,jdbcType=TINYINT}, #{repeatEvery,jdbcType=INTEGER}, #{repeatBy,jdbcType=VARCHAR},
      #{startsOn,jdbcType=DATE}, #{times,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{endOn,jdbcType=CHAR},
      #{isNotice,jdbcType=TINYINT}, #{setEndOn,jdbcType=DATE}, #{repeatEveryType,jdbcType=TINYINT},
      #{monthDay,jdbcType=TINYINT}, #{monthWeekTimes,jdbcType=TINYINT}, #{monthWeekDay,jdbcType=TINYINT},
      #{type,jdbcType=CHAR}, #{ssFlag,jdbcType=TINYINT}, #{ssBeforeDays,jdbcType=INTEGER},
      #{ssAfterDays,jdbcType=INTEGER}, #{companyId,jdbcType=BIGINT}, #{repeatByDays,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingRepeat">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_repeat
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="repeatType != null">
        repeat_type,
      </if>
      <if test="repeatEvery != null">
        repeat_every,
      </if>
      <if test="repeatBy != null">
        repeat_by,
      </if>
      <if test="startsOn != null">
        starts_on,
      </if>
      <if test="times != null">
        times,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="endOn != null">
        end_on,
      </if>
      <if test="isNotice != null">
        is_notice,
      </if>
      <if test="setEndOn != null">
        set_end_on,
      </if>
      <if test="repeatEveryType != null">
        repeat_every_type,
      </if>
      <if test="monthDay != null">
        month_day,
      </if>
      <if test="monthWeekTimes != null">
        month_week_times,
      </if>
      <if test="monthWeekDay != null">
        month_week_day,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="ssFlag != null">
        ss_flag,
      </if>
      <if test="ssBeforeDays != null">
        ss_before_days,
      </if>
      <if test="ssAfterDays != null">
        ss_after_days,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="repeatByDays != null">
        repeat_by_days,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="repeatType != null">
        #{repeatType,jdbcType=TINYINT},
      </if>
      <if test="repeatEvery != null">
        #{repeatEvery,jdbcType=INTEGER},
      </if>
      <if test="repeatBy != null">
        #{repeatBy,jdbcType=VARCHAR},
      </if>
      <if test="startsOn != null">
        #{startsOn,jdbcType=DATE},
      </if>
      <if test="times != null">
        #{times,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="endOn != null">
        #{endOn,jdbcType=CHAR},
      </if>
      <if test="isNotice != null">
        #{isNotice,jdbcType=TINYINT},
      </if>
      <if test="setEndOn != null">
        #{setEndOn,jdbcType=DATE},
      </if>
      <if test="repeatEveryType != null">
        #{repeatEveryType,jdbcType=TINYINT},
      </if>
      <if test="monthDay != null">
        #{monthDay,jdbcType=TINYINT},
      </if>
      <if test="monthWeekTimes != null">
        #{monthWeekTimes,jdbcType=TINYINT},
      </if>
      <if test="monthWeekDay != null">
        #{monthWeekDay,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=CHAR},
      </if>
      <if test="ssFlag != null">
        #{ssFlag,jdbcType=TINYINT},
      </if>
      <if test="ssBeforeDays != null">
        #{ssBeforeDays,jdbcType=INTEGER},
      </if>
      <if test="ssAfterDays != null">
        #{ssAfterDays,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="repeatByDays != null">
        #{repeatByDays,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingRepeatExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_repeat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_repeat
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.staffId != null">
        staff_id = #{record.staffId,jdbcType=INTEGER},
      </if>
      <if test="record.repeatType != null">
        repeat_type = #{record.repeatType,jdbcType=TINYINT},
      </if>
      <if test="record.repeatEvery != null">
        repeat_every = #{record.repeatEvery,jdbcType=INTEGER},
      </if>
      <if test="record.repeatBy != null">
        repeat_by = #{record.repeatBy,jdbcType=VARCHAR},
      </if>
      <if test="record.startsOn != null">
        starts_on = #{record.startsOn,jdbcType=DATE},
      </if>
      <if test="record.times != null">
        times = #{record.times,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.endOn != null">
        end_on = #{record.endOn,jdbcType=CHAR},
      </if>
      <if test="record.isNotice != null">
        is_notice = #{record.isNotice,jdbcType=TINYINT},
      </if>
      <if test="record.setEndOn != null">
        set_end_on = #{record.setEndOn,jdbcType=DATE},
      </if>
      <if test="record.repeatEveryType != null">
        repeat_every_type = #{record.repeatEveryType,jdbcType=TINYINT},
      </if>
      <if test="record.monthDay != null">
        month_day = #{record.monthDay,jdbcType=TINYINT},
      </if>
      <if test="record.monthWeekTimes != null">
        month_week_times = #{record.monthWeekTimes,jdbcType=TINYINT},
      </if>
      <if test="record.monthWeekDay != null">
        month_week_day = #{record.monthWeekDay,jdbcType=TINYINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=CHAR},
      </if>
      <if test="record.ssFlag != null">
        ss_flag = #{record.ssFlag,jdbcType=TINYINT},
      </if>
      <if test="record.ssBeforeDays != null">
        ss_before_days = #{record.ssBeforeDays,jdbcType=INTEGER},
      </if>
      <if test="record.ssAfterDays != null">
        ss_after_days = #{record.ssAfterDays,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.repeatByDays != null">
        repeat_by_days = #{record.repeatByDays,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_repeat
    set id = #{record.id,jdbcType=INTEGER},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      staff_id = #{record.staffId,jdbcType=INTEGER},
      repeat_type = #{record.repeatType,jdbcType=TINYINT},
      repeat_every = #{record.repeatEvery,jdbcType=INTEGER},
      repeat_by = #{record.repeatBy,jdbcType=VARCHAR},
      starts_on = #{record.startsOn,jdbcType=DATE},
      times = #{record.times,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      end_on = #{record.endOn,jdbcType=CHAR},
      is_notice = #{record.isNotice,jdbcType=TINYINT},
      set_end_on = #{record.setEndOn,jdbcType=DATE},
      repeat_every_type = #{record.repeatEveryType,jdbcType=TINYINT},
      month_day = #{record.monthDay,jdbcType=TINYINT},
      month_week_times = #{record.monthWeekTimes,jdbcType=TINYINT},
      month_week_day = #{record.monthWeekDay,jdbcType=TINYINT},
      type = #{record.type,jdbcType=CHAR},
      ss_flag = #{record.ssFlag,jdbcType=TINYINT},
      ss_before_days = #{record.ssBeforeDays,jdbcType=INTEGER},
      ss_after_days = #{record.ssAfterDays,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=BIGINT},
      repeat_by_days = #{record.repeatByDays,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingRepeat">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_repeat
    <set>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="repeatType != null">
        repeat_type = #{repeatType,jdbcType=TINYINT},
      </if>
      <if test="repeatEvery != null">
        repeat_every = #{repeatEvery,jdbcType=INTEGER},
      </if>
      <if test="repeatBy != null">
        repeat_by = #{repeatBy,jdbcType=VARCHAR},
      </if>
      <if test="startsOn != null">
        starts_on = #{startsOn,jdbcType=DATE},
      </if>
      <if test="times != null">
        times = #{times,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="endOn != null">
        end_on = #{endOn,jdbcType=CHAR},
      </if>
      <if test="isNotice != null">
        is_notice = #{isNotice,jdbcType=TINYINT},
      </if>
      <if test="setEndOn != null">
        set_end_on = #{setEndOn,jdbcType=DATE},
      </if>
      <if test="repeatEveryType != null">
        repeat_every_type = #{repeatEveryType,jdbcType=TINYINT},
      </if>
      <if test="monthDay != null">
        month_day = #{monthDay,jdbcType=TINYINT},
      </if>
      <if test="monthWeekTimes != null">
        month_week_times = #{monthWeekTimes,jdbcType=TINYINT},
      </if>
      <if test="monthWeekDay != null">
        month_week_day = #{monthWeekDay,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=CHAR},
      </if>
      <if test="ssFlag != null">
        ss_flag = #{ssFlag,jdbcType=TINYINT},
      </if>
      <if test="ssBeforeDays != null">
        ss_before_days = #{ssBeforeDays,jdbcType=INTEGER},
      </if>
      <if test="ssAfterDays != null">
        ss_after_days = #{ssAfterDays,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="repeatByDays != null">
        repeat_by_days = #{repeatByDays,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingRepeat">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_repeat
    set customer_id = #{customerId,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      repeat_type = #{repeatType,jdbcType=TINYINT},
      repeat_every = #{repeatEvery,jdbcType=INTEGER},
      repeat_by = #{repeatBy,jdbcType=VARCHAR},
      starts_on = #{startsOn,jdbcType=DATE},
      times = #{times,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      end_on = #{endOn,jdbcType=CHAR},
      is_notice = #{isNotice,jdbcType=TINYINT},
      set_end_on = #{setEndOn,jdbcType=DATE},
      repeat_every_type = #{repeatEveryType,jdbcType=TINYINT},
      month_day = #{monthDay,jdbcType=TINYINT},
      month_week_times = #{monthWeekTimes,jdbcType=TINYINT},
      month_week_day = #{monthWeekDay,jdbcType=TINYINT},
      type = #{type,jdbcType=CHAR},
      ss_flag = #{ssFlag,jdbcType=TINYINT},
      ss_before_days = #{ssBeforeDays,jdbcType=INTEGER},
      ss_after_days = #{ssAfterDays,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=BIGINT},
      repeat_by_days = #{repeatByDays,jdbcType=CHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="queryByBusinessId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_repeat
    where business_id = #{businessId,jdbcType=INTEGER}
  </select>
</mapper>
