<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineProfileMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineProfile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="business_email" jdbcType="VARCHAR" property="businessEmail" />
    <result column="avatar_path" jdbcType="VARCHAR" property="avatarPath" />
    <result column="facebook" jdbcType="VARCHAR" property="facebook" />
    <result column="instagram" jdbcType="VARCHAR" property="instagram" />
    <result column="google" jdbcType="VARCHAR" property="google" />
    <result column="yelp" jdbcType="VARCHAR" property="yelp" />
    <result column="other" jdbcType="VARCHAR" property="other" />
    <result column="language" jdbcType="VARCHAR" property="language" />
    <result column="button_color" jdbcType="VARCHAR" property="buttonColor" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeBookOnlineProfile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="business_hours_json" jdbcType="LONGVARCHAR" property="businessHoursJson" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, business_name, phone_number, website, address, business_email, avatar_path, 
    facebook, instagram, google, yelp, other, language, button_color, create_time, update_time, 
    company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    description, business_hours_json
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_profile
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_profile
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineProfile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_profile (business_id, business_name, phone_number, 
      website, address, business_email, 
      avatar_path, facebook, instagram, 
      google, yelp, other, 
      language, button_color, create_time, 
      update_time, company_id, description, 
      business_hours_json)
    values (#{businessId,jdbcType=INTEGER}, #{businessName,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, 
      #{website,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{businessEmail,jdbcType=VARCHAR}, 
      #{avatarPath,jdbcType=VARCHAR}, #{facebook,jdbcType=VARCHAR}, #{instagram,jdbcType=VARCHAR}, 
      #{google,jdbcType=VARCHAR}, #{yelp,jdbcType=VARCHAR}, #{other,jdbcType=VARCHAR}, 
      #{language,jdbcType=VARCHAR}, #{buttonColor,jdbcType=VARCHAR}, #{createTime,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=INTEGER}, #{companyId,jdbcType=BIGINT}, #{description,jdbcType=LONGVARCHAR}, 
      #{businessHoursJson,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineProfile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_profile
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="website != null">
        website,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="businessEmail != null">
        business_email,
      </if>
      <if test="avatarPath != null">
        avatar_path,
      </if>
      <if test="facebook != null">
        facebook,
      </if>
      <if test="instagram != null">
        instagram,
      </if>
      <if test="google != null">
        google,
      </if>
      <if test="yelp != null">
        yelp,
      </if>
      <if test="other != null">
        other,
      </if>
      <if test="language != null">
        language,
      </if>
      <if test="buttonColor != null">
        button_color,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="businessHoursJson != null">
        business_hours_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="website != null">
        #{website,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="businessEmail != null">
        #{businessEmail,jdbcType=VARCHAR},
      </if>
      <if test="avatarPath != null">
        #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="facebook != null">
        #{facebook,jdbcType=VARCHAR},
      </if>
      <if test="instagram != null">
        #{instagram,jdbcType=VARCHAR},
      </if>
      <if test="google != null">
        #{google,jdbcType=VARCHAR},
      </if>
      <if test="yelp != null">
        #{yelp,jdbcType=VARCHAR},
      </if>
      <if test="other != null">
        #{other,jdbcType=VARCHAR},
      </if>
      <if test="language != null">
        #{language,jdbcType=VARCHAR},
      </if>
      <if test="buttonColor != null">
        #{buttonColor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="businessHoursJson != null">
        #{businessHoursJson,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineProfile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_profile
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="website != null">
        website = #{website,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="businessEmail != null">
        business_email = #{businessEmail,jdbcType=VARCHAR},
      </if>
      <if test="avatarPath != null">
        avatar_path = #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="facebook != null">
        facebook = #{facebook,jdbcType=VARCHAR},
      </if>
      <if test="instagram != null">
        instagram = #{instagram,jdbcType=VARCHAR},
      </if>
      <if test="google != null">
        google = #{google,jdbcType=VARCHAR},
      </if>
      <if test="yelp != null">
        yelp = #{yelp,jdbcType=VARCHAR},
      </if>
      <if test="other != null">
        other = #{other,jdbcType=VARCHAR},
      </if>
      <if test="language != null">
        language = #{language,jdbcType=VARCHAR},
      </if>
      <if test="buttonColor != null">
        button_color = #{buttonColor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="businessHoursJson != null">
        business_hours_json = #{businessHoursJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineProfile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_profile
    set business_id = #{businessId,jdbcType=INTEGER},
      business_name = #{businessName,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      website = #{website,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      business_email = #{businessEmail,jdbcType=VARCHAR},
      avatar_path = #{avatarPath,jdbcType=VARCHAR},
      facebook = #{facebook,jdbcType=VARCHAR},
      instagram = #{instagram,jdbcType=VARCHAR},
      google = #{google,jdbcType=VARCHAR},
      yelp = #{yelp,jdbcType=VARCHAR},
      other = #{other,jdbcType=VARCHAR},
      language = #{language,jdbcType=VARCHAR},
      button_color = #{buttonColor,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=BIGINT},
      description = #{description,jdbcType=LONGVARCHAR},
      business_hours_json = #{businessHoursJson,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineProfile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_profile
    set business_id = #{businessId,jdbcType=INTEGER},
      business_name = #{businessName,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      website = #{website,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      business_email = #{businessEmail,jdbcType=VARCHAR},
      avatar_path = #{avatarPath,jdbcType=VARCHAR},
      facebook = #{facebook,jdbcType=VARCHAR},
      instagram = #{instagram,jdbcType=VARCHAR},
      google = #{google,jdbcType=VARCHAR},
      yelp = #{yelp,jdbcType=VARCHAR},
      other = #{other,jdbcType=VARCHAR},
      language = #{language,jdbcType=VARCHAR},
      button_color = #{buttonColor,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectByBusinessId" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from moe_book_online_profile
        where business_id = #{businessId,jdbcType=INTEGER}
    </select>
  <select id="getBusinessProfileList" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_profile
    where business_id in
    <foreach close=")" collection="businessIdList" item="businessId" open="(" separator=",">
      #{businessId,jdbcType=INTEGER}
    </foreach>
  </select>
    <update id="updateProfileByPrimaryIdOrBusinessId">
        update moe_book_online_profile
        <set>
            <if test="businessName != null">
                business_name = #{businessName,jdbcType=VARCHAR},
            </if>
            <if test="phoneNumber != null">
                phone_number = #{phoneNumber,jdbcType=VARCHAR},
            </if>
            <if test="website != null">
                website = #{website,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="businessEmail != null">
                business_email = #{businessEmail,jdbcType=VARCHAR},
            </if>
            <if test="avatarPath != null">
                avatar_path = #{avatarPath,jdbcType=VARCHAR},
            </if>
            <if test="facebook != null">
                facebook = #{facebook,jdbcType=VARCHAR},
            </if>
            <if test="instagram != null">
                instagram = #{instagram,jdbcType=VARCHAR},
            </if>
            <if test="google != null">
                google = #{google,jdbcType=VARCHAR},
            </if>
            <if test="yelp != null">
                yelp = #{yelp,jdbcType=VARCHAR},
            </if>
            <if test="other != null">
                other = #{other,jdbcType=VARCHAR},
            </if>
            <if test="language != null">
                language = #{language,jdbcType=VARCHAR},
            </if>
            <if test="buttonColor != null">
                button_color = #{buttonColor,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="businessHoursJson != null">
                business_hours_json =  #{businessHoursJson,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where 1=1
        <if test="id != null and id != 0">
            and id = #{id,jdbcType=INTEGER}
        </if>
        <if test="businessId != null and businessId != 0">
            and business_id = #{businessId,jdbcType=INTEGER}
        </if>
    </update>
</mapper>