package com.moego.server.grooming.service.client;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@Component
public class ClientApptServiceContext implements InitializingBean {

    @Autowired
    private Map<String, IBaseClientApptService> clientApptServiceMap;

    @Override
    public void afterPropertiesSet() {
        this.clientApptServiceMap = this.clientApptServiceMap.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> String.valueOf(entry.getValue().getApptType()), Map.Entry::getValue));
    }

    public IBaseClientApptService getApptService(Byte apptType) {
        IBaseClientApptService clientApptService = clientApptServiceMap.get(String.valueOf(apptType));
        if (Objects.isNull(clientApptService)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }
        return clientApptService;
    }
}
