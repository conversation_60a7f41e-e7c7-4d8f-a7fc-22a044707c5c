package com.moego.server.grooming.mapstruct;

import com.moego.common.enums.QuestionConst;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionSave;
import com.moego.server.grooming.web.vo.ob.OBRequestDetailVO;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/6/30
 */
@Mapper
public interface QuestionMapper {
    QuestionMapper INSTANCE = Mappers.getMapper(QuestionMapper.class);

    default Map<String, Object> mergeCustomQuestion(
            Map<String, Object> oldCustomQuestionMap,
            Map<String, Object> newCustomQuestionMap,
            Boolean autoAcceptConflict) {
        if (CollectionUtils.isEmpty(newCustomQuestionMap)) {
            return oldCustomQuestionMap;
        }
        if (CollectionUtils.isEmpty(oldCustomQuestionMap)) {
            return newCustomQuestionMap;
        }
        Map<String, Object> result = new HashMap<>();
        oldCustomQuestionMap.forEach((key, oldAnswer) -> {
            Object newAnswer = newCustomQuestionMap.get(key);
            // 原本有字段没值，新增的值直接合入
            if (Objects.isNull(oldAnswer) || !StringUtils.hasText(oldAnswer.toString())) {
                result.put(key, newAnswer);
            } else if (autoAcceptConflict && Objects.nonNull(newAnswer)) {
                // 自动接受冲突字段，新值覆盖旧值
                result.put(key, newAnswer);
            } else {
                // 其他情况：冲突但不自动接受；原本有值，新增的没值，旧值合入
                result.put(key, oldAnswer);
            }
        });
        // 新增的字段直接合入
        newCustomQuestionMap.forEach((key, newAnswer) -> {
            if (!oldCustomQuestionMap.containsKey(key)) {
                result.put(key, newAnswer);
            }
        });
        return result;
    }

    default Map<String, Object> filterNoteQuestion(
            Map<String, Object> oldCustomQuestionMap,
            Map<String, Object> newCustomQuestionMap,
            Boolean autoAcceptConflict) {
        if (CollectionUtils.isEmpty(oldCustomQuestionMap)) {
            return newCustomQuestionMap;
        }
        if (CollectionUtils.isEmpty(newCustomQuestionMap)) {
            return Map.of();
        }
        Map<String, Object> result = new HashMap<>();
        newCustomQuestionMap.forEach((key, newAnswer) -> {
            Object oldAnswer = oldCustomQuestionMap.get(key);
            if (Objects.isNull(oldAnswer) || !StringUtils.hasText(oldAnswer.toString())) {
                result.put(key, newAnswer);
            } else if (autoAcceptConflict && Objects.nonNull(newAnswer) && !Objects.equals(oldAnswer, newAnswer)) {
                result.put(key, newAnswer);
            }
        });
        return result;
    }

    default List<OBRequestDetailVO.QuestionAnswerVO> entity2QuestionAnswerVO(
            Map<String, Object> customQuestions, Map<String, MoeBookOnlineQuestion> questionMap) {
        if (CollectionUtils.isEmpty(customQuestions)) {
            return List.of();
        }
        return customQuestions.entrySet().stream()
                .map(entry -> {
                    MoeBookOnlineQuestion question = questionMap.get(entry.getKey());
                    if (Objects.isNull(question)) {
                        return null;
                    }
                    String answer = "";
                    if (Objects.nonNull(entry.getValue())) {
                        answer = entry.getValue().toString();
                    }
                    return OBRequestDetailVO.QuestionAnswerVO.builder()
                            .key(entry.getKey())
                            .question(question.getQuestion())
                            .answer(answer)
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();
    }

    default BookOnlineQuestionSaveDTO buildCustomerQuestionSaveDTO(
            Integer businessId, Integer customerId, List<MoeBookOnlineQuestionSave> saveList) {
        Map<Integer, Map<String, Object>> petCustomQuestionMap = new HashMap<>();
        BookOnlineQuestionSaveDTO questionSaveDTO = new BookOnlineQuestionSaveDTO()
                .setBusinessId(businessId)
                .setCustomerId(customerId)
                .setClientCustomQuestionMap(Map.of())
                .setPetCustomQuestionMap(petCustomQuestionMap);
        saveList.stream()
                .sorted(Comparator.comparing(MoeBookOnlineQuestionSave::getCreateTime))
                .forEach(questionSave -> {
                    if (!StringUtils.hasText(questionSave.getQuestionJson())) {
                        return;
                    }
                    Map<String, Object> customQuestionMap =
                            JsonUtil.toBean(questionSave.getQuestionJson(), new TypeRef<>() {});
                    if (Objects.equals(questionSave.getType(), QuestionConst.TYPE_PET_OWNER_QUESTION)) {
                        questionSaveDTO.setClientCustomQuestionMap(customQuestionMap);
                    } else if (Objects.equals(questionSave.getType(), QuestionConst.TYPE_PET_QUESTION)) {
                        petCustomQuestionMap.put(questionSave.getPetId(), customQuestionMap);
                    }
                });
        return questionSaveDTO;
    }

    default Map<String, Object> replaceRequestAnswer(
            Map<String, Object> customQuestionMap, Map<String, Object> requestQuestionMap) {
        if (CollectionUtils.isEmpty(requestQuestionMap)) {
            return customQuestionMap;
        }
        // replace request answer
        return customQuestionMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            Object answer = requestQuestionMap.get(entry.getKey());
            if (Objects.nonNull(answer) && StringUtils.hasText(answer.toString())) {
                return answer;
            }
            return entry.getValue();
        }));
    }
}
