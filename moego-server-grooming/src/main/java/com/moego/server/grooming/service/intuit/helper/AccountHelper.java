package com.moego.server.grooming.service.intuit.helper;

import com.intuit.ipp.data.Account;
import com.intuit.ipp.data.AccountSubTypeEnum;
import com.intuit.ipp.data.AccountTypeEnum;
import com.intuit.ipp.data.ReferenceType;
import com.moego.server.grooming.enums.IntuitAccountTypeEnum;
import java.util.Objects;

public class AccountHelper {

    public static Account createAccount(String name, AccountTypeEnum accType) {
        return createAccountWithParent(name, accType, null);
    }

    public static Account createAccountWithParent(String name, AccountTypeEnum accType, ReferenceType parent) {
        Account account = new Account();
        account.setName(name);
        account.setAccountType(accType);
        if (!Objects.isNull(parent)) {
            account.setParentRef(parent);
        }
        var subAcct = getSubAccountType(accType);
        if (Objects.nonNull(subAcct)) {
            account.setAccountSubType(subAcct.value());
        }
        return account;
    }

    private static AccountSubTypeEnum getSubAccountType(AccountTypeEnum acctType) {
        return switch (acctType) {
            case EXPENSE -> AccountSubTypeEnum.BANK_CHARGES;
            case OTHER_CURRENT_ASSET -> AccountSubTypeEnum.OTHER_CURRENT_ASSETS;
            default -> null;
        };
    }

    /**
     * 传入MoeGo定义的QB相关帐号类型, 转换QB SDK 的帐号类型
     *
     * @param accType {@see com.moego.server.grooming.enums.IntuitAccountTypeEnum}
     * @return AccountTypeEnum QB SDK中定义的帐号类型
     */
    public static AccountTypeEnum convertAccTypeEnum(IntuitAccountTypeEnum accType) {
        return switch (accType) {
            case ACCOUNT_TYPE_MOEGO_BANK -> AccountTypeEnum.BANK;
            case ACCOUNT_TYPE_MOEGO_INVOICE,
                    ACCOUNT_TYPE_MOEGO_INVOICE_INCOME,
                    ACCOUNT_TYPE_MOEGO_INVOICE_SALES_REVENUE,
                    ACCOUNT_TYPE_MOEGO_INVOICE_TIPS,
                    ACCOUNT_TYPE_MOEGO_INCOME_FEE_BY_CLIENT_REVENUE,
                    ACCOUNT_TYPE_MOEGO_RECEIVED_PAYMENT,
                    ACCOUNT_TYPE_MOEGO_RECEIVED_GROSS_SALE,
                    ACCOUNT_TYPE_MOEGO_RECEIVED_NET_SALE,
                    ACCOUNT_TYPE_MOEGO_RECEIVED_TIPS,
                    // sales tax payable 是的情况
                    // 因为QB的限制, tax item不能同时出现在income 帐号和 current liability帐号中
                    // 所以先将起定义为income类型, 同步tax item后将帐号类型修改成current liability
                    // 修改时机在 QuickBooksSyncService line:1046
                    ACCOUNT_TYPE_MOEGO_SALES_TAX_PAYABLE,
                    ACCOUNT_TYPE_MOEGO_RECEIVED_TAX_PAYABLE,
                    ACCOUNT_TYPE_MOEGO_REFUND_SERVICE_INCOME,
                    ACCOUNT_TYPE_MOEGO_REFUND_FROM_CREDIT_MEMO,
                    ACCOUNT_TYPE_MOEGO_REFUND_FROM_MOEGO_PAYMENT -> AccountTypeEnum.INCOME;
            case ACCOUNT_TYPE_MOEGO_OTHER_CURRENT_ASSET,
                    ACCOUNT_TYPE_MOEGO_PAY_SALES_RECEIPT,
                    ACCOUNT_TYPE_MOEGO_PAYMENTS -> AccountTypeEnum.OTHER_CURRENT_ASSET;
            case ACCOUNT_TYPE_MOEGO_EXPENSE -> AccountTypeEnum.EXPENSE;
            case ACCOUNT_TYPE_MOEGO_OTHER_CURRENT_LIABILITY -> AccountTypeEnum.OTHER_CURRENT_LIABILITY;
            case ACCOUNT_TYPE_MOEGO_ACCOUNT_RECEIVABLE -> AccountTypeEnum.ACCOUNTS_RECEIVABLE;
        };
    }

    public static Account getAccountWithId(String qbAccountId) {
        Account account = new Account();
        account.setId(qbAccountId);
        return account;
    }

    public static ReferenceType getAccountRef(Account account) {
        if (account == null) {
            return null;
        }
        ReferenceType accountRef = new ReferenceType();
        accountRef.setName(account.getName());
        accountRef.setValue(account.getId());
        return accountRef;
    }
}
