package com.moego.server.grooming.service.intuit.qbo;

import com.intuit.ipp.util.Config;
import com.intuit.oauth2.client.OAuth2PlatformClient;
import com.intuit.oauth2.config.Environment;
import com.intuit.oauth2.config.OAuth2Config;
import com.moego.common.enums.QuickBooksConst;
import com.moego.lib.common.util.HostUtil;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Component
public class OAuth2PlatformClientFactory {

    public static final String STATE_DELIMITER = ",";

    @Autowired
    org.springframework.core.env.Environment env;

    OAuth2PlatformClient client;

    OAuth2Config oauth2Config;

    @Value("${quickbooks.oAuth.redirectUri}")
    private String redirectUri;

    @Value("${quickbooks.client.id}")
    private String clientId;

    @Value("${quickbooks.client.secret}")
    private String clientSecret;

    @Value("${quickbooks.environment}")
    private String environment;

    @Value("${quickbooks.web.invoice.url}")
    private String qbInvoiceUrl;

    @PostConstruct
    public void init() {
        // initialize the config -> set client id, secret
        // SANDBOX或PRODUCTION
        if (!StringUtils.isEmpty(environment) && environment.equals(Environment.SANDBOX.value())) {
            // 沙盒环境
            oauth2Config = new OAuth2Config.OAuth2ConfigBuilder(clientId, clientSecret)
                    .callDiscoveryAPI(Environment.SANDBOX)
                    .buildConfig();
        } else {
            // 正式环境
            oauth2Config = new OAuth2Config.OAuth2ConfigBuilder(clientId, clientSecret)
                    .callDiscoveryAPI(Environment.PRODUCTION)
                    .buildConfig();
        }
        // build the client
        client = new OAuth2PlatformClient(oauth2Config);
    }

    public void updateConfigUrl() {
        String url = getPropertyValue("quickbooks.api.host") + "/v3/company";
        Config.setProperty(Config.BASE_URL_QBO, url);
    }

    public String getInvoiceUrl(String tnxId) {
        return qbInvoiceUrl + tnxId;
    }

    public OAuth2PlatformClient getOAuth2PlatformClient() {
        return client;
    }

    public OAuth2Config getOAuth2Config() {
        return oauth2Config;
    }

    public String getRedirectUri(Integer userVersion) {
        if (QuickBooksConst.USER_VERSION_NEW.equals(userVersion)) {
            return HostUtil.getBusinessHost() + QuickBooksConst.QB_REDIRECT_URL;
        } else {
            return HostUtil.getBusinessHost() + QuickBooksConst.QB_REDIRECT_LEGACY_URL;
        }
    }

    public String getPropertyValue(String proppertyName) {
        return env.getProperty(proppertyName);
    }
}
