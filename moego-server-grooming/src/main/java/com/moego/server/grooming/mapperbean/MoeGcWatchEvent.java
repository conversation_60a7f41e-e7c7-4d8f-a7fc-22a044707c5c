package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_gc_watch_event
 */
public class MoeGcWatchEvent {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.gc_calendar_id
     *
     * @mbg.generated
     */
    private Integer gcCalendarId;

    /**
     * Database Column Remarks:
     *   resource_id等于gc_calendar_id内的google_calendar_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.resource_id
     *
     * @mbg.generated
     */
    private String resourceId;

    /**
     * Database Column Remarks:
     *   watch 的 channel uuid
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.channel_uuid
     *
     * @mbg.generated
     */
    private String channelUuid;

    /**
     * Database Column Remarks:
     *   watch 过期时间戳 需要定时任务刷新
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.token_expired_time
     *
     * @mbg.generated
     */
    private Long tokenExpiredTime;

    /**
     * Database Column Remarks:
     *   最后通知时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.last_notify_time
     *
     * @mbg.generated
     */
    private Long lastNotifyTime;

    /**
     * Database Column Remarks:
     *   最后同步时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.last_sync_time
     *
     * @mbg.generated
     */
    private Long lastSyncTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   最后更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_watch_event.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.id
     *
     * @return the value of moe_gc_watch_event.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.id
     *
     * @param id the value for moe_gc_watch_event.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.business_id
     *
     * @return the value of moe_gc_watch_event.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.business_id
     *
     * @param businessId the value for moe_gc_watch_event.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.gc_calendar_id
     *
     * @return the value of moe_gc_watch_event.gc_calendar_id
     *
     * @mbg.generated
     */
    public Integer getGcCalendarId() {
        return gcCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.gc_calendar_id
     *
     * @param gcCalendarId the value for moe_gc_watch_event.gc_calendar_id
     *
     * @mbg.generated
     */
    public void setGcCalendarId(Integer gcCalendarId) {
        this.gcCalendarId = gcCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.resource_id
     *
     * @return the value of moe_gc_watch_event.resource_id
     *
     * @mbg.generated
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.resource_id
     *
     * @param resourceId the value for moe_gc_watch_event.resource_id
     *
     * @mbg.generated
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId == null ? null : resourceId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.channel_uuid
     *
     * @return the value of moe_gc_watch_event.channel_uuid
     *
     * @mbg.generated
     */
    public String getChannelUuid() {
        return channelUuid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.channel_uuid
     *
     * @param channelUuid the value for moe_gc_watch_event.channel_uuid
     *
     * @mbg.generated
     */
    public void setChannelUuid(String channelUuid) {
        this.channelUuid = channelUuid == null ? null : channelUuid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.token_expired_time
     *
     * @return the value of moe_gc_watch_event.token_expired_time
     *
     * @mbg.generated
     */
    public Long getTokenExpiredTime() {
        return tokenExpiredTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.token_expired_time
     *
     * @param tokenExpiredTime the value for moe_gc_watch_event.token_expired_time
     *
     * @mbg.generated
     */
    public void setTokenExpiredTime(Long tokenExpiredTime) {
        this.tokenExpiredTime = tokenExpiredTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.last_notify_time
     *
     * @return the value of moe_gc_watch_event.last_notify_time
     *
     * @mbg.generated
     */
    public Long getLastNotifyTime() {
        return lastNotifyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.last_notify_time
     *
     * @param lastNotifyTime the value for moe_gc_watch_event.last_notify_time
     *
     * @mbg.generated
     */
    public void setLastNotifyTime(Long lastNotifyTime) {
        this.lastNotifyTime = lastNotifyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.last_sync_time
     *
     * @return the value of moe_gc_watch_event.last_sync_time
     *
     * @mbg.generated
     */
    public Long getLastSyncTime() {
        return lastSyncTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.last_sync_time
     *
     * @param lastSyncTime the value for moe_gc_watch_event.last_sync_time
     *
     * @mbg.generated
     */
    public void setLastSyncTime(Long lastSyncTime) {
        this.lastSyncTime = lastSyncTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.create_time
     *
     * @return the value of moe_gc_watch_event.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.create_time
     *
     * @param createTime the value for moe_gc_watch_event.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.update_time
     *
     * @return the value of moe_gc_watch_event.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.update_time
     *
     * @param updateTime the value for moe_gc_watch_event.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_watch_event.company_id
     *
     * @return the value of moe_gc_watch_event.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_watch_event.company_id
     *
     * @param companyId the value for moe_gc_watch_event.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
