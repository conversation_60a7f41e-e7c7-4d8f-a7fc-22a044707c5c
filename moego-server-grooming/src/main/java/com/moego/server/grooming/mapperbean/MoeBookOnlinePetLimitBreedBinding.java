package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_pet_limit_breed_binding
 */
public class MoeBookOnlinePetLimitBreedBinding {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit_breed_binding.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit_breed_binding.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   pet type, 1-dog 2-cat 3-other
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit_breed_binding.pet_type_id
     *
     * @mbg.generated
     */
    private Integer petTypeId;

    /**
     * Database Column Remarks:
     *   all breed, 1-all 2-customize
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit_breed_binding.all_breed
     *
     * @mbg.generated
     */
    private Byte allBreed;

    /**
     * Database Column Remarks:
     *   status, 1-normal 2-deleted
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit_breed_binding.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit_breed_binding.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit_breed_binding.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit_breed_binding.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   breed id list
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit_breed_binding.breed_id_list
     *
     * @mbg.generated
     */
    private String breedIdList;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit_breed_binding.id
     *
     * @return the value of moe_book_online_pet_limit_breed_binding.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit_breed_binding.id
     *
     * @param id the value for moe_book_online_pet_limit_breed_binding.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit_breed_binding.business_id
     *
     * @return the value of moe_book_online_pet_limit_breed_binding.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit_breed_binding.business_id
     *
     * @param businessId the value for moe_book_online_pet_limit_breed_binding.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit_breed_binding.pet_type_id
     *
     * @return the value of moe_book_online_pet_limit_breed_binding.pet_type_id
     *
     * @mbg.generated
     */
    public Integer getPetTypeId() {
        return petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit_breed_binding.pet_type_id
     *
     * @param petTypeId the value for moe_book_online_pet_limit_breed_binding.pet_type_id
     *
     * @mbg.generated
     */
    public void setPetTypeId(Integer petTypeId) {
        this.petTypeId = petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit_breed_binding.all_breed
     *
     * @return the value of moe_book_online_pet_limit_breed_binding.all_breed
     *
     * @mbg.generated
     */
    public Byte getAllBreed() {
        return allBreed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit_breed_binding.all_breed
     *
     * @param allBreed the value for moe_book_online_pet_limit_breed_binding.all_breed
     *
     * @mbg.generated
     */
    public void setAllBreed(Byte allBreed) {
        this.allBreed = allBreed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit_breed_binding.status
     *
     * @return the value of moe_book_online_pet_limit_breed_binding.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit_breed_binding.status
     *
     * @param status the value for moe_book_online_pet_limit_breed_binding.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit_breed_binding.create_time
     *
     * @return the value of moe_book_online_pet_limit_breed_binding.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit_breed_binding.create_time
     *
     * @param createTime the value for moe_book_online_pet_limit_breed_binding.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit_breed_binding.update_time
     *
     * @return the value of moe_book_online_pet_limit_breed_binding.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit_breed_binding.update_time
     *
     * @param updateTime the value for moe_book_online_pet_limit_breed_binding.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit_breed_binding.company_id
     *
     * @return the value of moe_book_online_pet_limit_breed_binding.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit_breed_binding.company_id
     *
     * @param companyId the value for moe_book_online_pet_limit_breed_binding.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit_breed_binding.breed_id_list
     *
     * @return the value of moe_book_online_pet_limit_breed_binding.breed_id_list
     *
     * @mbg.generated
     */
    public String getBreedIdList() {
        return breedIdList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit_breed_binding.breed_id_list
     *
     * @param breedIdList the value for moe_book_online_pet_limit_breed_binding.breed_id_list
     *
     * @mbg.generated
     */
    public void setBreedIdList(String breedIdList) {
        this.breedIdList = breedIdList == null ? null : breedIdList.trim();
    }
}
