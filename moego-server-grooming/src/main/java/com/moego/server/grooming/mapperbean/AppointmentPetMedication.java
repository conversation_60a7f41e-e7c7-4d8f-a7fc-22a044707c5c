package com.moego.server.grooming.mapperbean;

import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table appointment_pet_medication
 */
public class AppointmentPetMedication {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.appointment_id
     *
     * @mbg.generated
     */
    private Long appointmentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.pet_detail_id
     *
     * @mbg.generated
     */
    private Long petDetailId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.pet_id
     *
     * @mbg.generated
     */
    private Long petId;

    /**
     * Database Column Remarks:
     *   such as 1.2, 1/2, 1 etc.
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.medication_amount
     *
     * @mbg.generated
     */
    private String medicationAmount;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 7
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.medication_unit
     *
     * @mbg.generated
     */
    private String medicationUnit;

    /**
     * Database Column Remarks:
     *   medication name, user input
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.medication_name
     *
     * @mbg.generated
     */
    private String medicationName;

    /**
     * Database Column Remarks:
     *   medication note, user input
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.medication_note
     *
     * @mbg.generated
     */
    private String medicationNote;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.deleted_at
     *
     * @mbg.generated
     */
    private Date deletedAt;

    /**
     * Database Column Remarks:
     *   1-EVERYDAY_EXCEPT_CHECKOUT_DATE; 2-EVERYDAY_INCLUDE_CHECKOUT_DATE; 3-SPECIFIC_DATE
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.date_type
     *
     * @mbg.generated
     */
    private FeedingMedicationScheduleDateType dateType;

    /**
     * Database Column Remarks:
     *   specific_date, yyyy-mm-dd
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_medication.specific_dates
     *
     * @mbg.generated
     */
    private String specificDates;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.id
     *
     * @return the value of appointment_pet_medication.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.id
     *
     * @param id the value for appointment_pet_medication.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.company_id
     *
     * @return the value of appointment_pet_medication.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.company_id
     *
     * @param companyId the value for appointment_pet_medication.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.appointment_id
     *
     * @return the value of appointment_pet_medication.appointment_id
     *
     * @mbg.generated
     */
    public Long getAppointmentId() {
        return appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.appointment_id
     *
     * @param appointmentId the value for appointment_pet_medication.appointment_id
     *
     * @mbg.generated
     */
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.pet_detail_id
     *
     * @return the value of appointment_pet_medication.pet_detail_id
     *
     * @mbg.generated
     */
    public Long getPetDetailId() {
        return petDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.pet_detail_id
     *
     * @param petDetailId the value for appointment_pet_medication.pet_detail_id
     *
     * @mbg.generated
     */
    public void setPetDetailId(Long petDetailId) {
        this.petDetailId = petDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.pet_id
     *
     * @return the value of appointment_pet_medication.pet_id
     *
     * @mbg.generated
     */
    public Long getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.pet_id
     *
     * @param petId the value for appointment_pet_medication.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.medication_amount
     *
     * @return the value of appointment_pet_medication.medication_amount
     *
     * @mbg.generated
     */
    public String getMedicationAmount() {
        return medicationAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.medication_amount
     *
     * @param medicationAmount the value for appointment_pet_medication.medication_amount
     *
     * @mbg.generated
     */
    public void setMedicationAmount(String medicationAmount) {
        this.medicationAmount = medicationAmount == null ? null : medicationAmount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.medication_unit
     *
     * @return the value of appointment_pet_medication.medication_unit
     *
     * @mbg.generated
     */
    public String getMedicationUnit() {
        return medicationUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.medication_unit
     *
     * @param medicationUnit the value for appointment_pet_medication.medication_unit
     *
     * @mbg.generated
     */
    public void setMedicationUnit(String medicationUnit) {
        this.medicationUnit = medicationUnit == null ? null : medicationUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.medication_name
     *
     * @return the value of appointment_pet_medication.medication_name
     *
     * @mbg.generated
     */
    public String getMedicationName() {
        return medicationName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.medication_name
     *
     * @param medicationName the value for appointment_pet_medication.medication_name
     *
     * @mbg.generated
     */
    public void setMedicationName(String medicationName) {
        this.medicationName = medicationName == null ? null : medicationName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.medication_note
     *
     * @return the value of appointment_pet_medication.medication_note
     *
     * @mbg.generated
     */
    public String getMedicationNote() {
        return medicationNote;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.medication_note
     *
     * @param medicationNote the value for appointment_pet_medication.medication_note
     *
     * @mbg.generated
     */
    public void setMedicationNote(String medicationNote) {
        this.medicationNote = medicationNote == null ? null : medicationNote.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.created_at
     *
     * @return the value of appointment_pet_medication.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.created_at
     *
     * @param createdAt the value for appointment_pet_medication.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.updated_at
     *
     * @return the value of appointment_pet_medication.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.updated_at
     *
     * @param updatedAt the value for appointment_pet_medication.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.deleted_at
     *
     * @return the value of appointment_pet_medication.deleted_at
     *
     * @mbg.generated
     */
    public Date getDeletedAt() {
        return deletedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.deleted_at
     *
     * @param deletedAt the value for appointment_pet_medication.deleted_at
     *
     * @mbg.generated
     */
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.date_type
     *
     * @return the value of appointment_pet_medication.date_type
     *
     * @mbg.generated
     */
    public FeedingMedicationScheduleDateType getDateType() {
        return dateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.date_type
     *
     * @param dateType the value for appointment_pet_medication.date_type
     *
     * @mbg.generated
     */
    public void setDateType(FeedingMedicationScheduleDateType dateType) {
        this.dateType = dateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_medication.specific_dates
     *
     * @return the value of appointment_pet_medication.specific_dates
     *
     * @mbg.generated
     */
    public String getSpecificDates() {
        return specificDates;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_medication.specific_dates
     *
     * @param specificDates the value for appointment_pet_medication.specific_dates
     *
     * @mbg.generated
     */
    public void setSpecificDates(String specificDates) {
        this.specificDates = specificDates == null ? null : specificDates.trim();
    }
}
