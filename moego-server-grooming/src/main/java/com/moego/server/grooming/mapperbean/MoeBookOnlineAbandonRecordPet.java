package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_abandon_record_pet
 */
public class MoeBookOnlineAbandonRecordPet {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   moe_business.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   Unique ID for each booking flow
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.booking_flow_id
     *
     * @mbg.generated
     */
    private String bookingFlowId;

    /**
     * Database Column Remarks:
     *   existing pet id, moe_pet.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.pet_id
     *
     * @mbg.generated
     */
    private Integer petId;

    /**
     * Database Column Remarks:
     *   the index order of pet
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.index_id
     *
     * @mbg.generated
     */
    private Integer indexId;

    /**
     * Database Column Remarks:
     *   pet select service id, moe_grooming_service.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   pet select add-on ids, moe_grooming_service.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.addon_ids
     *
     * @mbg.generated
     */
    private String addonIds;

    /**
     * Database Column Remarks:
     *   pet type id, 1-dog, 2-cat, 11-other
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.pet_type_id
     *
     * @mbg.generated
     */
    private Integer petTypeId;

    /**
     * Database Column Remarks:
     *   avatar path
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.avatar_path
     *
     * @mbg.generated
     */
    private String avatarPath;

    /**
     * Database Column Remarks:
     *   new client last name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.pet_name
     *
     * @mbg.generated
     */
    private String petName;

    /**
     * Database Column Remarks:
     *   pet breed, moe_pet_breed.name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.breed
     *
     * @mbg.generated
     */
    private String breed;

    /**
     * Database Column Remarks:
     *   weight
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.weight
     *
     * @mbg.generated
     */
    private String weight;

    /**
     * Database Column Remarks:
     *   pet hair length, moe_pet_hair_length.name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.hair_length
     *
     * @mbg.generated
     */
    private String hairLength;

    /**
     * Database Column Remarks:
     *   vaccine list, contains vaccine ID, expiration date, document url
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.vaccine_list
     *
     * @mbg.generated
     */
    private String vaccineList;

    /**
     * Database Column Remarks:
     *   birthday, format yyyy-MM-dd
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.birthday
     *
     * @mbg.generated
     */
    private String birthday;

    /**
     * Database Column Remarks:
     *   1-male, 2-female
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.gender
     *
     * @mbg.generated
     */
    private Byte gender;

    /**
     * Database Column Remarks:
     *   is fixed
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.fixed
     *
     * @mbg.generated
     */
    private String fixed;

    /**
     * Database Column Remarks:
     *   behavior, moe_behavior.name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.behavior
     *
     * @mbg.generated
     */
    private String behavior;

    /**
     * Database Column Remarks:
     *   vet name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.vet_name
     *
     * @mbg.generated
     */
    private String vetName;

    /**
     * Database Column Remarks:
     *   vet phone number
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.vet_phone_number
     *
     * @mbg.generated
     */
    private String vetPhoneNumber;

    /**
     * Database Column Remarks:
     *   vet address
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.vet_address
     *
     * @mbg.generated
     */
    private String vetAddress;

    /**
     * Database Column Remarks:
     *   emergency contact name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.emergency_contact_name
     *
     * @mbg.generated
     */
    private String emergencyContactName;

    /**
     * Database Column Remarks:
     *   emergency contact phone number
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.emergency_contact_phone
     *
     * @mbg.generated
     */
    private String emergencyContactPhone;

    /**
     * Database Column Remarks:
     *   custom question answers, question ID to answer
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.pet_question_answers
     *
     * @mbg.generated
     */
    private String petQuestionAnswers;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   health issues
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_pet.health_issues
     *
     * @mbg.generated
     */
    private String healthIssues;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.id
     *
     * @return the value of moe_book_online_abandon_record_pet.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.id
     *
     * @param id the value for moe_book_online_abandon_record_pet.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.business_id
     *
     * @return the value of moe_book_online_abandon_record_pet.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.business_id
     *
     * @param businessId the value for moe_book_online_abandon_record_pet.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.booking_flow_id
     *
     * @return the value of moe_book_online_abandon_record_pet.booking_flow_id
     *
     * @mbg.generated
     */
    public String getBookingFlowId() {
        return bookingFlowId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.booking_flow_id
     *
     * @param bookingFlowId the value for moe_book_online_abandon_record_pet.booking_flow_id
     *
     * @mbg.generated
     */
    public void setBookingFlowId(String bookingFlowId) {
        this.bookingFlowId = bookingFlowId == null ? null : bookingFlowId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.pet_id
     *
     * @return the value of moe_book_online_abandon_record_pet.pet_id
     *
     * @mbg.generated
     */
    public Integer getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.pet_id
     *
     * @param petId the value for moe_book_online_abandon_record_pet.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Integer petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.index_id
     *
     * @return the value of moe_book_online_abandon_record_pet.index_id
     *
     * @mbg.generated
     */
    public Integer getIndexId() {
        return indexId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.index_id
     *
     * @param indexId the value for moe_book_online_abandon_record_pet.index_id
     *
     * @mbg.generated
     */
    public void setIndexId(Integer indexId) {
        this.indexId = indexId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.service_id
     *
     * @return the value of moe_book_online_abandon_record_pet.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.service_id
     *
     * @param serviceId the value for moe_book_online_abandon_record_pet.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.addon_ids
     *
     * @return the value of moe_book_online_abandon_record_pet.addon_ids
     *
     * @mbg.generated
     */
    public String getAddonIds() {
        return addonIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.addon_ids
     *
     * @param addonIds the value for moe_book_online_abandon_record_pet.addon_ids
     *
     * @mbg.generated
     */
    public void setAddonIds(String addonIds) {
        this.addonIds = addonIds == null ? null : addonIds.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.pet_type_id
     *
     * @return the value of moe_book_online_abandon_record_pet.pet_type_id
     *
     * @mbg.generated
     */
    public Integer getPetTypeId() {
        return petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.pet_type_id
     *
     * @param petTypeId the value for moe_book_online_abandon_record_pet.pet_type_id
     *
     * @mbg.generated
     */
    public void setPetTypeId(Integer petTypeId) {
        this.petTypeId = petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.avatar_path
     *
     * @return the value of moe_book_online_abandon_record_pet.avatar_path
     *
     * @mbg.generated
     */
    public String getAvatarPath() {
        return avatarPath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.avatar_path
     *
     * @param avatarPath the value for moe_book_online_abandon_record_pet.avatar_path
     *
     * @mbg.generated
     */
    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath == null ? null : avatarPath.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.pet_name
     *
     * @return the value of moe_book_online_abandon_record_pet.pet_name
     *
     * @mbg.generated
     */
    public String getPetName() {
        return petName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.pet_name
     *
     * @param petName the value for moe_book_online_abandon_record_pet.pet_name
     *
     * @mbg.generated
     */
    public void setPetName(String petName) {
        this.petName = petName == null ? null : petName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.breed
     *
     * @return the value of moe_book_online_abandon_record_pet.breed
     *
     * @mbg.generated
     */
    public String getBreed() {
        return breed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.breed
     *
     * @param breed the value for moe_book_online_abandon_record_pet.breed
     *
     * @mbg.generated
     */
    public void setBreed(String breed) {
        this.breed = breed == null ? null : breed.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.weight
     *
     * @return the value of moe_book_online_abandon_record_pet.weight
     *
     * @mbg.generated
     */
    public String getWeight() {
        return weight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.weight
     *
     * @param weight the value for moe_book_online_abandon_record_pet.weight
     *
     * @mbg.generated
     */
    public void setWeight(String weight) {
        this.weight = weight == null ? null : weight.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.hair_length
     *
     * @return the value of moe_book_online_abandon_record_pet.hair_length
     *
     * @mbg.generated
     */
    public String getHairLength() {
        return hairLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.hair_length
     *
     * @param hairLength the value for moe_book_online_abandon_record_pet.hair_length
     *
     * @mbg.generated
     */
    public void setHairLength(String hairLength) {
        this.hairLength = hairLength == null ? null : hairLength.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.vaccine_list
     *
     * @return the value of moe_book_online_abandon_record_pet.vaccine_list
     *
     * @mbg.generated
     */
    public String getVaccineList() {
        return vaccineList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.vaccine_list
     *
     * @param vaccineList the value for moe_book_online_abandon_record_pet.vaccine_list
     *
     * @mbg.generated
     */
    public void setVaccineList(String vaccineList) {
        this.vaccineList = vaccineList == null ? null : vaccineList.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.birthday
     *
     * @return the value of moe_book_online_abandon_record_pet.birthday
     *
     * @mbg.generated
     */
    public String getBirthday() {
        return birthday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.birthday
     *
     * @param birthday the value for moe_book_online_abandon_record_pet.birthday
     *
     * @mbg.generated
     */
    public void setBirthday(String birthday) {
        this.birthday = birthday == null ? null : birthday.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.gender
     *
     * @return the value of moe_book_online_abandon_record_pet.gender
     *
     * @mbg.generated
     */
    public Byte getGender() {
        return gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.gender
     *
     * @param gender the value for moe_book_online_abandon_record_pet.gender
     *
     * @mbg.generated
     */
    public void setGender(Byte gender) {
        this.gender = gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.fixed
     *
     * @return the value of moe_book_online_abandon_record_pet.fixed
     *
     * @mbg.generated
     */
    public String getFixed() {
        return fixed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.fixed
     *
     * @param fixed the value for moe_book_online_abandon_record_pet.fixed
     *
     * @mbg.generated
     */
    public void setFixed(String fixed) {
        this.fixed = fixed == null ? null : fixed.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.behavior
     *
     * @return the value of moe_book_online_abandon_record_pet.behavior
     *
     * @mbg.generated
     */
    public String getBehavior() {
        return behavior;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.behavior
     *
     * @param behavior the value for moe_book_online_abandon_record_pet.behavior
     *
     * @mbg.generated
     */
    public void setBehavior(String behavior) {
        this.behavior = behavior == null ? null : behavior.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.vet_name
     *
     * @return the value of moe_book_online_abandon_record_pet.vet_name
     *
     * @mbg.generated
     */
    public String getVetName() {
        return vetName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.vet_name
     *
     * @param vetName the value for moe_book_online_abandon_record_pet.vet_name
     *
     * @mbg.generated
     */
    public void setVetName(String vetName) {
        this.vetName = vetName == null ? null : vetName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.vet_phone_number
     *
     * @return the value of moe_book_online_abandon_record_pet.vet_phone_number
     *
     * @mbg.generated
     */
    public String getVetPhoneNumber() {
        return vetPhoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.vet_phone_number
     *
     * @param vetPhoneNumber the value for moe_book_online_abandon_record_pet.vet_phone_number
     *
     * @mbg.generated
     */
    public void setVetPhoneNumber(String vetPhoneNumber) {
        this.vetPhoneNumber = vetPhoneNumber == null ? null : vetPhoneNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.vet_address
     *
     * @return the value of moe_book_online_abandon_record_pet.vet_address
     *
     * @mbg.generated
     */
    public String getVetAddress() {
        return vetAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.vet_address
     *
     * @param vetAddress the value for moe_book_online_abandon_record_pet.vet_address
     *
     * @mbg.generated
     */
    public void setVetAddress(String vetAddress) {
        this.vetAddress = vetAddress == null ? null : vetAddress.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.emergency_contact_name
     *
     * @return the value of moe_book_online_abandon_record_pet.emergency_contact_name
     *
     * @mbg.generated
     */
    public String getEmergencyContactName() {
        return emergencyContactName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.emergency_contact_name
     *
     * @param emergencyContactName the value for moe_book_online_abandon_record_pet.emergency_contact_name
     *
     * @mbg.generated
     */
    public void setEmergencyContactName(String emergencyContactName) {
        this.emergencyContactName = emergencyContactName == null ? null : emergencyContactName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.emergency_contact_phone
     *
     * @return the value of moe_book_online_abandon_record_pet.emergency_contact_phone
     *
     * @mbg.generated
     */
    public String getEmergencyContactPhone() {
        return emergencyContactPhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.emergency_contact_phone
     *
     * @param emergencyContactPhone the value for moe_book_online_abandon_record_pet.emergency_contact_phone
     *
     * @mbg.generated
     */
    public void setEmergencyContactPhone(String emergencyContactPhone) {
        this.emergencyContactPhone = emergencyContactPhone == null ? null : emergencyContactPhone.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.pet_question_answers
     *
     * @return the value of moe_book_online_abandon_record_pet.pet_question_answers
     *
     * @mbg.generated
     */
    public String getPetQuestionAnswers() {
        return petQuestionAnswers;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.pet_question_answers
     *
     * @param petQuestionAnswers the value for moe_book_online_abandon_record_pet.pet_question_answers
     *
     * @mbg.generated
     */
    public void setPetQuestionAnswers(String petQuestionAnswers) {
        this.petQuestionAnswers = petQuestionAnswers == null ? null : petQuestionAnswers.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.create_time
     *
     * @return the value of moe_book_online_abandon_record_pet.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.create_time
     *
     * @param createTime the value for moe_book_online_abandon_record_pet.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.update_time
     *
     * @return the value of moe_book_online_abandon_record_pet.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.update_time
     *
     * @param updateTime the value for moe_book_online_abandon_record_pet.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.company_id
     *
     * @return the value of moe_book_online_abandon_record_pet.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.company_id
     *
     * @param companyId the value for moe_book_online_abandon_record_pet.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_pet.health_issues
     *
     * @return the value of moe_book_online_abandon_record_pet.health_issues
     *
     * @mbg.generated
     */
    public String getHealthIssues() {
        return healthIssues;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_pet.health_issues
     *
     * @param healthIssues the value for moe_book_online_abandon_record_pet.health_issues
     *
     * @mbg.generated
     */
    public void setHealthIssues(String healthIssues) {
        this.healthIssues = healthIssues == null ? null : healthIssues.trim();
    }
}
