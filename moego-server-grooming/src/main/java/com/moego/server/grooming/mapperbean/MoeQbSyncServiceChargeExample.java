package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeQbSyncServiceChargeExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public MoeQbSyncServiceChargeExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andConnectIdIsNull() {
            addCriterion("connect_id is null");
            return (Criteria) this;
        }

        public Criteria andConnectIdIsNotNull() {
            addCriterion("connect_id is not null");
            return (Criteria) this;
        }

        public Criteria andConnectIdEqualTo(Integer value) {
            addCriterion("connect_id =", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotEqualTo(Integer value) {
            addCriterion("connect_id <>", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdGreaterThan(Integer value) {
            addCriterion("connect_id >", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("connect_id >=", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdLessThan(Integer value) {
            addCriterion("connect_id <", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdLessThanOrEqualTo(Integer value) {
            addCriterion("connect_id <=", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdIn(List<Integer> values) {
            addCriterion("connect_id in", values, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotIn(List<Integer> values) {
            addCriterion("connect_id not in", values, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdBetween(Integer value1, Integer value2) {
            addCriterion("connect_id between", value1, value2, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotBetween(Integer value1, Integer value2) {
            addCriterion("connect_id not between", value1, value2, "connectId");
            return (Criteria) this;
        }

        public Criteria andRealmIdIsNull() {
            addCriterion("realm_id is null");
            return (Criteria) this;
        }

        public Criteria andRealmIdIsNotNull() {
            addCriterion("realm_id is not null");
            return (Criteria) this;
        }

        public Criteria andRealmIdEqualTo(String value) {
            addCriterion("realm_id =", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotEqualTo(String value) {
            addCriterion("realm_id <>", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdGreaterThan(String value) {
            addCriterion("realm_id >", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdGreaterThanOrEqualTo(String value) {
            addCriterion("realm_id >=", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdLessThan(String value) {
            addCriterion("realm_id <", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdLessThanOrEqualTo(String value) {
            addCriterion("realm_id <=", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdLike(String value) {
            addCriterion("realm_id like", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotLike(String value) {
            addCriterion("realm_id not like", value, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdIn(List<String> values) {
            addCriterion("realm_id in", values, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotIn(List<String> values) {
            addCriterion("realm_id not in", values, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdBetween(String value1, String value2) {
            addCriterion("realm_id between", value1, value2, "realmId");
            return (Criteria) this;
        }

        public Criteria andRealmIdNotBetween(String value1, String value2) {
            addCriterion("realm_id not between", value1, value2, "realmId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdIsNull() {
            addCriterion("qb_service_id is null");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdIsNotNull() {
            addCriterion("qb_service_id is not null");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdEqualTo(String value) {
            addCriterion("qb_service_id =", value, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdNotEqualTo(String value) {
            addCriterion("qb_service_id <>", value, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdGreaterThan(String value) {
            addCriterion("qb_service_id >", value, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdGreaterThanOrEqualTo(String value) {
            addCriterion("qb_service_id >=", value, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdLessThan(String value) {
            addCriterion("qb_service_id <", value, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdLessThanOrEqualTo(String value) {
            addCriterion("qb_service_id <=", value, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdLike(String value) {
            addCriterion("qb_service_id like", value, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdNotLike(String value) {
            addCriterion("qb_service_id not like", value, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdIn(List<String> values) {
            addCriterion("qb_service_id in", values, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdNotIn(List<String> values) {
            addCriterion("qb_service_id not in", values, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdBetween(String value1, String value2) {
            addCriterion("qb_service_id between", value1, value2, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andQbServiceIdNotBetween(String value1, String value2) {
            addCriterion("qb_service_id not between", value1, value2, "qbServiceId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdIsNull() {
            addCriterion("service_charge_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdIsNotNull() {
            addCriterion("service_charge_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdEqualTo(Integer value) {
            addCriterion("service_charge_id =", value, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdNotEqualTo(Integer value) {
            addCriterion("service_charge_id <>", value, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdGreaterThan(Integer value) {
            addCriterion("service_charge_id >", value, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_charge_id >=", value, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdLessThan(Integer value) {
            addCriterion("service_charge_id <", value, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdLessThanOrEqualTo(Integer value) {
            addCriterion("service_charge_id <=", value, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdIn(List<Integer> values) {
            addCriterion("service_charge_id in", values, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdNotIn(List<Integer> values) {
            addCriterion("service_charge_id not in", values, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdBetween(Integer value1, Integer value2) {
            addCriterion("service_charge_id between", value1, value2, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("service_charge_id not between", value1, value2, "serviceChargeId");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameIsNull() {
            addCriterion("service_charge_name is null");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameIsNotNull() {
            addCriterion("service_charge_name is not null");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameEqualTo(String value) {
            addCriterion("service_charge_name =", value, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameNotEqualTo(String value) {
            addCriterion("service_charge_name <>", value, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameGreaterThan(String value) {
            addCriterion("service_charge_name >", value, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameGreaterThanOrEqualTo(String value) {
            addCriterion("service_charge_name >=", value, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameLessThan(String value) {
            addCriterion("service_charge_name <", value, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameLessThanOrEqualTo(String value) {
            addCriterion("service_charge_name <=", value, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameLike(String value) {
            addCriterion("service_charge_name like", value, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameNotLike(String value) {
            addCriterion("service_charge_name not like", value, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameIn(List<String> values) {
            addCriterion("service_charge_name in", values, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameNotIn(List<String> values) {
            addCriterion("service_charge_name not in", values, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameBetween(String value1, String value2) {
            addCriterion("service_charge_name between", value1, value2, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andServiceChargeNameNotBetween(String value1, String value2) {
            addCriterion("service_charge_name not between", value1, value2, "serviceChargeName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
