<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="booking_flow_id" jdbcType="VARCHAR" property="bookingFlowId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="referer" jdbcType="VARCHAR" property="referer" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="first_name" jdbcType="VARCHAR" property="firstName" />
    <result column="last_name" jdbcType="VARCHAR" property="lastName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="referral_source_id" jdbcType="INTEGER" property="referralSourceId" />
    <result column="preferred_groomer_id" jdbcType="INTEGER" property="preferredGroomerId" />
    <result column="preferred_frequency_day" jdbcType="INTEGER" property="preferredFrequencyDay" />
    <result column="preferred_frequency_type" jdbcType="TINYINT" property="preferredFrequencyType" />
    <result column="preferred_day" jdbcType="VARCHAR" property="preferredDay" />
    <result column="preferred_time" jdbcType="VARCHAR" property="preferredTime" />
    <result column="customer_question_answers" jdbcType="CHAR" property="customerQuestionAnswers" />
    <result column="address_id" jdbcType="INTEGER" property="addressId" />
    <result column="address1" jdbcType="VARCHAR" property="address1" />
    <result column="address2" jdbcType="VARCHAR" property="address2" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="zipcode" jdbcType="VARCHAR" property="zipcode" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="abandon_step" jdbcType="VARCHAR" property="abandonStep" />
    <result column="abandon_time" jdbcType="BIGINT" property="abandonTime" />
    <result column="abandon_status" jdbcType="VARCHAR" property="abandonStatus" />
    <result column="last_texted_time" jdbcType="BIGINT" property="lastTextedTime" />
    <result column="last_emailed_time" jdbcType="BIGINT" property="lastEmailedTime" />
    <result column="recovery_type" jdbcType="BIGINT" property="recoveryType" />
    <result column="recovery_time" jdbcType="BIGINT" property="recoveryTime" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="appointment_id" jdbcType="INTEGER" property="appointmentId" />
    <result column="lead_type" jdbcType="VARCHAR" property="leadType" />
    <result column="appointment_date" jdbcType="VARCHAR" property="appointmentDate" />
    <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
    <result column="agreement_info" jdbcType="CHAR" property="agreementInfo" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="delete_type" jdbcType="TINYINT" property="deleteType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="use_payment_seg_setting" jdbcType="BIT" property="usePaymentSegSetting" />
    <result column="payment_seg_setting_rule" jdbcType="CHAR" property="paymentSegSettingRule" />
    <result column="is_send_schedule_message" jdbcType="BIT" property="isSendScheduleMessage" />
    <result column="care_type" jdbcType="VARCHAR" property="careType" />
    <result column="specific_dates" jdbcType="VARCHAR" property="specificDates" typeHandler="com.moego.server.grooming.mapper.typehandler.StringListTypeHandler" />
    <result column="appointment_end_date" jdbcType="VARCHAR" property="appointmentEndDate" />
    <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
    <result column="is_notification_sent" jdbcType="BIT" property="isNotificationSent" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="additional_note" jdbcType="LONGVARCHAR" property="additionalNote" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.specificDatesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.specificDatesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, booking_flow_id, customer_id, referer, phone_number, first_name, 
    last_name, email, referral_source_id, preferred_groomer_id, preferred_frequency_day, 
    preferred_frequency_type, preferred_day, preferred_time, customer_question_answers, 
    address_id, address1, address2, city, state, zipcode, country, lat, lng, abandon_step, 
    abandon_time, abandon_status, last_texted_time, last_emailed_time, recovery_type, 
    recovery_time, staff_id, appointment_id, lead_type, appointment_date, appointment_start_time, 
    agreement_info, is_deleted, delete_type, create_time, update_time, company_id, use_payment_seg_setting, 
    payment_seg_setting_rule, is_send_schedule_message, care_type, specific_dates, appointment_end_date, 
    appointment_end_time, is_notification_sent
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    additional_note
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_abandon_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_book_online_abandon_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_abandon_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_abandon_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_abandon_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_abandon_record (business_id, booking_flow_id, customer_id, 
      referer, phone_number, first_name, 
      last_name, email, referral_source_id, 
      preferred_groomer_id, preferred_frequency_day, 
      preferred_frequency_type, preferred_day, preferred_time, 
      customer_question_answers, address_id, address1, 
      address2, city, state, 
      zipcode, country, lat, 
      lng, abandon_step, abandon_time, 
      abandon_status, last_texted_time, last_emailed_time, 
      recovery_type, recovery_time, staff_id, 
      appointment_id, lead_type, appointment_date, 
      appointment_start_time, agreement_info, is_deleted, 
      delete_type, create_time, update_time, 
      company_id, use_payment_seg_setting, payment_seg_setting_rule, 
      is_send_schedule_message, care_type, specific_dates, 
      appointment_end_date, appointment_end_time, 
      is_notification_sent, additional_note)
    values (#{businessId,jdbcType=INTEGER}, #{bookingFlowId,jdbcType=VARCHAR}, #{customerId,jdbcType=INTEGER}, 
      #{referer,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, #{firstName,jdbcType=VARCHAR}, 
      #{lastName,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{referralSourceId,jdbcType=INTEGER}, 
      #{preferredGroomerId,jdbcType=INTEGER}, #{preferredFrequencyDay,jdbcType=INTEGER}, 
      #{preferredFrequencyType,jdbcType=TINYINT}, #{preferredDay,jdbcType=VARCHAR}, #{preferredTime,jdbcType=VARCHAR}, 
      #{customerQuestionAnswers,jdbcType=CHAR}, #{addressId,jdbcType=INTEGER}, #{address1,jdbcType=VARCHAR}, 
      #{address2,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, 
      #{zipcode,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{lat,jdbcType=VARCHAR}, 
      #{lng,jdbcType=VARCHAR}, #{abandonStep,jdbcType=VARCHAR}, #{abandonTime,jdbcType=BIGINT}, 
      #{abandonStatus,jdbcType=VARCHAR}, #{lastTextedTime,jdbcType=BIGINT}, #{lastEmailedTime,jdbcType=BIGINT}, 
      #{recoveryType,jdbcType=BIGINT}, #{recoveryTime,jdbcType=BIGINT}, #{staffId,jdbcType=INTEGER}, 
      #{appointmentId,jdbcType=INTEGER}, #{leadType,jdbcType=VARCHAR}, #{appointmentDate,jdbcType=VARCHAR}, 
      #{appointmentStartTime,jdbcType=INTEGER}, #{agreementInfo,jdbcType=CHAR}, #{isDeleted,jdbcType=BIT}, 
      #{deleteType,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{companyId,jdbcType=BIGINT}, #{usePaymentSegSetting,jdbcType=BIT}, #{paymentSegSettingRule,jdbcType=CHAR}, 
      #{isSendScheduleMessage,jdbcType=BIT}, #{careType,jdbcType=VARCHAR}, #{specificDates,jdbcType=VARCHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler}, 
      #{appointmentEndDate,jdbcType=VARCHAR}, #{appointmentEndTime,jdbcType=INTEGER}, 
      #{isNotificationSent,jdbcType=BIT}, #{additionalNote,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_abandon_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="bookingFlowId != null">
        booking_flow_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="referer != null">
        referer,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="firstName != null">
        first_name,
      </if>
      <if test="lastName != null">
        last_name,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="referralSourceId != null">
        referral_source_id,
      </if>
      <if test="preferredGroomerId != null">
        preferred_groomer_id,
      </if>
      <if test="preferredFrequencyDay != null">
        preferred_frequency_day,
      </if>
      <if test="preferredFrequencyType != null">
        preferred_frequency_type,
      </if>
      <if test="preferredDay != null">
        preferred_day,
      </if>
      <if test="preferredTime != null">
        preferred_time,
      </if>
      <if test="customerQuestionAnswers != null">
        customer_question_answers,
      </if>
      <if test="addressId != null">
        address_id,
      </if>
      <if test="address1 != null">
        address1,
      </if>
      <if test="address2 != null">
        address2,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="zipcode != null">
        zipcode,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="abandonStep != null">
        abandon_step,
      </if>
      <if test="abandonTime != null">
        abandon_time,
      </if>
      <if test="abandonStatus != null">
        abandon_status,
      </if>
      <if test="lastTextedTime != null">
        last_texted_time,
      </if>
      <if test="lastEmailedTime != null">
        last_emailed_time,
      </if>
      <if test="recoveryType != null">
        recovery_type,
      </if>
      <if test="recoveryTime != null">
        recovery_time,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="appointmentId != null">
        appointment_id,
      </if>
      <if test="leadType != null">
        lead_type,
      </if>
      <if test="appointmentDate != null">
        appointment_date,
      </if>
      <if test="appointmentStartTime != null">
        appointment_start_time,
      </if>
      <if test="agreementInfo != null">
        agreement_info,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="deleteType != null">
        delete_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="usePaymentSegSetting != null">
        use_payment_seg_setting,
      </if>
      <if test="paymentSegSettingRule != null">
        payment_seg_setting_rule,
      </if>
      <if test="isSendScheduleMessage != null">
        is_send_schedule_message,
      </if>
      <if test="careType != null">
        care_type,
      </if>
      <if test="specificDates != null">
        specific_dates,
      </if>
      <if test="appointmentEndDate != null">
        appointment_end_date,
      </if>
      <if test="appointmentEndTime != null">
        appointment_end_time,
      </if>
      <if test="isNotificationSent != null">
        is_notification_sent,
      </if>
      <if test="additionalNote != null">
        additional_note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="bookingFlowId != null">
        #{bookingFlowId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="referer != null">
        #{referer,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null">
        #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null">
        #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="referralSourceId != null">
        #{referralSourceId,jdbcType=INTEGER},
      </if>
      <if test="preferredGroomerId != null">
        #{preferredGroomerId,jdbcType=INTEGER},
      </if>
      <if test="preferredFrequencyDay != null">
        #{preferredFrequencyDay,jdbcType=INTEGER},
      </if>
      <if test="preferredFrequencyType != null">
        #{preferredFrequencyType,jdbcType=TINYINT},
      </if>
      <if test="preferredDay != null">
        #{preferredDay,jdbcType=VARCHAR},
      </if>
      <if test="preferredTime != null">
        #{preferredTime,jdbcType=VARCHAR},
      </if>
      <if test="customerQuestionAnswers != null">
        #{customerQuestionAnswers,jdbcType=CHAR},
      </if>
      <if test="addressId != null">
        #{addressId,jdbcType=INTEGER},
      </if>
      <if test="address1 != null">
        #{address1,jdbcType=VARCHAR},
      </if>
      <if test="address2 != null">
        #{address2,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="zipcode != null">
        #{zipcode,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=VARCHAR},
      </if>
      <if test="abandonStep != null">
        #{abandonStep,jdbcType=VARCHAR},
      </if>
      <if test="abandonTime != null">
        #{abandonTime,jdbcType=BIGINT},
      </if>
      <if test="abandonStatus != null">
        #{abandonStatus,jdbcType=VARCHAR},
      </if>
      <if test="lastTextedTime != null">
        #{lastTextedTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmailedTime != null">
        #{lastEmailedTime,jdbcType=BIGINT},
      </if>
      <if test="recoveryType != null">
        #{recoveryType,jdbcType=BIGINT},
      </if>
      <if test="recoveryTime != null">
        #{recoveryTime,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="appointmentId != null">
        #{appointmentId,jdbcType=INTEGER},
      </if>
      <if test="leadType != null">
        #{leadType,jdbcType=VARCHAR},
      </if>
      <if test="appointmentDate != null">
        #{appointmentDate,jdbcType=VARCHAR},
      </if>
      <if test="appointmentStartTime != null">
        #{appointmentStartTime,jdbcType=INTEGER},
      </if>
      <if test="agreementInfo != null">
        #{agreementInfo,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="deleteType != null">
        #{deleteType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="usePaymentSegSetting != null">
        #{usePaymentSegSetting,jdbcType=BIT},
      </if>
      <if test="paymentSegSettingRule != null">
        #{paymentSegSettingRule,jdbcType=CHAR},
      </if>
      <if test="isSendScheduleMessage != null">
        #{isSendScheduleMessage,jdbcType=BIT},
      </if>
      <if test="careType != null">
        #{careType,jdbcType=VARCHAR},
      </if>
      <if test="specificDates != null">
        #{specificDates,jdbcType=VARCHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler},
      </if>
      <if test="appointmentEndDate != null">
        #{appointmentEndDate,jdbcType=VARCHAR},
      </if>
      <if test="appointmentEndTime != null">
        #{appointmentEndTime,jdbcType=INTEGER},
      </if>
      <if test="isNotificationSent != null">
        #{isNotificationSent,jdbcType=BIT},
      </if>
      <if test="additionalNote != null">
        #{additionalNote,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_book_online_abandon_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.bookingFlowId != null">
        booking_flow_id = #{record.bookingFlowId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.referer != null">
        referer = #{record.referer,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNumber != null">
        phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.firstName != null">
        first_name = #{record.firstName,jdbcType=VARCHAR},
      </if>
      <if test="record.lastName != null">
        last_name = #{record.lastName,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.referralSourceId != null">
        referral_source_id = #{record.referralSourceId,jdbcType=INTEGER},
      </if>
      <if test="record.preferredGroomerId != null">
        preferred_groomer_id = #{record.preferredGroomerId,jdbcType=INTEGER},
      </if>
      <if test="record.preferredFrequencyDay != null">
        preferred_frequency_day = #{record.preferredFrequencyDay,jdbcType=INTEGER},
      </if>
      <if test="record.preferredFrequencyType != null">
        preferred_frequency_type = #{record.preferredFrequencyType,jdbcType=TINYINT},
      </if>
      <if test="record.preferredDay != null">
        preferred_day = #{record.preferredDay,jdbcType=VARCHAR},
      </if>
      <if test="record.preferredTime != null">
        preferred_time = #{record.preferredTime,jdbcType=VARCHAR},
      </if>
      <if test="record.customerQuestionAnswers != null">
        customer_question_answers = #{record.customerQuestionAnswers,jdbcType=CHAR},
      </if>
      <if test="record.addressId != null">
        address_id = #{record.addressId,jdbcType=INTEGER},
      </if>
      <if test="record.address1 != null">
        address1 = #{record.address1,jdbcType=VARCHAR},
      </if>
      <if test="record.address2 != null">
        address2 = #{record.address2,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=VARCHAR},
      </if>
      <if test="record.zipcode != null">
        zipcode = #{record.zipcode,jdbcType=VARCHAR},
      </if>
      <if test="record.country != null">
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.lat != null">
        lat = #{record.lat,jdbcType=VARCHAR},
      </if>
      <if test="record.lng != null">
        lng = #{record.lng,jdbcType=VARCHAR},
      </if>
      <if test="record.abandonStep != null">
        abandon_step = #{record.abandonStep,jdbcType=VARCHAR},
      </if>
      <if test="record.abandonTime != null">
        abandon_time = #{record.abandonTime,jdbcType=BIGINT},
      </if>
      <if test="record.abandonStatus != null">
        abandon_status = #{record.abandonStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lastTextedTime != null">
        last_texted_time = #{record.lastTextedTime,jdbcType=BIGINT},
      </if>
      <if test="record.lastEmailedTime != null">
        last_emailed_time = #{record.lastEmailedTime,jdbcType=BIGINT},
      </if>
      <if test="record.recoveryType != null">
        recovery_type = #{record.recoveryType,jdbcType=BIGINT},
      </if>
      <if test="record.recoveryTime != null">
        recovery_time = #{record.recoveryTime,jdbcType=BIGINT},
      </if>
      <if test="record.staffId != null">
        staff_id = #{record.staffId,jdbcType=INTEGER},
      </if>
      <if test="record.appointmentId != null">
        appointment_id = #{record.appointmentId,jdbcType=INTEGER},
      </if>
      <if test="record.leadType != null">
        lead_type = #{record.leadType,jdbcType=VARCHAR},
      </if>
      <if test="record.appointmentDate != null">
        appointment_date = #{record.appointmentDate,jdbcType=VARCHAR},
      </if>
      <if test="record.appointmentStartTime != null">
        appointment_start_time = #{record.appointmentStartTime,jdbcType=INTEGER},
      </if>
      <if test="record.agreementInfo != null">
        agreement_info = #{record.agreementInfo,jdbcType=CHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.deleteType != null">
        delete_type = #{record.deleteType,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.usePaymentSegSetting != null">
        use_payment_seg_setting = #{record.usePaymentSegSetting,jdbcType=BIT},
      </if>
      <if test="record.paymentSegSettingRule != null">
        payment_seg_setting_rule = #{record.paymentSegSettingRule,jdbcType=CHAR},
      </if>
      <if test="record.isSendScheduleMessage != null">
        is_send_schedule_message = #{record.isSendScheduleMessage,jdbcType=BIT},
      </if>
      <if test="record.careType != null">
        care_type = #{record.careType,jdbcType=VARCHAR},
      </if>
      <if test="record.specificDates != null">
        specific_dates = #{record.specificDates,jdbcType=VARCHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler},
      </if>
      <if test="record.appointmentEndDate != null">
        appointment_end_date = #{record.appointmentEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.appointmentEndTime != null">
        appointment_end_time = #{record.appointmentEndTime,jdbcType=INTEGER},
      </if>
      <if test="record.isNotificationSent != null">
        is_notification_sent = #{record.isNotificationSent,jdbcType=BIT},
      </if>
      <if test="record.additionalNote != null">
        additional_note = #{record.additionalNote,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      booking_flow_id = #{record.bookingFlowId,jdbcType=VARCHAR},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      referer = #{record.referer,jdbcType=VARCHAR},
      phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      first_name = #{record.firstName,jdbcType=VARCHAR},
      last_name = #{record.lastName,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      referral_source_id = #{record.referralSourceId,jdbcType=INTEGER},
      preferred_groomer_id = #{record.preferredGroomerId,jdbcType=INTEGER},
      preferred_frequency_day = #{record.preferredFrequencyDay,jdbcType=INTEGER},
      preferred_frequency_type = #{record.preferredFrequencyType,jdbcType=TINYINT},
      preferred_day = #{record.preferredDay,jdbcType=VARCHAR},
      preferred_time = #{record.preferredTime,jdbcType=VARCHAR},
      customer_question_answers = #{record.customerQuestionAnswers,jdbcType=CHAR},
      address_id = #{record.addressId,jdbcType=INTEGER},
      address1 = #{record.address1,jdbcType=VARCHAR},
      address2 = #{record.address2,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=VARCHAR},
      zipcode = #{record.zipcode,jdbcType=VARCHAR},
      country = #{record.country,jdbcType=VARCHAR},
      lat = #{record.lat,jdbcType=VARCHAR},
      lng = #{record.lng,jdbcType=VARCHAR},
      abandon_step = #{record.abandonStep,jdbcType=VARCHAR},
      abandon_time = #{record.abandonTime,jdbcType=BIGINT},
      abandon_status = #{record.abandonStatus,jdbcType=VARCHAR},
      last_texted_time = #{record.lastTextedTime,jdbcType=BIGINT},
      last_emailed_time = #{record.lastEmailedTime,jdbcType=BIGINT},
      recovery_type = #{record.recoveryType,jdbcType=BIGINT},
      recovery_time = #{record.recoveryTime,jdbcType=BIGINT},
      staff_id = #{record.staffId,jdbcType=INTEGER},
      appointment_id = #{record.appointmentId,jdbcType=INTEGER},
      lead_type = #{record.leadType,jdbcType=VARCHAR},
      appointment_date = #{record.appointmentDate,jdbcType=VARCHAR},
      appointment_start_time = #{record.appointmentStartTime,jdbcType=INTEGER},
      agreement_info = #{record.agreementInfo,jdbcType=CHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      delete_type = #{record.deleteType,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=BIGINT},
      use_payment_seg_setting = #{record.usePaymentSegSetting,jdbcType=BIT},
      payment_seg_setting_rule = #{record.paymentSegSettingRule,jdbcType=CHAR},
      is_send_schedule_message = #{record.isSendScheduleMessage,jdbcType=BIT},
      care_type = #{record.careType,jdbcType=VARCHAR},
      specific_dates = #{record.specificDates,jdbcType=VARCHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler},
      appointment_end_date = #{record.appointmentEndDate,jdbcType=VARCHAR},
      appointment_end_time = #{record.appointmentEndTime,jdbcType=INTEGER},
      is_notification_sent = #{record.isNotificationSent,jdbcType=BIT},
      additional_note = #{record.additionalNote,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      booking_flow_id = #{record.bookingFlowId,jdbcType=VARCHAR},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      referer = #{record.referer,jdbcType=VARCHAR},
      phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      first_name = #{record.firstName,jdbcType=VARCHAR},
      last_name = #{record.lastName,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      referral_source_id = #{record.referralSourceId,jdbcType=INTEGER},
      preferred_groomer_id = #{record.preferredGroomerId,jdbcType=INTEGER},
      preferred_frequency_day = #{record.preferredFrequencyDay,jdbcType=INTEGER},
      preferred_frequency_type = #{record.preferredFrequencyType,jdbcType=TINYINT},
      preferred_day = #{record.preferredDay,jdbcType=VARCHAR},
      preferred_time = #{record.preferredTime,jdbcType=VARCHAR},
      customer_question_answers = #{record.customerQuestionAnswers,jdbcType=CHAR},
      address_id = #{record.addressId,jdbcType=INTEGER},
      address1 = #{record.address1,jdbcType=VARCHAR},
      address2 = #{record.address2,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=VARCHAR},
      zipcode = #{record.zipcode,jdbcType=VARCHAR},
      country = #{record.country,jdbcType=VARCHAR},
      lat = #{record.lat,jdbcType=VARCHAR},
      lng = #{record.lng,jdbcType=VARCHAR},
      abandon_step = #{record.abandonStep,jdbcType=VARCHAR},
      abandon_time = #{record.abandonTime,jdbcType=BIGINT},
      abandon_status = #{record.abandonStatus,jdbcType=VARCHAR},
      last_texted_time = #{record.lastTextedTime,jdbcType=BIGINT},
      last_emailed_time = #{record.lastEmailedTime,jdbcType=BIGINT},
      recovery_type = #{record.recoveryType,jdbcType=BIGINT},
      recovery_time = #{record.recoveryTime,jdbcType=BIGINT},
      staff_id = #{record.staffId,jdbcType=INTEGER},
      appointment_id = #{record.appointmentId,jdbcType=INTEGER},
      lead_type = #{record.leadType,jdbcType=VARCHAR},
      appointment_date = #{record.appointmentDate,jdbcType=VARCHAR},
      appointment_start_time = #{record.appointmentStartTime,jdbcType=INTEGER},
      agreement_info = #{record.agreementInfo,jdbcType=CHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      delete_type = #{record.deleteType,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=BIGINT},
      use_payment_seg_setting = #{record.usePaymentSegSetting,jdbcType=BIT},
      payment_seg_setting_rule = #{record.paymentSegSettingRule,jdbcType=CHAR},
      is_send_schedule_message = #{record.isSendScheduleMessage,jdbcType=BIT},
      care_type = #{record.careType,jdbcType=VARCHAR},
      specific_dates = #{record.specificDates,jdbcType=VARCHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler},
      appointment_end_date = #{record.appointmentEndDate,jdbcType=VARCHAR},
      appointment_end_time = #{record.appointmentEndTime,jdbcType=INTEGER},
      is_notification_sent = #{record.isNotificationSent,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="bookingFlowId != null">
        booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="referer != null">
        referer = #{referer,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null">
        first_name = #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null">
        last_name = #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="referralSourceId != null">
        referral_source_id = #{referralSourceId,jdbcType=INTEGER},
      </if>
      <if test="preferredGroomerId != null">
        preferred_groomer_id = #{preferredGroomerId,jdbcType=INTEGER},
      </if>
      <if test="preferredFrequencyDay != null">
        preferred_frequency_day = #{preferredFrequencyDay,jdbcType=INTEGER},
      </if>
      <if test="preferredFrequencyType != null">
        preferred_frequency_type = #{preferredFrequencyType,jdbcType=TINYINT},
      </if>
      <if test="preferredDay != null">
        preferred_day = #{preferredDay,jdbcType=VARCHAR},
      </if>
      <if test="preferredTime != null">
        preferred_time = #{preferredTime,jdbcType=VARCHAR},
      </if>
      <if test="customerQuestionAnswers != null">
        customer_question_answers = #{customerQuestionAnswers,jdbcType=CHAR},
      </if>
      <if test="addressId != null">
        address_id = #{addressId,jdbcType=INTEGER},
      </if>
      <if test="address1 != null">
        address1 = #{address1,jdbcType=VARCHAR},
      </if>
      <if test="address2 != null">
        address2 = #{address2,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=VARCHAR},
      </if>
      <if test="zipcode != null">
        zipcode = #{zipcode,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=VARCHAR},
      </if>
      <if test="abandonStep != null">
        abandon_step = #{abandonStep,jdbcType=VARCHAR},
      </if>
      <if test="abandonTime != null">
        abandon_time = #{abandonTime,jdbcType=BIGINT},
      </if>
      <if test="abandonStatus != null">
        abandon_status = #{abandonStatus,jdbcType=VARCHAR},
      </if>
      <if test="lastTextedTime != null">
        last_texted_time = #{lastTextedTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmailedTime != null">
        last_emailed_time = #{lastEmailedTime,jdbcType=BIGINT},
      </if>
      <if test="recoveryType != null">
        recovery_type = #{recoveryType,jdbcType=BIGINT},
      </if>
      <if test="recoveryTime != null">
        recovery_time = #{recoveryTime,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="appointmentId != null">
        appointment_id = #{appointmentId,jdbcType=INTEGER},
      </if>
      <if test="leadType != null">
        lead_type = #{leadType,jdbcType=VARCHAR},
      </if>
      <if test="appointmentDate != null">
        appointment_date = #{appointmentDate,jdbcType=VARCHAR},
      </if>
      <if test="appointmentStartTime != null">
        appointment_start_time = #{appointmentStartTime,jdbcType=INTEGER},
      </if>
      <if test="agreementInfo != null">
        agreement_info = #{agreementInfo,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="deleteType != null">
        delete_type = #{deleteType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="usePaymentSegSetting != null">
        use_payment_seg_setting = #{usePaymentSegSetting,jdbcType=BIT},
      </if>
      <if test="paymentSegSettingRule != null">
        payment_seg_setting_rule = #{paymentSegSettingRule,jdbcType=CHAR},
      </if>
      <if test="isSendScheduleMessage != null">
        is_send_schedule_message = #{isSendScheduleMessage,jdbcType=BIT},
      </if>
      <if test="careType != null">
        care_type = #{careType,jdbcType=VARCHAR},
      </if>
      <if test="specificDates != null">
        specific_dates = #{specificDates,jdbcType=VARCHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler},
      </if>
      <if test="appointmentEndDate != null">
        appointment_end_date = #{appointmentEndDate,jdbcType=VARCHAR},
      </if>
      <if test="appointmentEndTime != null">
        appointment_end_time = #{appointmentEndTime,jdbcType=INTEGER},
      </if>
      <if test="isNotificationSent != null">
        is_notification_sent = #{isNotificationSent,jdbcType=BIT},
      </if>
      <if test="additionalNote != null">
        additional_note = #{additionalNote,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record
    set business_id = #{businessId,jdbcType=INTEGER},
      booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      referer = #{referer,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      first_name = #{firstName,jdbcType=VARCHAR},
      last_name = #{lastName,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      referral_source_id = #{referralSourceId,jdbcType=INTEGER},
      preferred_groomer_id = #{preferredGroomerId,jdbcType=INTEGER},
      preferred_frequency_day = #{preferredFrequencyDay,jdbcType=INTEGER},
      preferred_frequency_type = #{preferredFrequencyType,jdbcType=TINYINT},
      preferred_day = #{preferredDay,jdbcType=VARCHAR},
      preferred_time = #{preferredTime,jdbcType=VARCHAR},
      customer_question_answers = #{customerQuestionAnswers,jdbcType=CHAR},
      address_id = #{addressId,jdbcType=INTEGER},
      address1 = #{address1,jdbcType=VARCHAR},
      address2 = #{address2,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      state = #{state,jdbcType=VARCHAR},
      zipcode = #{zipcode,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR},
      lng = #{lng,jdbcType=VARCHAR},
      abandon_step = #{abandonStep,jdbcType=VARCHAR},
      abandon_time = #{abandonTime,jdbcType=BIGINT},
      abandon_status = #{abandonStatus,jdbcType=VARCHAR},
      last_texted_time = #{lastTextedTime,jdbcType=BIGINT},
      last_emailed_time = #{lastEmailedTime,jdbcType=BIGINT},
      recovery_type = #{recoveryType,jdbcType=BIGINT},
      recovery_time = #{recoveryTime,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=INTEGER},
      appointment_id = #{appointmentId,jdbcType=INTEGER},
      lead_type = #{leadType,jdbcType=VARCHAR},
      appointment_date = #{appointmentDate,jdbcType=VARCHAR},
      appointment_start_time = #{appointmentStartTime,jdbcType=INTEGER},
      agreement_info = #{agreementInfo,jdbcType=CHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      delete_type = #{deleteType,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      use_payment_seg_setting = #{usePaymentSegSetting,jdbcType=BIT},
      payment_seg_setting_rule = #{paymentSegSettingRule,jdbcType=CHAR},
      is_send_schedule_message = #{isSendScheduleMessage,jdbcType=BIT},
      care_type = #{careType,jdbcType=VARCHAR},
      specific_dates = #{specificDates,jdbcType=VARCHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler},
      appointment_end_date = #{appointmentEndDate,jdbcType=VARCHAR},
      appointment_end_time = #{appointmentEndTime,jdbcType=INTEGER},
      is_notification_sent = #{isNotificationSent,jdbcType=BIT},
      additional_note = #{additionalNote,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_abandon_record
    set business_id = #{businessId,jdbcType=INTEGER},
      booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      referer = #{referer,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      first_name = #{firstName,jdbcType=VARCHAR},
      last_name = #{lastName,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      referral_source_id = #{referralSourceId,jdbcType=INTEGER},
      preferred_groomer_id = #{preferredGroomerId,jdbcType=INTEGER},
      preferred_frequency_day = #{preferredFrequencyDay,jdbcType=INTEGER},
      preferred_frequency_type = #{preferredFrequencyType,jdbcType=TINYINT},
      preferred_day = #{preferredDay,jdbcType=VARCHAR},
      preferred_time = #{preferredTime,jdbcType=VARCHAR},
      customer_question_answers = #{customerQuestionAnswers,jdbcType=CHAR},
      address_id = #{addressId,jdbcType=INTEGER},
      address1 = #{address1,jdbcType=VARCHAR},
      address2 = #{address2,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      state = #{state,jdbcType=VARCHAR},
      zipcode = #{zipcode,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR},
      lng = #{lng,jdbcType=VARCHAR},
      abandon_step = #{abandonStep,jdbcType=VARCHAR},
      abandon_time = #{abandonTime,jdbcType=BIGINT},
      abandon_status = #{abandonStatus,jdbcType=VARCHAR},
      last_texted_time = #{lastTextedTime,jdbcType=BIGINT},
      last_emailed_time = #{lastEmailedTime,jdbcType=BIGINT},
      recovery_type = #{recoveryType,jdbcType=BIGINT},
      recovery_time = #{recoveryTime,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=INTEGER},
      appointment_id = #{appointmentId,jdbcType=INTEGER},
      lead_type = #{leadType,jdbcType=VARCHAR},
      appointment_date = #{appointmentDate,jdbcType=VARCHAR},
      appointment_start_time = #{appointmentStartTime,jdbcType=INTEGER},
      agreement_info = #{agreementInfo,jdbcType=CHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      delete_type = #{deleteType,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      use_payment_seg_setting = #{usePaymentSegSetting,jdbcType=BIT},
      payment_seg_setting_rule = #{paymentSegSettingRule,jdbcType=CHAR},
      is_send_schedule_message = #{isSendScheduleMessage,jdbcType=BIT},
      care_type = #{careType,jdbcType=VARCHAR},
      specific_dates = #{specificDates,jdbcType=VARCHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.StringListTypeHandler},
      appointment_end_date = #{appointmentEndDate,jdbcType=VARCHAR},
      appointment_end_time = #{appointmentEndTime,jdbcType=INTEGER},
      is_notification_sent = #{isNotificationSent,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <sql id="Abandoned_Status">
    '${@com.moego.server.grooming.web.params.SearchAbandonedClientParam$<EMAIL>}'
  </sql>
  <sql id="Recovered_Status">
    '${@com.moego.server.grooming.web.params.SearchAbandonedClientParam$<EMAIL>}'
  </sql>
  <sql id="Not_Recovered_Statuses">
    <foreach close=")" collection="@com.moego.server.grooming.web.params.SearchAbandonedClientParam$AbandonStatus@NOT_RECOVERED_STATUSES" item="status" open="(" separator=",">
      #{status}
    </foreach>
  </sql>
  <sql id="Recoverable_Steps">
    <foreach close=")" collection="@com.moego.server.grooming.enums.OBStepEnum@listRecoverableSteps()" item="step" open="(" separator=",">
      #{step}
    </foreach>
  </sql>

  <select id="selectUndeletedAbandonRecordByBookingFlowId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_abandon_record
    where business_id = #{businessId,jdbcType=INTEGER}
    and booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR}
    and is_deleted = 0
    limit 1
  </select>

  <select id="selectAbandonRecordByBookingFlowId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_abandon_record
    where business_id = #{businessId,jdbcType=INTEGER}
    and booking_flow_id = #{bookingFlowId,jdbcType=VARCHAR}
    order by abandon_time desc
    limit 1
  </select>

  <select id="selectBookingFlowIdsByKeyword" resultType="java.lang.String">
    select booking_flow_id
    from moe_book_online_abandon_record
    where business_id = #{businessId}
      and is_deleted = 0
      and concat(
              phone_number,
              COALESCE(first_name, ''),
              COALESCE(last_name, ''),
              COALESCE(email, ''),
              COALESCE(address1, ''),
              COALESCE(address2, ''),
              COALESCE(city, ''),
              COALESCE(state, ''),
              COALESCE(zipcode, ''),
              COALESCE(country, '')
            )
            like concat('%', #{keyword}, '%')
  </select>

  <update id="updateAbandonedRecordByCustomerId">
    update moe_book_online_abandon_record
    <set>
      <if test="param.recoveryType != null">
        recovery_type = #{param.recoveryType},
      </if>
      <if test="param.recoveryTimeSec != null">
        recovery_time = #{param.recoveryTimeSec},
      </if>
      <if test="param.isDeleted != null">
        is_deleted = #{param.isDeleted},
      </if>
      <if test="param.deleteType != null">
        delete_type = #{param.deleteType},
      </if>
      <if test="param.additionalNote != null">
        additional_note = #{param.additionalNote},
      </if>
      <if test="param.appointmentId != null">
        appointment_id = #{param.appointmentId},
      </if>
    </set>
    where business_id = #{businessId}
    and customer_id = #{customerId}
    and is_deleted = 0
    and abandon_status in <include refid="Not_Recovered_Statuses" />
  </update>

  <select id="searchByFilterPram" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from
        moe_book_online_abandon_record
    where
        business_id = #{param.businessId}
        <if test="param.leadType != null and param.leadType.size() &gt; 0">
          and lead_type in
          <foreach close=")" collection="param.leadType" item="it" open="(" separator=",">
            #{it}
          </foreach>
        </if>
        <if test="param.abandonStep != null and param.abandonStep.size() &gt; 0">
          and abandon_step in
          <foreach close=")" collection="param.abandonStep" item="step" open="(" separator=",">
            #{step}
          </foreach>
        </if>
        <if test="param.recoveryType != null and param.recoveryType.size() &gt; 0">
          and recovery_type in
          <foreach close=")" collection="param.recoveryType" item="type" open="(" separator=",">
            #{type}
          </foreach>
        </if>
        <if test="param.abandonStatuses != null and param.abandonStatuses.size() &gt; 0">
          and abandon_status in
          <foreach close=")" collection="param.abandonStatuses" item="status" open="(" separator=",">
            #{status}
          </foreach>
        </if>
        <if test="param.lastContactStartTimeSec != null">
          and GREATEST(last_texted_time, last_emailed_time) &gt;= #{param.lastContactStartTimeSec}
        </if>
        <if test="param.lastContactEndTimeSec != null">
          and GREATEST(last_texted_time, last_emailed_time) &lt;= #{param.lastContactEndTimeSec}
        </if>
        <if test="param.includeBookingFlowIds != null and param.includeBookingFlowIds.size() &gt; 0">
          and booking_flow_id in
          <foreach close=")" collection="param.includeBookingFlowIds" item="bookingFlowId" open="(" separator=",">
            #{bookingFlowId}
          </foreach>
        </if>
        <if test="param.excludeBookingFlowIds != null and param.excludeBookingFlowIds.size() &gt; 0">
          and booking_flow_id not in
          <foreach close=")" collection="param.excludeBookingFlowIds" item="bookingFlowId" open="(" separator=",">
            #{bookingFlowId}
          </foreach>
        </if>
        <if test="param.startTimeSec != null">
          and abandon_time &gt;= #{param.startTimeSec}
        </if>
        <if test="param.endTimeSec != null">
          and abandon_time &lt;= #{param.endTimeSec}
        </if>
        <if test="!param.includeDeleted">
          and is_deleted = 0
        </if>
        <if test="param.notAssociated != null and param.notAssociated">
          and customer_id is null
        </if>
        <if test="param.careTypes != null and param.careTypes.size() &gt; 0">
          and care_type in
          <foreach close=")" collection="param.careTypes" item="careType" open="(" separator=",">
            #{careType}
          </foreach>
        </if>
        <if test="param.hasKeyword">
            and (1 != 1
              <if test="param.keywordBookingFlowIds != null and param.keywordBookingFlowIds.size() &gt; 0">
                or booking_flow_id in
                <foreach close=")" collection="param.keywordBookingFlowIds" item="bookingFlowId" open="(" separator=",">
                  #{bookingFlowId}
                </foreach>
              </if>
              <if test="param.keywordCustomerIds != null and param.keywordCustomerIds.size() &gt; 0">
                or customer_id in
                <foreach close=")" collection="param.keywordCustomerIds" item="customerId" open="(" separator=",">
                  #{customerId}
                </foreach>
              </if>
              <if test="param.keywordAddressIds != null and param.keywordAddressIds.size() &gt; 0">
                or address_id in
                <foreach close=")" collection="param.keywordAddressIds" item="addressId" open="(" separator=",">
                  #{addressId}
                </foreach>
              </if>
            )
        </if>
  </select>

  <select id="countStartedClients" resultType="int">
    SELECT
      COUNT(DISTINCT CASE WHEN customer_id IS NULL THEN phone_number ELSE customer_id END)
    FROM
      moe_book_online_abandon_record
    WHERE
      business_id = #{businessId}
      AND abandon_time &gt;= #{startTime}
      AND abandon_time &lt;= #{endTime}
      AND (is_deleted = 0 OR (is_deleted = 1 AND delete_type IN (3, 4, 5, 6, 7)))
      AND abandon_step IN
      <foreach close=")" collection="recoverableSteps" item="step" open="(" separator=",">
        #{step}
      </foreach>
  </select>

  <select id="countStartedNotRecoveredRecords" resultType="int">
    SELECT
      COUNT(DISTINCT CASE WHEN customer_id IS NULL THEN phone_number ELSE customer_id END)
    FROM
      moe_book_online_abandon_record
    WHERE
      business_id = #{businessId}
      AND abandon_time &gt;= #{startTime}
      AND abandon_time &lt;= #{endTime}
      AND ((is_deleted = 0 AND abandon_status IN <include refid="Not_Recovered_Statuses" />) OR (is_deleted = 1 AND delete_type IN (3, 4, 5, 6, 7)))
  </select>

  <select id="countNotRecoveredClients" resultType="int">
    SELECT
      COUNT(DISTINCT CASE WHEN customer_id IS NULL THEN phone_number ELSE customer_id END)
    FROM
      moe_book_online_abandon_record
    WHERE
      business_id = #{businessId}
      AND abandon_time &gt;= #{startTime}
      AND abandon_time &lt;= #{endTime}
      AND ((is_deleted = 0 AND abandon_status IN <include refid="Not_Recovered_Statuses" />) OR (is_deleted = 1 AND delete_type IN (3, 5, 6, 7)))
      AND abandon_step IN
      <foreach close=")" collection="recoverableSteps" item="step" open="(" separator=",">
        #{step}
      </foreach>
  </select>

  <select id="countAbandonedClients" resultType="int">
    SELECT
      COUNT(DISTINCT CASE WHEN customer_id IS NULL THEN phone_number ELSE customer_id END)
    FROM
      moe_book_online_abandon_record
    WHERE
      business_id = #{businessId}
      AND abandon_time &gt;= #{startTime}
      AND abandon_time &lt;= #{endTime}
      AND (is_deleted = 0 OR (is_deleted = 1 AND delete_type IN (3, 5, 6, 7)))
      AND abandon_step IN
      <foreach close=")" collection="recoverableSteps" item="step" open="(" separator=",">
        #{step}
      </foreach>
  </select>

  <select id="countRecoverableClients" resultType="int">
    SELECT
      COUNT(DISTINCT CASE WHEN customer_id IS NULL THEN phone_number ELSE customer_id END)
    FROM
      moe_book_online_abandon_record
    WHERE
      business_id = #{businessId}
      AND abandon_time &gt;= #{startTime}
      AND abandon_time &lt;= #{endTime}
      AND is_deleted = 0
      AND abandon_step IN
      <foreach close=")" collection="recoverableSteps" item="step" open="(" separator=",">
        #{step}
      </foreach>
  </select>

  <select id="countRecoveredClients" resultType="int">
    SELECT
      COUNT(DISTINCT CASE WHEN customer_id IS NULL THEN phone_number ELSE customer_id END)
    FROM
      moe_book_online_abandon_record
    WHERE
      business_id = #{businessId}
      AND abandon_time &gt;= #{startTime}
      AND abandon_time &lt;= #{endTime}
      AND is_deleted = 0
      AND abandon_status = <include refid="Recovered_Status" />
  </select>

  <select id="countRecoverableAbandonedClients" resultType="int">
    SELECT
        COUNT(DISTINCT CASE WHEN customer_id IS NULL THEN phone_number ELSE customer_id END)
    FROM
        moe_book_online_abandon_record
    WHERE
      business_id = #{businessId}
      AND abandon_time &gt;= #{startTime}
      AND abandon_time &lt;= #{endTime}
      AND is_deleted = 0
      AND abandon_status IN <include refid="Not_Recovered_Statuses" />
      AND abandon_step IN
      <foreach close=")" collection="recoverableSteps" item="step" open="(" separator=",">
        #{step}
      </foreach>
  </select>

  <select id="countOnlyAbandonedClients" resultType="int">
    SELECT
    COUNT(DISTINCT CASE WHEN customer_id IS NULL THEN phone_number ELSE customer_id END)
    FROM
    moe_book_online_abandon_record
    WHERE
    business_id = #{businessId}
    AND abandon_time &gt;= #{startTime}
    AND abandon_time &lt;= #{endTime}
    AND is_deleted = 0
    AND abandon_status = <include refid="Abandoned_Status" />
    AND abandon_step IN
    <foreach close=")" collection="recoverableSteps" item="step" open="(" separator=",">
      #{step}
    </foreach>
  </select>

  <select id="countRecoveredRecords" resultType="int">
    SELECT
      COUNT(*)
    FROM
      moe_book_online_abandon_record
    WHERE
      business_id = #{businessId}
      AND abandon_time &gt;= #{startTime}
      AND abandon_time &lt;= #{endTime}
      AND is_deleted = 0
      AND abandon_status = <include refid="Recovered_Status" />
      AND abandon_step IN
      <foreach close=")" collection="recoverableSteps" item="step" open="(" separator=",">
        #{step}
      </foreach>
  </select>

  <select id="listRecoveredApptId" resultType="int">
    SELECT
      appointment_id
    FROM
      moe_book_online_abandon_record
    WHERE
      business_id = #{businessId}
      AND abandon_time &gt;= #{startTime}
      AND abandon_time &lt;= #{endTime}
      AND is_deleted = 0
      AND abandon_status = <include refid="Recovered_Status" />
  </select>

  <update id="removeAbandonRecords">
    UPDATE
        moe_book_online_abandon_record
    SET is_deleted = 1,
    delete_type = #{deleteType}
    WHERE business_id = #{businessId}
    AND is_deleted = 0
    AND abandon_status IN <include refid="Not_Recovered_Statuses" />
    AND (customer_id = #{customerId} OR customer_id IS NULL AND phone_number = #{phoneNumber})
  </update>

  <update id="deleteNotRecoveredAbandonedRecords">
    UPDATE
        moe_book_online_abandon_record
    SET is_deleted = 1,
        delete_type = #{deleteType}
    WHERE business_id = #{businessId}
    AND is_deleted = 0
    AND abandon_status IN <include refid="Not_Recovered_Statuses" />
    AND abandon_time &lt;= #{abandonTime}
    <choose>
      <when test="customerId != null">
        AND customer_id = #{customerId}
      </when>
      <otherwise>
        AND customer_id IS NULL AND phone_number = #{phoneNumber}
      </otherwise>
    </choose>
  </update>

  <select id="listByCustomerId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    moe_book_online_abandon_record
    WHERE
    business_id = #{businessId}
    AND customer_id = #{customerId}
    AND abandon_time &gt;= UNIX_TIMESTAMP(DATE_ADD(now(), INTERVAL -30 DAY))
    AND is_deleted = 0
    AND abandon_status IN <include refid="Not_Recovered_Statuses" />
    AND abandon_step IN <include refid="Recoverable_Steps" />
    ORDER BY
    update_time DESC
  </select>

  <select id="countExistingClient" resultType="java.lang.Integer">
    select
        count(distinct customer_id)
    from
        moe_book_online_abandon_record
    where
        business_id = #{businessId}
        and is_deleted = 0
        and lead_type = 'existing_client'
        and customer_id is not null
        <if test="startTime != null">
            and abandon_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and abandon_time &lt;= #{endTime}
        </if>
        <if test="abandonSteps != null and abandonSteps.size() &gt; 0">
            and abandon_step in
            <foreach close=")" collection="abandonSteps" item="abandonStep" open="(" separator=",">
                #{abandonStep}
            </foreach>
        </if>
  </select>

  <select id="countNewClient" resultType="java.lang.Integer">
    select
        count(distinct phone_number)
    from
        moe_book_online_abandon_record
    where
        business_id = #{businessId}
        and is_deleted = 0
        and lead_type = 'new_visitor'
        and phone_number != ''
        <if test="startTime != null">
            and abandon_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and abandon_time &lt;= #{endTime}
        </if>
        <if test="abandonSteps != null and abandonSteps.size() &gt; 0">
            and abandon_step in
            <foreach close=")" collection="abandonSteps" item="abandonStep" open="(" separator=",">
                #{abandonStep}
            </foreach>
        </if>
  </select>

  <select id="countExistingClientRecordByAbandonedStep" resultType="com.moego.server.grooming.mapper.po.CountByAbandonedStepPO">
    select
        abandon_step as abandonedStep,
        count(distinct case when abandon_status IN <include refid="Not_Recovered_Statuses" /> then customer_id end) +
        count(case when abandon_status = <include refid="Recovered_Status" /> then customer_id end) as count
    from
        moe_book_online_abandon_record
    where
        business_id = #{businessId}
        and is_deleted = 0
        and lead_type = 'existing_client'
        and customer_id is not null
        <if test="startTime != null">
            and abandon_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and abandon_time &lt;= #{endTime}
        </if>
        <if test="abandonSteps != null and abandonSteps.size() &gt; 0">
            and abandon_step in
            <foreach close=")" collection="abandonSteps" item="abandonStep" open="(" separator=",">
                #{abandonStep}
            </foreach>
        </if>
    group by abandon_step
  </select>

  <select id="countNewClientRecordByAbandonedStep" resultType="com.moego.server.grooming.mapper.po.CountByAbandonedStepPO">
    select
        abandon_step as abandonedStep,
        count(distinct case when abandon_status in <include refid="Not_Recovered_Statuses" /> then phone_number end) +
        count(case when abandon_status = <include refid="Recovered_Status" /> then phone_number end) as count
    from
        moe_book_online_abandon_record
    where
        business_id = #{businessId}
        and is_deleted = 0
        and lead_type = 'new_visitor'
        and phone_number != ''
        <if test="startTime != null">
            and abandon_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and abandon_time &lt;= #{endTime}
        </if>
        <if test="abandonSteps != null and abandonSteps.size() &gt; 0">
            and abandon_step in
            <foreach close=")" collection="abandonSteps" item="abandonStep" open="(" separator=",">
                #{abandonStep}
            </foreach>
        </if>
    group by abandon_step
  </select>

  <select id="getCustomerIds" resultType="java.lang.Integer">
    select
        distinct customer_id
    from
        moe_book_online_abandon_record
    where
        business_id = #{businessId}
        and is_deleted = 0
        and customer_id is not null
        <if test="startTime != null">
            and abandon_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and abandon_time &lt;= #{endTime}
        </if>
        <if test="abandonSteps != null and abandonSteps.size() &gt; 0">
            and abandon_step in
            <foreach close=")" collection="abandonSteps" item="abandonStep" open="(" separator=",">
                #{abandonStep}
            </foreach>
        </if>
  </select>

  <select id="getAddressIds" resultType="java.lang.Integer">
    select
        distinct address_id
    from
        moe_book_online_abandon_record
    where
        business_id = #{businessId}
        and is_deleted = 0
        and address_id is not null
        <if test="startTime != null">
            and abandon_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and abandon_time &lt;= #{endTime}
        </if>
        <if test="abandonSteps != null and abandonSteps.size() &gt; 0">
            and abandon_step in
            <foreach close=")" collection="abandonSteps" item="abandonStep" open="(" separator=",">
                #{abandonStep}
            </foreach>
        </if>
  </select>

  <select id="updateLeadTypeByCustomerId">
    UPDATE
        moe_book_online_abandon_record
    SET
        lead_type = #{leadType}
    WHERE business_id = #{businessId}
      AND customer_id = #{customerId}
      AND is_deleted = 0
      AND abandon_status IN <include refid="Not_Recovered_Statuses" />
  </select>

  <update id="resetCustomerIdById">
    UPDATE
      moe_book_online_abandon_record
    SET
        customer_id = null,
        lead_type = 'new_visitor'
    WHERE id = #{id}
  </update>
  <select id="listCustomerIdByFilter" resultType="int">
    SELECT
        DISTINCT customer_id
    FROM moe_book_online_abandon_record
    WHERE company_id = #{clientsFilter.companyId}
    <if test="clientsFilter.businessId != null">
      AND business_id = #{clientsFilter.businessId}
    </if>
      AND is_deleted = 0
      AND abandon_time &gt;= UNIX_TIMESTAMP(DATE_ADD(now(), INTERVAL -30 DAY))
      AND abandon_time &lt;= UNIX_TIMESTAMP(DATE_ADD(now(), INTERVAL -60 MINUTE))
      AND abandon_step IN
      <foreach close=")" collection="abandonSteps" item="abandonStep" open="(" separator=",">
        #{abandonStep}
      </foreach>
      AND customer_id IS NOT NULL
      <if test="clientsFilter.customerIds != null and clientsFilter.customerIds.size() &gt; 0">
        AND customer_id IN
        <foreach close=")" collection="clientsFilter.customerIds" item="customerId" open="(" separator=",">
          #{customerId}
        </foreach>
      </if>
      <if test="clientsFilter.filters != null and clientsFilter.filters.size &gt; 0">
        <foreach close=")" collection="clientsFilter.filters" index="idx" item="filter" open="AND (">
          ${filter.property.column} ${filter.whereClause}
          <if test="idx != clientsFilter.filters.size - 1">
            <choose>
              <when test="clientsFilter.type == @com.moego.common.enums.filter.TypeEnum@TYPE_AND">
                AND
              </when>
              <when test="clientsFilter.type == @com.moego.common.enums.filter.TypeEnum@TYPE_OR">
                OR
              </when>
            </choose>
          </if>
        </foreach>
      </if>
  </select>

  <select id="getLatestAbandonedRecordByCustomerIds" resultType="com.moego.server.grooming.service.dto.ob.OBAbandonRecordDTO">
    SELECT
        id,
        business_id businessId,
        customer_id customerId,
        abandon_time abandonTime
    FROM (SELECT
             id,
             business_id,
             customer_id,
             abandon_time,
             ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY abandon_time DESC) AS rn
           FROM
               moe_book_online_abandon_record
           WHERE business_id = #{businessId}
             AND is_deleted = 0
             AND abandon_status IN <include refid="Not_Recovered_Statuses" />
             AND customer_id IN
             <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
               #{customerId}
             </foreach>
         ) AS ranked_abandon
    WHERE rn = 1;
  </select>

    <select id="getLatestAbandonedRecord" resultMap="BaseResultMap">
      SELECT
        <include refid="Base_Column_List" />
      FROM
        moe_book_online_abandon_record
      WHERE is_deleted = 0
        AND recovery_type = 0
      ORDER BY id desc
      LIMIT 1
    </select>

    <select id="selectByAbandonTimeRange" resultType="java.lang.Integer">
      SELECT
        ar.id
      FROM
        moe_book_online_abandon_record ar
            left join moe_book_online_abandon_record_event_log e on ar.id = e.abandon_record_id
      WHERE ar.is_deleted = 0
      and ar.recovery_type = 0
      and ar.abandon_time &gt;= #{startTime}
      and ar.abandon_time &lt; #{endTime}
      and e.id is null
    </select>
</mapper>