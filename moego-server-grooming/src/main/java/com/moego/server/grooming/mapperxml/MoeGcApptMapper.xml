<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGcApptMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGcAppt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="gc_calendar_id" jdbcType="INTEGER" property="gcCalendarId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="sync_type" jdbcType="TINYINT" property="syncType" />
    <result column="import_event_id" jdbcType="VARCHAR" property="importEventId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, customer_id, gc_calendar_id, grooming_id, sync_type, import_event_id,
    create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_gc_appt
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_gc_appt
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGcAppt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_appt (business_id, customer_id, gc_calendar_id,
      grooming_id, sync_type, import_event_id,
      create_time, update_time, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{customerId,jdbcType=INTEGER}, #{gcCalendarId,jdbcType=INTEGER},
      #{groomingId,jdbcType=INTEGER}, #{syncType,jdbcType=TINYINT}, #{importEventId,jdbcType=VARCHAR},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcAppt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_appt
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="gcCalendarId != null">
        gc_calendar_id,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="syncType != null">
        sync_type,
      </if>
      <if test="importEventId != null">
        import_event_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="gcCalendarId != null">
        #{gcCalendarId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="syncType != null">
        #{syncType,jdbcType=TINYINT},
      </if>
      <if test="importEventId != null">
        #{importEventId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcAppt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_appt
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="gcCalendarId != null">
        gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="syncType != null">
        sync_type = #{syncType,jdbcType=TINYINT},
      </if>
      <if test="importEventId != null">
        import_event_id = #{importEventId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGcAppt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_appt
    set business_id = #{businessId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      sync_type = #{syncType,jdbcType=TINYINT},
      import_event_id = #{importEventId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByCalendarIdGroomingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_appt
        where business_id = #{businessId,jdbcType=INTEGER} and
        grooming_id = #{groomingId,jdbcType=INTEGER} and
        gc_calendar_id = #{calendarId,jdbcType=INTEGER} and sync_type = 2
    </select>

    <select id="selectByBusinessIdGroomingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_appt
        where business_id = #{businessId,jdbcType=INTEGER}
        and grooming_id = #{groomingId,jdbcType=INTEGER}
        and sync_type = 2
    </select>

    <select id="selectImportBlockByEventId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_appt
        where business_id = #{businessId,jdbcType=INTEGER} and
        gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER} and
        import_event_id = #{importEventId,jdbcType=VARCHAR} and sync_type = 1
    </select>
    <select id="selectDeletedImportBlockById" resultMap="BaseResultMap">
      select
      gca.id, gca.business_id, gca.customer_id, gca.gc_calendar_id, gca.grooming_id, gca.sync_type,gca.import_event_id, gca.create_time, gca.update_time
      from moe_gc_appt gca inner join moe_grooming_appointment g on gca.grooming_id = g.id and
      g.status != 4
      where gca.business_id = #{businessId,jdbcType=INTEGER} and
      gca.gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER} and gca.sync_type = 1
      <if test="idList != null">
        and gca.id not in
        <foreach close=")" collection="idList" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
    </select>
    <select id="selectByBlockId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_appt
        where business_id = #{businessId,jdbcType=INTEGER} and
        grooming_id = #{groomingId,jdbcType=INTEGER} and sync_type = 1 order by id desc limit 1
    </select>
</mapper>
