package com.moego.server.grooming.service.google;

import com.google.api.services.calendar.model.Event;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.grooming.dto.GroomingCustomerInfoDTO;
import com.moego.server.grooming.dto.GroomingPetInfoDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.service.dto.CalendarEventParamDto;
import com.moego.server.grooming.service.dto.PetDetailTimeDto;
import com.moego.server.grooming.utils.SyncCalendarUtil;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

public class CalendarCreateHelper {

    public static String getCalenderEventTitle(
            String customerName,
            List<GroomingPetInfoDetailDTO> petInfoDetails,
            List<Integer> petDetailIdList,
            Integer staffId,
            String staffName) {
        Map<Integer, Integer> petTypeCountMap = new HashMap<>();
        Map<Integer, String> petTypeNameMap = new HashMap<>();
        // pet统计过后，就会放在这里，下次统计时跳过
        List<Integer> petCountedList = new ArrayList<>();
        for (GroomingPetInfoDetailDTO petInfoDetail : petInfoDetails) {
            for (GroomingPetServiceDTO groomingPetServiceDTO : petInfoDetail.getGroomingPetServiceDTOS()) {
                if (!petDetailIdList.contains(groomingPetServiceDTO.getPetDetailId())) {
                    continue;
                }
                if (groomingPetServiceDTO.getStaffId().equals(staffId)) {
                    if (petCountedList.contains(petInfoDetail.getPetId())) {
                        continue;
                    }
                    petCountedList.add(petInfoDetail.getPetId());
                    petTypeNameMap.put(petInfoDetail.getPetTypeId(), petInfoDetail.getTypeName());
                    Integer petTypeCountValue = petTypeCountMap.get(petInfoDetail.getPetTypeId());
                    if (petTypeCountValue == null) {
                        petTypeCountValue = 0;
                    }
                    petTypeCountValue++;
                    petTypeCountMap.put(petInfoDetail.getPetTypeId(), petTypeCountValue);
                }
            }
        }

        Map<String, Integer> nameCountMap = new HashMap<>();
        for (Map.Entry<Integer, Integer> petTypeCount : petTypeCountMap.entrySet()) {
            nameCountMap.put(petTypeNameMap.get(petTypeCount.getKey()), petTypeCount.getValue());
        }
        StringBuilder nameCountSB = new StringBuilder();
        for (Map.Entry<String, Integer> integerStringEntry : nameCountMap.entrySet()) {
            String petTypeName = integerStringEntry.getKey();
            Integer petTypeCount = integerStringEntry.getValue();
            if (petTypeCount > 1) {
                petTypeName += "s";
            }
            nameCountSB.append(petTypeCount).append(petTypeName).append(", ");
        }
        String nameCountStr = nameCountSB.toString();
        return String.format(
                "%s (%s) by %s", customerName, nameCountStr.substring(0, nameCountStr.length() - 2), staffName);
    }

    public static String getPetAmount(
            List<GroomingPetInfoDetailDTO> petInfoDetails, List<Integer> petDetailIdList, Integer staffId) {
        return petInfoDetails.stream()
                // 按照 petId 去重
                .collect(Collectors.toMap(
                        GroomingPetInfoDetailDTO::getPetId, Function.identity(), (oldValue, newValue) -> newValue))
                .values()
                .stream()
                // 过滤
                .filter(petInfoDetail -> petInfoDetail.getGroomingPetServiceDTOS().stream()
                        .anyMatch(groomingPetServiceDTO ->
                                petDetailIdList.contains(groomingPetServiceDTO.getPetDetailId())
                                        && Objects.equals(staffId, groomingPetServiceDTO.getStaffId())))
                // 根据 typeName 分组计数
                .collect(Collectors.toMap(GroomingPetInfoDetailDTO::getTypeName, value -> 1, Integer::sum))
                .entrySet()
                .stream()
                // 拼接字符，示例结果：2Cats
                .map(entry -> {
                    StringBuilder result = new StringBuilder();
                    result.append(entry.getValue()).append(entry.getKey());
                    if (entry.getValue() > 1) {
                        return result.append("s");
                    }
                    return result;
                })
                // 连接，示例结果：1Dog, 2Cats
                .collect(Collectors.joining(", "));
    }

    public static CalendarEventParamDto convertBlockCalenderEventParamsList(
            MoeGroomingPetDetail petDetail, String description, String staffFirstName, String staffLastName) {
        CalendarEventParamDto eventParamDto = new CalendarEventParamDto();
        eventParamDto.setStartTime(petDetail.getStartTime());
        eventParamDto.setEndTime(petDetail.getStartTime() + petDetail.getServiceTime());
        eventParamDto.setClientEmail(null);
        eventParamDto.setFullAddress(null);
        if (!StringUtils.isEmpty(description)) {
            eventParamDto.setSummary(String.format("Blocked time - %s", getFullName(staffFirstName, staffLastName)));
        } else {
            eventParamDto.setSummary("Blocked time");
        }
        eventParamDto.setDescription(description);
        eventParamDto.setSyncedStaffId(petDetail.getStaffId());
        eventParamDto.setIsBlock(true);
        return eventParamDto;
    }

    public static List<CalendarEventParamDto> convertAppointmentCalenderEventParamsList(
            MoeBusinessDto businessInfo,
            GroomingCustomerInfoDTO customerInfo,
            List<GroomingPetInfoDetailDTO> petInfoDetails) {
        Map<Integer, String> staffNameMap = new HashMap<>();
        Map<Integer, List<PetDetailTimeDto>> staffPetDetailTimeMap = new HashMap<>();
        for (GroomingPetInfoDetailDTO petInfoDetail : petInfoDetails) {
            for (GroomingPetServiceDTO groomingPetServiceDTO : petInfoDetail.getGroomingPetServiceDTOS()) {
                staffNameMap.put(
                        groomingPetServiceDTO.getStaffId(),
                        getFullName(
                                groomingPetServiceDTO.getStaffFirstName(), groomingPetServiceDTO.getStaffLastName()));
                List<PetDetailTimeDto> staffTimeDto = staffPetDetailTimeMap.get(groomingPetServiceDTO.getStaffId());
                if (CollectionUtils.isEmpty(staffTimeDto)) {
                    staffTimeDto = new ArrayList<>();
                }
                PetDetailTimeDto timeDto = new PetDetailTimeDto();
                timeDto.setStartTime(groomingPetServiceDTO.getStartTime());
                timeDto.setEndTime(groomingPetServiceDTO.getStartTime() + groomingPetServiceDTO.getServiceTime());
                List<Integer> petDetailIdList = new ArrayList<>();
                petDetailIdList.add(groomingPetServiceDTO.getPetDetailId());
                timeDto.setPetDetailIdList(petDetailIdList);
                staffTimeDto.add(timeDto);
                staffPetDetailTimeMap.put(groomingPetServiceDTO.getStaffId(), staffTimeDto);
            }
        }
        List<CalendarEventParamDto> eventParamDtos = new ArrayList<>();
        for (Map.Entry<Integer, List<PetDetailTimeDto>> staffEntry : staffPetDetailTimeMap.entrySet()) {
            Integer staffId = staffEntry.getKey();
            List<PetDetailTimeDto> staffPetDetailList = staffEntry.getValue();
            if (staffPetDetailList.size() > 1) {
                Collections.sort(
                        staffPetDetailList,
                        (PetDetailTimeDto timeDto1, PetDetailTimeDto timeDto2) ->
                                timeDto1.getStartTime().compareTo(timeDto2.getEndTime()));
            }
            // 将staff的零散时间合并在一起
            List<PetDetailTimeDto> staffEventTimeList = new ArrayList<>();
            for (int i = 0; i < staffPetDetailList.size(); i++) {
                PetDetailTimeDto thisTimeDto = staffPetDetailList.get(i);
                if (i == 0) {
                    staffEventTimeList.add(thisTimeDto);
                } else {
                    PetDetailTimeDto lastTimeDto = staffPetDetailList.get(i - 1);
                    if (thisTimeDto.getStartTime().equals(lastTimeDto.getEndTime())) {
                        // 说明是相连的时间块
                        PetDetailTimeDto lastPetDetailTime = staffEventTimeList.get(staffEventTimeList.size() - 1);
                        lastPetDetailTime.getPetDetailIdList().addAll(thisTimeDto.getPetDetailIdList());
                        lastPetDetailTime.setEndTime(thisTimeDto.getEndTime());
                    } else {
                        staffEventTimeList.add(thisTimeDto);
                    }
                }
            }
            // 开始生成event数据
            for (PetDetailTimeDto eventTime : staffEventTimeList) {
                CalendarEventParamDto eventParamDto = new CalendarEventParamDto();
                eventParamDto.setStartTime(eventTime.getStartTime());
                eventParamDto.setEndTime(eventTime.getEndTime());
                eventParamDto.setClientEmail(customerInfo.getEmail());
                eventParamDto.setFullAddress(customerInfo.getClientFullAddress());
                eventParamDto.setCustomerName(getFullName(customerInfo.getFirstName(), customerInfo.getLastName()));
                eventParamDto.setStaffName(staffNameMap.get(staffEntry.getKey()));
                eventParamDto.setPetAmount(getPetAmount(petInfoDetails, eventTime.getPetDetailIdList(), staffId));
                eventParamDto.setStoreName(businessInfo.getBusinessName());
                eventParamDto.setPetInfoDetails(petInfoDetails);
                eventParamDto.setEventTime(eventTime);
                eventParamDto.setSyncedStaffId(staffId);
                eventParamDto.setIsBlock(false);
                eventParamDtos.add(eventParamDto);
            }
        }
        return eventParamDtos;
    }

    private static String getFullName(String staffFirstName, String staffLastName) {
        return staffFirstName + " " + staffLastName;
    }

    public static com.google.api.services.calendar.model.Event setEventData(CalendarEventParamDto eventParamDto)
            throws ParseException {
        com.google.api.services.calendar.model.Event newEvent = new com.google.api.services.calendar.model.Event();
        newEvent.setStart(SyncCalendarUtil.getEventDateTimeFromAppointmentDate(
                eventParamDto.getAppointmentDate(),
                eventParamDto.getStartTime().intValue(),
                eventParamDto.getTimezone()));
        newEvent.setEnd(SyncCalendarUtil.getEventDateTimeFromAppointmentDate(
                eventParamDto.getAppointmentDate(),
                eventParamDto.getEndTime().intValue(),
                eventParamDto.getTimezone()));
        newEvent.setSummary(eventParamDto.getSummary());
        // customer address
        if (!StringUtils.isEmpty(eventParamDto.getFullAddress())) {
            newEvent.setLocation(eventParamDto.getFullAddress());
        }
        // customer email
        // pet detail info
        if (!StringUtils.isEmpty(eventParamDto.getDescription())) {
            newEvent.setDescription(eventParamDto.getDescription());
        }
        //        if (eventParamDto.getIsBlock() != null && eventParamDto.getIsBlock()) {
        //            //https://developers.google.com/calendar/api/v3/reference/colors/get
        //            //https://developers.google.com/calendar/api/v3/reference/colors#resource
        //            //block颜色：E1E1E1E1——8号色
        //            newEvent.setColorId(String.valueOf(8));
        //        } else {
        //            //appt选色：ffb878——6号色
        //            newEvent.setColorId(String.valueOf(6));
        //        }
        newEvent.setReminders(new Event.Reminders().setUseDefault(false));
        newEvent.setGuestsCanModify(false);
        return newEvent;
    }
}
