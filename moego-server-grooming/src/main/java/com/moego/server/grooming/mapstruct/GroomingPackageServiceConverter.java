package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.GroomingPackageServiceDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageService;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/10/25
 */
@Mapper
public abstract class GroomingPackageServiceConverter {

    public static final GroomingPackageServiceConverter INSTANCE =
            Mappers.getMapper(GroomingPackageServiceConverter.class);

    @Mapping(target = "serviceName", ignore = true)
    public abstract GroomingPackageServiceDTO entityToDTO(MoeGroomingPackageService entity);

    public abstract MoeGroomingPackageService entityToEntity(MoeGroomingPackageService entity);
}
