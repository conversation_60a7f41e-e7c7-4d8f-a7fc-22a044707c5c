package com.moego.server.grooming.service.utils;

import static com.moego.common.enums.groomingreport.GroomingReportConst.SEND_BY_EMAIL;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SEND_BY_SMS;

import com.moego.common.enums.BusinessCustomerConst;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.PetTypeEnum;
import com.moego.common.utils.DateUtil;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
public class GroomingReportUtil {

    // SENDING METHOD 是按位存储，第1位是 sms, 第2位是 email, 后续有扩展则往左进位延伸
    /**
     * 前端传的 methodList 按位转为 byte 值存储
     *
     * @param methodList
     * @return
     */
    public static Byte getSendingMethodForDB(List<Byte> methodList) {
        byte originByte = 0;
        for (Byte method : methodList) {
            switch (method) {
                case SEND_BY_EMAIL -> originByte = (byte) (originByte | SEND_BY_EMAIL);
                case SEND_BY_SMS -> originByte = (byte) (originByte | SEND_BY_SMS);
                default -> log.warn("unknown method type: " + method);
            }
        }
        return originByte;
    }

    /**
     * 数据库存的 byte 值转为 method list
     *
     * @param method
     * @return
     */
    public static List<Byte> getSendingMethodListFromDB(Byte method) {
        List<Byte> methodList = new ArrayList<>();
        if (method == null) {
            return methodList;
        }
        if ((byte) (method & SEND_BY_EMAIL) == SEND_BY_EMAIL) {
            methodList.add(SEND_BY_EMAIL);
        }
        if ((byte) (method & SEND_BY_SMS) == SEND_BY_SMS) {
            methodList.add(SEND_BY_SMS);
        }
        return methodList;
    }

    public static Date toDate(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return new Date(timestamp * 1000);
    }

    public static Long toTimestamp(Date date) {
        if (date == null) {
            return null;
        }
        return date.getTime() / 1000;
    }

    public static String getDaysAfter(String date, Integer afterDays) {
        if (date == null) {
            return null;
        }
        try {
            return DateUtil.daysBeforeAfter(date, afterDays);
        } catch (ParseException e) {
            log.error("Parse date {} exception happened", date, e);
            return null;
        }
    }

    public static String getGenderText(Integer gender) {
        if (Objects.equals(CustomerPetEnum.GENDER_MALE.intValue(), gender)) {
            return "Male";
        } else if (Objects.equals(CustomerPetEnum.GENDER_FEMALE.intValue(), gender)) {
            return "Female";
        } else {
            return null;
        }
    }

    public static String getWeightText(String weight, String unitOfWeight) {
        return StringUtils.hasText(weight) ? weight + " " + unitOfWeight : null;
    }

    public static String getFrequencyText(Integer frequencyDays, Byte frequencyType) {
        if (frequencyDays == null) {
            return "Every 4 weeks";
        }
        String frequencyText;
        int frequency = frequencyDays;
        switch (frequencyType) {
            case BusinessCustomerConst.FREQUENCY_TYPE_BY_DAY -> frequencyText =
                    String.format("Every %s day", frequency);
            case BusinessCustomerConst.FREQUENCY_TYPE_BY_MONTH -> {
                frequency = frequencyDays / 30;
                frequencyText = String.format("Every %s month", frequency);
            }
            default -> {
                frequency = frequencyDays / 7;
                frequencyText = String.format("Every %s week", frequency);
            }
        }
        if (!StringUtils.hasText(frequencyText)) {
            return "Every 4 weeks";
        }
        // 复数额外处理
        if (frequency > 1) {
            frequencyText = frequencyText + "s";
        }
        return frequencyText;
    }

    public static boolean isDogOrCatType(Integer petTypeId) {
        return PetTypeEnum.DOG.getType().equals(petTypeId)
                || PetTypeEnum.CAT.getType().equals(petTypeId);
    }
}
