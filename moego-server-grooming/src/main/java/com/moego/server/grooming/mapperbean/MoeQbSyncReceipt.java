package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_sync_receipt
 */
public class MoeQbSyncReceipt {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   ????id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.connect_id
     *
     * @mbg.generated
     */
    private Integer connectId;

    /**
     * Database Column Remarks:
     *   realmId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.realm_id
     *
     * @mbg.generated
     */
    private String realmId;

    /**
     * Database Column Remarks:
     *   ?? payment detail ?id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.payment_detail_id
     *
     * @mbg.generated
     */
    private Integer paymentDetailId;

    /**
     * Database Column Remarks:
     *   quickbookds sales receipt id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.qb_receipt_id
     *
     * @mbg.generated
     */
    private String qbReceiptId;

    /**
     * Database Column Remarks:
     *   qb sales receipt ?? 1 ??  2???
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.qb_receipt_status
     *
     * @mbg.generated
     */
    private Byte qbReceiptStatus;

    /**
     * Database Column Remarks:
     *   ???
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.amount
     *
     * @mbg.generated
     */
    private BigDecimal amount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   receipt type, 1-sales receipt, 2-refund receipt, 3-credit memo
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_receipt.receipt_type
     *
     * @mbg.generated
     */
    private Byte receiptType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.id
     *
     * @return the value of moe_qb_sync_receipt.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.id
     *
     * @param id the value for moe_qb_sync_receipt.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.business_id
     *
     * @return the value of moe_qb_sync_receipt.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.business_id
     *
     * @param businessId the value for moe_qb_sync_receipt.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.company_id
     *
     * @return the value of moe_qb_sync_receipt.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.company_id
     *
     * @param companyId the value for moe_qb_sync_receipt.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.connect_id
     *
     * @return the value of moe_qb_sync_receipt.connect_id
     *
     * @mbg.generated
     */
    public Integer getConnectId() {
        return connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.connect_id
     *
     * @param connectId the value for moe_qb_sync_receipt.connect_id
     *
     * @mbg.generated
     */
    public void setConnectId(Integer connectId) {
        this.connectId = connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.realm_id
     *
     * @return the value of moe_qb_sync_receipt.realm_id
     *
     * @mbg.generated
     */
    public String getRealmId() {
        return realmId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.realm_id
     *
     * @param realmId the value for moe_qb_sync_receipt.realm_id
     *
     * @mbg.generated
     */
    public void setRealmId(String realmId) {
        this.realmId = realmId == null ? null : realmId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.payment_detail_id
     *
     * @return the value of moe_qb_sync_receipt.payment_detail_id
     *
     * @mbg.generated
     */
    public Integer getPaymentDetailId() {
        return paymentDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.payment_detail_id
     *
     * @param paymentDetailId the value for moe_qb_sync_receipt.payment_detail_id
     *
     * @mbg.generated
     */
    public void setPaymentDetailId(Integer paymentDetailId) {
        this.paymentDetailId = paymentDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.qb_receipt_id
     *
     * @return the value of moe_qb_sync_receipt.qb_receipt_id
     *
     * @mbg.generated
     */
    public String getQbReceiptId() {
        return qbReceiptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.qb_receipt_id
     *
     * @param qbReceiptId the value for moe_qb_sync_receipt.qb_receipt_id
     *
     * @mbg.generated
     */
    public void setQbReceiptId(String qbReceiptId) {
        this.qbReceiptId = qbReceiptId == null ? null : qbReceiptId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.qb_receipt_status
     *
     * @return the value of moe_qb_sync_receipt.qb_receipt_status
     *
     * @mbg.generated
     */
    public Byte getQbReceiptStatus() {
        return qbReceiptStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.qb_receipt_status
     *
     * @param qbReceiptStatus the value for moe_qb_sync_receipt.qb_receipt_status
     *
     * @mbg.generated
     */
    public void setQbReceiptStatus(Byte qbReceiptStatus) {
        this.qbReceiptStatus = qbReceiptStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.amount
     *
     * @return the value of moe_qb_sync_receipt.amount
     *
     * @mbg.generated
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.amount
     *
     * @param amount the value for moe_qb_sync_receipt.amount
     *
     * @mbg.generated
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.update_time
     *
     * @return the value of moe_qb_sync_receipt.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.update_time
     *
     * @param updateTime the value for moe_qb_sync_receipt.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.create_time
     *
     * @return the value of moe_qb_sync_receipt.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.create_time
     *
     * @param createTime the value for moe_qb_sync_receipt.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_receipt.receipt_type
     *
     * @return the value of moe_qb_sync_receipt.receipt_type
     *
     * @mbg.generated
     */
    public Byte getReceiptType() {
        return receiptType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_receipt.receipt_type
     *
     * @param receiptType the value for moe_qb_sync_receipt.receipt_type
     *
     * @mbg.generated
     */
    public void setReceiptType(Byte receiptType) {
        this.receiptType = receiptType;
    }
}
