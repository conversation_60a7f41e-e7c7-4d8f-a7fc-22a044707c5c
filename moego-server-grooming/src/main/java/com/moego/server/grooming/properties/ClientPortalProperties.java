package com.moego.server.grooming.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/11/7
 */
@Data
@Configuration(proxyBeanMethods = false)
@ConfigurationProperties(prefix = "client-portal")
public class ClientPortalProperties {

    private int corePoolSize;

    private int maximumPoolSize;

    private int aliveTimeSeconds;

    private int queueCapacity;

    private int notificationAheadMinutes;
}
