package com.moego.server.grooming.service.report;

import static java.math.RoundingMode.HALF_UP;

import com.moego.common.enums.PayrollConst;
import com.moego.common.utils.AmountUtils;
import com.moego.server.business.client.IPayrollCalculationClient;
import com.moego.server.business.client.IPayrollSettingClient;
import com.moego.server.business.dto.BusinessPayrollSettingDTO;
import com.moego.server.business.dto.PayrollExceptionDTO;
import com.moego.server.business.dto.StaffPayrollCalculationDTO;
import com.moego.server.business.dto.StaffPayrollSettingDTO;
import com.moego.server.grooming.service.dto.report.StaffRevenueDetail;
import com.moego.server.grooming.service.utils.ReportBeanUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class PayrollCalculateService {

    private final IPayrollSettingClient iPayrollSettingClient;
    private final IPayrollCalculationClient iPayrollCalculationClient;

    /**
     * Service Commission 计算
     * 1.service commission 关闭：赋默认值
     * 2.service commission 开启：
     * 2.1 判断是否有命中 staff 的 exception 规则的服务，有则计算这部分 commission
     * 2.2 根据 service commission type 计算其它服务的 commission
     * 2.2.1 by fixed rate: 实收金额 * rate
     * 2.2.2 by tier rate:
     * Sliding scale（浮动费率制，以最终落在区间的费率计算) 实收金额 * tierRate
     * Progressive（阶梯累加制，每个区间的金额以各自费率计算最终累加) tier1End金额 * tier1Rate + (tier2End - tier1End) * tier2Rate + ...
     *
     * @param businessId      businessId
     * @param employeeReports employee report 对象列表
     */
    public void processStaffCommission(Integer businessId, List<StaffRevenueDetail> employeeReports) {
        // 查询 staff 信息
        List<Integer> staffIds = employeeReports.stream()
                .map(StaffRevenueDetail::getStaffId)
                .distinct()
                .toList();
        // 查询新 Payroll 设置
        BusinessPayrollSettingDTO businessPayrollSetting = iPayrollSettingClient.getBusinessPayrollSetting(businessId);
        boolean isBasedOnCollected = Objects.equals(
                businessPayrollSetting.getServiceCommissionBased(), PayrollConst.COMMISSION_BASED_ACTUAL_PAYMENT);

        Map<Integer, StaffPayrollSettingDTO> staffPayrollSettingMap =
                iPayrollSettingClient
                        .getStaffPayrollSettingListByStaffIds(businessPayrollSetting.getCompanyId(), staffIds)
                        .stream()
                        .collect(Collectors.toMap(
                                StaffPayrollSettingDTO::getStaffId, Function.identity(), (s1, s2) -> s2));
        List<PayrollExceptionDTO> payrollExceptionList = iPayrollSettingClient.getPayrollExceptionList(businessId);

        for (StaffRevenueDetail employee : employeeReports) {
            StaffPayrollSettingDTO staffSetting = staffPayrollSettingMap.get(employee.getStaffId());
            if (Objects.isNull(staffSetting)) {
                continue;
            }
            // 初始化 commission 字段
            ReportBeanUtil.initEmployeeReportCommissionAmount(employee);
            // 计算 service exception、commission, service commission 开关打开时计算
            if (Boolean.TRUE.equals(staffSetting.getServiceCommissionEnable())) {
                calculateServiceExceptions(employee, payrollExceptionList, isBasedOnCollected);
                calculateStaffServiceCommission(employee, staffSetting, isBasedOnCollected);
            }
            // 计算 tips commission, tips commission 开关打开时计算
            boolean tipsCommissionEnable = Boolean.TRUE.equals(staffSetting.getTipsCommissionEnable());
            if (tipsCommissionEnable) {
                calculateTipsCommission(employee, staffSetting.getTipsPayRate(), isBasedOnCollected);
            }

            employee.setTotalCommission(AmountUtils.sum(
                    employee.getServiceCommission(), employee.getAddonCommission(), employee.getTipsCommission()));
        }
    }

    /**
     * 计算 staff service commission
     *
     * @param employee       employee report 对象，前面已计算各种金额
     * @param payrollSetting staff payroll setting
     */
    public void calculateStaffServiceCommission(
            StaffRevenueDetail employee, StaffPayrollSettingDTO payrollSetting, boolean isBasedOnCollected) {
        // 计算 service、add-on commission
        BigDecimal curServiceCommission = calculateServiceCommission(
                isBasedOnCollected ? employee.getCollectedServicePrice() : employee.getFinishExpectedServicePrice(),
                employee.getExceptionServiceBase(),
                payrollSetting,
                false);

        BigDecimal curAddonCommission = calculateServiceCommission(
                isBasedOnCollected ? employee.getCollectedAddonPrice() : employee.getFinishExpectedAddonPrice(),
                employee.getExceptionAddonBase(),
                payrollSetting,
                true);

        employee.setServiceCommission(AmountUtils.sum(employee.getServiceCommission(), curServiceCommission));
        employee.setAddonCommission(AmountUtils.sum(employee.getAddonCommission(), curAddonCommission));
    }

    /**
     * 根据 service commission 类型计算 commission
     *
     * @param calculationBase 总的计算基数
     * @param exceptionBase   已经计算的 exception 基数
     * @param payrollSetting  staff payroll setting
     * @return 返回 Commission 计算结果
     */
    public BigDecimal calculateServiceCommission(
            BigDecimal calculationBase,
            BigDecimal exceptionBase,
            StaffPayrollSettingDTO payrollSetting,
            boolean isAddon) {
        if (Objects.isNull(calculationBase) || Objects.isNull(payrollSetting)) {
            return BigDecimal.ZERO;
        }
        // 减掉 Exception 的部分
        if (Objects.nonNull(exceptionBase)) {
            calculationBase = calculationBase.subtract(exceptionBase);
        }

        Byte commissionType = payrollSetting.getServiceCommissionType();
        if (Objects.equals(commissionType, PayrollConst.SERVICE_COMMISSION_TYPE_FIXED_RATE)) {
            BigDecimal payRate = isAddon ? payrollSetting.getAddonPayRate() : payrollSetting.getServicePayRate();
            return calculateCommissionByRate(calculationBase, payRate);
        } else if (Objects.equals(commissionType, PayrollConst.SERVICE_COMMISSION_TYPE_TIER_RATE)) {
            StaffPayrollCalculationDTO calculationDTO = new StaffPayrollCalculationDTO();
            calculationDTO.setCollectedPrice(calculationBase);
            calculationDTO.setStaffPayrollSettingDTO(payrollSetting);
            return iPayrollCalculationClient.calculateCommissionByTierRate(calculationDTO);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 根据 实收金额 和 rate 计算 commission
     *
     * @param collectedPrice 实收金额
     * @param payRate        payRate
     * @return
     */
    public BigDecimal calculateCommissionByRate(BigDecimal collectedPrice, BigDecimal payRate) {
        if (payRate == null) {
            payRate = BigDecimal.ZERO;
        }
        if (collectedPrice == null) {
            collectedPrice = BigDecimal.ZERO;
        }
        return collectedPrice.multiply(payRate.scaleByPowerOfTen(-2)).setScale(2, HALF_UP);
    }

    /**
     * 计算 payroll exception commission
     *
     * @param employeeReport       employee report
     * @param payrollExceptionList payroll exception 配置列表
     * @param isBasedOnCollected   是否基于实收金额计算，目前仅有两种：1.总实收金额，2.finish 预约的应收金额
     */
    public void calculateServiceExceptions(
            StaffRevenueDetail employeeReport,
            List<PayrollExceptionDTO> payrollExceptionList,
            boolean isBasedOnCollected) {
        if (CollectionUtils.isEmpty(payrollExceptionList)) {
            return;
        }
        Map<Integer, BigDecimal> servicePayrollBaseMap =
                isBasedOnCollected ? employeeReport.getServiceCollectedMap() : employeeReport.getServiceExpectedMap();
        Map<Integer, BigDecimal> addonPayrollBaseMap =
                isBasedOnCollected ? employeeReport.getAddonCollectedMap() : employeeReport.getAddonExpectedMap();

        for (PayrollExceptionDTO payrollException : payrollExceptionList) {
            // 判断当前 staff 是否符合规则，如符合，则计算对应的 commission
            boolean matchException = payrollException.getIsAllStaff()
                    || payrollException.getStaffIdList().contains(employeeReport.getStaffId());
            if (!matchException) {
                continue;
            }

            // servicePayrollBaseMap 有 exception 指定的 service 时, 则计算 serviceException
            if (!CollectionUtils.isEmpty(servicePayrollBaseMap)
                    && servicePayrollBaseMap.containsKey(payrollException.getServiceId())) {
                BigDecimal curCalculationBase = servicePayrollBaseMap.get(payrollException.getServiceId());
                calculateServiceException(employeeReport, payrollException, curCalculationBase, false);
            }

            // addonPayrollBaseMap 有 exception 指定的 service 时, 则计算 serviceException
            if (!CollectionUtils.isEmpty(addonPayrollBaseMap)
                    && addonPayrollBaseMap.containsKey(payrollException.getServiceId())) {
                BigDecimal curCalculationBase = addonPayrollBaseMap.get(payrollException.getServiceId());
                calculateServiceException(employeeReport, payrollException, curCalculationBase, true);
            }
        }
    }

    /**
     * 计算单个 payroll exception
     *
     * @param employeeReport     employee report
     * @param payrollException   exception 设置
     * @param curCalculationBase 当前 payroll exception service 的计算基数
     * @param isAddon            是否是 add-on
     */
    private void calculateServiceException(
            StaffRevenueDetail employeeReport,
            PayrollExceptionDTO payrollException,
            BigDecimal curCalculationBase,
            boolean isAddon) {
        BigDecimal curCommission = calculateCommissionByRate(curCalculationBase, payrollException.getRate());

        if (isAddon) {
            employeeReport.setExceptionAddonBase(
                    AmountUtils.sum(employeeReport.getExceptionAddonBase(), curCalculationBase));
            employeeReport.setAddonCommission(AmountUtils.sum(employeeReport.getAddonCommission(), curCommission));
        } else {
            employeeReport.setExceptionServiceBase(
                    AmountUtils.sum(employeeReport.getExceptionServiceBase(), curCalculationBase));
            employeeReport.setServiceCommission(AmountUtils.sum(employeeReport.getServiceCommission(), curCommission));
        }
    }

    /**
     * 计算 tips commission
     *
     * @param employeeReport     employee report
     * @param tipsPayRate        tips pay rate
     * @param isBasedOnCollected 是否基于实收金额计算，目前仅有两种：1.总实收金额，2.finish 预约的应收金额
     */
    public void calculateTipsCommission(
            StaffRevenueDetail employeeReport, BigDecimal tipsPayRate, boolean isBasedOnCollected) {
        // 根据 isBasedOnCollected 取计算基数
        BigDecimal tipsCommissionBase =
                isBasedOnCollected ? employeeReport.getCollectedTips() : employeeReport.getFinishExpectedTips();

        BigDecimal curTipsCommission = BigDecimal.ZERO;
        if (Objects.nonNull(tipsCommissionBase)) {
            curTipsCommission = calculateCommissionByRate(tipsCommissionBase, tipsPayRate);
        }
        employeeReport.setTipsCommission(AmountUtils.sum(employeeReport.getTipsCommission(), curTipsCommission));
    }
}
