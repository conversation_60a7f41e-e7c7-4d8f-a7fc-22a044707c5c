package com.moego.server.grooming.service;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ScopeModifyTypeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.models.appointment.v1.AppointmentTracking;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.ValueModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.service.appointment.v1.AppointmentTrackingServiceGrpc;
import com.moego.idl.service.appointment.v1.ListAppointmentTrackingRequest;
import com.moego.idl.service.metadata.v1.DescribeValuesRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.client.ICustomerGroomingClient;
import com.moego.server.customer.client.IPetCodeClient;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.customer.dto.GroomingQueryDto;
import com.moego.server.customer.dto.MoePetCodeInfoDTO;
import com.moego.server.customer.params.GroomingCustomerInfoParams;
import com.moego.server.grooming.convert.AppointmentConverter;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerLastFinishedApptMapDto;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingPetInfoDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceDTO;
import com.moego.server.grooming.dto.GroomingPetServiceListInfoDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.PetCodeInfoDTO;
import com.moego.server.grooming.dto.PetDetailServiceDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.dto.appointment.history.CancelLogDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.PetDetailStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.listener.event.UpdateCustomerEvent;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.AppointmentPetFeedingMapper;
import com.moego.server.grooming.mapper.AppointmentPetMedicationMapper;
import com.moego.server.grooming.mapper.AppointmentPetScheduleSettingMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapper.po.GroomingStaffIdListPO;
import com.moego.server.grooming.mapperbean.AppointmentPetFeeding;
import com.moego.server.grooming.mapperbean.AppointmentPetMedication;
import com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting;
import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.mapstruct.AutoAssignConverter;
import com.moego.server.grooming.params.CancelParams;
import com.moego.server.grooming.params.CustomerServiceParams;
import com.moego.server.grooming.params.EditPetDetailParams;
import com.moego.server.grooming.params.EditPetDetailStaffCommissionParam;
import com.moego.server.grooming.params.PetDetailParams;
import com.moego.server.grooming.params.PetPassAwayParams;
import com.moego.server.grooming.params.appointment.PetParams;
import com.moego.server.grooming.params.appointment.ServiceAndOperationParams;
import com.moego.server.grooming.service.GroomingServiceOperationService.OperationBasic;
import com.moego.server.grooming.service.dto.PetDetailWithExtraDTO;
import com.moego.server.grooming.service.ob.OBAddressService;
import com.moego.server.grooming.utils.PetDetailUtil;
import com.moego.server.grooming.web.vo.PetDetailMonthlyQueryVo;
import com.moego.server.grooming.web.vo.PetDetailQueryVo;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.params.notification.NotificationApptRescheduledParams;
import com.moego.server.payment.client.IPaymentPreAuthClient;
import com.moego.server.payment.dto.PreAuthDTO;
import com.moego.server.payment.params.BatchQueryPreAuthParams;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class MoePetDetailService {

    public static final int PARALLEL_WORK_MODE = 0;

    public static final int SEQUENCE_WORK_MODE = 1;

    private static final String ALLOW_BOARDING_AND_DAYCARE_KEY = "allow_boarding_and_daycare_";

    @Autowired
    private PetDetailMapperProxy moeGroomingPetDetailMapper;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private INotificationClient iNotificationClient;

    @Autowired
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Autowired
    private MoeGroomingNoteService moeGroomingNoteService;

    @Autowired
    private QuickBooksService quickBooksService;

    @Autowired
    private CalendarSyncService calendarSyncService;

    @Autowired
    private ICustomerGroomingClient iCustomerGroomingClient;

    @Autowired
    private IPetCodeClient iPetCodeClient;

    @Autowired
    private MoeBookOnlineDepositService obDepositService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private PetDetailMapperProxy petDetailMapper;

    @Autowired
    private AppointmentPetFeedingMapper petFeedingMapper;

    @Autowired
    private AppointmentPetMedicationMapper petMedicationMapper;

    @Autowired
    private AppointmentPetScheduleSettingMapper petScheduleSettingMapper;

    @Autowired
    private MoeGroomingCustomerServicesService customerService;

    @Autowired
    private GroomingServiceOperationService groomingServiceOperationService;

    @Autowired
    private IPaymentPreAuthClient preAuthClient;

    @Autowired
    private MoeAppointmentQueryService appointmentQueryService;

    @Autowired
    private IBusinessServiceAreaClient iBusinessServiceAreaClient;

    @Autowired
    private AutoAssignService autoAssignService;

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private WaitListService waitListService;

    @Autowired
    private OBAddressService obAddressService;

    @Autowired
    private IPetService iPetServiceClient;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;

    @Autowired
    private StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    @Autowired
    AppointmentServiceDetailService appointmentServiceDetailService;

    @Autowired
    private MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SmartScheduleService smartScheduleService;

    @Autowired
    private AppointmentTrackingServiceGrpc.AppointmentTrackingServiceBlockingStub appointTrackingServiceBlockingStub;

    @Autowired
    private NewOrderHelper newOrderHelper;

    @Transactional
    public void addMoePetDetails(MoeGroomingAppointment moeGroomingAppointment, List<PetDetailParams> petServices) {
        Integer starStaff = petServices.stream()
                .filter(petDetailParams -> Objects.nonNull(petDetailParams.getStar()) && petDetailParams.getStar())
                .findFirst()
                .map(PetDetailParams::getStaffId)
                .orElse(0);

        List<CustomerServiceParams> customerServices = new ArrayList<>();
        // 判断是否save price
        Map<Integer, List<Integer>> petServiceMap = petServices.stream()
                .filter(param -> Objects.equals(param.getServiceType(), ServiceType.SERVICE_VALUE))
                .collect(Collectors.groupingBy(
                        PetDetailParams::getPetId,
                        Collectors.mapping(PetDetailParams::getServiceId, Collectors.toList())));
        var petDetailWithExtras = petServices.stream()
                .map(petDetailParams -> {
                    MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
                    BeanUtils.copyProperties(petDetailParams, moeGroomingPetDetail);
                    if (petDetailParams.getStartTime() != null) {
                        moeGroomingPetDetail.setStartDate(moeGroomingAppointment.getAppointmentDate());
                        moeGroomingPetDetail.setEndDate(moeGroomingAppointment.getAppointmentEndDate());
                        moeGroomingPetDetail.setStartTime(Long.valueOf(petDetailParams.getStartTime()));
                    }
                    if (petDetailParams.getEndTime() != null) {
                        moeGroomingPetDetail.setEndTime(Long.valueOf(petDetailParams.getEndTime()));
                    } else if (petDetailParams.getStartTime() != null) {
                        moeGroomingPetDetail.setEndTime(
                                (long) petDetailParams.getStartTime() + moeGroomingPetDetail.getServiceTime());
                    }
                    moeGroomingPetDetail.setStarStaffId(starStaff);
                    moeGroomingPetDetail.setGroomingId(moeGroomingAppointment.getId());
                    moeGroomingPetDetail.setUpdateTime(CommonUtil.get10Timestamp());
                    moeGroomingPetDetail.setServiceItemType(
                            petDetailParams.getServiceItemEnum() != null
                                    ? petDetailParams.getServiceItemEnum().getServiceItem()
                                    : null);
                    // add-on 关联主 service
                    if (Objects.equals(petDetailParams.getServiceType(), ServiceType.ADDON_VALUE)
                            && petServiceMap.containsKey(petDetailParams.getPetId())) {
                        List<Integer> services = petServiceMap.get(petDetailParams.getPetId());
                        moeGroomingPetDetail.setAssociatedServiceId(Long.valueOf(services.get(0)));
                    }
                    if (petDetailParams.getQuantityPerDay() == null) {
                        moeGroomingPetDetail.setQuantityPerDay(1);
                    }
                    if (shouldSaveCustomService(petDetailParams.getScopeTypePrice())) {
                        customerServices.add(getCustomerServicePriceParams(moeGroomingAppointment, petDetailParams));
                        moeGroomingPetDetail.setScopeTypePrice(ScopeModifyTypeEnum.THIS_FUTURE.getScopeType());
                    }
                    if (shouldSaveCustomService(petDetailParams.getScopeTypeTime())) {
                        customerServices.add(getCustomerServiceTimeParams(moeGroomingAppointment, petDetailParams));
                        moeGroomingPetDetail.setScopeTypeTime(ScopeModifyTypeEnum.THIS_FUTURE.getScopeType());
                    }

                    if (Boolean.TRUE.equals(moeGroomingPetDetail.getEnableOperation())
                            && Objects.isNull(moeGroomingPetDetail.getWorkMode())) {
                        boolean parallelWorkMode = petDetailParams.getOperationList().stream()
                                        .map(GroomingServiceOperationDTO::getStartTime)
                                        .distinct()
                                        .count()
                                == 1;
                        moeGroomingPetDetail.setWorkMode(parallelWorkMode ? PARALLEL_WORK_MODE : SEQUENCE_WORK_MODE);
                    }
                    return PetDetailWithExtraDTO.builder()
                            .petDetail(moeGroomingPetDetail)
                            .feedings(getFeedingSchedules(
                                    petDetailParams.getFeedings(),
                                    moeGroomingAppointment,
                                    petDetailParams.getPetId().longValue()))
                            .medications(getMedicationSchedules(
                                    petDetailParams.getMedications(),
                                    moeGroomingAppointment,
                                    petDetailParams.getPetId().longValue()))
                            .build();
                })
                .toList();
        // DONE new order flow 计算 grooming/daycare 的 quantity 和 total_price
        if (newOrderHelper.enableNewOrder(moeGroomingAppointment.getCompanyId())) {
            var petDetails = petDetailWithExtras.stream()
                    .map(PetDetailWithExtraDTO::getPetDetail)
                    .toList();
            var petIdToServiceIdToDetail = PetDetailUtil.getPetServiceMap(petDetails);
            petDetails.forEach(petDetail -> {
                // 计算 quantity 依赖该字段，默认都是按次计费
                petDetail.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
                var quantity = PetDetailUtil.getQuantity(petDetail, petIdToServiceIdToDetail);
                petDetail.setTotalPrice(petDetail.getServicePrice().multiply(BigDecimal.valueOf(quantity)));
                petDetail.setQuantity(quantity);
            });
        }

        petDetailWithExtras.forEach(petDetailWithExtraDTO -> {
            var petDetail = petDetailWithExtraDTO.getPetDetail();
            if (!isNormal(petDetail.getStaffId())) {
                petDetail.setStaffId(0);
            }
            petDetailMapper.insertSelective(petDetail);
            petDetailWithExtraDTO.getFeedings().forEach(feedingSchedule -> {
                var feeding = feedingSchedule.getFeeding();
                feeding.setPetDetailId(petDetail.getId().longValue());
                petFeedingMapper.insertSelective(feeding);
                feedingSchedule.getSchedules().forEach(schedule -> {
                    schedule.setScheduleId(feeding.getId());
                    petScheduleSettingMapper.insertSelective(schedule);
                });
            });
            petDetailWithExtraDTO.getMedications().forEach(medicationSchedule -> {
                var medication = medicationSchedule.getMedication();
                medication.setPetDetailId(petDetail.getId().longValue());
                petMedicationMapper.insertSelective(medication);
                medicationSchedule.getSchedules().forEach(schedule -> {
                    schedule.setScheduleId(medication.getId());
                    petScheduleSettingMapper.insertSelective(schedule);
                });
            });
        });

        var petDetails = petDetailWithExtras.stream()
                .map(PetDetailWithExtraDTO::getPetDetail)
                .toList();
        addOperation(moeGroomingAppointment, petServices, petDetails);

        // 判断是否save price，更新到customer service
        if (!CollectionUtils.isEmpty(customerServices)) {
            customerService.addCustomerServices(customerServices);
        }
    }

    private static List<PetDetailWithExtraDTO.FeedingScheduleDTO> getFeedingSchedules(
            List<PetDetailParams.FeedingParams> feedingParams, MoeGroomingAppointment appointment, Long petId) {
        if (CollectionUtils.isEmpty(feedingParams)) {
            return List.of();
        }
        return feedingParams.stream()
                .map(param -> PetDetailWithExtraDTO.FeedingScheduleDTO.builder()
                        .feeding(getAppointmentPetFeeding(appointment, petId, param))
                        .schedules(param.getFeedingTimes().stream()
                                .map(time -> getAppointmentPetScheduleSetting(
                                        time, appointment, BusinessPetScheduleType.FEEDING))
                                .toList())
                        .build())
                .toList();
    }

    @NotNull
    private static AppointmentPetFeeding getAppointmentPetFeeding(
            MoeGroomingAppointment appointment, Long petId, PetDetailParams.FeedingParams param) {
        var feeding = new AppointmentPetFeeding();
        feeding.setCompanyId(appointment.getCompanyId());
        feeding.setAppointmentId(appointment.getId().longValue());
        feeding.setPetId(petId);
        feeding.setFeedingAmount(param.getFeedingAmount());
        feeding.setFeedingUnit(param.getFeedingUnit());
        feeding.setFeedingType(param.getFeedingType());
        feeding.setFeedingSource(param.getFeedingSource());
        feeding.setFeedingInstruction(param.getFeedingInstruction());
        feeding.setFeedingNote(param.getFeedingNote());
        return feeding;
    }

    @NotNull
    private static AppointmentPetScheduleSetting getAppointmentPetScheduleSetting(
            PetDetailParams.ScheduleTimeParams scheduleTimeParams,
            MoeGroomingAppointment appointment,
            BusinessPetScheduleType scheduleType) {
        var schedule = new AppointmentPetScheduleSetting();
        schedule.setCompanyId(appointment.getCompanyId());
        schedule.setAppointmentId(appointment.getId().longValue());
        schedule.setScheduleType(scheduleType.getNumber());
        schedule.setScheduleTime(scheduleTimeParams.getScheduleTime());
        schedule.setScheduleExtraJson(JsonUtil.toJson(scheduleTimeParams.getExtraJson()));
        return schedule;
    }

    private static List<PetDetailWithExtraDTO.MedicationScheduleDTO> getMedicationSchedules(
            List<PetDetailParams.MedicationParams> medicationParams, MoeGroomingAppointment appointment, Long petId) {
        if (CollectionUtils.isEmpty(medicationParams)) {
            return List.of();
        }
        return medicationParams.stream()
                .map(param -> PetDetailWithExtraDTO.MedicationScheduleDTO.builder()
                        .medication(getAppointmentPetMedication(appointment, petId, param))
                        .schedules(param.getMedicationTimes().stream()
                                .map(time -> getAppointmentPetScheduleSetting(
                                        time, appointment, BusinessPetScheduleType.MEDICATION))
                                .toList())
                        .build())
                .toList();
    }

    @NotNull
    private static AppointmentPetMedication getAppointmentPetMedication(
            MoeGroomingAppointment appointment, Long petId, PetDetailParams.MedicationParams param) {
        var medication = new AppointmentPetMedication();
        medication.setCompanyId(appointment.getCompanyId());
        medication.setAppointmentId(appointment.getId().longValue());
        medication.setPetId(petId);
        medication.setMedicationAmount(param.getMedicationAmount());
        medication.setMedicationUnit(param.getMedicationUnit());
        medication.setMedicationName(param.getMedicationName());
        medication.setMedicationNote(param.getMedicationNote());
        return medication;
    }

    public void addPetDetails(MoeGroomingAppointment appointment, List<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return;
        }
        // 外层已经 set 了 start date，这里 waitlist 类型 appt 没有 start date 值
        petDetails.forEach(petDetail -> petDetail.setGroomingId(appointment.getId()));
        moeGroomingPetDetailMapper.insertSelectiveBatch(petDetails);
    }

    private static CustomerServiceParams getCustomerServiceTimeParams(
            MoeGroomingAppointment moeGroomingAppointment, PetDetailParams petDetailParams) {
        CustomerServiceParams customerServiceParams = new CustomerServiceParams();

        customerServiceParams.setBusinessId(moeGroomingAppointment.getBusinessId());
        customerServiceParams.setCompanyId(moeGroomingAppointment.getCompanyId());
        customerServiceParams.setCreateBy(moeGroomingAppointment.getCreatedById());
        customerServiceParams.setCustomerId(moeGroomingAppointment.getCustomerId());

        customerServiceParams.setPetId(petDetailParams.getPetId());
        customerServiceParams.setServiceId(petDetailParams.getServiceId());
        customerServiceParams.setServiceType(petDetailParams.getServiceType());

        customerServiceParams.setServiceTime(petDetailParams.getServiceTime());
        customerServiceParams.setSaveType(CustomerPetEnum.SERVICE_SAVE_TYPE_TIME);
        return customerServiceParams;
    }

    private static CustomerServiceParams getCustomerServicePriceParams(
            MoeGroomingAppointment moeGroomingAppointment, PetDetailParams petDetailParams) {
        CustomerServiceParams customerServiceParams = new CustomerServiceParams();

        customerServiceParams.setBusinessId(moeGroomingAppointment.getBusinessId());
        customerServiceParams.setCompanyId(moeGroomingAppointment.getCompanyId());
        customerServiceParams.setCreateBy(moeGroomingAppointment.getCreatedById());
        customerServiceParams.setCustomerId(moeGroomingAppointment.getCustomerId());

        customerServiceParams.setPetId(petDetailParams.getPetId());
        customerServiceParams.setServiceId(petDetailParams.getServiceId());
        customerServiceParams.setServiceType(petDetailParams.getServiceType());

        customerServiceParams.setServiceFee(petDetailParams.getServicePrice());
        customerServiceParams.setSaveType(CustomerPetEnum.SERVICE_SAVE_TYPE_PRICE);
        return customerServiceParams;
    }

    public static boolean shouldSaveCustomService(Integer scopeType) {
        return (scopeType != null
                && scopeType != 0
                && !Objects.equals(ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType(), scopeType));
    }

    public void deleteByAppointId(Integer appointmentId) {
        moeGroomingPetDetailMapper.deleteByAppointmentId(appointmentId);
        groomingServiceOperationService.deleteOperationByGroomingId(appointmentId);
    }

    public void deleteByAppointIds(List<Integer> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return;
        }
        moeGroomingPetDetailMapper.deleteByGroomingIds(appointmentIds);
        groomingServiceOperationService.deleteOperationByGroomingIds(appointmentIds);
    }

    public List<GroomingPetServiceListInfoDTO> queryPetDetailList(
            boolean migrated, long companyId, PetDetailQueryVo petDetailQueryVo, Integer tokenStaffId) {
        LocalDate startDate = LocalDate.parse(petDetailQueryVo.getStartDate());
        LocalDate endDate = LocalDate.parse(petDetailQueryVo.getEndDate());

        List<Integer> staffIdList =
                iBusinessStaffClient.showCalenderStaff(petDetailQueryVo.getBusinessId(), tokenStaffId);
        petDetailQueryVo.setStaffIdList(staffIdList);
        List<GroomingPetServiceListInfoDTO> groomingPetServiceListInfoS =
                getGroomingPetServiceList(petDetailQueryVo, companyId).stream()
                        .filter(pd -> {
                            if (StringUtils.isBlank(pd.getAppointmentDate())) {
                                return false;
                            }
                            LocalDate appointmentDate = LocalDate.parse(pd.getAppointmentDate());
                            // appointmentDate >= startDate && appointmentDate <= endDate
                            return !appointmentDate.isBefore(startDate) && !appointmentDate.isAfter(endDate);
                        })
                        .toList();

        if (CollectionUtils.isEmpty(groomingPetServiceListInfoS)) {
            return List.of();
        }

        // 查询宠物信息
        queryPetInfo(groomingPetServiceListInfoS);

        // 查询预约创建人信息
        List<Integer> staffIds = groomingPetServiceListInfoS.stream()
                .map(GroomingPetServiceListInfoDTO::getCreatedById)
                .toList();

        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(petDetailQueryVo.getBusinessId());
        staffIdListParams.setStaffIdList(staffIds);
        List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(staffIdListParams);

        groomingPetServiceListInfoS.forEach(info -> staffList.stream()
                .filter(moeStaffDto -> moeStaffDto.getId().equals(info.getCreatedById()))
                .findFirst()
                .ifPresent(moeStaffDto -> {
                    info.setCreatedByFirstName(moeStaffDto.getFirstName());
                    info.setCustomerLastName(moeStaffDto.getLastName());
                }));

        // 查询顾客信息
        queryCustomerInfo(groomingPetServiceListInfoS, tokenStaffId);

        // 查询 area 信息
        queryAreaInfo(groomingPetServiceListInfoS, petDetailQueryVo.getBusinessId());

        // 标记是否为new customer
        setIsNewCustomer(migrated, companyId, petDetailQueryVo.getBusinessId(), groomingPetServiceListInfoS);

        // 组装client full name
        // address1空格address2逗号空格city逗号空格state逗号空格country空格zip code
        setClientFullAddress(groomingPetServiceListInfoS);

        // 增加返回notice
        List<Integer> groomingIds = groomingPetServiceListInfoS.stream()
                .map(GroomingPetServiceListInfoDTO::getTicketId)
                .toList();
        if (!groomingIds.isEmpty()) {
            // alert notes
            List<MoeGroomingNote> alertNoteList = moeGroomingNoteService.getNoteListByGroomingIdListAndType(
                    groomingIds, GroomingAppointmentEnum.NOTE_ALERT.intValue());
            if (!CollectionUtils.isEmpty(alertNoteList)) {
                /**
                 * List -> Map
                 * 需要注意的是：
                 * toMap 如果集合对象有重复的key，会报错Duplicate key ....
                 * 这时可以用 (note1,note2)->note2 来设置，如果有重复的key,则保留 note2,舍弃note1
                 */
                Map<Integer, MoeGroomingNote> alertNoteMap = alertNoteList.stream()
                        .collect(Collectors.toMap(
                                MoeGroomingNote::getGroomingId, note -> note, (note1, note2) -> note2));
                if (!CollectionUtils.isEmpty(alertNoteMap)) {
                    for (GroomingPetServiceListInfoDTO groomingPetServiceListInfoDTO : groomingPetServiceListInfoS) {
                        if (alertNoteMap.containsKey(groomingPetServiceListInfoDTO.getTicketId())) {
                            groomingPetServiceListInfoDTO.setAlertNotes(alertNoteMap
                                    .get(groomingPetServiceListInfoDTO.getTicketId())
                                    .getNote());
                        }
                    }
                }
            }

            // 查询 operation
            setOperationList(groomingPetServiceListInfoS, groomingIds, petDetailQueryVo.getBusinessId());

            // comment notes
            List<MoeGroomingNote> commentNoteList = moeGroomingNoteService.getNoteListByGroomingIdListAndType(
                    groomingIds, GroomingAppointmentEnum.NOTE_COMMENT.intValue());
            if (!CollectionUtils.isEmpty(commentNoteList)) {
                Map<Integer, MoeGroomingNote> commentNoteMap = commentNoteList.stream()
                        .collect(Collectors.toMap(
                                MoeGroomingNote::getGroomingId, note -> note, (note1, note2) -> note2));
                if (!CollectionUtils.isEmpty(commentNoteMap)) {
                    for (GroomingPetServiceListInfoDTO groomingPetServiceListInfoDTO : groomingPetServiceListInfoS) {
                        if (commentNoteMap.containsKey(groomingPetServiceListInfoDTO.getTicketId())) {
                            groomingPetServiceListInfoDTO.setTicketComments(commentNoteMap
                                    .get(groomingPetServiceListInfoDTO.getTicketId())
                                    .getNote());
                        }
                    }
                }
            }

            Map<Integer, MoeGroomingInvoice> groomingIdToInvoiceMap = new HashMap<>();
            Map<Integer, MoeBookOnlineDeposit> depositMap = new HashMap<>();
            // 返回增加amount
            List<MoeGroomingInvoice> invoices =
                    orderService.getListByGroomingIds(null, groomingIds, OrderSourceType.APPOINTMENT.getSource());
            invoices.forEach(invoice -> groomingIdToInvoiceMap.put(invoice.getGroomingId(), invoice));
            // 查询定金支付金额
            if (groomingIdToInvoiceMap.size() > 0) {
                List<MoeBookOnlineDeposit> deposits = obDepositService.getOBDepositByGroomingIds(
                        petDetailQueryVo.getBusinessId(), groomingIdToInvoiceMap.keySet());
                depositMap = deposits.stream()
                        .collect(Collectors.toMap(MoeBookOnlineDeposit::getGroomingId, i -> i, (d1, d2) -> d2));
            }

            for (GroomingPetServiceListInfoDTO groomingDTO : groomingPetServiceListInfoS) {
                MoeGroomingInvoice invoice = groomingIdToInvoiceMap.get(groomingDTO.getTicketId());
                if (!Objects.isNull(invoice)) {
                    groomingDTO.setRemainingAmount(invoice.getRemainAmount());
                    groomingDTO.setTotalAmount(invoice.getPaymentAmount());
                    groomingDTO.setPaidAmount(invoice.getPaidAmount());
                    groomingDTO.setRefundAmount(invoice.getRefundedAmount());
                    groomingDTO.setInvoiceId(invoice.getId());
                }
                MoeBookOnlineDeposit deposit = depositMap.get(groomingDTO.getTicketId());
                if (!Objects.isNull(deposit) && DepositPaymentTypeEnum.PrePay.equals(deposit.getDepositType())) {
                    groomingDTO.setPrepaidAmount(deposit.getAmount());
                    groomingDTO.setPrepayStatus(deposit.getStatus());
                    groomingDTO.setPrepayRate(obDepositService.getPrepayRate(deposit, invoice.getPaymentAmount()));
                }
            }
            // 获取preauth状态
            List<PreAuthDTO> preAuthDTOS = preAuthClient.batchQueryByTicketIds(BatchQueryPreAuthParams.builder()
                    .businessId(petDetailQueryVo.getBusinessId())
                    .ticketIds(groomingIds)
                    .build());
            if (!CollectionUtils.isEmpty(preAuthDTOS)) {
                Map<Integer, PreAuthDTO> preauthMap = preAuthDTOS.stream()
                        .collect(Collectors.toMap(PreAuthDTO::getTicketId, note -> note, (note1, note2) -> note2));
                for (GroomingPetServiceListInfoDTO serviceListInfo : groomingPetServiceListInfoS) {
                    if (preauthMap.containsKey(serviceListInfo.getTicketId())) {
                        serviceListInfo.setPreAuthInfo(preauthMap.get(serviceListInfo.getTicketId()));
                    }
                }
            }

            // appointment tracking map
            var apptIdToTracking = appointTrackingServiceBlockingStub
                    .listAppointmentTracking(ListAppointmentTrackingRequest.newBuilder()
                            .setFilter(ListAppointmentTrackingRequest.Filter.newBuilder()
                                    .addAllAppointmentIds(groomingIds.stream()
                                            .map(Integer::longValue)
                                            .collect(Collectors.toList()))
                                    .build())
                            .build())
                    .getAppointmentTrackingList()
                    .stream()
                    .collect(Collectors.toMap(AppointmentTracking::getAppointmentId, Function.identity(), (o, n) -> o));
            for (GroomingPetServiceListInfoDTO groomingDTO : groomingPetServiceListInfoS) {
                groomingDTO.setAppointmentTracking(AppointmentConverter.INSTANCE.toTrackingViewDto(
                        apptIdToTracking.get(groomingDTO.getTicketId().longValue())));
            }
        }

        /*
         * 兼容代码 by ZhangDong
         * 因为 appointment 新增了两个状态字段，但是前端存在版本碎片问题，无法正确解析，因此需要增加兼容逻辑
         * status: 旧字段，状态枚举为 1 - 4，旧版本前端会读该字段
         * appointmentStatus：新字段，状态枚举为 1 - 6，新版本前端会读该字段
         */
        groomingPetServiceListInfoS.forEach(e -> {
            Byte status = e.getStatus().byteValue();
            e.setStatus(moeGroomingAppointmentService.getCompatibleStatus(status));
            e.setAppointmentStatus(
                    moeGroomingAppointmentService.getCompatibleAppointmentStatus(status, e.getCheckInTime()));
        });
        // ------------------------------

        setAutoAssign(groomingPetServiceListInfoS);

        setServiceItems(groomingPetServiceListInfoS);

        return groomingPetServiceListInfoS;
    }

    private List<GroomingPetServiceListInfoDTO> getGroomingPetServiceList(
            PetDetailQueryVo petDetailQueryVo, long companyId) {
        if (isAllowBoardingAndDaycare(companyId)) {
            return moeGroomingPetDetailMapper.queryPetDetailListIncludeHybrid(petDetailQueryVo);
        }
        return moeGroomingPetDetailMapper.queryPetDetailListForGroomingOnly(petDetailQueryVo);
    }

    private boolean isAllowBoardingAndDaycare(long companyId) {
        String cacheValue = redisUtil.get(ALLOW_BOARDING_AND_DAYCARE_KEY + companyId);
        if (StringUtils.isNotBlank(cacheValue)) {
            return Boolean.parseBoolean(cacheValue);
        }

        boolean allow = metadataServiceBlockingStub
                .describeValues(DescribeValuesRequest.newBuilder()
                        .setKeyId(metadataServiceBlockingStub
                                .getKey(GetKeyRequest.newBuilder()
                                        .setName("allow_boarding_and_daycare")
                                        .build())
                                .getKey()
                                .getId())
                        .addOwnerIds(companyId)
                        .build())
                .getValuesList()
                .stream()
                .map(ValueModel::getValue)
                .anyMatch(value -> value.equalsIgnoreCase("true"));
        redisUtil.setEx(ALLOW_BOARDING_AND_DAYCARE_KEY + companyId, String.valueOf(allow), 1, TimeUnit.DAYS);
        return allow;
    }

    private void setServiceItems(List<GroomingPetServiceListInfoDTO> infos) {
        infos.forEach(
                info -> info.setServiceItems(ServiceItemEnum.convertBitValueList(info.getServiceTypeInclude()).stream()
                        .map(ServiceItemEnum::getServiceItem)
                        .distinct()
                        .toList()));
    }

    private void setAutoAssign(List<GroomingPetServiceListInfoDTO> groomingPetServiceListInfoS) {
        List<Integer> appointmentIds = groomingPetServiceListInfoS.stream()
                .map(GroomingPetServiceListInfoDTO::getTicketId)
                .toList();
        Map<Integer, AutoAssign> apptIdToAutoAssign = autoAssignService.listAutoAssign(appointmentIds).stream()
                .collect(Collectors.toMap(AutoAssign::getAppointmentId, Function.identity(), (o, n) -> o));
        groomingPetServiceListInfoS.forEach(e ->
                e.setAutoAssign(AutoAssignConverter.INSTANCE.entityToDTO(apptIdToAutoAssign.get(e.getTicketId()))));
    }

    private void setOperationList(
            List<GroomingPetServiceListInfoDTO> groomingPetServiceListInfoS,
            List<Integer> groomingIdList,
            Integer businessId) {
        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingIdList(businessId, groomingIdList);
        if (CollectionUtils.isEmpty(operationMap)) {
            return;
        }
        groomingPetServiceListInfoS.forEach(
                info -> info.setOperationList(operationMap.getOrDefault(info.getPetDetailId(), List.of())));
    }

    private void setIsNewCustomer(
            boolean migrated,
            long companyId,
            Integer businessId,
            List<GroomingPetServiceListInfoDTO> groomingPetServiceListInfoS) {
        List<Integer> customerIdsParam = new ArrayList<>();
        for (GroomingPetServiceListInfoDTO groomingPetServiceListInfoDTO : groomingPetServiceListInfoS) {
            if (groomingPetServiceListInfoDTO.getIsBlock() == 1) {
                continue;
            }
            customerIdsParam.add(groomingPetServiceListInfoDTO.getCustomerId());
        }
        CustomerLastFinishedApptMapDto lastFinishedApptMapDto =
                appointmentQueryService.getCustomerLastFinishedAppointment(
                        migrated, companyId, businessId, customerIdsParam);
        Map<Integer, CustomerGroomingAppointmentDTO> lastFinishedApptMap =
                lastFinishedApptMapDto.getLastFinishedApptMap();
        // 新增isNewCustomer字段
        for (GroomingPetServiceListInfoDTO groomingPetServiceListInfoDTO : groomingPetServiceListInfoS) {
            if (groomingPetServiceListInfoDTO.getIsBlock() == 1) {
                continue;
            }
            boolean isNewCustomer = lastFinishedApptMap.get(groomingPetServiceListInfoDTO.getCustomerId()) == null;
            groomingPetServiceListInfoDTO.setIsNewCustomer(isNewCustomer);
        }
    }

    private void setClientFullAddress(List<GroomingPetServiceListInfoDTO> groomingPetServiceListInfoS) {
        groomingPetServiceListInfoS.forEach(e -> {
            StringJoiner sj = new StringJoiner(", ");

            if (StringUtils.isNotBlank(e.getAddress1())) {
                sj.add(e.getAddress1());
            }
            if (StringUtils.isNotBlank(e.getAddress2())) {
                sj.add(e.getAddress2());
            }
            if (StringUtils.isNotBlank(e.getCity())) {
                sj.add(e.getCity());
            }
            if (StringUtils.isNotBlank(e.getState())) {
                sj.add(e.getState());
            }
            if (StringUtils.isNotBlank(e.getCountry())) {
                sj.add(e.getCountry());
            }
            if (StringUtils.isNotBlank(e.getZipcode())) {
                sj.add(e.getZipcode());
            }

            e.setClientFullAddress(sj.toString());
        });
    }

    private void queryCustomerInfo(
            List<GroomingPetServiceListInfoDTO> groomingPetServiceListInfoS, Integer tokenStaffId) {
        if (groomingPetServiceListInfoS.isEmpty()) return;
        List<GroomingQueryDto> ticketInfo = new ArrayList<>();

        // 收集需要查询的customerId
        for (GroomingPetServiceListInfoDTO groomingPetServiceListInfoDTO : groomingPetServiceListInfoS) {
            if (groomingPetServiceListInfoDTO.getIsBlock() == 1) {
                continue;
            }
            GroomingQueryDto groomingQueryDto = new GroomingQueryDto();
            groomingQueryDto.setCustomerId(groomingPetServiceListInfoDTO.getCustomerId());
            groomingQueryDto.setGroomingId(groomingPetServiceListInfoDTO.getTicketId());
            groomingQueryDto.setCustomerAddressId(groomingPetServiceListInfoDTO.getCustomerAddressId());

            ticketInfo.add(groomingQueryDto);
        }

        GroomingCustomerInfoParams groomingCustomerInfoParams = new GroomingCustomerInfoParams();
        groomingCustomerInfoParams.setTokenStaffId(tokenStaffId);
        groomingCustomerInfoParams.setTicketInfo(ticketInfo);

        List<GroomingCalenderCustomerInfo> groomingCalenderCustomerInfos =
                iCustomerGroomingClient.getGroomingCalenderCustomerInfo(groomingCustomerInfoParams);

        for (GroomingPetServiceListInfoDTO groomingPetServiceListInfoDTO : groomingPetServiceListInfoS) {
            // 顾客信息
            for (GroomingCalenderCustomerInfo groomingCalenderCustomerInfo : groomingCalenderCustomerInfos) {
                if (groomingPetServiceListInfoDTO.getCustomerId().equals(groomingCalenderCustomerInfo.getCustomerId())
                        && groomingPetServiceListInfoDTO
                                .getTicketId()
                                .equals(groomingCalenderCustomerInfo.getGroomingId())) {
                    groomingPetServiceListInfoDTO.setCustomerFirstName(
                            groomingCalenderCustomerInfo.getCustomerFirstName());
                    groomingPetServiceListInfoDTO.setCustomerLastName(
                            groomingCalenderCustomerInfo.getCustomerLastName());
                    groomingPetServiceListInfoDTO.setClientPhoneNumber(
                            groomingCalenderCustomerInfo.getClientPhoneNumber());
                    groomingPetServiceListInfoDTO.setAddress1(groomingCalenderCustomerInfo.getAddress1());
                    groomingPetServiceListInfoDTO.setAddress2(groomingCalenderCustomerInfo.getAddress2());
                    groomingPetServiceListInfoDTO.setCountry(groomingCalenderCustomerInfo.getCountry());
                    groomingPetServiceListInfoDTO.setState(groomingCalenderCustomerInfo.getState());
                    groomingPetServiceListInfoDTO.setCity(groomingCalenderCustomerInfo.getCity());
                    groomingPetServiceListInfoDTO.setZipcode(groomingCalenderCustomerInfo.getZipcode());
                    groomingPetServiceListInfoDTO.setLat(groomingCalenderCustomerInfo.getLat());
                    groomingPetServiceListInfoDTO.setLng(groomingCalenderCustomerInfo.getLng());
                    groomingPetServiceListInfoDTO.setCustomerAvatar(groomingCalenderCustomerInfo.getAvatarPath());
                    groomingPetServiceListInfoDTO.setClientColor(groomingCalenderCustomerInfo.getClientColor());
                    groomingPetServiceListInfoDTO.setPrimaryContactFirstName(
                            groomingCalenderCustomerInfo.getPrimaryContactFirstName());
                    groomingPetServiceListInfoDTO.setPrimaryContactLastName(
                            groomingCalenderCustomerInfo.getPrimaryContactLastName());
                    groomingPetServiceListInfoDTO.setPrimaryContactId(
                            groomingCalenderCustomerInfo.getPrimaryContactId());
                    groomingPetServiceListInfoDTO.setHasPetParentAppAccount(
                            groomingCalenderCustomerInfo.getHasPetParentAppAccount());
                    break;
                }
            }
        }
    }

    private void queryAreaInfo(List<GroomingPetServiceListInfoDTO> groomingPetServiceListInfoS, Integer businessId) {
        List<GetAreasByLocationParams> locations = groomingPetServiceListInfoS.stream()
                .filter(groomingPetServiceListInfo -> !Strings.isEmpty(groomingPetServiceListInfo.getLat())
                        && !Strings.isEmpty(groomingPetServiceListInfo.getLng()))
                .collect(Collectors.toMap(
                        GroomingPetServiceListInfoDTO::getCustomerId,
                        groomingPetServiceListInfo -> new GetAreasByLocationParams(
                                groomingPetServiceListInfo.getCustomerId().longValue(),
                                groomingPetServiceListInfo.getLat(),
                                groomingPetServiceListInfo.getLng(),
                                groomingPetServiceListInfo.getZipcode()),
                        (c1, c2) -> c1))
                .values()
                .stream()
                .toList();
        Map<Long, List<CertainAreaDTO>> certainAreaMap = iBusinessServiceAreaClient.getAreasByLocation(
                new BatchGetAreasByLocationParams(businessId.longValue(), null, locations));
        if (CollectionUtils.isEmpty(certainAreaMap)) return;
        groomingPetServiceListInfoS.forEach(groomingPetServiceListInfo -> {
            if (certainAreaMap.containsKey(
                    groomingPetServiceListInfo.getCustomerId().longValue())) {
                groomingPetServiceListInfo.setAreas(certainAreaMap.get(
                        groomingPetServiceListInfo.getCustomerId().longValue()));
            }
        });
    }

    public List<GroomingPetServiceListInfoDTO> queryPetDetailListMonthly(
            PetDetailMonthlyQueryVo petDetailMonthlyQueryVo, Integer tokenStaffId, Long companyId) {
        LocalDate startDate = LocalDate.parse(petDetailMonthlyQueryVo.getStartDate());
        LocalDate endDate = LocalDate.parse(petDetailMonthlyQueryVo.getEndDate());

        Integer businessId = petDetailMonthlyQueryVo.getBusinessId();
        List<Integer> staffIdList = iBusinessStaffClient.showCalenderStaff(businessId, tokenStaffId);
        petDetailMonthlyQueryVo.setStaffIdList(staffIdList);

        List<GroomingPetServiceListInfoDTO> result =
                getGroomingPetServiceListMonthly(petDetailMonthlyQueryVo, companyId).stream()
                        .filter(pd -> {
                            LocalDate appointmentDate = LocalDate.parse(pd.getAppointmentDate());
                            // appointmentDate >= startDate && appointmentDate <= endDate
                            return !appointmentDate.isBefore(startDate) && !appointmentDate.isAfter(endDate);
                        })
                        .toList();

        if (result == null || result.isEmpty()) {
            return List.of();
        }

        // 根据规则返回
        queryPetInfo(result);

        queryCustomerInfo(result, tokenStaffId);

        setClientFullAddress(result);

        // block time 需要在monthly view下发
        List<Integer> groomingIdList =
                result.stream().map(GroomingPetServiceListInfoDTO::getTicketId).toList();
        List<MoeGroomingNote> commentNoteList = moeGroomingNoteService.getNoteListByGroomingIdListAndType(
                groomingIdList, GroomingAppointmentEnum.NOTE_COMMENT.intValue());

        // 查询 operation
        setOperationList(result, groomingIdList, businessId);

        if (!CollectionUtils.isEmpty(commentNoteList)) {
            /**
             * List -> Map
             * 需要注意的是：
             * toMap 如果集合对象有重复的key，会报错Duplicate key ....
             * 这时可以用 (note1,note2)->note2 来设置，如果有重复的key,则保留 note2,舍弃note1
             */
            Map<Integer, MoeGroomingNote> commentNoteMap = commentNoteList.stream()
                    .collect(Collectors.toMap(MoeGroomingNote::getGroomingId, note -> note, (note1, note2) -> note2));
            if (!CollectionUtils.isEmpty(commentNoteMap)) {
                for (GroomingPetServiceListInfoDTO groomingPetServiceListInfoDTO : result) {
                    if (commentNoteMap.containsKey(groomingPetServiceListInfoDTO.getTicketId())) {
                        groomingPetServiceListInfoDTO.setTicketComments(commentNoteMap
                                .get(groomingPetServiceListInfoDTO.getTicketId())
                                .getNote());
                    }
                }
            }
        }

        /*
         * 兼容代码 by ZhangDong
         * 因为 appointment 新增了两个状态字段，但是前端存在版本碎片问题，无法正确解析，因此需要增加兼容逻辑
         * status: 旧字段，状态枚举为 1 - 4，旧版本前端会读该字段
         * appointmentStatus：新字段，状态枚举为 1 - 6，新版本前端会读该字段
         */
        result.forEach(e -> {
            Byte status = e.getStatus().byteValue();
            e.setStatus(moeGroomingAppointmentService.getCompatibleStatus(status));
            e.setAppointmentStatus(
                    moeGroomingAppointmentService.getCompatibleAppointmentStatus(status, e.getCheckInTime()));
        });
        // ------------------------------

        setServiceItems(result);
        return result;
    }

    private List<GroomingPetServiceListInfoDTO> getGroomingPetServiceListMonthly(
            PetDetailMonthlyQueryVo petDetailMonthlyQueryVo, Long companyId) {
        if (isAllowBoardingAndDaycare(companyId)) {
            return moeGroomingPetDetailMapper.queryPetDetailListMonthlyIncludeHybrid(petDetailMonthlyQueryVo);
        }

        return moeGroomingPetDetailMapper.queryPetDetailListMonthlyForGroomingOnly(petDetailMonthlyQueryVo);
    }

    private void queryPetInfo(List<GroomingPetServiceListInfoDTO> groomingPetServiceListInfoDTOS) {
        List<Integer> petIds = groomingPetServiceListInfoDTOS.stream()
                .filter(dto -> dto.getIsBlock() != 1)
                .map(GroomingPetServiceListInfoDTO::getPetId)
                .toList();
        // 查询宠物名称
        List<CustomerPetPetCodeDTO> groomingCalenderPetInfos =
                iPetCodeClient.getCustomerPetPetCodeListByIdList(false, petIds);
        for (GroomingPetServiceListInfoDTO groomingPetServiceListInfoDTO : groomingPetServiceListInfoDTOS) {
            for (CustomerPetPetCodeDTO groomingCalenderPetInfo : groomingCalenderPetInfos) {
                if (groomingPetServiceListInfoDTO.getPetId() != null
                        && groomingPetServiceListInfoDTO.getPetId().equals(groomingCalenderPetInfo.getPetId())) {
                    groomingPetServiceListInfoDTO.setPetName(groomingCalenderPetInfo.getPetName());
                    groomingPetServiceListInfoDTO.setPetBreed(groomingCalenderPetInfo.getBreed());
                    groomingPetServiceListInfoDTO.setPetAvatar(groomingCalenderPetInfo.getAvatarPath());
                    groomingPetServiceListInfoDTO.setPetTypeId(groomingCalenderPetInfo.getPetTypeId());
                    groomingPetServiceListInfoDTO.setWeight(groomingCalenderPetInfo.getWeight());
                    groomingPetServiceListInfoDTO.setCoatType(groomingCalenderPetInfo.getHairLength());

                    List<PetCodeInfoDTO> moePetCodeInfos = new ArrayList<>();
                    List<MoePetCodeInfoDTO> moePetCodeInfo = groomingCalenderPetInfo.getMoePetCodeInfos();
                    for (MoePetCodeInfoDTO moePetCodeInfoDTO : moePetCodeInfo) {
                        PetCodeInfoDTO petCodeInfoDTO = new PetCodeInfoDTO();
                        petCodeInfoDTO.setPetCodeId(moePetCodeInfoDTO.getPetCodeId());
                        petCodeInfoDTO.setCodeNumber(moePetCodeInfoDTO.getCodeNumber());
                        petCodeInfoDTO.setColor(moePetCodeInfoDTO.getColor());
                        petCodeInfoDTO.setDescription(moePetCodeInfoDTO.getDescription());

                        moePetCodeInfos.add(petCodeInfoDTO);
                    }
                    groomingPetServiceListInfoDTO.setMoePetCodeInfos(moePetCodeInfos);
                    break;
                }
            }
        }
    }

    // 暂只查询开始时间和服务时间
    public List<MoeGroomingPetDetail> queryPetDetailByAppointmentDateAndStaffId(
            Integer businessId, String appointmentDate, Integer staffId) {
        return moeGroomingPetDetailMapper.queryPetDetailByAppointmentDateAndStaffId(
                businessId, appointmentDate, staffId);
    }

    public ResponseResult<Integer> editServiceTime(
            Integer businessId, Integer tokenStaffId, List<EditPetDetailParams> editPetDetailParam) {
        boolean updateDate = true;

        if (CollectionUtils.isEmpty(editPetDetailParam)) {
            return ResponseResult.success(0);
        }

        EditPetDetailParams param = editPetDetailParam.get(0);

        // 如果修改的啥时间大于预约的最晚时间需修改预约的最晚时间
        MoeGroomingAppointment moeGroomingAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKey(param.getGroomingId());
        if (moeGroomingAppointment == null || !businessId.equals(moeGroomingAppointment.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "ticket not found");
        }

        List<MoeGroomingPetDetail> petDetails =
                moeGroomingPetDetailMapper.queryPetDetailCountByGroomingId(param.getGroomingId());

        // 如果transfer数量不一致，则不修改appointmentDate
        if (editPetDetailParam.size() != petDetails.size()) {
            updateDate = false;
        }

        // 判断是否是一个ticket， 执行update前，做petDetailId校验，后面更新时，无须再次校验
        for (EditPetDetailParams editPetDetailParams : editPetDetailParam) {
            boolean flag = true;
            for (MoeGroomingPetDetail moeGroomingPetDetail : petDetails) {
                if (moeGroomingPetDetail.getId().equals(editPetDetailParams.getPetDetailId())) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not a ticket ,can not update");
            }
        }

        // 获取发送的staffIdList
        Set<Integer> notificationStaffIdList = new HashSet<>();

        for (EditPetDetailParams editPetDetailParams : editPetDetailParam) {
            MoeGroomingPetDetail moeGroomingPetDetail =
                    moeGroomingPetDetailMapper.selectByPrimaryKey(editPetDetailParams.getPetDetailId());
            if (moeGroomingPetDetail == null) {
                log.warn("cant find pet detail record for {}", editPetDetailParams.getPetDetailId());
                continue;
            }
            notificationStaffIdList.add(moeGroomingPetDetail.getStaffId());
            moeGroomingPetDetail = new MoeGroomingPetDetail();
            moeGroomingPetDetail.setId(editPetDetailParams.getPetDetailId());
            moeGroomingPetDetail.setStartTime(editPetDetailParams.getStartTime());
            moeGroomingPetDetail.setStartDate(param.getAppointmentDate());
            moeGroomingPetDetail.setEndDate(param.getAppointmentDate());
            moeGroomingPetDetail.setServiceTime(editPetDetailParams.getServiceTime());
            moeGroomingPetDetail.setEndTime(editPetDetailParams.getStartTime() + editPetDetailParams.getServiceTime());
            moeGroomingPetDetail.setUpdateTime(CommonUtil.get10Timestamp());
            moeGroomingPetDetailMapper.updateByPrimaryKeySelective(moeGroomingPetDetail);
        }

        List<Integer> endTimes = new ArrayList<>();
        List<Integer> startTimes = new ArrayList<>();

        // 修改之后计算预约开始和结束时间
        List<MoeGroomingPetDetail> petDetailEnd =
                moeGroomingPetDetailMapper.queryPetDetailCountByGroomingId(param.getGroomingId());

        for (MoeGroomingPetDetail moeGroomingPetDetail : petDetailEnd) {
            int endTime = moeGroomingPetDetail.getStartTime().intValue() + moeGroomingPetDetail.getServiceTime();

            endTimes.add(endTime);
            startTimes.add(moeGroomingPetDetail.getStartTime().intValue());
        }

        Integer max = Collections.max(endTimes);
        Integer min = Collections.min(startTimes);

        // 只有当apptDate或startTime修改后，才会发送ApptRescheduled通知
        boolean isModifyApptDate = false;
        boolean isModifyApptStartTime = false;
        // 修改预约日期和起止时间
        String oldAppointmentDate = moeGroomingAppointment.getAppointmentDate();
        Integer oldAppointmentStartTime = moeGroomingAppointment.getAppointmentStartTime();
        Integer oldAppointmentEndTime = moeGroomingAppointment.getAppointmentEndTime();

        MoeGroomingAppointment updatedApp = new MoeGroomingAppointment();
        updatedApp.setId(param.getGroomingId());
        updatedApp.setAppointmentEndTime(max);
        updatedApp.setAppointmentStartTime(min);

        updatedApp.setOldAppointmentDate(oldAppointmentDate);
        updatedApp.setOldAppointmentStartTime(oldAppointmentStartTime);
        updatedApp.setOldAppointmentEndTime(oldAppointmentEndTime);
        updatedApp.setUpdateTime(CommonUtil.get10Timestamp());
        // 当修改的是所有服务，且日期发生了变化，才可修改预约日期
        if (StringUtils.isNotBlank(param.getAppointmentDate())
                && updateDate
                && !oldAppointmentDate.equals(param.getAppointmentDate())) {
            updatedApp.setAppointmentDate(param.getAppointmentDate());
            updatedApp.setAppointmentEndDate(param.getAppointmentDate());
            isModifyApptDate = true;
        }
        if (!min.equals(oldAppointmentStartTime)) {
            isModifyApptStartTime = true;
        }

        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(updatedApp);

        if (isModifyApptDate || isModifyApptStartTime) {
            ThreadPool.execute(() -> {
                MoeGroomingAppointment appointment =
                        moeGroomingAppointmentMapper.selectByPrimaryKey(param.getGroomingId());
                // 调用通知发送
                NotificationApptRescheduledParams apptRescheduledParams = new NotificationApptRescheduledParams();
                apptRescheduledParams.setBusinessId(appointment.getBusinessId());
                apptRescheduledParams.setStaffIdList(notificationStaffIdList);
                apptRescheduledParams.setTokenStaffId(tokenStaffId);
                // 给前端的数据体
                NotificationExtraApptCommonDto apptRescheduledDto = new NotificationExtraApptCommonDto();
                apptRescheduledDto.setAppointmentDate(appointment.getAppointmentDate());
                apptRescheduledDto.setAppointmentStartTime(appointment.getAppointmentStartTime());
                apptRescheduledDto.setNoStartTime(appointment.getNoStartTime());
                apptRescheduledDto.setAppointmentEndTime(appointment.getAppointmentEndTime());
                apptRescheduledDto.setCustomerId(appointment.getCustomerId());
                apptRescheduledDto.setGroomingId(appointment.getId());
                // customer firstName 和 lastName 在notification模块组装
                apptRescheduledParams.setWebPushDto(apptRescheduledDto);
                iNotificationClient.sendNotificationApptRescheduled(apptRescheduledParams);
            });
        }

        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(moeGroomingAppointment.getBusinessId())
                .setCustomerId(moeGroomingAppointment.getCustomerId()));
        return ResponseResult.success(editPetDetailParam.size());
    }

    public List<MoeGroomingPetDetail> queryPetDetailsByAppointmentId(Integer appointmentId) {
        MoeGroomingPetDetailExample example = new MoeGroomingPetDetailExample();
        example.createCriteria()
                .andGroomingIdEqualTo(appointmentId)
                .andStatusEqualTo(PetDetailStatusEnum.NORMAL.getValue().byteValue())
                .andServiceItemTypeEqualTo(ServiceItemType.GROOMING_VALUE);
        return moeGroomingPetDetailMapper.selectByExample(example);
    }

    /**
     * All-in-One 宠物服务详情
     *
     * @param groomingIds appointment id
     * @return pet details
     */
    public List<GroomingPetDetailDTO> queryAllPetDetailByGroomingIds(List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return List.of();
        }
        return moeGroomingPetDetailMapper.queryAllPetDetailByGroomingIds(groomingIds);
    }

    public List<GroomingPetDetailDTO> queryPetDetailByGroomingId(List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return List.of();
        }
        return moeGroomingPetDetailMapper.queryPetDetailByGroomingIds(groomingIds);
    }

    public List<MoeGroomingPetDetail> queryMoePetDetailByGroomingIds(List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return List.of();
        }
        return moeGroomingPetDetailMapper.selectPetDetailByGroomingIdList(groomingIds);
    }

    /**
     * 根据appointmentId查询所有petDetail，包含 grooming、boarding、daycare、evaluation
     */
    public List<MoeGroomingPetDetail> queryPetDetailByAppointmentIds(List<Integer> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        appointmentIds = appointmentIds.stream().filter(k -> k > 0).toList();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        return moeGroomingPetDetailMapper.selectByAppointmentIdList(appointmentIds);
    }

    public List<MoeGroomingPetDetail> queryPetDetailByOneGroomingId(Integer groomingId) {
        return moeGroomingPetDetailMapper.queryPetDetailCountByGroomingId(groomingId);
    }

    /**
     * @return <petId, List<GroomingPetServiceDTO>>
     */
    public Map<Integer, List<GroomingPetServiceDTO>> getPetServiceDTOs(
            Integer businessId, Map<Integer, List<MoeGroomingPetDetail>> petServiceMap) {
        List<Integer> groomingServiceIdList = new ArrayList<>();
        List<Integer> serviceIdList = new ArrayList<>();
        for (List<MoeGroomingPetDetail> petDetails : petServiceMap.values()) {
            for (MoeGroomingPetDetail petDetail : petDetails) {
                if (petDetail.getEnableOperation()) {
                    groomingServiceIdList.add(petDetail.getId());
                }
                if (petDetail.getServiceId() != null && petDetail.getServiceId() > 0) {
                    serviceIdList.add(petDetail.getServiceId());
                }
            }
        }

        Map<Integer, MoeGroomingService> serviceMap = groomingServiceService.getServiceMap(businessId, serviceIdList);
        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingServiceIdList(
                        businessId, groomingServiceIdList);

        Map<Integer, MoeStaffDto> staffDtoMap = new HashMap<>();
        List<Integer> staffIds = petServiceMap.values().stream()
                .flatMap(Collection::stream)
                .map(MoeGroomingPetDetail::getStaffId)
                .filter(k -> k != null && k > 0)
                .toList();
        if (!CollectionUtils.isEmpty(staffIds)) {
            StaffIdListParams staffIdListParams = new StaffIdListParams();
            staffIdListParams.setBusinessId(businessId);
            staffIdListParams.setStaffIdList(staffIds);
            staffDtoMap = iBusinessStaffClient.getStaffList(staffIdListParams).stream()
                    .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity(), (k1, k2) -> k2));
        }
        Map<Integer, List<GroomingPetServiceDTO>> petInfoDetails = new HashMap<>();
        Map<Integer, MoeStaffDto> finalStaffDtoMap = staffDtoMap;
        petServiceMap.forEach((petId, petDetails) -> {
            List<GroomingPetServiceDTO> list = petDetails.stream()
                    .map(k -> makeGroomingPetServiceDTO(k, operationMap, serviceMap, finalStaffDtoMap))
                    .toList();
            petInfoDetails.put(petId, list);
        });
        return petInfoDetails;
    }

    GroomingPetServiceDTO makeGroomingPetServiceDTO(
            MoeGroomingPetDetail petDetail,
            Map<Integer, List<GroomingServiceOperationDTO>> operationMap,
            Map<Integer, MoeGroomingService> serviceMap,
            Map<Integer, MoeStaffDto> staffDtoMap) {
        GroomingPetServiceDTO groomingPetServiceDTO = new GroomingPetServiceDTO();

        BeanUtils.copyProperties(petDetail, groomingPetServiceDTO);
        groomingPetServiceDTO.setPetDetailId(petDetail.getId());
        if (petDetail.getServiceId() != null && serviceMap.containsKey(petDetail.getServiceId())) {
            MoeGroomingService groomingService = serviceMap.get(petDetail.getServiceId());
            groomingPetServiceDTO.setServiceName(groomingService.getName());
            groomingPetServiceDTO.setServiceStatus(groomingService.getStatus());
        }
        MoeStaffDto staffDto = staffDtoMap.get(petDetail.getStaffId());
        if (staffDto != null) {
            groomingPetServiceDTO.setStaffFirstName(staffDto.getFirstName());
            groomingPetServiceDTO.setStaffLastName(staffDto.getLastName());
        }
        if (Boolean.TRUE.equals(petDetail.getEnableOperation())) {
            groomingPetServiceDTO.setOperationList(operationMap.get(petDetail.getId()));
        }
        return groomingPetServiceDTO;
    }

    public ResponseResult<Integer> deletePetDetail(List<Integer> ids) {
        if (ids == null || ids.size() == 0) {
            return ResponseResult.success(0);
        }

        Integer integer = moeGroomingPetDetailMapper.deletePetDetail(ids);
        return ResponseResult.success(integer);
    }

    public List<PetDetailServiceDTO> queryPetDetailServiceByGroomingId(Integer groomingId, Integer petId) {
        return moeGroomingPetDetailMapper.queryPetDetailServiceByGroomingId(groomingId, petId);
    }

    public ResponseResult<Integer> addMoePetDetailsWaiting(
            MoeGroomingAppointment moeGroomingAppointment, List<PetDetailParams> petServices) {
        List<MoeGroomingPetDetail> moeGroomingPetDetails = new ArrayList<>();

        Integer starStaff = 0;
        for (PetDetailParams petDetailParams : petServices) {
            MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
            BeanUtils.copyProperties(petDetailParams, moeGroomingPetDetail);
            if (Objects.nonNull(petDetailParams.getStartTime())) {
                moeGroomingPetDetail.setStartTime(Long.valueOf(petDetailParams.getStartTime()));
            }

            if (petDetailParams.getStar() != null && petDetailParams.getStar()) {
                starStaff = petDetailParams.getStaffId();
            }
            moeGroomingPetDetail.setGroomingId(moeGroomingAppointment.getId());
            moeGroomingPetDetail.setUpdateTime(CommonUtil.get10Timestamp());
            moeGroomingPetDetail.setStartDate(moeGroomingAppointment.getAppointmentDate());
            moeGroomingPetDetail.setEndDate(moeGroomingAppointment.getAppointmentEndDate());

            if (moeGroomingPetDetail.getStartTime() != null && moeGroomingPetDetail.getServiceTime() != null) {
                moeGroomingPetDetail.setEndTime(
                        moeGroomingPetDetail.getStartTime() + moeGroomingPetDetail.getServiceTime());
            }
            moeGroomingPetDetails.add(moeGroomingPetDetail);
        }

        for (MoeGroomingPetDetail temp : moeGroomingPetDetails) {
            temp.setStarStaffId(starStaff);
        }

        moeGroomingPetDetails.forEach(petDetail -> petDetailMapper.insertSelective(petDetail));

        addOperation(moeGroomingAppointment, petServices, moeGroomingPetDetails);

        return ResponseResult.success(petServices.size());
    }

    private void addOperation(
            MoeGroomingAppointment moeGroomingAppointment,
            List<PetDetailParams> petServices,
            List<MoeGroomingPetDetail> moeGroomingPetDetails) {
        if (CollectionUtils.isEmpty(petServices) || CollectionUtils.isEmpty(moeGroomingPetDetails)) {
            return;
        }
        for (int i = 0; i < moeGroomingPetDetails.size(); i++) {
            MoeGroomingPetDetail moeGroomingPetDetail = moeGroomingPetDetails.get(i);
            if (Objects.isNull(moeGroomingPetDetail.getEnableOperation())
                    || Objects.equals(Boolean.FALSE, moeGroomingPetDetail.getEnableOperation())) {
                continue;
            }
            PetDetailParams petDetailParams = petServices.get(i);
            OperationBasic basic = new OperationBasic(
                    moeGroomingAppointment.getBusinessId(),
                    moeGroomingAppointment.getCompanyId(),
                    moeGroomingPetDetail.getGroomingId(),
                    moeGroomingPetDetail.getId(),
                    moeGroomingPetDetail.getPetId());
            groomingServiceOperationService.addOperation(basic, petDetailParams.getOperationList());
        }
    }

    /**
     * petId 宠物
     *
     * type=1 pass away
     * - 如果appt里只有这一只宠物，则整个appt 设置为cancel；
     * - 如果appt里有多只，移除该预约中 pass away 的宠物和与该宠物相关的服务。
     * type=2 delete
     * - 如果该 Pet 有未来预约，删除操作同上
     * @return
     */
    public Boolean petRemove(PetPassAwayParams params) {
        // TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition());
        // 对应grooming将被cancel
        List<Integer> onlyThisPetGroomingIdList = new ArrayList<>();
        // 对应petDetail将被移除
        List<Integer> multPetGroomingIdList = new ArrayList<>();

        Map<Integer, MoeGroomingAppointment> appointmentMap = new HashMap<>();

        var staffId = params.getStaffId();
        var migrateInfo = migrateHelper.getMigrationInfo(params.getBusinessId());
        var companyId = migrateInfo.companyId();

        String timeZoneName = companyServiceBlockingStub
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getTimeZone()
                .getName();

        Set<Integer> workingLocationIds = staffServiceBlockingStub
                .getStaffDetail(GetStaffDetailRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setId(staffId)
                        .build())
                .getStaff()
                .getWorkingLocationListList()
                .stream()
                .map(LocationBriefView::getId)
                .map(Long::intValue)
                .collect(Collectors.toSet());

        // 处理 waitList
        petRemoveWaitList(companyId, params.getPetId(), params.getCustomerId(), timeZoneName);

        // 处理预约，查询该宠物是否有未来预约
        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, timeZoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timeZoneName);
        List<MoeGroomingAppointment> appointmentList = moeGroomingPetDetailMapper.selectUpcomingByCustomerId(
                companyId, params.getCustomerId(), date, nowMinutes);
        if (CollectionUtils.isEmpty(appointmentList)) {
            return true;
        }

        var businessAppointments =
                appointmentList.stream().collect(Collectors.groupingBy(MoeGroomingAppointment::getBusinessId));
        if (!workingLocationIds.containsAll(businessAppointments.keySet())) {
            if (params.getType().intValue() == 1) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "Failed to update pet as passed away. Current pet has upcoming appointments outside of your working businesses, please remove all upcoming appointments before proceed.");
            }

            if (params.getType().intValue() == 2) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "Failed to delete pet. Current pet has upcoming appointments outside of your working businesses, please remove all upcoming appointments before proceed.");
            }

            // should not reach here
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Failed to update pet. Current pet has upcoming appointments outside of your working businesses, please remove all upcoming appointments before proceed.");
        }

        try {
            List<Integer> groomingIdList =
                    appointmentList.stream().map(MoeGroomingAppointment::getId).toList();
            appointmentMap.putAll(appointmentList.stream()
                    .collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity())));

            // 拉取 petDetail 信息
            Map<Integer, com.moego.lib.utils.model.Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>>
                    appointmentDetailsMap = appointmentServiceDetailService.getByAppointments(groomingIdList);
            if (CollectionUtils.isEmpty(appointmentDetailsMap)) {
                return true;
            }

            // 过滤出包含该 pet 的预约，需要区分仅包含与包含且不仅包含两种 case
            Pair<List<Integer>, List<Integer>> upcomingGroomingIds =
                    diffGroomingWithSinglePetMultiPet(appointmentDetailsMap, params.getPetId());
            multPetGroomingIdList.addAll(upcomingGroomingIds.getRight());
            onlyThisPetGroomingIdList.addAll(upcomingGroomingIds.getLeft());

            // cancel 仅包含该 pet 的预约
            waitListService.batchDeleteRelatedWaitList(companyId, null, timeZoneName, onlyThisPetGroomingIdList);
            for (Integer cancelGroomingId : onlyThisPetGroomingIdList) {
                var appointment = appointmentMap.get(cancelGroomingId);
                var businessId = appointment.getBusinessId();

                CancelParams editIdParams = new CancelParams();
                editIdParams.setNoShow((byte) 2);
                editIdParams.setId(cancelGroomingId);
                editIdParams.setBusinessId(businessId);
                editIdParams.setAccountId(params.getStaffId());
                moeGroomingAppointmentService.editAppointmentCancel(
                        editIdParams, true, true); // pet remove batch cancel
                ActivityLogRecorder.record(
                        AppointmentAction.CANCEL,
                        ResourceType.APPOINTMENT,
                        cancelGroomingId,
                        new CancelLogDTO("", AppointmentUpdatedBy.BY_DELETE_PET));

                publisher.publishEvent(new UpdateCustomerEvent(this)
                        .setBusinessId(appointment.getBusinessId())
                        .setCustomerId(params.getCustomerId()));
            }

            // 将被移除的petDetail
            List<Integer> needRemovePetDetailIds = new ArrayList<>();
            for (Integer removePetDetailGroomingId : multPetGroomingIdList) {
                for (MoeGroomingPetDetail petDetail :
                        appointmentDetailsMap.get(removePetDetailGroomingId).key()) {
                    if (petDetail.getPetId().equals(params.getPetId())) {
                        needRemovePetDetailIds.add(petDetail.getId());
                    }
                }
            }
            // 删除petDetailList涉及到的petDetail
            deletePetDetail(needRemovePetDetailIds);
        } catch (RuntimeException r) {
            // transactionManager.rollback(transactionStatus);
        }
        // transactionManager.commit(transactionStatus);
        ThreadPool.execute(() -> {
            for (Integer groomingId : multPetGroomingIdList) {
                updateGroomingTime(groomingId);
                var appointment = appointmentMap.get(groomingId);
                var businessId = appointment.getBusinessId();
                orderService.updateOrderByGroomingId(companyId, businessId, groomingId, params.getStaffId());
            }
        });
        ThreadPool.execute(() -> {
            // cancel
            for (Integer groomingId : onlyThisPetGroomingIdList) {
                var appointment = appointmentMap.get(groomingId);
                var businessId = appointment.getBusinessId();
                calendarSyncService.checkBusinessHaveGoogleCalendarSync(
                        businessId, groomingId, null, true); // petRemove 1
                quickBooksService.addRedisSyncGroomingData(businessId, groomingId, null); // petRemove 1
            }
            // update invoice
            for (Integer groomingId : multPetGroomingIdList) {
                var appointment = appointmentMap.get(groomingId);
                var businessId = appointment.getBusinessId();
                calendarSyncService.checkBusinessHaveGoogleCalendarSync(
                        businessId, groomingId, null, true); // petRemove 2
                quickBooksService.addRedisSyncGroomingData(businessId, groomingId, null); // petRemove 2
            }
        });
        return true;
    }

    void petRemoveWaitList(long companyId, Integer petId, Integer customerId, String timezone) {
        // 查询该宠物是否有waitList
        List<MoeGroomingAppointment> appointmentList = moeGroomingAppointmentMapper.queryWaitList(
                companyId, null, null, List.of(WaitListStatusEnum.WAITLISTONLY), List.of(customerId));
        if (CollectionUtils.isEmpty(appointmentList)) {
            return;
        }

        // 拉取 petDetail 信息
        Map<Integer, com.moego.lib.utils.model.Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>>
                appointmentDetailsMap = appointmentServiceDetailService.getByAppointments(appointmentList.stream()
                .map(MoeGroomingAppointment::getId)
                .toList());
        if (CollectionUtils.isEmpty(appointmentDetailsMap)) {
            return;
        }

        // 过滤出包含该 pet 的预约，需要区分仅包含与包含且不仅包含两种 case
        Pair<List<Integer>, List<Integer>> waitListGroomingIds =
                diffGroomingWithSinglePetMultiPet(appointmentDetailsMap, petId);
        Set<Integer> onlyThisPetGroomingIds = new HashSet<>(waitListGroomingIds.getLeft());
        Set<Integer> multiPetGroomingIds = new HashSet<>(waitListGroomingIds.getRight());

        var businessAppointments = appointmentList.stream()
                .collect(Collectors.groupingBy(MoeGroomingAppointment::getBusinessId, Collectors.toList()));

        for (var entry : businessAppointments.entrySet()) {
            var businessId = entry.getKey();
            var appointments = entry.getValue();

            // 只有该宠物的 wait list，直接删除
            List<Integer> onlyThisPetGroomingIdsInBusiness = appointments.stream()
                    .map(MoeGroomingAppointment::getId)
                    .filter(onlyThisPetGroomingIds::contains)
                    .toList();
            if (!CollectionUtils.isEmpty(onlyThisPetGroomingIdsInBusiness)) {
                waitListService.batchDeletePureWaitList(
                        businessId.longValue(), timezone, onlyThisPetGroomingIdsInBusiness);
            }

            // 删除waitList 中属于该 pet 的 petDetail
            List<Integer> multiPetGroomingIdsInBusiness = appointments.stream()
                    .map(MoeGroomingAppointment::getId)
                    .filter(multiPetGroomingIds::contains)
                    .toList();
            List<Integer> needRemovePetDetailIds = new ArrayList<>();
            for (Integer removePetDetailGroomingId : multiPetGroomingIdsInBusiness) {
                for (MoeGroomingPetDetail petDetail :
                        appointmentDetailsMap.get(removePetDetailGroomingId).key()) {
                    if (petDetail.getPetId().equals(petId)) {
                        needRemovePetDetailIds.add(petDetail.getId());
                    }
                }
            }
            // 删除petDetailList涉及到的petDetail
            deletePetDetail(needRemovePetDetailIds);
        }
    }

    /**
     * 将预约划分为两类：1. 仅包含该宠物  2. 包含且不仅包含该宠物
     * @param petId 当前操作的 petId
     * @return <onlyThisPetGroomingIdList, multiPetGroomingIdList>
     */
    Pair<List<Integer>, List<Integer>> diffGroomingWithSinglePetMultiPet(
            Map<Integer, com.moego.lib.utils.model.Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>>
                    details,
            Integer petId) {
        List<Integer> onlyThisPetGroomingIdList = new ArrayList<>();
        List<Integer> multPetGroomingIdList = new ArrayList<>();
        Pair<List<Integer>, List<Integer>> result = Pair.of(onlyThisPetGroomingIdList, multPetGroomingIdList);
        if (CollectionUtils.isEmpty(details)) {
            return result;
        }
        details.forEach((appointmentId, value) -> {
            List<MoeGroomingPetDetail> petDetailList = value.key();
            List<EvaluationServiceDetail> evaluationList = value.value();

            Set<Integer> petIds =
                    petDetailList.stream().map(MoeGroomingPetDetail::getPetId).collect(Collectors.toSet());
            petIds.addAll(
                    evaluationList.stream().map(k -> k.getPetId().intValue()).toList());
            if (!petIds.contains(petId)) {
                return;
            }
            if (petIds.size() == 1) {
                onlyThisPetGroomingIdList.add(appointmentId);
            } else {
                multPetGroomingIdList.add(appointmentId);
            }
        });
        return result;
    }

    private void updateGroomingTime(Integer groomingId) {
        // 修改之后计算预约开始和结束时间
        List<MoeGroomingPetDetail> petDetailList =
                moeGroomingPetDetailMapper.selectByAppointmentIdList(List.of(groomingId));

        boolean startAtSameTime = petDetailList.stream()
                        .collect(Collectors.toMap(
                                MoeGroomingPetDetail::getPetId, MoeGroomingPetDetail::getStartTime, Long::min))
                        .values()
                        .stream()
                        .distinct()
                        .count()
                == 1;

        MoeGroomingAppointment grooming = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);

        calculateServiceStartTimeAndEndTime(petDetailList, startAtSameTime, grooming.getAppointmentStartTime());

        petDetailList.forEach(service -> {
            MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
            petDetail.setId(service.getId());
            petDetail.setStartTime(service.getStartTime());
            petDetail.setEndTime(service.getEndTime());
            petDetail.setUpdateTime(DateUtil.get10Timestamp());
            moeGroomingPetDetailMapper.updateByPrimaryKeySelective(petDetail);
        });

        // 计算结束时间,取最大值作为预约预计结束时间
        long newAppointmentEndTime = petDetailList.stream()
                .mapToLong(service -> service.getStartTime() + service.getServiceTime())
                .summaryStatistics()
                .getMax();

        MoeGroomingAppointment moeGroomingAppointmentSaveBean = new MoeGroomingAppointment();
        moeGroomingAppointmentSaveBean.setId(groomingId);
        moeGroomingAppointmentSaveBean.setAppointmentEndTime(Math.toIntExact(newAppointmentEndTime));
        moeGroomingAppointmentSaveBean.setOldAppointmentEndTime(grooming.getAppointmentEndTime());
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointmentSaveBean);
    }

    /**
     * 根据 startAtSameTime 来计算每一个 service 的 start time 和 end time
     *
     * @param petDetailList
     * @param startAtSameTime
     * @param appointmentStartTime
     */
    private void calculateServiceStartTimeAndEndTime(
            List<MoeGroomingPetDetail> petDetailList, boolean startAtSameTime, int appointmentStartTime) {
        AtomicLong startTime = new AtomicLong(appointmentStartTime);
        if (startAtSameTime) {
            Map<Integer, Long> petIdStartTime = new HashMap<>(8);
            petDetailList.forEach(service -> {
                Long currentStartTime = petIdStartTime.getOrDefault(service.getPetId(), startTime.get());
                service.setStartTime(currentStartTime);
                Long currentEndTime = currentStartTime + service.getServiceTime();
                service.setEndTime(currentEndTime);
                petIdStartTime.put(service.getPetId(), currentEndTime);
            });
        } else {
            petDetailList.forEach(service -> {
                service.setStartTime(startTime.get());
                service.setEndTime(startTime.addAndGet(service.getServiceTime()));
            });
        }
    }

    /**
     * Get groomed pet ids.
     *
     * @param businessId businessId
     * @param staffId    staffId, can be null, if null, return all staff groomed pet ids
     * @param startDate  startDate
     * @param endDate    endDate
     * @return pet id list
     */
    public List<Integer> getGroomedPetIds(Integer businessId, Integer staffId, String startDate, String endDate) {
        return moeGroomingAppointmentMapper.getGroomedPetIds(businessId, staffId, startDate, endDate);
    }

    public List<GroomingPetServiceListInfoDTO> queryFinishedPetDetailByBusinessIdList(
            List<Integer> businessIdList, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(businessIdList)) {
            return List.of();
        }

        List<GroomingPetServiceListInfoDTO> result =
                moeGroomingPetDetailMapper.queryFinishedPetDetail(businessIdList, startDate, endDate);

        if (CollectionUtils.isEmpty(result)) {
            return List.of();
        }

        queryPetInfo(result);

        queryCustomerInfo(result, null);

        return result;
    }

    /**
     * 查询一段时间内的预约。左闭右开
     * @param startDate 查询结果包含 startDate
     * @param endDate 查询结果不包含 endDate
     * @return
     */
    public List<SmartScheduleGroomingDetailsDTO> getServiceList(
            Integer businessId, LocalDate startDate, LocalDate endDate, List<Integer> staffIdList) {
        if (CollectionUtils.isEmpty(staffIdList)) {
            return List.of();
        }

        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);

        return smartScheduleService
                .queryByBusinessIdBetweenDates(
                        businessId, startDate, endDate, staffIdList, businessInfo.getTimezoneName())
                .stream()
                .toList();
    }

    public Map<Integer, CustomerAddressDto> getCustomerAddress(
            List<SmartScheduleGroomingDetailsDTO> petDetailInfoList) {
        List<Integer> customerIds = petDetailInfoList.stream()
                .map(SmartScheduleGroomingDetailsDTO::getCustomerId)
                .filter(id -> id > 0) /* skip dummy customer for block appointment */
                .distinct()
                .toList();
        if (customerIds.isEmpty()) {
            return new HashMap<>();
        }
        return obAddressService.batchGetPrimaryAddress(customerIds);
    }

    /**
     * 结合 customer services 和 services 计算 time & price
     * @param petServiceList <petId, MoeGroomingService>
     * @return <<petId, serviceId>, <time, price>>
     */
    public Map<Pair<Integer, Integer>, Pair<Integer, BigDecimal>> getServiceTimeAndPrice(
            Integer businessId, List<Pair<Integer, MoeGroomingService>> petServiceList) {
        if (CollectionUtils.isEmpty(petServiceList)) {
            return new HashMap<>();
        }
        List<Integer> petIdList = petServiceList.stream().map(Pair::getLeft).collect(Collectors.toList());
        List<Integer> serviceIdList =
                petServiceList.stream().map(k -> k.getRight().getId()).collect(Collectors.toList());
        List<MoeGroomingCustomerServices> customerServicesList =
                customerService.getCustomerServiceList(businessId, petIdList, serviceIdList);
        Map<Pair<Integer, Integer>, BigDecimal> scopeTypePriceMap = buildScopeTypePriceMap(customerServicesList);
        Map<Pair<Integer, Integer>, Integer> scopeTypeTimeMap = buildScopeTypeTimeMap(customerServicesList);

        Map<Pair<Integer, Integer>, Pair<Integer, BigDecimal>> result = new HashMap<>();
        petServiceList.forEach(petService -> {
            MoeGroomingService moeGroomingService = petService.getRight();
            Pair<Integer, Integer> key =
                    Pair.of(petService.getLeft(), moeGroomingService.getId()); // <petId, serviceId>
            Pair<Integer, BigDecimal> timeAndPrice = Pair.of(
                    scopeTypeTimeMap.getOrDefault(key, moeGroomingService.getDuration()),
                    scopeTypePriceMap.getOrDefault(key, moeGroomingService.getPrice()));
            result.put(key, timeAndPrice);
        });
        return result;
    }

    /**
     * 用于同步 service 最新的 price & time
     * 目前使用在 waitList
     * @param petDetailsBatch <id, <petId, List<MoeGroomingPetDetail>>>
     */
    public void batchSyncNewestTimeAndPrice(
            Integer businessId, Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> petDetailsBatch) {
        if (CollectionUtils.isEmpty(petDetailsBatch)) {
            return;
        }
        List<Integer> serviceIdList = new ArrayList<>();
        List<Integer> petIdList = new ArrayList<>();
        for (Map<Integer, List<MoeGroomingPetDetail>> petDetails : petDetailsBatch.values()) {
            if (CollectionUtils.isEmpty(petDetails)) {
                continue;
            }
            for (List<MoeGroomingPetDetail> petDetailList : petDetails.values()) {
                if (CollectionUtils.isEmpty(petDetailList)) {
                    continue;
                }
                for (MoeGroomingPetDetail petDetail : petDetailList) {
                    if (petDetail.getServiceId() != null && petDetail.getServiceId() > 0) {
                        serviceIdList.add(petDetail.getServiceId());
                    }
                    if (petDetail.getPetId() != null && petDetail.getPetId() > 0) {
                        petIdList.add(petDetail.getPetId());
                    }
                }
            }
        }

        Map<Integer, MoeGroomingService> serviceMap = groomingServiceService.getServiceMap(businessId, serviceIdList);
        List<MoeGroomingCustomerServices> customerServicesList =
                customerService.getCustomerServiceList(businessId, petIdList, serviceIdList);
        Map<Pair<Integer, Integer>, BigDecimal> scopeTypePriceMap = buildScopeTypePriceMap(customerServicesList);
        Map<Pair<Integer, Integer>, Integer> scopeTypeTimeMap = buildScopeTypeTimeMap(customerServicesList);
        petDetailsBatch
                .values()
                .forEach(petDetails ->
                        syncNewestTimeAndPrice(petDetails, serviceMap, scopeTypePriceMap, scopeTypeTimeMap));
    }

    /**
     * @param petDetails <petId, List<MoeGroomingPetDetail>>
     * @param serviceMap <serviceId, MoeGroomingService>
     * @param scopeTypePriceMap <<petId, serviceId>, price>
     * @param scopeTypeTimeMap <<petId, serviceId>, time>
     */
    public void syncNewestTimeAndPrice(
            Map<Integer, List<MoeGroomingPetDetail>> petDetails,
            Map<Integer, MoeGroomingService> serviceMap,
            Map<Pair<Integer, Integer>, BigDecimal> scopeTypePriceMap,
            Map<Pair<Integer, Integer>, Integer> scopeTypeTimeMap) {
        petDetails.forEach((petId, petDetailList) -> {
            petDetailList.sort(Comparator.comparing(MoeGroomingPetDetail::getStartTime));
            Integer diff = 0;
            for (MoeGroomingPetDetail petDetail : petDetailList) {
                if (petDetail.getServiceId() == null || petDetail.getServiceId() <= 0) {
                    return;
                }
                Integer oldServiceTime = petDetail.getServiceTime();
                MoeGroomingService moeGroomingService = serviceMap.get(petDetail.getServiceId());
                // 脏数据兼容
                if (moeGroomingService == null) {
                    return;
                }
                petDetail.setServicePrice(moeGroomingService.getPrice());
                petDetail.setServiceTime(moeGroomingService.getDuration());
                Pair<Integer, Integer> key =
                        Pair.of(petDetail.getPetId(), petDetail.getServiceId()); // <petId, serviceId>
                if (scopeTypeTimeMap.containsKey(key)) {
                    petDetail.setServiceTime(scopeTypeTimeMap.get(key));
                    petDetail.setDurationOverrideType(ServiceOverrideType.CLIENT);
                }
                if (scopeTypePriceMap.containsKey(key)) {
                    petDetail.setServicePrice(scopeTypePriceMap.get(key));
                    petDetail.setPriceOverrideType(ServiceOverrideType.CLIENT);
                }
                petDetail.setStartTime(petDetail.getStartTime() + diff);
                petDetail.setEndTime(petDetail.getStartTime() + petDetail.getServiceTime());
                diff += petDetail.getServiceTime() - oldServiceTime;
            }
        });
    }

    public boolean isAllPetsStartAtSameTime(List<GroomingPetInfoDetailDTO> petDetailList) {
        if (CollectionUtils.isEmpty(petDetailList)) {
            return false;
        }
        // single pet 的预约 startAtSameTime 开关设置为 false
        boolean isSinglePet = petDetailList.stream()
                        .map(GroomingPetInfoDetailDTO::getPetId)
                        .distinct()
                        .count()
                == 1;
        if (isSinglePet) {
            return false;
        }

        return petDetailList.stream()
                        .filter(pet -> !CollectionUtils.isEmpty(pet.getGroomingPetServiceDTOS()))
                        .map(pet -> pet.getGroomingPetServiceDTOS().stream()
                                .min(Comparator.comparing(GroomingPetServiceDTO::getStartTime))
                                .get()
                                .getStartTime())
                        .distinct()
                        .count()
                == 1;
    }

    // TODO: single pet 的预约 startAtSameTime 开关设置为 false，和前端对齐后修改，考虑复用 AppointmentUtil#isAllPetsStartAtSameTime
    public boolean isAllPetsStartAtSameTimeV2(Map<Integer, List<MoeGroomingPetDetail>> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return false;
        }
        return petDetails.values().stream()
                        .filter(moeGroomingPetDetails -> !CollectionUtils.isEmpty(moeGroomingPetDetails))
                        .map(moeGroomingPetDetails -> moeGroomingPetDetails.stream()
                                .min(Comparator.comparing(MoeGroomingPetDetail::getStartTime))
                                .get()
                                .getStartTime())
                        .distinct()
                        .count()
                == 1;
    }

    // TODO: single pet 的预约 startAtSameTime 开关设置为 false，和前端对齐后修改，考虑复用 AppointmentUtil#isAllPetsStartAtSameTime
    public boolean isAllPetsStartAtSameTimeV3(List<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return false;
        }
        Map<Integer, List<MoeGroomingPetDetail>> petDetailMap =
                petDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId));
        return isAllPetsStartAtSameTimeV2(petDetailMap);
    }

    /**
     * @return <<petId, serviceId>, price>
     */
    Map<Pair<Integer, Integer>, BigDecimal> buildScopeTypePriceMap(
            final List<MoeGroomingCustomerServices> customerServicesList) {
        return customerServicesList.stream()
                .filter(customerService -> Objects.nonNull(customerService.getPetId())
                        && Objects.nonNull(customerService.getServiceId())
                        && Objects.nonNull(customerService.getSaveType())
                        && ServiceEnum.SAVE_TYPE_PRICE.equals(customerService.getSaveType()))
                .collect(Collectors.toMap(
                        customerService -> Pair.of(customerService.getPetId(), customerService.getServiceId()),
                        MoeGroomingCustomerServices::getServiceFee,
                        (v1, v2) -> v1));
    }

    /**
     * @return <<petId, serviceId>, time>
     */
    Map<Pair<Integer, Integer>, Integer> buildScopeTypeTimeMap(
            final List<MoeGroomingCustomerServices> customerServicesList) {
        return customerServicesList.stream()
                .filter(customerService -> Objects.nonNull(customerService.getPetId())
                        && Objects.nonNull(customerService.getServiceId())
                        && Objects.nonNull(customerService.getSaveType())
                        && ServiceEnum.SAVE_TYPE_TIME.equals(customerService.getSaveType()))
                .collect(Collectors.toMap(
                        customerService -> Pair.of(customerService.getPetId(), customerService.getServiceId()),
                        MoeGroomingCustomerServices::getServiceTime,
                        (v1, v2) -> v1));
    }

    /**
     * waitList 创建场景用到
     * @return list of pet detail
     */
    List<MoeGroomingPetDetail> buildPetDetailWithoutService(List<PetParams> petParams) {
        List<MoeGroomingPetDetail> petDetailList = new ArrayList<>();
        Long now = CommonUtil.get10Timestamp();
        petParams.stream().filter(k -> CollectionUtils.isEmpty(k.serviceList())).forEach(pet -> {
            MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
            petDetail.setPetId(pet.petId());
            petDetail.setUpdateTime(now);
            petDetail.setScopeTypePrice(ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType());
            petDetail.setScopeTypeTime(ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType());
            petDetailList.add(petDetail);
        });
        return petDetailList;
    }

    /**
     * @param petParams              detail of pet, from front end
     * @param businessId             business id
     * @param startTime              earliest start time of all pets
     * @param allPetsStartAtSameTime whether all pets start at same time
     * @return list of pet detail
     */
    List<MoeGroomingPetDetail> buildPetDetailList(
            List<PetParams> petParams,
            Integer businessId,
            String date,
            Integer startTime,
            Boolean allPetsStartAtSameTime) {
        List<MoeGroomingPetDetail> petDetailList = new ArrayList<>();

        // 获取 business service 属性
        List<Integer> serviceIdList = petParams.stream()
                .flatMap(pet -> pet.serviceList().stream().map(ServiceAndOperationParams::serviceId))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(serviceIdList)) {
            return petDetailList;
        }
        Map<Integer, MoeGroomingService> serviceMap = groomingServiceService.getServiceMap(businessId, serviceIdList);

        // 查询所有宠物的服务时间和价格
        List<Pair<Integer, MoeGroomingService>> petServiceList = new ArrayList<>();
        petParams.forEach(pet -> pet.serviceList().forEach(service -> {
            if (!serviceMap.containsKey(service.serviceId())) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, String.format("serviceId %d not exist", service.serviceId()));
            }
            petServiceList.add(Pair.of(pet.petId(), serviceMap.get(service.serviceId())));
        }));
        Map<Pair<Integer, Integer>, Pair<Integer, BigDecimal>> allServiceTimeAndPrice =
                getServiceTimeAndPrice(businessId, petServiceList);

        // 构建 MoeGroomingPetDetail
        Long now = CommonUtil.get10Timestamp();
        AtomicReference<Integer> nextStartTime = new AtomicReference<>(startTime);
        petParams.forEach(pet -> {
            if (Objects.nonNull(allPetsStartAtSameTime) && allPetsStartAtSameTime) {
                nextStartTime.set(startTime);
            }
            pet.serviceList().forEach(service -> {
                MoeGroomingService moeGroomingService = serviceMap.get(service.serviceId());
                Pair<Integer, BigDecimal> recordServiceTimeAndPrice =
                        allServiceTimeAndPrice.get(Pair.of(pet.petId(), moeGroomingService.getId()));

                MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
                petDetail.setPetId(pet.petId());
                petDetail.setServiceId(service.serviceId());
                petDetail.setStaffId(service.staffId());
                petDetail.setServiceType(moeGroomingService.getType().intValue());
                petDetail.setServicePrice(
                        Objects.nonNull(service.servicePrice())
                                ? service.servicePrice()
                                : recordServiceTimeAndPrice.getRight());
                petDetail.setServiceTime(
                        Objects.nonNull(service.serviceTime())
                                ? service.serviceTime()
                                : recordServiceTimeAndPrice.getLeft());
                petDetail.setStartDate(date);
                petDetail.setEndDate(date);
                petDetail.setStartTime(nextStartTime.get().longValue());
                petDetail.setEndTime(nextStartTime.get().longValue() + petDetail.getServiceTime());
                nextStartTime.updateAndGet(v -> v + petDetail.getServiceTime());
                petDetail.setScopeTypePrice(
                        (Objects.nonNull(service.scopeTypePrice())
                                        && !ScopeModifyTypeEnum.DO_NOT_SAVE.equals(service.scopeTypePrice()))
                                ? ScopeModifyTypeEnum.THIS_FUTURE.getScopeType()
                                : ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType());
                petDetail.setScopeTypeTime(
                        (Objects.nonNull(service.serviceTime())
                                        && !ScopeModifyTypeEnum.DO_NOT_SAVE.equals(service.scopeTypeTime()))
                                ? ScopeModifyTypeEnum.THIS_FUTURE.getScopeType()
                                : ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType());
                petDetail.setUpdateTime(now);
                if (Objects.nonNull(service.enableOperation()) && service.enableOperation()) {
                    petDetail.setWorkMode(service.workMode());
                    petDetail.setEnableOperation(true);
                }
                petDetailList.add(petDetail);
            });
        });
        return petDetailList;
    }

    /**
     * 适用于 petDetail 变更发生，需要重新构建预约的 petDetail 时。
     * 比如更新 startTime、更新 allPetsStartAtSameTime、更新 petDetails
     */
    List<MoeGroomingPetDetail> reBuildPetDetail(
            Map<Integer, List<MoeGroomingPetDetail>> petDetailMap,
            String startDate,
            Integer startTime,
            Boolean allPetsStartAtSameTime) {
        List<MoeGroomingPetDetail> result = new ArrayList<>();
        AtomicReference<Integer> nextStartTime = new AtomicReference<>(startTime);

        petDetailMap.forEach((petId, petDetailList) -> {
            if (Boolean.TRUE.equals(allPetsStartAtSameTime)) {
                nextStartTime.set(startTime);
            }
            petDetailList.forEach(petDetail -> {
                petDetail.setStartDate(startDate);
                petDetail.setEndDate(startDate);
                petDetail.setStartTime(nextStartTime.get().longValue());
                petDetail.setEndTime(nextStartTime.get().longValue() + petDetail.getServiceTime());
                nextStartTime.updateAndGet(v -> v + petDetail.getServiceTime());
                result.add(petDetail);
            });
        });
        return result;
    }

    public Integer calculateServiceDuration(List<MoeGroomingPetDetail> service) {
        if (CollectionUtils.isEmpty(service)) {
            return 0;
        }
        // 过滤掉没有确定 service 的 petService
        service = service.stream()
                .filter(k -> k.getServiceId() != null && k.getServiceId() > 0)
                .toList();
        Long minStartTime = service.stream()
                .map(MoeGroomingPetDetail::getStartTime)
                .min(Long::compareTo)
                .orElse(0L);
        Long maxEndTime = service.stream()
                .map(MoeGroomingPetDetail::getEndTime)
                .max(Long::compareTo)
                .orElse(0L);
        return maxEndTime.intValue() - minStartTime.intValue();
    }

    /**
     * 查询 staff 预约列表，用于 ss 查询驾驶信息
     */
    public List<SmartScheduleGroomingDetailsDTO> getApptServiceListForSmartSchedule(
            Integer businessId, LocalDate startDate, LocalDate endDate, List<Integer> staffIdList) {
        if (CollectionUtils.isEmpty(staffIdList) || startDate == null) {
            return List.of();
        }

        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);

        Map<Integer, List<SmartScheduleGroomingDetailsDTO>> staffServiceListMap = smartScheduleService
                .queryByBusinessIdBetweenDates(
                        businessId, startDate, endDate, staffIdList, businessInfo.getTimezoneName())
                .stream()
                .collect(Collectors.groupingBy(SmartScheduleGroomingDetailsDTO::getStaffId));

        List<SmartScheduleGroomingDetailsDTO> allServiceList = new ArrayList<>();

        // 同一个预约的 pet detail 合并成一个
        staffServiceListMap.forEach((key, value) -> allServiceList.addAll(value.stream()
                .collect(Collectors.toMap(
                        SmartScheduleGroomingDetailsDTO::getGroomingId, Function.identity(), (a, b) -> {
                            long minStartTime = Math.min(a.getStartTime(), b.getStartTime());
                            long maxEndTime = Math.max(a.getEndTime(), b.getEndTime());
                            a.setStartTime(minStartTime);
                            a.setEndTime(maxEndTime);
                            return a;
                        }))
                .values()));
        // 按预约开始时间升序
        allServiceList.sort(Comparator.comparing(SmartScheduleGroomingDetailsDTO::getStartTime));
        return allServiceList;
    }

    public Map<Integer, List<Integer>> getGroomingStaffIdListMap(List<Integer> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Map.of();
        }
        List<GroomingStaffIdListPO> groomingStaffIdList =
                moeGroomingPetDetailMapper.queryStaffIdByGroomingIds(appointmentIds);
        groomingStaffIdList.addAll(groomingServiceOperationService.queryStaffIdByGroomingIds(appointmentIds));
        return groomingStaffIdList.stream()
                .collect(Collectors.toMap(
                        GroomingStaffIdListPO::getGroomingId,
                        GroomingStaffIdListPO::getStaffIdList,
                        (k1, k2) -> Stream.of(k1, k2)
                                .flatMap(Collection::stream)
                                .distinct()
                                .toList()));
    }

    Map<Integer, CustomerPetDetailDTO> getCustomerPetDetail(List<MoeGroomingPetDetail> petDetails) {
        List<Integer> petIds = petDetails.stream()
                .map(MoeGroomingPetDetail::getPetId)
                .filter(Objects::nonNull)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(petIds)) {
            return new HashMap<>();
        }
        return iPetServiceClient.getCustomerPetListByIdList(petIds).stream()
                .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity()));
    }

    /**
     * 根据id列表查询GroomingPetDetailDTO列表
     *
     * @param petDetailIdList pet detail id列表
     * @return List<GroomingPetDetailDTO>
     */
    public List<GroomingPetDetailDTO> queryPetDetailDTOByIdList(List<Long> petDetailIdList) {
        if (CollectionUtils.isEmpty(petDetailIdList)) {
            return List.of();
        }
        return moeGroomingPetDetailMapper.queryNormalPetDetailDTOByIds(petDetailIdList);
    }

    @Transactional
    public void updatePetDetailStaff(EditPetDetailStaffCommissionParam params) {

        List<EditPetDetailStaffCommissionParam.EditPetDetailStaffCommissionItem> editStaffCommissionItemsList =
                params.getEditPetDetailStaffCommissionItemList().stream()
                        .filter(item ->
                                OrderItemType.ITEM_TYPE_SERVICE.getType().equalsIgnoreCase(item.getOrderItemType()))
                        .toList();
        if (CollectionUtils.isEmpty(editStaffCommissionItemsList)) {
            return;
        }
        for (EditPetDetailStaffCommissionParam.EditPetDetailStaffCommissionItem editStaffCommissionItem :
                editStaffCommissionItemsList) {

            List<EditPetDetailStaffCommissionParam.EditPetDetailStaffCommissionOperationItem> operationList =
                    editStaffCommissionItem.getOperationItemList();
            long petDetailId = editStaffCommissionItem.getPetDetailId();
            MoeGroomingPetDetail petDetailFromDB =
                    moeGroomingPetDetailMapper.selectByPrimaryKey(Math.toIntExact(petDetailId));
            if (petDetailFromDB == null) {
                continue;
            }
            long businessId = editStaffCommissionItem.getBusinessId();
            long staffId = editStaffCommissionItem.getStaffId();
            long companyId = editStaffCommissionItem.getCompanyId();
            MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
            petDetail.setId(petDetailFromDB.getId());
            petDetail.setStaffId(Math.toIntExact(staffId));
            moeGroomingPetDetailMapper.updateByPrimaryKeySelective(petDetail);
            // 删除原来的
            groomingServiceOperationService.deleteByPetDetailId(petDetailId);
            List<MoeGroomingServiceOperation> needAddOperationList = operationList.stream()
                    .map(operation -> {
                        MoeGroomingServiceOperation needAddOperation = new MoeGroomingServiceOperation();
                        needAddOperation.setBusinessId(Math.toIntExact(businessId));
                        needAddOperation.setGroomingId(petDetailFromDB.getGroomingId());
                        needAddOperation.setGroomingServiceId(petDetailFromDB.getId());
                        needAddOperation.setPetId(petDetailFromDB.getPetId());
                        needAddOperation.setStaffId(Math.toIntExact(operation.getStaffId()));
                        needAddOperation.setOperationName(operation.getOperationName());
                        needAddOperation.setStartTime(Math.toIntExact(petDetailFromDB.getStartTime()));
                        needAddOperation.setDuration(operation.getDuration());
                        needAddOperation.setComment("");
                        BigDecimal priceRatio = BigDecimal.valueOf(operation.getRatio());
                        needAddOperation.setPriceRatio(priceRatio);
                        needAddOperation.setPrice(
                                petDetailFromDB.getServicePrice().multiply(priceRatio));
                        needAddOperation.setStatus(true);
                        needAddOperation.setCompanyId(companyId);
                        return needAddOperation;
                    })
                    .toList();
            groomingServiceOperationService.batchInsertOperation(needAddOperationList);
        }
    }

    /**
     * 根据pet detail id列表查询
     *
     * @param petDetailIdList pet detail id列表
     * @return List<MoeGroomingPetDetail>
     */
    public List<MoeGroomingPetDetail> queryByPetDetailIds(List<Long> petDetailIdList) {
        if (CollectionUtils.isEmpty(petDetailIdList)) {
            return List.of();
        }
        return moeGroomingPetDetailMapper.selectNormalPetDetailByIdList(petDetailIdList);
    }
}
