package com.moego.server.grooming.mapstruct;

import com.moego.common.params.VaccineParams;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO;
import com.moego.server.grooming.web.vo.ob.OBPetDetailVO;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/3
 */
@Mapper(imports = JsonUtil.class)
public interface VaccineMapper {
    VaccineMapper INSTANCE = Mappers.getMapper(VaccineMapper.class);

    List<OBPetDetailVO.OBVaccineDetailVO> dto2VO(List<VaccineBindingRecordDto> list);

    OBPetDetailVO.OBVaccineDetailVO dto2VO(VaccineBindingRecordDto dto);

    List<VaccineBindingRecordDto> params2DTO(List<VaccineParams> params);

    @Mappings({
        @Mapping(target = "vaccineName", ignore = true),
        @Mapping(target = "documentUrls", expression = "java(vaccine2String(param))"),
    })
    VaccineBindingRecordDto params2DTO(VaccineParams param);

    /**
     * 兼容原本接口单个 document，优先取新增的多个 document urls
     *
     * @param param vaccine params
     * @return document url array
     */
    default List<String> vaccine2String(VaccineParams param) {
        if (Objects.isNull(param)) {
            return null;
        }
        if (!CollectionUtils.isEmpty(param.getDocumentUrls())) {
            return param.getDocumentUrls();
        }
        if (StringUtils.hasText(param.getVaccineDocument())) {
            return List.of(param.getVaccineDocument());
        }
        return List.of();
    }

    List<AbandonClientRecordVO.AbandonPetDetailVO.VaccineDetailVO> dtoAbandonVaccineDetailVO(
            List<VaccineBindingRecordDto> dtoList);

    default AbandonClientRecordVO.AbandonPetDetailVO.VaccineDetailVO dtoAbandonVaccineDetailVO(
            VaccineBindingRecordDto dto) {
        return AbandonClientRecordVO.AbandonPetDetailVO.VaccineDetailVO.builder()
                .vaccineId(dto.getVaccineId())
                .expirationDate(dto.getExpirationDate())
                .documentUrls(dto.getDocumentUrls())
                .vaccineDocument(
                        !CollectionUtils.isEmpty(dto.getDocumentUrls())
                                ? dto.getDocumentUrls().get(0)
                                : null)
                .build();
    }
}
