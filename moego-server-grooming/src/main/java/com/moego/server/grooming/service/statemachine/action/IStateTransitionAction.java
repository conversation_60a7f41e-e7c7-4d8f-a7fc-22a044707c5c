package com.moego.server.grooming.service.statemachine.action;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.statemachine.context.ActionContext;

public interface IStateTransitionAction {
    boolean suit(AppointmentStatusEnum newStatus);

    int execute(MoeGroomingAppointment moeGroomingAppointment, ActionContext actionContext);

    int revert(MoeGroomingAppointment moeGroomingAppointment);
}
