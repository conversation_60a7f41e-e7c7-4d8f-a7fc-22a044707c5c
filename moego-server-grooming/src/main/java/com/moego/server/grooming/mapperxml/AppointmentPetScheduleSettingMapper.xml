<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.AppointmentPetScheduleSettingMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="appointment_id" jdbcType="BIGINT" property="appointmentId" />
    <result column="schedule_type" jdbcType="INTEGER" property="scheduleType" />
    <result column="schedule_id" jdbcType="BIGINT" property="scheduleId" />
    <result column="schedule_time" jdbcType="INTEGER" property="scheduleTime" />
    <result column="schedule_extra_json" jdbcType="CHAR" property="scheduleExtraJson" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, appointment_id, schedule_type, schedule_id, schedule_time, schedule_extra_json
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetScheduleSettingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from appointment_pet_schedule_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from appointment_pet_schedule_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from appointment_pet_schedule_setting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetScheduleSettingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from appointment_pet_schedule_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_pet_schedule_setting (company_id, appointment_id, schedule_type, 
      schedule_id, schedule_time, schedule_extra_json
      )
    values (#{companyId,jdbcType=BIGINT}, #{appointmentId,jdbcType=BIGINT}, #{scheduleType,jdbcType=INTEGER}, 
      #{scheduleId,jdbcType=BIGINT}, #{scheduleTime,jdbcType=INTEGER}, #{scheduleExtraJson,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_pet_schedule_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="appointmentId != null">
        appointment_id,
      </if>
      <if test="scheduleType != null">
        schedule_type,
      </if>
      <if test="scheduleId != null">
        schedule_id,
      </if>
      <if test="scheduleTime != null">
        schedule_time,
      </if>
      <if test="scheduleExtraJson != null">
        schedule_extra_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appointmentId != null">
        #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="scheduleType != null">
        #{scheduleType,jdbcType=INTEGER},
      </if>
      <if test="scheduleId != null">
        #{scheduleId,jdbcType=BIGINT},
      </if>
      <if test="scheduleTime != null">
        #{scheduleTime,jdbcType=INTEGER},
      </if>
      <if test="scheduleExtraJson != null">
        #{scheduleExtraJson,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetScheduleSettingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from appointment_pet_schedule_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_schedule_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.appointmentId != null">
        appointment_id = #{record.appointmentId,jdbcType=BIGINT},
      </if>
      <if test="record.scheduleType != null">
        schedule_type = #{record.scheduleType,jdbcType=INTEGER},
      </if>
      <if test="record.scheduleId != null">
        schedule_id = #{record.scheduleId,jdbcType=BIGINT},
      </if>
      <if test="record.scheduleTime != null">
        schedule_time = #{record.scheduleTime,jdbcType=INTEGER},
      </if>
      <if test="record.scheduleExtraJson != null">
        schedule_extra_json = #{record.scheduleExtraJson,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_schedule_setting
    set id = #{record.id,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      appointment_id = #{record.appointmentId,jdbcType=BIGINT},
      schedule_type = #{record.scheduleType,jdbcType=INTEGER},
      schedule_id = #{record.scheduleId,jdbcType=BIGINT},
      schedule_time = #{record.scheduleTime,jdbcType=INTEGER},
      schedule_extra_json = #{record.scheduleExtraJson,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_schedule_setting
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appointmentId != null">
        appointment_id = #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="scheduleType != null">
        schedule_type = #{scheduleType,jdbcType=INTEGER},
      </if>
      <if test="scheduleId != null">
        schedule_id = #{scheduleId,jdbcType=BIGINT},
      </if>
      <if test="scheduleTime != null">
        schedule_time = #{scheduleTime,jdbcType=INTEGER},
      </if>
      <if test="scheduleExtraJson != null">
        schedule_extra_json = #{scheduleExtraJson,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update appointment_pet_schedule_setting
    set company_id = #{companyId,jdbcType=BIGINT},
      appointment_id = #{appointmentId,jdbcType=BIGINT},
      schedule_type = #{scheduleType,jdbcType=INTEGER},
      schedule_id = #{scheduleId,jdbcType=BIGINT},
      schedule_time = #{scheduleTime,jdbcType=INTEGER},
      schedule_extra_json = #{scheduleExtraJson,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>