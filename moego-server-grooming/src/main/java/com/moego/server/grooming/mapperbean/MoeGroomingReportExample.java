package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeGroomingReportExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public MoeGroomingReportExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdIsNull() {
            addCriterion("grooming_id is null");
            return (Criteria) this;
        }

        public Criteria andGroomingIdIsNotNull() {
            addCriterion("grooming_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroomingIdEqualTo(Integer value) {
            addCriterion("grooming_id =", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdNotEqualTo(Integer value) {
            addCriterion("grooming_id <>", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdGreaterThan(Integer value) {
            addCriterion("grooming_id >", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("grooming_id >=", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdLessThan(Integer value) {
            addCriterion("grooming_id <", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdLessThanOrEqualTo(Integer value) {
            addCriterion("grooming_id <=", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdIn(List<Integer> values) {
            addCriterion("grooming_id in", values, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdNotIn(List<Integer> values) {
            addCriterion("grooming_id not in", values, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdBetween(Integer value1, Integer value2) {
            addCriterion("grooming_id between", value1, value2, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdNotBetween(Integer value1, Integer value2) {
            addCriterion("grooming_id not between", value1, value2, "groomingId");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNull() {
            addCriterion("pet_id is null");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNotNull() {
            addCriterion("pet_id is not null");
            return (Criteria) this;
        }

        public Criteria andPetIdEqualTo(Integer value) {
            addCriterion("pet_id =", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotEqualTo(Integer value) {
            addCriterion("pet_id <>", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThan(Integer value) {
            addCriterion("pet_id >", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("pet_id >=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThan(Integer value) {
            addCriterion("pet_id <", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThanOrEqualTo(Integer value) {
            addCriterion("pet_id <=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdIn(List<Integer> values) {
            addCriterion("pet_id in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotIn(List<Integer> values) {
            addCriterion("pet_id not in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdBetween(Integer value1, Integer value2) {
            addCriterion("pet_id between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotBetween(Integer value1, Integer value2) {
            addCriterion("pet_id not between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdIsNull() {
            addCriterion("pet_type_id is null");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdIsNotNull() {
            addCriterion("pet_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdEqualTo(Integer value) {
            addCriterion("pet_type_id =", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdNotEqualTo(Integer value) {
            addCriterion("pet_type_id <>", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdGreaterThan(Integer value) {
            addCriterion("pet_type_id >", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("pet_type_id >=", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdLessThan(Integer value) {
            addCriterion("pet_type_id <", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdLessThanOrEqualTo(Integer value) {
            addCriterion("pet_type_id <=", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdIn(List<Integer> values) {
            addCriterion("pet_type_id in", values, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdNotIn(List<Integer> values) {
            addCriterion("pet_type_id not in", values, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdBetween(Integer value1, Integer value2) {
            addCriterion("pet_type_id between", value1, value2, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("pet_type_id not between", value1, value2, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andUuidIsNull() {
            addCriterion("uuid is null");
            return (Criteria) this;
        }

        public Criteria andUuidIsNotNull() {
            addCriterion("uuid is not null");
            return (Criteria) this;
        }

        public Criteria andUuidEqualTo(String value) {
            addCriterion("uuid =", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidNotEqualTo(String value) {
            addCriterion("uuid <>", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidGreaterThan(String value) {
            addCriterion("uuid >", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidGreaterThanOrEqualTo(String value) {
            addCriterion("uuid >=", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidLessThan(String value) {
            addCriterion("uuid <", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidLessThanOrEqualTo(String value) {
            addCriterion("uuid <=", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidLike(String value) {
            addCriterion("uuid like", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidNotLike(String value) {
            addCriterion("uuid not like", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidIn(List<String> values) {
            addCriterion("uuid in", values, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidNotIn(List<String> values) {
            addCriterion("uuid not in", values, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidBetween(String value1, String value2) {
            addCriterion("uuid between", value1, value2, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidNotBetween(String value1, String value2) {
            addCriterion("uuid not between", value1, value2, "uuid");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeIsNull() {
            addCriterion("template_publish_time is null");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeIsNotNull() {
            addCriterion("template_publish_time is not null");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeEqualTo(Date value) {
            addCriterion("template_publish_time =", value, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeNotEqualTo(Date value) {
            addCriterion("template_publish_time <>", value, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeGreaterThan(Date value) {
            addCriterion("template_publish_time >", value, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("template_publish_time >=", value, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeLessThan(Date value) {
            addCriterion("template_publish_time <", value, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeLessThanOrEqualTo(Date value) {
            addCriterion("template_publish_time <=", value, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeIn(List<Date> values) {
            addCriterion("template_publish_time in", values, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeNotIn(List<Date> values) {
            addCriterion("template_publish_time not in", values, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeBetween(Date value1, Date value2) {
            addCriterion("template_publish_time between", value1, value2, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplatePublishTimeNotBetween(Date value1, Date value2) {
            addCriterion("template_publish_time not between", value1, value2, "templatePublishTime");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonIsNull() {
            addCriterion("template_json is null");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonIsNotNull() {
            addCriterion("template_json is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonEqualTo(String value) {
            addCriterion("template_json =", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonNotEqualTo(String value) {
            addCriterion("template_json <>", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonGreaterThan(String value) {
            addCriterion("template_json >", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonGreaterThanOrEqualTo(String value) {
            addCriterion("template_json >=", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonLessThan(String value) {
            addCriterion("template_json <", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonLessThanOrEqualTo(String value) {
            addCriterion("template_json <=", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonLike(String value) {
            addCriterion("template_json like", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonNotLike(String value) {
            addCriterion("template_json not like", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonIn(List<String> values) {
            addCriterion("template_json in", values, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonNotIn(List<String> values) {
            addCriterion("template_json not in", values, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonBetween(String value1, String value2) {
            addCriterion("template_json between", value1, value2, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonNotBetween(String value1, String value2) {
            addCriterion("template_json not between", value1, value2, "templateJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonIsNull() {
            addCriterion("content_json is null");
            return (Criteria) this;
        }

        public Criteria andContentJsonIsNotNull() {
            addCriterion("content_json is not null");
            return (Criteria) this;
        }

        public Criteria andContentJsonEqualTo(String value) {
            addCriterion("content_json =", value, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonNotEqualTo(String value) {
            addCriterion("content_json <>", value, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonGreaterThan(String value) {
            addCriterion("content_json >", value, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonGreaterThanOrEqualTo(String value) {
            addCriterion("content_json >=", value, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonLessThan(String value) {
            addCriterion("content_json <", value, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonLessThanOrEqualTo(String value) {
            addCriterion("content_json <=", value, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonLike(String value) {
            addCriterion("content_json like", value, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonNotLike(String value) {
            addCriterion("content_json not like", value, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonIn(List<String> values) {
            addCriterion("content_json in", values, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonNotIn(List<String> values) {
            addCriterion("content_json not in", values, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonBetween(String value1, String value2) {
            addCriterion("content_json between", value1, value2, "contentJson");
            return (Criteria) this;
        }

        public Criteria andContentJsonNotBetween(String value1, String value2) {
            addCriterion("content_json not between", value1, value2, "contentJson");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeIsNull() {
            addCriterion("submitted_time is null");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeIsNotNull() {
            addCriterion("submitted_time is not null");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeEqualTo(Date value) {
            addCriterion("submitted_time =", value, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeNotEqualTo(Date value) {
            addCriterion("submitted_time <>", value, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeGreaterThan(Date value) {
            addCriterion("submitted_time >", value, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("submitted_time >=", value, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeLessThan(Date value) {
            addCriterion("submitted_time <", value, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeLessThanOrEqualTo(Date value) {
            addCriterion("submitted_time <=", value, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeIn(List<Date> values) {
            addCriterion("submitted_time in", values, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeNotIn(List<Date> values) {
            addCriterion("submitted_time not in", values, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeBetween(Date value1, Date value2) {
            addCriterion("submitted_time between", value1, value2, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andSubmittedTimeNotBetween(Date value1, Date value2) {
            addCriterion("submitted_time not between", value1, value2, "submittedTime");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountIsNull() {
            addCriterion("link_opened_count is null");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountIsNotNull() {
            addCriterion("link_opened_count is not null");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountEqualTo(Integer value) {
            addCriterion("link_opened_count =", value, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountNotEqualTo(Integer value) {
            addCriterion("link_opened_count <>", value, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountGreaterThan(Integer value) {
            addCriterion("link_opened_count >", value, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("link_opened_count >=", value, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountLessThan(Integer value) {
            addCriterion("link_opened_count <", value, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountLessThanOrEqualTo(Integer value) {
            addCriterion("link_opened_count <=", value, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountIn(List<Integer> values) {
            addCriterion("link_opened_count in", values, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountNotIn(List<Integer> values) {
            addCriterion("link_opened_count not in", values, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountBetween(Integer value1, Integer value2) {
            addCriterion("link_opened_count between", value1, value2, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andLinkOpenedCountNotBetween(Integer value1, Integer value2) {
            addCriterion("link_opened_count not between", value1, value2, "linkOpenedCount");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Integer value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Integer value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Integer value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Integer value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Integer value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Integer> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Integer> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Integer value1, Integer value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Integer value1, Integer value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andThemeCodeIsNull() {
            addCriterion("theme_code is null");
            return (Criteria) this;
        }

        public Criteria andThemeCodeIsNotNull() {
            addCriterion("theme_code is not null");
            return (Criteria) this;
        }

        public Criteria andThemeCodeEqualTo(String value) {
            addCriterion("theme_code =", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeNotEqualTo(String value) {
            addCriterion("theme_code <>", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeGreaterThan(String value) {
            addCriterion("theme_code >", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("theme_code >=", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeLessThan(String value) {
            addCriterion("theme_code <", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeLessThanOrEqualTo(String value) {
            addCriterion("theme_code <=", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeLike(String value) {
            addCriterion("theme_code like", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeNotLike(String value) {
            addCriterion("theme_code not like", value, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeIn(List<String> values) {
            addCriterion("theme_code in", values, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeNotIn(List<String> values) {
            addCriterion("theme_code not in", values, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeBetween(String value1, String value2) {
            addCriterion("theme_code between", value1, value2, "themeCode");
            return (Criteria) this;
        }

        public Criteria andThemeCodeNotBetween(String value1, String value2) {
            addCriterion("theme_code not between", value1, value2, "themeCode");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_report
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
