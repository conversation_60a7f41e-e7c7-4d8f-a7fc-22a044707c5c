package com.moego.server.grooming.service;

import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.notification.v1.AppPushDef;
import com.moego.idl.models.notification.v1.AppointmentBookedDef;
import com.moego.idl.models.notification.v1.AppointmentCancelledDef;
import com.moego.idl.models.notification.v1.AppointmentRescheduledDef;
import com.moego.idl.models.notification.v1.AskForReviewDef;
import com.moego.idl.models.notification.v1.BookingRequestAcceptedDef;
import com.moego.idl.models.notification.v1.BookingRequestDeclinedDef;
import com.moego.idl.models.notification.v1.DeleteOnlineBookingWaitlistDef;
import com.moego.idl.models.notification.v1.NotificationExtraDef;
import com.moego.idl.models.notification.v1.NotificationType;
import com.moego.idl.models.notification.v1.PushTokenSource;
import com.moego.idl.models.notification.v1.ReadyToPickUpDef;
import com.moego.idl.models.notification.v1.ScheduleOnlineBookingWaitlistDef;
import com.moego.idl.service.account.v1.AccountServiceGrpc.AccountServiceBlockingStub;
import com.moego.idl.service.account.v1.GetAccountRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.idl.service.notification.v1.AppPushServiceGrpc.AppPushServiceBlockingStub;
import com.moego.idl.service.notification.v1.CreateAppPushRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc.BusinessServiceBlockingStub;
import com.moego.idl.service.organization.v1.GetLocationDetailRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Service
@RequiredArgsConstructor
public class BrandedAppNotificationService {

    private final AppointmentMapperProxy appointmentMapper;
    private final PetDetailMapperProxy petDetailMapper;

    private final AppPushServiceBlockingStub appPushService;
    private final BusinessCustomerServiceBlockingStub businessCustomerService;
    private final AccountServiceBlockingStub accountService;
    private final BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final BusinessServiceBlockingStub businessService;

    public void pushNotification(Integer appointmentId, NotificationType type) {
        ThreadPool.execute(() -> {
            var appointment = appointmentMapper.selectByPrimaryKey(appointmentId);
            if (appointment == null) {
                throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
            }
            var petDetails = petDetailMapper.selectByAppointmentIdList(List.of(appointmentId));
            long accountId = getRelatedAccountId(appointment.getCustomerId());
            if (accountId == 0L || !isFromBrandedApp(accountId)) {
                return;
            }
            var request = CreateAppPushRequest.newBuilder()
                    .setAccountId(accountId)
                    .setType(type)
                    .setExtra(buildNotificationExtraDef(type, appointment, petDetails))
                    .setAppPush(AppPushDef.newBuilder()
                            .setSource(PushTokenSource.PUSH_TOKEN_SOURCE_CLIENT)
                            .setFromTemplate(true)
                            .build())
                    .build();
            appPushService.createAppPush(request);
        });
    }

    private boolean isFromBrandedApp(long accountId) {
        var account = accountService.getAccount(
                GetAccountRequest.newBuilder().setId(accountId).build());
        return !Objects.equals(account.getNamespace().getType(), AccountNamespaceType.MOEGO);
    }

    private long getRelatedAccountId(long customerId) {
        var customer = businessCustomerService
                .getCustomerInfo(
                        GetCustomerInfoRequest.newBuilder().setId(customerId).build())
                .getCustomer();
        return customer.getAccountId();
    }

    private List<BusinessCustomerPetInfoModel> getPets(List<MoeGroomingPetDetail> petDetails) {
        List<Long> petIds = petDetails.stream()
                .map(MoeGroomingPetDetail::getPetId)
                .distinct()
                .map(Integer::longValue)
                .toList();
        return businessCustomerPetService
                .batchGetPetInfo(
                        BatchGetPetInfoRequest.newBuilder().addAllIds(petIds).build())
                .getPetsList();
    }

    private NotificationExtraDef buildNotificationExtraDef(
            NotificationType type, MoeGroomingAppointment appointment, List<MoeGroomingPetDetail> petDetails) {
        var pets = getPets(petDetails);
        var location = businessService
                .getLocationDetail(GetLocationDetailRequest.newBuilder()
                        .setId(appointment.getBusinessId())
                        .build())
                .getLocation();
        var appointmentDate = LocalDate.parse(appointment.getAppointmentDate());
        String dayOfWeek = appointmentDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
        String month = appointmentDate.getMonth().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
        String day = appointmentDate.format(DateTimeFormatter.ofPattern("dd"));
        String petName =
                pets.stream().map(BusinessCustomerPetInfoModel::getPetName).collect(Collectors.joining(" & "));
        String verb = pets.size() == 1 ? "is" : "are";
        return switch (type) {
            case NOTIFICATION_TYPE_BOOKING_REQUEST_ACCEPTED -> NotificationExtraDef.newBuilder()
                    .setBookingRequestAccepted(BookingRequestAcceptedDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .build())
                    .build();
            case NOTIFICATION_TYPE_BOOKING_REQUEST_DECLINED -> NotificationExtraDef.newBuilder()
                    .setBookingRequestDeclined(BookingRequestDeclinedDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .build())
                    .build();
            case NOTIFICATION_TYPE_SCHEDULE_ONLINE_BOOKING_WAITLIST -> NotificationExtraDef.newBuilder()
                    .setScheduleOnlineBookingWaitlist(ScheduleOnlineBookingWaitlistDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .build())
                    .build();
            case NOTIFICATION_TYPE_DELETE_ONLINE_BOOKING_WAITLIST -> NotificationExtraDef.newBuilder()
                    .setDeleteOnlineBookingWaitlist(DeleteOnlineBookingWaitlistDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .build())
                    .build();
            case NOTIFICATION_TYPE_APPOINTMENT_BOOKED -> NotificationExtraDef.newBuilder()
                    .setAppointmentBooked(AppointmentBookedDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .setPetName(petName)
                            .setVerb(verb)
                            .setDayOfWeek(dayOfWeek)
                            .setMonth(month)
                            .setDay(day)
                            .build())
                    .build();
            case NOTIFICATION_TYPE_APPOINTMENT_RESCHEDULED -> NotificationExtraDef.newBuilder()
                    .setAppointmentRescheduled(AppointmentRescheduledDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .setDayOfWeek(dayOfWeek)
                            .setMonth(month)
                            .setDay(day)
                            .build())
                    .build();
            case NOTIFICATION_TYPE_APPOINTMENT_CANCELLED -> NotificationExtraDef.newBuilder()
                    .setAppointmentCancelled(AppointmentCancelledDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .setDayOfWeek(dayOfWeek)
                            .setMonth(month)
                            .setDay(day)
                            .build())
                    .build();
            case NOTIFICATION_TYPE_READY_TO_PICK_UP -> NotificationExtraDef.newBuilder()
                    .setReadyToPickUp(ReadyToPickUpDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .setPetName(petName)
                            .setVerb(verb)
                            .build())
                    .build();
            case NOTIFICATION_TYPE_ASK_FOR_REVIEW -> NotificationExtraDef.newBuilder()
                    .setAskForReview(AskForReviewDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .build())
                    .build();
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Unsupported notification type: " + type);
        };
    }
}
