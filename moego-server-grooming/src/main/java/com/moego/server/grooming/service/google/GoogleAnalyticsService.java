package com.moego.server.grooming.service.google;

import com.google.analytics.data.v1beta.BetaAnalyticsDataClient;
import com.google.analytics.data.v1beta.DateRange;
import com.google.analytics.data.v1beta.Dimension;
import com.google.analytics.data.v1beta.DimensionValue;
import com.google.analytics.data.v1beta.Metric;
import com.google.analytics.data.v1beta.MetricValue;
import com.google.analytics.data.v1beta.Row;
import com.google.analytics.data.v1beta.RunReportRequest;
import com.google.analytics.data.v1beta.RunReportResponse;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.dto.ob.BookOnlineConfigDTO;
import com.moego.server.grooming.enums.DataRangeAliasEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineMetricsMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineMetrics;
import com.moego.server.grooming.service.dto.ob.GAMetricsDTO;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/6/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoogleAnalyticsService {

    @Value("${google.analytics.property-id}")
    private String propertyId;

    private final MoeBookOnlineMetricsMapper metricsMapper;
    private final MoeBusinessBookOnlineMapper businessBookOnlineMapper;
    private final BusinessInfoHelper businessInfoHelper;

    /**
     * Get all metrics by single dimension
     *
     * @param gaMetricsDTO ga query dto
     * @return Single metric value
     */
    public List<Row> listRowBySingleDimension(GAMetricsDTO gaMetricsDTO) {
        if (CollectionUtils.isEmpty(gaMetricsDTO.dimensions())
                || CollectionUtils.isEmpty(gaMetricsDTO.metrics())
                || CollectionUtils.isEmpty(gaMetricsDTO.dateRanges())) {
            return List.of();
        }
        try (BetaAnalyticsDataClient analyticsDataClient = BetaAnalyticsDataClient.create()) {
            RunReportRequest.Builder builder = RunReportRequest.newBuilder().setProperty("properties/" + propertyId);
            if (Objects.nonNull(gaMetricsDTO.dimensionFilter())) {
                builder.setDimensionFilter(gaMetricsDTO.dimensionFilter());
            }
            RunReportRequest request = builder.addAllDimensions(gaMetricsDTO.dimensions())
                    .addAllMetrics(gaMetricsDTO.metrics())
                    .addAllDateRanges(gaMetricsDTO.dateRanges())
                    .build();
            // Make the request.
            RunReportResponse response = analyticsDataClient.runReport(request);
            // Iterate through every row of the API response.
            return response.getRowsList();
        } catch (Exception e) {
            log.error("Call google analytics api error: {}", e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * Get single metric by single dimension
     *
     * @param gaMetricsDTO ga query dto
     * @return Single metric value
     */
    public MetricValue getMetricBySingleDimension(GAMetricsDTO gaMetricsDTO) {
        List<Row> rows = listRowBySingleDimension(gaMetricsDTO);
        if (!CollectionUtils.isEmpty(rows)) {
            return rows.get(0).getMetricValues(0);
        }
        return MetricValue.newBuilder().build();
    }

    public DateRange buildDateRange(LocalDate startDate, LocalDate endDate, String name) {
        return DateRange.newBuilder()
                .setStartDate(startDate.toString())
                .setEndDate(endDate.toString())
                .setName(name)
                .build();
    }

    public Map<String, DateRange> buildDateRanges() {
        LocalDate now = LocalDate.now();
        return Map.of(
                DataRangeAliasEnum.LAST_7_DAYS.getAlias(),
                buildDateRange(now.minusDays(7), now, DataRangeAliasEnum.LAST_7_DAYS.getAlias()),
                DataRangeAliasEnum.LAST_14_DAYS.getAlias(),
                buildDateRange(now.minusDays(14), now, DataRangeAliasEnum.LAST_14_DAYS.getAlias()),
                DataRangeAliasEnum.LAST_30_DAYS.getAlias(),
                buildDateRange(now.minusDays(30), now, DataRangeAliasEnum.LAST_30_DAYS.getAlias()));
    }

    public Map<String, DateRange> buildLastDateRanges() {
        LocalDate now = LocalDate.now();
        return Map.of(
                DataRangeAliasEnum.LAST_14_DAYS_TO_7_DAYS.getAlias(),
                buildDateRange(
                        now.minusDays(14), now.minusDays(7), DataRangeAliasEnum.LAST_14_DAYS_TO_7_DAYS.getAlias()),
                DataRangeAliasEnum.LAST_28_DAYS_TO_14_DAYS.getAlias(),
                buildDateRange(
                        now.minusDays(28), now.minusDays(14), DataRangeAliasEnum.LAST_28_DAYS_TO_14_DAYS.getAlias()),
                DataRangeAliasEnum.LAST_60_DAYS_TO_30_DAYS.getAlias(),
                buildDateRange(
                        now.minusDays(60), now.minusDays(30), DataRangeAliasEnum.LAST_60_DAYS_TO_30_DAYS.getAlias()));
    }

    public Boolean syncTotalUsersMetricsToDB() {
        Map<String, DateRange> dateRangeMap = buildDateRanges();
        Boolean res = syncTotalUsersMetricsToDB(dateRangeMap);
        Map<String, DateRange> lastDateRangeMap = buildLastDateRanges();
        Boolean lastRes = syncTotalUsersMetricsToDB(lastDateRangeMap);
        return res && lastRes;
    }

    /**
     * Sync total business metrics to db
     *
     * @return sync result
     */
    public Boolean syncTotalUsersMetricsToDB(Map<String, DateRange> dateRangeMap) {
        if (CollectionUtils.isEmpty(dateRangeMap)) {
            return false;
        }
        // GA requests are limited to 4 dateRanges.
        if (dateRangeMap.size() > 4) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "date range size must less than 4");
        }
        String metricName = "totalUsers";
        GAMetricsDTO gaMetricsDTO = GAMetricsDTO.builder()
                .dimensions(List.of(Dimension.newBuilder()
                        .setName("customEvent:businessName")
                        .build()))
                .metrics(List.of(Metric.newBuilder().setName(metricName).build()))
                .dateRanges(new ArrayList<>(dateRangeMap.values()))
                .build();
        // 1. get all book online name and business id
        List<BookOnlineConfigDTO> bookOnlineConfigDTOList = businessBookOnlineMapper.listAllBookOnlineName();
        Map<String, BusinessCompanyPO> bookOnlineNameMap = bookOnlineConfigDTOList.stream()
                .collect(Collectors.toMap(
                        BookOnlineConfigDTO::getBookOnlineName,
                        bookOnlineConfigDTO -> new BusinessCompanyPO(
                                bookOnlineConfigDTO.getBusinessId(), bookOnlineConfigDTO.getCompanyId()),
                        (b1, b2) -> b1));
        // 2. get all metrics
        List<Row> rowList = listRowBySingleDimension(gaMetricsDTO);
        // 3. save to db
        return saveMetricsToDB(rowList, bookOnlineNameMap, metricName, dateRangeMap);
    }

    public Map<Integer, Map<String, String>> buildBusinessDateRangeMetricsMap(
            List<Row> rowList, Map<String, BusinessCompanyPO> bookOnlineNameMap) {
        Map<Integer, Map<String, String>> businessDateRangeMetricsMap = new HashMap<>();
        Set<String> notExistingBookOnlineNameSet = new HashSet<>();
        rowList.forEach(row -> {
            List<DimensionValue> dimensionValuesList = row.getDimensionValuesList();
            String bookOnlineName = dimensionValuesList.get(0).getValue();
            String dateRangeAlias = dimensionValuesList.get(1).getValue();
            List<MetricValue> metricValuesList = row.getMetricValuesList();
            String totalUsers = metricValuesList.get(0).getValue();
            BusinessCompanyPO businessCompanyPO = bookOnlineNameMap.get(bookOnlineName);
            if (Objects.isNull(businessCompanyPO) || Objects.isNull(businessCompanyPO.getBusinessId())) {
                notExistingBookOnlineNameSet.add(bookOnlineName);
                return;
            }
            Integer businessId = businessCompanyPO.getBusinessId();
            Map<String, String> dateRangeMetricsMap =
                    businessDateRangeMetricsMap.getOrDefault(businessId, new HashMap<>());
            dateRangeMetricsMap.put(dateRangeAlias, totalUsers);
            businessDateRangeMetricsMap.putIfAbsent(businessId, dateRangeMetricsMap);
        });
        if (CollectionUtils.isEmpty(notExistingBookOnlineNameSet)) {
            log.error("Not existing book online name: {}", notExistingBookOnlineNameSet);
        }
        return businessDateRangeMetricsMap;
    }

    public Boolean saveMetricsToDB(
            List<Row> rowList,
            Map<String, BusinessCompanyPO> bookOnlineNameMap,
            String metricName,
            Map<String, DateRange> dateRangeMap) {
        Map<Integer, Map<String, String>> businessDateRangeMetricsMap =
                buildBusinessDateRangeMetricsMap(rowList, bookOnlineNameMap);
        List<MoeBookOnlineMetrics> uvMetricsList = bookOnlineNameMap.entrySet().stream()
                .map(entry -> {
                    String bookOnlineName = entry.getKey();
                    Integer businessId = Objects.isNull(entry.getValue())
                            ? null
                            : entry.getValue().getBusinessId();
                    Long companyId = Objects.isNull(entry.getValue())
                            ? null
                            : entry.getValue().getCompanyId();
                    Map<String, String> dateRangeMetricsMap = businessDateRangeMetricsMap.get(businessId);
                    if (CollectionUtils.isEmpty(dateRangeMetricsMap)) {
                        // no data
                        return buildEmptyMetrics(entry, metricName, dateRangeMap);
                    }
                    return dateRangeMap.entrySet().stream()
                            .map(dateRangeEntry -> {
                                MoeBookOnlineMetrics metrics = new MoeBookOnlineMetrics();
                                metrics.setBusinessId(businessId);
                                metrics.setCompanyId(companyId);
                                metrics.setBookOnlineName(bookOnlineName);
                                metrics.setMetricName(metricName);
                                metrics.setMetricValue(dateRangeMetricsMap.getOrDefault(dateRangeEntry.getKey(), "0"));
                                metrics.setDateRangeAlias(dateRangeEntry.getKey());
                                DateRange dateRange = dateRangeEntry.getValue();
                                metrics.setStartDate(dateRange.getStartDate());
                                metrics.setEndDate(dateRange.getEndDate());
                                return metrics;
                            })
                            .toList();
                })
                .flatMap(Collection::stream)
                .toList();
        return metricsMapper.batchUpsert(uvMetricsList) != 0;
    }

    public List<MoeBookOnlineMetrics> buildEmptyMetrics(
            Map.Entry<String, BusinessCompanyPO> entry, String metricName, Map<String, DateRange> dateRangeMap) {
        return dateRangeMap.entrySet().stream()
                .map(dateRangeEntry -> {
                    MoeBookOnlineMetrics metrics = new MoeBookOnlineMetrics();
                    metrics.setBusinessId(
                            Objects.isNull(entry.getValue())
                                    ? null
                                    : entry.getValue().getBusinessId());
                    metrics.setCompanyId(
                            Objects.isNull(entry.getValue())
                                    ? null
                                    : entry.getValue().getCompanyId());
                    metrics.setBookOnlineName(entry.getKey());
                    metrics.setMetricName(metricName);
                    metrics.setMetricValue("0");
                    metrics.setDateRangeAlias(dateRangeEntry.getKey());
                    DateRange dateRange = dateRangeEntry.getValue();
                    metrics.setStartDate(dateRange.getStartDate());
                    metrics.setEndDate(dateRange.getEndDate());
                    return metrics;
                })
                .toList();
    }
}
