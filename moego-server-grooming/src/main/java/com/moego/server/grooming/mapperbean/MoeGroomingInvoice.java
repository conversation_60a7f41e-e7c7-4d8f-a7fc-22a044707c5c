package com.moego.server.grooming.mapperbean;

import com.moego.common.enums.order.OrderItemType;
import com.moego.idl.models.grooming.v1.AppointmentSource;
import com.moego.server.grooming.dto.order.LineDiscountDTO;
import com.moego.server.grooming.dto.order.LineExtraFeeDTO;
import com.moego.server.grooming.dto.order.LineTaxDTO;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * 旧的 invoice 结构，已删除 Mapper 文件，旧结构保留，兼容旧接口，后续替换完再删除
 */
@Data
public class MoeGroomingInvoice {

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.type
     *
     * @mbg.generated
     */
    private String type;

    private String guid;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.sub_total_amount
     *
     * @mbg.generated
     */
    private BigDecimal subTotalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.discount_amount
     *
     * @mbg.generated
     */
    private BigDecimal discountAmount;

    private BigDecimal depositAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.discount_rate
     *
     * @mbg.generated
     */
    private BigDecimal discountRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.discount_type
     *
     * @mbg.generated
     */
    private String discountType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.discounted_sub_total_amount
     *
     * @mbg.generated
     */
    private BigDecimal discountedSubTotalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.tips_amount
     *
     * @mbg.generated
     */
    private BigDecimal tipsAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.tips_rate
     *
     * @mbg.generated
     */
    private BigDecimal tipsRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.tips_type
     *
     * @mbg.generated
     */
    private String tipsType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.tax_amount
     *
     * @mbg.generated
     */
    private BigDecimal taxAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.total_amount
     *
     * @mbg.generated
     */
    private BigDecimal totalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.payment_amount
     *
     * @mbg.generated
     */
    private BigDecimal paymentAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.paid_amount
     *
     * @mbg.generated
     */
    private BigDecimal paidAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.remain_amount
     *
     * @mbg.generated
     */
    private BigDecimal remainAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.refunded_amount
     *
     * @mbg.generated
     */
    private BigDecimal refundedAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.create_by
     *
     * @mbg.generated
     */
    private Integer createBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.update_by
     *
     * @mbg.generated
     */
    private Integer updateBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    // processing fee pay by client，多笔payment记录会产生多次processing fee，convenienceFee = processingFee总和
    private BigDecimal convenienceFee;

    private AppointmentSource source;

    private BigDecimal tipsBaseAmount;

    private String paymentStatus;
    private Long completeTime;
    private String orderType;
    private String fulfillmentStatus;
    private Long orderRefId;
    private Boolean hasExtraOrder;
    private String currencyCode;
    private Integer orderVersion;

    private List<MoeGroomingInvoiceItem> items;
    private List<LineTaxDTO> lineTaxes;
    private List<LineDiscountDTO> lineDiscounts;
    private List<LineExtraFeeDTO> lineExtraFees;
    private Boolean hasProduct;
    private Boolean hasServiceCharge;

    public String getItemNames() {
        if (CollectionUtils.isEmpty(items)) return "";

        List<String> itemNames = items.stream()
                .filter(item -> !Boolean.TRUE.equals(item.getIsDeleted()))
                .map(MoeGroomingInvoiceItem::getServiceName)
                .distinct()
                .collect(Collectors.toList());
        return String.join(", ", itemNames);
    }

    public void initAmount() {
        if (subTotalAmount == null) subTotalAmount = BigDecimal.ZERO;
        if (discountedSubTotalAmount == null) discountedSubTotalAmount = BigDecimal.ZERO;
        if (taxAmount == null) taxAmount = BigDecimal.ZERO;
        if (totalAmount == null) totalAmount = BigDecimal.ZERO;
        if (paymentAmount == null) paymentAmount = BigDecimal.ZERO;
        if (discountAmount == null) discountAmount = BigDecimal.ZERO;
        if (tipsAmount == null) tipsAmount = BigDecimal.ZERO;
        if (tipsBaseAmount == null) tipsBaseAmount = BigDecimal.ZERO;
        if (paidAmount == null) paidAmount = BigDecimal.ZERO;
        if (convenienceFee == null) convenienceFee = BigDecimal.ZERO;
        if (remainAmount == null || remainAmount.compareTo(BigDecimal.ZERO) < 0) {
            remainAmount = BigDecimal.ZERO;
        }
        if (discountRate == null) discountRate = BigDecimal.ZERO;
        if (tipsRate == null) tipsRate = BigDecimal.ZERO;
    }

    public void setNoShowItem(BigDecimal amount) {
        MoeGroomingInvoiceItem item =
                this.items != null && this.items.size() > 0 ? this.items.get(0) : new MoeGroomingInvoiceItem();
        item.setInvoiceId(this.id);
        item.setServiceName("No show fee");
        item.setType(OrderItemType.ITEM_TYPE_NO_SHOW.getType());
        item.setQuantity(1);
        item.setServiceId(0);
        item.setPetDetailId(0);
        item.setServiceUnitPrice(amount);
        this.items = Arrays.asList(item);
    }

    /**
     * 获取所有service使用package前的总金额
     *
     * @return
     */
    public BigDecimal getOriginalAmount() {
        if (items == null || items.size() == 0) {
            return BigDecimal.ZERO;
        }

        return items.stream()
                .filter(t -> OrderItemType.ITEM_TYPE_SERVICE.getType().equals(t.getType())
                        || OrderItemType.ITEM_TYPE_NO_SHOW.getType().equals(t.getType()))
                .filter(t -> !Boolean.TRUE.equals(t.getIsDeleted()))
                .map(t -> t.getServiceUnitPrice().multiply(new BigDecimal(t.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
