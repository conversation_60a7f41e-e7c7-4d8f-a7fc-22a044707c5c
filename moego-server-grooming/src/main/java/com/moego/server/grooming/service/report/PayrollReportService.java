package com.moego.server.grooming.service.report;

import com.moego.common.dto.PageDTO;
import com.moego.common.enums.ReportConst;
import com.moego.common.utils.CommonUtil;
import com.moego.server.grooming.dto.report.PayrollReportCountDTO;
import com.moego.server.grooming.dto.report.PayrollReportCountV2DTO;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.params.report.GetReportParams;
import com.moego.server.grooming.params.report.QueryPayrollReportByPageParams;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.TipsApptsPageDTO;
import com.moego.server.grooming.service.utils.ReportBeanUtil;
import com.moego.server.grooming.service.utils.ReportUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Payroll/Commission 相关计算
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PayrollReportService {

    private final AppointmentMapperProxy appointmentMapper;
    private final ReportCalculateService reportCalculateService;
    private final ReportAppointmentService reportAppointmentService;
    private final PayrollCalculateService payrollCalculateService;

    /**
     * 查询 staff service payroll 和 tips payroll report 的数量
     *
     * @param businessId
     * @param startDate
     * @param endDate
     * @param staffIds
     * @return
     */
    public List<PayrollReportCountDTO> getStaffPayrollReportCounts(
            Integer businessId, String startDate, String endDate, List<Integer> staffIds) {
        if (businessId == null || CollectionUtils.isEmpty(staffIds)) {
            return Collections.emptyList();
        }

        return staffIds.stream()
                .map(staffId -> {
                    PayrollReportCountDTO dto = new PayrollReportCountDTO();
                    dto.setStaffId(staffId);
                    // service payroll: 查询 staff 预约数
                    dto.setServicePayrollCount(
                            appointmentMapper.getPayrollApptsCountV2(List.of(businessId), staffId, startDate, endDate));
                    // tips payroll: 查询 tips 大于0的预约数（pageNum, pageSize 传0是因为只需要查询数量，不需要分页查询）
                    dto.setTipsPayrollCount(reportAppointmentService.getStaffTipsAppointmentCount(
                            businessId, staffId, startDate, endDate));
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public List<PayrollReportCountV2DTO> getStaffServicePayrollReportCounts(GetReportParams params) {
        if (CollectionUtils.isEmpty(params.getStaffIds())) {
            return List.of();
        }

        return params.getStaffIds().stream()
                .map(staffId -> {
                    Integer apptCount = appointmentMapper.getPayrollApptsCountV2(
                            List.of(params.getBusinessId()), staffId, params.getStartDate(), params.getEndDate());
                    return new PayrollReportCountV2DTO(staffId, apptCount);
                })
                .toList();
    }

    public List<PayrollReportCountV2DTO> getStaffTipsPayrollReportCounts(GetReportParams params) {
        if (CollectionUtils.isEmpty(params.getStaffIds())) {
            return List.of();
        }

        return params.getStaffIds().stream()
                .map(staffId -> {
                    Integer apptCount = reportAppointmentService.getStaffTipsAppointmentCount(
                            params.getBusinessId(), staffId, params.getStartDate(), params.getEndDate());
                    return new PayrollReportCountV2DTO(staffId, apptCount);
                })
                .toList();
    }

    public PageDTO<ReportWebEmployee> queryStaffPayrollByPage(QueryPayrollReportByPageParams params) {
        List<Integer> businessIds = params.getBusinessIds();
        Integer staffId = params.getStaffId();
        Byte type = params.getType();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        Integer pageNum = params.getPageNum();
        Integer pageSize = params.getPageSize();

        List<ReportWebEmployee> employees = new ArrayList<>();
        int totalCount = 0;

        Integer offset = CommonUtil.getLimitOffset(pageNum, pageSize);
        switch (type) {
            case ReportConst.PAYROLL_TYPE_SERVICE -> {
                employees = getStaffPayrollReportV2(businessIds, staffId, startDate, endDate, offset, pageSize);
                totalCount = appointmentMapper.getPayrollApptsCountV2(businessIds, staffId, startDate, endDate);
            }
            case ReportConst.PAYROLL_TYPE_TIPS -> {
                TipsApptsPageDTO pageDTO =
                        getStaffTipsPayrollReport(businessIds, staffId, startDate, endDate, pageNum, pageSize);
                employees = pageDTO.getReportBeans();
                totalCount = pageDTO.getCount();
            }
            default -> {}
        }
        return PageDTO.create(employees, totalCount, pageNum, pageSize);
    }

    public List<ReportWebEmployee> getStaffPayrollReportV2(
            List<Integer> businessIds,
            Integer staffId,
            String startDate,
            String endDate,
            Integer offset,
            Integer size) {
        Map<Integer, List<GroomingReportWebAppointment>> apptMap =
                reportAppointmentService.getStaffPayrollAppointmentsByPage(
                        businessIds, staffId, startDate, endDate, offset, size);
        List<ReportWebEmployee> resultList = new ArrayList<>();
        for (Map.Entry<Integer, List<GroomingReportWebAppointment>> entry : apptMap.entrySet()) {
            List<ReportWebEmployee> employees =
                    reportCalculateService.buildEmployeeReports(entry.getKey(), startDate, endDate, entry.getValue());
            // 过滤非当前staff的report
            employees.removeIf(employee -> !Objects.equals(employee.getStaffId(), staffId));
            reportCalculateService.postProcessEmployeeMoneyFormat(employees);
            reportCalculateService.processPaymentMethod(employees);
            resultList.addAll(employees);
        }
        resultList.sort(Comparator.comparing(ReportWebEmployee::getApptDate)
                .thenComparing(ReportWebEmployee::getApptTime)
                .reversed());
        return resultList;
    }

    public TipsApptsPageDTO getStaffTipsPayrollReport(
            List<Integer> businessIds,
            Integer staffId,
            String startDate,
            String endDate,
            Integer pageNum,
            Integer pageSize) {
        TipsApptsPageDTO pageDTO = reportAppointmentService.getStaffTipsAppointmentsByPage(
                businessIds, staffId, startDate, endDate, pageNum, pageSize);
        if (pageDTO.getCount() == 0 || CollectionUtils.isEmpty(pageDTO.getAppointments())) {
            return pageDTO.setReportBeans(Collections.emptyList());
        }
        List<GroomingReportWebAppointment> appointments = pageDTO.getAppointments();
        Map<Integer, List<GroomingReportWebAppointment>> apptMap =
                appointments.stream().collect(Collectors.groupingBy(GroomingReportWebAppointment::getBusinessId));

        List<ReportWebEmployee> resultList = new ArrayList<>();
        for (Map.Entry<Integer, List<GroomingReportWebAppointment>> entry : apptMap.entrySet()) {
            List<ReportWebEmployee> employees =
                    reportCalculateService.buildEmployeeReports(entry.getKey(), startDate, endDate, entry.getValue());
            // 过滤非当前staff的report
            employees.removeIf(employee -> !Objects.equals(employee.getStaffId(), staffId));
            reportCalculateService.postProcessEmployeeMoneyFormat(employees);
            reportCalculateService.processPaymentMethod(employees);
            resultList.addAll(employees);
        }
        resultList.sort(Comparator.comparing(ReportWebEmployee::getApptDate)
                .thenComparing(ReportWebEmployee::getApptTime)
                .reversed());

        pageDTO.setReportBeans(resultList);
        return pageDTO;
    }

    /**
     * 查询staff  payroll数据，全量查询，用于导出
     *
     * @param businessId
     * @param staffId
     * @param type
     * @param startDate
     * @param endDate
     * @return
     */
    public List<ReportWebEmployee> queryPayrollReportAll(
            Integer businessId, Integer staffId, Byte type, String startDate, String endDate) {
        List<ReportWebEmployee> employees = new ArrayList<>();
        switch (type) {
            case ReportConst.PAYROLL_TYPE_SERVICE -> employees =
                    getStaffPayrollReportV2(List.of(businessId), staffId, startDate, endDate, null, null);
            case ReportConst.PAYROLL_TYPE_TIPS -> employees = getStaffTipsPayrollReport(
                            List.of(businessId), staffId, startDate, endDate, -1, -1)
                    .getReportBeans();
            default -> {}
        }
        return employees;
    }

    public List<ReportWebEmployee> queryReportWebEmployee(
            Integer businessId, Integer reportId, String startDate, String endDate) {
        List<ReportWebEmployee> employees = new ArrayList<>();
        return switch (reportId) {
            case 4001, 4004 -> getStaffPayrollSummary(businessId, startDate, endDate);
            case 4002 -> getPayrollReport(businessId, startDate, endDate);
            default -> employees;
        };
    }

    public List<ReportWebEmployee> getStaffPayrollSummary(Integer businessId, String startDate, String endDate) {
        List<GroomingReportWebAppointment> appointments =
                reportAppointmentService.getPayrollAppointmentDetails(businessId, startDate, endDate);
        if (CollectionUtils.isEmpty(appointments)) {
            return List.of();
        }
        var staffPayrollDetails = reportCalculateService.calculateAppointments(businessId, appointments).stream()
                // 过滤不在查询时间范围内的 payroll details
                .filter(payroll -> ReportUtil.isDateBetween(payroll.getDate(), startDate, endDate))
                .collect(Collectors.toList());
        var employees = ReportBeanUtil.convertToStaffPayrollSummary(staffPayrollDetails);

        // process total appts, pets and clients
        reportCalculateService.processCounts(appointments, employees);
        reportCalculateService.postProcessEmployeeMoneyFormat(employees);
        return employees;
    }

    private List<ReportWebEmployee> getPayrollReport(Integer businessId, String startDate, String endDate) {
        List<GroomingReportWebAppointment> appointments =
                reportAppointmentService.getPayrollAppointmentDetails(businessId, startDate, endDate);
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyList();
        }
        List<ReportWebEmployee> employees =
                reportCalculateService.buildEmployeeReports(businessId, startDate, endDate, appointments);
        reportCalculateService.postProcessEmployeeMoneyFormat(employees);
        reportCalculateService.processPaymentMethod(employees);
        return employees;
    }

    public PageDTO<ReportWebEmployee> queryPayrollReportByPage(
            Integer businessId,
            Integer staffId,
            Byte type,
            String startDate,
            String endDate,
            Integer pageNum,
            Integer pageSize) {
        List<ReportWebEmployee> employees = new ArrayList<>();
        int totalCount = 0;
        Integer offset = CommonUtil.getLimitOffset(pageNum, pageSize);
        switch (type) {
            case ReportConst.PAYROLL_TYPE_SERVICE:
                employees = getStaffPayrollReportV2(List.of(businessId), staffId, startDate, endDate, offset, pageSize);
                totalCount = appointmentMapper.getPayrollApptsCountV2(List.of(businessId), staffId, startDate, endDate);
                break;
            case ReportConst.PAYROLL_TYPE_TIPS:
                TipsApptsPageDTO pageDTO =
                        getStaffTipsPayrollReport(List.of(businessId), staffId, startDate, endDate, pageNum, pageSize);
                employees = pageDTO.getReportBeans();
                totalCount = pageDTO.getCount();
                break;
            default:
                break;
        }
        return PageDTO.create(employees, totalCount, pageNum, pageSize);
    }
}
