<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingNoteHistoryMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingNoteHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="grooming_note_id" jdbcType="INTEGER" property="groomingNoteId" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
  </resultMap>
  <resultMap
    extends="BaseResultMap"
    id="ResultMapWithBLOBs"
    type="com.moego.server.grooming.mapperbean.MoeGroomingNoteHistory"
  >
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="old_note" jdbcType="LONGVARCHAR" property="oldNote" />
    <result column="new_note" jdbcType="LONGVARCHAR" property="newNote" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, customer_id, grooming_note_id, create_by, update_by, create_time
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    old_note, new_note
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_grooming_note_history
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_note_history
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNoteHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_note_history (customer_id, grooming_note_id, create_by, 
      update_by, create_time, old_note, 
      new_note)
    values (#{customerId,jdbcType=INTEGER}, #{groomingNoteId,jdbcType=INTEGER}, #{createBy,jdbcType=INTEGER}, 
      #{updateBy,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, #{oldNote,jdbcType=LONGVARCHAR}, 
      #{newNote,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNoteHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_note_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="groomingNoteId != null">
        grooming_note_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="oldNote != null">
        old_note,
      </if>
      <if test="newNote != null">
        new_note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="groomingNoteId != null">
        #{groomingNoteId,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=INTEGER},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="oldNote != null">
        #{oldNote,jdbcType=LONGVARCHAR},
      </if>
      <if test="newNote != null">
        #{newNote,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNoteHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_note_history
    <set>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="groomingNoteId != null">
        grooming_note_id = #{groomingNoteId,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=INTEGER},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="oldNote != null">
        old_note = #{oldNote,jdbcType=LONGVARCHAR},
      </if>
      <if test="newNote != null">
        new_note = #{newNote,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNoteHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_note_history
    set customer_id = #{customerId,jdbcType=INTEGER},
      grooming_note_id = #{groomingNoteId,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=INTEGER},
      update_by = #{updateBy,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      old_note = #{oldNote,jdbcType=LONGVARCHAR},
      new_note = #{newNote,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNoteHistory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_note_history
    set customer_id = #{customerId,jdbcType=INTEGER},
      grooming_note_id = #{groomingNoteId,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=INTEGER},
      update_by = #{updateBy,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
