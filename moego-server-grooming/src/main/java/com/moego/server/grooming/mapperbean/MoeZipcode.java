package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_zipcode
 */
public class MoeZipcode {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   zip_code
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.zip_code
     *
     * @mbg.generated
     */
    private String zipCode;

    /**
     * Database Column Remarks:
     *   place_name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.place_name
     *
     * @mbg.generated
     */
    private String placeName;

    /**
     * Database Column Remarks:
     *   state
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.state
     *
     * @mbg.generated
     */
    private String state;

    /**
     * Database Column Remarks:
     *   state_abbreviation
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.state_abbreviation
     *
     * @mbg.generated
     */
    private String stateAbbreviation;

    /**
     * Database Column Remarks:
     *   county
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.county
     *
     * @mbg.generated
     */
    private String county;

    /**
     * Database Column Remarks:
     *   country name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.country_name
     *
     * @mbg.generated
     */
    private String countryName;

    /**
     * Database Column Remarks:
     *   lat
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.lat
     *
     * @mbg.generated
     */
    private String lat;

    /**
     * Database Column Remarks:
     *   lng
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.lng
     *
     * @mbg.generated
     */
    private String lng;

    /**
     * Database Column Remarks:
     *    Google Place
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.place_id
     *
     * @mbg.generated
     */
    private String placeId;

    /**
     * Database Column Remarks:
     *   0-刚导入, 1-已完成爬取
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.status
     *
     * @mbg.generated
     */
    private Boolean status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_zipcode.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.id
     *
     * @return the value of moe_zipcode.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.id
     *
     * @param id the value for moe_zipcode.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.zip_code
     *
     * @return the value of moe_zipcode.zip_code
     *
     * @mbg.generated
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.zip_code
     *
     * @param zipCode the value for moe_zipcode.zip_code
     *
     * @mbg.generated
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode == null ? null : zipCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.place_name
     *
     * @return the value of moe_zipcode.place_name
     *
     * @mbg.generated
     */
    public String getPlaceName() {
        return placeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.place_name
     *
     * @param placeName the value for moe_zipcode.place_name
     *
     * @mbg.generated
     */
    public void setPlaceName(String placeName) {
        this.placeName = placeName == null ? null : placeName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.state
     *
     * @return the value of moe_zipcode.state
     *
     * @mbg.generated
     */
    public String getState() {
        return state;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.state
     *
     * @param state the value for moe_zipcode.state
     *
     * @mbg.generated
     */
    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.state_abbreviation
     *
     * @return the value of moe_zipcode.state_abbreviation
     *
     * @mbg.generated
     */
    public String getStateAbbreviation() {
        return stateAbbreviation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.state_abbreviation
     *
     * @param stateAbbreviation the value for moe_zipcode.state_abbreviation
     *
     * @mbg.generated
     */
    public void setStateAbbreviation(String stateAbbreviation) {
        this.stateAbbreviation = stateAbbreviation == null ? null : stateAbbreviation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.county
     *
     * @return the value of moe_zipcode.county
     *
     * @mbg.generated
     */
    public String getCounty() {
        return county;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.county
     *
     * @param county the value for moe_zipcode.county
     *
     * @mbg.generated
     */
    public void setCounty(String county) {
        this.county = county == null ? null : county.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.country_name
     *
     * @return the value of moe_zipcode.country_name
     *
     * @mbg.generated
     */
    public String getCountryName() {
        return countryName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.country_name
     *
     * @param countryName the value for moe_zipcode.country_name
     *
     * @mbg.generated
     */
    public void setCountryName(String countryName) {
        this.countryName = countryName == null ? null : countryName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.lat
     *
     * @return the value of moe_zipcode.lat
     *
     * @mbg.generated
     */
    public String getLat() {
        return lat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.lat
     *
     * @param lat the value for moe_zipcode.lat
     *
     * @mbg.generated
     */
    public void setLat(String lat) {
        this.lat = lat == null ? null : lat.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.lng
     *
     * @return the value of moe_zipcode.lng
     *
     * @mbg.generated
     */
    public String getLng() {
        return lng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.lng
     *
     * @param lng the value for moe_zipcode.lng
     *
     * @mbg.generated
     */
    public void setLng(String lng) {
        this.lng = lng == null ? null : lng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.place_id
     *
     * @return the value of moe_zipcode.place_id
     *
     * @mbg.generated
     */
    public String getPlaceId() {
        return placeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.place_id
     *
     * @param placeId the value for moe_zipcode.place_id
     *
     * @mbg.generated
     */
    public void setPlaceId(String placeId) {
        this.placeId = placeId == null ? null : placeId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.status
     *
     * @return the value of moe_zipcode.status
     *
     * @mbg.generated
     */
    public Boolean getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.status
     *
     * @param status the value for moe_zipcode.status
     *
     * @mbg.generated
     */
    public void setStatus(Boolean status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.updated_at
     *
     * @return the value of moe_zipcode.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.updated_at
     *
     * @param updatedAt the value for moe_zipcode.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_zipcode.created_at
     *
     * @return the value of moe_zipcode.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_zipcode.created_at
     *
     * @param createdAt the value for moe_zipcode.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}
