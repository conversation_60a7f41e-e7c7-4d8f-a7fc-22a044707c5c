package com.moego.server.grooming.service;

import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingTransferMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingTransfer;
import com.moego.server.grooming.params.TransferParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MoeTransferService {

    @Autowired
    private MoeGroomingTransferMapper moeGroomingTransferMapper;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    // 0203 @Transactional
    public Object addTransfer(TransferParams transferParams) {
        MoeGroomingAppointment moeGroomingAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKey(transferParams.getGroomingId());

        MoeGroomingTransfer moeGroomingTransfer = new MoeGroomingTransfer();
        moeGroomingTransfer.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingTransfer.setFromBy(transferParams.getFromId());
        moeGroomingTransfer.setToBy(transferParams.getToId());
        moeGroomingTransfer.setPetDetailId(transferParams.getPetDetailId());
        moeGroomingTransfer.setGroomingId(transferParams.getGroomingId());
        moeGroomingTransfer.setOrderId(moeGroomingAppointment.getOrderId());

        int i = moeGroomingTransferMapper.insertSelective(moeGroomingTransfer);

        return ResponseResult.success(i);
    }
}
