package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_sync_payment_method
 */
public class MoeQbSyncPaymentMethod {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment_method.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment_method.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment_method.connect_id
     *
     * @mbg.generated
     */
    private Integer connectId;

    /**
     * Database Column Remarks:
     *   realmId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment_method.realm_id
     *
     * @mbg.generated
     */
    private String realmId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment_method.method
     *
     * @mbg.generated
     */
    private String method;

    /**
     * Database Column Remarks:
     *   quickbookds 支付方式ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment_method.qb_payment_method_id
     *
     * @mbg.generated
     */
    private String qbPaymentMethodId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment_method.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment_method.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_payment_method.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment_method.id
     *
     * @return the value of moe_qb_sync_payment_method.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment_method.id
     *
     * @param id the value for moe_qb_sync_payment_method.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment_method.business_id
     *
     * @return the value of moe_qb_sync_payment_method.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment_method.business_id
     *
     * @param businessId the value for moe_qb_sync_payment_method.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment_method.connect_id
     *
     * @return the value of moe_qb_sync_payment_method.connect_id
     *
     * @mbg.generated
     */
    public Integer getConnectId() {
        return connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment_method.connect_id
     *
     * @param connectId the value for moe_qb_sync_payment_method.connect_id
     *
     * @mbg.generated
     */
    public void setConnectId(Integer connectId) {
        this.connectId = connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment_method.realm_id
     *
     * @return the value of moe_qb_sync_payment_method.realm_id
     *
     * @mbg.generated
     */
    public String getRealmId() {
        return realmId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment_method.realm_id
     *
     * @param realmId the value for moe_qb_sync_payment_method.realm_id
     *
     * @mbg.generated
     */
    public void setRealmId(String realmId) {
        this.realmId = realmId == null ? null : realmId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment_method.method
     *
     * @return the value of moe_qb_sync_payment_method.method
     *
     * @mbg.generated
     */
    public String getMethod() {
        return method;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment_method.method
     *
     * @param method the value for moe_qb_sync_payment_method.method
     *
     * @mbg.generated
     */
    public void setMethod(String method) {
        this.method = method == null ? null : method.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment_method.qb_payment_method_id
     *
     * @return the value of moe_qb_sync_payment_method.qb_payment_method_id
     *
     * @mbg.generated
     */
    public String getQbPaymentMethodId() {
        return qbPaymentMethodId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment_method.qb_payment_method_id
     *
     * @param qbPaymentMethodId the value for moe_qb_sync_payment_method.qb_payment_method_id
     *
     * @mbg.generated
     */
    public void setQbPaymentMethodId(String qbPaymentMethodId) {
        this.qbPaymentMethodId = qbPaymentMethodId == null ? null : qbPaymentMethodId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment_method.update_time
     *
     * @return the value of moe_qb_sync_payment_method.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment_method.update_time
     *
     * @param updateTime the value for moe_qb_sync_payment_method.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment_method.create_time
     *
     * @return the value of moe_qb_sync_payment_method.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment_method.create_time
     *
     * @param createTime the value for moe_qb_sync_payment_method.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_payment_method.company_id
     *
     * @return the value of moe_qb_sync_payment_method.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_payment_method.company_id
     *
     * @param companyId the value for moe_qb_sync_payment_method.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
