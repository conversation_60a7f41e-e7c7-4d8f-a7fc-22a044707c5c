package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_gallery
 */
public class MoeBookOnlineGallery {
    /**
     * Database Column Remarks:
     *   商家ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   内容的用户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   图片文件url
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.image_path
     *
     * @mbg.generated
     */
    private String imagePath;

    /**
     * Database Column Remarks:
     *   排序值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     * Database Column Remarks:
     *   状态：1 正常
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   是否已删除 0 否   1 是
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.is_delete
     *
     * @mbg.generated
     */
    private Byte isDelete;

    /**
     * Database Column Remarks:
     *   是否星标 0 否 1是
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.is_star
     *
     * @mbg.generated
     */
    private Byte isStar;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.create_time
     *
     * @mbg.generated
     */
    private Integer createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.update_time
     *
     * @mbg.generated
     */
    private Integer updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_gallery.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.id
     *
     * @return the value of moe_book_online_gallery.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.id
     *
     * @param id the value for moe_book_online_gallery.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.business_id
     *
     * @return the value of moe_book_online_gallery.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.business_id
     *
     * @param businessId the value for moe_book_online_gallery.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.staff_id
     *
     * @return the value of moe_book_online_gallery.staff_id
     *
     * @mbg.generated
     */
    public Integer getAccountId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.staff_id
     *
     * @param staffId the value for moe_book_online_gallery.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.image_path
     *
     * @return the value of moe_book_online_gallery.image_path
     *
     * @mbg.generated
     */
    public String getImagePath() {
        return imagePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.image_path
     *
     * @param imagePath the value for moe_book_online_gallery.image_path
     *
     * @mbg.generated
     */
    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.sort
     *
     * @return the value of moe_book_online_gallery.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.sort
     *
     * @param sort the value for moe_book_online_gallery.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.status
     *
     * @return the value of moe_book_online_gallery.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.status
     *
     * @param status the value for moe_book_online_gallery.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.is_delete
     *
     * @return the value of moe_book_online_gallery.is_delete
     *
     * @mbg.generated
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.is_delete
     *
     * @param isDelete the value for moe_book_online_gallery.is_delete
     *
     * @mbg.generated
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.is_star
     *
     * @return the value of moe_book_online_gallery.is_star
     *
     * @mbg.generated
     */
    public Byte getIsStar() {
        return isStar;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.is_star
     *
     * @param isStar the value for moe_book_online_gallery.is_star
     *
     * @mbg.generated
     */
    public void setIsStar(Byte isStar) {
        this.isStar = isStar;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.create_time
     *
     * @return the value of moe_book_online_gallery.create_time
     *
     * @mbg.generated
     */
    public Integer getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.create_time
     *
     * @param createTime the value for moe_book_online_gallery.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.update_time
     *
     * @return the value of moe_book_online_gallery.update_time
     *
     * @mbg.generated
     */
    public Integer getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.update_time
     *
     * @param updateTime the value for moe_book_online_gallery.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_gallery.company_id
     *
     * @return the value of moe_book_online_gallery.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_gallery.company_id
     *
     * @param companyId the value for moe_book_online_gallery.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
