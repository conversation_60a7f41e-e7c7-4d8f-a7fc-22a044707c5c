<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineServiceMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="show_base_price" jdbcType="TINYINT" property="showBasePrice" />
    <result column="book_online_available" jdbcType="TINYINT" property="bookOnlineAvailable" />
    <result column="is_all_staff" jdbcType="TINYINT" property="isAllStaff" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="allow_booking_with_other_care_type" jdbcType="BIT" property="allowBookingWithOtherCareType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, business_id, service_id, show_base_price, book_online_available, 
    is_all_staff, create_time, update_time, allow_booking_with_other_care_type
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineServiceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_book_online_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_book_online_service
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_service
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineServiceExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_service (company_id, business_id, service_id, 
      show_base_price, book_online_available, is_all_staff, 
      create_time, update_time, allow_booking_with_other_care_type
      )
    values (#{companyId,jdbcType=BIGINT}, #{businessId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER}, 
      #{showBasePrice,jdbcType=TINYINT}, #{bookOnlineAvailable,jdbcType=TINYINT}, #{isAllStaff,jdbcType=TINYINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{allowBookingWithOtherCareType,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_service
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="showBasePrice != null">
        show_base_price,
      </if>
      <if test="bookOnlineAvailable != null">
        book_online_available,
      </if>
      <if test="isAllStaff != null">
        is_all_staff,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="allowBookingWithOtherCareType != null">
        allow_booking_with_other_care_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="showBasePrice != null">
        #{showBasePrice,jdbcType=TINYINT},
      </if>
      <if test="bookOnlineAvailable != null">
        #{bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="isAllStaff != null">
        #{isAllStaff,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="allowBookingWithOtherCareType != null">
        #{allowBookingWithOtherCareType,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineServiceExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_book_online_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_service
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=INTEGER},
      </if>
      <if test="record.showBasePrice != null">
        show_base_price = #{record.showBasePrice,jdbcType=TINYINT},
      </if>
      <if test="record.bookOnlineAvailable != null">
        book_online_available = #{record.bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="record.isAllStaff != null">
        is_all_staff = #{record.isAllStaff,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.allowBookingWithOtherCareType != null">
        allow_booking_with_other_care_type = #{record.allowBookingWithOtherCareType,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_service
    set id = #{record.id,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=INTEGER},
      service_id = #{record.serviceId,jdbcType=INTEGER},
      show_base_price = #{record.showBasePrice,jdbcType=TINYINT},
      book_online_available = #{record.bookOnlineAvailable,jdbcType=TINYINT},
      is_all_staff = #{record.isAllStaff,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      allow_booking_with_other_care_type = #{record.allowBookingWithOtherCareType,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_service
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="showBasePrice != null">
        show_base_price = #{showBasePrice,jdbcType=TINYINT},
      </if>
      <if test="bookOnlineAvailable != null">
        book_online_available = #{bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="isAllStaff != null">
        is_all_staff = #{isAllStaff,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="allowBookingWithOtherCareType != null">
        allow_booking_with_other_care_type = #{allowBookingWithOtherCareType,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineService">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_service
    set company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      show_base_price = #{showBasePrice,jdbcType=TINYINT},
      book_online_available = #{bookOnlineAvailable,jdbcType=TINYINT},
      is_all_staff = #{isAllStaff,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      allow_booking_with_other_care_type = #{allowBookingWithOtherCareType,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByUniqueIndexBidSid" resultMap="BaseResultMap">
  select
  <include refid="Base_Column_List" />
      from moe_book_online_service
    where company_id = #{companyId}
    and business_id = #{businessId}
    and service_id = #{serviceId}
  </select>
  <select id="selectByUniqueIndexBidSids" resultMap="BaseResultMap">
  select
  <include refid="Base_Column_List" />
      from moe_book_online_service
    where company_id = #{companyId}
    and business_id = #{businessId}
    and service_id in
    <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>