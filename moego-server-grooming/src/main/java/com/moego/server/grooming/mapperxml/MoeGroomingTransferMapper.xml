<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingTransferMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingTransfer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="from_by" jdbcType="INTEGER" property="fromBy" />
    <result column="to_by" jdbcType="INTEGER" property="toBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="pet_detail_id" property="petDetailId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, grooming_id, from_by, to_by, create_time,pet_detail_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_grooming_transfer
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_transfer
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingTransfer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_transfer (order_id, grooming_id, from_by, 
      to_by, create_time,pet_detail_id)
    values (#{orderId,jdbcType=VARCHAR}, #{groomingId,jdbcType=INTEGER}, #{fromBy,jdbcType=INTEGER}, 
      #{toBy,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT},#{petDetailId})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingTransfer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_transfer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="fromBy != null">
        from_by,
      </if>
      <if test="toBy != null">
        to_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="petDetailId != null">
        pet_detail_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="fromBy != null">
        #{fromBy,jdbcType=INTEGER},
      </if>
      <if test="toBy != null">
        #{toBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
       <if test="petDetailId != null">
        #{petDetailId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingTransfer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_transfer
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="fromBy != null">
        from_by = #{fromBy,jdbcType=INTEGER},
      </if>
      <if test="toBy != null">
        to_by = #{toBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="petDetailId != null">
        pet_detail_id = #{petDetailId},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingTransfer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_transfer
    set order_id = #{orderId,jdbcType=VARCHAR},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      from_by = #{fromBy,jdbcType=INTEGER},
      to_by = #{toBy,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
    pet_detail_id = #{petDetailId}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
