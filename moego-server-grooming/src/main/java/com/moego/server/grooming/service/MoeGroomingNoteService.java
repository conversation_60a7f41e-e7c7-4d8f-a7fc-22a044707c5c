package com.moego.server.grooming.service;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.grooming.dto.GroomingTicketCommentsDTO;
import com.moego.server.grooming.dto.GroomingTicketNotesDTO;
import com.moego.server.grooming.dto.HistoryCommentsDTO;
import com.moego.server.grooming.mapper.MoeGroomingNoteHistoryMapper;
import com.moego.server.grooming.mapper.MoeGroomingNoteMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingNoteExample;
import com.moego.server.grooming.mapperbean.MoeGroomingNoteHistory;
import com.moego.server.grooming.service.dto.AppointmentNote;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 封装对 grooming note 的操作
 *
 * <AUTHOR>
 * @since 2022/5/12 16:15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MoeGroomingNoteService {

    private final MoeGroomingNoteMapper moeGroomingNoteMapper;
    private final MoeGroomingNoteHistoryMapper moeGroomingNoteHistoryMapper;
    private final IBusinessStaffClient iBusinessStaffClient;

    public MoeGroomingNote buildGroomingComment(
            Long companyId,
            Integer businessId,
            Integer staffId,
            Integer appointmentId,
            Integer customerId,
            String note,
            Long now) {
        MoeGroomingNote moeGroomingNote = new MoeGroomingNote();
        moeGroomingNote.setBusinessId(businessId);
        moeGroomingNote.setCompanyId(companyId);
        moeGroomingNote.setCustomerId(customerId);
        moeGroomingNote.setGroomingId(appointmentId);
        moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_COMMENT);
        moeGroomingNote.setNote(note);
        moeGroomingNote.setCreateBy(staffId);
        moeGroomingNote.setCreateTime(now);
        moeGroomingNote.setUpdateBy(staffId);
        moeGroomingNote.setUpdateTime(now);
        return moeGroomingNote;
    }

    /**
     * 新增或更新 grooming note
     *
     * @param note
     * @return 返回成功编辑的条数
     */
    public int atomicityInsertOrUpdate(MoeGroomingNote note) {
        Integer groomingId = note.getGroomingId();
        Integer type = note.getType().intValue();

        MoeGroomingNote moeGroomingNote = getNoteByGroomingIdAndType(groomingId, type, note.getPetId());
        if (moeGroomingNote == null) {
            if (StringUtils.isBlank(note.getNote())) {
                return 0;
            }

            try {
                moeGroomingNoteMapper.insertSelective(note);
            } catch (DuplicateKeyException ex) {
                // 违反唯一键冲突，尝试进行更新 #ERP-1254
                moeGroomingNote = getNoteByGroomingIdAndType(groomingId, type, note.getPetId());
                if (moeGroomingNote != null) {
                    log.debug(
                            "A duplicateKeyException was thrown when inserting note<groomingId:{}, type:{}, msg:{}>. "
                                    + "Try to update",
                            note.getGroomingId(),
                            note.getType(),
                            note.getNote());
                    updateNote(note.getUpdateBy(), note.getNote(), moeGroomingNote);
                }
            }
        } else {
            updateNote(note.getUpdateBy(), note.getNote(), moeGroomingNote);
        }
        return 1;
    }

    /**
     * 批量插入 grooming note
     *
     * @param moeGroomingNoteList
     * @return
     */
    public int batchInsert(List<MoeGroomingNote> moeGroomingNoteList) {
        return moeGroomingNoteMapper.batchInsert(moeGroomingNoteList);
    }

    /**
     * 根据 grooming id 和 type 查询 note （对于非 pet comment，可以唯一确定一条note）
     *
     * @param groomingId
     * @param type
     * @return
     */
    public MoeGroomingNote getNoteByGroomingIdAndType(Integer groomingId, Integer type) {
        List<MoeGroomingNote> moeGroomingNoteList = moeGroomingNoteMapper.selectByGroomingId(groomingId, type);
        if (moeGroomingNoteList != null && moeGroomingNoteList.size() > 0) {
            return moeGroomingNoteList.get(0);
        }
        return null;
    }

    /**
     * 根据 grooming id,type, pet id 查询 note （可以唯一确定一条note）
     *
     * @param groomingId
     * @param type
     * @Param petId
     * @return
     */
    public MoeGroomingNote getNoteByGroomingIdAndType(Integer groomingId, Integer type, Long petId) {
        if (Objects.isNull(petId)) {
            petId = 0L;
        }
        MoeGroomingNoteExample example = new MoeGroomingNoteExample();
        example.createCriteria()
                .andGroomingIdEqualTo(groomingId)
                .andTypeEqualTo(type.byteValue())
                .andPetIdEqualTo(petId)
                .andIsDeletedEqualTo(false);
        List<MoeGroomingNote> moeGroomingNoteList = moeGroomingNoteMapper.selectByExampleWithBLOBs(example);
        if (!CollectionUtils.isEmpty(moeGroomingNoteList)) {
            return moeGroomingNoteList.get(0);
        }
        return null;
    }

    /**
     * 根据 grooming id list 和 type 查询多个 note
     *
     * @param groomingIds
     * @param type
     * @return
     */
    public List<MoeGroomingNote> getNoteListByGroomingIdListAndType(List<Integer> groomingIds, Integer type) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return new ArrayList<>();
        }
        return moeGroomingNoteMapper.selectByGroomingIds(groomingIds, type);
    }

    /**
     * 根据 grooming id list 查询 notes（可以返回多条 note，包含多个 type）
     *
     * @param groomingIdList
     * @return
     */
    public List<MoeGroomingNote> getNoteListByGroomingIdList(List<Integer> groomingIdList) {
        if (CollectionUtils.isEmpty(groomingIdList)) {
            return List.of();
        }
        return moeGroomingNoteMapper.selectByGroomingIds(groomingIdList, null);
    }

    /**
     * 查询客户在指定店铺中的所有历史 comment note
     *
     * @param tokenBusinessId
     * @param customerId
     * @return
     */
    public List<HistoryCommentsDTO> getCustomerHistoryCommentList(
            Integer tokenBusinessId, Integer customerId, Long petId) {
        return moeGroomingNoteMapper.queryCustomerHistoryComments(tokenBusinessId, customerId, petId);
    }

    /**
     * 查询客户在指定店铺中的所有历史 comment note 最新的一条
     *
     * @param businessId
     * @param customerId
     * @param type
     * @return
     */
    public MoeGroomingNote getLastNoteByCustomerId(Long companyId, Integer businessId, Integer customerId, Byte type) {
        return moeGroomingNoteMapper.selectLastNoteByCustomerId(companyId, businessId, customerId, type);
    }

    /**
     * 更新 note, 并将 old note 插入到历史表留存
     *
     * @param tokenStaffId
     * @param newNote
     * @param moeGroomingNote
     */
    private void updateNote(Integer tokenStaffId, String newNote, MoeGroomingNote moeGroomingNote) {
        // 如果 newNote 不为 null， 并且和 oldNote 保持一直，不更新
        if (StringUtils.isNotBlank(moeGroomingNote.getNote())) {
            if (moeGroomingNote.getNote().equals(newNote)) {
                return;
            }
        }

        MoeGroomingNote updateMoeGroomingNote = new MoeGroomingNote();
        updateMoeGroomingNote.setId(moeGroomingNote.getId());
        if (StringUtils.isBlank(newNote)) {
            newNote = "";
        }

        updateMoeGroomingNote.setNote(newNote);
        updateMoeGroomingNote.setUpdateBy(tokenStaffId);
        updateMoeGroomingNote.setUpdateTime(CommonUtil.get10Timestamp());
        updateMoeGroomingNote.setPetId(moeGroomingNote.getPetId());
        moeGroomingNoteMapper.updateByPrimaryKeySelective(updateMoeGroomingNote);
        // 修改完增加修改记录
        MoeGroomingNoteHistory moeGroomingNoteHistory = new MoeGroomingNoteHistory();
        moeGroomingNoteHistory.setCreateBy(moeGroomingNote.getUpdateBy());
        moeGroomingNoteHistory.setUpdateBy(tokenStaffId);
        moeGroomingNoteHistory.setCustomerId(moeGroomingNote.getCustomerId());
        moeGroomingNoteHistory.setGroomingNoteId(moeGroomingNote.getId());
        moeGroomingNoteHistory.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingNoteHistory.setNewNote(newNote);
        moeGroomingNoteHistory.setOldNote(moeGroomingNote.getNote());
        moeGroomingNoteHistoryMapper.insertSelective(moeGroomingNoteHistory);
    }

    public GroomingTicketCommentsDTO getGroomingTicketComments(MoeGroomingAppointment appt) {
        // 查询comments信息
        MoeGroomingNote commentNote =
                this.getNoteByGroomingIdAndType(appt.getId(), GroomingAppointmentEnum.NOTE_COMMENT.intValue());
        if (Objects.isNull(commentNote)) {
            /**
             * 兼容逻辑，当 ticket comment 不存在时，尝试获取 additional note，来代替 ticket comment
             */
            commentNote =
                    this.getNoteByGroomingIdAndType(appt.getId(), GroomingAppointmentEnum.NOTE_ADDITIONAL.intValue());
            if (Objects.isNull(commentNote)) {
                return null;
            }
        }
        GroomingTicketCommentsDTO groomingTicketCommentsDTO = new GroomingTicketCommentsDTO();
        groomingTicketCommentsDTO.setGroomingNoteId(commentNote.getId());
        groomingTicketCommentsDTO.setTicketComments(commentNote.getNote());
        groomingTicketCommentsDTO.setEditTime(commentNote.getUpdateTime());
        groomingTicketCommentsDTO.setUpdateBy(commentNote.getUpdateBy());
        // 查询修改人名称
        if (Objects.equals(commentNote.getUpdateBy(), 0)) {
            return groomingTicketCommentsDTO;
        }
        MoeStaffDto staffDto = getStaffById(appt.getBusinessId(), commentNote.getUpdateBy());
        if (Objects.nonNull(staffDto)) {
            groomingTicketCommentsDTO.setEditLastName(staffDto.getLastName());
            groomingTicketCommentsDTO.setEditFirstName(staffDto.getFirstName());
        }
        return groomingTicketCommentsDTO;
    }

    public GroomingTicketNotesDTO getGroomingAlertNotes(MoeGroomingAppointment appt) {
        // 查询 alertNote 信息
        MoeGroomingNote alertNote =
                this.getNoteByGroomingIdAndType(appt.getId(), GroomingAppointmentEnum.NOTE_ALERT.intValue());
        if (Objects.isNull(alertNote)) {
            return null;
        }
        GroomingTicketNotesDTO groomingTicketNotesDTO = new GroomingTicketNotesDTO();
        groomingTicketNotesDTO.setGroomingNoteId(alertNote.getId());
        groomingTicketNotesDTO.setNote(alertNote.getNote());
        groomingTicketNotesDTO.setEditTime(alertNote.getUpdateTime());
        groomingTicketNotesDTO.setUpdateBy(alertNote.getUpdateBy());

        // 查询修改人名称
        if (Objects.equals(alertNote.getUpdateBy(), 0)) {
            return groomingTicketNotesDTO;
        }
        MoeStaffDto staffDto = getStaffById(appt.getBusinessId(), alertNote.getUpdateBy());
        if (Objects.nonNull(staffDto)) {
            groomingTicketNotesDTO.setEditLastName(staffDto.getLastName());
            groomingTicketNotesDTO.setEditFirstName(staffDto.getFirstName());
        }
        return groomingTicketNotesDTO;
    }

    public MoeStaffDto getStaffById(Integer businessId, Integer staffId) {
        StaffIdParams staffIdParams = new StaffIdParams();
        staffIdParams.setBusinessId(businessId);
        staffIdParams.setStaffId(staffId);
        return iBusinessStaffClient.getStaff(staffIdParams);
    }

    public GroomingTicketNotesDTO getAdditionalNote(Integer apptId) {
        MoeGroomingNote additionalNote =
                this.getNoteByGroomingIdAndType(apptId, GroomingAppointmentEnum.NOTE_ADDITIONAL.intValue());
        if (Objects.isNull(additionalNote)) {
            return null;
        }
        GroomingTicketNotesDTO notesDTO = new GroomingTicketNotesDTO();
        notesDTO.setGroomingNoteId(additionalNote.getId());
        notesDTO.setNote(additionalNote.getNote());
        notesDTO.setUpdateBy(additionalNote.getUpdateBy());
        notesDTO.setEditTime(additionalNote.getUpdateTime());
        return notesDTO;
    }

    AppointmentNote toAppointmentNote(MoeGroomingNote note) {
        AppointmentNote result = new AppointmentNote();
        result.setId(note.getId());
        result.setBusinessId(note.getBusinessId());
        result.setCustomerId(note.getCustomerId());
        result.setGroomingId(note.getGroomingId());
        result.setType(note.getType());
        result.setNote(note.getNote());
        return result;
    }

    public List<AppointmentNote> toAppointmentNoteList(List<MoeGroomingNote> details) {
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        return details.stream().map(this::toAppointmentNote).collect(Collectors.toList());
    }
}
