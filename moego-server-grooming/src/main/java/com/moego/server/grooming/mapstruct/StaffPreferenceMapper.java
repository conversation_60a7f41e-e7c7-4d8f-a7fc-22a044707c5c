package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.mapper.po.StaffPreferencePO;
import com.moego.server.grooming.web.params.waitlist.StaffPreference;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface StaffPreferenceMapper {
    StaffPreferenceMapper INSTANCE = Mappers.getMapper(StaffPreferenceMapper.class);

    StaffPreferencePO toEntity(StaffPreference param);
}
