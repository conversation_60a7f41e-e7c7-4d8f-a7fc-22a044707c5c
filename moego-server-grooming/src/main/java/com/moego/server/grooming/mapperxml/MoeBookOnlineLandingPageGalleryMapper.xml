<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineLandingPageGalleryMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, image_path, sort, is_deleted, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_book_online_landing_page_gallery
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_landing_page_gallery
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_landing_page_gallery (business_id, image_path, sort,
      is_deleted, create_time, update_time,
      company_id)
    values (#{businessId,jdbcType=INTEGER}, #{imagePath,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER},
      #{isDeleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_landing_page_gallery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="imagePath != null">
        image_path,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="imagePath != null">
        #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_landing_page_gallery
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="imagePath != null">
        image_path = #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_landing_page_gallery
    set business_id = #{businessId,jdbcType=INTEGER},
      image_path = #{imagePath,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="listGalleryByCondition" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageGallery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_book_online_landing_page_gallery
    where is_deleted = #{isDeleted,jdbcType=BIT}
    and business_id = #{businessId,jdbcType=INTEGER}
    order by sort desc;
  </select>

  <insert id="batchInsert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageGallery">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_landing_page_gallery (business_id, image_path, sort, company_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessId,jdbcType=INTEGER}, #{item.imagePath,jdbcType=VARCHAR}, #{item.sort,jdbcType=INTEGER}, #{item.companyId,jdbcType=BIGINT})
    </foreach>
  </insert>

  <update id="deleteByBusinessId" parameterType="java.lang.Integer">
    update
      moe_book_online_landing_page_gallery
    set is_deleted = 1
    where business_id = #{businessId,jdbcType=INTEGER};
  </update>

  <select id="listGalleryMinSortImageByBusinessId" resultMap="BaseResultMap">
    select
    a.id, a.business_id, a.image_path, a.sort, a.is_deleted, a.create_time, a.update_time
    from moe_book_online_landing_page_gallery a
        inner join (select business_id, min(sort) min_sort
                    from moe_book_online_landing_page_gallery where is_deleted = 0 and business_id in
                    <foreach close=")" collection="businessIdList" item="businessId" open="(" separator=",">
                      #{businessId,jdbcType=INTEGER}
                    </foreach> group by business_id) b
            on a.business_id = b.business_id and a.sort = b.min_sort and a.is_deleted = 0
  </select>
</mapper>
