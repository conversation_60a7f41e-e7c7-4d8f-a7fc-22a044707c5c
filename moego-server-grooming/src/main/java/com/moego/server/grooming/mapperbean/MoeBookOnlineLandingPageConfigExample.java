package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeBookOnlineLandingPageConfigExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public MoeBookOnlineLandingPageConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andAmenitiesIsNull() {
            addCriterion("amenities is null");
            return (Criteria) this;
        }

        public Criteria andAmenitiesIsNotNull() {
            addCriterion("amenities is not null");
            return (Criteria) this;
        }

        public Criteria andAmenitiesEqualTo(String value) {
            addCriterion("amenities =", value, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesNotEqualTo(String value) {
            addCriterion("amenities <>", value, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesGreaterThan(String value) {
            addCriterion("amenities >", value, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesGreaterThanOrEqualTo(String value) {
            addCriterion("amenities >=", value, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesLessThan(String value) {
            addCriterion("amenities <", value, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesLessThanOrEqualTo(String value) {
            addCriterion("amenities <=", value, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesLike(String value) {
            addCriterion("amenities like", value, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesNotLike(String value) {
            addCriterion("amenities not like", value, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesIn(List<String> values) {
            addCriterion("amenities in", values, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesNotIn(List<String> values) {
            addCriterion("amenities not in", values, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesBetween(String value1, String value2) {
            addCriterion("amenities between", value1, value2, "amenities");
            return (Criteria) this;
        }

        public Criteria andAmenitiesNotBetween(String value1, String value2) {
            addCriterion("amenities not between", value1, value2, "amenities");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageIsNull() {
            addCriterion("showcase_before_image is null");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageIsNotNull() {
            addCriterion("showcase_before_image is not null");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageEqualTo(String value) {
            addCriterion("showcase_before_image =", value, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageNotEqualTo(String value) {
            addCriterion("showcase_before_image <>", value, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageGreaterThan(String value) {
            addCriterion("showcase_before_image >", value, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageGreaterThanOrEqualTo(String value) {
            addCriterion("showcase_before_image >=", value, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageLessThan(String value) {
            addCriterion("showcase_before_image <", value, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageLessThanOrEqualTo(String value) {
            addCriterion("showcase_before_image <=", value, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageLike(String value) {
            addCriterion("showcase_before_image like", value, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageNotLike(String value) {
            addCriterion("showcase_before_image not like", value, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageIn(List<String> values) {
            addCriterion("showcase_before_image in", values, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageNotIn(List<String> values) {
            addCriterion("showcase_before_image not in", values, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageBetween(String value1, String value2) {
            addCriterion("showcase_before_image between", value1, value2, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseBeforeImageNotBetween(String value1, String value2) {
            addCriterion("showcase_before_image not between", value1, value2, "showcaseBeforeImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageIsNull() {
            addCriterion("showcase_after_image is null");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageIsNotNull() {
            addCriterion("showcase_after_image is not null");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageEqualTo(String value) {
            addCriterion("showcase_after_image =", value, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageNotEqualTo(String value) {
            addCriterion("showcase_after_image <>", value, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageGreaterThan(String value) {
            addCriterion("showcase_after_image >", value, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageGreaterThanOrEqualTo(String value) {
            addCriterion("showcase_after_image >=", value, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageLessThan(String value) {
            addCriterion("showcase_after_image <", value, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageLessThanOrEqualTo(String value) {
            addCriterion("showcase_after_image <=", value, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageLike(String value) {
            addCriterion("showcase_after_image like", value, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageNotLike(String value) {
            addCriterion("showcase_after_image not like", value, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageIn(List<String> values) {
            addCriterion("showcase_after_image in", values, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageNotIn(List<String> values) {
            addCriterion("showcase_after_image not in", values, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageBetween(String value1, String value2) {
            addCriterion("showcase_after_image between", value1, value2, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andShowcaseAfterImageNotBetween(String value1, String value2) {
            addCriterion("showcase_after_image not between", value1, value2, "showcaseAfterImage");
            return (Criteria) this;
        }

        public Criteria andThemeColorIsNull() {
            addCriterion("theme_color is null");
            return (Criteria) this;
        }

        public Criteria andThemeColorIsNotNull() {
            addCriterion("theme_color is not null");
            return (Criteria) this;
        }

        public Criteria andThemeColorEqualTo(String value) {
            addCriterion("theme_color =", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotEqualTo(String value) {
            addCriterion("theme_color <>", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorGreaterThan(String value) {
            addCriterion("theme_color >", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorGreaterThanOrEqualTo(String value) {
            addCriterion("theme_color >=", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorLessThan(String value) {
            addCriterion("theme_color <", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorLessThanOrEqualTo(String value) {
            addCriterion("theme_color <=", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorLike(String value) {
            addCriterion("theme_color like", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotLike(String value) {
            addCriterion("theme_color not like", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorIn(List<String> values) {
            addCriterion("theme_color in", values, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotIn(List<String> values) {
            addCriterion("theme_color not in", values, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorBetween(String value1, String value2) {
            addCriterion("theme_color between", value1, value2, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotBetween(String value1, String value2) {
            addCriterion("theme_color not between", value1, value2, "themeColor");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameIsNull() {
            addCriterion("url_domain_name is null");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameIsNotNull() {
            addCriterion("url_domain_name is not null");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameEqualTo(String value) {
            addCriterion("url_domain_name =", value, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameNotEqualTo(String value) {
            addCriterion("url_domain_name <>", value, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameGreaterThan(String value) {
            addCriterion("url_domain_name >", value, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameGreaterThanOrEqualTo(String value) {
            addCriterion("url_domain_name >=", value, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameLessThan(String value) {
            addCriterion("url_domain_name <", value, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameLessThanOrEqualTo(String value) {
            addCriterion("url_domain_name <=", value, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameLike(String value) {
            addCriterion("url_domain_name like", value, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameNotLike(String value) {
            addCriterion("url_domain_name not like", value, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameIn(List<String> values) {
            addCriterion("url_domain_name in", values, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameNotIn(List<String> values) {
            addCriterion("url_domain_name not in", values, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameBetween(String value1, String value2) {
            addCriterion("url_domain_name between", value1, value2, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andUrlDomainNameNotBetween(String value1, String value2) {
            addCriterion("url_domain_name not between", value1, value2, "urlDomainName");
            return (Criteria) this;
        }

        public Criteria andIsPublishedIsNull() {
            addCriterion("is_published is null");
            return (Criteria) this;
        }

        public Criteria andIsPublishedIsNotNull() {
            addCriterion("is_published is not null");
            return (Criteria) this;
        }

        public Criteria andIsPublishedEqualTo(Boolean value) {
            addCriterion("is_published =", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedNotEqualTo(Boolean value) {
            addCriterion("is_published <>", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedGreaterThan(Boolean value) {
            addCriterion("is_published >", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_published >=", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedLessThan(Boolean value) {
            addCriterion("is_published <", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_published <=", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedIn(List<Boolean> values) {
            addCriterion("is_published in", values, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedNotIn(List<Boolean> values) {
            addCriterion("is_published not in", values, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_published between", value1, value2, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_published not between", value1, value2, "isPublished");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdIsNull() {
            addCriterion("ga_measurement_id is null");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdIsNotNull() {
            addCriterion("ga_measurement_id is not null");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdEqualTo(String value) {
            addCriterion("ga_measurement_id =", value, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdNotEqualTo(String value) {
            addCriterion("ga_measurement_id <>", value, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdGreaterThan(String value) {
            addCriterion("ga_measurement_id >", value, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdGreaterThanOrEqualTo(String value) {
            addCriterion("ga_measurement_id >=", value, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdLessThan(String value) {
            addCriterion("ga_measurement_id <", value, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdLessThanOrEqualTo(String value) {
            addCriterion("ga_measurement_id <=", value, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdLike(String value) {
            addCriterion("ga_measurement_id like", value, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdNotLike(String value) {
            addCriterion("ga_measurement_id not like", value, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdIn(List<String> values) {
            addCriterion("ga_measurement_id in", values, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdNotIn(List<String> values) {
            addCriterion("ga_measurement_id not in", values, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdBetween(String value1, String value2) {
            addCriterion("ga_measurement_id between", value1, value2, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andGaMeasurementIdNotBetween(String value1, String value2) {
            addCriterion("ga_measurement_id not between", value1, value2, "gaMeasurementId");
            return (Criteria) this;
        }

        public Criteria andPageComponentsIsNull() {
            addCriterion("page_components is null");
            return (Criteria) this;
        }

        public Criteria andPageComponentsIsNotNull() {
            addCriterion("page_components is not null");
            return (Criteria) this;
        }

        public Criteria andPageComponentsEqualTo(String value) {
            addCriterion("page_components =", value, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsNotEqualTo(String value) {
            addCriterion("page_components <>", value, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsGreaterThan(String value) {
            addCriterion("page_components >", value, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsGreaterThanOrEqualTo(String value) {
            addCriterion("page_components >=", value, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsLessThan(String value) {
            addCriterion("page_components <", value, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsLessThanOrEqualTo(String value) {
            addCriterion("page_components <=", value, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsLike(String value) {
            addCriterion("page_components like", value, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsNotLike(String value) {
            addCriterion("page_components not like", value, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsIn(List<String> values) {
            addCriterion("page_components in", values, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsNotIn(List<String> values) {
            addCriterion("page_components not in", values, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsBetween(String value1, String value2) {
            addCriterion("page_components between", value1, value2, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andPageComponentsNotBetween(String value1, String value2) {
            addCriterion("page_components not between", value1, value2, "pageComponents");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoIsNull() {
            addCriterion("is_display_client_review_showcase_photo is null");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoIsNotNull() {
            addCriterion("is_display_client_review_showcase_photo is not null");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoEqualTo(Boolean value) {
            addCriterion("is_display_client_review_showcase_photo =", value, "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoNotEqualTo(Boolean value) {
            addCriterion("is_display_client_review_showcase_photo <>", value, "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoGreaterThan(Boolean value) {
            addCriterion("is_display_client_review_showcase_photo >", value, "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_display_client_review_showcase_photo >=", value, "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoLessThan(Boolean value) {
            addCriterion("is_display_client_review_showcase_photo <", value, "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoLessThanOrEqualTo(Boolean value) {
            addCriterion("is_display_client_review_showcase_photo <=", value, "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoIn(List<Boolean> values) {
            addCriterion("is_display_client_review_showcase_photo in", values, "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoNotIn(List<Boolean> values) {
            addCriterion(
                    "is_display_client_review_showcase_photo not in", values, "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoBetween(Boolean value1, Boolean value2) {
            addCriterion(
                    "is_display_client_review_showcase_photo between",
                    value1,
                    value2,
                    "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }

        public Criteria andIsDisplayClientReviewShowcasePhotoNotBetween(Boolean value1, Boolean value2) {
            addCriterion(
                    "is_display_client_review_showcase_photo not between",
                    value1,
                    value2,
                    "isDisplayClientReviewShowcasePhoto");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
