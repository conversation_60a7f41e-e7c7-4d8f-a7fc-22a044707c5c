package com.moego.server.grooming.listener;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessWorkingHourClient;
import com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO;
import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.grooming.enums.OBRequestSubmittedAutoTypeEnum;
import com.moego.server.grooming.listener.event.UpgradeLandingPageEvent;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.service.ob.OBLandingPageGalleryService;
import com.moego.server.grooming.web.params.OBLandingPageMergeParams;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Slf4j
@Component
@AllArgsConstructor
public class UpgradeLandingPageListener {

    private final MoeBusinessBookOnlineMapper businessBookOnlineMapper;

    private final OBLandingPageConfigService landingPageConfigService;
    private final OBLandingPageGalleryService landingPageGalleryService;

    private final IBusinessBusinessClient businessClient;
    private final IBusinessWorkingHourClient businessWorkingHourClient;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void commit(UpgradeLandingPageEvent upgradeLandingPageEvent) {
        log.info("biz: [{}] upgrade landing page 3.0 success", upgradeLandingPageEvent.getBusinessId());
    }

    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    public void updateBizInfo(UpgradeLandingPageEvent upgradeLandingPageEvent) {
        Integer businessId = upgradeLandingPageEvent.getBusinessId();
        OBLandingPageMergeParams.OBMergeProfileParams mergeProfileParams =
                upgradeLandingPageEvent.getMergeParams().getProfile();
        OBBusinessInfoDTO businessInfoDTO = new OBBusinessInfoDTO();
        BeanUtils.copyProperties(mergeProfileParams, businessInfoDTO);
        businessInfoDTO.setId(businessId);
        log.info("biz: [{}] merge biz info: [{}]", businessId, mergeProfileParams);
        businessClient.updateBizInfoAndFlushCache(businessInfoDTO);
    }

    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    public void updateBizWorkingHours(UpgradeLandingPageEvent upgradeLandingPageEvent) {
        Integer businessId = upgradeLandingPageEvent.getBusinessId();
        BusinessWorkingHourDayDetailDTO workingHours =
                upgradeLandingPageEvent.getMergeParams().getWorkingHours();
        BusinessWorkingHourDetailDTO businessWorkingHourDetailDTO = new BusinessWorkingHourDetailDTO();
        businessWorkingHourDetailDTO.setBusinessId(businessId);
        businessWorkingHourDetailDTO.setTimeData(workingHours);
        log.info("biz: [{}] merge biz working hours: [{}]", businessId, workingHours);
        businessWorkingHourClient.updateBusinessWorkingHour(businessWorkingHourDetailDTO);
    }

    @EventListener
    public void initializeLandingPageConfig(UpgradeLandingPageEvent upgradeLandingPageEvent) {
        Integer businessId = upgradeLandingPageEvent.getBusinessId();
        Long companyId = upgradeLandingPageEvent.getCompanyId();
        landingPageConfigService.inheritedLandingPageConfig(businessId, companyId);
    }

    @EventListener
    public void inheritedOBGallery(UpgradeLandingPageEvent upgradeLandingPageEvent) {
        Integer businessId = upgradeLandingPageEvent.getBusinessId();
        Long companyId = upgradeLandingPageEvent.getCompanyId();
        landingPageGalleryService.inheritedOBGallery(businessId, companyId);
    }

    @EventListener
    public void upgradeEnableOB(UpgradeLandingPageEvent upgradeLandingPageEvent) {
        Integer businessId = upgradeLandingPageEvent.getBusinessId();
        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setBusinessId(businessId);
        businessBookOnline.setIsEnable(CommonConstant.ENABLE);
        businessBookOnline.setUseVersion(OnlineBookingConst.VERSION_TWO);
        MoeBusinessBookOnline exist = businessBookOnlineMapper.selectByBusinessId(businessId);
        OBRequestSubmittedAutoTypeEnum autoTypeEnum = OBRequestSubmittedAutoTypeEnum.NO_AUTOMATION;
        if (Objects.equals(CommonConstant.ENABLE, exist.getAutoAccept())) {
            autoTypeEnum = OBRequestSubmittedAutoTypeEnum.AUTO_ACCEPT_REQUEST;
        } else if (Objects.equals(CommonConstant.ENABLE, exist.getAutoMoveWait())) {
            autoTypeEnum = OBRequestSubmittedAutoTypeEnum.AUTO_MOVE_WAITLIST;
        }
        businessBookOnline.setRequestSubmittedAutoType(autoTypeEnum.name());
        businessBookOnlineMapper.updateInfoByBusinessId(businessBookOnline);
    }
}
