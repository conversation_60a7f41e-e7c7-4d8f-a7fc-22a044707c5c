<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeQbSyncReceiptMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeQbSyncReceipt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="connect_id" jdbcType="INTEGER" property="connectId" />
    <result column="realm_id" jdbcType="VARCHAR" property="realmId" />
    <result column="payment_detail_id" jdbcType="INTEGER" property="paymentDetailId" />
    <result column="qb_receipt_id" jdbcType="VARCHAR" property="qbReceiptId" />
    <result column="qb_receipt_status" jdbcType="TINYINT" property="qbReceiptStatus" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="receipt_type" jdbcType="TINYINT" property="receiptType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, company_id, connect_id, realm_id, payment_detail_id, qb_receipt_id,
    qb_receipt_status, amount, update_time, create_time, receipt_type
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncReceiptExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_qb_sync_receipt
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_qb_sync_receipt
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_sync_receipt
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncReceiptExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_sync_receipt
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncReceipt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_receipt (business_id, company_id, connect_id,
      realm_id, payment_detail_id, qb_receipt_id,
      qb_receipt_status, amount, update_time,
      create_time, receipt_type)
    values (#{businessId,jdbcType=INTEGER}, #{companyId,jdbcType=BIGINT}, #{connectId,jdbcType=INTEGER},
      #{realmId,jdbcType=VARCHAR}, #{paymentDetailId,jdbcType=INTEGER}, #{qbReceiptId,jdbcType=VARCHAR},
      #{qbReceiptStatus,jdbcType=TINYINT}, #{amount,jdbcType=DECIMAL}, #{updateTime,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP}, #{receiptType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncReceipt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_receipt
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="connectId != null">
        connect_id,
      </if>
      <if test="realmId != null">
        realm_id,
      </if>
      <if test="paymentDetailId != null">
        payment_detail_id,
      </if>
      <if test="qbReceiptId != null">
        qb_receipt_id,
      </if>
      <if test="qbReceiptStatus != null">
        qb_receipt_status,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="receiptType != null">
        receipt_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="connectId != null">
        #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="paymentDetailId != null">
        #{paymentDetailId,jdbcType=INTEGER},
      </if>
      <if test="qbReceiptId != null">
        #{qbReceiptId,jdbcType=VARCHAR},
      </if>
      <if test="qbReceiptStatus != null">
        #{qbReceiptStatus,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiptType != null">
        #{receiptType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncReceiptExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_qb_sync_receipt
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_receipt
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.connectId != null">
        connect_id = #{record.connectId,jdbcType=INTEGER},
      </if>
      <if test="record.realmId != null">
        realm_id = #{record.realmId,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentDetailId != null">
        payment_detail_id = #{record.paymentDetailId,jdbcType=INTEGER},
      </if>
      <if test="record.qbReceiptId != null">
        qb_receipt_id = #{record.qbReceiptId,jdbcType=VARCHAR},
      </if>
      <if test="record.qbReceiptStatus != null">
        qb_receipt_status = #{record.qbReceiptStatus,jdbcType=TINYINT},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.receiptType != null">
        receipt_type = #{record.receiptType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_receipt
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=BIGINT},
      connect_id = #{record.connectId,jdbcType=INTEGER},
      realm_id = #{record.realmId,jdbcType=VARCHAR},
      payment_detail_id = #{record.paymentDetailId,jdbcType=INTEGER},
      qb_receipt_id = #{record.qbReceiptId,jdbcType=VARCHAR},
      qb_receipt_status = #{record.qbReceiptStatus,jdbcType=TINYINT},
      amount = #{record.amount,jdbcType=DECIMAL},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      receipt_type = #{record.receiptType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncReceipt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_receipt
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="connectId != null">
        connect_id = #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        realm_id = #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="paymentDetailId != null">
        payment_detail_id = #{paymentDetailId,jdbcType=INTEGER},
      </if>
      <if test="qbReceiptId != null">
        qb_receipt_id = #{qbReceiptId,jdbcType=VARCHAR},
      </if>
      <if test="qbReceiptStatus != null">
        qb_receipt_status = #{qbReceiptStatus,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiptType != null">
        receipt_type = #{receiptType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncReceipt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_receipt
    set business_id = #{businessId,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=BIGINT},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      payment_detail_id = #{paymentDetailId,jdbcType=INTEGER},
      qb_receipt_id = #{qbReceiptId,jdbcType=VARCHAR},
      qb_receipt_status = #{qbReceiptStatus,jdbcType=TINYINT},
      amount = #{amount,jdbcType=DECIMAL},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      receipt_type = #{receiptType,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPaymentDetailIdAndReceiptType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_qb_sync_receipt
    where payment_detail_id = #{paymentDetailId}
    and receipt_type = #{receiptType}
  </select>
  <select id="selectByPaymentDetailId"
          resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from moe_qb_sync_receipt
    where payment_detail_id = #{paymentDetailId}
  </select>
    <select id="selectByBusinessIdAndRealmId"
            resultMap="BaseResultMap">
      select
        <include refid="Base_Column_List"/>
      from moe_qb_sync_receipt
      where business_id = #{businessId}
      and realm_id = #{realmId}
    </select>
  <select id="selectByBusinessRealmIdDetailIdAndType"
          resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from moe_qb_sync_receipt
    where business_id = #{businessId}
    and realm_id = #{realmId}
    and payment_detail_id = #{paymentDetailId}
    and receipt_type = #{receiptType}
  </select>
</mapper>
