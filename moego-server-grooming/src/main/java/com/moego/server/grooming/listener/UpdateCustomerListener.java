package com.moego.server.grooming.listener;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.params.UpdateCustomerLastServiceParams;
import com.moego.server.grooming.listener.event.UpdateCustomerEvent;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/6/23
 */
@Component
@Slf4j
public class UpdateCustomerListener implements ApplicationListener<UpdateCustomerEvent> {

    @Autowired
    private AppointmentMapperProxy groomingAppointmentMapper;

    @Autowired
    private ICustomerCustomerClient customerClient;

    @Override
    public void onApplicationEvent(UpdateCustomerEvent updateCustomerEvent) {
        if (Objects.isNull(updateCustomerEvent.getBusinessId())
                || Objects.isNull(updateCustomerEvent.getCustomerId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "businessId or customerId is null");
        }
        if (updateCustomerEvent.getBusinessId() <= 0 || updateCustomerEvent.getCustomerId() <= 0) {
            return;
        }
        String lastServiceTime = groomingAppointmentMapper.selectLastServiceTime(
                updateCustomerEvent.getBusinessId(), updateCustomerEvent.getCustomerId());
        customerClient.updateCustomerLastServiceTime(new UpdateCustomerLastServiceParams()
                .setBusinessId(updateCustomerEvent.getBusinessId())
                .setCustomerId(updateCustomerEvent.getCustomerId())
                // last_service_time column not null
                .setLastServiceTime(Objects.nonNull(lastServiceTime) ? lastServiceTime : ""));
    }
}
