package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table appointment_pet_schedule_setting
 */
public class AppointmentPetScheduleSetting {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_schedule_setting.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_schedule_setting.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_schedule_setting.appointment_id
     *
     * @mbg.generated
     */
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   1-feeding, 2-medication
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_schedule_setting.schedule_type
     *
     * @mbg.generated
     */
    private Integer scheduleType;

    /**
     * Database Column Remarks:
     *   appointment_pet_feeding.id, appointment_pet_medication.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_schedule_setting.schedule_id
     *
     * @mbg.generated
     */
    private Long scheduleId;

    /**
     * Database Column Remarks:
     *   Scheduled time, unit in minutes. 09:00 AM = 540, 09:30 AM = 570 etc.
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_schedule_setting.schedule_time
     *
     * @mbg.generated
     */
    private Integer scheduleTime;

    /**
     * Database Column Remarks:
     *   Schedule extra information, such as schedule alias name etc.
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_schedule_setting.schedule_extra_json
     *
     * @mbg.generated
     */
    private String scheduleExtraJson;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_schedule_setting.id
     *
     * @return the value of appointment_pet_schedule_setting.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_schedule_setting.id
     *
     * @param id the value for appointment_pet_schedule_setting.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_schedule_setting.company_id
     *
     * @return the value of appointment_pet_schedule_setting.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_schedule_setting.company_id
     *
     * @param companyId the value for appointment_pet_schedule_setting.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_schedule_setting.appointment_id
     *
     * @return the value of appointment_pet_schedule_setting.appointment_id
     *
     * @mbg.generated
     */
    public Long getAppointmentId() {
        return appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_schedule_setting.appointment_id
     *
     * @param appointmentId the value for appointment_pet_schedule_setting.appointment_id
     *
     * @mbg.generated
     */
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_schedule_setting.schedule_type
     *
     * @return the value of appointment_pet_schedule_setting.schedule_type
     *
     * @mbg.generated
     */
    public Integer getScheduleType() {
        return scheduleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_schedule_setting.schedule_type
     *
     * @param scheduleType the value for appointment_pet_schedule_setting.schedule_type
     *
     * @mbg.generated
     */
    public void setScheduleType(Integer scheduleType) {
        this.scheduleType = scheduleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_schedule_setting.schedule_id
     *
     * @return the value of appointment_pet_schedule_setting.schedule_id
     *
     * @mbg.generated
     */
    public Long getScheduleId() {
        return scheduleId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_schedule_setting.schedule_id
     *
     * @param scheduleId the value for appointment_pet_schedule_setting.schedule_id
     *
     * @mbg.generated
     */
    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_schedule_setting.schedule_time
     *
     * @return the value of appointment_pet_schedule_setting.schedule_time
     *
     * @mbg.generated
     */
    public Integer getScheduleTime() {
        return scheduleTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_schedule_setting.schedule_time
     *
     * @param scheduleTime the value for appointment_pet_schedule_setting.schedule_time
     *
     * @mbg.generated
     */
    public void setScheduleTime(Integer scheduleTime) {
        this.scheduleTime = scheduleTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_schedule_setting.schedule_extra_json
     *
     * @return the value of appointment_pet_schedule_setting.schedule_extra_json
     *
     * @mbg.generated
     */
    public String getScheduleExtraJson() {
        return scheduleExtraJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_schedule_setting.schedule_extra_json
     *
     * @param scheduleExtraJson the value for appointment_pet_schedule_setting.schedule_extra_json
     *
     * @mbg.generated
     */
    public void setScheduleExtraJson(String scheduleExtraJson) {
        this.scheduleExtraJson = scheduleExtraJson == null ? null : scheduleExtraJson.trim();
    }
}
