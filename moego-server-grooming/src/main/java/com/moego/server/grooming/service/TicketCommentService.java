package com.moego.server.grooming.service;

import static java.time.temporal.ChronoUnit.DAYS;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.grooming.dto.appointment.comment.TicketComment;
import com.moego.server.grooming.dto.appointment.comment.TicketCommentsDTO;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingNoteMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingNoteExample;
import com.moego.server.grooming.mapstruct.TicketNoteMapper;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class TicketCommentService {
    private final MoeGroomingNoteMapper moeGroomingNoteMapper;
    private final AppointmentMapperProxy moeGroomingAppointmentMapper;
    private final ICustomerCustomerService customerCustomerService;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    @Deprecated
    public TicketCommentsDTO getTicketComment(Integer businessId, Integer appointmentId, Long petId) {
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
        if (appointment == null || !Objects.equals(appointment.getBusinessId(), businessId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not found");
        }

        MoeGroomingNoteExample example = new MoeGroomingNoteExample();
        MoeGroomingNoteExample.Criteria criteria = example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andCustomerIdEqualTo(appointment.getCustomerId())
                .andTypeIn(List.of(GroomingAppointmentEnum.NOTE_COMMENT, GroomingAppointmentEnum.NOTE_ADDITIONAL))
                .andIsDeletedEqualTo(false);
        if (petId != null && petId > 0) {
            criteria.andPetIdEqualTo(petId);
        }
        example.setOrderByClause("update_time desc");
        List<MoeGroomingNote> notes = moeGroomingNoteMapper.selectByExampleWithBLOBs(example);

        TicketComment commentForThisTicket = notes.stream()
                .filter(note -> note.getGroomingId().equals(appointmentId)
                        && note.getType().equals(GroomingAppointmentEnum.NOTE_COMMENT)
                        && note.getPetId().equals(petId))
                .findFirst()
                .map(TicketNoteMapper.INSTANCE::entity2DTO)
                .orElse(null);

        TicketComment clientCommentForThisTicket = notes.stream()
                .filter(note -> note.getGroomingId().equals(appointmentId)
                        && note.getType().equals(GroomingAppointmentEnum.NOTE_ADDITIONAL)
                        && note.getPetId().equals(petId))
                .findFirst()
                .map(TicketNoteMapper.INSTANCE::entity2DTO)
                .orElse(null);

        List<TicketComment> historyComments = notes.stream()
                .filter(note -> !note.getGroomingId().equals(appointmentId)
                        && note.getType().equals(GroomingAppointmentEnum.NOTE_COMMENT)
                        && note.getPetId().equals(petId))
                .map(TicketNoteMapper.INSTANCE::entity2DTO)
                .toList();

        return new TicketCommentsDTO(commentForThisTicket, clientCommentForThisTicket, historyComments);
    }

    public TicketCommentsDTO getTicketCommentV2(
            Integer businessId, Integer customerId, Integer appointmentId, Long companyId) {
        MoeGroomingNoteExample example = new MoeGroomingNoteExample();
        MoeGroomingNoteExample.Criteria criteria = example.createCriteria();
        if (companyId == null) {
            criteria.andBusinessIdEqualTo(businessId);
        } else {
            criteria.andCompanyIdEqualTo(companyId);
        }
        criteria.andCustomerIdEqualTo(customerId)
                .andTypeIn(List.of(GroomingAppointmentEnum.NOTE_COMMENT, GroomingAppointmentEnum.NOTE_ADDITIONAL))
                .andIsDeletedEqualTo(false);
        example.setOrderByClause("update_time desc");
        List<MoeGroomingNote> notes = moeGroomingNoteMapper.selectByExampleWithBLOBs(example);

        List<Long> staffIds = notes.stream()
                .filter(note -> !note.getType().equals(GroomingAppointmentEnum.NOTE_ADDITIONAL))
                .map(MoeGroomingNote::getUpdateBy)
                .distinct()
                .map(Integer::longValue)
                .toList();
        Map<Long, StaffModel> staffMap = getStaffMap(staffIds);
        // query appointment
        List<Integer> apptIds =
                notes.stream().map(MoeGroomingNote::getGroomingId).distinct().toList();
        Map<Integer, MoeGroomingAppointment> apptMapById;
        if (!CollectionUtils.isEmpty(apptIds)) {
            apptMapById = moeGroomingAppointmentMapper.selectByIdList(apptIds).stream()
                    .collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity()));
        } else {
            apptMapById = Map.of();
        }

        TicketComment commentForThisTicket = notes.stream()
                .filter(note -> note.getGroomingId().equals(appointmentId)
                        && note.getType().equals(GroomingAppointmentEnum.NOTE_COMMENT))
                .findFirst()
                .map(note -> {
                    TicketComment ticketComment = TicketNoteMapper.INSTANCE.entity2DTO(note);
                    if (staffMap.containsKey(ticketComment.getUpdateBy().longValue())) {
                        StaffModel staff =
                                staffMap.get(ticketComment.getUpdateBy().longValue());
                        ticketComment.setOperatorFirstName(staff.getFirstName());
                        ticketComment.setOperatorLastName(staff.getLastName());
                        ticketComment.setOperatorAvatar(staff.getAvatarPath());
                        ticketComment.setOperatorColorCode(staff.getColorCode());
                    }
                    if (apptMapById.containsKey(note.getGroomingId())) {
                        var appt = apptMapById.get(note.getGroomingId());
                        ticketComment.setAppointmentDate(appt.getAppointmentDate());
                        ticketComment.setAppointmentEndDate(appt.getAppointmentEndDate());
                    }
                    return ticketComment;
                })
                .orElse(null);

        List<TicketComment> historyComments = notes.stream()
                .filter(note -> !note.getGroomingId().equals(appointmentId)
                        && note.getType().equals(GroomingAppointmentEnum.NOTE_COMMENT))
                .map(note -> {
                    TicketComment ticketComment = TicketNoteMapper.INSTANCE.entity2DTO(note);
                    if (staffMap.containsKey(ticketComment.getUpdateBy().longValue())) {
                        StaffModel staff =
                                staffMap.get(ticketComment.getUpdateBy().longValue());
                        ticketComment.setOperatorFirstName(staff.getFirstName());
                        ticketComment.setOperatorLastName(staff.getLastName());
                        ticketComment.setOperatorAvatar(staff.getAvatarPath());
                        ticketComment.setOperatorColorCode(staff.getColorCode());
                    }
                    if (apptMapById.containsKey(note.getGroomingId())) {
                        var appt = apptMapById.get(note.getGroomingId());
                        ticketComment.setAppointmentDate(appt.getAppointmentDate());
                        ticketComment.setAppointmentEndDate(appt.getAppointmentEndDate());
                    }
                    return ticketComment;
                })
                .toList();

        var customer = customerCustomerService.getCustomerWithDeleted(customerId);
        TicketComment clientCommentForThisTicket = notes.stream()
                .filter(note -> note.getGroomingId().equals(appointmentId)
                        && note.getType().equals(GroomingAppointmentEnum.NOTE_ADDITIONAL))
                .findFirst()
                .map(note -> {
                    TicketComment ticketComment = TicketNoteMapper.INSTANCE.entity2DTO(note);
                    ticketComment.setOperatorFirstName(customer.getFirstName());
                    ticketComment.setOperatorLastName(customer.getLastName());
                    ticketComment.setOperatorAvatar(customer.getAvatarPath());
                    ticketComment.setOperatorColorCode(customer.getClientColor());
                    return ticketComment;
                })
                .orElse(null);
        return new TicketCommentsDTO(
                commentForThisTicket, clientCommentForThisTicket, removeDuplicatesTicketComment(historyComments));
    }

    /**
     * comment 去重 ERP-4023
     * 使用 last edit date time (只精确到分钟)、 staff、 comment 三个值去重，只展示一个， 展示 appointment date 离当前日期最近的一个 history appt
     *
     * @param comments
     * @return
     */
    public List<TicketComment> removeDuplicatesTicketComment(List<TicketComment> comments) {
        DateTimeFormatter minuteFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 用来去重的 map
        Map<String, TicketComment> uniqueCommentsMap = new HashMap<>();

        for (TicketComment comment : comments) {
            // 只精确到分钟
            LocalDateTime updateTime = Instant.ofEpochSecond(comment.getUpdateTime())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();

            String updateTimeMinutes = updateTime.format(minuteFormatter);

            String key = comment.getNote() + "_" + updateTimeMinutes + "_" + comment.getUpdateBy();

            TicketComment existingComment = uniqueCommentsMap.get(key);
            // key 相同的情况下，判断那天更近
            if (existingComment == null
                    || isCloserToToday(existingComment.getAppointmentDate(), comment.getAppointmentDate())) {
                uniqueCommentsMap.put(key, comment);
            }
        }
        return new ArrayList<>(uniqueCommentsMap.values())
                .stream()
                        .sorted(Comparator.comparing(TicketComment::getUpdateTime)
                                .reversed())
                        .collect(Collectors.toList());
    }

    public boolean isCloserToToday(String existingApptDate, String newApptDate) {
        if (!StringUtils.hasText(existingApptDate) || !StringUtils.hasText(newApptDate)) {
            return false;
        }
        try {
            LocalDate today = LocalDate.now();
            LocalDate existingDate = LocalDate.parse(existingApptDate);
            LocalDate newDate = LocalDate.parse(newApptDate);
            return Math.abs(DAYS.between(today, newDate)) < Math.abs(DAYS.between(today, existingDate));
        } catch (DateTimeParseException e) {
            log.error("parse date error", e);
            return false;
        }
    }

    private Map<Long, StaffModel> getStaffMap(List<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return new HashMap<>();
        }
        staffIds = staffIds.stream().filter(k -> k != null && k > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(staffIds)) {
            return new HashMap<>();
        }
        var resp = staffServiceBlockingStub.queryStaffByIds(
                QueryStaffByIdsRequest.newBuilder().addAllStaffIds(staffIds).build());
        return resp.getStaffsList().stream().collect(Collectors.toMap(StaffModel::getId, Function.identity()));
    }
}
