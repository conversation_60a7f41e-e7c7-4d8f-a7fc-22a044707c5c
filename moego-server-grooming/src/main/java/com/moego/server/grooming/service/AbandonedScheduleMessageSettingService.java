package com.moego.server.grooming.service;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.server.grooming.dto.AbandonedScheduleMessageSettingDTO.ClientType;
import static com.moego.server.grooming.dto.AbandonedScheduleMessageSettingDTO.SendOutType;
import static java.time.DayOfWeek.FRIDAY;
import static java.time.DayOfWeek.MONDAY;
import static java.time.DayOfWeek.THURSDAY;
import static java.time.DayOfWeek.TUESDAY;
import static java.time.DayOfWeek.WEDNESDAY;
import static org.springframework.util.CollectionUtils.firstElement;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.mapper.AbandonedScheduleMessageSettingMapper;
import com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSetting;
import com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSettingExample;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AbandonedScheduleMessageSettingService {

    private final AbandonedScheduleMessageSettingMapper abandonedScheduleMessageSettingMapper;

    /**
     * Get abandoned schedule message setting by business id, if not exists, init it.
     *
     * <p> This method is idempotent.
     *
     * @param businessId business id
     * @return {@link AbandonedScheduleMessageSetting}
     */
    public AbandonedScheduleMessageSetting getOrInit(Integer businessId) {
        AbandonedScheduleMessageSetting entity = get(businessId);
        if (entity != null) {
            return entity;
        }
        insert(businessId);
        return get(businessId);
    }

    /**
     * Update abandoned schedule message setting by business id.
     *
     * @param entity {@link AbandonedScheduleMessageSetting}
     * @return affected rows
     */
    public int updateByBusinessId(AbandonedScheduleMessageSetting entity) {
        Integer businessId = Optional.ofNullable(entity.getBusinessId())
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "businessId is required"));

        entity.setUpdatedAt(new Date());

        if (Boolean.FALSE.equals(getOrInit(businessId).getIsEnabled()) && Boolean.TRUE.equals(entity.getIsEnabled())) {
            entity.setEnabledAt(new Date());
        }

        AbandonedScheduleMessageSettingExample example = new AbandonedScheduleMessageSettingExample();
        example.createCriteria().andBusinessIdEqualTo(businessId);
        return abandonedScheduleMessageSettingMapper.updateByExampleSelective(entity, example);
    }

    private void insert(Integer businessId) {
        AbandonedScheduleMessageSetting setting = new AbandonedScheduleMessageSetting();
        setting.setBusinessId(businessId);
        setting.setIsEnabled(false);
        setting.setSendOutType(SendOutType.WAIT_FOR.name());
        setting.setWaitForTypeMinute(60);
        setting.setOnTypeDays(JsonUtil.toJson(List.of(MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY)));
        setting.setOnTypeMinute(9 * 60);
        setting.setClientTypes(JsonUtil.toJson(List.of(ClientType.EXISTING_CLIENTS)));
        try {
            abandonedScheduleMessageSettingMapper.insertSelective(setting);
        } catch (DuplicateKeyException e) {
            log.warn("AbandonedScheduleMessageSetting already exists, businessId: {}", businessId, e);
        }
    }

    private AbandonedScheduleMessageSetting get(Integer businessId) {
        AbandonedScheduleMessageSettingExample example = new AbandonedScheduleMessageSettingExample();
        example.createCriteria().andBusinessIdEqualTo(businessId);
        return firstElement(abandonedScheduleMessageSettingMapper.selectByExample(example));
    }
}
