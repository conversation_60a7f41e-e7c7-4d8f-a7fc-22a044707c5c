package com.moego.server.grooming.service;

import com.moego.common.response.ResponseResult;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-07-04 15:53
 */
@Service
public class TestService {

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    public ResponseResult<MoeGroomingAppointment> testDb() {
        MoeGroomingAppointment test = moeGroomingAppointmentMapper.selectByPrimaryKey(1);
        return ResponseResult.success(test);
    }
}
