package com.moego.server.grooming.service.utils;

import com.moego.idl.models.order.v1.OrderModel;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class OrderUtil {

    public static String buildItemKey(MoeGroomingInvoiceItem item) {
        // 相同 price 的 item 不再合并，
        // object id + pet id 可以作为唯一键
        // 为了兼容旧 order， 仍然保留 unitPrice，旧版 order 仍可以正常更新（旧 order line item 中 pet id 都为 0）
        return item.getServiceId() + "-" + item.getServiceUnitPrice().setScale(2, RoundingMode.HALF_UP) + "-"
                + item.getPetId();
    }

    public static String buildItemKeyForOldOrder(MoeGroomingInvoiceItem item) {
        return item.getServiceId() + "-" + item.getServiceUnitPrice().setScale(2, RoundingMode.HALF_UP);
    }

    public static String buildItemKey(ReportWebApptPetDetail petDetail) {
        return petDetail.getServiceId() + "-" + petDetail.getServicePrice().setScale(2, RoundingMode.HALF_UP) + "-"
                + petDetail.getPetId();
    }

    public static String buildItemKeyForOldOrder(ReportWebApptPetDetail petDetail) {
        return petDetail.getServiceId() + "-" + petDetail.getServicePrice().setScale(2, RoundingMode.HALF_UP);
    }

    public static String buildItemKey(Integer serviceId, BigDecimal servicePrice, Integer petId) {
        return serviceId + "-" + servicePrice.setScale(2, RoundingMode.HALF_UP) + "-" + petId;
    }

    public static boolean isOriginOrder(MoeGroomingInvoice invoice) {
        return OrderModel.OrderType.ORIGIN.name().equalsIgnoreCase(invoice.getOrderType());
    }
}
