package com.moego.server.grooming.mapstruct;

import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.params.SaveWithPetPetVo;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import com.moego.server.grooming.web.vo.ob.AbandonedClientVO;
import com.moego.server.grooming.web.vo.ob.OBPetDetailVO;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/7/5
 */
@Mapper
public interface PetMapper {
    PetMapper INSTANCE = Mappers.getMapper(PetMapper.class);

    List<OBPetDetailVO> petDetailDTO2OBPetDetailVO(List<CustomerPetDetailDTO> petDetailDTOList);

    @Mapping(target = "gender", expression = "java(int2Byte(petDetailDTO.getGender()))")
    @Mapping(target = "vetPhoneNumber", source = "vetPhone")
    @Mapping(target = "serviceId", ignore = true)
    @Mapping(target = "questionAnswerList", ignore = true)
    OBPetDetailVO petDetailDTO2OBPetDetailVO(CustomerPetDetailDTO petDetailDTO);

    default Byte int2Byte(Integer res) {
        if (Objects.isNull(res)) {
            return null;
        }
        return res.byteValue();
    }

    @Mapping(target = "customQuestions", ignore = true)
    CustomerProfileRequestDTO.PetProfileDTO dto2PetProfileDTO(CustomerPetDetailDTO dto);

    AbandonedClientVO.Pet abandonedPet2VO(MoeBookOnlineAbandonRecordPet pet);

    List<OBClientApptDTO.OBPetInfoDTO> petDetailDTO2PetInfoDTO(List<CustomerPetDetailDTO> dtoList);

    OBClientApptDTO.OBPetInfoDTO petDetailDTO2PetInfoDTO(CustomerPetDetailDTO dto);

    List<SaveWithPetPetVo> abandonEntity2SaveVO(List<MoeBookOnlineAbandonRecordPet> entityList);

    @Mapping(target = "vetPhone", ignore = true)
    @Mapping(target = "petCodeIdList", ignore = true)
    @Mapping(target = "expiryNotification", ignore = true)
    @Mapping(target = "breedMix", ignore = true)
    SaveWithPetPetVo abandonEntity2SaveVO(MoeBookOnlineAbandonRecordPet entity);
}
