package com.moego.server.grooming.service.statemachine.action;

import com.google.protobuf.util.Timestamps;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.appointment.v1.AppointmentPaymentStatus;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.helper.OrderHelper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.statemachine.context.ActionContext;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FinishAction implements IStateTransitionAction {

    private final AppointmentServiceBlockingStub appointmentStub;
    private final NewOrderHelper newOrderHelper;
    private final OrderHelper orderHelper;

    @Override
    public boolean suit(AppointmentStatusEnum newStatus) {
        return AppointmentStatusEnum.FINISHED.equals(newStatus);
    }

    @Override
    public int execute(MoeGroomingAppointment moeGroomingAppointment, ActionContext actionContext) {
        if (Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "Appointment[%d] status is %s, can not finish",
                            moeGroomingAppointment.getId(), moeGroomingAppointment.getStatus()));
        }

        if (Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.FINISHED.getValue())) {
            // already finished
            return 0;
        }

        // 保留之前的逻辑：where id = xx and status in (unconfirmed, confirmed, checked_in, ready)
        if (!Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.UNCONFIRMED_VALUE)
                && !Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.CONFIRMED_VALUE)
                && !Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.CHECKED_IN_VALUE)
                && !Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.READY_VALUE)) {
            return 0;
        }

        var builder = UpdateAppointmentSelectiveRequest.newBuilder();
        builder.setId(moeGroomingAppointment.getId());
        builder.setStatus(AppointmentStatus.FINISHED);
        builder.setUpdateTime(Timestamps.now());
        Optional.ofNullable(AuthContext.get().staffId()).ifPresent(builder::setUpdatedById);
        builder.setStatusBeforeFinish(AppointmentStatus.forNumber(moeGroomingAppointment.getStatus()));
        if (PrimitiveTypeUtil.isNumberNullOrZero(moeGroomingAppointment.getCheckInTime())) {
            builder.setCheckInTime(Instant.now().getEpochSecond());
        }
        if (PrimitiveTypeUtil.isNumberNullOrZero(moeGroomingAppointment.getReadyTime())) {
            builder.setReadyTime(Instant.now().getEpochSecond());
        }
        if (PrimitiveTypeUtil.isNumberNullOrZero(moeGroomingAppointment.getCheckOutTime())) {
            builder.setCheckOutTime(Instant.now().getEpochSecond());
        }

        // DONE new order flow
        if (newOrderHelper.isNewOrder(moeGroomingAppointment.getId())) {
            var invoiceId = orderHelper.createInvoiceId(moeGroomingAppointment);
            builder.setOrderId(invoiceId);
        }

        return appointmentStub.updateAppointmentSelective(builder.build()).getAffectedRows();
    }

    @Override
    public int revert(MoeGroomingAppointment moeGroomingAppointment) {
        if (!Objects.equals(AppointmentStatusEnum.FINISHED.getValue(), moeGroomingAppointment.getStatus())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "Appointment[%d] status is %s, can not revert",
                            moeGroomingAppointment.getId(), moeGroomingAppointment.getStatus()));
        }

        var builder = UpdateAppointmentSelectiveRequest.newBuilder();
        builder.setId(moeGroomingAppointment.getId());
        // 这里是为了兼容一些老数据，没有记录前置状态，就跳转到 confirmed 状态
        builder.setStatus(
                Objects.equals(moeGroomingAppointment.getStatusBeforeFinish(), AppointmentStatusEnum.UNKNOWN)
                        ? AppointmentStatus.CONFIRMED
                        : AppointmentStatus.forNumber(
                                moeGroomingAppointment.getStatusBeforeFinish().getValue()));
        builder.setUpdateTime(Timestamps.now());
        Optional.ofNullable(AuthContext.get().staffId()).ifPresent(builder::setUpdatedById);
        builder.setStatusBeforeFinish(AppointmentStatus.forNumber(AppointmentStatusEnum.UNCONFIRMED.getValue()));
        builder.setCheckOutTime(0L);
        if (Objects.equals(moeGroomingAppointment.getIsPaid(), GroomingAppointmentEnum.PAID)) {
            builder.setPaymentStatus(AppointmentPaymentStatus.PARTIAL_PAID);
        }
        switch (moeGroomingAppointment.getStatusBeforeFinish()) {
                // 如果是回到 CHECK IN 状态，需要置空 ready time
            case CHECK_IN -> builder.setReadyTime(0L);
                // 如果是回到 READY 状态，什么都不需要做
            case READY -> {
                // do nothing
            }
                // 如果回到其他状态（目前只会包括 confirmed 和 unconfirmed），则需要置空 check in time 和 ready time
            default -> {
                builder.setCheckInTime(0L);
                builder.setReadyTime(0L);
            }
        }

        // 如果是回退到 READY 的前置状态，需要清空 ready time
        if (!Objects.equals(AppointmentStatusEnum.READY, moeGroomingAppointment.getStatusBeforeFinish())) {
            builder.setReadyTime(0L);
            // 如果是回退到 CHECK_IN 的前置状态，需要清空 check in time
            if (!Objects.equals(AppointmentStatusEnum.CHECK_IN, moeGroomingAppointment.getStatusBeforeFinish())) {
                builder.setCheckInTime(0L);
            }
        }

        return appointmentStub.updateAppointmentSelective(builder.build()).getAffectedRows();
    }
}
