package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_gc_auth_staff
 */
public class MoeGcAuthStaff {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   发起授权的staff
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   从calendar解析出来的日历email
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.email
     *
     * @mbg.generated
     */
    private String email;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.access_token
     *
     * @mbg.generated
     */
    private String accessToken;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.refresh_token
     *
     * @mbg.generated
     */
    private String refreshToken;

    /**
     * Database Column Remarks:
     *   access token 过期时间戳(提前10分钟更新)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.token_expired_time
     *
     * @mbg.generated
     */
    private Long tokenExpiredTime;

    /**
     * Database Column Remarks:
     *   主日历id 唯一，不会空，无法删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.main_calendar_id
     *
     * @mbg.generated
     */
    private String mainCalendarId;

    /**
     * Database Column Remarks:
     *   1 正常 2 用户取消 3过期失效
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_auth_staff.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.id
     *
     * @return the value of moe_gc_auth_staff.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.id
     *
     * @param id the value for moe_gc_auth_staff.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.business_id
     *
     * @return the value of moe_gc_auth_staff.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.business_id
     *
     * @param businessId the value for moe_gc_auth_staff.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.staff_id
     *
     * @return the value of moe_gc_auth_staff.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.staff_id
     *
     * @param staffId the value for moe_gc_auth_staff.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.email
     *
     * @return the value of moe_gc_auth_staff.email
     *
     * @mbg.generated
     */
    public String getEmail() {
        return email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.email
     *
     * @param email the value for moe_gc_auth_staff.email
     *
     * @mbg.generated
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.access_token
     *
     * @return the value of moe_gc_auth_staff.access_token
     *
     * @mbg.generated
     */
    public String getAccessToken() {
        return accessToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.access_token
     *
     * @param accessToken the value for moe_gc_auth_staff.access_token
     *
     * @mbg.generated
     */
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken == null ? null : accessToken.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.refresh_token
     *
     * @return the value of moe_gc_auth_staff.refresh_token
     *
     * @mbg.generated
     */
    public String getRefreshToken() {
        return refreshToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.refresh_token
     *
     * @param refreshToken the value for moe_gc_auth_staff.refresh_token
     *
     * @mbg.generated
     */
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken == null ? null : refreshToken.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.token_expired_time
     *
     * @return the value of moe_gc_auth_staff.token_expired_time
     *
     * @mbg.generated
     */
    public Long getTokenExpiredTime() {
        return tokenExpiredTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.token_expired_time
     *
     * @param tokenExpiredTime the value for moe_gc_auth_staff.token_expired_time
     *
     * @mbg.generated
     */
    public void setTokenExpiredTime(Long tokenExpiredTime) {
        this.tokenExpiredTime = tokenExpiredTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.main_calendar_id
     *
     * @return the value of moe_gc_auth_staff.main_calendar_id
     *
     * @mbg.generated
     */
    public String getMainCalendarId() {
        return mainCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.main_calendar_id
     *
     * @param mainCalendarId the value for moe_gc_auth_staff.main_calendar_id
     *
     * @mbg.generated
     */
    public void setMainCalendarId(String mainCalendarId) {
        this.mainCalendarId = mainCalendarId == null ? null : mainCalendarId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.status
     *
     * @return the value of moe_gc_auth_staff.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.status
     *
     * @param status the value for moe_gc_auth_staff.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.create_time
     *
     * @return the value of moe_gc_auth_staff.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.create_time
     *
     * @param createTime the value for moe_gc_auth_staff.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.update_time
     *
     * @return the value of moe_gc_auth_staff.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.update_time
     *
     * @param updateTime the value for moe_gc_auth_staff.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_auth_staff.company_id
     *
     * @return the value of moe_gc_auth_staff.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_auth_staff.company_id
     *
     * @param companyId the value for moe_gc_auth_staff.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
