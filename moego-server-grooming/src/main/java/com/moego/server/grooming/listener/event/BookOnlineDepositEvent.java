package com.moego.server.grooming.listener.event;

import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
public abstract sealed class BookOnlineDepositEvent extends ApplicationEvent {

    BookOnlineDepositEvent(Object source) {
        super(source);
    }

    @Getter
    @EqualsAndHashCode(callSuper = true)
    public static final class Created extends BookOnlineDepositEvent {
        private final transient MoeBookOnlineDeposit after;

        public Created(Object source, MoeBookOnlineDeposit after) {
            super(source);
            this.after = after;
        }
    }

    @Getter
    @EqualsAndHashCode(callSuper = true)
    public static final class Updated extends BookOnlineDepositEvent {
        private final transient MoeBookOnlineDeposit before;
        private final transient MoeBookOnlineDeposit after;

        public Updated(Object source, MoeBookOnlineDeposit before, <PERSON>BookOnlineDeposit after) {
            super(source);
            this.before = before;
            this.after = after;
        }
    }

    @Getter
    @EqualsAndHashCode(callSuper = true)
    public static final class Deleted extends BookOnlineDepositEvent {
        private final transient MoeBookOnlineDeposit before;

        public Deleted(Object source, MoeBookOnlineDeposit before) {
            super(source);
            this.before = before;
        }
    }
}
