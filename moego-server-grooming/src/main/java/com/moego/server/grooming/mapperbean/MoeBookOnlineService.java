package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_service
 */
public class MoeBookOnlineService {
    /**
     * Database Column Remarks:
     *   商家ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   服务id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   booking online 是否显示价格
     *   0 do not show price
     *   1 show fixd service price
     *   2 show price with "starting at"
     *   3 show price sa "Varies"
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.show_base_price
     *
     * @mbg.generated
     */
    private Byte showBasePrice;

    /**
     * Database Column Remarks:
     *   0-not  1-yes
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.book_online_available
     *
     * @mbg.generated
     */
    private Byte bookOnlineAvailable;

    /**
     * Database Column Remarks:
     *   是否全选staff
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.is_all_staff
     *
     * @mbg.generated
     */
    private Byte isAllStaff;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_service.allow_booking_with_other_care_type
     *
     * @mbg.generated
     */
    private Boolean allowBookingWithOtherCareType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.id
     *
     * @return the value of moe_book_online_service.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.id
     *
     * @param id the value for moe_book_online_service.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.company_id
     *
     * @return the value of moe_book_online_service.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.company_id
     *
     * @param companyId the value for moe_book_online_service.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.business_id
     *
     * @return the value of moe_book_online_service.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.business_id
     *
     * @param businessId the value for moe_book_online_service.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.service_id
     *
     * @return the value of moe_book_online_service.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.service_id
     *
     * @param serviceId the value for moe_book_online_service.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.show_base_price
     *
     * @return the value of moe_book_online_service.show_base_price
     *
     * @mbg.generated
     */
    public Byte getShowBasePrice() {
        return showBasePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.show_base_price
     *
     * @param showBasePrice the value for moe_book_online_service.show_base_price
     *
     * @mbg.generated
     */
    public void setShowBasePrice(Byte showBasePrice) {
        this.showBasePrice = showBasePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.book_online_available
     *
     * @return the value of moe_book_online_service.book_online_available
     *
     * @mbg.generated
     */
    public Byte getBookOnlineAvailable() {
        return bookOnlineAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.book_online_available
     *
     * @param bookOnlineAvailable the value for moe_book_online_service.book_online_available
     *
     * @mbg.generated
     */
    public void setBookOnlineAvailable(Byte bookOnlineAvailable) {
        this.bookOnlineAvailable = bookOnlineAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.is_all_staff
     *
     * @return the value of moe_book_online_service.is_all_staff
     *
     * @mbg.generated
     */
    public Byte getIsAllStaff() {
        return isAllStaff;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.is_all_staff
     *
     * @param isAllStaff the value for moe_book_online_service.is_all_staff
     *
     * @mbg.generated
     */
    public void setIsAllStaff(Byte isAllStaff) {
        this.isAllStaff = isAllStaff;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.create_time
     *
     * @return the value of moe_book_online_service.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.create_time
     *
     * @param createTime the value for moe_book_online_service.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.update_time
     *
     * @return the value of moe_book_online_service.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.update_time
     *
     * @param updateTime the value for moe_book_online_service.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_service.allow_booking_with_other_care_type
     *
     * @return the value of moe_book_online_service.allow_booking_with_other_care_type
     *
     * @mbg.generated
     */
    public Boolean getAllowBookingWithOtherCareType() {
        return allowBookingWithOtherCareType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_service.allow_booking_with_other_care_type
     *
     * @param allowBookingWithOtherCareType the value for moe_book_online_service.allow_booking_with_other_care_type
     *
     * @mbg.generated
     */
    public void setAllowBookingWithOtherCareType(Boolean allowBookingWithOtherCareType) {
        this.allowBookingWithOtherCareType = allowBookingWithOtherCareType;
    }
}
