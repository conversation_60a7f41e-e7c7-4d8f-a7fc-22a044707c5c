package com.moego.server.grooming.mapperbean;

import com.moego.server.grooming.enums.PackageActivityEnum;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_package_history
 */
public class MoeGroomingPackageHistory {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   packageid
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.package_id
     *
     * @mbg.generated
     */
    private Integer packageId;

    /**
     * Database Column Remarks:
     *   package service id, moe_grooming_package_service.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.package_service_id
     *
     * @mbg.generated
     */
    private Integer packageServiceId;

    /**
     * Database Column Remarks:
     *   服务id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   使用数量
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.quantity
     *
     * @mbg.generated
     */
    private Integer quantity;

    /**
     * Database Column Remarks:
     *   预约日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.appointment_date
     *
     * @mbg.generated
     */
    private String appointmentDate;

    /**
     * Database Column Remarks:
     *   使用时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.use_time
     *
     * @mbg.generated
     */
    private Long useTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     * Database Column Remarks:
     *   invoice主键id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.invoice_id
     *
     * @mbg.generated
     */
    private Integer invoiceId;

    /**
     * Database Column Remarks:
     *   状态：1-正常，2-删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   1-Redeem package; 2-Extend package; 3-Manual change
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.activity_type
     *
     * @mbg.generated
     */
    private PackageActivityEnum activityType;

    /**
     * Database Column Remarks:
     *   延长有效期后的截止日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.after_extend_expire_date
     *
     * @mbg.generated
     */
    private String afterExtendExpireDate;

    /**
     * Database Column Remarks:
     *   operate staff id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_history.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.id
     *
     * @return the value of moe_grooming_package_history.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.id
     *
     * @param id the value for moe_grooming_package_history.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.package_id
     *
     * @return the value of moe_grooming_package_history.package_id
     *
     * @mbg.generated
     */
    public Integer getPackageId() {
        return packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.package_id
     *
     * @param packageId the value for moe_grooming_package_history.package_id
     *
     * @mbg.generated
     */
    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.package_service_id
     *
     * @return the value of moe_grooming_package_history.package_service_id
     *
     * @mbg.generated
     */
    public Integer getPackageServiceId() {
        return packageServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.package_service_id
     *
     * @param packageServiceId the value for moe_grooming_package_history.package_service_id
     *
     * @mbg.generated
     */
    public void setPackageServiceId(Integer packageServiceId) {
        this.packageServiceId = packageServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.service_id
     *
     * @return the value of moe_grooming_package_history.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.service_id
     *
     * @param serviceId the value for moe_grooming_package_history.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.quantity
     *
     * @return the value of moe_grooming_package_history.quantity
     *
     * @mbg.generated
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.quantity
     *
     * @param quantity the value for moe_grooming_package_history.quantity
     *
     * @mbg.generated
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.appointment_date
     *
     * @return the value of moe_grooming_package_history.appointment_date
     *
     * @mbg.generated
     */
    public String getAppointmentDate() {
        return appointmentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.appointment_date
     *
     * @param appointmentDate the value for moe_grooming_package_history.appointment_date
     *
     * @mbg.generated
     */
    public void setAppointmentDate(String appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.use_time
     *
     * @return the value of moe_grooming_package_history.use_time
     *
     * @mbg.generated
     */
    public Long getUseTime() {
        return useTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.use_time
     *
     * @param useTime the value for moe_grooming_package_history.use_time
     *
     * @mbg.generated
     */
    public void setUseTime(Long useTime) {
        this.useTime = useTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.grooming_id
     *
     * @return the value of moe_grooming_package_history.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.grooming_id
     *
     * @param groomingId the value for moe_grooming_package_history.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.invoice_id
     *
     * @return the value of moe_grooming_package_history.invoice_id
     *
     * @mbg.generated
     */
    public Integer getInvoiceId() {
        return invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.invoice_id
     *
     * @param invoiceId the value for moe_grooming_package_history.invoice_id
     *
     * @mbg.generated
     */
    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.status
     *
     * @return the value of moe_grooming_package_history.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.status
     *
     * @param status the value for moe_grooming_package_history.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.activity_type
     *
     * @return the value of moe_grooming_package_history.activity_type
     *
     * @mbg.generated
     */
    public PackageActivityEnum getActivityType() {
        return activityType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.activity_type
     *
     * @param activityType the value for moe_grooming_package_history.activity_type
     *
     * @mbg.generated
     */
    public void setActivityType(PackageActivityEnum activityType) {
        this.activityType = activityType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.after_extend_expire_date
     *
     * @return the value of moe_grooming_package_history.after_extend_expire_date
     *
     * @mbg.generated
     */
    public String getAfterExtendExpireDate() {
        return afterExtendExpireDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.after_extend_expire_date
     *
     * @param afterExtendExpireDate the value for moe_grooming_package_history.after_extend_expire_date
     *
     * @mbg.generated
     */
    public void setAfterExtendExpireDate(String afterExtendExpireDate) {
        this.afterExtendExpireDate = afterExtendExpireDate == null ? null : afterExtendExpireDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_history.staff_id
     *
     * @return the value of moe_grooming_package_history.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_history.staff_id
     *
     * @param staffId the value for moe_grooming_package_history.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }
}
