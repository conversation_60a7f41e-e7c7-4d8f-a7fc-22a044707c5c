package com.moego.server.grooming.properties;

import static com.moego.server.grooming.properties.GoogleReserveProperties.PREFIX;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(PREFIX)
public class GoogleReserveProperties {

    public static final String PREFIX = "moego.google-reserve";

    /**
     * Google partner id.
     *
     * @see <a href="https://developers.google.com/maps-booking/verticals/appointment-booking/guides/integration/conversion-tracking#sending">Conversion Tracking</a>
     */
    private String partnerId;
    /**
     * Google conversion tracking endpoint.
     *
     * @see <a href="https://developers.google.com/maps-booking/verticals/appointment-booking/guides/integration/conversion-tracking#sending">Conversion Tracking</a>
     */
    private String conversionTrackingUrl;
}
