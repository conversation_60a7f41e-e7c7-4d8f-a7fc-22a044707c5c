package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_setting
 */
public class MoeQbSetting {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   关联的connect id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.connect_id
     *
     * @mbg.generated
     */
    private Integer connectId;

    /**
     * Database Column Remarks:
     *   是否开启同步  1 开启  0 关闭(默认)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.enable_sync
     *
     * @mbg.generated
     */
    private Byte enableSync;

    /**
     * Database Column Remarks:
     *   预约开始同步日期(默认创建时间)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.sync_begin_date
     *
     * @mbg.generated
     */
    private String syncBeginDate;

    /**
     * Database Column Remarks:
     *   当前设置日期变动后，已同步过的时间，最小同步日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.min_sync_date
     *
     * @mbg.generated
     */
    private String minSyncDate;

    /**
     * Database Column Remarks:
     *   设置记录状态  1 正常  2已过期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   tax sync type, 1-in line, 2-in total
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.tax_sync_type
     *
     * @mbg.generated
     */
    private Byte taxSyncType;

    /**
     * Database Column Remarks:
     *   用于标记qb sync的版本 0-存量用户, 1-增量用户
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.user_version
     *
     * @mbg.generated
     */
    private Integer userVersion;

    /**
     * Database Column Remarks:
     *   last disconnected time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.last_disconnected_time
     *
     * @mbg.generated
     */
    private String lastDisconnectedTime;

    /**
     * Database Column Remarks:
     *   是否同步sales receipt? 0-同步, 1-不同步
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_setting.sales_receipt_enable
     *
     * @mbg.generated
     */
    private Byte salesReceiptEnable;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.id
     *
     * @return the value of moe_qb_setting.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.id
     *
     * @param id the value for moe_qb_setting.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.business_id
     *
     * @return the value of moe_qb_setting.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.business_id
     *
     * @param businessId the value for moe_qb_setting.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.connect_id
     *
     * @return the value of moe_qb_setting.connect_id
     *
     * @mbg.generated
     */
    public Integer getConnectId() {
        return connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.connect_id
     *
     * @param connectId the value for moe_qb_setting.connect_id
     *
     * @mbg.generated
     */
    public void setConnectId(Integer connectId) {
        this.connectId = connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.enable_sync
     *
     * @return the value of moe_qb_setting.enable_sync
     *
     * @mbg.generated
     */
    public Byte getEnableSync() {
        return enableSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.enable_sync
     *
     * @param enableSync the value for moe_qb_setting.enable_sync
     *
     * @mbg.generated
     */
    public void setEnableSync(Byte enableSync) {
        this.enableSync = enableSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.sync_begin_date
     *
     * @return the value of moe_qb_setting.sync_begin_date
     *
     * @mbg.generated
     */
    public String getSyncBeginDate() {
        return syncBeginDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.sync_begin_date
     *
     * @param syncBeginDate the value for moe_qb_setting.sync_begin_date
     *
     * @mbg.generated
     */
    public void setSyncBeginDate(String syncBeginDate) {
        this.syncBeginDate = syncBeginDate == null ? null : syncBeginDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.min_sync_date
     *
     * @return the value of moe_qb_setting.min_sync_date
     *
     * @mbg.generated
     */
    public String getMinSyncDate() {
        return minSyncDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.min_sync_date
     *
     * @param minSyncDate the value for moe_qb_setting.min_sync_date
     *
     * @mbg.generated
     */
    public void setMinSyncDate(String minSyncDate) {
        this.minSyncDate = minSyncDate == null ? null : minSyncDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.status
     *
     * @return the value of moe_qb_setting.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.status
     *
     * @param status the value for moe_qb_setting.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.create_time
     *
     * @return the value of moe_qb_setting.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.create_time
     *
     * @param createTime the value for moe_qb_setting.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.update_time
     *
     * @return the value of moe_qb_setting.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.update_time
     *
     * @param updateTime the value for moe_qb_setting.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.company_id
     *
     * @return the value of moe_qb_setting.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.company_id
     *
     * @param companyId the value for moe_qb_setting.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.tax_sync_type
     *
     * @return the value of moe_qb_setting.tax_sync_type
     *
     * @mbg.generated
     */
    public Byte getTaxSyncType() {
        return taxSyncType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.tax_sync_type
     *
     * @param taxSyncType the value for moe_qb_setting.tax_sync_type
     *
     * @mbg.generated
     */
    public void setTaxSyncType(Byte taxSyncType) {
        this.taxSyncType = taxSyncType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.user_version
     *
     * @return the value of moe_qb_setting.user_version
     *
     * @mbg.generated
     */
    public Integer getUserVersion() {
        return userVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.user_version
     *
     * @param userVersion the value for moe_qb_setting.user_version
     *
     * @mbg.generated
     */
    public void setUserVersion(Integer userVersion) {
        this.userVersion = userVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.last_disconnected_time
     *
     * @return the value of moe_qb_setting.last_disconnected_time
     *
     * @mbg.generated
     */
    public String getLastDisconnectedTime() {
        return lastDisconnectedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.last_disconnected_time
     *
     * @param lastDisconnectedTime the value for moe_qb_setting.last_disconnected_time
     *
     * @mbg.generated
     */
    public void setLastDisconnectedTime(String lastDisconnectedTime) {
        this.lastDisconnectedTime = lastDisconnectedTime == null ? null : lastDisconnectedTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_setting.sales_receipt_enable
     *
     * @return the value of moe_qb_setting.sales_receipt_enable
     *
     * @mbg.generated
     */
    public Byte getSalesReceiptEnable() {
        return salesReceiptEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_setting.sales_receipt_enable
     *
     * @param salesReceiptEnable the value for moe_qb_setting.sales_receipt_enable
     *
     * @mbg.generated
     */
    public void setSalesReceiptEnable(Byte salesReceiptEnable) {
        this.salesReceiptEnable = salesReceiptEnable;
    }
}
