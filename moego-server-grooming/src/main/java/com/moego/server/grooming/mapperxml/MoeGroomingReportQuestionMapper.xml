<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingReportQuestionMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="category" jdbcType="TINYINT" property="category" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="key" jdbcType="VARCHAR" property="key" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
    <result column="required" jdbcType="BIT" property="required" />
    <result column="type_editable" jdbcType="BIT" property="typeEditable" />
    <result column="title_editable" jdbcType="BIT" property="titleEditable" />
    <result column="options_editable" jdbcType="BIT" property="optionsEditable" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, category, type, `key`, title, is_default, required, type_editable,
    title_editable, options_editable, sort, `status`, create_time, update_time, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    extra_json
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_grooming_report_question
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_report_question
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_report_question (business_id, category, type,
      `key`, title, is_default,
      required, type_editable, title_editable,
      options_editable, sort, `status`,
      create_time, update_time, company_id,
      extra_json)
    values (#{businessId,jdbcType=INTEGER}, #{category,jdbcType=TINYINT}, #{type,jdbcType=VARCHAR},
      #{key,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{isDefault,jdbcType=BIT},
      #{required,jdbcType=BIT}, #{typeEditable,jdbcType=BIT}, #{titleEditable,jdbcType=BIT},
      #{optionsEditable,jdbcType=BIT}, #{sort,jdbcType=INTEGER}, #{status,jdbcType=TINYINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT},
      #{extraJson,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_report_question
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="key != null">
        `key`,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
      <if test="required != null">
        required,
      </if>
      <if test="typeEditable != null">
        type_editable,
      </if>
      <if test="titleEditable != null">
        title_editable,
      </if>
      <if test="optionsEditable != null">
        options_editable,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="extraJson != null">
        extra_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="category != null">
        #{category,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=BIT},
      </if>
      <if test="required != null">
        #{required,jdbcType=BIT},
      </if>
      <if test="typeEditable != null">
        #{typeEditable,jdbcType=BIT},
      </if>
      <if test="titleEditable != null">
        #{titleEditable,jdbcType=BIT},
      </if>
      <if test="optionsEditable != null">
        #{optionsEditable,jdbcType=BIT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="extraJson != null">
        #{extraJson,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report_question
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        `key` = #{key,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
      <if test="required != null">
        required = #{required,jdbcType=BIT},
      </if>
      <if test="typeEditable != null">
        type_editable = #{typeEditable,jdbcType=BIT},
      </if>
      <if test="titleEditable != null">
        title_editable = #{titleEditable,jdbcType=BIT},
      </if>
      <if test="optionsEditable != null">
        options_editable = #{optionsEditable,jdbcType=BIT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="extraJson != null">
        extra_json = #{extraJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report_question
    set business_id = #{businessId,jdbcType=INTEGER},
      category = #{category,jdbcType=TINYINT},
      type = #{type,jdbcType=VARCHAR},
      `key` = #{key,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=BIT},
      required = #{required,jdbcType=BIT},
      type_editable = #{typeEditable,jdbcType=BIT},
      title_editable = #{titleEditable,jdbcType=BIT},
      options_editable = #{optionsEditable,jdbcType=BIT},
      sort = #{sort,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      extra_json = #{extraJson,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_report_question
    set business_id = #{businessId,jdbcType=INTEGER},
      category = #{category,jdbcType=TINYINT},
      type = #{type,jdbcType=VARCHAR},
      `key` = #{key,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=BIT},
      required = #{required,jdbcType=BIT},
      type_editable = #{typeEditable,jdbcType=BIT},
      title_editable = #{titleEditable,jdbcType=BIT},
      options_editable = #{optionsEditable,jdbcType=BIT},
      sort = #{sort,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByBusinessId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    select count(*) from moe_grooming_report_question
    where business_id = #{businessId} and status = 0
  </select>

  <insert id="initGroomingReportQuestions">
    insert into moe_grooming_report_question (business_id, category, type, `key`, title, is_default, required,
        type_editable, title_editable, options_editable, sort, `status`, extra_json, company_id)
    values
    <foreach collection="questions" item="item" separator=",">
      (#{item.businessId,jdbcType=INTEGER}, #{item.category,jdbcType=TINYINT}, #{item.type,jdbcType=VARCHAR},
       #{item.key,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR}, #{item.isDefault,jdbcType=BIT},
       #{item.required,jdbcType=BIT}, #{item.typeEditable,jdbcType=BIT}, #{item.titleEditable,jdbcType=BIT},
       #{item.optionsEditable,jdbcType=BIT}, #{item.sort,jdbcType=INTEGER},
       #{item.status,jdbcType=TINYINT}, #{item.extraJson,jdbcType=LONGVARCHAR}, #{item.companyId,jdbcType=BIGINT})
    </foreach>
  </insert>

  <select id="selectByBusinessId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />, <include refid="Blob_Column_List" />
    from moe_grooming_report_question
    where business_id = #{businessId,jdbcType=INTEGER}
    and status = 0
  </select>

  <select id="selectByBusinessIdAndCategory" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />, <include refid="Blob_Column_List" />
    from moe_grooming_report_question
    where business_id = #{businessId,jdbcType=INTEGER}
    and category = #{category,jdbcType=TINYINT}
    and status = 0
  </select>

  <select id="selectByBusinessIdAndIds" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />, <include refid="Blob_Column_List" />
    from moe_grooming_report_question
    where business_id = #{businessId,jdbcType=INTEGER} and id in
    <foreach close=")" collection="ids" index="index" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </select>

</mapper>
