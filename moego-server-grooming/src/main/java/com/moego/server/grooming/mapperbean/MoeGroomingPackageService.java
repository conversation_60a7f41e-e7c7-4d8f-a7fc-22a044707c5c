package com.moego.server.grooming.mapperbean;

import com.moego.server.retail.dto.PackageInfoDto.Service;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_package_service
 */
public class MoeGroomingPackageService {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_service.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   打包id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_service.package_id
     *
     * @mbg.generated
     */
    private Integer packageId;

    /**
     * Database Column Remarks:
     *   服务id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_service.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   打包服务总数量
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_service.total_quantity
     *
     * @mbg.generated
     */
    private Integer totalQuantity;

    /**
     * Database Column Remarks:
     *   打包服务剩余数量
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_service.remaining_quantity
     *
     * @mbg.generated
     */
    private Integer remainingQuantity;

    /**
     * Database Column Remarks:
     *   打包服务单价
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_service.service_unit_price
     *
     * @mbg.generated
     */
    private BigDecimal serviceUnitPrice;

    /**
     * Database Column Remarks:
     *   package item 支持选择多个 service，多个 service 之间共享 quantity，单个 service 结构参考：com.moego.server.retail.dto.PackageInfoDto.Service
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_package_service.services
     *
     * @mbg.generated
     */
    private List<Service> services;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_service.id
     *
     * @return the value of moe_grooming_package_service.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_service.id
     *
     * @param id the value for moe_grooming_package_service.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_service.package_id
     *
     * @return the value of moe_grooming_package_service.package_id
     *
     * @mbg.generated
     */
    public Integer getPackageId() {
        return packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_service.package_id
     *
     * @param packageId the value for moe_grooming_package_service.package_id
     *
     * @mbg.generated
     */
    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_service.service_id
     *
     * @return the value of moe_grooming_package_service.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_service.service_id
     *
     * @param serviceId the value for moe_grooming_package_service.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_service.total_quantity
     *
     * @return the value of moe_grooming_package_service.total_quantity
     *
     * @mbg.generated
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_service.total_quantity
     *
     * @param totalQuantity the value for moe_grooming_package_service.total_quantity
     *
     * @mbg.generated
     */
    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_service.remaining_quantity
     *
     * @return the value of moe_grooming_package_service.remaining_quantity
     *
     * @mbg.generated
     */
    public Integer getRemainingQuantity() {
        return remainingQuantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_service.remaining_quantity
     *
     * @param remainingQuantity the value for moe_grooming_package_service.remaining_quantity
     *
     * @mbg.generated
     */
    public void setRemainingQuantity(Integer remainingQuantity) {
        this.remainingQuantity = remainingQuantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_service.service_unit_price
     *
     * @return the value of moe_grooming_package_service.service_unit_price
     *
     * @mbg.generated
     */
    public BigDecimal getServiceUnitPrice() {
        return serviceUnitPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_service.service_unit_price
     *
     * @param serviceUnitPrice the value for moe_grooming_package_service.service_unit_price
     *
     * @mbg.generated
     */
    public void setServiceUnitPrice(BigDecimal serviceUnitPrice) {
        this.serviceUnitPrice = serviceUnitPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_package_service.services
     *
     * @return the value of moe_grooming_package_service.services
     *
     * @mbg.generated
     */
    public List<Service> getServices() {
        return services;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_package_service.services
     *
     * @param services the value for moe_grooming_package_service.services
     *
     * @mbg.generated
     */
    public void setServices(List<Service> services) {
        this.services = services;
    }
}
