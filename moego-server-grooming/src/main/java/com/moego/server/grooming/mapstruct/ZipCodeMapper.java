package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.ob.MoeZipCodeDTO;
import com.moego.server.grooming.mapperbean.MoeZipcode;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ZipCodeMapper {
    ZipCodeMapper INSTANCE = Mappers.getMapper(ZipCodeMapper.class);

    MoeZipCodeDTO toMoeZipCodeDTO(MoeZipcode entity);

    List<MoeZipCodeDTO> toMoeZipCodeDTOList(List<MoeZipcode> ins);
}
