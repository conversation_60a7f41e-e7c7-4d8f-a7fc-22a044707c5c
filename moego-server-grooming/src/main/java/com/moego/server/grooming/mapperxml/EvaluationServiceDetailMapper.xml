<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.EvaluationServiceDetailMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.EvaluationServiceDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="appointment_id" jdbcType="BIGINT" property="appointmentId" />
    <result column="pet_id" jdbcType="BIGINT" property="petId" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
    <result column="service_time" jdbcType="INTEGER" property="serviceTime" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="start_time" jdbcType="INTEGER" property="startTime" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="end_time" jdbcType="INTEGER" property="endTime" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, appointment_id, pet_id, service_id, service_price, service_time, start_date, 
    start_time, end_date, end_time, created_at, updated_at, staff_id
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.EvaluationServiceDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from evaluation_service_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from evaluation_service_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from evaluation_service_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.EvaluationServiceDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from evaluation_service_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.EvaluationServiceDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluation_service_detail (appointment_id, pet_id, service_id, 
      service_price, service_time, start_date, 
      start_time, end_date, end_time, 
      created_at, updated_at, staff_id
      )
    values (#{appointmentId,jdbcType=BIGINT}, #{petId,jdbcType=BIGINT}, #{serviceId,jdbcType=BIGINT}, 
      #{servicePrice,jdbcType=DECIMAL}, #{serviceTime,jdbcType=INTEGER}, #{startDate,jdbcType=DATE}, 
      #{startTime,jdbcType=INTEGER}, #{endDate,jdbcType=DATE}, #{endTime,jdbcType=INTEGER}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{staffId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.EvaluationServiceDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluation_service_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appointmentId != null">
        appointment_id,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="servicePrice != null">
        service_price,
      </if>
      <if test="serviceTime != null">
        service_time,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appointmentId != null">
        #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=BIGINT},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="servicePrice != null">
        #{servicePrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceTime != null">
        #{serviceTime,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.EvaluationServiceDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from evaluation_service_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluation_service_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appointmentId != null">
        appointment_id = #{record.appointmentId,jdbcType=BIGINT},
      </if>
      <if test="record.petId != null">
        pet_id = #{record.petId,jdbcType=BIGINT},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=BIGINT},
      </if>
      <if test="record.servicePrice != null">
        service_price = #{record.servicePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceTime != null">
        service_time = #{record.serviceTime,jdbcType=INTEGER},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=DATE},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=INTEGER},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=DATE},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=INTEGER},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.staffId != null">
        staff_id = #{record.staffId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluation_service_detail
    set id = #{record.id,jdbcType=BIGINT},
      appointment_id = #{record.appointmentId,jdbcType=BIGINT},
      pet_id = #{record.petId,jdbcType=BIGINT},
      service_id = #{record.serviceId,jdbcType=BIGINT},
      service_price = #{record.servicePrice,jdbcType=DECIMAL},
      service_time = #{record.serviceTime,jdbcType=INTEGER},
      start_date = #{record.startDate,jdbcType=DATE},
      start_time = #{record.startTime,jdbcType=INTEGER},
      end_date = #{record.endDate,jdbcType=DATE},
      end_time = #{record.endTime,jdbcType=INTEGER},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      staff_id = #{record.staffId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.EvaluationServiceDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluation_service_detail
    <set>
      <if test="appointmentId != null">
        appointment_id = #{appointmentId,jdbcType=BIGINT},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=BIGINT},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="servicePrice != null">
        service_price = #{servicePrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceTime != null">
        service_time = #{serviceTime,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.EvaluationServiceDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluation_service_detail
    set appointment_id = #{appointmentId,jdbcType=BIGINT},
      pet_id = #{petId,jdbcType=BIGINT},
      service_id = #{serviceId,jdbcType=BIGINT},
      service_price = #{servicePrice,jdbcType=DECIMAL},
      service_time = #{serviceTime,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=DATE},
      start_time = #{startTime,jdbcType=INTEGER},
      end_date = #{endDate,jdbcType=DATE},
      end_time = #{endTime,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      staff_id = #{staffId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryByAppointmentIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from evaluation_service_detail
    where appointment_id in
    <foreach close=")" collection="appointmentList" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectCustomerIdWithInAllAppointmentDate" resultType="int">
    SELECT DISTINCT a.customer_id
    FROM moe_grooming_appointment a
    inner join evaluation_service_detail e on a.id = e.appointment_id
    WHERE a.business_id = #{businessId}
    AND a.is_waiting_list = 0
    AND a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

    AND a.is_deprecate = 0
    AND a.is_block = 2
    and e.staff_id is not null and e.staff_id = #{staffId}
  </select>
</mapper>