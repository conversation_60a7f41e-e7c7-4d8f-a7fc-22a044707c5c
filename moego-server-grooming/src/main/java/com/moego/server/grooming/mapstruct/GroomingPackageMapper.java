package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.GroomingPackageDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingPackage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GroomingPackageMapper {
    GroomingPackageMapper INSTANCE = Mappers.getMapper(GroomingPackageMapper.class);

    GroomingPackageDTO beanToDTO(MoeGroomingPackage moeGroomingPackage);
}
