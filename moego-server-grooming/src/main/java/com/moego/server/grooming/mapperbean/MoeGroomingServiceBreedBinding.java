package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_service_breed_binding
 */
public class MoeGroomingServiceBreedBinding {
    /**
     * Database Column Remarks:
     *   primary key
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   service id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   pet type binding, 1-dog 2-cat 3-other...
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.pet_type_id
     *
     * @mbg.generated
     */
    private Integer petTypeId;

    /**
     * Database Column Remarks:
     *   status, 0-normal 1-invalid
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   is all breed
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.is_all
     *
     * @mbg.generated
     */
    private Boolean isAll;

    /**
     * Database Column Remarks:
     *   breed name list in json format
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.breed_name_list
     *
     * @mbg.generated
     */
    private String breedNameList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   Old breed name list
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_breed_binding.breed_names
     *
     * @mbg.generated
     */
    private String breedNames;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.id
     *
     * @return the value of moe_grooming_service_breed_binding.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.id
     *
     * @param id the value for moe_grooming_service_breed_binding.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.business_id
     *
     * @return the value of moe_grooming_service_breed_binding.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.business_id
     *
     * @param businessId the value for moe_grooming_service_breed_binding.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.service_id
     *
     * @return the value of moe_grooming_service_breed_binding.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.service_id
     *
     * @param serviceId the value for moe_grooming_service_breed_binding.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.pet_type_id
     *
     * @return the value of moe_grooming_service_breed_binding.pet_type_id
     *
     * @mbg.generated
     */
    public Integer getPetTypeId() {
        return petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.pet_type_id
     *
     * @param petTypeId the value for moe_grooming_service_breed_binding.pet_type_id
     *
     * @mbg.generated
     */
    public void setPetTypeId(Integer petTypeId) {
        this.petTypeId = petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.status
     *
     * @return the value of moe_grooming_service_breed_binding.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.status
     *
     * @param status the value for moe_grooming_service_breed_binding.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.create_time
     *
     * @return the value of moe_grooming_service_breed_binding.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.create_time
     *
     * @param createTime the value for moe_grooming_service_breed_binding.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.update_time
     *
     * @return the value of moe_grooming_service_breed_binding.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.update_time
     *
     * @param updateTime the value for moe_grooming_service_breed_binding.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.is_all
     *
     * @return the value of moe_grooming_service_breed_binding.is_all
     *
     * @mbg.generated
     */
    public Boolean getIsAll() {
        return isAll;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.is_all
     *
     * @param isAll the value for moe_grooming_service_breed_binding.is_all
     *
     * @mbg.generated
     */
    public void setIsAll(Boolean isAll) {
        this.isAll = isAll;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.breed_name_list
     *
     * @return the value of moe_grooming_service_breed_binding.breed_name_list
     *
     * @mbg.generated
     */
    public String getBreedNameList() {
        return breedNameList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.breed_name_list
     *
     * @param breedNameList the value for moe_grooming_service_breed_binding.breed_name_list
     *
     * @mbg.generated
     */
    public void setBreedNameList(String breedNameList) {
        this.breedNameList = breedNameList == null ? null : breedNameList.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.company_id
     *
     * @return the value of moe_grooming_service_breed_binding.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.company_id
     *
     * @param companyId the value for moe_grooming_service_breed_binding.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_breed_binding.breed_names
     *
     * @return the value of moe_grooming_service_breed_binding.breed_names
     *
     * @mbg.generated
     */
    public String getBreedNames() {
        return breedNames;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_breed_binding.breed_names
     *
     * @param breedNames the value for moe_grooming_service_breed_binding.breed_names
     *
     * @mbg.generated
     */
    public void setBreedNames(String breedNames) {
        this.breedNames = breedNames == null ? null : breedNames.trim();
    }
}
