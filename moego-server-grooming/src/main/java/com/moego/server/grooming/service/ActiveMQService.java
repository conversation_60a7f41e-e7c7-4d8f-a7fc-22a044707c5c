package com.moego.server.grooming.service;

import com.moego.common.enums.AppointmentEventEnum;
import com.moego.lib.actimvemq.autoconfigure.MoeMessageSender;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.ob.OBRequestSyncDTO;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.params.AppointmentParams;
import com.moego.server.grooming.params.PreAuthParams;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.server.payment.params.AppointmentEventParams;
import jakarta.annotation.Nullable;
import jakarta.jms.Queue;
import jakarta.jms.Topic;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.command.ActiveMQTextMessage;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class ActiveMQService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private MoeMessageSender moeMessageSender;

    @Autowired
    @Qualifier("appointTopic")
    private Topic appointTopic;

    @Autowired
    @Qualifier("bookingRequestQueue")
    private Queue bookingRequestQueue;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private NewOrderHelper newOrderHelper;

    @Deprecated // use publishAppointmentEventV2 instead
    public void publishAppointmentEvent(
            AppointmentParams request, MoeGroomingAppointment appointment, Long invoiceId, AppointmentEventEnum event) {
        AppointmentEventParams params = new AppointmentEventParams();
        BeanUtils.copyProperties(appointment, params);
        params.setEvent(event.name());
        params.setTicketId(appointment.getId());
        params.setPreAuthPaymentMethod(request.getPreAuthPaymentMethod());
        params.setPreAuthCardNumber(request.getPreAuthCardNumber());
        params.setPreAuthStatus(request.getPreAuthEnable());
        params.setAppointmentSource(appointment.getSource());
        if (StringUtils.hasText(request.getAppointmentDateString())) {
            params.setApptDateStr(request.getAppointmentDateString());
        } else {
            params.setApptDateStr(appointment.getAppointmentDate());
        }
        if (request.getAppointmentStartTime() != null) {
            params.setApptTime(request.getAppointmentStartTime());
        } else {
            params.setApptTime(appointment.getAppointmentStartTime());
        }
        if (invoiceId != null) {
            MoeGroomingInvoice invoice = getInvoice(appointment, invoiceId);
            params.setInvoiceId(invoice.getId());
            params.setPreAuthAmount(invoice.getSubTotalAmount());
        }
        doSend(params);
    }

    public void publishAppointmentEventV2(
            PreAuthParams preAuthParams,
            MoeGroomingAppointment appointment,
            @Nullable Long invoiceId,
            AppointmentEventEnum event) {
        if (newOrderHelper.isNewOrder(appointment.getId()) && invoiceId == null) {
            return;
        }
        AppointmentEventParams params = new AppointmentEventParams();
        BeanUtils.copyProperties(appointment, params);
        params.setEvent(event.name());
        params.setTicketId(appointment.getId());
        if (Objects.nonNull(preAuthParams)) {
            params.setPreAuthPaymentMethod(preAuthParams.preAuthPaymentMethod());
            params.setPreAuthCardNumber(preAuthParams.preAuthCardNumber());
            params.setPreAuthStatus(preAuthParams.preAuthEnable());
        }
        params.setAppointmentSource(appointment.getSource());
        params.setApptDateStr(appointment.getAppointmentDate());
        params.setApptTime(appointment.getAppointmentStartTime());
        MoeGroomingInvoice invoice = getInvoice(appointment, invoiceId);
        params.setInvoiceId(invoice.getId());
        params.setPreAuthAmount(invoice.getSubTotalAmount());
        doSend(params);
    }

    private MoeGroomingInvoice getInvoice(MoeGroomingAppointment appointment, Long invoiceId) {
        if (invoiceId != null) {
            return orderService.getOrderById(appointment.getBusinessId(), invoiceId.intValue());
        } else {
            return orderService.getOrderByGroomingId(appointment.getBusinessId(), appointment.getId());
        }
    }

    private void doSend(AppointmentEventParams params) {
        try {
            ActiveMQTextMessage t = new ActiveMQTextMessage();
            t.setText(JsonUtil.toJson(params));
            moeMessageSender.send(appointTopic, t);
        } catch (Exception e) {
            log.error("doSend message error", e);
        }
    }

    public void publishAppointmentCancelEvent(
            MoeGroomingAppointment appointment, List<Integer> ids, boolean releasePreAuth) {
        AppointmentEventParams params = new AppointmentEventParams();
        params.setBusinessId(appointment.getBusinessId());
        params.setCustomerId(params.getCustomerId());
        params.setTicketId(appointment.getId());
        params.setReleasePreAuth(releasePreAuth);
        if (!CollectionUtils.isEmpty(ids)) {
            params.setTicketIds(ids);
            params.setEvent(AppointmentEventEnum.CANCEL_BATCH.name());
        } else {
            params.setEvent(AppointmentEventEnum.CANCEL_SINGLE.name());
        }
        doSend(params);
    }

    /**
     * 发布 booking request 事件，同步 appointment 到 booking request
     *
     * @param params appointment id and event
     */
    public void publishBookingRequestEvent(BookingRequestEventParams params) {
        try {
            ActiveMQTextMessage t = new ActiveMQTextMessage();
            t.setText(JsonUtil.toJson(params));
            moeMessageSender.send(bookingRequestQueue, t);
        } catch (Exception e) {
            log.error("publishBookingRequestEvent error", e);
            stringRedisTemplate.opsForSet().add(OBRequestSyncDTO.FAILED_KEY, JsonUtil.toJson(params));
        }
    }
}
