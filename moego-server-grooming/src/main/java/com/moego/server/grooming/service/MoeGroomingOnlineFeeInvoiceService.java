package com.moego.server.grooming.service;

import com.moego.common.enums.BooleanEnum;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.grooming.mapper.MoeGroomingOnlineFeeInvoiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MoeGroomingOnlineFeeInvoiceService {

    @Autowired
    private MoeGroomingOnlineFeeInvoiceMapper moeGroomingOnlineFeeInvoiceMapper;

    @Autowired
    private IBusinessBusinessClient businessBusinessClient;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    public void addOrUpdateOnlineFeeRecord(
            Integer businessId, Integer invoiceId, Byte type, Boolean requiredProcessingFee) {
        MoeGroomingOnlineFeeInvoice onlineFeeInvoice = new MoeGroomingOnlineFeeInvoice();
        onlineFeeInvoice.setBusinessId(businessId);
        onlineFeeInvoice.setInvoiceId(invoiceId);
        onlineFeeInvoice.setType(type);
        onlineFeeInvoice.setRequiredFee(
                Boolean.TRUE.equals(requiredProcessingFee) ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE);
        insertOrUpdateOnlineFeeRecord(onlineFeeInvoice);
    }

    private void insertOrUpdateOnlineFeeRecord(MoeGroomingOnlineFeeInvoice onlineFeeInvoice) {
        MoeGroomingOnlineFeeInvoice record = selectOnlineFeeRecord(
                onlineFeeInvoice.getBusinessId(), onlineFeeInvoice.getInvoiceId(), onlineFeeInvoice.getType());
        if (record != null) {
            record.setRequiredFee(onlineFeeInvoice.getRequiredFee());
            moeGroomingOnlineFeeInvoiceMapper.updateByPrimaryKeySelective(record);
        } else {
            record = new MoeGroomingOnlineFeeInvoice();
            BeanUtils.copyProperties(onlineFeeInvoice, record);
            record.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(onlineFeeInvoice.getBusinessId()));
            moeGroomingOnlineFeeInvoiceMapper.insertSelective(record);
        }
    }

    public MoeGroomingOnlineFeeInvoice selectOnlineFeeRecord(Integer businessId, Integer invoiceId, Byte type) {
        return moeGroomingOnlineFeeInvoiceMapper.selectByInvoiceIdAndType(businessId, invoiceId, type);
    }
}
