package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table ob_config_team
 */
public class ObConfigTeam {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_team.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_team.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   staff id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_team.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   instagram link
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_team.instagram_link
     *
     * @mbg.generated
     */
    private String instagramLink;

    /**
     * Database Column Remarks:
     *   introduction
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_team.introduction
     *
     * @mbg.generated
     */
    private String introduction;

    /**
     * Database Column Remarks:
     *   tags, json string array, e.g. ["cool", "cute"]
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_team.tags
     *
     * @mbg.generated
     */
    private String tags;

    /**
     * Database Column Remarks:
     *   is enabled
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_team.is_enabled
     *
     * @mbg.generated
     */
    private Boolean isEnabled;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_team.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ob_config_team.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_team.id
     *
     * @return the value of ob_config_team.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_team.id
     *
     * @param id the value for ob_config_team.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_team.business_id
     *
     * @return the value of ob_config_team.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_team.business_id
     *
     * @param businessId the value for ob_config_team.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_team.staff_id
     *
     * @return the value of ob_config_team.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_team.staff_id
     *
     * @param staffId the value for ob_config_team.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_team.instagram_link
     *
     * @return the value of ob_config_team.instagram_link
     *
     * @mbg.generated
     */
    public String getInstagramLink() {
        return instagramLink;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_team.instagram_link
     *
     * @param instagramLink the value for ob_config_team.instagram_link
     *
     * @mbg.generated
     */
    public void setInstagramLink(String instagramLink) {
        this.instagramLink = instagramLink == null ? null : instagramLink.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_team.introduction
     *
     * @return the value of ob_config_team.introduction
     *
     * @mbg.generated
     */
    public String getIntroduction() {
        return introduction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_team.introduction
     *
     * @param introduction the value for ob_config_team.introduction
     *
     * @mbg.generated
     */
    public void setIntroduction(String introduction) {
        this.introduction = introduction == null ? null : introduction.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_team.tags
     *
     * @return the value of ob_config_team.tags
     *
     * @mbg.generated
     */
    public String getTags() {
        return tags;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_team.tags
     *
     * @param tags the value for ob_config_team.tags
     *
     * @mbg.generated
     */
    public void setTags(String tags) {
        this.tags = tags == null ? null : tags.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_team.is_enabled
     *
     * @return the value of ob_config_team.is_enabled
     *
     * @mbg.generated
     */
    public Boolean getIsEnabled() {
        return isEnabled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_team.is_enabled
     *
     * @param isEnabled the value for ob_config_team.is_enabled
     *
     * @mbg.generated
     */
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_team.created_at
     *
     * @return the value of ob_config_team.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_team.created_at
     *
     * @param createdAt the value for ob_config_team.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ob_config_team.updated_at
     *
     * @return the value of ob_config_team.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ob_config_team.updated_at
     *
     * @param updatedAt the value for ob_config_team.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
