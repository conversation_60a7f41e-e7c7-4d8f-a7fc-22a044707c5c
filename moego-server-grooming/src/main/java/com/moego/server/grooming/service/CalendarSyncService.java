package com.moego.server.grooming.service;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.auth.oauth2.TokenResponse;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.client.util.DateTime;
import com.google.api.services.calendar.Calendar;
import com.google.api.services.calendar.model.CalendarListEntry;
import com.google.api.services.calendar.model.Channel;
import com.google.api.services.calendar.model.Event;
import com.google.api.services.calendar.model.Events;
import com.moego.common.constant.CommonConstant;
import com.moego.common.distributed.LockManager;
import com.moego.common.enums.GoogleCalendarConst;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.QuickBooksConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.RedisUtil;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerGroup;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetrics;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingTicketDetailDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.CalendarContentEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGcApptMapper;
import com.moego.server.grooming.mapper.MoeGcAuthStaffMapper;
import com.moego.server.grooming.mapper.MoeGcCalendarMapper;
import com.moego.server.grooming.mapper.MoeGcEventMapper;
import com.moego.server.grooming.mapper.MoeGcSettingMapper;
import com.moego.server.grooming.mapper.MoeGcTaskMapper;
import com.moego.server.grooming.mapper.MoeGcWatchEventMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGcAppt;
import com.moego.server.grooming.mapperbean.MoeGcAuthStaff;
import com.moego.server.grooming.mapperbean.MoeGcCalendar;
import com.moego.server.grooming.mapperbean.MoeGcEvent;
import com.moego.server.grooming.mapperbean.MoeGcSetting;
import com.moego.server.grooming.mapperbean.MoeGcTask;
import com.moego.server.grooming.mapperbean.MoeGcWatchEvent;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.params.AppointmentBlockParams;
import com.moego.server.grooming.params.IdParams;
import com.moego.server.grooming.service.dto.CalendarEventParamDto;
import com.moego.server.grooming.service.dto.GoogleCalendarAuthInfoDto;
import com.moego.server.grooming.service.dto.GoogleCalendarSettingDto;
import com.moego.server.grooming.service.dto.QbQueryGroomingResultDto;
import com.moego.server.grooming.service.google.BlockCreateHelper;
import com.moego.server.grooming.service.google.CalendarCreateHelper;
import com.moego.server.grooming.service.intuit.RedisGroomingListElementObj;
import com.moego.server.grooming.utils.SyncCalendarUtil;
import com.moego.server.grooming.web.vo.CreateCalendarSyncSettingVo;
import com.moego.server.grooming.web.vo.UpdateCalendarSyncSettingVo;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class CalendarSyncService {

    @Autowired
    private MoeGcApptMapper gcApptMapper;

    @Autowired
    private MoeGcAuthStaffMapper gcAuthStaffMapper;

    @Autowired
    private MoeGcWatchEventMapper gcWatchEventMapper;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Autowired
    private MoeGcCalendarMapper gcCalendarMapper;

    @Autowired
    private AppointmentMapperProxy groomingAppointmentMapper;

    @Autowired
    private PetDetailMapperProxy groomingPetDetailMapper;

    @Autowired
    private MoeGroomingNoteService moeGroomingNoteService;

    @Autowired
    private MoeGroomingAppointmentService groomingAppointmentService;

    @Autowired
    private MoeGcEventMapper gcEventMapper;

    @Autowired
    private MoeGcTaskMapper gcTaskMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private MoeGcSettingMapper gcSettingMapper;

    @Value("${google.calendar.auth.redirect.uri}")
    private String calendarAuthRedirectUri;

    @Value("${google.event.web.hook}")
    private String eventWebHook;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private LockManager moegoLockManager;

    @Autowired
    private PetDetailMapperProxy moeGroomingPetDetailMapper;

    public GoogleCalendarSettingDto createGooGleCalendarSetting(
            Integer businessId, Long companyId, Integer tokenStaffId, CreateCalendarSyncSettingVo settingVo) {
        // 查询authId是否被用过且24h内
        MoeGcAuthStaff authStaff = gcAuthStaffMapper.selectByPrimaryKey(settingVo.getGoogleAuthId());
        List<MoeGcSetting> settingList = gcSettingMapper.selectByGoogleAuthId(settingVo.getGoogleAuthId());
        if (authStaff.getCreateTime() < (DateUtil.get10Timestamp() - GoogleCalendarConst.AUTH_USE_TIME_LIMIT)
                || !CollectionUtils.isEmpty(settingList)
                || !authStaff.getStaffId().equals(tokenStaffId)) {
            throw new CommonException(ResponseCodeEnum.GOOGLE_OAUTH_IS_USED);
        }
        // 保存设置信息
        MoeGcSetting setting = new MoeGcSetting();
        setting.setBusinessId(businessId);
        setting.setCompanyId(companyId);
        setting.setStaffId(tokenStaffId);
        setting.setGoogleAuthId(settingVo.getGoogleAuthId());
        setting.setSyncType(settingVo.getSyncType());
        setting.setStatus(GoogleCalendarConst.SETTING_STATUS_NORMAL);
        setting.setCreateTime(DateUtil.get10Timestamp());
        setting.setUpdateTime(DateUtil.get10Timestamp());
        setting.setCalendarName(CalendarContentEnum.CALENDAR_NAME.getContent());
        setting.setEventTitle(CalendarContentEnum.EVENT_TITLE.getContent());
        setting.setEventDescription(CalendarContentEnum.EVENT_DESCRIPTION.getContent());
        gcSettingMapper.insertSelective(setting);
        // create calendar

        // 查询staff信息，获取staff name
        Map<Integer, MoeStaffDto> staffMap = getStaffDtoMap(businessId, settingVo.getSyncedStaffIdList());
        List<MoeGcCalendar> newCalendarList = new ArrayList<>();

        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(businessIdParams);

        for (Integer syncedStaffId : settingVo.getSyncedStaffIdList()) {
            MoeStaffDto staffDto = staffMap.get(syncedStaffId);
            MoeGcCalendar newCalendar = new MoeGcCalendar();
            try {
                // 创建 calendar name
                com.google.api.services.calendar.model.Calendar newGoogleCalendar = createStaffCalendar(
                        settingVo.getGoogleAuthId(),
                        CalendarContentEnum.CALENDAR_NAME.getContent(),
                        CommonUtil.nameFormat(staffDto.getFirstName(), staffDto.getLastName()),
                        businessDto);
                newCalendar.setCalendarTitle(newGoogleCalendar.getSummary());
                newCalendar.setGoogleCalendarId(newGoogleCalendar.getId());
            } catch (Exception e) {
                log.error("create new google calendar error", e);
                continue;
            }
            newCalendar.setBusinessId(businessId);
            newCalendar.setCompanyId(companyId);
            newCalendar.setSettingId(setting.getId());
            newCalendar.setStaffId(tokenStaffId);
            newCalendar.setSyncedStaffId(syncedStaffId);
            newCalendar.setCalendarStatus(GoogleCalendarConst.CALENDAR_STATUS_NORMAL);
            newCalendar.setCreateTime(DateUtil.get10Timestamp());
            newCalendar.setUpdateTime(DateUtil.get10Timestamp());
            gcCalendarMapper.insertSelective(newCalendar);
            newCalendarList.add(newCalendar);
            // 异步开始在redis内同步
        }
        ThreadPool.execute(() -> {
            for (MoeGcCalendar newCalendar : newCalendarList) {
                addRedisSyncDateRange(businessId, newCalendar);
                watchEvent(setting.getSyncType(), newCalendar);
            }
        });

        GoogleCalendarSettingDto result = new GoogleCalendarSettingDto();
        result.setBusinessId(businessId);
        result.setSettingId(setting.getId());
        result.setSyncedStaffIdList(settingVo.getSyncedStaffIdList());
        result.setCalendarName(CalendarContentEnum.CALENDAR_NAME.getContent());
        result.setEventTitle(CalendarContentEnum.EVENT_TITLE.getContent());
        result.setEventDescription(CalendarContentEnum.EVENT_DESCRIPTION.getContent());
        return result;
    }

    public Map<Integer, MoeStaffDto> getStaffDtoMap(Integer businessId, List<Integer> staffIdList) {
        // 查询staff信息，获取staff name
        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(businessId);
        staffIdListParams.setStaffIdList(staffIdList);
        List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(staffIdListParams);
        // 将staff id和 staff info 映射
        Map<Integer, MoeStaffDto> staffMap = new HashMap<>();
        staffList.forEach(staffDto -> staffMap.put(staffDto.getId(), staffDto));
        return staffMap;
    }

    public Boolean updateGooGleCalendarSetting(
            Integer businessId, Integer staffId, UpdateCalendarSyncSettingVo settingVo) {
        GoogleCalendarSettingDto settingDto = getCalendarSettingDto(businessId, staffId, true);

        // 断开连接
        if (settingVo.getDisconnect() != null
                && settingVo.getDisconnect()
                && GoogleCalendarConst.SETTING_STATUS_NORMAL.equals(settingDto.getStatus())) {
            MoeGcSetting updateSettingBean = new MoeGcSetting();
            updateSettingBean.setId(settingDto.getSettingId());
            updateSettingBean.setStatus(GoogleCalendarConst.SETTING_STATUS_EXPIRE);
            updateSettingBean.setUpdateTime(DateUtil.get10Timestamp());
            gcSettingMapper.updateByPrimaryKeySelective(updateSettingBean);

            ThreadPool.execute(() -> {
                // delete calendar
                List<MoeGcCalendar> calendarList = getSyncedCalendar(
                        settingDto.getGoogleAuthId(), settingDto.getSettingId(), settingDto.getStaffId(), false);
                for (MoeGcCalendar gcCalendar : calendarList) {
                    deleteGoogleCalendarByGcCalendarId(settingDto.getGoogleAuthId(), gcCalendar.getId());
                    deleteRedisGcListBygcCalendarId(gcCalendar.getId());
                    setEventDeleteByGcCalendarId(businessId, gcCalendar.getId());
                }
                // todo disconnect google?
                if (settingDto.getGoogleAuthId() != null) {
                    MoeGcAuthStaff updateStaffBean = new MoeGcAuthStaff();
                    updateStaffBean.setId(settingDto.getGoogleAuthId());
                    updateStaffBean.setStatus(GoogleCalendarConst.AUTH_STATUS_CANCEL);
                    updateStaffBean.setUpdateTime(DateUtil.get10Timestamp());
                    gcAuthStaffMapper.updateByPrimaryKeySelective(updateStaffBean);
                }
            });
            return true;
        }
        if (settingVo.getSyncedStaffIdList() == null) {
            return true;
        }

        // 修改 calendar name
        if (Objects.nonNull(settingVo.getCalendarName())
                && !Objects.equals(settingVo.getCalendarName(), settingDto.getCalendarName())) {
            settingDto.setCalendarName(settingVo.getCalendarName());
            updateCalendarName(businessId, settingDto);
            return true;
        }

        // 修改 event title\description
        if ((Objects.nonNull(settingVo.getEventTitle())
                        && !Objects.equals(settingVo.getEventTitle(), settingDto.getEventTitle()))
                || (Objects.nonNull(settingVo.getEventDescription())
                        && !Objects.equals(settingVo.getEventDescription(), settingDto.getEventDescription()))) {
            if (Objects.nonNull(settingVo.getEventTitle())) {
                settingDto.setEventTitle(settingVo.getEventTitle());
            }
            if (Objects.nonNull(settingVo.getEventDescription())) {
                settingDto.setEventDescription(settingVo.getEventDescription());
            }

            updateCalendarEventTitleAndDescription(businessId, settingDto);
            return true;
        }

        List<Integer> oldStaffIds = settingDto.getSyncedStaffIdList();
        List<Integer> newStaffIds = settingVo.getSyncedStaffIdList();

        Map<Integer, MoeStaffDto> staffDtoMap = getStaffDtoMap(businessId, newStaffIds);

        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(businessIdParams);

        // 为新增加的员工添加日历
        for (Integer newStaffId : newStaffIds) {
            if (!oldStaffIds.contains(newStaffId)) {
                String staffName = Strings.EMPTY;
                if (staffDtoMap.get(newStaffId) != null) {
                    staffName = CommonUtil.nameFormat(
                            staffDtoMap.get(newStaffId).getFirstName(),
                            staffDtoMap.get(newStaffId).getLastName());
                }
                createStaffCalendar(settingDto, newStaffId, staffName, businessDto);
            }
        }

        // 为删除的员工删除日历
        ThreadPool.execute(() -> {
            for (Integer oldStaffId : oldStaffIds) {
                if (!newStaffIds.contains(oldStaffId)) {
                    List<MoeGcCalendar> syncStaffCalendar =
                            gcCalendarMapper.selectBySyncedStaffId(businessId, oldStaffId);
                    for (MoeGcCalendar gcCalendar : syncStaffCalendar) {
                        deleteGoogleCalendarByGcCalendarId(settingDto.getGoogleAuthId(), gcCalendar.getId());
                        deleteRedisGcListBygcCalendarId(gcCalendar.getId());
                        setEventDeleteByGcCalendarId(businessId, gcCalendar.getId());
                    }
                }
            }
        });
        return true;
    }

    private void updateCalendarName(Integer businessId, GoogleCalendarSettingDto settingDto) {
        MoeGcSetting updateSettingBean = new MoeGcSetting();
        updateSettingBean.setId(settingDto.getSettingId());
        updateSettingBean.setUpdateTime(DateUtil.get10Timestamp());
        updateSettingBean.setCalendarName(settingDto.getCalendarName());
        gcSettingMapper.updateByPrimaryKeySelective(updateSettingBean);

        // create calendar
        ThreadPool.execute(() -> {
            // 查询staff信息，获取staff name
            Map<Integer, MoeStaffDto> staffMap = getStaffDtoMap(businessId, settingDto.getSyncedStaffIdList());

            InfoIdParams businessIdParams = new InfoIdParams();
            businessIdParams.setInfoId(businessId);
            MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(businessIdParams);

            // 获取 staff 对应的 calendar id
            Map<Integer, String> calendarMap =
                    gcCalendarMapper.selectBySyncedStaffIds(businessId, settingDto.getSyncedStaffIdList()).stream()
                            .filter(calendar ->
                                    GoogleCalendarConst.CALENDAR_STATUS_NORMAL.equals(calendar.getCalendarStatus()))
                            .collect(Collectors.toMap(
                                    MoeGcCalendar::getSyncedStaffId,
                                    MoeGcCalendar::getGoogleCalendarId,
                                    (oldValue, newValue) -> newValue));

            for (Integer syncedStaffId : settingDto.getSyncedStaffIdList()) {
                MoeStaffDto staffDto = staffMap.get(syncedStaffId);
                try {
                    // 更新 calendar name
                    updateStaffCalendarName(
                            settingDto.getGoogleAuthId(),
                            settingDto.getCalendarName(),
                            CommonUtil.nameFormat(staffDto.getFirstName(), staffDto.getLastName()),
                            calendarMap.get(syncedStaffId),
                            businessDto);
                } catch (IOException e) {
                    log.error("update google calendar name error", e);
                    throw new CommonException(ResponseCodeEnum.UPDATE_GOOGLE_CALENDAR_ERROR);
                }
            }
        });
    }

    private void updateCalendarEventTitleAndDescription(Integer businessId, GoogleCalendarSettingDto settingDto) {
        MoeGcSetting updateSettingBean = new MoeGcSetting();
        updateSettingBean.setId(settingDto.getSettingId());
        updateSettingBean.setUpdateTime(DateUtil.get10Timestamp());
        updateSettingBean.setEventTitle(settingDto.getEventTitle());
        updateSettingBean.setEventDescription(settingDto.getEventDescription());
        gcSettingMapper.updateByPrimaryKeySelective(updateSettingBean);

        // create calendar
        ThreadPool.execute(() -> {
            // delete calendar
            List<MoeGcCalendar> calendarList = getSyncedCalendar(
                    settingDto.getGoogleAuthId(), settingDto.getSettingId(), settingDto.getStaffId(), false);
            for (MoeGcCalendar gcCalendar : calendarList) {
                deleteGoogleCalendarByGcCalendarId(settingDto.getGoogleAuthId(), gcCalendar.getId());
                deleteRedisGcListBygcCalendarId(gcCalendar.getId());
                setEventDeleteByGcCalendarId(businessId, gcCalendar.getId());
            }

            List<Integer> syncedStaffIdList = calendarList.stream()
                    .map(MoeGcCalendar::getSyncedStaffId)
                    .distinct()
                    .collect(Collectors.toList());

            // 查询staff信息，获取staff name
            Map<Integer, MoeStaffDto> staffMap = getStaffDtoMap(businessId, syncedStaffIdList);

            InfoIdParams businessIdParams = new InfoIdParams();
            businessIdParams.setInfoId(businessId);
            MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(businessIdParams);

            syncedStaffIdList.forEach(newStaffId -> {
                String staffName = Strings.EMPTY;
                MoeStaffDto moeStaffDto = staffMap.get(newStaffId);
                if (Objects.nonNull(moeStaffDto)) {
                    staffName = CommonUtil.nameFormat(moeStaffDto.getFirstName(), moeStaffDto.getLastName());
                }
                createStaffCalendar(settingDto, newStaffId, staffName, businessDto);
            });
        });
    }

    public void deleteStaffCalendar(Integer businessId, Integer staffId) {
        // 为删除的员工删除日历
        ThreadPool.execute(() -> {
            List<MoeGcCalendar> syncStaffCalendar = gcCalendarMapper.selectBySyncedStaffId(businessId, staffId);

            Map<Integer, Integer> calenderIdToAuthIdMap = new HashMap<>();
            syncStaffCalendar.forEach(gcCalendar -> {
                MoeGcSetting moeGcSetting = gcSettingMapper.selectByPrimaryKey(gcCalendar.getSettingId());
                MoeGcAuthStaff moeGcAuthStaff = gcAuthStaffMapper.selectByPrimaryKey(moeGcSetting.getGoogleAuthId());
                calenderIdToAuthIdMap.put(gcCalendar.getId(), moeGcAuthStaff.getId());
            });

            syncStaffCalendar.forEach(gcCalendar -> {
                Integer gcCalendarId = gcCalendar.getId();
                deleteGoogleCalendarByGcCalendarId(calenderIdToAuthIdMap.get(gcCalendarId), gcCalendarId);
                deleteRedisGcListBygcCalendarId(gcCalendarId);
                setEventDeleteByGcCalendarId(businessId, gcCalendar.getId());
            });
        });
    }

    /**
     * 将google calendar redis 队列删除，用户重新关联的用户
     *
     * @param gcCalendarId
     */
    public void deleteRedisGcListBygcCalendarId(Integer gcCalendarId) {
        String gcRedisListKey = getGcRedisListKey(gcCalendarId);
        redisUtil.delete(gcRedisListKey);
    }

    public void createStaffCalendar(
            GoogleCalendarSettingDto settingDto, Integer syncedStaffId, String staffName, MoeBusinessDto businessDto) {
        MoeGcCalendar newCalendar = new MoeGcCalendar();
        try {
            com.google.api.services.calendar.model.Calendar newGoogleCalendar = createStaffCalendar(
                    settingDto.getGoogleAuthId(), settingDto.getCalendarName(), staffName, businessDto);
            newCalendar.setCalendarTitle(newGoogleCalendar.getSummary());
            newCalendar.setGoogleCalendarId(newGoogleCalendar.getId());
        } catch (Exception e) {
            log.error("create new google calendar error", e);
            return;
        }
        newCalendar.setBusinessId(settingDto.getBusinessId());
        newCalendar.setCompanyId(settingDto.getCompanyId());
        newCalendar.setSettingId(settingDto.getSettingId());
        newCalendar.setStaffId(settingDto.getStaffId());
        newCalendar.setSyncedStaffId(syncedStaffId);
        newCalendar.setCalendarStatus(GoogleCalendarConst.CALENDAR_STATUS_NORMAL);
        newCalendar.setCreateTime(DateUtil.get10Timestamp());
        newCalendar.setUpdateTime(DateUtil.get10Timestamp());
        gcCalendarMapper.insertSelective(newCalendar);

        ThreadPool.execute(() -> {
            addRedisSyncDateRange(settingDto.getBusinessId(), newCalendar);
            watchEvent(settingDto.getSyncType(), newCalendar);
        });
    }

    public void deleteGoogleCalendarByGcCalendarId(Integer authId, Integer gcCalendarId) {
        MoeGcCalendar gcCalendar = gcCalendarMapper.selectByPrimaryKey(gcCalendarId);
        deleteGoogleCalendar(authId, gcCalendar.getGoogleCalendarId());
        googleCalendarApiLimitAdd(gcCalendar.getId());

        MoeGcCalendar updateBean = new MoeGcCalendar();
        updateBean.setId(gcCalendar.getId());
        updateBean.setCalendarStatus(GoogleCalendarConst.CALENDAR_STATUS_DELETE);
        updateBean.setUpdateTime(DateUtil.get10Timestamp());
        gcCalendarMapper.updateByPrimaryKeySelective(updateBean);
    }

    public void deleteGoogleCalendar(Integer authId, String googleCalendarId) {
        try {
            if (StringUtils.isEmpty(googleCalendarId)) {
                return;
            }
            MoeGcAuthStaff gcAuthStaff = gcAuthStaffMapper.selectByPrimaryKey(authId);
            Calendar calendarService = SyncCalendarUtil.initCalendarByCredential(createMyTokenResponse(gcAuthStaff));
            calendarService.calendarList().delete(googleCalendarId).execute();
            calendarService.calendars().delete(googleCalendarId).execute();
        } catch (IOException e) {
            log.error(String.format("delete google calendar error %s %s", authId, googleCalendarId), e);
        }
    }

    public GoogleCalendarSettingDto getCalendarSettingDto(Integer businessId, Integer staffId) {
        return getCalendarSettingDto(businessId, staffId, false);
    }

    public GoogleCalendarSettingDto getCalendarSettingDto(Integer businessId, Integer staffId, Boolean isForceCheck) {
        GoogleCalendarSettingDto settingDto = new GoogleCalendarSettingDto();
        settingDto.setRedirectUri(SyncCalendarUtil.getRedirectUrlForGoogle(calendarAuthRedirectUri));
        settingDto.setStaffId(staffId);
        settingDto.setBusinessId(businessId);

        MoeGcSetting gcSetting = gcSettingMapper.selectByStaffIdBusinessId(businessId, staffId);

        if (gcSetting == null) {
            settingDto.setStatus(GoogleCalendarConst.SETTING_STATUS_EXPIRE);
            return settingDto;
        }
        settingDto.setCompanyId(gcSetting.getCompanyId());
        settingDto.setSettingId(gcSetting.getId());
        settingDto.setGoogleAuthId(gcSetting.getGoogleAuthId());
        settingDto.setSyncType(gcSetting.getSyncType());
        settingDto.setCreateTime(gcSetting.getCreateTime());
        settingDto.setUpdateTime(gcSetting.getUpdateTime());
        settingDto.setCalendarName(gcSetting.getCalendarName());
        settingDto.setEventTitle(gcSetting.getEventTitle());
        settingDto.setEventDescription(gcSetting.getEventDescription());
        MoeGcAuthStaff gcAuthStaff = gcAuthStaffMapper.selectByPrimaryKey(gcSetting.getGoogleAuthId());
        if (gcAuthStaff == null || !GoogleCalendarConst.AUTH_STATUS_NORMAL.equals(gcAuthStaff.getStatus())) {
            settingDto.setStatus(GoogleCalendarConst.SETTING_STATUS_EXPIRE);
        } else {
            settingDto.setGoogleAuthEmail(gcAuthStaff.getEmail());
            if (GoogleCalendarConst.SETTING_STATUS_NORMAL.equals(gcSetting.getStatus())) {
                settingDto.setStatus(GoogleCalendarConst.SETTING_STATUS_NORMAL);
            }
        }
        if (GoogleCalendarConst.SETTING_STATUS_NORMAL.equals(settingDto.getStatus())) {
            List<MoeGcCalendar> calendarList = getSyncedCalendar(
                    gcSetting.getGoogleAuthId(), gcSetting.getId(), gcSetting.getStaffId(), isForceCheck);
            settingDto.setGcCalendarList(calendarList);
            if (!CollectionUtils.isEmpty(calendarList)) {
                List<Integer> syncedStaffIdList = new ArrayList<>();
                calendarList.forEach(calendar -> syncedStaffIdList.add(calendar.getSyncedStaffId()));
                settingDto.setSyncedStaffIdList(syncedStaffIdList);
            }
        } else {
            gcSetting.setGoogleAuthId(null);
        }
        if (CollectionUtils.isEmpty(settingDto.getSyncedStaffIdList())) {
            settingDto.setSyncedStaffIdList(Collections.emptyList());
        }
        return settingDto;
    }

    private List<MoeGcCalendar> getSyncedCalendar(
            Integer gcAuthId, Integer googleCalendarSettingId, Integer staffId, Boolean isForceCheck) {
        List<MoeGcCalendar> gcCalendars = gcCalendarMapper.selectByStaffIdSettingId(staffId, googleCalendarSettingId);
        List<MoeGcCalendar> noDeleteCalendarList = new ArrayList<>();
        var nowTime = DateUtil.get10Timestamp();
        for (MoeGcCalendar gcCalendar : gcCalendars) {
            MoeGcCalendar updateBean = new MoeGcCalendar();
            boolean isDelete = true;
            if (Boolean.TRUE.equals(isForceCheck)
                    || gcCalendar.getLastCheckTime() + GoogleCalendarConst.CALENDAR_CHECK_INTERVAL < nowTime) {
                isDelete = getCalendarIsDelete(gcAuthId, gcCalendar.getGoogleCalendarId());
                googleCalendarApiLimitAdd(gcCalendar.getId());
                updateBean.setId(gcCalendar.getId());
                updateBean.setLastCheckTime(nowTime);
            } else {
                isDelete = false;
            }
            if (isDelete) {
                updateBean.setId(gcCalendar.getId());
                updateBean.setCalendarStatus(GoogleCalendarConst.CALENDAR_STATUS_DELETE);
            } else {
                noDeleteCalendarList.add(gcCalendar);
            }
            if (updateBean.getId() != null) {
                gcCalendarMapper.updateByPrimaryKeySelective(updateBean);
            }
        }
        return noDeleteCalendarList;
    }

    public Credential createMyGcCalendar(MoeGcCalendar calendar) {
        GoogleCalendarSettingDto settingDto =
                getCalendarSettingDto(calendar.getBusinessId(), calendar.getStaffId(), false);
        if (settingDto.getGoogleAuthId() != null) {
            return createMyTokenResponse(gcAuthStaffMapper.selectByPrimaryKey(settingDto.getGoogleAuthId()));
        }
        // 设置关闭或授权异常
        throw new CommonException(ResponseCodeEnum.GOOGLE_OAUTH_CHECK_ERROR, "createMyGcCalendar by gcCalendar");
    }

    public Credential createMyTokenResponse(Integer gcAuthId) {
        MoeGcAuthStaff gcAuthStaff = gcAuthStaffMapper.selectByPrimaryKey(gcAuthId);
        return createMyTokenResponse(gcAuthStaff);
    }

    public Credential createMyTokenResponse(MoeGcAuthStaff gcAuthStaff) {
        long expiresInSeconds = gcAuthStaff.getTokenExpiredTime() - DateUtil.get10Timestamp();
        TokenResponse response = new TokenResponse();
        response.setAccessToken(gcAuthStaff.getAccessToken());
        response.setExpiresInSeconds(expiresInSeconds);
        response.setRefreshToken(gcAuthStaff.getRefreshToken());
        response.setScope(GoogleCalendarConst.DEFAULT_CALENDAR_SCOPE);
        response.setTokenType(GoogleCalendarConst.DEFAULT_TOKEN_TYPE);
        Credential credential = null;
        if (expiresInSeconds <= GoogleCalendarConst.BEFORE_TIME) {
            credential = SyncCalendarUtil.refreshToken(
                    response, gcAuthStaff.getStaffId().toString());
            // 刷新成功，保存数据库
            if (credential != null) {
                MoeGcAuthStaff updateBean = new MoeGcAuthStaff();
                updateBean.setTokenExpiredTime(credential.getExpiresInSeconds() + DateUtil.get10Timestamp());
                updateBean.setRefreshToken(credential.getRefreshToken());
                updateBean.setAccessToken(credential.getAccessToken());
                updateBean.setUpdateTime(DateUtil.get10Timestamp());
                updateBean.setId(gcAuthStaff.getId());
                gcAuthStaffMapper.updateByPrimaryKeySelective(updateBean);
            } else {
                // 刷新失败
                log.error("刷新失败");
            }
        } else {
            credential = SyncCalendarUtil.initCredential(
                    response, gcAuthStaff.getStaffId().toString());
        }
        if (credential == null) {
            // auth 异常，断连用户设置
            MoeGcAuthStaff updateBean = new MoeGcAuthStaff();
            updateBean.setStatus(GoogleCalendarConst.AUTH_STATUS_EXPIRE);
            updateBean.setUpdateTime(DateUtil.get10Timestamp());
            updateBean.setId(gcAuthStaff.getId());
            gcAuthStaffMapper.updateByPrimaryKeySelective(updateBean);

            // auth 异常 抛出
            throw new CommonException(ResponseCodeEnum.GOOGLE_OAUTH_CHECK_ERROR);
        }
        return credential;
    }

    public GoogleCalendarAuthInfoDto saveAuthResult(
            Integer businessId, Long companyId, Integer staffId, String code, String scope) {
        try {
            // 通过code换区授权信息
            TokenResponse response = SyncCalendarUtil.globalFlow
                    .newTokenRequest(code)
                    .setRedirectUri(calendarAuthRedirectUri)
                    .execute();
            MoeGcAuthStaff gcAuthStaff = new MoeGcAuthStaff();

            gcAuthStaff.setBusinessId(businessId);
            gcAuthStaff.setCompanyId(companyId);
            gcAuthStaff.setStaffId(staffId);
            // private String email;
            gcAuthStaff.setAccessToken(response.getAccessToken());
            gcAuthStaff.setRefreshToken(response.getRefreshToken());
            gcAuthStaff.setTokenExpiredTime(DateUtil.get10Timestamp() + response.getExpiresInSeconds());
            gcAuthStaff.setCreateTime(DateUtil.get10Timestamp());
            gcAuthStaff.setUpdateTime(DateUtil.get10Timestamp());
            gcAuthStaff.setStatus(GoogleCalendarConst.AUTH_STATUS_EXPIRE);
            gcAuthStaffMapper.insertSelective(gcAuthStaff);
            GoogleCalendarAuthInfoDto authInfoDto = new GoogleCalendarAuthInfoDto();
            // 获取主calendar 一定会有一个，不会为空
            Calendar calendar = SyncCalendarUtil.initCalendarByResponse(response, staffId.toString());
            List<CalendarListEntry> calendarListEntries =
                    calendar.calendarList().list().execute().getItems();
            for (CalendarListEntry calendarListEntry : calendarListEntries) {
                if (calendarListEntry.getPrimary() != null && calendarListEntry.getPrimary()) {
                    MoeGcAuthStaff updateGcAuthStaff = new MoeGcAuthStaff();
                    updateGcAuthStaff.setEmail(calendarListEntry.getId());
                    updateGcAuthStaff.setMainCalendarId(calendarListEntry.getId());
                    updateGcAuthStaff.setStatus(GoogleCalendarConst.AUTH_STATUS_NORMAL);
                    updateGcAuthStaff.setId(gcAuthStaff.getId());
                    gcAuthStaffMapper.updateByPrimaryKeySelective(updateGcAuthStaff);
                    authInfoDto.setEmail(updateGcAuthStaff.getEmail());
                    authInfoDto.setGoogleAuthId(gcAuthStaff.getId());
                    return authInfoDto;
                }
            }
        } catch (Exception e) {
            log.error("save google calendar auth result error", e);
            throw new CommonException(ResponseCodeEnum.GOOGLE_OAUTH_ERROR);
        }
        return null;
    }

    public Boolean testSyncOneAppointment(Integer groomingId) {
        MoeGroomingAppointment groomingAppointment = groomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (groomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.GROOMING_NOT_FOUND);
        }
        return syncOneAppointment(null, null, groomingAppointment.getBusinessId(), groomingId);
    }

    /**
     * 默认不延迟同步
     */
    public void checkBusinessHaveGoogleCalendarSync(Integer businessId, Integer groomingId, String appointmentDateStr) {
        checkBusinessHaveGoogleCalendarSync(businessId, groomingId, appointmentDateStr, false);
    }

    /**
     * 立即同步google calendar
     * 可能影响别的代码执行，所以包了一层exception捕获
     * 此方法只会在异步调用，不需要给前端返回状态
     *
     * @param businessId
     * @param groomingId
     */
    public void checkBusinessHaveGoogleCalendarSync(
            Integer businessId, Integer groomingId, String appointmentDateStr, Boolean isGcSyncDelay) {
        if (isGcSyncDelay == null) {
            isGcSyncDelay = false;
        }
        try {
            List<MoeGcSetting> allSetting = gcSettingMapper.selectByBusinessId(businessId);
            // 商家内没有人需要同步到google calendar
            if (CollectionUtils.isEmpty(allSetting)) {
                return;
            }

            // 只同步前三个月-一年内的预约
            java.util.Calendar threeMonthBeforeCalendar = java.util.Calendar.getInstance(); // 得到日历
            threeMonthBeforeCalendar.setTime(new Date()); // 把当前时间赋给日历
            threeMonthBeforeCalendar.add(java.util.Calendar.MONTH, -3);
            java.util.Calendar oneYearAfterCalendar = java.util.Calendar.getInstance(); // 得到日历
            oneYearAfterCalendar.setTime(new Date()); // 把当前时间赋给日历
            oneYearAfterCalendar.add(java.util.Calendar.YEAR, +1);
            Date appointmentDate = null;
            try {
                if (StringUtils.isEmpty(appointmentDateStr)) {
                    appointmentDateStr = groomingAppointmentMapper
                            .selectByPrimaryKey(groomingId)
                            .getAppointmentDate();
                }
                appointmentDate = DateUtil.convertStringToDate(appointmentDateStr);
            } catch (ParseException e) {
                log.error("error appointment date,continue", e);
                return;
            }
            if (appointmentDate.after(threeMonthBeforeCalendar.getTime())
                    && appointmentDate.before(oneYearAfterCalendar.getTime())) {
                if (isGcSyncDelay) {
                    log.info(String.format("addGroomingIdToRedisForGcSyncDelay bid:%s id:%s", businessId, groomingId));
                    addGroomingIdToRedisForGcSyncDelay(businessId, groomingId, appointmentDateStr);
                } else {
                    log.info(String.format("syncOneAppointment bid:%s id:%s", businessId, groomingId));
                    syncOneAppointment(null, null, businessId, groomingId);
                }
            }
        } catch (Exception e) {
            log.error(String.format("sync google calendar error,groomingId:%s", groomingId), e);
        }
    }

    private void addGroomingIdToRedisForGcSyncDelay(Integer businessId, Integer groomingId, String appointmentDateStr) {
        List<MoeGcCalendar> calendarList = gcCalendarMapper.selectByBusinessId(businessId);
        Long syncCreateTime = DateUtil.get10Timestamp();
        for (MoeGcCalendar calendar : calendarList) {

            String gcRedisListKey = getGcRedisListKey(calendar.getId());
            RedisGroomingListElementObj elementObj =
                    new RedisGroomingListElementObj(businessId, groomingId, appointmentDateStr, syncCreateTime);
            // 增加在最后面
            redisUtil.lRightPush(gcRedisListKey, JsonUtil.toJson(elementObj));
        }
    }

    public void deleteBlock(Integer businessId, String eventId) {
        gcEventMapper.selectByEventId(businessId, eventId);
    }

    public void deleteGroomingAllEvent(Integer businessId, Integer groomingId) {
        List<MoeGcEvent> events = gcEventMapper.selectByBusinessIdGroomingId(businessId, groomingId);
        if (!CollectionUtils.isEmpty(events)) {
            for (MoeGcEvent event : events) {
                if (GoogleCalendarConst.EVENT_STATUS_NORMAL.equals(event.getEventStatus())) {
                    deleteGoogleCalendarEvent(event);
                    MoeGcEvent updateBean = new MoeGcEvent();
                    updateBean.setId(event.getId());
                    updateBean.setEventStatus(GoogleCalendarConst.EVENT_STATUS_DELETE);
                    updateBean.setUpdateTime(DateUtil.get10Timestamp());
                    gcEventMapper.updateByPrimaryKeySelective(updateBean);
                }
            }
        }
    }

    public void deleteEventByNotDeleteGcCalendarId(
            Integer businessId, Integer groomingId, Collection<Integer> notNeedUpdateGcCalendarIdSet) {
        List<MoeGcEvent> events = gcEventMapper.selectByBusinessIdGroomingId(businessId, groomingId);
        if (CollectionUtils.isEmpty(events)) {
            return;
        }
        events.stream()
                .filter(event -> CollectionUtils.isEmpty(notNeedUpdateGcCalendarIdSet)
                        || !notNeedUpdateGcCalendarIdSet.contains(event.getGcCalendarId()))
                .forEach(event -> {
                    deleteGoogleCalendarEvent(event);
                    MoeGcEvent updateBean = new MoeGcEvent();
                    updateBean.setId(event.getId());
                    updateBean.setEventStatus(GoogleCalendarConst.EVENT_STATUS_DELETE);
                    updateBean.setUpdateTime(DateUtil.get10Timestamp());
                    gcEventMapper.updateByPrimaryKeySelective(updateBean);
                });
    }

    public void setEventDeleteByGcCalendarId(Integer businessId, Integer gcCalendarId) {
        List<MoeGcEvent> events = gcEventMapper.selectByGcCalendarId(businessId, gcCalendarId);
        if (CollectionUtils.isEmpty(events)) {
            return;
        }
        for (MoeGcEvent event : events) {
            MoeGcEvent updateBean = new MoeGcEvent();
            updateBean.setId(event.getId());
            updateBean.setEventStatus(GoogleCalendarConst.EVENT_STATUS_DELETE);
            updateBean.setUpdateTime(DateUtil.get10Timestamp());
            gcEventMapper.updateByPrimaryKeySelective(updateBean);
        }
    }

    /**
     * 同步某个预约到该同步的日历上
     *
     * @param businessInfo 可选，为空重新获取
     * @param staffDtoList 可选，staff 不在 list 里再查一遍接口
     * @param businessId
     * @param groomingId
     * @return
     */
    public Boolean syncOneAppointment(
            MoeBusinessDto businessInfo, List<MoeStaffDto> staffDtoList, Integer businessId, Integer groomingId) {
        MoeGroomingAppointment groomingAppointment =
                groomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(groomingId, businessId);
        if (groomingAppointment == null) {
            log.error("groomingAppointment is null, appointmentId:{}", groomingId);
            return false;
        }

        // 从google calendar同步过来的block，还可能需要同步到别的calendar上
        if (!appointmentIsNeedSyncCalendar(groomingAppointment)) {
            // 只删除
            // 检查是否同步过，同步过就删除
            deleteGroomingAllEvent(businessId, groomingId);
            return true;
        }

        if (Objects.isNull(businessInfo)) {
            // 获取商户时区
            businessInfo = iBusinessBusinessClient.getBusinessInfo(
                    InfoIdParams.builder().infoId(businessId).build());
        }

        // 保存(插入或更新)
        List<CalendarEventParamDto> eventParamDtoList = new ArrayList<>();
        if (GroomingAppointmentEnum.IS_BLOCK_TRUE.equals(groomingAppointment.getIsBlock())) {
            // block数据的处理
            List<MoeGroomingPetDetail> petDetailList =
                    groomingPetDetailMapper.queryPetDetailCountByGroomingId(groomingId);
            if (CollectionUtils.isEmpty(petDetailList)) {
                return true;
            }
            MoeGroomingPetDetail blockPetDetail = petDetailList.get(petDetailList.size() - 1);
            MoeGroomingNote commentNote = moeGroomingNoteService.getNoteByGroomingIdAndType(
                    groomingId, GroomingAppointmentEnum.NOTE_COMMENT.intValue());
            String description = null;
            if (commentNote != null) {
                description = commentNote.getNote();
            }
            MoeStaffDto staffDto = staffDtoList != null
                    ? staffDtoList.stream()
                            .findAny()
                            .filter(staff -> staff.getId().equals(blockPetDetail.getStaffId()))
                            .orElse(null)
                    : null;
            if (staffDto == null) {
                StaffIdParams staffIdParams = new StaffIdParams();
                staffIdParams.setBusinessId(businessId);
                staffIdParams.setStaffId(blockPetDetail.getStaffId());
                staffDto = iBusinessStaffClient.getStaff(staffIdParams);
            }
            eventParamDtoList.add(CalendarCreateHelper.convertBlockCalenderEventParamsList(
                    blockPetDetail, description, staffDto.getFirstName(), staffDto.getLastName()));
            if (GroomingAppointmentEnum.SOURCE_GOOGLE_CALENDAR.equals(groomingAppointment.getSource())) {
                MoeGcAppt appt = gcApptMapper.selectByBlockId(businessId, groomingId);
                eventParamDtoList.get(0).setImportGcCalendarId(appt.getGcCalendarId());
            }
        } else {
            // 预约数据的处理
            GroomingTicketDetailDTO detailDTO =
                    groomingAppointmentService.getAppointmentDetailForGoogleCalendarSync(businessId, groomingId);
            eventParamDtoList = CalendarCreateHelper.convertAppointmentCalenderEventParamsList(
                    businessInfo, detailDTO.getGroomingCustomerInfo(), detailDTO.getPetInfoDetails());
        }
        if (CollectionUtils.isEmpty(eventParamDtoList)) {
            return true;
        }

        // 查询所有的calendar
        List<Integer> syncedStaffIds = new ArrayList<>();
        // 补充预约日期和商户时区
        for (CalendarEventParamDto eventParamDto : eventParamDtoList) {
            syncedStaffIds.add(eventParamDto.getSyncedStaffId());
            eventParamDto.setAppointmentDate(groomingAppointment.getAppointmentDate());
            eventParamDto.setGroomingId(groomingAppointment.getId());
            eventParamDto.setTimezone(businessInfo.getTimezoneName());
        }
        List<MoeGcCalendar> calendarList = gcCalendarMapper.selectBySyncedStaffIds(businessId, syncedStaffIds);
        List<MoeGcCalendar> needSyncedCalendarList = new ArrayList<>();
        calendarList.stream()
                .filter(calendar -> GoogleCalendarConst.CALENDAR_STATUS_NORMAL.equals(calendar.getCalendarStatus()))
                .forEach(needSyncedCalendarList::add);

        Map<Integer, List<MoeGcAppt>> gcApptMap =
                gcApptMapper.selectByBusinessIdGroomingId(businessId, groomingId).stream()
                        .collect(Collectors.groupingBy(MoeGcAppt::getGcCalendarId));
        Long appointmentUpdateTime = getAppointmentUpdateTime(groomingId);

        Set<Integer> notNeedUpdateGcCalendarIdSet = new HashSet<>();
        for (MoeGcCalendar gcCalendar : needSyncedCalendarList) {
            // 比对最后同步时间，如果在预约更新前已经同步过，则跳过
            List<MoeGcAppt> currentAppt = gcApptMap.getOrDefault(gcCalendar.getId(), List.of());
            Long lastSyncTime = getLastSyncTimeByAppt(currentAppt);
            if (lastSyncTime >= appointmentUpdateTime) {
                log.info(
                        "appointmentId:{} for gcCalendarId:{} has been synced, lastSyncTime:{}, appointmentUpdateTime:{}",
                        groomingId,
                        gcCalendar.getId(),
                        lastSyncTime,
                        appointmentUpdateTime);
                notNeedUpdateGcCalendarIdSet.add(gcCalendar.getId());
            }
        }

        // 删除日历
        deleteEventByNotDeleteGcCalendarId(businessId, groomingId, notNeedUpdateGcCalendarIdSet);

        // 遍历发起同步
        for (MoeGcCalendar gcCalendar : needSyncedCalendarList) {
            if (notNeedUpdateGcCalendarIdSet.contains(gcCalendar.getId())) {
                continue;
            }

            // 同步至 Google Calendar
            try {
                createOrUpdateCalendarEvent(eventParamDtoList, gcCalendar);
            } catch (Exception e) {
                log.error("createOrUpdateCalendarEvent error", e);
                throw e;
            }

            // 保存同步结果
            List<MoeGcAppt> gcApptList = gcApptMap.getOrDefault(gcCalendar.getId(), List.of());
            if (CollectionUtils.isEmpty(gcApptList)) {
                MoeGcAppt addBean = new MoeGcAppt();
                addBean.setBusinessId(businessId);
                addBean.setCompanyId(groomingAppointment.getCompanyId());
                addBean.setCustomerId(groomingAppointment.getCustomerId());
                addBean.setGcCalendarId(gcCalendar.getId());
                addBean.setGroomingId(groomingId);
                addBean.setSyncType(GoogleCalendarConst.SYNC_TYPE_EXPORT);
                addBean.setCreateTime(DateUtil.get10Timestamp());
                addBean.setUpdateTime(DateUtil.get10Timestamp());
                gcApptMapper.insertSelective(addBean);
            } else {
                for (MoeGcAppt gcAppt : gcApptList) {
                    MoeGcAppt updateBean = new MoeGcAppt();
                    updateBean.setUpdateTime(DateUtil.get10Timestamp());
                    updateBean.setId(gcAppt.getId());
                    gcApptMapper.updateByPrimaryKeySelective(updateBean);
                }
            }
        }
        return true;
    }

    private static Long getLastSyncTime(List<MoeGcEvent> events) {
        return events.stream()
                .map(MoeGcEvent::getUpdateTime)
                .max(Long::compareTo)
                .orElse(0L);
    }

    private static Long getLastSyncTimeByAppt(List<MoeGcAppt> events) {
        return events.stream()
                .map(MoeGcAppt::getUpdateTime)
                .max(Long::compareTo)
                .orElse(0L);
    }

    private Long getAppointmentUpdateTime(Integer groomingId) {
        // 获取预约最后更新时间
        return moeGroomingPetDetailMapper
                .queryPetDetailByServiceItems(groomingId, List.of(ServiceItemEnum.GROOMING.getServiceItem()))
                .stream()
                .map(GroomingPetDetailDTO::getUpdatedAt)
                .map(DateUtil::get10Timestamp)
                .max(Integer::compareTo)
                .map(Integer::longValue)
                .orElse(0L);
    }

    public void deleteGoogleCalendarEvent(Calendar calendarService, String googleCalendarId, String eventId) {
        try {
            calendarService.events().delete(googleCalendarId, eventId).execute();
        } catch (IOException e) {
            log.error(String.format("delete event error,%s", eventId), e);
        }
    }

    public void deleteGoogleCalendarEvent(MoeGcEvent gcEvent) {
        MoeGcCalendar gcCalendar = gcCalendarMapper.selectByPrimaryKey(gcEvent.getGcCalendarId());
        Calendar calendarService = SyncCalendarUtil.initCalendarByCredential(createMyGcCalendar(gcCalendar));
        deleteGoogleCalendarEvent(calendarService, gcCalendar.getGoogleCalendarId(), gcEvent.getEventId());
        googleCalendarApiLimitAdd(gcEvent.getGcCalendarId());
    }

    public void createOrUpdateCalendarEvent(List<CalendarEventParamDto> eventParamDtoList, MoeGcCalendar gcCalendar) {
        // 如果是block，且从某个calendar导入进来，那跳过这个calendar，别的calendar正常导出
        if (eventParamDtoList.get(0).getImportGcCalendarId() != null
                && eventParamDtoList.get(0).getImportGcCalendarId().equals(gcCalendar.getId())) {
            return;
        }
        GoogleCalendarSettingDto settingDto =
                getCalendarSettingDto(gcCalendar.getBusinessId(), gcCalendar.getStaffId());
        // setting断连，不需要同步
        if (!GoogleCalendarConst.SETTING_STATUS_NORMAL.equals(settingDto.getStatus())) {
            return;
        }
        // setting未断连，但是把calendar删除了，不需要同步了
        boolean isFind = false;
        for (MoeGcCalendar calendar : settingDto.getGcCalendarList()) {
            if (gcCalendar.getId().equals(calendar.getId())) {
                isFind = true;
                break;
            }
        }
        if (!isFind) {
            return;
        }
        // 获取google calendar service
        Calendar calendarService =
                SyncCalendarUtil.initCalendarByCredential(createMyTokenResponse(settingDto.getGoogleAuthId()));
        String googleCalendarId = gcCalendar.getGoogleCalendarId();
        // 将event排序
        eventParamDtoList.sort(Comparator.comparing(CalendarEventParamDto::getStartTime));

        // 重新插入新的event
        for (CalendarEventParamDto eventParamDto : eventParamDtoList) {
            // 如果不是这个 calendar 的 staff，不需要同步
            if (!eventParamDto.getSyncedStaffId().equals(gcCalendar.getSyncedStaffId())) {
                continue;
            }

            if (Boolean.FALSE.equals(eventParamDto.getIsBlock())) {
                // 解析 title 和 description
                SyncCalendarUtil.parseEventTitle(settingDto.getEventTitle(), eventParamDto);
                SyncCalendarUtil.parseEventDescription(settingDto.getEventDescription(), eventParamDto);
            }

            Event newEvent = createGoogleCalendarEvent(eventParamDto, calendarService, googleCalendarId);
            if (Objects.isNull(newEvent)) {
                continue;
            }
            googleCalendarApiLimitAdd(gcCalendar.getId());
            MoeGcEvent addEvent = new MoeGcEvent();
            addEvent.setBusinessId(gcCalendar.getBusinessId());
            addEvent.setCompanyId(gcCalendar.getCompanyId());
            addEvent.setGcCalendarId(gcCalendar.getId());
            addEvent.setGroomingId(eventParamDto.getGroomingId());
            addEvent.setEventStatus(GoogleCalendarConst.EVENT_STATUS_NORMAL);
            addEvent.setStaffId(eventParamDto.getSyncedStaffId());
            addEvent.setStartTime(eventParamDto.getStartTime());
            addEvent.setEndTime(eventParamDto.getEndTime());
            addEvent.setEventId(newEvent.getId());
            addEvent.setCreateTime(DateUtil.get10Timestamp());
            addEvent.setUpdateTime(DateUtil.get10Timestamp());
            gcEventMapper.insertSelective(addEvent);
        }
    }

    private static Event createGoogleCalendarEvent(
            CalendarEventParamDto eventParamDto, Calendar calendarService, String googleCalendarId) {
        Event newEvent;
        try {
            newEvent = CalendarCreateHelper.setEventData(eventParamDto);
        } catch (ParseException e) {
            log.error(
                    String.format(
                            "CalendarCreateHelper setEventData date error,json:%s", JsonUtil.toJson(eventParamDto)),
                    e);
            return null;
        }
        try {
            newEvent =
                    calendarService.events().insert(googleCalendarId, newEvent).execute();
        } catch (IOException e) {
            log.error(
                    String.format(
                            "calendarService insert event error,googleCalendarId:%s newEvent:%s",
                            googleCalendarId, JsonUtil.toJson(newEvent)),
                    e);
            return null;
        }
        return newEvent;
    }

    public Boolean appointmentIsNeedSyncCalendar(MoeGroomingAppointment groomingAppointment) {
        if (groomingAppointment == null) {
            return false;
        }
        if (AppointmentStatusEnum.CANCELED.getValue().equals(groomingAppointment.getStatus())) {
            return false;
        }
        if (GroomingAppointmentEnum.IS_WAITING_LIST.equals(groomingAppointment.getIsWaitingList())) {
            return false;
        }
        if (GroomingAppointmentEnum.IS_DEPRECATE_TRUE.equals(groomingAppointment.getIsDeprecate())) {
            return false;
        }
        if (!GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB.equals(groomingAppointment.getBookOnlineStatus())) {
            return false;
        }
        return true;
    }

    public Object testWatchCalendar(Integer authId, String calendarId) throws IOException {
        MoeGcAuthStaff gcAuthStaff = gcAuthStaffMapper.selectByPrimaryKey(authId);
        Calendar calendarService = SyncCalendarUtil.initCalendarByCredential(createMyTokenResponse(gcAuthStaff));
        com.google.api.services.calendar.model.Channel content = new Channel();
        content.setId(CommonUtil.getRandomIntLowString(16));
        content.setToken("hereIsToken");
        content.setType("webHook");
        content.setAddress("https://api.t2.moego.pet/grooming/google/calendar/test/webhook");
        return calendarService.calendarList().watch(content).execute();
    }

    public void freshWebHookLastNotifyTime(String channelUuid) {
        MoeGcWatchEvent watchEvent = gcWatchEventMapper.selectByChannelUuid(channelUuid);
        if (watchEvent == null) {
            return;
        }
        MoeGcWatchEvent updateBean = new MoeGcWatchEvent();
        updateBean.setId(watchEvent.getId());
        updateBean.setLastNotifyTime(DateUtil.get10Timestamp());
        updateBean.setUpdateTime(DateUtil.get10Timestamp());
        gcWatchEventMapper.updateByPrimaryKeySelective(updateBean);
    }

    public boolean getCalendarIsDelete(Integer gcAuthId, String googleCalendarId) {
        try {
            MoeGcAuthStaff gcAuthStaff = gcAuthStaffMapper.selectByPrimaryKey(gcAuthId);
            Calendar calendarService = SyncCalendarUtil.initCalendarByCredential(createMyTokenResponse(gcAuthStaff));
            CalendarListEntry calendarListEntry =
                    calendarService.calendarList().get(googleCalendarId).execute();
            if (calendarListEntry.getDeleted() == null) {
                return false;
            }
            return calendarListEntry.getDeleted();
        } catch (IOException e) {
            if (((GoogleJsonResponseException) e).getStatusCode() != 404) {
                log.error("get calendar list entry 404", e);
                return false;
            }
            log.error("get calendar list entry error", e);
            return true;
        }
    }

    public void testBatchSyncEvent(Integer gcCalendarId) {
        MoeGcCalendar gcCalendar = gcCalendarMapper.selectByPrimaryKey(gcCalendarId);
        batchSyncEvent(getCalendarSettingDto(gcCalendar.getBusinessId(), gcCalendar.getStaffId()), gcCalendar);
    }

    public void batchSyncEvent(GoogleCalendarSettingDto settingDto, MoeGcCalendar gcCalendar) {
        // 获取商户时区
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(gcCalendar.getBusinessId());
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        String timezoneName = businessInfo.getTimezoneName();
        MoeGcAuthStaff gcAuthStaff = gcAuthStaffMapper.selectByPrimaryKey(settingDto.getGoogleAuthId());
        Calendar calendarService = SyncCalendarUtil.initCalendarByCredential(createMyTokenResponse(gcAuthStaff));
        String calendarId = gcCalendar.getGoogleCalendarId();
        String pageToken = null;
        Calendar.Events.List eventRequest;
        try {
            eventRequest = calendarService.events().list(calendarId).setMaxResults(1000);

            // 只同步三个月前-两年后的日历
            java.util.Calendar threeMonthBeforeCalendar = java.util.Calendar.getInstance(); // 得到日历
            threeMonthBeforeCalendar.setTime(new Date()); // 把当前时间赋给日历
            threeMonthBeforeCalendar.add(java.util.Calendar.MONTH, -3);
            java.util.Calendar twoYearAfterCalendar = java.util.Calendar.getInstance(); // 得到日历
            twoYearAfterCalendar.setTime(new Date()); // 把当前时间赋给日历
            twoYearAfterCalendar.add(java.util.Calendar.YEAR, +2);
            eventRequest.setTimeMin(new DateTime(threeMonthBeforeCalendar.getTime(), TimeZone.getTimeZone("UTC")));
            eventRequest.setTimeMax(new DateTime(twoYearAfterCalendar.getTime(), TimeZone.getTimeZone("UTC")));
        } catch (IOException e) {
            log.error("calendar service list error", e);
            throw new CommonException(ResponseCodeEnum.GET_EVENT_ERROR);
        }
        java.util.Calendar threeMonthCalendar = java.util.Calendar.getInstance();
        threeMonthCalendar.setTime(new Date());
        threeMonthCalendar.add(java.util.Calendar.MONTH, -3);
        eventRequest.setTimeMin(new DateTime(threeMonthCalendar.getTime(), TimeZone.getTimeZone(timezoneName)));
        Events events;
        // 获取calendar所有同步过的appt
        List<MoeGcEvent> allSyncEvent =
                gcEventMapper.selectByGcCalendarId(gcCalendar.getBusinessId(), gcCalendar.getId());
        List<String> exportAllEventId = new ArrayList<>();
        allSyncEvent.forEach(syncEvent -> {
            exportAllEventId.add(syncEvent.getEventId());
        });
        List<Integer> importAllGcApptIdList = new ArrayList<>();
        do {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("sleep error", e);
            }
            if (pageToken != null) {
                eventRequest.setPageToken(pageToken);
            }
            try {
                // get event list
                events = eventRequest.execute();
            } catch (IOException i) {
                log.error("calendar service list IO", i);
                throw new CommonException(ResponseCodeEnum.GET_EVENT_ERROR);
            }
            pageToken = events.getNextPageToken();
            if (CollectionUtils.isEmpty(events.getItems())) {
                break;
            }
            for (Event event : events.getItems()) {
                if (exportAllEventId.contains(event.getId())) {
                    // 从moego export的appt/block  不需要被同步
                    continue;
                }
                if (event.getStart() == null || event.getEnd() == null) {
                    // 没有时间的event无法导入
                    continue;
                }
                List<AppointmentBlockParams> params = BlockCreateHelper.conventEventToBlock(event, timezoneName);
                // delete or update
                List<MoeGcAppt> gcBlockList = gcApptMapper.selectImportBlockByEventId(
                        gcCalendar.getBusinessId(), gcCalendar.getId(), event.getId());
                gcBlockList.forEach(gcAppt -> {
                    importAllGcApptIdList.add(gcAppt.getId());
                });
                int maxEventItem = params.size();
                int blockSize = gcBlockList.size();
                for (int i = 0; i < maxEventItem; i++) {
                    Integer groomingId = null;
                    MoeGcAppt gcAppt = null;
                    AppointmentBlockParams blockParams = params.get(i);
                    blockParams.setTokenBusinessId(gcCalendar.getBusinessId());
                    blockParams.setTokenCompanyId(gcCalendar.getCompanyId());
                    blockParams.setTokenStaffId(gcCalendar.getStaffId());
                    blockParams.setStaffId(gcCalendar.getSyncedStaffId());
                    blockParams.setSource(GroomingAppointmentEnum.SOURCE_GOOGLE_CALENDAR);
                    if (blockSize > i) {
                        gcAppt = gcBlockList.get(i);
                    }
                    if (gcAppt == null) {
                        // insert
                        blockParams.setColorCode(GroomingAppointmentEnum.BLOCK_DEFAULT_COLOR);
                        AddResultDTO resultDTO = moeGroomingAppointmentService.addAppointmentBlock(
                                blockParams); // google calende import event to moego
                        groomingId = resultDTO.getId();
                        // 更新保存 appt记录
                        MoeGcAppt addBean = new MoeGcAppt();
                        addBean.setBusinessId(gcCalendar.getBusinessId());
                        addBean.setCompanyId(gcCalendar.getCompanyId());
                        addBean.setCustomerId(0);
                        addBean.setGcCalendarId(gcCalendar.getId());
                        addBean.setGroomingId(resultDTO.getId());
                        addBean.setSyncType(GoogleCalendarConst.SYNC_TYPE_IMPORT);
                        addBean.setImportEventId(event.getId());
                        addBean.setCreateTime(DateUtil.get10Timestamp());
                        addBean.setUpdateTime(DateUtil.get10Timestamp());
                        gcApptMapper.insertSelective(addBean);
                        // 将新增的插入，否则后面会删除
                        importAllGcApptIdList.add(addBean.getId());
                    } else {
                        // update
                        blockParams.setTicketId(gcAppt.getGroomingId());
                        blockParams.setCheckGcExport(false);
                        moeGroomingAppointmentService.modifyAppointmentBlock(blockParams);
                        // 更新gc appt记录
                        MoeGcAppt updateBean = new MoeGcAppt();
                        updateBean.setId(gcAppt.getId());
                        updateBean.setUpdateTime(DateUtil.get10Timestamp());
                        gcApptMapper.updateByPrimaryKeySelective(updateBean);
                        groomingId = gcAppt.getGroomingId();
                    }
                }
                if (gcBlockList.size() > maxEventItem) {
                    for (int i = maxEventItem; i < blockSize; i++) {
                        // delete block
                        IdParams idParams = new IdParams();
                        idParams.setBusinessId(gcCalendar.getBusinessId());
                        idParams.setId(gcBlockList.get(i).getGroomingId());
                        moeGroomingAppointmentService.deleteAppointmentBlockAndRepeatBlock(
                                idParams, gcCalendar.getBusinessId());
                    }
                }
            }
        } while (pageToken != null);
        if (CollectionUtils.isEmpty(importAllGcApptIdList)) {
            return;
        }
        // 删除找不到的event 且  block状态是未 cancel 状态
        List<MoeGcAppt> gcAppts = gcApptMapper.selectDeletedImportBlockById(
                gcCalendar.getBusinessId(), gcCalendar.getId(), importAllGcApptIdList);
        if (!CollectionUtils.isEmpty(gcAppts)) {
            for (MoeGcAppt gcAppt : gcAppts) {
                // delete block
                IdParams idParams = new IdParams();
                idParams.setBusinessId(gcCalendar.getBusinessId());
                idParams.setId(gcAppt.getGroomingId());
                moeGroomingAppointmentService.deleteAppointmentBlockAndRepeatBlock(
                        idParams, gcCalendar.getBusinessId());
            }
        }
    }

    public void watchEvent(Byte settingSyncType, MoeGcCalendar gcCalendar) {
        if (GoogleCalendarConst.SETTING_SYNC_TYPE_ALL.equals(settingSyncType)
                || GoogleCalendarConst.SETTING_SYNC_TYPE_ONLY_IMPORT.equals(settingSyncType)) {
            try {
                Calendar calendarService = SyncCalendarUtil.initCalendarByCredential(createMyGcCalendar(gcCalendar));
                com.google.api.services.calendar.model.Channel content = new Channel();
                content.setId(CommonUtil.getRandomIntLowString(32));
                content.setType(GoogleCalendarConst.WATCH_TYPE_WEBHOOK);
                content.setAddress(eventWebHook);
                Channel channel = calendarService
                        .events()
                        .watch(gcCalendar.getGoogleCalendarId(), content)
                        .execute();
                MoeGcWatchEvent event = new MoeGcWatchEvent();
                event.setBusinessId(gcCalendar.getBusinessId());
                event.setCompanyId(gcCalendar.getCompanyId());
                event.setGcCalendarId(gcCalendar.getId());
                event.setResourceId(channel.getResourceId());
                event.setChannelUuid(channel.getId());
                event.setTokenExpiredTime(channel.getExpiration() / 1000);
                event.setCreateTime(DateUtil.get10Timestamp());
                event.setUpdateTime(DateUtil.get10Timestamp());
                gcWatchEventMapper.insertSelective(event);
            } catch (IOException e) {
                log.error("watch event error id:" + gcCalendar.getId(), e);
            }
        }
    }

    public com.google.api.services.calendar.model.Calendar createStaffCalendar(
            Integer authId, String calendarName, String staffName, MoeBusinessDto businessDto) throws IOException {
        MoeGcAuthStaff gcAuthStaff = gcAuthStaffMapper.selectByPrimaryKey(authId);
        Calendar calendarService = SyncCalendarUtil.initCalendarByCredential(createMyTokenResponse(gcAuthStaff));
        com.google.api.services.calendar.model.Calendar myCalendar =
                new com.google.api.services.calendar.model.Calendar();
        myCalendar.setTimeZone(businessDto.getTimezoneName());
        myCalendar.setSummary(
                SyncCalendarUtil.parseCalendarName(calendarName, staffName, businessDto.getBusinessName()));
        return calendarService.calendars().insert(myCalendar).execute();
    }

    public com.google.api.services.calendar.model.Calendar updateStaffCalendarName(
            Integer authId, String calendarName, String staffName, String calendarId, MoeBusinessDto businessDto)
            throws IOException {
        MoeGcAuthStaff gcAuthStaff = gcAuthStaffMapper.selectByPrimaryKey(authId);
        Calendar calendarService = SyncCalendarUtil.initCalendarByCredential(createMyTokenResponse(gcAuthStaff));
        com.google.api.services.calendar.model.Calendar myCalendar =
                new com.google.api.services.calendar.model.Calendar();
        myCalendar.setTimeZone(businessDto.getTimezoneName());
        myCalendar.setSummary(
                SyncCalendarUtil.parseCalendarName(calendarName, staffName, businessDto.getBusinessName()));
        return calendarService.calendars().update(calendarId, myCalendar).execute();
    }

    /**
     * 不同的setting下的不同calendar，执行相互独立，错误不应该影响别的执行
     */
    @TimerMetrics(group = TimerGroup.TASK)
    public void taskBeginImport() {
        List<MoeGcSetting> settingList = gcSettingMapper.selectNeedImportSetting();
        Long maxExpireTime = DateUtil.get10Timestamp() + GoogleCalendarConst.WEB_HOOK_EXPIRED_TIME_BEFORE_SECOND;

        List<Future> futureList = new LinkedList<>();

        for (MoeGcSetting setting : settingList) {
            // 查询setting内是否打开
            GoogleCalendarSettingDto settingDto = getCalendarSettingDto(setting.getBusinessId(), setting.getStaffId());
            if (!GoogleCalendarConst.SETTING_STATUS_NORMAL.equals(settingDto.getStatus())) {
                continue;
            }
            Future future = ThreadPool.submit(() -> {
                // 加锁，一个setting一个锁
                String lockKey = moegoLockManager.getResourceKey(LockManager.GC_IMPORT, setting.getId());
                String randomValue = CommonUtil.getUuid();
                try {
                    if (moegoLockManager.lock(lockKey, randomValue, GoogleCalendarConst.LOCK_MAX_SECONDS)) {
                        // 根据setting 内的 dto 查询需要检查的 calendar
                        for (MoeGcCalendar gcCalendar : settingDto.getGcCalendarList()) {
                            try {
                                boolean isDelete = getCalendarIsDelete(
                                        settingDto.getGoogleAuthId(), gcCalendar.getGoogleCalendarId());
                                googleCalendarApiLimitAdd(gcCalendar.getId());
                                if (isDelete) {
                                    MoeGcCalendar updateBean = new MoeGcCalendar();
                                    updateBean.setId(gcCalendar.getId());
                                    updateBean.setCalendarStatus(GoogleCalendarConst.CALENDAR_STATUS_DELETE);
                                    gcCalendarMapper.updateByPrimaryKeySelective(updateBean);
                                    continue;
                                }
                                MoeGcWatchEvent watchEvent =
                                        gcWatchEventMapper.selectByGcCalendarId(gcCalendar.getId());
                                // 判断是否更新webhook
                                if (watchEvent == null || watchEvent.getTokenExpiredTime() < maxExpireTime) {
                                    watchEvent(setting.getSyncType(), gcCalendar);
                                }
                                // 判断是否触发import block
                                if (watchEvent != null
                                        && watchEvent.getLastSyncTime() < watchEvent.getLastNotifyTime()) {
                                    batchSyncEvent(settingDto, gcCalendar);
                                    // 刷新 last sync time
                                    MoeGcWatchEvent updateBean = new MoeGcWatchEvent();
                                    updateBean.setId(watchEvent.getId());
                                    updateBean.setLastSyncTime(DateUtil.get10Timestamp());
                                    gcWatchEventMapper.updateByPrimaryKeySelective(updateBean);
                                }
                            } catch (Exception e) {
                                log.error(String.format("taskBeginImport gcCalendar(%s) error", gcCalendar.getId()), e);
                            }
                        }
                    } else {
                        throw new CommonException(ResponseCodeEnum.PARALLEL_ERROR, "get lock failed for " + lockKey);
                    }
                } finally {
                    moegoLockManager.unlock(lockKey, randomValue);
                }
            });
            futureList.add(future);
        }

        // 阻塞等待所有子线程运行完毕，以便 TimerMetrics 记录真实的运行时间
        futureList.forEach(future -> {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("taskBeginImport future task error", e);
            }
        });
    }

    public void addRedisSyncDateRange(Integer businessId, MoeGcCalendar newCalendar) {
        java.util.Calendar twoMonthBeforeCalendar = java.util.Calendar.getInstance();
        twoMonthBeforeCalendar.setTime(new Date());
        twoMonthBeforeCalendar.add(java.util.Calendar.MONTH, -3);
        java.util.Calendar twoYearAfterCalendar = java.util.Calendar.getInstance();
        twoYearAfterCalendar.setTime(new Date());
        twoYearAfterCalendar.add(java.util.Calendar.YEAR, +1);

        // 根据需求，同步次序为：当月、下个月、上个月、其余月份
        java.util.Calendar utilsCalendar = java.util.Calendar.getInstance();
        // 查询上个月第一天
        utilsCalendar.setTime(new Date());
        utilsCalendar.add(java.util.Calendar.MONTH, -1);
        utilsCalendar.set(java.util.Calendar.DAY_OF_MONTH, 1);
        Date lastMonthFirstDayTime = utilsCalendar.getTime();
        // 查询当月第一天
        utilsCalendar.setTime(new Date());
        utilsCalendar.set(java.util.Calendar.DAY_OF_MONTH, 1);
        Date thisMonthFirstDayTime = utilsCalendar.getTime();
        // 查询下个月第一天
        utilsCalendar.setTime(new Date());
        utilsCalendar.add(java.util.Calendar.MONTH, +1);
        utilsCalendar.set(java.util.Calendar.DAY_OF_MONTH, 1);
        Date nextMonthFirstDayTime = utilsCalendar.getTime();
        // 查询下下个月最后一天
        utilsCalendar.setTime(new Date());
        utilsCalendar.add(java.util.Calendar.MONTH, +2);
        utilsCalendar.set(java.util.Calendar.DAY_OF_MONTH, 1);
        Date nextNextMonthFirstDayTime = utilsCalendar.getTime();
        // 上个月的预约查询
        List<QbQueryGroomingResultDto> groomingResultDtosLastMonth =
                moeGroomingAppointmentMapper.queryAppointmentDateByDateRangeWithStaffId(
                        businessId,
                        newCalendar.getSyncedStaffId(),
                        DateUtil.dateToStr(lastMonthFirstDayTime),
                        DateUtil.dateToStr(thisMonthFirstDayTime));
        // 当月的预约总计
        List<QbQueryGroomingResultDto> groomingResultDtosThisMonth =
                moeGroomingAppointmentMapper.queryAppointmentDateByDateRangeWithStaffId(
                        businessId,
                        newCalendar.getSyncedStaffId(),
                        DateUtil.dateToStr(thisMonthFirstDayTime),
                        DateUtil.dateToStr(nextMonthFirstDayTime));
        // 下个月的预约查询
        List<QbQueryGroomingResultDto> groomingResultDtosNextMonth =
                moeGroomingAppointmentMapper.queryAppointmentDateByDateRangeWithStaffId(
                        businessId,
                        newCalendar.getSyncedStaffId(),
                        DateUtil.dateToStr(nextMonthFirstDayTime),
                        DateUtil.dateToStr(nextNextMonthFirstDayTime));
        // 查询某个staff 的所有预约
        List<QbQueryGroomingResultDto> groomingResultDtos =
                moeGroomingAppointmentMapper.queryAppointmentDateByDateRangeWithStaffId(
                        businessId,
                        newCalendar.getSyncedStaffId(),
                        DateUtil.dateToStr(twoMonthBeforeCalendar.getTime()),
                        DateUtil.dateToStr(twoYearAfterCalendar.getTime()));
        List<QbQueryGroomingResultDto> groomingSyncList = new ArrayList<>();
        groomingSyncList.addAll(groomingResultDtosThisMonth);
        groomingSyncList.addAll(groomingResultDtosNextMonth);
        groomingSyncList.addAll(groomingResultDtosLastMonth);
        groomingSyncList.addAll(groomingResultDtos);

        String gcRedisListKey = getGcRedisListKey(newCalendar.getId());
        Long syncCreateTime = DateUtil.get10Timestamp();
        for (QbQueryGroomingResultDto resultDto : groomingSyncList) {
            RedisGroomingListElementObj elementObj = new RedisGroomingListElementObj(
                    businessId, resultDto.getGroomingId(), resultDto.getAppointmentDate(), syncCreateTime);
            // 增加在最后面
            redisUtil.lRightPush(gcRedisListKey, JsonUtil.toJson(elementObj));
        }
        taskBeginExport();
    }

    public String getGcInProgress(Integer businessId) {
        String gcRedisStringKey = getGcRedisListKeyInProgress(businessId);
        return redisUtil.get(gcRedisStringKey);
    }

    public RedisGroomingListElementObj getRedisSyncGrooming(Integer gcCalendarId) {
        String gcRedisListKey = getGcRedisListKey(gcCalendarId);
        String objJson = getGcInProgress(gcCalendarId);
        if (StringUtils.isEmpty(objJson)) {
            objJson = redisUtil.lLeftPop(gcRedisListKey);
            if (StringUtils.isEmpty(objJson)) {
                return null;
            }
        }
        // 将当前值保存在in progress key内
        setGcInProgress(gcCalendarId, objJson);

        if (!StringUtils.isEmpty(objJson)) {
            RedisGroomingListElementObj elementObj = JsonUtil.toBean(objJson, RedisGroomingListElementObj.class);

            // 错误多次的转移到另一个队列内
            if (elementObj.getErrorCount() == null || elementObj.getErrorCount() >= 3) {
                addRedisErrorSyncToListRight(gcCalendarId, objJson);
                // 当前值已转移，从in progress移除
                delGcInProgress(gcCalendarId);
                return getRedisSyncGrooming(gcCalendarId);
            }
            return elementObj;
        }
        return null;
    }

    public void addRedisToListLeft(MoeGcCalendar gcCalendar, Integer groomingId, String appointmentDate) {
        String gcRedisListKey = getGcRedisListKey(gcCalendar.getId());
        RedisGroomingListElementObj elementObj = new RedisGroomingListElementObj(
                gcCalendar.getBusinessId(), groomingId, appointmentDate, DateUtil.get10Timestamp());
        // 增加在最前面
        redisUtil.lLeftPush(gcRedisListKey, JsonUtil.toJson(elementObj));
    }

    /**
     * 将同步失败的对象放在list最后，等待下次重新同步
     *
     * @param gcCalendarId
     * @param objJson
     */
    public void addRedisErrorSyncToListRight(Integer gcCalendarId, String objJson) {
        String gcRedisListKey = getGcRedisErrorListKey(gcCalendarId);
        redisUtil.lRightPush(gcRedisListKey, objJson);
    }

    /**
     * 将同步失败的对象放在list最后，等待下次重新同步
     *
     * @param gcCalendarId
     * @param elementObj
     */
    public void addRedisErrorSyncToListRight(Integer gcCalendarId, RedisGroomingListElementObj elementObj) {
        String gcRedisListKey = getGcRedisListKey(gcCalendarId);
        elementObj.errorCountPlus();
        redisUtil.lRightPush(gcRedisListKey, JsonUtil.toJson(elementObj));
    }

    public void setGcInProgress(Integer gcCalendarId, String objJson) {
        String gcRedisStringKey = getGcRedisListKeyInProgress(gcCalendarId);
        redisUtil.setEx(gcRedisStringKey, objJson, CommonConstant.DEFAULT_CACHE_TTL, TimeUnit.SECONDS);
    }

    public void delGcInProgress(Integer gcCalendarId) {
        String gcRedisStringKey = getGcRedisListKeyInProgress(gcCalendarId);
        redisUtil.delete(gcRedisStringKey);
    }

    public void beginOneCalendarSyncTask(
            MoeBusinessDto businessInfo, List<MoeStaffDto> staffDtoList, MoeGcCalendar gcCalendar, MoeGcTask gcTask) {
        Integer businessId = gcCalendar.getBusinessId();
        Integer gcCalendarId = gcCalendar.getId();
        RedisGroomingListElementObj groomingData = getRedisSyncGrooming(gcCalendar.getId());
        while (groomingData != null) {
            if (!checkGooGleCalendarApiLimit(gcCalendar.getId())) {
                return;
            }
            MoeGcTask updateGcTask = new MoeGcTask();
            updateGcTask.setId(gcTask.getId());
            updateGcTask.setUpdateTime(DateUtil.get10Timestamp());
            try {
                Integer groomingId = groomingData.getGroomingId();
                // 同步单个groomingId
                syncOneAppointment(businessInfo, staffDtoList, businessId, groomingId);
                updateGcTask.setCompleteCount(gcTask.getCompleteCount() + 1);
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("sleep error", e);
            } catch (Exception e) {
                // 后台任务的异常不会中断执行，会记录错误信息，并执行下一个
                log.error(e.getMessage());
                log.error(String.format(
                        "gcCalendarId:%s,groomingDataJson:%s", gcCalendarId, JsonUtil.toJson(groomingData)));
                addRedisErrorSyncToListRight(gcCalendarId, groomingData);
            }
            googleCalendarApiLimitAdd(gcCalendarId);
            gcTaskMapper.updateByPrimaryKeySelective(updateGcTask);
            // 更新task数据
            gcTask = gcTaskMapper.selectByPrimaryKey(gcTask.getId());
            delGcInProgress(gcCalendar.getId());
            groomingData = getRedisSyncGrooming(gcCalendarId);
        }
        MoeGcTask closeTask = new MoeGcTask();
        closeTask.setId(gcTask.getId());
        closeTask.setTaskStatus(QuickBooksConst.TASK_STATUS_COMPLETE);
        closeTask.setCompleteTime(DateUtil.get10Timestamp());
        closeTask.setUpdateTime(DateUtil.get10Timestamp());
        gcTaskMapper.updateByPrimaryKeySelective(closeTask);
    }

    /**
     * 限制每个小时的调用次数，大于阈值后，跳过执行，下个小时恢复
     * 返回false是不继续执行
     */
    public boolean checkGooGleCalendarApiLimit(Integer gcCalendarId) {
        String thisHourLimit = redisUtil.get(getGcRedisApiLimitKey(gcCalendarId));
        if (StringUtils.isEmpty(thisHourLimit)) {
            return true;
        }
        int thisHourLimitInt = Integer.parseInt(thisHourLimit);
        if (thisHourLimitInt <= GoogleCalendarConst.GOOGLE_CALENDAR_API_LIMIT_BY_HOUR) {
            return true;
        }
        return false;
    }

    public void googleCalendarApiLimitAdd(Integer gcCalendarId) {
        String gcRedisApiLimitKey = getGcRedisApiLimitKey(gcCalendarId);
        redisUtil.incrBy(gcRedisApiLimitKey, 1);
        redisUtil.expire(gcRedisApiLimitKey, GoogleCalendarConst.GOOGLE_CALENDAR_REDIS_KEY_EXPIRE_TIME, TimeUnit.DAYS);
    }

    @TimerMetrics(group = TimerGroup.TASK)
    public void taskBeginExport() {
        // 将已有的超时task标记为失败
        Long nowTime = DateUtil.get10Timestamp();
        gcTaskMapper.updateFailTask(nowTime, nowTime - GoogleCalendarConst.TASK_FAIL_TIME_SECOND);

        List<Future> futureList = new LinkedList<>();

        List<MoeGcSetting> allSettingList = gcSettingMapper.selectNeedExportSetting();
        // split to 10
        List<List<MoeGcSetting>> settingTaskList = CommonUtil.splitList(allSettingList, 10);
        for (List<MoeGcSetting> settingList : settingTaskList) {
            Future<?> future = ThreadPool.submit(() -> {
                gcSettingBeginTask(settingList);
            });
            futureList.add(future);
        }
        // 阻塞等待所有子线程运行完毕，以便 TimerMetrics 记录真实的运行时间
        futureList.forEach(future -> {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("taskBeginExport future task error", e);
            }
        });
    }

    public void gcSettingBeginTask(List<MoeGcSetting> settingList) {
        for (MoeGcSetting setting : settingList) {
            try {
                GoogleCalendarSettingDto settingDto =
                        getCalendarSettingDto(setting.getBusinessId(), setting.getStaffId());
                if (GoogleCalendarConst.SETTING_STATUS_NORMAL.equals(settingDto.getStatus())) {
                    List<MoeStaffDto> staffList = null;
                    MoeBusinessDto businessInfo = null;
                    if (!settingDto.getGcCalendarList().isEmpty()) {
                        // 查 company 下所有 staff
                        staffList =
                                iBusinessStaffClient.getStaffList(new StaffIdListParams(setting.getBusinessId(), null));
                        // 查 business info
                        businessInfo = iBusinessBusinessClient.getBusinessInfo(InfoIdParams.builder()
                                .infoId(setting.getBusinessId())
                                .build());
                    }
                    for (MoeGcCalendar gcCalendar : settingDto.getGcCalendarList()) {
                        try {
                            String gcRedisListKey = getGcRedisListKey(gcCalendar.getId());
                            Long needSyncLen = redisUtil.lLen(gcRedisListKey);
                            if (needSyncLen == null || needSyncLen == 0) {
                                // redis没有数据，不需要同步
                                continue;
                            }
                            MoeGcTask gcTask = gcTaskMapper.selectByGcCalendarId(gcCalendar.getId());
                            if (gcTask != null) {
                                // 已经有任务了，不重复执行
                                continue;
                            }
                            // 创建新task记录
                            MoeGcTask task = new MoeGcTask();
                            task.setGcCalendarId(gcCalendar.getId());
                            task.setTaskStatus(GoogleCalendarConst.TASK_STATUS_START);
                            Long startTime = DateUtil.get10Timestamp();
                            task.setCreateTime(startTime);
                            task.setUpdateTime(startTime);
                            gcTaskMapper.insertSelective(task);
                            // 没有报错就执行下去
                            beginOneCalendarSyncTask(
                                    businessInfo, staffList, gcCalendar, gcTaskMapper.selectByPrimaryKey(task.getId()));
                        } catch (Exception e) {
                            log.error(String.format("taskBeginExport gcCalendar(%s) error", gcCalendar.getId()), e);
                        }
                    }
                }
            } catch (Exception e) {
                log.error(String.format("taskBeginExport setting(%s) error", setting.getId()), e);
            }
        }
    }

    public String getGcRedisListKey(Integer gcCalendarId) {
        return String.format(GoogleCalendarConst.REDIS_KEY_FORMAT_STR, gcCalendarId);
    }

    public String getGcRedisListKeyInProgress(Integer gcCalendarId) {
        return String.format(GoogleCalendarConst.REDIS_KEY_FORMAT_STR_SYNCING, gcCalendarId);
    }

    public String getGcRedisErrorListKey(Integer gcCalendarId) {
        return String.format(GoogleCalendarConst.REDIS_KEY_FORMAT_STR_ERROR, gcCalendarId);
    }

    public String getGcRedisApiLimitKey(Integer gcCalendarId) {
        java.util.Calendar now = java.util.Calendar.getInstance();
        Integer month = now.get(java.util.Calendar.MONTH);
        Integer day = now.get(java.util.Calendar.DAY_OF_MONTH);
        Integer hour = now.get(java.util.Calendar.HOUR_OF_DAY);
        return String.format(GoogleCalendarConst.REDIS_KEY_FORMAT_STR_API_LIMIT, gcCalendarId, month, day, hour);
    }
}
