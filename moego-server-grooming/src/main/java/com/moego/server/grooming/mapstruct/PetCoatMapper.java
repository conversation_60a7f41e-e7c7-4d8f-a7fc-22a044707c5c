package com.moego.server.grooming.mapstruct;

import com.moego.idl.models.business_customer.v1.BusinessPetCoatTypeModel;
import com.moego.server.customer.dto.PetCoatDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PetCoatMapper {
    PetCoatMapper INSTANCE = Mappers.getMapper(PetCoatMapper.class);

    List<PetCoatDTO> modelToDto(List<BusinessPetCoatTypeModel> modelList);
}
