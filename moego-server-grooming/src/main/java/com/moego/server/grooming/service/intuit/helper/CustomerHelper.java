package com.moego.server.grooming.service.intuit.helper;

import com.intuit.ipp.data.Customer;
import com.intuit.ipp.data.EmailAddress;
import com.intuit.ipp.data.PhysicalAddress;
import com.intuit.ipp.data.ReferenceType;
import com.intuit.ipp.data.TelephoneNumber;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerQbQueryDto;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class CustomerHelper {

    public static Customer getCustomerWithMandatoryFields() {
        Customer customer = new Customer();
        // Mandatory Fields
        String firstName = "brebe";
        String lastName = "eytjrj";
        customer.setDisplayName(firstName + " " + lastName);
        customer.setGivenName(firstName);
        customer.setFamilyName(lastName);
        return customer;
    }

    public static Customer getCustomerWithId(String qbCustomerId) {
        Customer customer = new Customer();
        customer.setId(qbCustomerId);
        return customer;
    }

    public static ReferenceType getCustomerRef(Customer customer) {
        if (customer == null) {
            return null;
        }
        ReferenceType customerRef = new ReferenceType();
        customerRef.setName(customer.getDisplayName());
        customerRef.setValue(customer.getId());
        return customerRef;
    }

    public static List<Customer> convertDtoToModel(List<CustomerQbQueryDto> qbQueryDtoList) {
        List<Customer> customerList = new ArrayList<>();
        for (CustomerQbQueryDto customerDto : qbQueryDtoList) {
            customerList.add(convertDtoToModel(customerDto));
        }
        return customerList;
    }

    public static Customer convertDtoToModel(CustomerQbQueryDto customerDto, String expandName) {
        Customer customer = new Customer();
        // name
        String displayName = "";
        if (!StringUtils.isEmpty(customerDto.getFirstName())) {
            customer.setGivenName(customerDto.getFirstName());
            displayName = customerDto.getFirstName();
        }
        if (!StringUtils.isEmpty(customerDto.getLastName())) {
            customer.setFamilyName(customerDto.getLastName());
            displayName = displayName + " " + customerDto.getLastName();
        }
        if (!StringUtils.isEmpty(expandName)) {
            displayName = String.format("%s %s", displayName, expandName);
        }
        //        displayName = "Park and Bark Tel: 800 - 336 7196";
        customer.setDisplayName(replaceInvalidString(displayName));
        //        customer.setDisplayName((displayName)+"\r");
        // phone number
        if (!StringUtils.isEmpty(customerDto.getOwnerPhoneNumber())) {
            TelephoneNumber primaryNum = new TelephoneNumber();
            primaryNum.setFreeFormNumber(customerDto.getOwnerPhoneNumber());
            primaryNum.setDefault(true);
            customer.setPrimaryPhone(primaryNum);
        }
        // email
        if (!StringUtils.isEmpty(customerDto.getOwnerEmail())) {
            EmailAddress emailAddr = new EmailAddress();
            emailAddr.setAddress(customerDto.getOwnerEmail());
            customer.setPrimaryEmailAddr(emailAddr);
        }
        // address
        if (customerDto.getPrimaryAddress() != null) {
            PhysicalAddress ownerAddress = new PhysicalAddress();
            CustomerAddressDto primaryAddress = customerDto.getPrimaryAddress();
            ownerAddress.setLine1(primaryAddress.getAddress1());
            ownerAddress.setLine2(primaryAddress.getAddress2());
            ownerAddress.setCity(primaryAddress.getCity());
            ownerAddress.setCountry(primaryAddress.getCountry());
            ownerAddress.setLat(primaryAddress.getLat());
            ownerAddress.setLong(primaryAddress.getLng());
            ownerAddress.setCountrySubDivisionCode(primaryAddress.getState());
            ownerAddress.setPostalCode(primaryAddress.getZipcode());
            customer.setBillAddr(ownerAddress);
        }
        // MOE-1053
        //        if (!CollectionUtils.isEmpty(customerDto.getNoteList())) {
        //            Collections.reverse(customerDto.getNoteList());
        //            StringBuilder builder = new StringBuilder();
        //            for (String note : customerDto.getNoteList()) {
        //                builder.append(note + "\n");
        //            }
        //            customer.setNotes(builder.toString());
        //        }
        return customer;
    }

    public static Customer convertDtoToModel(CustomerQbQueryDto customerDto) {
        return convertDtoToModel(customerDto, StringUtils.EMPTY);
    }

    public static String replaceInvalidString(String str) {
        // 只允许字母、数字和空格通过，移除其他所有字符
        return str.replaceAll("[^a-zA-Z0-9 ]", "");
    }
}
