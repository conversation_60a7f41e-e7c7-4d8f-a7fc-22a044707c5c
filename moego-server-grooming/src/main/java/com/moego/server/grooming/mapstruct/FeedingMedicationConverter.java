package com.moego.server.grooming.mapstruct;

import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.server.grooming.dto.PetFeedingDTO;
import com.moego.server.grooming.dto.PetMedicationDTO;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface FeedingMedicationConverter {
    FeedingMedicationConverter INSTANCE = Mappers.getMapper(FeedingMedicationConverter.class);

    default List<AppointmentPetFeedingScheduleDef> toPetFeedingScheduleDefList(List<PetFeedingDTO> petFeedingDTOList) {
        if (petFeedingDTOList == null) {
            return null;
        }
        return petFeedingDTOList.stream().map(this::toPetFeedingScheduleDef).toList();
    }

    default List<AppointmentPetMedicationScheduleDef> toPetMedicationScheduleDefList(
            List<PetMedicationDTO> petMedicationDTOList) {
        if (petMedicationDTOList == null) {
            return null;
        }
        return petMedicationDTOList.stream()
                .map(this::toPetMedicationScheduleDef)
                .toList();
    }

    default AppointmentPetFeedingScheduleDef toPetFeedingScheduleDef(PetFeedingDTO petFeedingDTO) {
        if (petFeedingDTO == null) {
            return null;
        }
        return AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingType(petFeedingDTO.getFeedingType())
                .setFeedingUnit(petFeedingDTO.getFeedingUnit())
                .setFeedingNote(petFeedingDTO.getFeedingNote())
                .setFeedingInstruction(petFeedingDTO.getFeedingInstruction())
                .setFeedingSource(petFeedingDTO.getFeedingSource())
                .setFeedingAmount(petFeedingDTO.getFeedingAmount())
                .addAllFeedingTimes(petFeedingDTO.getFeedingSchedules().stream()
                        .map(schedule -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(schedule.getScheduleTime())
                                .putAllExtraJson(schedule.getExtraJson())
                                .build())
                        .toList())
                .build();
    }

    default AppointmentPetMedicationScheduleDef toPetMedicationScheduleDef(PetMedicationDTO petMedicationDTO) {
        if (petMedicationDTO == null) {
            return null;
        }
        AppointmentPetMedicationScheduleDef.Builder builder = AppointmentPetMedicationScheduleDef.newBuilder();

        builder.setMedicationName(petMedicationDTO.getMedicationName());
        builder.setMedicationUnit(petMedicationDTO.getMedicationUnit());
        builder.setMedicationNote(petMedicationDTO.getMedicationNote());
        builder.setMedicationAmount(petMedicationDTO.getMedicationAmount());

        builder.addAllMedicationTimes(petMedicationDTO.getMedicationSchedules().stream()
                        .map(schedule -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(schedule.getScheduleTime())
                                .putAllExtraJson(schedule.getExtraJson())
                                .build())
                        .toList())
                .build();

        if (Objects.nonNull(petMedicationDTO.getSelectedDate())) {
            PetMedicationDTO.SelectedDate selectedDate = petMedicationDTO.getSelectedDate();
            AppointmentPetMedicationScheduleDef.SelectedDateDef.Builder selectedDateBuilder =
                    AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder();
            if (Objects.nonNull(selectedDate.getDateType())) {
                selectedDateBuilder.setDateTypeValue(selectedDate.getDateType());
            }
            if (Objects.nonNull(selectedDate.getSpecificDates())) {
                selectedDateBuilder.addAllSpecificDates(selectedDate.getSpecificDates());
            }
            builder.setSelectedDate(selectedDateBuilder.build());
        }

        return builder.build();
    }
}
