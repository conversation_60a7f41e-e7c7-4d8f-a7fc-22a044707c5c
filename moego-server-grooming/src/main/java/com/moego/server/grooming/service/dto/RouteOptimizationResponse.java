package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class RouteOptimizationResponse {

    Integer wayPointCount;

    Integer optimizedDrivingMinutes;

    BigDecimal optimizedDrivingMiles;

    Integer savedDrivingMinutes;

    BigDecimal savedDrivingMiles;

    Byte unitOfDistanceType;

    List<RouteOptimizationDetail> detailList;

    List<String> optimizedPolyline;

    List<String> unoptimizedPolyline;
}
