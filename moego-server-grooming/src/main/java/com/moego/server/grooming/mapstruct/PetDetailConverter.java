package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.AppointmentDetailClientPortalDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.PetDetailDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/10/18
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PetDetailConverter {
    PetDetailConverter INSTANCE = Mappers.getMapper(PetDetailConverter.class);

    @Mapping(target = "taxRate", ignore = true)
    @Mapping(target = "taxId", ignore = true)
    @Mapping(target = "serviceName", ignore = true)
    @Mapping(target = "serviceDescription", ignore = true)
    PetDetailDTO entityToDTO(MoeGroomingPetDetail entity);

    AppointmentDetailClientPortalDTO.PetDetailClientPortalDTO entityToDto(MoeGroomingPetDetail entity);

    List<AppointmentDetailClientPortalDTO.PetDetailClientPortalDTO> entityToDto(List<MoeGroomingPetDetail> entities);

    MoeGroomingPetDetail toMoeGroomingPetDetail(ReportWebApptPetDetail petDetail);

    List<MoeGroomingPetDetail> toMoeGroomingPetDetails(List<ReportWebApptPetDetail> petDetails);

    GroomingPetDetailDTO entityToGroomingPetDetailDTO(MoeGroomingPetDetail petDetail);

    List<GroomingPetDetailDTO> entityToGroomingPetDetailDTOs(List<MoeGroomingPetDetail> petDetails);

    MoeGroomingPetDetail groomingPetDetailDTOToEntity(GroomingPetDetailDTO petDetailDTO);

    List<MoeGroomingPetDetail> groomingPetDetailDTOToEntities(List<GroomingPetDetailDTO> petDetailDTOs);

    GroomingPetDetailDTO reportBeanToGroomingPetDetailDTO(ReportWebApptPetDetail petDetail);

    List<GroomingPetDetailDTO> reportBeansToGroomingPetDetailDTOs(List<ReportWebApptPetDetail> petDetails);
}
