package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.ob.BookOnlinePaymentGroupSettingDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentSettingDTO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.params.ob.BookOnlinePaymentGroupParams;
import com.moego.server.grooming.params.ob.BusinessBookOnlinePaymentParams;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ObPaymentSettingMapper {
    ObPaymentSettingMapper INSTANCE = Mappers.getMapper(ObPaymentSettingMapper.class);

    @Mappings({
        @Mapping(target = "paymentType", source = "enableNoShowFee"),
    })
    BookOnlinePaymentSettingDTO toMainSetting(MoeBusinessBookOnline ob);

    @Mappings({
        @Mapping(target = "paymentType", source = "groupPaymentType"),
        @Mapping(target = "prepayType", source = "groupPrepayType"),
        @Mapping(target = "prepayTipEnable", source = "groupPrepayTipEnable"),
        @Mapping(target = "depositType", source = "groupDepositType"),
        @Mapping(target = "depositPercentage", source = "groupDepositPercentage"),
        @Mapping(target = "depositAmount", source = "groupDepositAmount"),
        @Mapping(target = "preAuthTipEnable", source = "groupPreAuthTipEnable"),
        @Mapping(target = "prepayPolicy", source = "groupPrepayPolicy"),
        @Mapping(target = "preAuthPolicy", source = "groupPreAuthPolicy"),
        @Mapping(target = "cancellationPolicy", source = "groupCancellationPolicy"),
        @Mapping(target = "acceptClient", source = "groupAcceptClient"),
        @Mapping(target = "acceptRule", source = "groupFilterRule"),
    })
    BookOnlinePaymentGroupSettingDTO toCertainGroupSetting(MoeBusinessBookOnline ob);

    @Mappings({
        @Mapping(source = "main.paymentType", target = "enableNoShowFee"),
        @Mapping(source = "main.prepayType", target = "prepayType"),
        @Mapping(source = "main.prepayTipEnable", target = "prepayTipEnable"),
        @Mapping(source = "main.depositType", target = "depositType"),
        @Mapping(source = "main.depositPercentage", target = "depositPercentage"),
        @Mapping(source = "main.depositAmount", target = "depositAmount"),
        @Mapping(source = "main.preAuthTipEnable", target = "preAuthTipEnable"),
        @Mapping(source = "main.cancellationPolicy", target = "cancellationPolicy"),
        @Mapping(source = "main.prepayPolicy", target = "prepayPolicy"),
        @Mapping(source = "main.preAuthPolicy", target = "preAuthPolicy"),
        @Mapping(target = "acceptClient", ignore = true),
        @Mapping(source = "group.paymentType", target = "groupPaymentType"),
        @Mapping(source = "group.acceptClient", target = "groupAcceptClient"),
        @Mapping(source = "group.acceptRule", target = "groupFilterRule"),
        @Mapping(source = "group.prepayType", target = "groupPrepayType"),
        @Mapping(source = "group.prepayTipEnable", target = "groupPrepayTipEnable"),
        @Mapping(source = "group.depositType", target = "groupDepositType"),
        @Mapping(source = "group.depositPercentage", target = "groupDepositPercentage"),
        @Mapping(source = "group.depositAmount", target = "groupDepositAmount"),
        @Mapping(source = "group.preAuthTipEnable", target = "groupPreAuthTipEnable"),
        @Mapping(source = "group.cancellationPolicy", target = "groupCancellationPolicy"),
        @Mapping(source = "group.prepayPolicy", target = "groupPrepayPolicy"),
        @Mapping(source = "group.preAuthPolicy", target = "groupPreAuthPolicy"),
    })
    MoeBusinessBookOnline paramsToDO(BusinessBookOnlinePaymentParams main, BookOnlinePaymentGroupParams group);
}
