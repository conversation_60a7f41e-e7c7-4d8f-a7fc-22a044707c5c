package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/10/25
 */
@Mapper
public abstract class InvoiceItemConverter {

    public static final InvoiceItemConverter INSTANCE = Mappers.getMapper(InvoiceItemConverter.class);

    public abstract MoeGroomingInvoiceItem entityToEntity(MoeGroomingInvoiceItem entity);
}
