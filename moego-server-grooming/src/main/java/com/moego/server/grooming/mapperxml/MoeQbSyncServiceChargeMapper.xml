<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeQbSyncServiceChargeMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeQbSyncServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="connect_id" jdbcType="INTEGER" property="connectId" />
    <result column="realm_id" jdbcType="VARCHAR" property="realmId" />
    <result column="qb_service_id" jdbcType="VARCHAR" property="qbServiceId" />
    <result column="service_charge_id" jdbcType="INTEGER" property="serviceChargeId" />
    <result column="service_charge_name" jdbcType="VARCHAR" property="serviceChargeName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeQbSyncServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="service_charge_description" jdbcType="LONGVARCHAR" property="serviceChargeDescription" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, connect_id, realm_id, qb_service_id, service_charge_id, service_charge_name,
    create_time, update_time, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    service_charge_description
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_qb_sync_service_charge
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_sync_service_charge
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_service_charge (business_id, connect_id, realm_id,
      qb_service_id, service_charge_id, service_charge_name,
      create_time, update_time, company_id,
      service_charge_description)
    values (#{businessId,jdbcType=INTEGER}, #{connectId,jdbcType=INTEGER}, #{realmId,jdbcType=VARCHAR},
      #{qbServiceId,jdbcType=VARCHAR}, #{serviceChargeId,jdbcType=INTEGER}, #{serviceChargeName,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT},
      #{serviceChargeDescription,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_service_charge
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="connectId != null">
        connect_id,
      </if>
      <if test="realmId != null">
        realm_id,
      </if>
      <if test="qbServiceId != null">
        qb_service_id,
      </if>
      <if test="serviceChargeId != null">
        service_charge_id,
      </if>
      <if test="serviceChargeName != null">
        service_charge_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="serviceChargeDescription != null">
        service_charge_description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="qbServiceId != null">
        #{qbServiceId,jdbcType=VARCHAR},
      </if>
      <if test="serviceChargeId != null">
        #{serviceChargeId,jdbcType=INTEGER},
      </if>
      <if test="serviceChargeName != null">
        #{serviceChargeName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceChargeDescription != null">
        #{serviceChargeDescription,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_service_charge
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        connect_id = #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        realm_id = #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="qbServiceId != null">
        qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      </if>
      <if test="serviceChargeId != null">
        service_charge_id = #{serviceChargeId,jdbcType=INTEGER},
      </if>
      <if test="serviceChargeName != null">
        service_charge_name = #{serviceChargeName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceChargeDescription != null">
        service_charge_description = #{serviceChargeDescription,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_service_charge
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      service_charge_id = #{serviceChargeId,jdbcType=INTEGER},
      service_charge_name = #{serviceChargeName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      service_charge_description = #{serviceChargeDescription,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_service_charge
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
      service_charge_id = #{serviceChargeId,jdbcType=INTEGER},
      service_charge_name = #{serviceChargeName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByBusinessIdRealmIdServiceChargeIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_qb_sync_service_charge
    where business_id = #{businessId}
    and realm_id = #{realmId}
    and service_charge_id in
    <foreach close=")" collection="serviceChargeIdList" item="serviceChargeId" open="(" separator=",">
      #{serviceChargeId}
    </foreach>
  </select>

  <insert id="insertOrUpdate" keyProperty="id" useGeneratedKeys="true">
    insert into moe_qb_sync_service_charge
    (business_id, connect_id,
     realm_id, qb_service_id,
     service_charge_id, service_charge_name, service_charge_description, company_id)
    values (#{businessId,jdbcType=INTEGER}, #{connectId,jdbcType=INTEGER},
            #{realmId,jdbcType=VARCHAR}, #{qbServiceId,jdbcType=VARCHAR},
            #{serviceChargeId,jdbcType=INTEGER}, #{serviceChargeName,jdbcType=VARCHAR},
            #{serviceChargeDescription,jdbcType=LONGVARCHAR}, #{companyId,jdbcType=BIGINT})
    on duplicate key update
                       qb_service_id = #{qbServiceId,jdbcType=VARCHAR},
                       service_charge_name = #{serviceChargeName,jdbcType=VARCHAR},
                       connect_id = #{connectId,jdbcType=INTEGER},
                       service_charge_description = #{serviceChargeDescription,jdbcType=LONGVARCHAR}
  </insert>

</mapper>
