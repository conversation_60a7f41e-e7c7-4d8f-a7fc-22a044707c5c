package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.List;

public class MoeBookOnlineServiceExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public MoeBookOnlineServiceExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNull() {
            addCriterion("service_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNotNull() {
            addCriterion("service_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceIdEqualTo(Integer value) {
            addCriterion("service_id =", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotEqualTo(Integer value) {
            addCriterion("service_id <>", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThan(Integer value) {
            addCriterion("service_id >", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_id >=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThan(Integer value) {
            addCriterion("service_id <", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThanOrEqualTo(Integer value) {
            addCriterion("service_id <=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdIn(List<Integer> values) {
            addCriterion("service_id in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotIn(List<Integer> values) {
            addCriterion("service_id not in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdBetween(Integer value1, Integer value2) {
            addCriterion("service_id between", value1, value2, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("service_id not between", value1, value2, "serviceId");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceIsNull() {
            addCriterion("show_base_price is null");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceIsNotNull() {
            addCriterion("show_base_price is not null");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceEqualTo(Byte value) {
            addCriterion("show_base_price =", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceNotEqualTo(Byte value) {
            addCriterion("show_base_price <>", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceGreaterThan(Byte value) {
            addCriterion("show_base_price >", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceGreaterThanOrEqualTo(Byte value) {
            addCriterion("show_base_price >=", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceLessThan(Byte value) {
            addCriterion("show_base_price <", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceLessThanOrEqualTo(Byte value) {
            addCriterion("show_base_price <=", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceIn(List<Byte> values) {
            addCriterion("show_base_price in", values, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceNotIn(List<Byte> values) {
            addCriterion("show_base_price not in", values, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceBetween(Byte value1, Byte value2) {
            addCriterion("show_base_price between", value1, value2, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceNotBetween(Byte value1, Byte value2) {
            addCriterion("show_base_price not between", value1, value2, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableIsNull() {
            addCriterion("book_online_available is null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableIsNotNull() {
            addCriterion("book_online_available is not null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableEqualTo(Byte value) {
            addCriterion("book_online_available =", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableNotEqualTo(Byte value) {
            addCriterion("book_online_available <>", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableGreaterThan(Byte value) {
            addCriterion("book_online_available >", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableGreaterThanOrEqualTo(Byte value) {
            addCriterion("book_online_available >=", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableLessThan(Byte value) {
            addCriterion("book_online_available <", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableLessThanOrEqualTo(Byte value) {
            addCriterion("book_online_available <=", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableIn(List<Byte> values) {
            addCriterion("book_online_available in", values, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableNotIn(List<Byte> values) {
            addCriterion("book_online_available not in", values, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableBetween(Byte value1, Byte value2) {
            addCriterion("book_online_available between", value1, value2, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableNotBetween(Byte value1, Byte value2) {
            addCriterion("book_online_available not between", value1, value2, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffIsNull() {
            addCriterion("is_all_staff is null");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffIsNotNull() {
            addCriterion("is_all_staff is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffEqualTo(Byte value) {
            addCriterion("is_all_staff =", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffNotEqualTo(Byte value) {
            addCriterion("is_all_staff <>", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffGreaterThan(Byte value) {
            addCriterion("is_all_staff >", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_all_staff >=", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffLessThan(Byte value) {
            addCriterion("is_all_staff <", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffLessThanOrEqualTo(Byte value) {
            addCriterion("is_all_staff <=", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffIn(List<Byte> values) {
            addCriterion("is_all_staff in", values, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffNotIn(List<Byte> values) {
            addCriterion("is_all_staff not in", values, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffBetween(Byte value1, Byte value2) {
            addCriterion("is_all_staff between", value1, value2, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffNotBetween(Byte value1, Byte value2) {
            addCriterion("is_all_staff not between", value1, value2, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeIsNull() {
            addCriterion("allow_booking_with_other_care_type is null");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeIsNotNull() {
            addCriterion("allow_booking_with_other_care_type is not null");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeEqualTo(Boolean value) {
            addCriterion("allow_booking_with_other_care_type =", value, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeNotEqualTo(Boolean value) {
            addCriterion("allow_booking_with_other_care_type <>", value, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeGreaterThan(Boolean value) {
            addCriterion("allow_booking_with_other_care_type >", value, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allow_booking_with_other_care_type >=", value, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeLessThan(Boolean value) {
            addCriterion("allow_booking_with_other_care_type <", value, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeLessThanOrEqualTo(Boolean value) {
            addCriterion("allow_booking_with_other_care_type <=", value, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeIn(List<Boolean> values) {
            addCriterion("allow_booking_with_other_care_type in", values, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeNotIn(List<Boolean> values) {
            addCriterion("allow_booking_with_other_care_type not in", values, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_booking_with_other_care_type between", value1, value2, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }

        public Criteria andAllowBookingWithOtherCareTypeNotBetween(Boolean value1, Boolean value2) {
            addCriterion(
                    "allow_booking_with_other_care_type not between", value1, value2, "allowBookingWithOtherCareType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_service
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
