package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.quickbooks.QuickBookInvoiceDTO;
import com.moego.server.grooming.dto.quickbooks.QuickBookPaymentDTO;
import com.moego.server.grooming.mapperbean.MoeQbSetting;
import com.moego.server.grooming.mapperbean.MoeQbSyncInvoice;
import com.moego.server.grooming.mapperbean.MoeQbSyncPayment;
import com.moego.server.payment.dto.QuickBookSettingDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface QuickBookMapper {
    QuickBookMapper INSTANCE = Mappers.getMapper(QuickBookMapper.class);

    List<QuickBookSettingDTO> listBoToDto(List<MoeQbSetting> setting);

    List<QuickBookInvoiceDTO> listInvoiceToDto(List<MoeQbSyncInvoice> invoices);

    QuickBookPaymentDTO paymentToDTO(MoeQbSyncPayment payment);
}
