<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingCustomerServicesMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="pet_id" jdbcType="INTEGER" property="petId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="service_type" jdbcType="INTEGER" property="serviceType" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="service_time" jdbcType="INTEGER" property="serviceTime" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="save_type" jdbcType="TINYINT" property="saveType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="tax_id" jdbcType="INTEGER" property="taxId" />
    <result column="tax_rate" jdbcType="DOUBLE" property="taxRate" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="service_detail" jdbcType="LONGVARCHAR" property="serviceDetail" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, customer_id, create_by, pet_id, service_id, service_name, service_type,
    category_id, service_time, service_fee, save_type, status, create_time, update_time,
    tax_id, tax_rate, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    service_detail
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_grooming_customer_services
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_customer_services
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_customer_services (business_id, customer_id, create_by,
      pet_id, service_id, service_name,
      service_type, category_id, service_time,
      service_fee, save_type, status,
      create_time, update_time, tax_id,
      tax_rate, company_id, service_detail
      )
    values (#{businessId,jdbcType=INTEGER}, #{customerId,jdbcType=INTEGER}, #{createBy,jdbcType=INTEGER},
      #{petId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER}, #{serviceName,jdbcType=VARCHAR},
      #{serviceType,jdbcType=INTEGER}, #{categoryId,jdbcType=INTEGER}, #{serviceTime,jdbcType=INTEGER},
      #{serviceFee,jdbcType=DECIMAL}, #{saveType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{taxId,jdbcType=INTEGER},
      #{taxRate,jdbcType=DOUBLE}, #{companyId,jdbcType=BIGINT}, #{serviceDetail,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_customer_services
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="serviceName != null">
        service_name,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="serviceTime != null">
        service_time,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="saveType != null">
        save_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taxId != null">
        tax_id,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="serviceDetail != null">
        service_detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=INTEGER},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="serviceName != null">
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="serviceTime != null">
        #{serviceTime,jdbcType=INTEGER},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="saveType != null">
        #{saveType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="taxId != null">
        #{taxId,jdbcType=INTEGER},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DOUBLE},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceDetail != null">
        #{serviceDetail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_customer_services
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=INTEGER},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="serviceName != null">
        service_name = #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="serviceTime != null">
        service_time = #{serviceTime,jdbcType=INTEGER},
      </if>
      <if test="serviceFee != null">
        service_fee = #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="saveType != null">
        save_type = #{saveType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=INTEGER},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DOUBLE},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceDetail != null">
        service_detail = #{serviceDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_customer_services
    set business_id = #{businessId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=INTEGER},
      pet_id = #{petId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      service_name = #{serviceName,jdbcType=VARCHAR},
      service_type = #{serviceType,jdbcType=INTEGER},
      category_id = #{categoryId,jdbcType=INTEGER},
      service_time = #{serviceTime,jdbcType=INTEGER},
      service_fee = #{serviceFee,jdbcType=DECIMAL},
      save_type = #{saveType,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      tax_id = #{taxId,jdbcType=INTEGER},
      tax_rate = #{taxRate,jdbcType=DOUBLE},
      company_id = #{companyId,jdbcType=BIGINT},
      service_detail = #{serviceDetail,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_customer_services
    set business_id = #{businessId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=INTEGER},
      pet_id = #{petId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      service_name = #{serviceName,jdbcType=VARCHAR},
      service_type = #{serviceType,jdbcType=INTEGER},
      category_id = #{categoryId,jdbcType=INTEGER},
      service_time = #{serviceTime,jdbcType=INTEGER},
      service_fee = #{serviceFee,jdbcType=DECIMAL},
      save_type = #{saveType,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      tax_id = #{taxId,jdbcType=INTEGER},
      tax_rate = #{taxRate,jdbcType=DOUBLE},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="getCustomizeServices" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM moe_grooming_customer_services
        where status = 1
        and customer_id = #{customerId}
        <if test="businessId != null">
          and business_id = #{businessId}
        </if>
        <if test="companyId != null">
          and company_id = #{companyId}
        </if>
        <if test="saveType != null">
          and save_type  = #{saveType}
        </if>
        <if test="petIds != null">
            AND `pet_id` IN
            <foreach close=")" collection="petIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="serviceIds != null">
            AND `service_id` IN
            <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

  <select id="getCustomizeServicesByPetIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM moe_grooming_customer_services
    WHERE status = 1
    <if test="companyId != null">
      AND company_id = #{companyId}
    </if>
    AND `pet_id` IN
    <foreach close=")" collection="petIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    <if test="saveType != null">
      AND save_type  = #{saveType}
    </if>
    <if test="serviceIds != null">
      AND `service_id` IN
      <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

    <select id="queryCustomerServiceByProperties" resultMap="ResultMapWithBLOBs">
    select
     <include refid="Base_Column_List" />
     from moe_grooming_customer_services
     where status = 1
      <if test="customerServiceQueryDTO.businessId != null">
        and business_id = #{customerServiceQueryDTO.businessId}
      </if>
      <if test="customerServiceQueryDTO.companyId != null">
        and company_id = #{customerServiceQueryDTO.companyId}
      </if>
     <if test="customerServiceQueryDTO.customerId !=null">
       and customer_id = #{customerServiceQueryDTO.customerId}
     </if>
     <if test="customerServiceQueryDTO.petId !=null">
       and pet_id = #{customerServiceQueryDTO.petId}
     </if>
     <if test="customerServiceQueryDTO.serviceId !=null">
       and service_id = #{customerServiceQueryDTO.serviceId}
     </if>
     <if test="customerServiceQueryDTO.saveType !=null">
       and save_type = #{customerServiceQueryDTO.saveType}
     </if>
  </select>

  <select id="queryCustomizeServices" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM moe_grooming_customer_services
    where status = 1
    <if test="businessId != null">
      and business_id = #{businessId}
    </if>
    <if test="companyId != null">
      and company_id = #{companyId}
    </if>
    <if test="petIds != null">
      AND `pet_id` IN
      <foreach close=")" collection="petIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="serviceIds != null">
      AND `service_id` IN
      <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <delete id="deleteByLastServiceIdNewServiceId">
    delete from moe_grooming_customer_services
    where service_id in (#{lastServiceId},#{newServiceId})
    and business_id = #{businessId}
    and customer_id = #{customerId}
    and pet_id = #{petId}
  </delete>

  <delete id="deleteByLastServiceIdNewServiceIdByCid">
    delete from moe_grooming_customer_services
    where service_id in (#{lastServiceId},#{newServiceId})
    and company_id = #{companyId}
    and customer_id = #{customerId}
    and pet_id = #{petId}
  </delete>

  <update id="batchUpdateCategoryId">
    UPDATE moe_grooming_customer_services
    SET category_id = #{toCategoryId}
    WHERE category_id = #{fromCategoryId}
      and company_id = #{companyId};
  </update>
</mapper>
