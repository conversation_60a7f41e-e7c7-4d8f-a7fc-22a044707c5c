<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeQbSyncRefundMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeQbSyncRefund">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="connect_id" jdbcType="INTEGER" property="connectId" />
    <result column="realm_id" jdbcType="VARCHAR" property="realmId" />
    <result column="payment_id" jdbcType="INTEGER" property="paymentId" />
    <result column="refund_id" jdbcType="INTEGER" property="refundId" />
    <result column="qb_refund_id" jdbcType="VARCHAR" property="qbRefundId" />
    <result column="refund_name" jdbcType="VARCHAR" property="refundName" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeQbSyncRefund">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="refund_description" jdbcType="LONGVARCHAR" property="refundDescription" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, connect_id, realm_id, payment_id, refund_id, qb_refund_id, refund_name,
    update_time, create_time, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    refund_description
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncRefundExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_qb_sync_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncRefundExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_qb_sync_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_qb_sync_refund
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_sync_refund
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncRefundExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_sync_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncRefund">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_refund (business_id, connect_id, realm_id,
      payment_id, refund_id, qb_refund_id,
      refund_name, update_time, create_time,
      company_id, refund_description)
    values (#{businessId,jdbcType=INTEGER}, #{connectId,jdbcType=INTEGER}, #{realmId,jdbcType=VARCHAR},
      #{paymentId,jdbcType=INTEGER}, #{refundId,jdbcType=INTEGER}, #{qbRefundId,jdbcType=VARCHAR},
      #{refundName,jdbcType=VARCHAR}, #{updateTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT},
      #{companyId,jdbcType=BIGINT}, #{refundDescription,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncRefund">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_sync_refund
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="connectId != null">
        connect_id,
      </if>
      <if test="realmId != null">
        realm_id,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="refundId != null">
        refund_id,
      </if>
      <if test="qbRefundId != null">
        qb_refund_id,
      </if>
      <if test="refundName != null">
        refund_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="refundDescription != null">
        refund_description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="paymentId != null">
        #{paymentId,jdbcType=INTEGER},
      </if>
      <if test="refundId != null">
        #{refundId,jdbcType=INTEGER},
      </if>
      <if test="qbRefundId != null">
        #{qbRefundId,jdbcType=VARCHAR},
      </if>
      <if test="refundName != null">
        #{refundName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="refundDescription != null">
        #{refundDescription,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncRefundExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_qb_sync_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_refund
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.connectId != null">
        connect_id = #{record.connectId,jdbcType=INTEGER},
      </if>
      <if test="record.realmId != null">
        realm_id = #{record.realmId,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentId != null">
        payment_id = #{record.paymentId,jdbcType=INTEGER},
      </if>
      <if test="record.refundId != null">
        refund_id = #{record.refundId,jdbcType=INTEGER},
      </if>
      <if test="record.qbRefundId != null">
        qb_refund_id = #{record.qbRefundId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundName != null">
        refund_name = #{record.refundName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.refundDescription != null">
        refund_description = #{record.refundDescription,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_refund
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      connect_id = #{record.connectId,jdbcType=INTEGER},
      realm_id = #{record.realmId,jdbcType=VARCHAR},
      payment_id = #{record.paymentId,jdbcType=INTEGER},
      refund_id = #{record.refundId,jdbcType=INTEGER},
      qb_refund_id = #{record.qbRefundId,jdbcType=VARCHAR},
      refund_name = #{record.refundName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      refund_description = #{record.refundDescription,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_refund
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      connect_id = #{record.connectId,jdbcType=INTEGER},
      realm_id = #{record.realmId,jdbcType=VARCHAR},
      payment_id = #{record.paymentId,jdbcType=INTEGER},
      refund_id = #{record.refundId,jdbcType=INTEGER},
      qb_refund_id = #{record.qbRefundId,jdbcType=VARCHAR},
      refund_name = #{record.refundName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncRefund">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_refund
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        connect_id = #{connectId,jdbcType=INTEGER},
      </if>
      <if test="realmId != null">
        realm_id = #{realmId,jdbcType=VARCHAR},
      </if>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=INTEGER},
      </if>
      <if test="refundId != null">
        refund_id = #{refundId,jdbcType=INTEGER},
      </if>
      <if test="qbRefundId != null">
        qb_refund_id = #{qbRefundId,jdbcType=VARCHAR},
      </if>
      <if test="refundName != null">
        refund_name = #{refundName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="refundDescription != null">
        refund_description = #{refundDescription,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncRefund">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_refund
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      payment_id = #{paymentId,jdbcType=INTEGER},
      refund_id = #{refundId,jdbcType=INTEGER},
      qb_refund_id = #{qbRefundId,jdbcType=VARCHAR},
      refund_name = #{refundName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      refund_description = #{refundDescription,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeQbSyncRefund">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_sync_refund
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      realm_id = #{realmId,jdbcType=VARCHAR},
      payment_id = #{paymentId,jdbcType=INTEGER},
      refund_id = #{refundId,jdbcType=INTEGER},
      qb_refund_id = #{qbRefundId,jdbcType=VARCHAR},
      refund_name = #{refundName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>


  <select id="selectByBusinessIdAndPaymentId" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from moe_qb_sync_refund
    where business_id = #{businessId}
      and realm_id = #{realmId}
    and payment_id = #{paymentId}
  </select>
  <select id="selectByBusinessIdAndRefundIds"
          resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from moe_qb_sync_refund
    where business_id = #{businessId}
    and refund_id in
    <foreach collection="refundIds" item="refundId" open="(" close=")" separator=",">
      #{refundId}
    </foreach>
  </select>
</mapper>
