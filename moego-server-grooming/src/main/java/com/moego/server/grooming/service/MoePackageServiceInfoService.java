package com.moego.server.grooming.service;

import com.moego.server.grooming.mapper.MoeGroomingPackageServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageService;
import com.moego.server.grooming.params.PurchasedPackage;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MoePackageServiceInfoService {

    @Autowired
    private MoeGroomingPackageServiceMapper moeGroomingPackageServiceMapper;

    /**
     * @deprecated by <PERSON>, 发完版之后可以删除
     */
    @Deprecated(since = "2024/10/25")
    public void addMoePackageService(int groomingPackageId, List<PurchasedPackage.ServiceInfo> services) {

        // 不需要批量插入
        for (var serviceInfo : services) {

            var moeGroomingPackageService = new MoeGroomingPackageService();
            moeGroomingPackageService.setPackageId(groomingPackageId);
            moeGroomingPackageService.setServiceId(serviceInfo.getServiceId());
            moeGroomingPackageService.setTotalQuantity(serviceInfo.getQuantity());
            moeGroomingPackageService.setRemainingQuantity(serviceInfo.getQuantity());
            moeGroomingPackageService.setServiceUnitPrice(serviceInfo.getServiceUnitPrice());

            moeGroomingPackageServiceMapper.insertSelective(moeGroomingPackageService);
        }
    }
}
