<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingExchangeRateMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingExchangeRate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="timestamp" jdbcType="INTEGER" property="timestamp" />
    <result column="base" jdbcType="VARCHAR" property="base" />
    <result column="date" jdbcType="VARCHAR" property="date" />
    <result column="rates" jdbcType="VARCHAR" property="rates" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, timestamp, base, date, rates
  </sql>

    <!--获取最新汇率-->
  <select id="selectLatestRate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_exchange_rate
    <where>
        <if test="base != null">
            base = #{base}
        </if>
    </where>
    order by timestamp desc
    limit 1
  </select>

    <update id="updateById" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingExchangeRate">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update moe_grooming_exchange_rate
        <set>
            <if test="timestamp != null">
                timestamp = #{timestamp,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=VARCHAR},
            </if>
            <if test="rates != null">
                rates = #{rates,jdbcType=VARCHAR},
            </if>
        </set>
        where id=#{id}
    </update>

  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingExchangeRate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_exchange_rate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="timestamp != null">
        timestamp,
      </if>
      <if test="base != null">
        base,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="rates != null">
        rates,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="timestamp != null">
        #{timestamp,jdbcType=INTEGER},
      </if>
      <if test="base != null">
        #{base,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=VARCHAR},
      </if>
      <if test="rates != null">
        #{rates,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
