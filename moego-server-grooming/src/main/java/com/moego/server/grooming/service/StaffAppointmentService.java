package com.moego.server.grooming.service;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.utils.DateUtil;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StaffAppointmentService {
    private final AppointmentMapperProxy moeGroomingAppointmentMapper;
    private final PetDetailMapperProxy moeGroomingPetDetailMapper;
    private final MoeGroomingServiceOperationMapper moeGroomingServiceOperationMapper;

    public Set<Integer> getClientListAssignedAppointment(Integer businessId, String startDate, String endDate) {
        List<Integer> staffIdsAssignedAppointment = new ArrayList<>();

        // 查询 appointment
        MoeGroomingAppointmentExample moeGroomingAppointmentExample = new MoeGroomingAppointmentExample();
        moeGroomingAppointmentExample
                .createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andAppointmentDateBetween(DateUtil.getStrDateByDaysDiff(endDate, 60L), endDate)
                .andAppointmentEndDateBetween(startDate, DateUtil.getStrDateByDaysDiff(startDate, -60L))
                .andIsBlockEqualTo(GroomingAppointmentEnum.UNBLOCK)
                .andStatusIn(AppointmentStatusSet.ACTIVE_STATUS_SET.stream()
                        .map(AppointmentStatusEnum::getValue)
                        .collect(Collectors.toList()))
                .andIsDeprecateEqualTo(0)
                .andIsWaitingListEqualTo((byte) 0);
        // DONE: BD
        List<MoeGroomingAppointment> appointments =
                moeGroomingAppointmentMapper.selectByExample(moeGroomingAppointmentExample);
        List<Integer> appointmentIds =
                appointments.stream().map(MoeGroomingAppointment::getId).toList();
        if (appointmentIds.isEmpty()) return new HashSet<>();

        // 查询 petDetail
        List<GroomingPetDetailDTO> petDetailDTOS =
                moeGroomingPetDetailMapper.queryHasStaffPetDetailByGroomingIds(appointmentIds, startDate, endDate);
        petDetailDTOS.forEach(petDetailDTO -> {
            if (petDetailDTO.getStaffId() != null) {
                staffIdsAssignedAppointment.add(petDetailDTO.getStaffId());
            }
        });

        // 查询 operation
        List<MoeGroomingServiceOperation> operations =
                moeGroomingServiceOperationMapper.selectByBusinessIdAndGroomingIdList(businessId, appointmentIds);
        operations.forEach(operation -> {
            if (operation.getStaffId() != null) {
                staffIdsAssignedAppointment.add(operation.getStaffId());
            }
        });

        return new HashSet<>(staffIdsAssignedAppointment);
    }

    public Map<String, Set<Integer>> getStaffAssignedAppointmentsByDate(
            Integer businessId, String startDate, String endDate) {
        Map<String, Set<Integer>> staffAssignedAppointmentsByDate = new HashMap<>();
        // 查询 appointment
        MoeGroomingAppointmentExample moeGroomingAppointmentExample = new MoeGroomingAppointmentExample();
        moeGroomingAppointmentExample
                .createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andAppointmentDateBetween(DateUtil.getStrDateByDaysDiff(endDate, 60L), endDate)
                .andAppointmentEndDateBetween(startDate, DateUtil.getStrDateByDaysDiff(startDate, -60L))
                .andIsBlockEqualTo(0)
                .andStatusIn(AppointmentStatusSet.ACTIVE_STATUS_SET.stream()
                        .map(AppointmentStatusEnum::getValue)
                        .collect(Collectors.toList()));
        List<MoeGroomingAppointment> appointments =
                moeGroomingAppointmentMapper.selectByExample(moeGroomingAppointmentExample);
        if (appointments.isEmpty()) return staffAssignedAppointmentsByDate;
        List<Integer> appointmentIds =
                appointments.stream().map(MoeGroomingAppointment::getId).toList();
        Map<Integer, MoeGroomingAppointment> appointmentMap = appointments.stream()
                .collect(Collectors.toMap(MoeGroomingAppointment::getId, appointment -> appointment));

        // 查询 petDetail
        List<GroomingPetDetailDTO> petDetailDTOS =
                moeGroomingPetDetailMapper.queryHasStaffPetDetailByGroomingIds(appointmentIds, startDate, endDate);
        petDetailDTOS.forEach(petDetailDTO -> {
            staffAssignedAppointmentsByDate
                    .computeIfAbsent(
                            appointmentMap.get(petDetailDTO.getGroomingId()).getAppointmentDate(),
                            k -> new HashSet<>() {})
                    .add(petDetailDTO.getStaffId());
        });

        // 查询 operation
        List<MoeGroomingServiceOperation> operations =
                moeGroomingServiceOperationMapper.selectByBusinessIdAndGroomingIdList(businessId, appointmentIds);
        operations.forEach(operation -> {
            staffAssignedAppointmentsByDate
                    .computeIfAbsent(
                            appointmentMap.get(operation.getGroomingId()).getAppointmentDate(), k -> new HashSet<>() {})
                    .add(operation.getStaffId());
        });
        return staffAssignedAppointmentsByDate;
    }
}
