package com.moego.server.grooming.mapstruct;

import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.server.grooming.dto.LodgingUnitDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LodgingUnitConverter {
    LodgingUnitConverter INSTANCE = Mappers.getMapper(LodgingUnitConverter.class);

    LodgingUnitDTO toLodgingUnitDTO(LodgingUnitModel lodgingUnitModel);
}
