<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingPetDetailMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingPetDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="pet_id" jdbcType="INTEGER" property="petId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="service_type" jdbcType="INTEGER" property="serviceType" />
    <result column="service_time" jdbcType="INTEGER" property="serviceTime" />
    <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="scope_type_price" jdbcType="INTEGER" property="scopeTypePrice" />
    <result column="scope_type_time" jdbcType="INTEGER" property="scopeTypeTime" />
    <result column="star_staff_id" jdbcType="INTEGER" property="starStaffId" />
    <result column="package_service_id" jdbcType="INTEGER" property="packageServiceId" />
    <result column="enable_operation" jdbcType="BIT" property="enableOperation" />
    <result column="work_mode" jdbcType="INTEGER" property="workMode" />
    <result column="service_color_code" jdbcType="VARCHAR" property="serviceColorCode" />
    <result column="start_date" jdbcType="VARCHAR" property="startDate" />
    <result column="end_date" jdbcType="VARCHAR" property="endDate" />
    <result column="service_item_type" jdbcType="INTEGER" property="serviceItemType" />
    <result column="lodging_id" jdbcType="BIGINT" property="lodgingId" />
    <result column="price_unit" jdbcType="INTEGER" property="priceUnit" />
    <result column="specific_dates" jdbcType="VARCHAR" property="specificDates" />
    <result column="associated_service_id" jdbcType="BIGINT" property="associatedServiceId" />
    <result column="price_override_type" jdbcType="TINYINT" property="priceOverrideType" typeHandler="com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler" />
    <result column="duration_override_type" jdbcType="TINYINT" property="durationOverrideType" typeHandler="com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="quantity_per_day" jdbcType="INTEGER" property="quantityPerDay" />
    <result column="date_type" jdbcType="INTEGER" property="dateType" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="order_line_item_id" jdbcType="BIGINT" property="orderLineItemId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.priceOverrideTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.durationOverrideTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.priceOverrideTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.durationOverrideTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, grooming_id, pet_id, staff_id, service_id, service_type, service_time, service_price,
    start_time, end_time, status, update_time, scope_type_price, scope_type_time, star_staff_id,
    package_service_id, enable_operation, work_mode, service_color_code, start_date,
    end_date, service_item_type, lodging_id, price_unit, specific_dates, associated_service_id,
    price_override_type, duration_override_type, created_at, updated_at, quantity_per_day,
    date_type, total_price, quantity, order_line_item_id
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_pet_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_pet_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_pet_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_pet_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPetDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_pet_detail (grooming_id, pet_id, staff_id,
      service_id, service_type, service_time,
      service_price, start_time, end_time,
      status, update_time, scope_type_price,
      scope_type_time, star_staff_id, package_service_id,
      enable_operation, work_mode, service_color_code,
      start_date, end_date, service_item_type,
      lodging_id, price_unit, specific_dates,
      associated_service_id, price_override_type,
      duration_override_type,
      created_at, updated_at, quantity_per_day,
      date_type, total_price, quantity,
      order_line_item_id)
    values (#{groomingId,jdbcType=INTEGER}, #{petId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER},
      #{serviceId,jdbcType=INTEGER}, #{serviceType,jdbcType=INTEGER}, #{serviceTime,jdbcType=INTEGER},
      #{servicePrice,jdbcType=DECIMAL}, #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT},
      #{status,jdbcType=TINYINT}, #{updateTime,jdbcType=BIGINT}, #{scopeTypePrice,jdbcType=INTEGER},
      #{scopeTypeTime,jdbcType=INTEGER}, #{starStaffId,jdbcType=INTEGER}, #{packageServiceId,jdbcType=INTEGER},
      #{enableOperation,jdbcType=BIT}, #{workMode,jdbcType=INTEGER}, #{serviceColorCode,jdbcType=VARCHAR},
      #{startDate,jdbcType=VARCHAR}, #{endDate,jdbcType=VARCHAR}, #{serviceItemType,jdbcType=INTEGER},
      #{lodgingId,jdbcType=BIGINT}, #{priceUnit,jdbcType=INTEGER}, #{specificDates,jdbcType=VARCHAR},
      #{associatedServiceId,jdbcType=BIGINT}, #{priceOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      #{durationOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{quantityPerDay,jdbcType=INTEGER},
      #{dateType,jdbcType=INTEGER}, #{totalPrice,jdbcType=DECIMAL}, #{quantity,jdbcType=INTEGER},
      #{orderLineItemId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPetDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_pet_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="serviceTime != null">
        service_time,
      </if>
      <if test="servicePrice != null">
        service_price,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="scopeTypePrice != null">
        scope_type_price,
      </if>
      <if test="scopeTypeTime != null">
        scope_type_time,
      </if>
      <if test="starStaffId != null">
        star_staff_id,
      </if>
      <if test="packageServiceId != null">
        package_service_id,
      </if>
      <if test="enableOperation != null">
        enable_operation,
      </if>
      <if test="workMode != null">
        work_mode,
      </if>
      <if test="serviceColorCode != null">
        service_color_code,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="serviceItemType != null">
        service_item_type,
      </if>
      <if test="lodgingId != null">
        lodging_id,
      </if>
      <if test="priceUnit != null">
        price_unit,
      </if>
      <if test="specificDates != null">
        specific_dates,
      </if>
      <if test="associatedServiceId != null">
        associated_service_id,
      </if>
      <if test="priceOverrideType != null">
        price_override_type,
      </if>
      <if test="durationOverrideType != null">
        duration_override_type,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="quantityPerDay != null">
        quantity_per_day,
      </if>
      <if test="dateType != null">
        date_type,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="orderLineItemId != null">
        order_line_item_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="serviceTime != null">
        #{serviceTime,jdbcType=INTEGER},
      </if>
      <if test="servicePrice != null">
        #{servicePrice,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="scopeTypePrice != null">
        #{scopeTypePrice,jdbcType=INTEGER},
      </if>
      <if test="scopeTypeTime != null">
        #{scopeTypeTime,jdbcType=INTEGER},
      </if>
      <if test="starStaffId != null">
        #{starStaffId,jdbcType=INTEGER},
      </if>
      <if test="packageServiceId != null">
        #{packageServiceId,jdbcType=INTEGER},
      </if>
      <if test="enableOperation != null">
        #{enableOperation,jdbcType=BIT},
      </if>
      <if test="workMode != null">
        #{workMode,jdbcType=INTEGER},
      </if>
      <if test="serviceColorCode != null">
        #{serviceColorCode,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemType != null">
        #{serviceItemType,jdbcType=INTEGER},
      </if>
      <if test="lodgingId != null">
        #{lodgingId,jdbcType=BIGINT},
      </if>
      <if test="priceUnit != null">
        #{priceUnit,jdbcType=INTEGER},
      </if>
      <if test="specificDates != null">
        #{specificDates,jdbcType=VARCHAR},
      </if>
      <if test="associatedServiceId != null">
        #{associatedServiceId,jdbcType=BIGINT},
      </if>
      <if test="priceOverrideType != null">
        #{priceOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      </if>
      <if test="durationOverrideType != null">
        #{durationOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="quantityPerDay != null">
        #{quantityPerDay,jdbcType=INTEGER},
      </if>
      <if test="dateType != null">
        #{dateType,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="orderLineItemId != null">
        #{orderLineItemId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_pet_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_pet_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.groomingId != null">
        grooming_id = #{record.groomingId,jdbcType=INTEGER},
      </if>
      <if test="record.petId != null">
        pet_id = #{record.petId,jdbcType=INTEGER},
      </if>
      <if test="record.staffId != null">
        staff_id = #{record.staffId,jdbcType=INTEGER},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=INTEGER},
      </if>
      <if test="record.serviceType != null">
        service_type = #{record.serviceType,jdbcType=INTEGER},
      </if>
      <if test="record.serviceTime != null">
        service_time = #{record.serviceTime,jdbcType=INTEGER},
      </if>
      <if test="record.servicePrice != null">
        service_price = #{record.servicePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=BIGINT},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.scopeTypePrice != null">
        scope_type_price = #{record.scopeTypePrice,jdbcType=INTEGER},
      </if>
      <if test="record.scopeTypeTime != null">
        scope_type_time = #{record.scopeTypeTime,jdbcType=INTEGER},
      </if>
      <if test="record.starStaffId != null">
        star_staff_id = #{record.starStaffId,jdbcType=INTEGER},
      </if>
      <if test="record.packageServiceId != null">
        package_service_id = #{record.packageServiceId,jdbcType=INTEGER},
      </if>
      <if test="record.enableOperation != null">
        enable_operation = #{record.enableOperation,jdbcType=BIT},
      </if>
      <if test="record.workMode != null">
        work_mode = #{record.workMode,jdbcType=INTEGER},
      </if>
      <if test="record.serviceColorCode != null">
        service_color_code = #{record.serviceColorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=VARCHAR},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceItemType != null">
        service_item_type = #{record.serviceItemType,jdbcType=INTEGER},
      </if>
      <if test="record.lodgingId != null">
        lodging_id = #{record.lodgingId,jdbcType=BIGINT},
      </if>
      <if test="record.priceUnit != null">
        price_unit = #{record.priceUnit,jdbcType=INTEGER},
      </if>
      <if test="record.specificDates != null">
        specific_dates = #{record.specificDates,jdbcType=VARCHAR},
      </if>
      <if test="record.associatedServiceId != null">
        associated_service_id = #{record.associatedServiceId,jdbcType=BIGINT},
      </if>
      <if test="record.priceOverrideType != null">
        price_override_type = #{record.priceOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      </if>
      <if test="record.durationOverrideType != null">
        duration_override_type = #{record.durationOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.quantityPerDay != null">
        quantity_per_day = #{record.quantityPerDay,jdbcType=INTEGER},
      </if>
      <if test="record.dateType != null">
        date_type = #{record.dateType,jdbcType=INTEGER},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.orderLineItemId != null">
        order_line_item_id = #{record.orderLineItemId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_pet_detail
    set id = #{record.id,jdbcType=INTEGER},
      grooming_id = #{record.groomingId,jdbcType=INTEGER},
      pet_id = #{record.petId,jdbcType=INTEGER},
      staff_id = #{record.staffId,jdbcType=INTEGER},
      service_id = #{record.serviceId,jdbcType=INTEGER},
      service_type = #{record.serviceType,jdbcType=INTEGER},
      service_time = #{record.serviceTime,jdbcType=INTEGER},
      service_price = #{record.servicePrice,jdbcType=DECIMAL},
      start_time = #{record.startTime,jdbcType=BIGINT},
      end_time = #{record.endTime,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      scope_type_price = #{record.scopeTypePrice,jdbcType=INTEGER},
      scope_type_time = #{record.scopeTypeTime,jdbcType=INTEGER},
      star_staff_id = #{record.starStaffId,jdbcType=INTEGER},
      package_service_id = #{record.packageServiceId,jdbcType=INTEGER},
      enable_operation = #{record.enableOperation,jdbcType=BIT},
      work_mode = #{record.workMode,jdbcType=INTEGER},
      service_color_code = #{record.serviceColorCode,jdbcType=VARCHAR},
      start_date = #{record.startDate,jdbcType=VARCHAR},
      end_date = #{record.endDate,jdbcType=VARCHAR},
      service_item_type = #{record.serviceItemType,jdbcType=INTEGER},
      lodging_id = #{record.lodgingId,jdbcType=BIGINT},
      price_unit = #{record.priceUnit,jdbcType=INTEGER},
      specific_dates = #{record.specificDates,jdbcType=VARCHAR},
      associated_service_id = #{record.associatedServiceId,jdbcType=BIGINT},
      price_override_type = #{record.priceOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      duration_override_type = #{record.durationOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      quantity_per_day = #{record.quantityPerDay,jdbcType=INTEGER},
      date_type = #{record.dateType,jdbcType=INTEGER},
      total_price = #{record.totalPrice,jdbcType=DECIMAL},
      quantity = #{record.quantity,jdbcType=INTEGER},
      order_line_item_id = #{record.orderLineItemId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPetDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_pet_detail
    <set>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="serviceTime != null">
        service_time = #{serviceTime,jdbcType=INTEGER},
      </if>
      <if test="servicePrice != null">
        service_price = #{servicePrice,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="scopeTypePrice != null">
        scope_type_price = #{scopeTypePrice,jdbcType=INTEGER},
      </if>
      <if test="scopeTypeTime != null">
        scope_type_time = #{scopeTypeTime,jdbcType=INTEGER},
      </if>
      <if test="starStaffId != null">
        star_staff_id = #{starStaffId,jdbcType=INTEGER},
      </if>
      <if test="packageServiceId != null">
        package_service_id = #{packageServiceId,jdbcType=INTEGER},
      </if>
      <if test="enableOperation != null">
        enable_operation = #{enableOperation,jdbcType=BIT},
      </if>
      <if test="workMode != null">
        work_mode = #{workMode,jdbcType=INTEGER},
      </if>
      <if test="serviceColorCode != null">
        service_color_code = #{serviceColorCode,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemType != null">
        service_item_type = #{serviceItemType,jdbcType=INTEGER},
      </if>
      <if test="lodgingId != null">
        lodging_id = #{lodgingId,jdbcType=BIGINT},
      </if>
      <if test="priceUnit != null">
        price_unit = #{priceUnit,jdbcType=INTEGER},
      </if>
      <if test="specificDates != null">
        specific_dates = #{specificDates,jdbcType=VARCHAR},
      </if>
      <if test="associatedServiceId != null">
        associated_service_id = #{associatedServiceId,jdbcType=BIGINT},
      </if>
      <if test="priceOverrideType != null">
        price_override_type = #{priceOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      </if>
      <if test="durationOverrideType != null">
        duration_override_type = #{durationOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="quantityPerDay != null">
        quantity_per_day = #{quantityPerDay,jdbcType=INTEGER},
      </if>
      <if test="dateType != null">
        date_type = #{dateType,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="orderLineItemId != null">
        order_line_item_id = #{orderLineItemId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPetDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_pet_detail
    set grooming_id = #{groomingId,jdbcType=INTEGER},
      pet_id = #{petId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      service_type = #{serviceType,jdbcType=INTEGER},
      service_time = #{serviceTime,jdbcType=INTEGER},
      service_price = #{servicePrice,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      scope_type_price = #{scopeTypePrice,jdbcType=INTEGER},
      scope_type_time = #{scopeTypeTime,jdbcType=INTEGER},
      star_staff_id = #{starStaffId,jdbcType=INTEGER},
      package_service_id = #{packageServiceId,jdbcType=INTEGER},
      enable_operation = #{enableOperation,jdbcType=BIT},
      work_mode = #{workMode,jdbcType=INTEGER},
      service_color_code = #{serviceColorCode,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=VARCHAR},
      end_date = #{endDate,jdbcType=VARCHAR},
      service_item_type = #{serviceItemType,jdbcType=INTEGER},
      lodging_id = #{lodgingId,jdbcType=BIGINT},
      price_unit = #{priceUnit,jdbcType=INTEGER},
      specific_dates = #{specificDates,jdbcType=VARCHAR},
      associated_service_id = #{associatedServiceId,jdbcType=BIGINT},
      price_override_type = #{priceOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      duration_override_type = #{durationOverrideType,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      quantity_per_day = #{quantityPerDay,jdbcType=INTEGER},
      date_type = #{dateType,jdbcType=INTEGER},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      quantity = #{quantity,jdbcType=INTEGER},
      order_line_item_id = #{orderLineItemId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <resultMap id="GroomingStaffIdListMap" type="com.moego.server.grooming.mapper.po.GroomingStaffIdListPO">
    <result column="grooming_id" property="groomingId" />
    <collection ofType="java.lang.Integer" property="staffIdList">
      <result column="staff_id" />
    </collection>
  </resultMap>

  <resultMap id="GroomingPetDetailDTOResultMap" type="com.moego.server.grooming.dto.GroomingPetDetailDTO">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="pet_id" jdbcType="INTEGER" property="petId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="service_time" jdbcType="INTEGER" property="serviceTime" />
    <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="scope_type_price" jdbcType="BIT" property="scopeTypePrice" />
    <result column="scope_type_time" jdbcType="BIT" property="scopeTypeTime" />
    <result column="star_staff_id" property="starStaffId" />
    <result column="enable_operation" jdbcType="BIT" property="enableOperation" />
    <result column="work_mode" jdbcType="BIT" property="workMode" />
    <result column="name" property="serviceName" />
    <result column="type" property="serviceType" />
    <result column="status" property="serviceStatus" />
    <result column="color_code" property="colorCode" />
    <result column="service_item_type" property="serviceItemType" />
    <result column="lodging_id" property="lodgingId" />
    <result column="start_date" property="startDate" />
    <result column="end_date" property="endDate" />
    <result column="price_unit" property="priceUnit" />
    <result column="specific_dates" property="specificDates" />
    <result column="associated_service_id" property="associatedServiceId" />
    <result column="price_override_type" jdbcType="TINYINT" property="priceOverrideType" typeHandler="com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler" />
    <result column="duration_override_type" jdbcType="TINYINT" property="durationOverrideType" typeHandler="com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler" />
    <result column="updated_at" property="updatedAt" />
    <result column="quantity_per_day" property="quantityPerDay" />
    <result column="require_dedicated_staff" property="requireDedicatedStaff" />
    <result column="date_type" property="dateType" />
    <result column="order_line_item_id" property="orderLineItemId" />
  </resultMap>

  <resultMap id="GroomingPetServiceListInfoDTOResultMap" type="com.moego.server.grooming.dto.GroomingPetServiceListInfoDTO">
    <result column="id" property="petDetailId" />
    <result column="grooming_id" property="ticketId" />
    <result column="staff_id" property="staffId" />
    <result column="pet_id" property="petId" />
    <result column="service_type_include" property="serviceTypeInclude" />
    <result column="service_time" property="serviceTime" />
    <result column="service_id" property="serviceId" />
    <result column="start_time" property="startTime" />
    <result column="appointment_date" property="appointmentDate" />
    <result column="no_start_time" property="noStartTime" />
    <result column="customer_address_id" property="customerAddressId" />
    <result column="is_block" property="isBlock" />
    <result column="service_price" property="servicePrice" />
    <result column="service_color_code" property="serviceColorCode" />
    <result column="name" property="serviceName" />
    <result column="note" property="ticketComments" />
    <result column="alertNotes" property="alertNotes" />
    <result column="customer_id" property="customerId" />
    <result column="status" property="status" />
    <result column="book_online_status" property="bookOnlineStatus" />
    <result column="color_code" property="colorCode" />
    <result column="check_in_time" property="checkInTime" />
    <result column="check_out_time" property="checkOutTime" />
    <result column="create_time" property="createTime" />
    <result column="created_by_id" property="createdById" />
    <result column="repeat_id" property="repeatId" />
    <result column="is_paid" property="isPaid" />
    <result column="service_category_name" property="serviceCategoryName" />
    <result column="is_auto_accept" property="isAutoAccept" />
  </resultMap>

  <resultMap id="PetDetailServiceDTOResultMap" type="com.moego.server.grooming.dto.PetDetailServiceDTO">
    <result column="grooming_id" property="groomingId" />
    <result column="pet_id" property="petId" />
    <result column="service_id" property="serviceId" />
    <result column="type" property="type" />
    <result column="service_time" property="serviceTime" />
    <result column="service_price" property="servicePrice" />
  </resultMap>

  <resultMap id="PetDetailInvoiceDTOResultMap" type="com.moego.server.grooming.dto.PetDetailInvoiceDTO">
    <result column="id" property="petDetailId" />
    <result column="service_id" property="serviceId" />
    <result column="pet_id" property="petId" />
    <result column="type" property="serviceType" />
    <result column="service_item_type" property="serviceItemType" />
    <result column="service_price" property="servicePrice" />
    <result column="name" property="serviceName" />
    <result column="description" property="serviceDescription" />
    <result column="tax_id" property="taxId" />
    <result column="price_unit" property="priceUnit" />
    <result column="start_date" property="startDate" />
    <result column="end_date" property="endDate" />
    <result column="start_time" property="startTime" />
    <result column="end_time" property="endTime" />
  </resultMap>

  <resultMap id="CustomerGroomingAppointmentPetDetailDTOResultMap" type="com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO">
    <result column="id" jdbcType="INTEGER" property="petDetailId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="pet_id" jdbcType="INTEGER" property="petId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="service_time" jdbcType="INTEGER" property="serviceTime" />
    <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
    <result column="type" jdbcType="TINYINT" property="serviceType" />
    <result column="inactive" jdbcType="TINYINT" property="serviceInactive" />
    <result column="serviceIsDelete" jdbcType="TINYINT" property="serviceIsDelete" />
    <result column="serviceAvailableForBookingOnline" jdbcType="TINYINT" property="serviceAvailableForBookingOnline" />
    <result column="name" property="serviceName" />
    <result column="work_mode" property="workMode" />
    <result column="scope_type_price" property="scopeTypePrice" />
    <result column="scope_type_time" property="scopeTypeTime" />
  </resultMap>

  <resultMap id="ApptPetDetailResultMap" type="com.moego.server.grooming.service.dto.ob.OBClientApptDTO$OBClientApptPetDetailDTO">
    <result column="id" jdbcType="INTEGER" property="petDetailId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="pet_id" jdbcType="INTEGER" property="petId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="service_time" jdbcType="INTEGER" property="serviceTime" />
    <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
  </resultMap>

  <resultMap id="smartScheduleGroomingDto" type="com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO">
  </resultMap>
  <resultMap id="StaffConflictDTOResultMap" type="com.moego.server.grooming.dto.StaffConflictDTO">
    <result column="grooming_id" property="groomingId" />
    <result column="staff_id" property="staffId" />
    <result column="service_time" property="serviceTime" />
    <result column="start_time" property="startTime" />
    <result column="appointment_date" property="appointmentDate" />
    <result column="end_time" property="endTime" />
    <result column="customer_id" property="customerId" />
    <result column="service_id" property="serviceId" />
    <result column="is_block" property="isBlock" />
  </resultMap>
  <insert id="insertSelectiveBatch" keyProperty="id" useGeneratedKeys="true">
    <foreach collection="moeGroomingPetDetails" item="item" separator=";">
      insert into moe_grooming_pet_detail
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.groomingId != null">
          grooming_id,
        </if>
        <if test="item.petId != null">
          pet_id,
        </if>
        <if test="item.staffId != null">
          staff_id,
        </if>
        <if test="item.serviceId != null">
          service_id,
        </if>
        <if test="item.serviceType != null">
          service_type,
        </if>
        <if test="item.serviceTime != null">
          service_time,
        </if>
        <if test="item.servicePrice != null">
          service_price,
        </if>
        <if test="item.startTime != null">
          start_time,
        </if>
        <if test="item.status != null">
          status,
        </if>
        <if test="item.updateTime != null">
          update_time,
        </if>
        <if test="item.scopeTypePrice != null">
          scope_type_price,
        </if>
        <if test="item.scopeTypeTime != null">
          scope_type_time,
        </if>
        <if test="item.starStaffId != null">
          star_staff_id,
        </if>
        <if test="item.endTime != null">
          end_time,
        </if>
        <if test="item.enableOperation != null">
          enable_operation,
        </if>
        <if test="item.workMode != null">
          work_mode,
        </if>
        <if test="item.startDate != null">
          start_date,
        </if>
        <if test="item.endDate != null">
          end_date,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.groomingId != null">
          #{item.groomingId,jdbcType=INTEGER},
        </if>
        <if test="item.petId != null">
          #{item.petId,jdbcType=INTEGER},
        </if>
        <if test="item.staffId != null">
          #{item.staffId,jdbcType=INTEGER},
        </if>
        <if test="item.serviceId != null">
          #{item.serviceId,jdbcType=INTEGER},
        </if>
        <if test="item.serviceType != null">
          #{item.serviceType},
        </if>
        <if test="item.serviceTime != null">
          #{item.serviceTime,jdbcType=INTEGER},
        </if>
        <if test="item.servicePrice != null">
          #{item.servicePrice,jdbcType=DECIMAL},
        </if>
        <if test="item.startTime != null">
          #{item.startTime,jdbcType=BIGINT},
        </if>
        <if test="item.status != null">
          #{item.status,jdbcType=BIT},
        </if>
        <if test="item.updateTime != null">
          #{item.updateTime,jdbcType=BIGINT},
        </if>
        <if test="item.scopeTypePrice != null">
          #{item.scopeTypePrice,jdbcType=BIT},
        </if>
        <if test="item.scopeTypeTime != null">
          #{item.scopeTypeTime,jdbcType=BIT},
        </if>
        <if test="item.starStaffId != null">
          #{item.starStaffId},
        </if>
        <if test="item.endTime != null">
          #{item.endTime},
        </if>
        <if test="item.enableOperation != null">
          #{item.enableOperation},
        </if>
        <if test="item.workMode != null">
          #{item.workMode},
        </if>
        <if test="item.startDate != null">
          #{item.startDate,jdbcType=VARCHAR},
        </if>
        <if test="item.endDate != null">
          #{item.endDate,jdbcType=VARCHAR},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="deleteByAppointmentId">
    update moe_grooming_pet_detail
    set status = 3, update_time = unix_timestamp(now())
    where grooming_id = #{groomingId}
  </update>

  <update id="deletePetDetail" parameterType="int">
    update moe_grooming_pet_detail
    set status = 2, update_time = unix_timestamp(now())
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </update>

  <update id="deleteByGroomingIds" parameterType="int">
    update moe_grooming_pet_detail
    set status = 2, update_time = unix_timestamp(now())
    where grooming_id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </update>

  <select id="queryPetDetailByGroomingId" resultMap="GroomingPetDetailDTOResultMap">
    select pd.id,
           pd.grooming_id,
           pd.pet_id,
           pd.staff_id,
           pd.service_id,
           pd.service_time,
           pd.service_price,
           pd.start_time,
           pd.end_time,
           pd.scope_type_price,
           pd.star_staff_id,
           pd.scope_type_time,
           pd.enable_operation,
           pd.work_mode,
           s.name,
           s.type,
           s.status,
           pd.lodging_id,
           pd.service_item_type
    from moe_grooming_pet_detail pd
           left join moe_grooming_service s on pd.service_id = s.id
    where pd.status = 1
      and pd.service_item_type = 1
      and pd.grooming_id = #{groomingId}
    order by pd.start_time asc, pd.id asc
  </select>

  <select id="queryPetDetailByServiceItems" resultMap="GroomingPetDetailDTOResultMap">
    select pd.id,
           pd.grooming_id,
           pd.pet_id,
           pd.staff_id,
           pd.service_id,
           pd.service_time,
           pd.service_price,
           pd.start_time,
           pd.end_time,
           pd.scope_type_price,
           pd.star_staff_id,
           pd.scope_type_time,
           pd.enable_operation,
           pd.work_mode,
           s.name,
           s.type,
           s.status,
           pd.lodging_id,
           pd.service_item_type,
           pd.start_date,
           pd.end_date,
           pd.specific_dates,
           pd.duration_override_type,
           pd.price_override_type,
           pd.updated_at,
           pd.quantity_per_day,
           pd.date_type
    from moe_grooming_pet_detail pd
           left join moe_grooming_service s on pd.service_id = s.id
    where pd.status = 1
      <if test="serviceItems != null and serviceItems.size != 0">
        and pd.service_item_type in
        <foreach close=")" collection="serviceItems" item="serviceItem" open="(" separator=",">
          #{serviceItem}
        </foreach>
      </if>
      and pd.grooming_id = #{groomingId}
    order by pd.start_time, pd.id
  </select>

  <select id="queryPetDetailCountByGroomingId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_pet_detail
    where status = 1
      and grooming_id = #{groomingId}
      and service_item_type = 1
    order by staff_id asc, start_time asc
  </select>

  <select id="queryPetDetailByGroomingIds" parameterType="int" resultMap="GroomingPetDetailDTOResultMap">
    select pd.id,
           pd.grooming_id,
           pd.pet_id,
           pd.staff_id,
           pd.service_id,
           pd.service_time,
           pd.service_price,
           pd.start_time,
           pd.end_time,
           pd.scope_type_price,
           pd.star_staff_id,
           pd.scope_type_time,
           pd.enable_operation,
           s.name,
           s.type,
           s.color_code,
           pd.service_item_type,
           pd.quantity_per_day,
           pd.lodging_id
    from moe_grooming_pet_detail pd
           left join moe_grooming_service s on pd.service_id = s.id
    where pd.status = 1
      and pd.service_item_type = 1
      and pd.grooming_id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryHasStaffPetDetailByGroomingIds" resultMap="GroomingPetDetailDTOResultMap">
    select pd.id,
    pd.grooming_id,
    pd.pet_id,
    pd.staff_id,
    pd.service_id,
    pd.service_time,
    pd.service_price,
    pd.start_time,
    pd.end_time,
    pd.scope_type_price,
    pd.star_staff_id,
    pd.scope_type_time,
    pd.enable_operation,
    s.name,
    s.type,
    s.color_code,
    pd.service_item_type,
    pd.quantity_per_day,
    pd.lodging_id,
    pd.start_date,
    pd.end_date
    from moe_grooming_pet_detail pd
    left join moe_grooming_service s on pd.service_id = s.id
    where pd.status = 1
    and pd.staff_id != 0
    and start_date &lt;= #{endDate}
    and end_date &gt;= #{startDate}
    and pd.grooming_id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryAllPetDetailByGroomingIds" parameterType="int" resultMap="GroomingPetDetailDTOResultMap">
    select pd.id,
    pd.grooming_id,
    pd.pet_id,
    pd.staff_id,
    pd.service_id,
    pd.service_time,
    pd.service_price,
    pd.start_time,
    pd.end_time,
    pd.scope_type_price,
    pd.star_staff_id,
    pd.scope_type_time,
    pd.enable_operation,
    pd.start_date,
    pd.end_date,
    pd.price_unit,
    pd.specific_dates,
    pd.associated_service_id,
    pd.service_item_type,
    s.name,
    s.type,
    s.color_code,
    pd.service_item_type,
    pd.lodging_id,
    pd.price_override_type,
    pd.duration_override_type,
    pd.quantity_per_day,
    s.require_dedicated_staff,
    pd.date_type,
    pd.order_line_item_id
    from moe_grooming_pet_detail pd
    left join moe_grooming_service s on pd.service_id = s.id
    where pd.status = 1
    and pd.grooming_id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryPetDetailListForGroomingOnly" resultMap="GroomingPetServiceListInfoDTOResultMap">
    select p.grooming_id,
           p.id,
           p.staff_id,
           p.service_time,
           p.service_price,
           p.start_time,
           p.pet_id,
           p.service_id,
           a.service_type_include,
           a.status,
           a.book_online_status,
           a.color_code,
           a.customer_id,
           a.is_block,
           p.start_date as appointment_date,
           a.no_start_time,
           a.check_in_time,
           a.repeat_id,
           a.created_by_id,
           a.check_out_time,
           a.create_time,
           a.is_paid,
           a.customer_address_id,
           a.is_auto_accept,
           s.name,
           s.type          as serviceType,
           s.color_code    as service_color_code,
           n.note,
           alertNotes.note as alertNotes
    from moe_grooming_appointment a
           left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
           left join moe_grooming_service s on s.id = p.service_id
           left join moe_grooming_note n on a.id = n.grooming_id and n.type = 2 and n.is_deleted = false and n.pet_id = 0
           left join moe_grooming_note alertNotes
                     on a.id = alertNotes.grooming_id and alertNotes.type = 1 and alertNotes.is_deleted = false
    where a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      and a.appointment_date &gt;= #{petDetailQuery.startDate}
      and a.appointment_date &lt;= #{petDetailQuery.endDate}
      and a.is_deprecate = 0
      and a.business_id = #{petDetailQuery.businessId}
      and a.service_type_include = 1
    <if test="petDetailQuery.staffIdList != null and petDetailQuery.staffIdList.size != 0">
      and p.staff_id in
      <foreach close=")" collection="petDetailQuery.staffIdList" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
    </if>
    <if test="petDetailQuery.isWaitingList != null">
      and a.is_waiting_list = #{petDetailQuery.isWaitingList}
    </if>
    and p.enable_operation = 0
    and p.service_item_type = 1
    union
    select p.grooming_id,
           p.id,
           gso.staff_id    as staff_id,
           gso.duration    as service_time,
           gso.price       as service_price,
           gso.start_time  as start_time,
           p.pet_id,
           p.service_id,
           a.service_type_include,
           a.status,
           a.book_online_status,
           a.color_code,
           a.customer_id,
           a.is_block,
           p.start_date as appointment_date,
           a.no_start_time,
           a.check_in_time,
           a.repeat_id,
           a.created_by_id,
           a.check_out_time,
           a.create_time,
           a.is_paid,
           a.customer_address_id,
           a.is_auto_accept,
           s.name,
           s.type          as serviceType,
           s.color_code    as service_color_code,
           n.note,
           alertNotes.note as alertNotes
    from moe_grooming_appointment a
           left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
           left join moe_grooming_service_operation gso on p.id = gso.grooming_service_id
           left join moe_grooming_service s on s.id = p.service_id
           left join moe_grooming_note n on a.id = n.grooming_id and n.type = 2 and n.is_deleted = false and n.pet_id = 0
           left join moe_grooming_note alertNotes
                     on a.id = alertNotes.grooming_id and alertNotes.type = 1 and alertNotes.is_deleted = false
    where a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      and a.appointment_date &gt;= #{petDetailQuery.startDate}
      and a.appointment_date &lt;= #{petDetailQuery.endDate}
      and a.is_deprecate = 0
      and a.business_id = #{petDetailQuery.businessId}
      and a.service_type_include = 1
    <if test="petDetailQuery.staffIdList != null and petDetailQuery.staffIdList.size != 0">
      and gso.staff_id in
      <foreach close=")" collection="petDetailQuery.staffIdList" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
    </if>
    <if test="petDetailQuery.isWaitingList != null">
      and a.is_waiting_list = #{petDetailQuery.isWaitingList}
    </if>
    and p.enable_operation = 1
    and p.service_item_type = 1
  </select>

  <select id="queryPetDetailListIncludeHybrid" resultMap="GroomingPetServiceListInfoDTOResultMap">
    select p.grooming_id,
           p.id,
           p.staff_id,
           p.service_time,
           p.service_price,
           p.start_time,
           p.pet_id,
           p.service_id,
           a.service_type_include,
           a.status,
           a.book_online_status,
           a.color_code,
           a.customer_id,
           a.is_block,
           p.start_date as appointment_date,
           a.no_start_time,
           a.check_in_time,
           a.repeat_id,
           a.created_by_id,
           a.check_out_time,
           a.create_time,
           a.is_paid,
           a.customer_address_id,
           a.is_auto_accept,
           s.name,
           s.type          as serviceType,
           s.color_code    as service_color_code,
           n.note,
           alertNotes.note as alertNotes
    from moe_grooming_appointment a
           left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
           left join moe_grooming_service s on s.id = p.service_id
           left join moe_grooming_note n on a.id = n.grooming_id and n.type = 2 and n.is_deleted = false and n.pet_id = 0
           left join moe_grooming_note alertNotes
                     on a.id = alertNotes.grooming_id and alertNotes.type = 1 and alertNotes.is_deleted = false
    where a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      and a.appointment_date &gt;= #{petDetailQuery.startDateBegin}
      and a.appointment_date &lt;= #{petDetailQuery.startDateFinish}
    <if test="petDetailQuery.endDateBegin != null and petDetailQuery.endDateFinish != null">
      and a.appointment_end_date &gt;= #{petDetailQuery.endDateBegin}
      and a.appointment_end_date &lt;= #{petDetailQuery.endDateFinish}
    </if>
      and a.is_deprecate = 0
      and a.business_id = #{petDetailQuery.businessId}
    <if test="petDetailQuery.serviceTypeIncludes != null and petDetailQuery.serviceTypeIncludes.size != 0">
      and a.service_type_include in
      <foreach close=")" collection="petDetailQuery.serviceTypeIncludes" item="serviceTypeInclude" open="(" separator=",">
        #{serviceTypeInclude}
      </foreach>
    </if>
    <if test="petDetailQuery.staffIdList != null and petDetailQuery.staffIdList.size != 0">
      and p.staff_id in
      <foreach close=")" collection="petDetailQuery.staffIdList" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
    </if>
    <if test="petDetailQuery.isWaitingList != null">
      and a.is_waiting_list = #{petDetailQuery.isWaitingList}
    </if>
    <if test="petDetailQuery.serviceItems != null and petDetailQuery.serviceItems.size != 0">
      and p.service_item_type in
      <foreach close=")" collection="petDetailQuery.serviceItems" item="serviceItem" open="(" separator=",">
        #{serviceItem}
      </foreach>
    </if>
    and p.enable_operation = 0
    union
    select p.grooming_id,
           p.id,
           gso.staff_id    as staff_id,
           gso.duration    as service_time,
           gso.price       as service_price,
           gso.start_time  as start_time,
           p.pet_id,
           p.service_id,
           a.service_type_include,
           a.status,
           a.book_online_status,
           a.color_code,
           a.customer_id,
           a.is_block,
           p.start_date as appointment_date,
           a.no_start_time,
           a.check_in_time,
           a.repeat_id,
           a.created_by_id,
           a.check_out_time,
           a.create_time,
           a.is_paid,
           a.customer_address_id,
           a.is_auto_accept,
           s.name,
           s.type          as serviceType,
           s.color_code    as service_color_code,
           n.note,
           alertNotes.note as alertNotes
    from moe_grooming_appointment a
           left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
           left join moe_grooming_service_operation gso on p.id = gso.grooming_service_id
           left join moe_grooming_service s on s.id = p.service_id
           left join moe_grooming_note n on a.id = n.grooming_id and n.type = 2 and n.is_deleted = false and n.pet_id = 0
           left join moe_grooming_note alertNotes
                     on a.id = alertNotes.grooming_id and alertNotes.type = 1 and alertNotes.is_deleted = false
    where a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and a.appointment_date &gt;= #{petDetailQuery.startDateBegin}
    and a.appointment_date &lt;= #{petDetailQuery.startDateFinish}
    <if test="petDetailQuery.endDateBegin != null and petDetailQuery.endDateFinish != null">
      and a.appointment_end_date &gt;= #{petDetailQuery.endDateBegin}
      and a.appointment_end_date &lt;= #{petDetailQuery.endDateFinish}
    </if>
      and a.is_deprecate = 0
      and a.business_id = #{petDetailQuery.businessId}
    <if test="petDetailQuery.serviceTypeIncludes != null and petDetailQuery.serviceTypeIncludes.size != 0">
      and a.service_type_include in
      <foreach close=")" collection="petDetailQuery.serviceTypeIncludes" item="serviceTypeInclude" open="(" separator=",">
        #{serviceTypeInclude}
      </foreach>
    </if>
    <if test="petDetailQuery.staffIdList != null and petDetailQuery.staffIdList.size != 0">
      and gso.staff_id in
      <foreach close=")" collection="petDetailQuery.staffIdList" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
    </if>
    <if test="petDetailQuery.isWaitingList != null">
      and a.is_waiting_list = #{petDetailQuery.isWaitingList}
    </if>
    <if test="petDetailQuery.serviceItems != null and petDetailQuery.serviceItems.size != 0">
      and p.service_item_type in
      <foreach close=")" collection="petDetailQuery.serviceItems" item="serviceItem" open="(" separator=",">
        #{serviceItem}
      </foreach>
    </if>
    and p.enable_operation = 1
  </select>

  <select id="queryPetDetailListMonthlyForGroomingOnly" resultMap="GroomingPetServiceListInfoDTOResultMap">
    select p.grooming_id,
           p.id,
           p.staff_id,
           p.service_time,
           p.service_price,
           p.start_time,
           p.pet_id,
           p.service_id,
           p.service_item_type,
           a.status,
           a.service_type_include,
           a.book_online_status,
           a.color_code,
           a.customer_id,
           a.is_block,
           p.start_date as appointment_date,
           a.no_start_time,
           a.check_in_time,
           a.repeat_id,
           a.created_by_id,
           a.check_out_time,
           a.create_time,
           a.is_paid,
           a.customer_address_id,
           a.is_auto_accept,
           s.name,
           s.color_code as service_color_code
    from moe_grooming_appointment a
           left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
           left join moe_grooming_service s on s.id = p.service_id
    where a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      and a.is_deprecate = 0
      and a.business_id = #{petDetailMonthlyQuery.businessId}
      and a.appointment_date &gt;= #{petDetailMonthlyQuery.startDate}
      and a.appointment_date &lt;= #{petDetailMonthlyQuery.endDate}
      and a.service_type_include = 1
    <if test="petDetailMonthlyQuery.staffIdList != null and petDetailMonthlyQuery.staffIdList.size != 0">
      and p.staff_id in
      <foreach close=")" collection="petDetailMonthlyQuery.staffIdList" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
    </if>
    <if test="petDetailMonthlyQuery.isWaitingList != null">
      and a.is_waiting_list = #{petDetailMonthlyQuery.isWaitingList}
    </if>
    and p.enable_operation = 0
    and p.service_item_type = 1
    union
    select p.grooming_id,
           p.id,
           gso.staff_id   as staff_id,
           gso.duration   as service_time,
           gso.price      as service_price,
           gso.start_time as start_time,
           p.pet_id,
           p.service_id,
           p.service_item_type,
           a.service_type_include,
           a.status,
           a.book_online_status,
           a.color_code,
           a.customer_id,
           a.is_block,
           p.start_date as appointment_date,
           a.no_start_time,
           a.check_in_time,
           a.repeat_id,
           a.created_by_id,
           a.check_out_time,
           a.create_time,
           a.is_paid,
           a.customer_address_id,
           a.is_auto_accept,
           s.name,
           s.color_code   as service_color_code
    from moe_grooming_appointment a
           left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
           left join moe_grooming_service_operation gso on p.id = gso.grooming_service_id
           left join moe_grooming_service s on s.id = p.service_id
    where a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      and a.is_deprecate = 0
      and a.business_id = #{petDetailMonthlyQuery.businessId}
    and a.appointment_date &gt;= #{petDetailMonthlyQuery.startDate}
    and a.appointment_date &lt;= #{petDetailMonthlyQuery.endDate}
      and a.service_type_include = 1
    <if test="petDetailMonthlyQuery.staffIdList != null and petDetailMonthlyQuery.staffIdList.size != 0">
      and gso.staff_id in
      <foreach close=")" collection="petDetailMonthlyQuery.staffIdList" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
    </if>
    <if test="petDetailMonthlyQuery.isWaitingList != null">
      and a.is_waiting_list = #{petDetailMonthlyQuery.isWaitingList}
    </if>
    and p.enable_operation = 1
    and p.service_item_type = 1
  </select>

  <select id="queryPetDetailListMonthlyIncludeHybrid" resultMap="GroomingPetServiceListInfoDTOResultMap">
    select p.grooming_id,
           p.id,
           p.staff_id,
           p.service_time,
           p.service_price,
           p.start_time,
           p.pet_id,
           p.service_id,
           p.service_item_type,
           a.status,
           a.service_type_include,
           a.book_online_status,
           a.color_code,
           a.customer_id,
           a.is_block,
           p.start_date as appointment_date,
           a.no_start_time,
           a.check_in_time,
           a.repeat_id,
           a.created_by_id,
           a.check_out_time,
           a.create_time,
           a.is_paid,
           a.customer_address_id,
           a.is_auto_accept,
           s.name,
           s.color_code as service_color_code
    from moe_grooming_appointment a
           left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
           left join moe_grooming_service s on s.id = p.service_id
    where a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      and a.is_deprecate = 0
      and a.business_id = #{petDetailMonthlyQuery.businessId}
      and a.appointment_date &gt;= #{petDetailMonthlyQuery.startDateBegin}
      and a.appointment_date &lt;= #{petDetailMonthlyQuery.startDateFinish}
      and a.appointment_end_date &gt;= #{petDetailMonthlyQuery.endDateBegin}
      and a.appointment_end_date &lt;= #{petDetailMonthlyQuery.endDateFinish}
    <if test="petDetailMonthlyQuery.serviceTypeIncludes != null and petDetailMonthlyQuery.serviceTypeIncludes.size != 0">
      and a.service_type_include in
      <foreach close=")" collection="petDetailMonthlyQuery.serviceTypeIncludes" item="serviceTypeInclude" open="(" separator=",">
        #{serviceTypeInclude}
      </foreach>
    </if>
    <if test="petDetailMonthlyQuery.serviceItems != null and petDetailMonthlyQuery.serviceItems.size != 0">
      and p.service_item_type in
      <foreach close=")" collection="petDetailMonthlyQuery.serviceItems" item="serviceItem" open="(" separator=",">
        #{serviceItem}
      </foreach>
    </if>
    <if test="petDetailMonthlyQuery.staffIdList != null and petDetailMonthlyQuery.staffIdList.size != 0">
      and p.staff_id in
      <foreach close=")" collection="petDetailMonthlyQuery.staffIdList" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
    </if>
    <if test="petDetailMonthlyQuery.isWaitingList != null">
      and a.is_waiting_list = #{petDetailMonthlyQuery.isWaitingList}
    </if>
    and p.enable_operation = 0
    union
    select p.grooming_id,
           p.id,
           gso.staff_id   as staff_id,
           gso.duration   as service_time,
           gso.price      as service_price,
           gso.start_time as start_time,
           p.pet_id,
           p.service_id,
           p.service_item_type,
           a.service_type_include,
           a.status,
           a.book_online_status,
           a.color_code,
           a.customer_id,
           a.is_block,
           p.start_date as appointment_date,
           a.no_start_time,
           a.check_in_time,
           a.repeat_id,
           a.created_by_id,
           a.check_out_time,
           a.create_time,
           a.is_paid,
           a.customer_address_id,
           a.is_auto_accept,
           s.name,
           s.color_code   as service_color_code
    from moe_grooming_appointment a
           left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
           left join moe_grooming_service_operation gso on p.id = gso.grooming_service_id
           left join moe_grooming_service s on s.id = p.service_id
    where a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      and a.is_deprecate = 0
      and a.business_id = #{petDetailMonthlyQuery.businessId}
      and a.appointment_date &gt;= #{petDetailMonthlyQuery.startDateBegin}
      and a.appointment_date &lt;= #{petDetailMonthlyQuery.startDateFinish}
      and a.appointment_end_date &gt;= #{petDetailMonthlyQuery.endDateBegin}
      and a.appointment_end_date &lt;= #{petDetailMonthlyQuery.endDateFinish}
    <if test="petDetailMonthlyQuery.serviceTypeIncludes != null and petDetailMonthlyQuery.serviceTypeIncludes.size != 0">
      and a.service_type_include in
      <foreach close=")" collection="petDetailMonthlyQuery.serviceTypeIncludes" item="serviceTypeInclude" open="(" separator=",">
        #{serviceTypeInclude}
      </foreach>
    </if>
    <if test="petDetailMonthlyQuery.serviceItems != null and petDetailMonthlyQuery.serviceItems.size != 0">
      and p.service_item_type in
      <foreach close=")" collection="petDetailMonthlyQuery.serviceItems" item="serviceItem" open="(" separator=",">
        #{serviceItem}
      </foreach>
    </if>
    <if test="petDetailMonthlyQuery.staffIdList != null and petDetailMonthlyQuery.staffIdList.size != 0">
      and gso.staff_id in
      <foreach close=")" collection="petDetailMonthlyQuery.staffIdList" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
    </if>
    <if test="petDetailMonthlyQuery.isWaitingList != null">
      and a.is_waiting_list = #{petDetailMonthlyQuery.isWaitingList}
    </if>
    and p.enable_operation = 1
  </select>

  <select id="queryPetDetailByAppointmentDateAndStaffId" resultMap="BaseResultMap">
    select p.start_time, p.service_time, p.staff_id, p.grooming_id, p.id
    from moe_grooming_appointment a
           inner join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
           left join moe_grooming_service_operation o on p.id = o.grooming_service_id
    where a.is_waiting_list = 0
      and a.is_deprecate = 0
      and a.service_type_include = 1
      and a.is_block = 2
      and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

      and a.business_id = #{businessId}
      and a.appointment_date = #{appointmentDate}
      and (p.staff_id = #{staffId} or o.staff_id = #{staffId})
    group by p.id
  </select>

  <select id="queryPetDetailByAppointmentDatesAndStaffIds" resultMap="StaffConflictDTOResultMap">
    SELECT p.start_time,
           p.service_time,
           p.staff_id,
           p.grooming_id,
           a.appointment_date,
           (p.start_time + p.service_time) as end_time,
           p.service_id,
           a.customer_id,
           a.is_block
    FROM moe_grooming_appointment a
           inner JOIN moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
    WHERE a.is_waiting_list = 0
      and a.is_deprecate = 0
      and a.service_type_include = 1
      and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
      and p.staff_id in
      <foreach close=")" collection="staffIds" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
      and a.business_id = #{businessId}
      and  a.appointment_date in
      <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
        #{item}
      </foreach>
      <if test="exceptIds != null and exceptIds.size() &gt; 0">
        and a.id not in
        <foreach close=")" collection="exceptIds" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="exceptRepeatId != null">
        and a.repeat_id != #{exceptRepeatId}
      </if>
      order by a.appointment_date, p.start_time asc
  </select>

  <select id="queryPetDetailByAppointmentDateAndStaffIdBlock" resultMap="BaseResultMap">
    SELECT p.start_time, p.service_time, p.staff_id
    FROM moe_grooming_appointment a
           LEFT JOIN moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
    WHERE a.is_deprecate = 0
      and a.service_type_include = 1
      and a.is_block = 1
      and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

      and a.appointment_date = #{appointmentDate}
      and a.business_id = #{businessId}
      and p.staff_id = #{staffId}
  </select>

  <select id="queryPetDetailByAppointmentDatesAndStaffIdBlock" resultMap="StaffConflictDTOResultMap">
    SELECT p.start_time, p.service_time, p.staff_id, a.appointment_date
    FROM moe_grooming_appointment a
           inner JOIN moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
    WHERE a.is_deprecate = 0
      and a.service_type_include = 1
      and a.is_block = 1
      and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

      and p.staff_id = #{staffId}
      and a.business_id = #{businessId}
      and  a.appointment_date in
    <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>


  <select id="queryPetDetailServiceByGroomingId" resultMap="PetDetailServiceDTOResultMap">
    SELECT p.grooming_id,
           p.pet_id,
           p.service_id,
           p.service_time,
           p.service_price,
           s.type
    from moe_grooming_pet_detail p
           INNER JOIN moe_grooming_service s
                      on p.service_id = s.id and p.status = 1 and s.status = 1 and p.service_item_type = 1
    where p.grooming_id = #{groomingId}
    <if test="petId != null">
      and p.pet_id = #{petId}
    </if>
  </select>

  <select id="queryPetDetailInvoiceByGroomingId" resultMap="PetDetailInvoiceDTOResultMap">
    SELECT p.id,
           p.service_id,
           p.pet_id,
           s.type,
           p.service_price,
           s.`name`,
           s.description,
           p.service_item_type,
           p.price_unit,
           p.start_date,
           p.end_date,
           p.start_time,
           p.end_time,
           s.tax_id
    from moe_grooming_pet_detail p
           INNER JOIN moe_grooming_service s on p.service_id = s.id AND p.status = 1
    WHERE p.grooming_id = #{groomingId}
  </select>
  <select id="queryPetDetailInvoiceByCidBidGid" resultMap="PetDetailInvoiceDTOResultMap">
    SELECT p.id,
           p.service_id,
           p.pet_id,
           gs.type,
           p.service_price,
           gs.`name`,
           gs.description,
           p.service_item_type,
           p.price_unit,
           p.start_date,
           p.end_date,
           p.start_time,
           p.end_time,
      CASE WHEN gsl.tax_id is not NULL THEN gsl.tax_id ELSE gs.tax_id END as tax_id
    from moe_grooming_pet_detail p
         INNER JOIN moe_grooming_service gs on p.service_id = gs.id AND p.status = 1
         LEFT JOIN moe_grooming_service_location gsl
                   ON gsl.service_id = gs.id AND gsl.business_id = #{businessId} and gsl.is_deleted = 0
    WHERE p.grooming_id = #{groomingId}
    and gs.company_id = #{companyId,jdbcType=BIGINT}
  </select>


  <!-- https://moego.atlassian.net/browse/ERP-913: 只 transfer unconfirmed、confirmed 预约 -->
  <select id="queryTransferAppointment" resultType="integer">
    select distinct gpd.grooming_id
    from moe_grooming_pet_detail gpd
           left join moe_grooming_service_operation gso on gpd.id = gso.grooming_service_id
    where gpd.grooming_id in (select id
                              from moe_grooming_appointment
                              where (appointment_date &gt; #{nowDate} or
                                     (appointment_date = #{nowDate} and appointment_end_time
                                       &gt;= #{endTimes}))
                                and is_block = 2
                                and business_id = #{businessId}
                                and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
                                and is_deprecate = 0)
      and gpd.service_item_type = 1
      and (gpd.staff_id = #{sourceStaffId} or gso.staff_id = #{sourceStaffId})
      and gpd.status = 1
  </select>

  <update id="transferAppointment">
    update moe_grooming_pet_detail
    set staff_id    = #{targetStaffId},
        update_time = #{updateTime}
    where grooming_id in
    <foreach close=")" collection="groomingIdList" item="groomingId" open="(" separator=",">
      #{groomingId}
    </foreach>
    and status = 1
    and staff_id = #{sourceStaffId}
  </update>

  <select id="queryCustomerAppointmentPetDetail" resultMap="CustomerGroomingAppointmentPetDetailDTOResultMap">
    SELECT p.id,
           p.service_id,
           p.grooming_id,
           p.service_price,
           p.pet_id,
           p.service_time,
           p.staff_id,
           s.type,
           s.inactive,
           s.`name`,
           s.status                as serviceIsDelete,
           s.book_online_available as serviceAvailableForBookingOnline,
           p.work_mode,
    p.scope_type_price,
    p.scope_type_time
    from moe_grooming_pet_detail p
           LEFT JOIN moe_grooming_service s on p.service_id = s.id
    WHERE p.status = 1
      AND p.grooming_id = #{groomingId}
  </select>

  <select id="getApptPetDetail" resultMap="ApptPetDetailResultMap">
    SELECT id,
           service_id,
           grooming_id,
           pet_id,
           staff_id,
           service_price,
           service_time
    from moe_grooming_pet_detail
    WHERE status = 1
      AND grooming_id = #{groomingId}
      AND service_item_type = 1
  </select>

  <select id="selectUpcomingByCustomerId" resultMap="com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper.BaseResultMap">
    SELECT <include refid="com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper.Base_Column_List" />
    from moe_grooming_appointment
    <where>
      company_id = #{companyId}
      and is_waiting_list = 0
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_block = 2
        and (appointment_date &gt; #{appointmentDate} or
             (appointment_date = #{appointmentDate} and appointment_end_time
               &gt;= #{endTimes}))
        and customer_id = #{customerId}
    </where>
  </select>

  <select id="queryStaffIdByGroomingId" resultType="integer">
    SELECT DISTINCT staff_id
    FROM moe_grooming_pet_detail
    WHERE grooming_id = #{groomingId}
      AND STATUS = 1
      AND (service_item_type = 1 OR service_item_type = 5)
  </select>

  <select id="queryStaffIdByGroomingIds" resultMap="GroomingStaffIdListMap">
    SELECT DISTINCT grooming_id, staff_id
    FROM moe_grooming_pet_detail
    WHERE grooming_id in
    <foreach close=")" collection="groomingIds" item="groomingId" open="(" separator=",">
      #{groomingId}
    </foreach>
    AND STATUS = 1
  </select>

  <select id="selectPetDetailByGroomingIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_pet_detail
    where status = 1
      and service_item_type = 1
      and grooming_id in
    <foreach close=")" collection="groomingIdList" item="item" open="(" separator=",">
      #{item}
    </foreach>
    order by grooming_id, start_time, id
  </select>

  <select id="selectByAppointmentIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_pet_detail
    where status = 1
    and grooming_id in
    <foreach close=")" collection="appointmentIdList" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryByBusinessIdAndDateOrderByTime" resultMap="BaseResultMap">
    select a.id,
           a.grooming_id,
           a.start_time,
           (a.start_time + a.service_time) as end_time,
           a.service_id,
           a.staff_id,
           a.status
    from moe_grooming_pet_detail a
           join moe_grooming_appointment b
                on a.grooming_id = b.id
    where b.business_id = #{businessId}
      and b.service_type_include = 1
      and b.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      and b.appointment_date = #{date}
      and b.is_deprecate = 0
      and b.is_waiting_list = 0
      and a.status = 1
    order by a.start_time asc;
  </select>

  <select id="queryInProgressByBusinessIdBetweenDates" resultMap="smartScheduleGroomingDto">
    select a.id,
           a.pet_id                        as petId,
           a.grooming_id                   as groomingId,
           a.start_time                    as startTime,
           (a.start_time + a.service_time) as endTime,
           a.service_id                    as serviceId,
           a.staff_id                      as staffId,
           b.customer_id                   as customerId,
           b.appointment_date              as appointmentDate
    from moe_grooming_pet_detail a
           join moe_grooming_appointment b on a.grooming_id = b.id
    where b.business_id = #{businessId}
      and b.service_type_include = 1
      and b.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and b.appointment_date &gt;= #{startDate}
    and b.appointment_date &lt; #{endDate}
    and b.is_deprecate = 0
    and b.is_waiting_list = 0
    and a.status = 1
    and a.staff_id in
    <foreach close=")" collection="staffIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    union
    select a.id,
           a.pet_id                    as petId,
           a.grooming_id               as groomingId,
           o.start_time                as startTime,
           (o.start_time + o.duration) as endTime,
           a.service_id                as serviceId,
           o.staff_id                  as staffId,
           b.customer_id               as customerId,
           b.appointment_date          as appointmentDate
    from moe_grooming_pet_detail a
           inner join moe_grooming_appointment b on a.grooming_id = b.id
           inner join moe_grooming_service_operation o on a.id = o.grooming_service_id
    where b.business_id = #{businessId}
      and b.service_type_include = 1
      and b.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and b.appointment_date &gt;= #{startDate}
    and b.appointment_date &lt; #{endDate}
    and b.is_deprecate = 0
    and b.is_waiting_list = 0
    and a.status = 1
    and o.staff_id in
    <foreach close=")" collection="staffIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    order by appointmentDate, startTime
  </select>

  <select id="queryStaffIdListByDateRange" resultType="integer">
    SELECT staff_id
    FROM moe_grooming_pet_detail gpd
           LEFT JOIN moe_grooming_appointment ga ON ga.id = gpd.grooming_id
    WHERE ga.appointment_date &gt;= #{startDate}
      AND ga.appointment_date &lt;= #{endDate}
      AND ga.is_block = 2
      AND ga.is_deprecate = 0
      AND ga.service_type_include = 1
      AND ga.is_waiting_list = 0
      AND ga.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

      and ga.book_online_status = 0
      AND ga.business_id = #{businessId}
      AND gpd.`status` = 1
  </select>

  <select id="queryUpcomingApptsByPetId" resultMap="GroomingPetDetailDTOResultMap">
    select a.id,
           a.grooming_id,
           a.start_time,
           a.service_time,
           a.service_id,
           a.staff_id,
           a.pet_id,
           a.service_price
    from moe_grooming_pet_detail a
           join moe_grooming_appointment b
                on a.grooming_id = b.id
    where b.business_id = #{businessId}
      and b.service_type_include = 1
      and b.status = 1
      and b.is_block = 2
      and b.appointment_date &gt;= #{startDate}
      and b.is_deprecate = 0
      and b.is_waiting_list = 0
      and a.status = 1
      and a.pet_id = #{petId}
      and a.service_id = #{serviceId}
    order by a.grooming_id
  </select>
  <select id="queryUpcomingApptsByServiceId" resultType="int">
    select distinct a.grooming_id
    from moe_grooming_pet_detail a
           join moe_grooming_appointment b
                on a.grooming_id = b.id
    where b.business_id = #{businessId}
      and b.service_type_include = 1
      and b.status = 1
      and a.status = 1
      and b.is_block = 2
      and (appointment_date &gt; #{appointmentDate} or
           (appointment_date = #{appointmentDate} and appointment_end_time
             &gt;= #{endTimes}))
      and b.is_deprecate = 0
      and a.service_id = #{serviceId}
  </select>

  <select id="countApptRevenue" resultType="bigDecimal">
    SELECT SUM(service_price)
    FROM moe_grooming_pet_detail
    WHERE
      grooming_id IN
    <foreach close=")" collection="groomingIds" item="groomingId" open="(" separator=",">
      #{groomingId}
    </foreach>
    AND status = 1
    AND service_item_type = 1
  </select>

  <select id="countATVRecentlyDays" resultType="bigDecimal">
    SELECT SUM(service_price) / COUNT(DISTINCT grooming_id)
    FROM moe_grooming_appointment mga
           INNER JOIN moe_grooming_pet_detail mgpd on
      mga.id = mgpd.grooming_id
    WHERE mga.business_id = #{businessId}
      AND mga.service_type_include = 1
      AND mga.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

      AND mgpd.status = 1
      AND mga.is_deprecate = 0
      AND mga.is_block = 2
      AND mga.create_time &gt;= #{startTime}
      AND mga.create_time &lt;= #{endTime}
  </select>

  <select id="queryFinishedPetDetail" resultMap="GroomingPetServiceListInfoDTOResultMap">
    select p.grooming_id,
           p.id,
           p.staff_id,
           p.service_time,
           p.service_price,
           p.start_time,
           p.pet_id,
           p.service_id,
           a.status,
           a.customer_id,
           a.is_block,
           a.appointment_date,
           a.no_start_time,
           a.customer_id,
           a.customer_address_id,
           a.is_auto_accept,
           a.is_block,
           a.is_paid,
           s.name  as name,
           sc.name as service_category_name
    from moe_grooming_appointment a
           left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1 and a.service_type_include = 1
           left join moe_grooming_service s on s.id = p.service_id
           left join moe_grooming_service_category sc on s.category_id = sc.id
    where s.type = 1
      and a.is_deprecate = 0
      and a.is_block = 2
      and a.status = 3
      and a.is_paid = 1
      and a.appointment_date &gt;= #{startDate}
      and a.appointment_date &lt;= #{endDate}
      and a.business_id in
    <foreach close=")" collection="businessIdList" item="businessId" open="(" separator=",">
      #{businessId}
    </foreach>
  </select>
  <select id="describePetDetailReports" parameterType="com.moego.server.grooming.params.report.DescribePetDetailReportsParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_pet_detail
    where service_item_type = 1
      <if test="ids != null and ids.size()&gt;0">
        and id in
        <foreach close=")" collection="ids" item="id" open="(" separator=",">
          #{id}
        </foreach>
      </if>
      <if test="groomingIds != null and groomingIds.size()&gt;0">
        and grooming_id in
        <foreach close=")" collection="groomingIds" item="groomingId" open="(" separator=",">
          #{groomingId}
        </foreach>
      </if>
      <if test="staffIds != null and staffIds.size()&gt;0">
        and staff_id in
        <foreach close=")" collection="staffIds" item="staffId" open="(" separator=",">
          #{staffId}
        </foreach>
      </if>
      <if test="petIds != null and petIds.size()&gt;0">
        and pet_id in
        <foreach close=")" collection="petIds" item="petId" open="(" separator=",">
          #{petId}
        </foreach>
      </if>
      <if test="!includeDeleted">
        and status = 1
      </if>
    order by id
  </select>

  <select id="queryNormalPetDetailDTOByIds" resultMap="GroomingPetDetailDTOResultMap">
    select pd.id,
    pd.grooming_id,
    pd.pet_id,
    pd.staff_id,
    pd.service_id,
    pd.service_time,
    pd.service_price,
    pd.start_time,
    pd.end_time,
    pd.scope_type_price,
    pd.star_staff_id,
    pd.scope_type_time,
    pd.enable_operation,
    pd.start_date,
    pd.end_date,
    pd.price_unit,
    pd.specific_dates,
    pd.associated_service_id,
    pd.service_item_type,
    s.name,
    s.type,
    s.color_code,
    pd.service_item_type,
    pd.lodging_id,
    pd.quantity_per_day,
    pd.date_type
    from moe_grooming_pet_detail pd
    left join moe_grooming_service s on pd.service_id = s.id
    where pd.status = 1
    and pd.service_item_type = 1
    and pd.id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectNormalPetDetailByIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_pet_detail
    where status = 1
    and id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <insert id="batchInsertBlockPetDetail" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO moe_grooming_pet_detail (
        grooming_id, staff_id, service_time, start_time, update_time, end_time, start_date, end_date
    )
    VALUES
    <foreach collection="moeGroomingPetDetails" item="item" separator=",">
      (
        #{item.groomingId}, #{item.staffId}, #{item.serviceTime}, #{item.startTime}, #{item.updateTime}, #{item.endTime}, #{item.startDate}, #{item.endDate}
      )
    </foreach>
  </insert>
</mapper>
