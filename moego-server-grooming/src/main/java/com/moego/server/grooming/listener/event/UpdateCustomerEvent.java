package com.moego.server.grooming.listener.event;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * update moe_customer.moe_business_customer last_service_time
 *
 * <AUTHOR>
 * @since 2022/6/23
 */
@Setter
@Getter
@Accessors(chain = true)
public class UpdateCustomerEvent extends ApplicationEvent {

    private Integer businessId;

    private Integer customerId;

    public UpdateCustomerEvent(Object source) {
        super(source);
    }
}
