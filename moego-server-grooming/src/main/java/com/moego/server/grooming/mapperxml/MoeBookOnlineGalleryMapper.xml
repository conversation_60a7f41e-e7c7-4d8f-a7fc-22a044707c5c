<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineGalleryMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="is_star" jdbcType="TINYINT" property="isStar" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, staff_id, image_path, sort, status, is_delete, is_star, create_time, 
    update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_book_online_gallery
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_gallery
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_gallery (business_id, staff_id, image_path, 
      sort, status, is_delete, 
      is_star, create_time, update_time, 
      company_id)
    values (#{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{imagePath,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{isDelete,jdbcType=TINYINT}, 
      #{isStar,jdbcType=TINYINT}, #{createTime,jdbcType=INTEGER}, #{updateTime,jdbcType=INTEGER}, 
      #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_gallery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="imagePath != null">
        image_path,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="isStar != null">
        is_star,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="imagePath != null">
        #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isStar != null">
        #{isStar,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_gallery
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="imagePath != null">
        image_path = #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isStar != null">
        is_star = #{isStar,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineGallery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_gallery
    set business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      image_path = #{imagePath,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      is_delete = #{isDelete,jdbcType=TINYINT},
      is_star = #{isStar,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectByBusinessId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_book_online_gallery
        where business_id = #{businessId,jdbcType=INTEGER} and is_delete=0
        order by sort desc, id desc
    </select>
    <update id="batchDeleteGallery">
        update moe_book_online_gallery
        set is_delete=1, update_time= UNIX_TIMESTAMP()
        WHERE business_id=#{batchParams.businessId,jdbcType=INTEGER}
        <if test="batchParams.imageIds != null">
            AND `id` IN
            <foreach close=")" collection="batchParams.imageIds" item="id" open="(" separator=",">
                #{id}
            </foreach>
        </if>
    </update>
    <update id="batchStarGallery">
        <foreach collection="galleries" item="item" separator=";">
            update moe_book_online_gallery
            set is_star = #{item.isStar,jdbcType=TINYINT},
                sort = #{item.sort},
                update_time = UNIX_TIMESTAMP()
            WHERE id = #{item.id}
        </foreach>
    </update>
  <select id="listGalleryMaxSortImageByBusinessId" resultMap="BaseResultMap">
    select a.id, a.business_id, a.staff_id, a.image_path, a.sort, a.status, a.is_delete,
           a.is_star, a.create_time, a.update_time
    from moe_book_online_gallery a inner join
    (select business_id, max(sort) max_sort from moe_book_online_gallery
    where is_delete = 0
      and business_id in
      <foreach close=")" collection="businessIdList" item="businessId" open="(" separator=",">
        #{businessId,jdbcType=INTEGER}
      </foreach>
    group by business_id) b
        on a.business_id = b.business_id and a.sort = b.max_sort and is_delete = 0;
  </select>
</mapper>