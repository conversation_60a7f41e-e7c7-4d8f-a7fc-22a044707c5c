package com.moego.server.grooming.service.intuit.helper;

import com.intuit.ipp.data.Customer;
import com.intuit.ipp.data.GlobalTaxCalculationEnum;
import com.intuit.ipp.data.Invoice;
import com.intuit.ipp.data.Item;
import com.intuit.ipp.data.Line;
import com.intuit.ipp.data.LineDetailTypeEnum;
import com.intuit.ipp.data.ReferenceType;
import com.intuit.ipp.data.SalesItemLineDetail;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.utils.QuickBooksDateUtils;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.util.StringUtils;

public class InvoiceHelper {

    public static final String TYPE_SERVICE = "service";

    public static final String TYPE_PRODUCT = "product";

    public static final String TYPE_PACKAGE = "package";

    public static final String TYPE_SERVICE_CHARGE = "service_charge";

    public static Invoice convertDataToInvoice(
            OrderDetailModel orderDetail,
            List<OrderLineItemModel> itemList,
            Customer customer,
            Map<Integer, Item> itemServiceMap,
            Map<Integer, Item> itemProductMap,
            Map<Integer, Item> itemServiceChargeMap,
            Item noShowServiceItem) {
        return convertDataToInvoice(
                orderDetail,
                itemList,
                customer,
                itemServiceMap,
                itemProductMap,
                itemServiceChargeMap,
                Collections.emptyMap(),
                noShowServiceItem);
    }

    public static Invoice convertDataToInvoice(
            OrderDetailModel orderDetail,
            List<OrderLineItemModel> itemList,
            Customer customer,
            Map<Integer, Item> itemServiceMap,
            Map<Integer, Item> itemProductMap,
            Map<Integer, Item> itemServiceChargeMap,
            Map<Integer, Item> itemPackageMap,
            Item noShowServiceItem) {
        Invoice invoice = new Invoice();
        // Mandatory Fields
        invoice.setDocNumber(String.valueOf(orderDetail.getOrder().getId()));
        // customer
        invoice.setCustomerRef(CustomerHelper.getCustomerRef(customer));
        invoice.setBalance(BigDecimal.valueOf(orderDetail.getOrder().getRemainAmount()));
        invoice.setBillAddr(customer.getBillAddr());
        invoice.setApplyTaxAfterDiscount(false);
        if (customer.getPrimaryEmailAddr() != null) {
            invoice.setBillEmail(customer.getPrimaryEmailAddr());
        }
        List<Line> invLine = new ArrayList<>();
        List<OrderLineItemModel> mergedLineItemList = mergeLineItem(itemList);
        for (OrderLineItemModel item : mergedLineItemList) {
            Line line = new Line();
            Item serviceItem;

            if (Objects.equals(item.getObjectId(), 0L) && InvoiceStatusEnum.TYPE_NOSHOW.equals(item.getType())) {
                serviceItem = noShowServiceItem;
            } else {
                int serviceId = Math.toIntExact(item.getObjectId());
                if (TYPE_SERVICE_CHARGE.equals(item.getType())) {
                    // service charge
                    serviceItem = itemServiceChargeMap.get(serviceId);
                } else if (TYPE_PRODUCT.equals(item.getType())) {
                    // product
                    serviceItem = itemProductMap.get(serviceId);
                } else if (TYPE_PACKAGE.equals(item.getType())) {
                    // package
                    serviceItem = itemPackageMap.get(serviceId);
                } else {
                    // service
                    serviceItem = itemServiceMap.get(serviceId);
                }
            }
            if (StringUtils.hasText(serviceItem.getDescription())) {
                line.setDescription(serviceItem.getDescription());
            }
            line.setAmount(BigDecimal.valueOf(item.getSubTotalAmount()));
            line.setDetailType(LineDetailTypeEnum.SALES_ITEM_LINE_DETAIL);
            SalesItemLineDetail silDetails = new SalesItemLineDetail();
            silDetails.setItemRef(ServiceHelper.getItemRef(serviceItem));
            silDetails.setQty(new BigDecimal(item.getQuantity() - item.getPurchasedQuantity()));
            silDetails.setRatePercent(new BigDecimal(0));
            silDetails.setUnitPrice(BigDecimal.valueOf(item.getUnitPrice()));
            if (BigDecimal.valueOf(item.getTaxAmount()).compareTo(BigDecimal.ZERO) > 0) {
                ReferenceType referenceType = new ReferenceType();
                referenceType.setValue("TAX");
                silDetails.setTaxCodeRef(referenceType);
            } else {
                silDetails.setTaxCodeRef(null);
            }
            silDetails.setTaxInclusiveAmt(null);
            invoice.setGlobalTaxCalculation(GlobalTaxCalculationEnum.NOT_APPLICABLE);
            line.setSalesItemLineDetail(silDetails);
            invLine.add(line);
        }
        invoice.setLine(invLine);
        invoice.setRemitToRef(CustomerHelper.getCustomerRef(customer));
        invoice.setTotalAmt(BigDecimal.valueOf(orderDetail.getOrder().getTotalAmount()));
        invoice.setBalance(BigDecimal.valueOf(orderDetail.getOrder().getRemainAmount()));
        invoice.setFinanceCharge(false);
        return invoice;
    }

    private static List<OrderLineItemModel> mergeLineItem(List<OrderLineItemModel> itemList) {
        // 使用 Map 进行合并，Key = objectId + type + unitPrice,
        // 对于tax amount，在同步的时候会单独作为一项同步，这里key不需要包含
        Map<String, OrderLineItemModel> mergedMap = new HashMap<>();

        for (OrderLineItemModel item : itemList) {
            String key = item.getObjectId() + "_" + item.getType() + "_"
                    + BigDecimal.valueOf(item.getUnitPrice()).setScale(2, RoundingMode.HALF_UP);

            // map 中不存在的，直接加入
            if (!mergedMap.containsKey(key)) {
                mergedMap.put(key, item);
                continue;
            }
            // 如果 Map 中已经有相同 object_id 和 type 的 item，累加数量
            OrderLineItemModel existingItem = mergedMap.get(key);
            OrderLineItemModel build = OrderLineItemModel.newBuilder(existingItem)
                    .setQuantity(existingItem.getQuantity() + item.getQuantity())
                    .setPurchasedQuantity(existingItem.getPurchasedQuantity() + item.getPurchasedQuantity())
                    .setTipsAmount(BigDecimal.valueOf(existingItem.getTipsAmount())
                            .add(BigDecimal.valueOf(item.getTipsAmount()))
                            .doubleValue())
                    .setTaxAmount(BigDecimal.valueOf(existingItem.getTaxAmount())
                            .add(BigDecimal.valueOf(item.getTaxAmount()))
                            .doubleValue())
                    .setDiscountAmount(BigDecimal.valueOf(existingItem.getDiscountAmount())
                            .add(BigDecimal.valueOf(item.getDiscountAmount()))
                            .doubleValue())
                    .setExtraFeeAmount(BigDecimal.valueOf(existingItem.getExtraFeeAmount())
                            .add(BigDecimal.valueOf(item.getExtraFeeAmount()))
                            .doubleValue())
                    .setSubTotalAmount(BigDecimal.valueOf(existingItem.getSubTotalAmount())
                            .add(BigDecimal.valueOf(item.getSubTotalAmount()))
                            .doubleValue())
                    .setTotalAmount(BigDecimal.valueOf(existingItem.getTotalAmount())
                            .add(BigDecimal.valueOf(item.getTotalAmount()))
                            .doubleValue())
                    .build();
            mergedMap.put(key, build);
        }

        // 返回合并后的 List
        return new ArrayList<>(mergedMap.values());
    }

    public static void invoiceAddTxnDate(Invoice invoice, Long appointmentDateTime) {
        invoice.setTxnDate(QuickBooksDateUtils.getDateTimeByLong(appointmentDateTime));
    }

    public static void invoiceAddDueTime(Invoice invoice, Long dueTime) {
        invoice.setDueDate(QuickBooksDateUtils.getDateTimeByLong(dueTime));
    }

    public static Invoice getInvoiceWithId(String qbInvoiceId) {
        Invoice invoice = new Invoice();
        invoice.setId(qbInvoiceId);
        return invoice;
    }

    /**
     * 用于特殊line的增加
     *
     * @param invoice
     * @param item
     * @param price
     */
    public static void invoiceAddLine(Invoice invoice, Item item, BigDecimal price) {
        Line line = new Line();
        if (!StringUtils.isEmpty(item.getDescription())) {
            line.setDescription(item.getDescription());
        }
        line.setAmount(price);
        line.setDetailType(LineDetailTypeEnum.SALES_ITEM_LINE_DETAIL);
        SalesItemLineDetail silDetails = new SalesItemLineDetail();
        silDetails.setItemRef(ServiceHelper.getItemRef(item));
        silDetails.setQty(new BigDecimal(1));
        silDetails.setRatePercent(new BigDecimal(0));
        silDetails.setUnitPrice(price);
        silDetails.setTaxCodeRef(null);
        silDetails.setTaxInclusiveAmt(null);
        line.setSalesItemLineDetail(silDetails);
        invoice.getLine().add(line);
    }
}
