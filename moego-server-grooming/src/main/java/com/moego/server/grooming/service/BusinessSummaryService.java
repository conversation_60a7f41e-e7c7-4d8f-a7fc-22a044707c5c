package com.moego.server.grooming.service;

import static com.moego.common.utils.DateUtil.STANDARD_DATE_TIME;
import static com.moego.server.grooming.enums.OBMetricsEnum.recovered_records;
import static com.moego.server.grooming.enums.OBMetricsEnum.recovered_revenue;
import static com.moego.server.grooming.enums.OBMetricsTypeEnum.SUM;
import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.message.v1.MarketingCampaignsSummaryRequest;
import com.moego.idl.service.message.v1.MarketingCampaignsSummaryResponse;
import com.moego.idl.service.message.v1.MarketingEmailServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.grooming.dto.GroomingBookingDTO;
import com.moego.server.grooming.dto.GroomingCustomerPetdetailDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapper.MoeGroomingReportMapper;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.service.ob.OBMetricService;
import com.moego.server.grooming.web.params.OBMetricsParams;
import com.moego.server.grooming.web.vo.Business2023SummaryVo;
import com.moego.server.grooming.web.vo.ob.OBMetricVO;
import com.moego.server.message.client.IReviewBoosterClient;
import com.moego.server.message.dto.ReviewBoosterSummaryDTO;
import com.moego.server.message.params.ReviewBoosterSummaryParams;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.dto.MoeGoPayTransactionSummaryDto;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class BusinessSummaryService {
    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private MoeAppointmentQueryService moeAppointmentQueryService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OBMetricService metricService;

    @Autowired
    private MoeGroomingReportMapper moeGroomingReportMapper;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;

    @Autowired
    private IReviewBoosterClient reviewBoosterClient;

    @Autowired
    private IPaymentPaymentClient paymentPaymentClient;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private IPetClient iPetClient;

    @Autowired
    private MarketingEmailServiceGrpc.MarketingEmailServiceBlockingStub marketingEmailClient;

    public Business2023SummaryVo getBusiness2023Summary(Integer businessId, Integer staffId) {
        String startDate = "2023-01-01";
        String endDate = "2023-12-31";
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(businessId));
        if (businessInfo == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business not found: " + businessId);
        }
        String tz = businessInfo.getTimezoneName();
        if (!StringUtils.hasText(tz)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "business '" + businessId + "' has empty timezone");
        }

        List<Integer> businessAppointmentIds =
                moeGroomingAppointmentMapper.getNotCanceledAppointmentIds(businessId, null, startDate, endDate);
        List<GroomingBookingDTO> groomingBookingDTOS =
                moeAppointmentQueryService.queryApptWithPetDetailByIds(businessAppointmentIds);
        ReviewBoosterSummaryParams params = new ReviewBoosterSummaryParams();
        params.setBusinessId(businessId);
        params.setStaffId(staffId);
        params.setStartDate(DateUtil.parseDateWithTimeZone(startDate, tz, STANDARD_DATE_TIME));
        params.setEndDate(DateUtil.parseDateWithTimeZone(endDate, tz, STANDARD_DATE_TIME));
        ReviewBoosterSummaryDTO reviewBoosterSummaryDTO = reviewBoosterClient.reviewBoosterSummary(params);

        Business2023SummaryVo result = new Business2023SummaryVo();
        result.setStaff2023Summary(get2023StaffSummary(
                businessId, staffId, groomingBookingDTOS, reviewBoosterSummaryDTO.getStaffSummary()));
        result.setBusinessSummary(
                get2023BusinessSummary(businessId, groomingBookingDTOS, reviewBoosterSummaryDTO.getBusinessSummary()));
        result.setOwnerSummary(get2023OwnerSummary(businessInfo, staffId, startDate, endDate, groomingBookingDTOS));
        return result;
    }

    Business2023SummaryVo.Staff2023Summary get2023StaffSummary(
            Integer businessId,
            Integer staffId,
            List<GroomingBookingDTO> groomingBookingDTOS,
            ReviewBoosterSummaryDTO.Summary reviewBoosterSummaryDTO) {
        // 过滤不包含该职工的 appointment
        groomingBookingDTOS = groomingBookingDTOS.stream()
                .filter(k -> !k.getNoStartTime())
                .filter(k ->
                        k.getPetList().stream().anyMatch(pet -> pet.getStaffId().equals(staffId)))
                .toList();
        if (CollectionUtils.isEmpty(groomingBookingDTOS)) {
            return null;
        }
        Business2023SummaryVo.Staff2023Summary result = new Business2023SummaryVo.Staff2023Summary();

        Integer serviceDuration = groomingBookingDTOS.stream()
                .map(GroomingBookingDTO::getPetList)
                .flatMap(Collection::stream)
                .filter(k -> staffId.equals(k.getStaffId()))
                .mapToInt(GroomingCustomerPetdetailDTO::getServiceTime)
                .sum();
        result.setTotalServiceTimeInMin(serviceDuration);
        result.setLongestWorkingDay(getLongestWorkingDay(groomingBookingDTOS, staffId));
        result.setTakeCaredPetCnt(getTakeCaredPet(groomingBookingDTOS, staffId));

        List<GroomingCustomerPetdetailDTO> staffPetDetailDTOS = groomingBookingDTOS.stream()
                .map(GroomingBookingDTO::getPetList)
                .flatMap(Collection::stream)
                .filter(k -> staffId.equals(k.getStaffId()))
                .toList();
        result.setMostPetServices(getMostPetService(businessId, staffPetDetailDTOS));

        List<Integer> petIds = staffPetDetailDTOS.stream()
                .map(GroomingCustomerPetdetailDTO::getPetId)
                .distinct()
                .toList();
        List<CustomerPetDetailDTO> petDetails = iPetClient.getCustomerPetListByIdList(petIds);
        result.setMostPetBreeds(getMostPetBreed(petDetails));
        result.setMostPopularPetName(getMostPopularPetName(petDetails));

        Business2023SummaryVo.Review2023Summary reviewSummary = new Business2023SummaryVo.Review2023Summary();
        reviewSummary.setReviewCnt(reviewBoosterSummaryDTO.getReviewTotal());
        reviewSummary.setAvgScore(reviewBoosterSummaryDTO
                .getAvgScore()
                .setScale(1, RoundingMode.HALF_UP)
                .toString());
        result.setReviewSummary(reviewSummary);

        return result;
    }

    Business2023SummaryVo.Business2023Summary get2023BusinessSummary(
            Integer businessId,
            List<GroomingBookingDTO> groomingBookingDTOS,
            ReviewBoosterSummaryDTO.Summary reviewBoosterSummaryDTO) {
        groomingBookingDTOS =
                groomingBookingDTOS.stream().filter(k -> !k.getNoStartTime()).toList();
        if (CollectionUtils.isEmpty(groomingBookingDTOS)) {
            return null;
        }
        Business2023SummaryVo.Business2023Summary result = new Business2023SummaryVo.Business2023Summary();

        Integer serviceDuration = groomingBookingDTOS.stream()
                .map(GroomingBookingDTO::getPetList)
                .flatMap(Collection::stream)
                .mapToInt(GroomingCustomerPetdetailDTO::getServiceTime)
                .sum();
        result.setTotalServiceTimeInMin(serviceDuration);
        result.setLongestWorkingDay(getLongestWorkingDay(groomingBookingDTOS, null));
        result.setTakeCaredPetCnt(getTakeCaredPet(groomingBookingDTOS, null));

        List<GroomingCustomerPetdetailDTO> petDetailDTOS = groomingBookingDTOS.stream()
                .map(GroomingBookingDTO::getPetList)
                .flatMap(Collection::stream)
                .toList();
        result.setMostPetServices(getMostPetService(businessId, petDetailDTOS));
        List<Integer> petIds = petDetailDTOS.stream()
                .map(GroomingCustomerPetdetailDTO::getPetId)
                .distinct()
                .toList();
        List<CustomerPetDetailDTO> petDetails = iPetClient.getCustomerPetListByIdList(petIds);
        result.setMostPetBreeds(getMostPetBreed(petDetails));
        result.setMostPopularPetName(getMostPopularPetName(petDetails));

        Business2023SummaryVo.Review2023Summary reviewSummary = new Business2023SummaryVo.Review2023Summary();
        reviewSummary.setReviewCnt(reviewBoosterSummaryDTO.getReviewTotal());
        reviewSummary.setAvgScore(reviewBoosterSummaryDTO
                .getAvgScore()
                .setScale(1, RoundingMode.HALF_UP)
                .toString());
        result.setReviewSummary(reviewSummary);
        return result;
    }

    Business2023SummaryVo.Owner2023Summary get2023OwnerSummary(
            MoeBusinessDto businessInfo,
            Integer staffId,
            String startDate,
            String endDate,
            List<GroomingBookingDTO> groomingBookingDTOS) {
        if (!Objects.equals(staffId, iBusinessStaffClient.getOwnerStaffId(businessInfo.getId()))) {
            return null;
        }
        if (CollectionUtils.isEmpty(groomingBookingDTOS)) {
            return null;
        }
        List<Integer> appointmentIds = groomingBookingDTOS.stream()
                .map(GroomingBookingDTO::getGroomingId)
                .toList();

        Business2023SummaryVo.Owner2023Summary result = new Business2023SummaryVo.Owner2023Summary();

        String tz = businessInfo.getTimezoneName();
        long startAt = DateUtil.timestamp(startDate, tz) / 1000;
        long endAt = (DateUtil.timestamp(endDate, tz) + 60 * 60 * 24 * 1000) / 1000;

        // moeGoPay transaction summary
        if (Objects.equals(businessInfo.getPrimaryPayType(), PaymentMethodEnum.CARD_PROCESSOR_TYPE_STRIPE)) {
            MoeGoPayTransactionSummaryDto moeGoPayTransactionSummary =
                    paymentPaymentClient.getMoeGoPayTransactionSummary(businessInfo.getId(), startAt, endAt);
            Business2023SummaryVo.Owner2023Summary.MoegoPay2023Summary moegoPaySummary =
                    new Business2023SummaryVo.Owner2023Summary.MoegoPay2023Summary();
            BigDecimal transactionAmount = new BigDecimal(0);
            if (moeGoPayTransactionSummary != null && moeGoPayTransactionSummary.getTotalAmount() != null) {
                transactionAmount = BigDecimal.valueOf(moeGoPayTransactionSummary.getTotalAmount());
            }
            moegoPaySummary.setTransactionAmount(
                    transactionAmount.setScale(2, RoundingMode.HALF_UP).toString());

            BigDecimal tipsBoosted = new BigDecimal(0);
            if (moeGoPayTransactionSummary != null && moeGoPayTransactionSummary.getTotalTips() != null) {
                tipsBoosted = BigDecimal.valueOf(moeGoPayTransactionSummary.getTotalTips());
            }
            moegoPaySummary.setTipsBoosted(
                    tipsBoosted.setScale(2, RoundingMode.HALF_UP).toString());

            BigDecimal feeSaved = new BigDecimal(0);
            if (moeGoPayTransactionSummary != null && moeGoPayTransactionSummary.getTotalFee() != null) {
                feeSaved = BigDecimal.valueOf(moeGoPayTransactionSummary.getTotalFee());
            }
            moegoPaySummary.setFeeSaved(
                    feeSaved.setScale(2, RoundingMode.HALF_UP).toString());
            result.setMoegoPaySummary(moegoPaySummary);
        }

        // online booking summary
        MoeBusinessBookOnline businessBookOnline = moeBusinessBookOnlineMapper.selectByBusinessId(businessInfo.getId());
        if (businessBookOnline != null
                && Objects.equals(businessBookOnline.getIsEnable(), CustomerContactEnum.BUSINESS_IS_ENABLE)) {
            Business2023SummaryVo.Owner2023Summary.OnlineBooking2023Summary onlineBooking2023Summary =
                    new Business2023SummaryVo.Owner2023Summary.OnlineBooking2023Summary();
            onlineBooking2023Summary.setObRequestCnt(
                    moeGroomingAppointmentMapper.getOnlineBookingCount(businessInfo.getId(), startDate, endDate));
            List<OBMetricVO> abandonResult = metricService.listMetrics(new OBMetricsParams(
                    startDate,
                    endDate,
                    List.of(
                            new OBMetricsParams.OBMetricParams(recovered_records, List.of(SUM)),
                            new OBMetricsParams.OBMetricParams(recovered_revenue, List.of(SUM)))));
            abandonResult.forEach(vo -> {
                if (recovered_records.equals(vo.name())) {
                    Object v = vo.metrics().get(SUM);
                    onlineBooking2023Summary.setAbandonedBookingsRecovered(
                            v == null ? Integer.valueOf(0) : (Integer) v);
                }
                if (recovered_revenue.equals(vo.name())) {
                    Object v = vo.metrics().get(SUM);
                    onlineBooking2023Summary.setRecoveredRevenue(v == null ? new BigDecimal(0) : (BigDecimal) v);
                }
            });
            result.setOnlineBookingSummary(onlineBooking2023Summary);
        }

        // grooming report summary
        List<MoeGroomingReport> reports = moeGroomingReportMapper.queryList(
                businessInfo.getId(),
                appointmentIds,
                List.of(GroomingReportStatusEnum.submitted, GroomingReportStatusEnum.sent));
        Business2023SummaryVo.Owner2023Summary.GroomingReport2023Summary groomingReport2023Summary =
                new Business2023SummaryVo.Owner2023Summary.GroomingReport2023Summary();
        groomingReport2023Summary.setReportSendCnt(reports.size());
        Map<String, Integer> moodCnt = new HashMap<>();
        reports.forEach(report -> {
            try {
                JsonObject originJson =
                        JsonParser.parseString(report.getContentJson()).getAsJsonObject();
                if (!originJson.has("feedbacks")) {
                    return;
                }
                JsonArray feedBacks = originJson.get("feedbacks").getAsJsonArray();
                feedBacks.forEach(feedBack -> {
                    if (!feedBack.getAsJsonObject().has("key")) {
                        return;
                    }
                    if (!"mood".equals(feedBack.getAsJsonObject().get("key").getAsString())) {
                        return;
                    }
                    if (!feedBack.getAsJsonObject().has("choices")) {
                        return;
                    }
                    JsonArray moodChoices =
                            feedBack.getAsJsonObject().get("choices").getAsJsonArray();
                    moodChoices.forEach(moodChoice -> {
                        String mood = moodChoice.getAsString();
                        if (moodCnt.containsKey(mood)) {
                            moodCnt.put(mood, moodCnt.get(mood) + 1);
                        } else {
                            moodCnt.put(mood, 1);
                        }
                    });
                });
            } catch (Exception e) {
                log.info("parse grooming report error, report id: {}", report.getId(), e);
            }
        });
        String mostFrequentMood = moodCnt.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("");
        groomingReport2023Summary.setMostFrequentMood(mostFrequentMood);
        result.setGroomingReportSummary(groomingReport2023Summary);

        // marketing campaigns summary
        MarketingCampaignsSummaryRequest request = MarketingCampaignsSummaryRequest.newBuilder()
                .setBusinessId(businessInfo.getId())
                .setSentAtFrom(startAt)
                .setSentAtTo(endAt)
                .build();
        MarketingCampaignsSummaryResponse marketingCampaignsSummaryResponse =
                marketingEmailClient.marketingCampaignsSummary(request);
        Business2023SummaryVo.Owner2023Summary.MarketingCampaigns2023Summary marketingCampaigns2023Summary =
                new Business2023SummaryVo.Owner2023Summary.MarketingCampaigns2023Summary();
        marketingCampaigns2023Summary.setMarketingEmailsSentCnt(marketingCampaignsSummaryResponse.getEmailSentCnt());
        marketingCampaigns2023Summary.setMarketingEmailsOpenedCnt(
                marketingCampaignsSummaryResponse.getEmailOpenedCnt());
        marketingCampaigns2023Summary.setContributedBookingsCnt(
                marketingCampaignsSummaryResponse.getContributeBookingCnt());
        result.setMarketingCampaignsSummary(marketingCampaigns2023Summary);

        return result;
    }

    Business2023SummaryVo.LongestWorkingDay2023 getLongestWorkingDay(
            List<GroomingBookingDTO> groomingBookingDTOS, Integer staffId) {
        Map<String, Integer> dayWorkingTime = new HashMap<>();
        groomingBookingDTOS.forEach(groomingBookingDTO -> {
            groomingBookingDTO.getPetList().forEach(groomingCustomerPetdetailDTO -> {
                if (staffId != null
                        && !groomingCustomerPetdetailDTO.getStaffId().equals(staffId)) {
                    return;
                }
                String appointmentDate = groomingBookingDTO.getAppointmentDate();
                Integer serviceTime = groomingCustomerPetdetailDTO.getServiceTime();
                if (dayWorkingTime.containsKey(appointmentDate)) {
                    dayWorkingTime.put(appointmentDate, dayWorkingTime.get(appointmentDate) + serviceTime);
                } else {
                    dayWorkingTime.put(appointmentDate, serviceTime);
                }
            });
        });
        Map.Entry<String, Integer> maxEntry = dayWorkingTime.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .orElse(null);
        if (maxEntry == null) {
            return null;
        }
        Business2023SummaryVo.LongestWorkingDay2023 longestWorkingDay =
                new Business2023SummaryVo.LongestWorkingDay2023();
        longestWorkingDay.setDate(maxEntry.getKey());
        longestWorkingDay.setWorkingTimeInMin(maxEntry.getValue());
        return longestWorkingDay;
    }

    Integer getTakeCaredPet(List<GroomingBookingDTO> groomingBookingDTOS, Integer staffId) {
        Map</*apptId*/ Integer, /*petCount*/ Integer> counts = new HashMap<>();
        // 一个 appt 可能有多个 service, 一个 pet 有多个 service 只算一个 pet
        groomingBookingDTOS.forEach(appt -> {
            int count = (int) appt.getPetList().stream()
                    .filter(petDetail -> staffId == null || staffId.equals(petDetail.getStaffId()))
                    .map(GroomingCustomerPetdetailDTO::getPetId)
                    .distinct()
                    .count();
            counts.put(appt.getGroomingId(), count);
        });

        return counts.values().stream().mapToInt(Integer::intValue).sum();
    }

    List<Business2023SummaryVo.MostPetBreed> getMostPetBreed(List<CustomerPetDetailDTO> petDetails) {
        List<Business2023SummaryVo.MostPetBreed> result = new ArrayList<>();

        Map</*breed*/ String, /*count*/ Long> breedCountMap = petDetails.stream()
                .filter(pet -> StringUtils.hasText(pet.getBreed()))
                .collect(groupingBy(CustomerPetDetailDTO::getBreed, counting()));

        List<Map.Entry<String, Long>> sortedEntries = breedCountMap.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed().thenComparing(Map.Entry.comparingByKey()))
                .limit(3)
                .toList();

        sortedEntries.forEach(k -> result.add(
                new Business2023SummaryVo.MostPetBreed(k.getKey(), k.getValue().intValue())));
        return result;
    }

    String getMostPopularPetName(List<CustomerPetDetailDTO> petDetails) {
        Map</*petName*/ String, /*count*/ Long> petNameCountMap = petDetails.stream()
                .filter(pet -> StringUtils.hasText(pet.getPetName()))
                .collect(groupingBy(CustomerPetDetailDTO::getPetName, counting()));

        return petNameCountMap.entrySet().stream()
                .max(Map.Entry.<String, Long>comparingByValue().thenComparing(Map.Entry.comparingByKey()))
                .map(Map.Entry::getKey)
                .orElse("");
    }

    List<Business2023SummaryVo.MostPetService> getMostPetService(
            Integer businessId, List<GroomingCustomerPetdetailDTO> petDetailDTOS) {
        if (CollectionUtils.isEmpty(petDetailDTOS)) {
            return List.of();
        }
        Map</*serviceId*/ Integer, /*count*/ Long> serviceCountMap =
                petDetailDTOS.stream().collect(groupingBy(GroomingCustomerPetdetailDTO::getServiceId, counting()));

        Map<Integer, MoeGroomingServiceDTO> serviceDTOMap =
                groomingServiceService
                        .getServicesByServiceIds(businessId, new ArrayList<>(serviceCountMap.keySet()))
                        .stream()
                        .collect(Collectors.toMap(MoeGroomingServiceDTO::getId, Function.identity(), (k1, k2) -> k2));

        return serviceCountMap.entrySet().stream()
                .filter(k -> {
                    MoeGroomingServiceDTO serviceDTO = serviceDTOMap.get(k.getKey());
                    return serviceDTO != null && StringUtils.hasText(serviceDTO.getName());
                })
                .map(k -> new Business2023SummaryVo.MostPetService(
                        serviceDTOMap.get(k.getKey()).getName(), k.getValue().intValue()))
                .sorted(Comparator.comparing(Business2023SummaryVo.MostPetService::getCount)
                        .reversed()
                        .thenComparing(Business2023SummaryVo.MostPetService::getServiceName))
                .limit(3)
                .toList();
    }
}
