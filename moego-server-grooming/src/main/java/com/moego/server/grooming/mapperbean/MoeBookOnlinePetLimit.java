package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_pet_limit
 */
public class MoeBookOnlinePetLimit {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   type 1-size 2-breed
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     * Database Column Remarks:
     *   find 1-moe_pet_size 2-moe_book_online_pet_limit_breed_binding
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit.find_id
     *
     * @mbg.generated
     */
    private Long findId;

    /**
     * Database Column Remarks:
     *   max number limit
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit.max_number
     *
     * @mbg.generated
     */
    private Integer maxNumber;

    /**
     * Database Column Remarks:
     *   status 1-normal 2-deleted
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_pet_limit.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit.id
     *
     * @return the value of moe_book_online_pet_limit.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit.id
     *
     * @param id the value for moe_book_online_pet_limit.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit.business_id
     *
     * @return the value of moe_book_online_pet_limit.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit.business_id
     *
     * @param businessId the value for moe_book_online_pet_limit.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit.type
     *
     * @return the value of moe_book_online_pet_limit.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit.type
     *
     * @param type the value for moe_book_online_pet_limit.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit.find_id
     *
     * @return the value of moe_book_online_pet_limit.find_id
     *
     * @mbg.generated
     */
    public Long getFindId() {
        return findId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit.find_id
     *
     * @param findId the value for moe_book_online_pet_limit.find_id
     *
     * @mbg.generated
     */
    public void setFindId(Long findId) {
        this.findId = findId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit.max_number
     *
     * @return the value of moe_book_online_pet_limit.max_number
     *
     * @mbg.generated
     */
    public Integer getMaxNumber() {
        return maxNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit.max_number
     *
     * @param maxNumber the value for moe_book_online_pet_limit.max_number
     *
     * @mbg.generated
     */
    public void setMaxNumber(Integer maxNumber) {
        this.maxNumber = maxNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit.status
     *
     * @return the value of moe_book_online_pet_limit.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit.status
     *
     * @param status the value for moe_book_online_pet_limit.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit.create_time
     *
     * @return the value of moe_book_online_pet_limit.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit.create_time
     *
     * @param createTime the value for moe_book_online_pet_limit.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit.update_time
     *
     * @return the value of moe_book_online_pet_limit.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit.update_time
     *
     * @param updateTime the value for moe_book_online_pet_limit.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_pet_limit.company_id
     *
     * @return the value of moe_book_online_pet_limit.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_pet_limit.company_id
     *
     * @param companyId the value for moe_book_online_pet_limit.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
