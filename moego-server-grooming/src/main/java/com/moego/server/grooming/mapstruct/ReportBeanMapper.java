package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.service.dto.GroomingReportApptDetail;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.report.StaffRevenueDetail;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ReportBeanMapper {

    ReportBeanMapper INSTANCE = Mappers.getMapper(ReportBeanMapper.class);

    GroomingReportWebAppointment toGroomingReportWebAppointment(MoeGroomingAppointment appointment);

    GroomingReportApptDetail toGroomingReportApptDetail(MoeGroomingAppointment appointment);

    ReportWebApptPetDetail toReportWebApptPetDetail(MoeGroomingPetDetail detail);

    List<ReportWebApptPetDetail> toReportWebApptPetDetailList(List<MoeGroomingPetDetail> details);

    MoeGroomingPetDetail toMoeGroomingPetDetail(ReportWebApptPetDetail detail);

    List<MoeGroomingPetDetail> toMoeGroomingPetDetailList(List<ReportWebApptPetDetail> details);

    @Mapping(source = "date", target = "apptDate")
    @Mapping(source = "startTime", target = "apptTime")
    @Mapping(source = "appointmentStatus", target = "apptStatus")
    @Mapping(source = "customerId", target = "ownerId")
    @Mapping(source = "servicePrice", target = "totalServicesAmount")
    @Mapping(source = "addonPrice", target = "totalAddOnsAmount")
    @Mapping(source = "productPrice", target = "totalProductAmount")
    @Mapping(source = "totalCommission", target = "totalPayroll")
    @Mapping(source = "appointmentId", target = "aptId")
    @Mapping(source = "expectedRevenue", target = "revenue")
    @Mapping(source = "collectedRevenue", target = "collectedRevAll")
    @Mapping(target = "petNum", expression = "java(detail.getPetIds().stream().distinct().toList().size())")
    ReportWebEmployee toReportWebEmployee(StaffRevenueDetail detail);
}
