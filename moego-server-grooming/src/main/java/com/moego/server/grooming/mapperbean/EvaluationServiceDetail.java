package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table evaluation_service_detail
 */
public class EvaluationServiceDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   预约 id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.appointment_id
     *
     * @mbg.generated
     */
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   宠物 id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.pet_id
     *
     * @mbg.generated
     */
    private Long petId;

    /**
     * Database Column Remarks:
     *   服务 id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.service_id
     *
     * @mbg.generated
     */
    private Long serviceId;

    /**
     * Database Column Remarks:
     *   服务费用
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.service_price
     *
     * @mbg.generated
     */
    private BigDecimal servicePrice;

    /**
     * Database Column Remarks:
     *   服务时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.service_time
     *
     * @mbg.generated
     */
    private Integer serviceTime;

    /**
     * Database Column Remarks:
     *   开始日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.start_date
     *
     * @mbg.generated
     */
    private Date startDate;

    /**
     * Database Column Remarks:
     *   开始时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.start_time
     *
     * @mbg.generated
     */
    private Integer startTime;

    /**
     * Database Column Remarks:
     *   结束日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.end_date
     *
     * @mbg.generated
     */
    private Date endDate;

    /**
     * Database Column Remarks:
     *   结束时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.end_time
     *
     * @mbg.generated
     */
    private Integer endTime;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     * Database Column Remarks:
     *   The staff id responsible for this evaluation service
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluation_service_detail.staff_id
     *
     * @mbg.generated
     */
    private Long staffId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.id
     *
     * @return the value of evaluation_service_detail.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.id
     *
     * @param id the value for evaluation_service_detail.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.appointment_id
     *
     * @return the value of evaluation_service_detail.appointment_id
     *
     * @mbg.generated
     */
    public Long getAppointmentId() {
        return appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.appointment_id
     *
     * @param appointmentId the value for evaluation_service_detail.appointment_id
     *
     * @mbg.generated
     */
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.pet_id
     *
     * @return the value of evaluation_service_detail.pet_id
     *
     * @mbg.generated
     */
    public Long getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.pet_id
     *
     * @param petId the value for evaluation_service_detail.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.service_id
     *
     * @return the value of evaluation_service_detail.service_id
     *
     * @mbg.generated
     */
    public Long getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.service_id
     *
     * @param serviceId the value for evaluation_service_detail.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.service_price
     *
     * @return the value of evaluation_service_detail.service_price
     *
     * @mbg.generated
     */
    public BigDecimal getServicePrice() {
        return servicePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.service_price
     *
     * @param servicePrice the value for evaluation_service_detail.service_price
     *
     * @mbg.generated
     */
    public void setServicePrice(BigDecimal servicePrice) {
        this.servicePrice = servicePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.service_time
     *
     * @return the value of evaluation_service_detail.service_time
     *
     * @mbg.generated
     */
    public Integer getServiceTime() {
        return serviceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.service_time
     *
     * @param serviceTime the value for evaluation_service_detail.service_time
     *
     * @mbg.generated
     */
    public void setServiceTime(Integer serviceTime) {
        this.serviceTime = serviceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.start_date
     *
     * @return the value of evaluation_service_detail.start_date
     *
     * @mbg.generated
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.start_date
     *
     * @param startDate the value for evaluation_service_detail.start_date
     *
     * @mbg.generated
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.start_time
     *
     * @return the value of evaluation_service_detail.start_time
     *
     * @mbg.generated
     */
    public Integer getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.start_time
     *
     * @param startTime the value for evaluation_service_detail.start_time
     *
     * @mbg.generated
     */
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.end_date
     *
     * @return the value of evaluation_service_detail.end_date
     *
     * @mbg.generated
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.end_date
     *
     * @param endDate the value for evaluation_service_detail.end_date
     *
     * @mbg.generated
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.end_time
     *
     * @return the value of evaluation_service_detail.end_time
     *
     * @mbg.generated
     */
    public Integer getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.end_time
     *
     * @param endTime the value for evaluation_service_detail.end_time
     *
     * @mbg.generated
     */
    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.created_at
     *
     * @return the value of evaluation_service_detail.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.created_at
     *
     * @param createdAt the value for evaluation_service_detail.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.updated_at
     *
     * @return the value of evaluation_service_detail.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.updated_at
     *
     * @param updatedAt the value for evaluation_service_detail.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluation_service_detail.staff_id
     *
     * @return the value of evaluation_service_detail.staff_id
     *
     * @mbg.generated
     */
    public Long getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluation_service_detail.staff_id
     *
     * @param staffId the value for evaluation_service_detail.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }
}
