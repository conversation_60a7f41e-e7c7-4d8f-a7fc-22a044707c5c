package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public abstract class OBSettingMapper {

    public static final OBSettingMapper INSTANCE = Mappers.getMapper(OBSettingMapper.class);

    public abstract BookOnlineDTO entity2DTO(MoeBusinessBookOnline entity);

    @Mapping(target = "companyId", ignore = true)
    public abstract MoeBusinessBookOnline dto2Entity(BookOnlineDTO dto);
}
