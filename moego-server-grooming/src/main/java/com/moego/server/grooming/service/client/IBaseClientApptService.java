package com.moego.server.grooming.service.client;

import com.github.pagehelper.Page;
import com.moego.common.params.PageQuery;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.dto.client.ListClientApptDTO;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
public interface IBaseClientApptService {
    Page<MoeGroomingAppointment> getApptList(ListClientApptDTO listClientApptDTO, PageQuery pageQuery);

    Byte getApptType();
}
