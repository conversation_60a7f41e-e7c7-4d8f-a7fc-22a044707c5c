package com.moego.server.grooming.mapperbean;

import com.moego.idl.models.offering.v1.ServiceOverrideType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeGroomingPetDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public MoeGroomingPetDetailExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> priceOverrideTypeCriteria;

        protected List<Criterion> durationOverrideTypeCriteria;

        protected List<Criterion> allCriteria;

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
            priceOverrideTypeCriteria = new ArrayList<>();
            durationOverrideTypeCriteria = new ArrayList<>();
        }

        public List<Criterion> getPriceOverrideTypeCriteria() {
            return priceOverrideTypeCriteria;
        }

        protected void addPriceOverrideTypeCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            priceOverrideTypeCriteria.add(new Criterion(
                    condition, value, "com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler"));
            allCriteria = null;
        }

        protected void addPriceOverrideTypeCriterion(
                String condition, ServiceOverrideType value1, ServiceOverrideType value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            priceOverrideTypeCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler"));
            allCriteria = null;
        }

        public List<Criterion> getDurationOverrideTypeCriteria() {
            return durationOverrideTypeCriteria;
        }

        protected void addDurationOverrideTypeCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            durationOverrideTypeCriteria.add(new Criterion(
                    condition, value, "com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler"));
            allCriteria = null;
        }

        protected void addDurationOverrideTypeCriterion(
                String condition, ServiceOverrideType value1, ServiceOverrideType value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            durationOverrideTypeCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler"));
            allCriteria = null;
        }

        public boolean isValid() {
            return criteria.size() > 0
                    || priceOverrideTypeCriteria.size() > 0
                    || durationOverrideTypeCriteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            if (allCriteria == null) {
                allCriteria = new ArrayList<>();
                allCriteria.addAll(criteria);
                allCriteria.addAll(priceOverrideTypeCriteria);
                allCriteria.addAll(durationOverrideTypeCriteria);
            }
            return allCriteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
            allCriteria = null;
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGroomingIdIsNull() {
            addCriterion("grooming_id is null");
            return (Criteria) this;
        }

        public Criteria andGroomingIdIsNotNull() {
            addCriterion("grooming_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroomingIdEqualTo(Integer value) {
            addCriterion("grooming_id =", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdNotEqualTo(Integer value) {
            addCriterion("grooming_id <>", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdGreaterThan(Integer value) {
            addCriterion("grooming_id >", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("grooming_id >=", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdLessThan(Integer value) {
            addCriterion("grooming_id <", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdLessThanOrEqualTo(Integer value) {
            addCriterion("grooming_id <=", value, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdIn(List<Integer> values) {
            addCriterion("grooming_id in", values, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdNotIn(List<Integer> values) {
            addCriterion("grooming_id not in", values, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdBetween(Integer value1, Integer value2) {
            addCriterion("grooming_id between", value1, value2, "groomingId");
            return (Criteria) this;
        }

        public Criteria andGroomingIdNotBetween(Integer value1, Integer value2) {
            addCriterion("grooming_id not between", value1, value2, "groomingId");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNull() {
            addCriterion("pet_id is null");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNotNull() {
            addCriterion("pet_id is not null");
            return (Criteria) this;
        }

        public Criteria andPetIdEqualTo(Integer value) {
            addCriterion("pet_id =", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotEqualTo(Integer value) {
            addCriterion("pet_id <>", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThan(Integer value) {
            addCriterion("pet_id >", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("pet_id >=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThan(Integer value) {
            addCriterion("pet_id <", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThanOrEqualTo(Integer value) {
            addCriterion("pet_id <=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdIn(List<Integer> values) {
            addCriterion("pet_id in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotIn(List<Integer> values) {
            addCriterion("pet_id not in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdBetween(Integer value1, Integer value2) {
            addCriterion("pet_id between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotBetween(Integer value1, Integer value2) {
            addCriterion("pet_id not between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNull() {
            addCriterion("staff_id is null");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNotNull() {
            addCriterion("staff_id is not null");
            return (Criteria) this;
        }

        public Criteria andStaffIdEqualTo(Integer value) {
            addCriterion("staff_id =", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotEqualTo(Integer value) {
            addCriterion("staff_id <>", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThan(Integer value) {
            addCriterion("staff_id >", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("staff_id >=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThan(Integer value) {
            addCriterion("staff_id <", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThanOrEqualTo(Integer value) {
            addCriterion("staff_id <=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIn(List<Integer> values) {
            addCriterion("staff_id in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotIn(List<Integer> values) {
            addCriterion("staff_id not in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdBetween(Integer value1, Integer value2) {
            addCriterion("staff_id between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotBetween(Integer value1, Integer value2) {
            addCriterion("staff_id not between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNull() {
            addCriterion("service_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNotNull() {
            addCriterion("service_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceIdEqualTo(Integer value) {
            addCriterion("service_id =", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotEqualTo(Integer value) {
            addCriterion("service_id <>", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThan(Integer value) {
            addCriterion("service_id >", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_id >=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThan(Integer value) {
            addCriterion("service_id <", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThanOrEqualTo(Integer value) {
            addCriterion("service_id <=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdIn(List<Integer> values) {
            addCriterion("service_id in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotIn(List<Integer> values) {
            addCriterion("service_id not in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdBetween(Integer value1, Integer value2) {
            addCriterion("service_id between", value1, value2, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("service_id not between", value1, value2, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIsNull() {
            addCriterion("service_type is null");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIsNotNull() {
            addCriterion("service_type is not null");
            return (Criteria) this;
        }

        public Criteria andServiceTypeEqualTo(Integer value) {
            addCriterion("service_type =", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeNotEqualTo(Integer value) {
            addCriterion("service_type <>", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeGreaterThan(Integer value) {
            addCriterion("service_type >", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_type >=", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeLessThan(Integer value) {
            addCriterion("service_type <", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("service_type <=", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIn(List<Integer> values) {
            addCriterion("service_type in", values, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeNotIn(List<Integer> values) {
            addCriterion("service_type not in", values, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeBetween(Integer value1, Integer value2) {
            addCriterion("service_type between", value1, value2, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("service_type not between", value1, value2, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTimeIsNull() {
            addCriterion("service_time is null");
            return (Criteria) this;
        }

        public Criteria andServiceTimeIsNotNull() {
            addCriterion("service_time is not null");
            return (Criteria) this;
        }

        public Criteria andServiceTimeEqualTo(Integer value) {
            addCriterion("service_time =", value, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServiceTimeNotEqualTo(Integer value) {
            addCriterion("service_time <>", value, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServiceTimeGreaterThan(Integer value) {
            addCriterion("service_time >", value, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServiceTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_time >=", value, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServiceTimeLessThan(Integer value) {
            addCriterion("service_time <", value, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServiceTimeLessThanOrEqualTo(Integer value) {
            addCriterion("service_time <=", value, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServiceTimeIn(List<Integer> values) {
            addCriterion("service_time in", values, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServiceTimeNotIn(List<Integer> values) {
            addCriterion("service_time not in", values, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServiceTimeBetween(Integer value1, Integer value2) {
            addCriterion("service_time between", value1, value2, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServiceTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("service_time not between", value1, value2, "serviceTime");
            return (Criteria) this;
        }

        public Criteria andServicePriceIsNull() {
            addCriterion("service_price is null");
            return (Criteria) this;
        }

        public Criteria andServicePriceIsNotNull() {
            addCriterion("service_price is not null");
            return (Criteria) this;
        }

        public Criteria andServicePriceEqualTo(BigDecimal value) {
            addCriterion("service_price =", value, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andServicePriceNotEqualTo(BigDecimal value) {
            addCriterion("service_price <>", value, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andServicePriceGreaterThan(BigDecimal value) {
            addCriterion("service_price >", value, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andServicePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_price >=", value, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andServicePriceLessThan(BigDecimal value) {
            addCriterion("service_price <", value, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andServicePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_price <=", value, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andServicePriceIn(List<BigDecimal> values) {
            addCriterion("service_price in", values, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andServicePriceNotIn(List<BigDecimal> values) {
            addCriterion("service_price not in", values, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andServicePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_price between", value1, value2, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andServicePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_price not between", value1, value2, "servicePrice");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Long value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Long value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Long value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Long value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Long value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Long> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Long> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Long value1, Long value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Long value1, Long value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Long value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Long value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Long value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Long value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Long value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Long> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Long> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Long value1, Long value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Long value1, Long value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceIsNull() {
            addCriterion("scope_type_price is null");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceIsNotNull() {
            addCriterion("scope_type_price is not null");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceEqualTo(Integer value) {
            addCriterion("scope_type_price =", value, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceNotEqualTo(Integer value) {
            addCriterion("scope_type_price <>", value, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceGreaterThan(Integer value) {
            addCriterion("scope_type_price >", value, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("scope_type_price >=", value, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceLessThan(Integer value) {
            addCriterion("scope_type_price <", value, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceLessThanOrEqualTo(Integer value) {
            addCriterion("scope_type_price <=", value, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceIn(List<Integer> values) {
            addCriterion("scope_type_price in", values, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceNotIn(List<Integer> values) {
            addCriterion("scope_type_price not in", values, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceBetween(Integer value1, Integer value2) {
            addCriterion("scope_type_price between", value1, value2, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypePriceNotBetween(Integer value1, Integer value2) {
            addCriterion("scope_type_price not between", value1, value2, "scopeTypePrice");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeIsNull() {
            addCriterion("scope_type_time is null");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeIsNotNull() {
            addCriterion("scope_type_time is not null");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeEqualTo(Integer value) {
            addCriterion("scope_type_time =", value, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeNotEqualTo(Integer value) {
            addCriterion("scope_type_time <>", value, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeGreaterThan(Integer value) {
            addCriterion("scope_type_time >", value, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("scope_type_time >=", value, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeLessThan(Integer value) {
            addCriterion("scope_type_time <", value, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeLessThanOrEqualTo(Integer value) {
            addCriterion("scope_type_time <=", value, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeIn(List<Integer> values) {
            addCriterion("scope_type_time in", values, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeNotIn(List<Integer> values) {
            addCriterion("scope_type_time not in", values, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeBetween(Integer value1, Integer value2) {
            addCriterion("scope_type_time between", value1, value2, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andScopeTypeTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("scope_type_time not between", value1, value2, "scopeTypeTime");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdIsNull() {
            addCriterion("star_staff_id is null");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdIsNotNull() {
            addCriterion("star_staff_id is not null");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdEqualTo(Integer value) {
            addCriterion("star_staff_id =", value, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdNotEqualTo(Integer value) {
            addCriterion("star_staff_id <>", value, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdGreaterThan(Integer value) {
            addCriterion("star_staff_id >", value, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("star_staff_id >=", value, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdLessThan(Integer value) {
            addCriterion("star_staff_id <", value, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdLessThanOrEqualTo(Integer value) {
            addCriterion("star_staff_id <=", value, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdIn(List<Integer> values) {
            addCriterion("star_staff_id in", values, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdNotIn(List<Integer> values) {
            addCriterion("star_staff_id not in", values, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdBetween(Integer value1, Integer value2) {
            addCriterion("star_staff_id between", value1, value2, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andStarStaffIdNotBetween(Integer value1, Integer value2) {
            addCriterion("star_staff_id not between", value1, value2, "starStaffId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdIsNull() {
            addCriterion("package_service_id is null");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdIsNotNull() {
            addCriterion("package_service_id is not null");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdEqualTo(Integer value) {
            addCriterion("package_service_id =", value, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdNotEqualTo(Integer value) {
            addCriterion("package_service_id <>", value, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdGreaterThan(Integer value) {
            addCriterion("package_service_id >", value, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("package_service_id >=", value, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdLessThan(Integer value) {
            addCriterion("package_service_id <", value, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdLessThanOrEqualTo(Integer value) {
            addCriterion("package_service_id <=", value, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdIn(List<Integer> values) {
            addCriterion("package_service_id in", values, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdNotIn(List<Integer> values) {
            addCriterion("package_service_id not in", values, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdBetween(Integer value1, Integer value2) {
            addCriterion("package_service_id between", value1, value2, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andPackageServiceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("package_service_id not between", value1, value2, "packageServiceId");
            return (Criteria) this;
        }

        public Criteria andEnableOperationIsNull() {
            addCriterion("enable_operation is null");
            return (Criteria) this;
        }

        public Criteria andEnableOperationIsNotNull() {
            addCriterion("enable_operation is not null");
            return (Criteria) this;
        }

        public Criteria andEnableOperationEqualTo(Boolean value) {
            addCriterion("enable_operation =", value, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andEnableOperationNotEqualTo(Boolean value) {
            addCriterion("enable_operation <>", value, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andEnableOperationGreaterThan(Boolean value) {
            addCriterion("enable_operation >", value, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andEnableOperationGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_operation >=", value, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andEnableOperationLessThan(Boolean value) {
            addCriterion("enable_operation <", value, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andEnableOperationLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_operation <=", value, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andEnableOperationIn(List<Boolean> values) {
            addCriterion("enable_operation in", values, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andEnableOperationNotIn(List<Boolean> values) {
            addCriterion("enable_operation not in", values, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andEnableOperationBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_operation between", value1, value2, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andEnableOperationNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_operation not between", value1, value2, "enableOperation");
            return (Criteria) this;
        }

        public Criteria andWorkModeIsNull() {
            addCriterion("work_mode is null");
            return (Criteria) this;
        }

        public Criteria andWorkModeIsNotNull() {
            addCriterion("work_mode is not null");
            return (Criteria) this;
        }

        public Criteria andWorkModeEqualTo(Integer value) {
            addCriterion("work_mode =", value, "workMode");
            return (Criteria) this;
        }

        public Criteria andWorkModeNotEqualTo(Integer value) {
            addCriterion("work_mode <>", value, "workMode");
            return (Criteria) this;
        }

        public Criteria andWorkModeGreaterThan(Integer value) {
            addCriterion("work_mode >", value, "workMode");
            return (Criteria) this;
        }

        public Criteria andWorkModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("work_mode >=", value, "workMode");
            return (Criteria) this;
        }

        public Criteria andWorkModeLessThan(Integer value) {
            addCriterion("work_mode <", value, "workMode");
            return (Criteria) this;
        }

        public Criteria andWorkModeLessThanOrEqualTo(Integer value) {
            addCriterion("work_mode <=", value, "workMode");
            return (Criteria) this;
        }

        public Criteria andWorkModeIn(List<Integer> values) {
            addCriterion("work_mode in", values, "workMode");
            return (Criteria) this;
        }

        public Criteria andWorkModeNotIn(List<Integer> values) {
            addCriterion("work_mode not in", values, "workMode");
            return (Criteria) this;
        }

        public Criteria andWorkModeBetween(Integer value1, Integer value2) {
            addCriterion("work_mode between", value1, value2, "workMode");
            return (Criteria) this;
        }

        public Criteria andWorkModeNotBetween(Integer value1, Integer value2) {
            addCriterion("work_mode not between", value1, value2, "workMode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeIsNull() {
            addCriterion("service_color_code is null");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeIsNotNull() {
            addCriterion("service_color_code is not null");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeEqualTo(String value) {
            addCriterion("service_color_code =", value, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeNotEqualTo(String value) {
            addCriterion("service_color_code <>", value, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeGreaterThan(String value) {
            addCriterion("service_color_code >", value, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("service_color_code >=", value, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeLessThan(String value) {
            addCriterion("service_color_code <", value, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeLessThanOrEqualTo(String value) {
            addCriterion("service_color_code <=", value, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeLike(String value) {
            addCriterion("service_color_code like", value, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeNotLike(String value) {
            addCriterion("service_color_code not like", value, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeIn(List<String> values) {
            addCriterion("service_color_code in", values, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeNotIn(List<String> values) {
            addCriterion("service_color_code not in", values, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeBetween(String value1, String value2) {
            addCriterion("service_color_code between", value1, value2, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andServiceColorCodeNotBetween(String value1, String value2) {
            addCriterion("service_color_code not between", value1, value2, "serviceColorCode");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(String value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(String value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(String value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(String value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(String value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(String value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLike(String value) {
            addCriterion("start_date like", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotLike(String value) {
            addCriterion("start_date not like", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<String> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<String> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(String value1, String value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(String value1, String value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(String value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(String value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(String value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(String value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(String value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLike(String value) {
            addCriterion("end_date like", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotLike(String value) {
            addCriterion("end_date not like", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<String> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<String> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(String value1, String value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(String value1, String value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIsNull() {
            addCriterion("service_item_type is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIsNotNull() {
            addCriterion("service_item_type is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeEqualTo(Integer value) {
            addCriterion("service_item_type =", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotEqualTo(Integer value) {
            addCriterion("service_item_type <>", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeGreaterThan(Integer value) {
            addCriterion("service_item_type >", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_item_type >=", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeLessThan(Integer value) {
            addCriterion("service_item_type <", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeLessThanOrEqualTo(Integer value) {
            addCriterion("service_item_type <=", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIn(List<Integer> values) {
            addCriterion("service_item_type in", values, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotIn(List<Integer> values) {
            addCriterion("service_item_type not in", values, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeBetween(Integer value1, Integer value2) {
            addCriterion("service_item_type between", value1, value2, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("service_item_type not between", value1, value2, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andLodgingIdIsNull() {
            addCriterion("lodging_id is null");
            return (Criteria) this;
        }

        public Criteria andLodgingIdIsNotNull() {
            addCriterion("lodging_id is not null");
            return (Criteria) this;
        }

        public Criteria andLodgingIdEqualTo(Long value) {
            addCriterion("lodging_id =", value, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andLodgingIdNotEqualTo(Long value) {
            addCriterion("lodging_id <>", value, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andLodgingIdGreaterThan(Long value) {
            addCriterion("lodging_id >", value, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andLodgingIdGreaterThanOrEqualTo(Long value) {
            addCriterion("lodging_id >=", value, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andLodgingIdLessThan(Long value) {
            addCriterion("lodging_id <", value, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andLodgingIdLessThanOrEqualTo(Long value) {
            addCriterion("lodging_id <=", value, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andLodgingIdIn(List<Long> values) {
            addCriterion("lodging_id in", values, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andLodgingIdNotIn(List<Long> values) {
            addCriterion("lodging_id not in", values, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andLodgingIdBetween(Long value1, Long value2) {
            addCriterion("lodging_id between", value1, value2, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andLodgingIdNotBetween(Long value1, Long value2) {
            addCriterion("lodging_id not between", value1, value2, "lodgingId");
            return (Criteria) this;
        }

        public Criteria andPriceUnitIsNull() {
            addCriterion("price_unit is null");
            return (Criteria) this;
        }

        public Criteria andPriceUnitIsNotNull() {
            addCriterion("price_unit is not null");
            return (Criteria) this;
        }

        public Criteria andPriceUnitEqualTo(Integer value) {
            addCriterion("price_unit =", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotEqualTo(Integer value) {
            addCriterion("price_unit <>", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitGreaterThan(Integer value) {
            addCriterion("price_unit >", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("price_unit >=", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitLessThan(Integer value) {
            addCriterion("price_unit <", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitLessThanOrEqualTo(Integer value) {
            addCriterion("price_unit <=", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitIn(List<Integer> values) {
            addCriterion("price_unit in", values, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotIn(List<Integer> values) {
            addCriterion("price_unit not in", values, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitBetween(Integer value1, Integer value2) {
            addCriterion("price_unit between", value1, value2, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("price_unit not between", value1, value2, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesIsNull() {
            addCriterion("specific_dates is null");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesIsNotNull() {
            addCriterion("specific_dates is not null");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesEqualTo(String value) {
            addCriterion("specific_dates =", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotEqualTo(String value) {
            addCriterion("specific_dates <>", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesGreaterThan(String value) {
            addCriterion("specific_dates >", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesGreaterThanOrEqualTo(String value) {
            addCriterion("specific_dates >=", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesLessThan(String value) {
            addCriterion("specific_dates <", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesLessThanOrEqualTo(String value) {
            addCriterion("specific_dates <=", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesLike(String value) {
            addCriterion("specific_dates like", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotLike(String value) {
            addCriterion("specific_dates not like", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesIn(List<String> values) {
            addCriterion("specific_dates in", values, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotIn(List<String> values) {
            addCriterion("specific_dates not in", values, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesBetween(String value1, String value2) {
            addCriterion("specific_dates between", value1, value2, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotBetween(String value1, String value2) {
            addCriterion("specific_dates not between", value1, value2, "specificDates");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdIsNull() {
            addCriterion("associated_service_id is null");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdIsNotNull() {
            addCriterion("associated_service_id is not null");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdEqualTo(Long value) {
            addCriterion("associated_service_id =", value, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdNotEqualTo(Long value) {
            addCriterion("associated_service_id <>", value, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdGreaterThan(Long value) {
            addCriterion("associated_service_id >", value, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("associated_service_id >=", value, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdLessThan(Long value) {
            addCriterion("associated_service_id <", value, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdLessThanOrEqualTo(Long value) {
            addCriterion("associated_service_id <=", value, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdIn(List<Long> values) {
            addCriterion("associated_service_id in", values, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdNotIn(List<Long> values) {
            addCriterion("associated_service_id not in", values, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdBetween(Long value1, Long value2) {
            addCriterion("associated_service_id between", value1, value2, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andAssociatedServiceIdNotBetween(Long value1, Long value2) {
            addCriterion("associated_service_id not between", value1, value2, "associatedServiceId");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeIsNull() {
            addCriterion("price_override_type is null");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeIsNotNull() {
            addCriterion("price_override_type is not null");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeEqualTo(ServiceOverrideType value) {
            addPriceOverrideTypeCriterion("price_override_type =", value, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeNotEqualTo(ServiceOverrideType value) {
            addPriceOverrideTypeCriterion("price_override_type <>", value, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeGreaterThan(ServiceOverrideType value) {
            addPriceOverrideTypeCriterion("price_override_type >", value, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeGreaterThanOrEqualTo(ServiceOverrideType value) {
            addPriceOverrideTypeCriterion("price_override_type >=", value, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeLessThan(ServiceOverrideType value) {
            addPriceOverrideTypeCriterion("price_override_type <", value, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeLessThanOrEqualTo(ServiceOverrideType value) {
            addPriceOverrideTypeCriterion("price_override_type <=", value, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeIn(List<ServiceOverrideType> values) {
            addPriceOverrideTypeCriterion("price_override_type in", values, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeNotIn(List<ServiceOverrideType> values) {
            addPriceOverrideTypeCriterion("price_override_type not in", values, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeBetween(ServiceOverrideType value1, ServiceOverrideType value2) {
            addPriceOverrideTypeCriterion("price_override_type between", value1, value2, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andPriceOverrideTypeNotBetween(ServiceOverrideType value1, ServiceOverrideType value2) {
            addPriceOverrideTypeCriterion("price_override_type not between", value1, value2, "priceOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeIsNull() {
            addCriterion("duration_override_type is null");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeIsNotNull() {
            addCriterion("duration_override_type is not null");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeEqualTo(ServiceOverrideType value) {
            addDurationOverrideTypeCriterion("duration_override_type =", value, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeNotEqualTo(ServiceOverrideType value) {
            addDurationOverrideTypeCriterion("duration_override_type <>", value, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeGreaterThan(ServiceOverrideType value) {
            addDurationOverrideTypeCriterion("duration_override_type >", value, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeGreaterThanOrEqualTo(ServiceOverrideType value) {
            addDurationOverrideTypeCriterion("duration_override_type >=", value, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeLessThan(ServiceOverrideType value) {
            addDurationOverrideTypeCriterion("duration_override_type <", value, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeLessThanOrEqualTo(ServiceOverrideType value) {
            addDurationOverrideTypeCriterion("duration_override_type <=", value, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeIn(List<ServiceOverrideType> values) {
            addDurationOverrideTypeCriterion("duration_override_type in", values, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeNotIn(List<ServiceOverrideType> values) {
            addDurationOverrideTypeCriterion("duration_override_type not in", values, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeBetween(ServiceOverrideType value1, ServiceOverrideType value2) {
            addDurationOverrideTypeCriterion("duration_override_type between", value1, value2, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andDurationOverrideTypeNotBetween(ServiceOverrideType value1, ServiceOverrideType value2) {
            addDurationOverrideTypeCriterion(
                    "duration_override_type not between", value1, value2, "durationOverrideType");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayIsNull() {
            addCriterion("quantity_per_day is null");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayIsNotNull() {
            addCriterion("quantity_per_day is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayEqualTo(Integer value) {
            addCriterion("quantity_per_day =", value, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayNotEqualTo(Integer value) {
            addCriterion("quantity_per_day <>", value, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayGreaterThan(Integer value) {
            addCriterion("quantity_per_day >", value, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity_per_day >=", value, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayLessThan(Integer value) {
            addCriterion("quantity_per_day <", value, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayLessThanOrEqualTo(Integer value) {
            addCriterion("quantity_per_day <=", value, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayIn(List<Integer> values) {
            addCriterion("quantity_per_day in", values, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayNotIn(List<Integer> values) {
            addCriterion("quantity_per_day not in", values, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayBetween(Integer value1, Integer value2) {
            addCriterion("quantity_per_day between", value1, value2, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andQuantityPerDayNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity_per_day not between", value1, value2, "quantityPerDay");
            return (Criteria) this;
        }

        public Criteria andDateTypeIsNull() {
            addCriterion("date_type is null");
            return (Criteria) this;
        }

        public Criteria andDateTypeIsNotNull() {
            addCriterion("date_type is not null");
            return (Criteria) this;
        }

        public Criteria andDateTypeEqualTo(Integer value) {
            addCriterion("date_type =", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeNotEqualTo(Integer value) {
            addCriterion("date_type <>", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeGreaterThan(Integer value) {
            addCriterion("date_type >", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("date_type >=", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeLessThan(Integer value) {
            addCriterion("date_type <", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("date_type <=", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeIn(List<Integer> values) {
            addCriterion("date_type in", values, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeNotIn(List<Integer> values) {
            addCriterion("date_type not in", values, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeBetween(Integer value1, Integer value2) {
            addCriterion("date_type between", value1, value2, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("date_type not between", value1, value2, "dateType");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(BigDecimal value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(BigDecimal value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(BigDecimal value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(BigDecimal value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<BigDecimal> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<BigDecimal> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdIsNull() {
            addCriterion("order_line_item_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdIsNotNull() {
            addCriterion("order_line_item_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdEqualTo(Long value) {
            addCriterion("order_line_item_id =", value, "orderLineItemId");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdNotEqualTo(Long value) {
            addCriterion("order_line_item_id <>", value, "orderLineItemId");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdGreaterThan(Long value) {
            addCriterion("order_line_item_id >", value, "orderLineItemId");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("order_line_item_id >=", value, "orderLineItemId");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdLessThan(Long value) {
            addCriterion("order_line_item_id <", value, "orderLineItemId");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdLessThanOrEqualTo(Long value) {
            addCriterion("order_line_item_id <=", value, "orderLineItemId");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdIn(List<Long> values) {
            addCriterion("order_line_item_id in", values, "orderLineItemId");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdNotIn(List<Long> values) {
            addCriterion("order_line_item_id not in", values, "orderLineItemId");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdBetween(Long value1, Long value2) {
            addCriterion("order_line_item_id between", value1, value2, "orderLineItemId");
            return (Criteria) this;
        }

        public Criteria andOrderLineItemIdNotBetween(Long value1, Long value2) {
            addCriterion("order_line_item_id not between", value1, value2, "orderLineItemId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
