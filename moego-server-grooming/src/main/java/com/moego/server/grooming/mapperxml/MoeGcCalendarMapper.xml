<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGcCalendarMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGcCalendar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="setting_id" jdbcType="INTEGER" property="settingId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="synced_staff_id" jdbcType="INTEGER" property="syncedStaffId" />
    <result column="calendar_title" jdbcType="VARCHAR" property="calendarTitle" />
    <result column="google_calendar_id" jdbcType="VARCHAR" property="googleCalendarId" />
    <result column="sync_token" jdbcType="VARCHAR" property="syncToken" />
    <result column="calendar_status" jdbcType="TINYINT" property="calendarStatus" />
    <result column="last_check_time" jdbcType="BIGINT" property="lastCheckTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, setting_id, staff_id, synced_staff_id, calendar_title, google_calendar_id, 
    sync_token, calendar_status, last_check_time, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_gc_calendar
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_gc_calendar
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGcCalendar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_calendar (business_id, setting_id, staff_id, 
      synced_staff_id, calendar_title, google_calendar_id, 
      sync_token, calendar_status, last_check_time, 
      create_time, update_time, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{settingId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, 
      #{syncedStaffId,jdbcType=INTEGER}, #{calendarTitle,jdbcType=VARCHAR}, #{googleCalendarId,jdbcType=VARCHAR}, 
      #{syncToken,jdbcType=VARCHAR}, #{calendarStatus,jdbcType=TINYINT}, #{lastCheckTime,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcCalendar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_calendar
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="settingId != null">
        setting_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="syncedStaffId != null">
        synced_staff_id,
      </if>
      <if test="calendarTitle != null">
        calendar_title,
      </if>
      <if test="googleCalendarId != null">
        google_calendar_id,
      </if>
      <if test="syncToken != null">
        sync_token,
      </if>
      <if test="calendarStatus != null">
        calendar_status,
      </if>
      <if test="lastCheckTime != null">
        last_check_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="settingId != null">
        #{settingId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="syncedStaffId != null">
        #{syncedStaffId,jdbcType=INTEGER},
      </if>
      <if test="calendarTitle != null">
        #{calendarTitle,jdbcType=VARCHAR},
      </if>
      <if test="googleCalendarId != null">
        #{googleCalendarId,jdbcType=VARCHAR},
      </if>
      <if test="syncToken != null">
        #{syncToken,jdbcType=VARCHAR},
      </if>
      <if test="calendarStatus != null">
        #{calendarStatus,jdbcType=TINYINT},
      </if>
      <if test="lastCheckTime != null">
        #{lastCheckTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcCalendar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_calendar
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="settingId != null">
        setting_id = #{settingId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="syncedStaffId != null">
        synced_staff_id = #{syncedStaffId,jdbcType=INTEGER},
      </if>
      <if test="calendarTitle != null">
        calendar_title = #{calendarTitle,jdbcType=VARCHAR},
      </if>
      <if test="googleCalendarId != null">
        google_calendar_id = #{googleCalendarId,jdbcType=VARCHAR},
      </if>
      <if test="syncToken != null">
        sync_token = #{syncToken,jdbcType=VARCHAR},
      </if>
      <if test="calendarStatus != null">
        calendar_status = #{calendarStatus,jdbcType=TINYINT},
      </if>
      <if test="lastCheckTime != null">
        last_check_time = #{lastCheckTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGcCalendar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_calendar
    set business_id = #{businessId,jdbcType=INTEGER},
      setting_id = #{settingId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      synced_staff_id = #{syncedStaffId,jdbcType=INTEGER},
      calendar_title = #{calendarTitle,jdbcType=VARCHAR},
      google_calendar_id = #{googleCalendarId,jdbcType=VARCHAR},
      sync_token = #{syncToken,jdbcType=VARCHAR},
      calendar_status = #{calendarStatus,jdbcType=TINYINT},
      last_check_time = #{lastCheckTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectByStaffIdSettingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_calendar
        where staff_id = #{staffId,jdbcType=INTEGER}
        AND setting_id = #{settingId,jdbcType=INTEGER}
        AND calendar_status = 1
    </select>
    <select id="selectBySyncedStaffIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_calendar
        where business_id = #{businessId,jdbcType=INTEGER}
        and synced_staff_id in
        <foreach close=")" collection="syncedStaffs" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectBySyncedStaffId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_calendar
        where business_id = #{businessId,jdbcType=INTEGER}
        and synced_staff_id = #{syncedStaffId,jdbcType=INTEGER}
        AND calendar_status = 1
    </select>
    <update id="updateSyncTokenByPrimaryKey">
        update moe_gc_calendar
        set sync_token = #{syncToken}
        where id = #{gcCalendarId}
    </update>

  <select id="selectByBusinessId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_gc_calendar
    where
    business_id = #{businessId,jdbcType=INTEGER}
    and calendar_status = 1
    and setting_id in (select gcs.id from moe_gc_setting gcs left join moe_gc_auth_staff gcas on gcs.google_auth_id = gcas.id where gcs.business_id = #{businessId,jdbcType=INTEGER});
  </select>
</mapper>