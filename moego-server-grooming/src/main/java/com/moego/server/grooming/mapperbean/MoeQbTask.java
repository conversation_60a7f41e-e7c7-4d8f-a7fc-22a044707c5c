package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_task
 */
public class MoeQbTask {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_task.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_task.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   1 正在执行  2执行完成  3意外退出
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_task.task_status
     *
     * @mbg.generated
     */
    private Byte taskStatus;

    /**
     * Database Column Remarks:
     *   任务完成时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_task.complete_time
     *
     * @mbg.generated
     */
    private Long completeTime;

    /**
     * Database Column Remarks:
     *   task完成的invoice数量
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_task.complete_count
     *
     * @mbg.generated
     */
    private Integer completeCount;

    /**
     * Database Column Remarks:
     *   任务开始时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_task.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   上个invoice同步时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_task.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_task.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   type of qb sync task, 0-normal, 1-migration
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_task.task_type
     *
     * @mbg.generated
     */
    private Byte taskType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_task.id
     *
     * @return the value of moe_qb_task.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_task.id
     *
     * @param id the value for moe_qb_task.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_task.business_id
     *
     * @return the value of moe_qb_task.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_task.business_id
     *
     * @param businessId the value for moe_qb_task.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_task.task_status
     *
     * @return the value of moe_qb_task.task_status
     *
     * @mbg.generated
     */
    public Byte getTaskStatus() {
        return taskStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_task.task_status
     *
     * @param taskStatus the value for moe_qb_task.task_status
     *
     * @mbg.generated
     */
    public void setTaskStatus(Byte taskStatus) {
        this.taskStatus = taskStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_task.complete_time
     *
     * @return the value of moe_qb_task.complete_time
     *
     * @mbg.generated
     */
    public Long getCompleteTime() {
        return completeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_task.complete_time
     *
     * @param completeTime the value for moe_qb_task.complete_time
     *
     * @mbg.generated
     */
    public void setCompleteTime(Long completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_task.complete_count
     *
     * @return the value of moe_qb_task.complete_count
     *
     * @mbg.generated
     */
    public Integer getCompleteCount() {
        return completeCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_task.complete_count
     *
     * @param completeCount the value for moe_qb_task.complete_count
     *
     * @mbg.generated
     */
    public void setCompleteCount(Integer completeCount) {
        this.completeCount = completeCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_task.create_time
     *
     * @return the value of moe_qb_task.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_task.create_time
     *
     * @param createTime the value for moe_qb_task.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_task.update_time
     *
     * @return the value of moe_qb_task.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_task.update_time
     *
     * @param updateTime the value for moe_qb_task.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_task.company_id
     *
     * @return the value of moe_qb_task.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_task.company_id
     *
     * @param companyId the value for moe_qb_task.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_task.task_type
     *
     * @return the value of moe_qb_task.task_type
     *
     * @mbg.generated
     */
    public Byte getTaskType() {
        return taskType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_task.task_type
     *
     * @param taskType the value for moe_qb_task.task_type
     *
     * @mbg.generated
     */
    public void setTaskType(Byte taskType) {
        this.taskType = taskType;
    }
}
