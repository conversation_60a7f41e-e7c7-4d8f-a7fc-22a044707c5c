package com.moego.server.grooming.service.params;

import com.moego.server.business.dto.TimeRangeDto;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class GetFreeSlotParams {
    // 待计算日期范围。与 BatchSSParams 的日期范围一致或是其子集。
    LocalDateTime startDateTime;
    LocalDateTime endDateTime;

    // cacd enable 时有效
    Long locationId; // 目标地址，SmartScheduleParams 中的 locations 的 key。如果 location 地址有问题，则返回空结果。

    List<Integer> filterAppointmentIdList; // 常用于过滤掉自身或相关的 appointment 所占用的时间段

    // 日期过滤
    List<LocalDate> expectDateList;
    List<Integer> expectDayOfWeekList; // "day of week, value, from 0 (Sunday) to 6 (Saturday)"

    // 时间段过滤
    List<Integer> expectStartTimeList; // 必须以该时间作为 available slot 的起始时间；数组元素之间为或的关系，且按序从小到大。
    List<TimeRangeDto> expectTimeRangeList; // freeSlot 需要在该时间段内
}
