package com.moego.server.grooming.service.utils;

import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.grooming.web.vo.client.BusinessInfoVO;
import com.moego.server.message.api.IMessageService;
import com.moego.server.message.dto.ArrivalWindowSettingDto;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BusinessInfoHelper {

    private final IMessageService messageApi;
    private final IBusinessBusinessClient businessBusinessClient;

    public BusinessInfoVO businessInfoDTO2VO(OBBusinessInfoDTO businessInfoDTO) {
        if (Objects.isNull(businessInfoDTO)) {
            return null;
        }
        BusinessInfoVO vo = new BusinessInfoVO()
                .setBusinessId(businessInfoDTO.getId())
                .setBusinessName(businessInfoDTO.getBusinessName())
                .setPhoneNumber(businessInfoDTO.getPhoneNumber())
                .setEmail(businessInfoDTO.getOwnerEmail())
                .setAddress1(businessInfoDTO.getAddress1())
                .setAddress2(businessInfoDTO.getAddress2())
                .setAddressCity(businessInfoDTO.getAddressCity())
                .setAddressState(businessInfoDTO.getAddressState())
                .setAddressZipcode(businessInfoDTO.getAddressZipcode())
                .setAddressCountry(businessInfoDTO.getCountry())
                .setAddressLat(businessInfoDTO.getAddressLat())
                .setAddressLng(businessInfoDTO.getAddressLng())
                .setAvatarPath(businessInfoDTO.getAvatarPath())
                .setBusinessMode(businessInfoDTO.getBusinessMode())
                .setTimezoneName(businessInfoDTO.getTimezoneName())
                .setCurrencySymbol(businessInfoDTO.getCurrencySymbol())
                .setCurrencyCode(businessInfoDTO.getCurrencyCode())
                .setTimeFormat(businessInfoDTO.getTimeFormat())
                .setTimeFormatType(businessInfoDTO.getTimeFormatType())
                .setDateFormatType(businessInfoDTO.getDateFormatType())
                .setDateFormat(businessInfoDTO.getDateFormat())
                .setAppType(businessInfoDTO.getAppType());
        ArrivalWindowSettingDto arrivalWindow = messageApi.getArrivalWindow(businessInfoDTO.getId());
        vo.setMobileArrivalWindowSetting(arrivalWindow);
        return vo;
    }

    public List<BusinessInfoVO> businessInfoDTO2VO(List<OBBusinessInfoDTO> infoList) {
        return infoList.stream().map(this::businessInfoDTO2VO).toList();
    }

    public Long getCompanyIdByBusinessId(Integer businessId) {
        try {
            Long companyId =
                    businessBusinessClient.getCompanyIdByBusinessId(businessId).companyId();
            if (Objects.isNull(companyId)) {
                return 0L;
            }
            return companyId;
        } catch (Exception e) {
            log.error("getCompanyIdByBusinessId with businessId[{}] error", businessId, e);
            return 0L;
        }
    }
}
