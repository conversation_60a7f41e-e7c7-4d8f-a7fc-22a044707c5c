package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table service_area_pic_cache
 */
public class ServiceAreaPicCache {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_area_pic_cache.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_area_pic_cache.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   图片地址
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_area_pic_cache.url
     *
     * @mbg.generated
     */
    private String url;

    /**
     * Database Column Remarks:
     *   影响图片生成的所有因素的 hash 值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_area_pic_cache.factors_hash
     *
     * @mbg.generated
     */
    private String factorsHash;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_area_pic_cache.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     * Database Column Remarks:
     *   更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_area_pic_cache.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_area_pic_cache.id
     *
     * @return the value of service_area_pic_cache.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_area_pic_cache.id
     *
     * @param id the value for service_area_pic_cache.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_area_pic_cache.business_id
     *
     * @return the value of service_area_pic_cache.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_area_pic_cache.business_id
     *
     * @param businessId the value for service_area_pic_cache.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_area_pic_cache.url
     *
     * @return the value of service_area_pic_cache.url
     *
     * @mbg.generated
     */
    public String getUrl() {
        return url;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_area_pic_cache.url
     *
     * @param url the value for service_area_pic_cache.url
     *
     * @mbg.generated
     */
    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_area_pic_cache.factors_hash
     *
     * @return the value of service_area_pic_cache.factors_hash
     *
     * @mbg.generated
     */
    public String getFactorsHash() {
        return factorsHash;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_area_pic_cache.factors_hash
     *
     * @param factorsHash the value for service_area_pic_cache.factors_hash
     *
     * @mbg.generated
     */
    public void setFactorsHash(String factorsHash) {
        this.factorsHash = factorsHash == null ? null : factorsHash.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_area_pic_cache.created_at
     *
     * @return the value of service_area_pic_cache.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_area_pic_cache.created_at
     *
     * @param createdAt the value for service_area_pic_cache.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_area_pic_cache.updated_at
     *
     * @return the value of service_area_pic_cache.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_area_pic_cache.updated_at
     *
     * @param updatedAt the value for service_area_pic_cache.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
