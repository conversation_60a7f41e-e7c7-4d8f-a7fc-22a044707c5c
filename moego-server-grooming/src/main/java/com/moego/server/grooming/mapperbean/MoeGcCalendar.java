package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_gc_calendar
 */
public class MoeGcCalendar {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   gc_setting id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.setting_id
     *
     * @mbg.generated
     */
    private Integer settingId;

    /**
     * Database Column Remarks:
     *   发起同步的staffId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   需要被同步时间的staffId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.synced_staff_id
     *
     * @mbg.generated
     */
    private Integer syncedStaffId;

    /**
     * Database Column Remarks:
     *   calendar_title
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.calendar_title
     *
     * @mbg.generated
     */
    private String calendarTitle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.google_calendar_id
     *
     * @mbg.generated
     */
    private String googleCalendarId;

    /**
     * Database Column Remarks:
     *   sync token，可能为空
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.sync_token
     *
     * @mbg.generated
     */
    private String syncToken;

    /**
     * Database Column Remarks:
     *   1 正常  2用户从moego remove staff 3 从google calendar 删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.calendar_status
     *
     * @mbg.generated
     */
    private Byte calendarStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.last_check_time
     *
     * @mbg.generated
     */
    private Long lastCheckTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   日历最后同步时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_calendar.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.id
     *
     * @return the value of moe_gc_calendar.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.id
     *
     * @param id the value for moe_gc_calendar.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.business_id
     *
     * @return the value of moe_gc_calendar.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.business_id
     *
     * @param businessId the value for moe_gc_calendar.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.setting_id
     *
     * @return the value of moe_gc_calendar.setting_id
     *
     * @mbg.generated
     */
    public Integer getSettingId() {
        return settingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.setting_id
     *
     * @param settingId the value for moe_gc_calendar.setting_id
     *
     * @mbg.generated
     */
    public void setSettingId(Integer settingId) {
        this.settingId = settingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.staff_id
     *
     * @return the value of moe_gc_calendar.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.staff_id
     *
     * @param staffId the value for moe_gc_calendar.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.synced_staff_id
     *
     * @return the value of moe_gc_calendar.synced_staff_id
     *
     * @mbg.generated
     */
    public Integer getSyncedStaffId() {
        return syncedStaffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.synced_staff_id
     *
     * @param syncedStaffId the value for moe_gc_calendar.synced_staff_id
     *
     * @mbg.generated
     */
    public void setSyncedStaffId(Integer syncedStaffId) {
        this.syncedStaffId = syncedStaffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.calendar_title
     *
     * @return the value of moe_gc_calendar.calendar_title
     *
     * @mbg.generated
     */
    public String getCalendarTitle() {
        return calendarTitle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.calendar_title
     *
     * @param calendarTitle the value for moe_gc_calendar.calendar_title
     *
     * @mbg.generated
     */
    public void setCalendarTitle(String calendarTitle) {
        this.calendarTitle = calendarTitle == null ? null : calendarTitle.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.google_calendar_id
     *
     * @return the value of moe_gc_calendar.google_calendar_id
     *
     * @mbg.generated
     */
    public String getGoogleCalendarId() {
        return googleCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.google_calendar_id
     *
     * @param googleCalendarId the value for moe_gc_calendar.google_calendar_id
     *
     * @mbg.generated
     */
    public void setGoogleCalendarId(String googleCalendarId) {
        this.googleCalendarId = googleCalendarId == null ? null : googleCalendarId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.sync_token
     *
     * @return the value of moe_gc_calendar.sync_token
     *
     * @mbg.generated
     */
    public String getSyncToken() {
        return syncToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.sync_token
     *
     * @param syncToken the value for moe_gc_calendar.sync_token
     *
     * @mbg.generated
     */
    public void setSyncToken(String syncToken) {
        this.syncToken = syncToken == null ? null : syncToken.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.calendar_status
     *
     * @return the value of moe_gc_calendar.calendar_status
     *
     * @mbg.generated
     */
    public Byte getCalendarStatus() {
        return calendarStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.calendar_status
     *
     * @param calendarStatus the value for moe_gc_calendar.calendar_status
     *
     * @mbg.generated
     */
    public void setCalendarStatus(Byte calendarStatus) {
        this.calendarStatus = calendarStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.last_check_time
     *
     * @return the value of moe_gc_calendar.last_check_time
     *
     * @mbg.generated
     */
    public Long getLastCheckTime() {
        return lastCheckTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.last_check_time
     *
     * @param lastCheckTime the value for moe_gc_calendar.last_check_time
     *
     * @mbg.generated
     */
    public void setLastCheckTime(Long lastCheckTime) {
        this.lastCheckTime = lastCheckTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.create_time
     *
     * @return the value of moe_gc_calendar.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.create_time
     *
     * @param createTime the value for moe_gc_calendar.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.update_time
     *
     * @return the value of moe_gc_calendar.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.update_time
     *
     * @param updateTime the value for moe_gc_calendar.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_calendar.company_id
     *
     * @return the value of moe_gc_calendar.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_calendar.company_id
     *
     * @param companyId the value for moe_gc_calendar.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
