package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Group Class Remove Result DTO
 * 团体课程移除结果
 */
@Data
@Schema(description = "团体课程移除结果")
public class GroupClassRemoveResultDTO {

    @Schema(description = "是否成功")
    private Boolean success;

    @Schema(description = "预约ID")
    private Integer appointmentId;

    @Schema(description = "客户ID")
    private Integer customerId;

    @Schema(description = "成功移除的宠物ID列表")
    private List<Integer> removedPetIds;

    @Schema(description = "失败的宠物ID列表")
    private List<Integer> failedPetIds;

    @Schema(description = "退费金额")
    private BigDecimal refundAmount;

    @Schema(description = "退费状态")
    private String refundStatus;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "剩余容量")
    private Integer remainingCapacity;
}
