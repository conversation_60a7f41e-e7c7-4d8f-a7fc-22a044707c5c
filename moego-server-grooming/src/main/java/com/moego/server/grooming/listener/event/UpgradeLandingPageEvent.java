package com.moego.server.grooming.listener.event;

import com.moego.server.grooming.web.params.OBLandingPageMergeParams;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Setter
@Getter
@Accessors(chain = true)
public class UpgradeLandingPageEvent extends ApplicationEvent {

    private Integer businessId;
    private Long companyId;

    private OBLandingPageMergeParams mergeParams;

    public UpgradeLandingPageEvent(Object source) {
        super(source);
    }

    public Long getCompanyId() {
        return Objects.requireNonNullElse(companyId, 0L);
    }
}
