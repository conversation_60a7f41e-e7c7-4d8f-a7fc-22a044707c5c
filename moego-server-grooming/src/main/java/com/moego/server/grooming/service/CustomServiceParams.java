package com.moego.server.grooming.service;

import java.math.BigDecimal;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CustomServiceParams {

    private Integer businessId;
    private Integer customerId;
    // 关联的 petId
    private Integer petId;
    // 关联的 serviceId
    private Integer serviceId;
    // 自定义的 serviceTime
    private Integer serviceTime;
    // 自定义的 servicePrice
    private BigDecimal servicePrice;
    // 自定义的 service startTime
    private Integer startTime;
    // 自定义的 service startDate
    private String startDate;
    // petId + serviceId 关联的 groomingId
    private Set<Integer> groomingIds;
}
