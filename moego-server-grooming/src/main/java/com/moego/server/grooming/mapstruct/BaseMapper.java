package com.moego.server.grooming.mapstruct;

import com.moego.lib.common.util.JsonUtil;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/8/16
 */
@Mapper
public interface BaseMapper {

    @Named("string2List")
    default List<Integer> string2List(String str) {
        return StringUtils.hasText(str) ? JsonUtil.toList(str, Integer.class) : List.of();
    }

    @Named("string2Array")
    default Integer[] string2Array(String str) {
        return StringUtils.hasText(str)
                ? JsonUtil.toList(str, Integer.class).toArray(new Integer[] {})
                : new Integer[0];
    }
}
