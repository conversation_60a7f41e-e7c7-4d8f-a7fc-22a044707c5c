package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.appointment.comment.TicketComment;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TicketNoteMapper {
    TicketNoteMapper INSTANCE = Mappers.getMapper(TicketNoteMapper.class);

    TicketComment entity2DTO(MoeGroomingNote note);
}
