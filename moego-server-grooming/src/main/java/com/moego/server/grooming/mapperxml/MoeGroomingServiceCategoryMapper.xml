<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingServiceCategoryMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="service_item_type" jdbcType="INTEGER" property="serviceItemType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, name, type, sort, status, create_time, update_time, company_id,
    service_item_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_category
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_service_category
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_category (business_id, name, type,
      sort, status, create_time,
      update_time, company_id, service_item_type
      )
    values (#{businessId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT},
      #{sort,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{serviceItemType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="serviceItemType != null">
        service_item_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceItemType != null">
        #{serviceItemType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_category
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceItemType != null">
        service_item_type = #{serviceItemType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_category
    set business_id = #{businessId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      service_item_type = #{serviceItemType,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <resultMap id="ServiceCategoryDTOResultMap" type="com.moego.server.grooming.dto.ServiceCategoryDTO">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
  <update id="updateByPrimaryKeySelectiveWithCid" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory">
    update moe_grooming_service_category
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER} and company_id = #{companyId,jdbcType=INTEGER}
  </update>

  <update id="batchUpdateSort">
    <foreach collection="serviceCategoryList" item="serviceCategory" separator=";">
      UPDATE moe_grooming_service_category SET sort = #{serviceCategory.sort}
      WHERE id = #{serviceCategory.id}
    </foreach>
  </update>
  <select id="selectByBusinessId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_category
    where business_id = #{businessId,jdbcType=INTEGER} AND status = 1
    <if test="type != null">
      AND type = #{type}
    </if>
    order by sort desc,id desc
  </select>
  <select id="selectByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_category
    where
        company_id = #{companyId,jdbcType=BIGINT}
      AND status = 1
    <if test="type != null">
      AND type = #{type}
    </if>
    <if test="serviceItemType != null">
      AND service_item_type = #{serviceItemType}
    </if>
    order by sort desc,id desc
  </select>
  <select id="selectCategoryByCidCategoryIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_category
    where
        company_id = #{companyId,jdbcType=BIGINT}
      AND status = 1
      AND `id` IN
      <foreach close=")" collection="categoryIdList" item="item" open="(" separator=",">
        #{item}
      </foreach>
    order by sort desc,id desc
  </select>
  <select id="selectByIdSet" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_category
    <if test="idSet != null">
      where `id` IN
      <foreach close=")" collection="idSet" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    order by sort desc,id desc
  </select>
  <select id="getCategoryCountWithName" resultType="java.lang.Integer">
    SELECT count(*) as count FROM moe_grooming_service_category
    WHERE business_id = #{businessId}
    AND status = 1
    AND type = #{type}
    AND name=#{name}
    <if test="updateCategoryId != null">
      AND id != #{updateCategoryId}
    </if>
  </select>
  <select id="getCategoryCountWithNameByCompanyId" resultType="java.lang.Integer">
    SELECT count(*) as count FROM moe_grooming_service_category
    WHERE company_id = #{companyId}
    AND status = 1
    AND type = #{type}
    AND name = #{name}
    <if test="updateCategoryId != null">
      AND id != #{updateCategoryId}
    </if>
    <if test="serviceItemType != null">
      AND service_item_type = #{serviceItemType}
    </if>
  </select>
  <select id="batchCountCategoryName" resultType="java.lang.Integer">
    SELECT count(*) as count FROM moe_grooming_service_category
    WHERE company_id = #{companyId}
      AND status = 1
      AND type = #{type}
       AND `name` IN
    <foreach close=")" collection="nameList" item="item" open="(" separator=",">
      #{item}
    </foreach>
    <if test="updateCategoryIdList != null and updateCategoryIdList.size() &gt; 0">
        and id not in
      <foreach close=")" collection="updateCategoryIdList" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="serviceItemType != null">
      AND service_item_type = #{serviceItemType}
    </if>
  </select>


  <select id="selectCategoryByBusinessId" resultMap="ServiceCategoryDTOResultMap">
    select
    id, business_id, name, type, sort
    from moe_grooming_service_category
     WHERE status = 1
    AND  business_id = #{businessId}
    <if test="type!=null">
      and type = #{type}
    </if>
    order by sort desc,id desc
  </select>
  <select id="selectCategoryByCompanyId" resultMap="ServiceCategoryDTOResultMap">
    select
    id, business_id, name, type, sort
    from moe_grooming_service_category
     WHERE
         status = 1
       AND  company_id = #{companyId}
    <if test="type!=null">
       and type = #{type}
    </if>
    <if test="serviceItemType != null">
      AND service_item_type = #{serviceItemType}
    </if>
    order by sort desc,id desc
  </select>
</mapper>
