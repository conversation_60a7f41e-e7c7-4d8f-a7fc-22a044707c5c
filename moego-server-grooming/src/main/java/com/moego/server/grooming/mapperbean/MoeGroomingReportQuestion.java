package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_report_question
 */
public class MoeGroomingReportQuestion {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   category: 1-feedbacks, 2-pet conditions
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.category
     *
     * @mbg.generated
     */
    private Byte category;

    /**
     * Database Column Remarks:
     *   question type: single_choice/multi_choice/text_input/body_view
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.type
     *
     * @mbg.generated
     */
    private String type;

    /**
     * Database Column Remarks:
     *   system default question key
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.key
     *
     * @mbg.generated
     */
    private String key;

    /**
     * Database Column Remarks:
     *   question title
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.title
     *
     * @mbg.generated
     */
    private String title;

    /**
     * Database Column Remarks:
     *   is default question: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.is_default
     *
     * @mbg.generated
     */
    private Boolean isDefault;

    /**
     * Database Column Remarks:
     *   required to fill in, true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.required
     *
     * @mbg.generated
     */
    private Boolean required;

    /**
     * Database Column Remarks:
     *   question type editable: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.type_editable
     *
     * @mbg.generated
     */
    private Boolean typeEditable;

    /**
     * Database Column Remarks:
     *   question title editable: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.title_editable
     *
     * @mbg.generated
     */
    private Boolean titleEditable;

    /**
     * Database Column Remarks:
     *   question options editable: true/false
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.options_editable
     *
     * @mbg.generated
     */
    private Boolean optionsEditable;

    /**
     * Database Column Remarks:
     *   question sort value
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     * Database Column Remarks:
     *   status: 0-Normal,1-Deleted
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   question extra info
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report_question.extra_json
     *
     * @mbg.generated
     */
    private String extraJson;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.id
     *
     * @return the value of moe_grooming_report_question.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.id
     *
     * @param id the value for moe_grooming_report_question.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.business_id
     *
     * @return the value of moe_grooming_report_question.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.business_id
     *
     * @param businessId the value for moe_grooming_report_question.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.category
     *
     * @return the value of moe_grooming_report_question.category
     *
     * @mbg.generated
     */
    public Byte getCategory() {
        return category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.category
     *
     * @param category the value for moe_grooming_report_question.category
     *
     * @mbg.generated
     */
    public void setCategory(Byte category) {
        this.category = category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.type
     *
     * @return the value of moe_grooming_report_question.type
     *
     * @mbg.generated
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.type
     *
     * @param type the value for moe_grooming_report_question.type
     *
     * @mbg.generated
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.key
     *
     * @return the value of moe_grooming_report_question.key
     *
     * @mbg.generated
     */
    public String getKey() {
        return key;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.key
     *
     * @param key the value for moe_grooming_report_question.key
     *
     * @mbg.generated
     */
    public void setKey(String key) {
        this.key = key == null ? null : key.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.title
     *
     * @return the value of moe_grooming_report_question.title
     *
     * @mbg.generated
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.title
     *
     * @param title the value for moe_grooming_report_question.title
     *
     * @mbg.generated
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.is_default
     *
     * @return the value of moe_grooming_report_question.is_default
     *
     * @mbg.generated
     */
    public Boolean getIsDefault() {
        return isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.is_default
     *
     * @param isDefault the value for moe_grooming_report_question.is_default
     *
     * @mbg.generated
     */
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.required
     *
     * @return the value of moe_grooming_report_question.required
     *
     * @mbg.generated
     */
    public Boolean getRequired() {
        return required;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.required
     *
     * @param required the value for moe_grooming_report_question.required
     *
     * @mbg.generated
     */
    public void setRequired(Boolean required) {
        this.required = required;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.type_editable
     *
     * @return the value of moe_grooming_report_question.type_editable
     *
     * @mbg.generated
     */
    public Boolean getTypeEditable() {
        return typeEditable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.type_editable
     *
     * @param typeEditable the value for moe_grooming_report_question.type_editable
     *
     * @mbg.generated
     */
    public void setTypeEditable(Boolean typeEditable) {
        this.typeEditable = typeEditable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.title_editable
     *
     * @return the value of moe_grooming_report_question.title_editable
     *
     * @mbg.generated
     */
    public Boolean getTitleEditable() {
        return titleEditable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.title_editable
     *
     * @param titleEditable the value for moe_grooming_report_question.title_editable
     *
     * @mbg.generated
     */
    public void setTitleEditable(Boolean titleEditable) {
        this.titleEditable = titleEditable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.options_editable
     *
     * @return the value of moe_grooming_report_question.options_editable
     *
     * @mbg.generated
     */
    public Boolean getOptionsEditable() {
        return optionsEditable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.options_editable
     *
     * @param optionsEditable the value for moe_grooming_report_question.options_editable
     *
     * @mbg.generated
     */
    public void setOptionsEditable(Boolean optionsEditable) {
        this.optionsEditable = optionsEditable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.sort
     *
     * @return the value of moe_grooming_report_question.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.sort
     *
     * @param sort the value for moe_grooming_report_question.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.status
     *
     * @return the value of moe_grooming_report_question.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.status
     *
     * @param status the value for moe_grooming_report_question.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.create_time
     *
     * @return the value of moe_grooming_report_question.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.create_time
     *
     * @param createTime the value for moe_grooming_report_question.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.update_time
     *
     * @return the value of moe_grooming_report_question.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.update_time
     *
     * @param updateTime the value for moe_grooming_report_question.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.company_id
     *
     * @return the value of moe_grooming_report_question.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.company_id
     *
     * @param companyId the value for moe_grooming_report_question.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report_question.extra_json
     *
     * @return the value of moe_grooming_report_question.extra_json
     *
     * @mbg.generated
     */
    public String getExtraJson() {
        return extraJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report_question.extra_json
     *
     * @param extraJson the value for moe_grooming_report_question.extra_json
     *
     * @mbg.generated
     */
    public void setExtraJson(String extraJson) {
        this.extraJson = extraJson;
    }
}
