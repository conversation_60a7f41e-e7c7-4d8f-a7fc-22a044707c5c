package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_metrics
 */
public class MoeBookOnlineMetrics {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   moe_business.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   business online booking name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.book_online_name
     *
     * @mbg.generated
     */
    private String bookOnlineName;

    /**
     * Database Column Remarks:
     *   metric name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.metric_name
     *
     * @mbg.generated
     */
    private String metricName;

    /**
     * Database Column Remarks:
     *   metric value
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.metric_value
     *
     * @mbg.generated
     */
    private String metricValue;

    /**
     * Database Column Remarks:
     *   date range alias name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.date_range_alias
     *
     * @mbg.generated
     */
    private String dateRangeAlias;

    /**
     * Database Column Remarks:
     *   start date
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.start_date
     *
     * @mbg.generated
     */
    private String startDate;

    /**
     * Database Column Remarks:
     *   end date
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.end_date
     *
     * @mbg.generated
     */
    private String endDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_metrics.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.id
     *
     * @return the value of moe_book_online_metrics.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.id
     *
     * @param id the value for moe_book_online_metrics.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.business_id
     *
     * @return the value of moe_book_online_metrics.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.business_id
     *
     * @param businessId the value for moe_book_online_metrics.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.book_online_name
     *
     * @return the value of moe_book_online_metrics.book_online_name
     *
     * @mbg.generated
     */
    public String getBookOnlineName() {
        return bookOnlineName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.book_online_name
     *
     * @param bookOnlineName the value for moe_book_online_metrics.book_online_name
     *
     * @mbg.generated
     */
    public void setBookOnlineName(String bookOnlineName) {
        this.bookOnlineName = bookOnlineName == null ? null : bookOnlineName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.metric_name
     *
     * @return the value of moe_book_online_metrics.metric_name
     *
     * @mbg.generated
     */
    public String getMetricName() {
        return metricName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.metric_name
     *
     * @param metricName the value for moe_book_online_metrics.metric_name
     *
     * @mbg.generated
     */
    public void setMetricName(String metricName) {
        this.metricName = metricName == null ? null : metricName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.metric_value
     *
     * @return the value of moe_book_online_metrics.metric_value
     *
     * @mbg.generated
     */
    public String getMetricValue() {
        return metricValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.metric_value
     *
     * @param metricValue the value for moe_book_online_metrics.metric_value
     *
     * @mbg.generated
     */
    public void setMetricValue(String metricValue) {
        this.metricValue = metricValue == null ? null : metricValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.date_range_alias
     *
     * @return the value of moe_book_online_metrics.date_range_alias
     *
     * @mbg.generated
     */
    public String getDateRangeAlias() {
        return dateRangeAlias;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.date_range_alias
     *
     * @param dateRangeAlias the value for moe_book_online_metrics.date_range_alias
     *
     * @mbg.generated
     */
    public void setDateRangeAlias(String dateRangeAlias) {
        this.dateRangeAlias = dateRangeAlias == null ? null : dateRangeAlias.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.start_date
     *
     * @return the value of moe_book_online_metrics.start_date
     *
     * @mbg.generated
     */
    public String getStartDate() {
        return startDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.start_date
     *
     * @param startDate the value for moe_book_online_metrics.start_date
     *
     * @mbg.generated
     */
    public void setStartDate(String startDate) {
        this.startDate = startDate == null ? null : startDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.end_date
     *
     * @return the value of moe_book_online_metrics.end_date
     *
     * @mbg.generated
     */
    public String getEndDate() {
        return endDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.end_date
     *
     * @param endDate the value for moe_book_online_metrics.end_date
     *
     * @mbg.generated
     */
    public void setEndDate(String endDate) {
        this.endDate = endDate == null ? null : endDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.create_time
     *
     * @return the value of moe_book_online_metrics.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.create_time
     *
     * @param createTime the value for moe_book_online_metrics.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.update_time
     *
     * @return the value of moe_book_online_metrics.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.update_time
     *
     * @param updateTime the value for moe_book_online_metrics.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_metrics.company_id
     *
     * @return the value of moe_book_online_metrics.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_metrics.company_id
     *
     * @param companyId the value for moe_book_online_metrics.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
