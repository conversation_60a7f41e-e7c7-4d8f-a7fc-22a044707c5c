package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_service_location
 */
public class MoeGroomingServiceLocation {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   商家id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   税费id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.tax_id
     *
     * @mbg.generated
     */
    private Integer taxId;

    /**
     * Database Column Remarks:
     *   服务价格
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.price
     *
     * @mbg.generated
     */
    private BigDecimal price;

    /**
     * Database Column Remarks:
     *   服务时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.duration
     *
     * @mbg.generated
     */
    private Integer duration;

    /**
     * Database Column Remarks:
     *   是否已删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.is_deleted
     *
     * @mbg.generated
     */
    private Byte isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_location.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.id
     *
     * @return the value of moe_grooming_service_location.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.id
     *
     * @param id the value for moe_grooming_service_location.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.company_id
     *
     * @return the value of moe_grooming_service_location.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.company_id
     *
     * @param companyId the value for moe_grooming_service_location.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.business_id
     *
     * @return the value of moe_grooming_service_location.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.business_id
     *
     * @param businessId the value for moe_grooming_service_location.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.service_id
     *
     * @return the value of moe_grooming_service_location.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.service_id
     *
     * @param serviceId the value for moe_grooming_service_location.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.tax_id
     *
     * @return the value of moe_grooming_service_location.tax_id
     *
     * @mbg.generated
     */
    public Integer getTaxId() {
        return taxId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.tax_id
     *
     * @param taxId the value for moe_grooming_service_location.tax_id
     *
     * @mbg.generated
     */
    public void setTaxId(Integer taxId) {
        this.taxId = taxId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.price
     *
     * @return the value of moe_grooming_service_location.price
     *
     * @mbg.generated
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.price
     *
     * @param price the value for moe_grooming_service_location.price
     *
     * @mbg.generated
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.duration
     *
     * @return the value of moe_grooming_service_location.duration
     *
     * @mbg.generated
     */
    public Integer getDuration() {
        return duration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.duration
     *
     * @param duration the value for moe_grooming_service_location.duration
     *
     * @mbg.generated
     */
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.is_deleted
     *
     * @return the value of moe_grooming_service_location.is_deleted
     *
     * @mbg.generated
     */
    public Byte getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.is_deleted
     *
     * @param isDeleted the value for moe_grooming_service_location.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Byte isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.create_time
     *
     * @return the value of moe_grooming_service_location.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.create_time
     *
     * @param createTime the value for moe_grooming_service_location.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_location.update_time
     *
     * @return the value of moe_grooming_service_location.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_location.update_time
     *
     * @param updateTime the value for moe_grooming_service_location.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
