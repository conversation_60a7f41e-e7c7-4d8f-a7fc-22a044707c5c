package com.moego.server.grooming.service.ob;

import static com.moego.common.utils.CommonUtil.isNormal;
import static java.util.stream.Collectors.groupingBy;

import com.moego.backend.proto.customer.v1.Customer;
import com.moego.backend.proto.customer.v1.CustomerServiceGrpc;
import com.moego.backend.proto.customer.v1.GetCustomerRequest;
import com.moego.common.enums.ServiceEnum;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.online_booking.v1.GroomingAddon;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetail;
import com.moego.idl.models.online_booking.v1.PetServiceDetails;
import com.moego.idl.models.online_booking.v1.ServiceDetail;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderModelV1;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.order.v1.PriceDetailModel;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.PreviewBookingRequestPricingRequest;
import com.moego.idl.service.order.v1.CreateDepositOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v2.DepositRuleServiceGrpc;
import com.moego.idl.service.order.v2.PreviewDepositOrderRequest;
import com.moego.idl.service.order.v2.PreviewDepositOrderResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.server.grooming.convert.DepositOrderConverter;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.params.AppointmentParams;
import com.moego.server.grooming.params.BookOnlineDepositV2Params;
import com.moego.server.grooming.params.BookOnlineDepositV2PreviewParams;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.params.PetDetailParams;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class OBDepositService {
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestService;
    private final DepositRuleServiceGrpc.DepositRuleServiceBlockingStub depositRuleService;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderService;
    private final CustomerServiceGrpc.CustomerServiceBlockingStub moegoCustomerService;
    private final NewOrderHelper newOrderHelper;

    @Nullable
    public BookOnlineDepositV2PreviewParams getDepositOrderPreviewParams(BookOnlineSubmitParams obParams) {
        if (!newOrderHelper.enableNewOrder(obParams.getCompanyId())) {
            return null;
        }

        var customerId = Optional.ofNullable(obParams.getCustomerData().getCustomerId())
                .map(Integer::longValue)
                .orElse(null);
        var ret = new BookOnlineDepositV2PreviewParams();

        ret.setCustomerIdFromRequest(customerId);
        ret.setIsNewPetList(obParams.getPetData().stream()
                .map(it -> !isNormal(it.getPetId()))
                .toList());

        ret.setNewVisitor(true);
        if (isNormal(customerId)) {
            var customer = moegoCustomerService.getCustomer(
                    GetCustomerRequest.newBuilder().setCustomerId(customerId).build());
            // Lead 也当做 new visitor
            if (!Customer.Type.LEAD.equals(customer.getType())) {
                ret.setNewVisitor(false);
            }
        }

        return ret;
    }

    @Nullable
    public BookOnlineDepositV2Params getDepositOrderParams(
            @Nullable BookOnlineDepositV2PreviewParams previewParams,
            BookOnlineSubmitParams obParams,
            AppointmentParams appointmentParams) {
        if (previewParams == null) {
            return null;
        }

        var retParams = new BookOnlineDepositV2Params();

        var depositOrderPreview = previewDepositOrderByRule(
                appointmentParams.getCompanyId(),
                appointmentParams.getBusinessId(),
                previewParams,
                appointmentParams.getAppointmentDateString(),
                obParams.getPetData(),
                appointmentParams.getPetServices());
        if (depositOrderPreview.hasDepositOrderDetailPreview()) {
            // 填充已经创建好的 customer 的 id
            retParams.setDepositOrderPreview(depositOrderPreview.getDepositOrderDetailPreview().toBuilder()
                    .mergeOrder(OrderModelV1.newBuilder()
                            .setCustomerId(appointmentParams.getCustomerId())
                            .build())
                    .build());
            retParams.setDepositOrderPriceItems(depositOrderPreview.getDepositOrderPriceItemsList());
        } else if (obParams.getPreAuthDetail() != null) {
            var preAuthDetail = obParams.getPreAuthDetail();
            retParams.setDepositOrderPreview(OrderDetailModelV1.newBuilder()
                    .setOrder(OrderModelV1.newBuilder()
                            .setCompanyId(appointmentParams.getCompanyId())
                            .setBusinessId(appointmentParams.getBusinessId())
                            .setCustomerId(appointmentParams.getCustomerId())
                            .setSourceType(
                                    OrderSourceType.APPOINTMENT.toString().toLowerCase())
                            // 这里不需要设置 sourceID，一是 appointment 还没创建，二是在后面实际创建 invoice 时会填充 sourceID
                            .setTotalAmount(MoneyUtils.toGoogleMoney(
                                    Optional.ofNullable(preAuthDetail.getServiceTotal())
                                            .orElse(BigDecimal.ZERO)
                                            .add(Optional.ofNullable(preAuthDetail.getTaxAmount())
                                                    .orElse(BigDecimal.ZERO))
                                            .add(Optional.ofNullable(preAuthDetail.getBookingFee())
                                                    .orElse(BigDecimal.ZERO))
                                            .add(Optional.ofNullable(preAuthDetail.getServiceChargeAmount())
                                                    .orElse(BigDecimal.ZERO))
                                            .add(Optional.ofNullable(preAuthDetail.getTipsAmount())
                                                    .orElse(BigDecimal.ZERO)),
                                    "USD"))
                            .setDescription("PreAuth")
                            .build())
                    .build());
        }
        return retParams;
    }

    private PreviewDepositOrderResponse previewDepositOrderByRule(
            long companyId,
            long businessId,
            BookOnlineDepositV2PreviewParams previewParams,
            String appointmentStartDate,
            List<BookOnlinePetParams> petData,
            List<PetDetailParams> petServices) {
        if (petServices.isEmpty()) {
            return PreviewDepositOrderResponse.newBuilder().build();
        }

        var previewPricingRequestBuilder = PreviewBookingRequestPricingRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addAllPetServices(convertPetServices(
                        appointmentStartDate, petData, previewParams.getIsNewPetList(), petServices));
        if (previewParams.getCustomerIdFromRequest() != null) {
            previewPricingRequestBuilder.setCustomerId(previewParams.getCustomerIdFromRequest());
        }
        var previewPricingResponse =
                bookingRequestService.previewBookingRequestPricing(previewPricingRequestBuilder.build());
        var servicePricingDetails = previewPricingResponse.getLineItemsList().stream()
                .map(it -> PreviewDepositOrderRequest.ServicePricingDetail.newBuilder()
                        .setPetId(it.getPetId())
                        .setService(it.getService())
                        .setUnitPrice(it.getUnitPrice())
                        .setQuantity(it.getQuantity())
                        .setTotalPrice(it.getTotalPrice())
                        .setAssociatedServiceId(it.getAssociatedServiceId())
                        .build())
                .toList();

        var previewDepositRequestBuilder = PreviewDepositOrderRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAppointmentStartDate(appointmentStartDate)
                .addAllServicePricingDetails(servicePricingDetails);
        if (!previewParams.isNewVisitor()) {
            previewDepositRequestBuilder.setCustomerId(
                    Objects.requireNonNull(previewParams.getCustomerIdFromRequest()));
        }
        return depositRuleService.previewDepositOrder(previewDepositRequestBuilder.build());
    }

    private List<PetServiceDetails> convertPetServices(
            String appointmentStartDate,
            List<BookOnlinePetParams> petData,
            List<Boolean> isNewPetList,
            List<PetDetailParams> petDetails) {
        if (petData.size() != isNewPetList.size()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petData and isNewPetList size mismatch");
        }

        var ret = new ArrayList<PetServiceDetails>();

        var petDetailsByPetIdMap =
                petDetails.stream().collect(groupingBy(it -> it.getPetId().longValue()));

        for (int i = 0; i < petData.size(); i++) {
            var pet = DepositOrderConverter.INSTANCE.toModel(petData.get(i));
            var pds = petDetailsByPetIdMap.getOrDefault(pet.getPetId(), new ArrayList<>());
            var services = pds.stream()
                    .filter(it -> it.getServiceType() == ServiceEnum.TYPE_SERVICE.intValue())
                    .toList();
            var addonModels = pds.stream()
                    .filter(it -> it.getServiceType() == ServiceEnum.TYPE_ADD_ONS.intValue())
                    .map(it ->
                            GroomingAddon.newBuilder().setId(it.getServiceId()).build())
                    .toList();
            var serviceDetails = services.stream()
                    .map(service -> ServiceDetail.newBuilder()
                            .setGrooming(GroomingServiceDetail.newBuilder()
                                    .setServiceId(service.getServiceId())
                                    .setStartDate(appointmentStartDate)
                                    // collectSelectedPetServiceList 里把 addons 拍平了，此处直接挂在 service 上
                                    .addAllAddons(addonModels)
                                    .build())
                            .build())
                    .toList();
            ret.add(PetServiceDetails.newBuilder()
                    .setPet(pet)
                    .setIsNewPet(isNewPetList.get(i))
                    .addAllServiceDetails(serviceDetails)
                    .build());
        }

        return ret;
    }

    public OrderDetailModelV1 createDepositOrder(
            long appointmentId,
            OrderDetailModelV1 depositOrderPreview,
            List<PreviewDepositOrderResponse.PriceItemByRule> priceItems) {
        var depositOrder = depositOrderPreview.getOrder();
        var createDepositRequest = CreateDepositOrderRequest.newBuilder()
                .setCompanyId(depositOrder.getCompanyId())
                .setBusinessId(depositOrder.getBusinessId())
                .setCustomerId(depositOrder.getCustomerId())
                .setSourceType(OrderSourceType.APPOINTMENT)
                .setSourceId(appointmentId)
                .setDepositAmount(depositOrder.getTotalAmount())
                .setDepositDescription(depositOrder.getDescription())
                .setDepositPriceDetail(PriceDetailModel.newBuilder()
                        .addAllPriceItems(priceItems.stream()
                                .map(PreviewDepositOrderResponse.PriceItemByRule::getPriceItem)
                                .toList())
                        .build())
                .build();
        return orderService.createDepositOrder(createDepositRequest).getOrder();
    }
}
