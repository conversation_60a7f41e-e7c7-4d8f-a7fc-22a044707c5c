package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 旧的 invoice item 结构，已删除 Mapper 文件，旧结构保留，兼容旧接口，后续替换完再删除
 */
@Data
public class MoeGroomingInvoiceItem {

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.invoice_id
     *
     * @mbg.generated
     */
    private Integer invoiceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.pet_detail_id
     *
     * @mbg.generated
     */
    private Integer petDetailId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.service_name
     *
     * @mbg.generated
     */
    private String serviceName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.service_description
     *
     * @mbg.generated
     */
    private String serviceDescription;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.service_unit_price
     *
     * @mbg.generated
     */
    private BigDecimal serviceUnitPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.tax_id
     *
     * @mbg.generated
     */
    private Integer taxId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.tax_rate
     *
     * @mbg.generated
     */
    private BigDecimal taxRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.quantity
     *
     * @mbg.generated
     */
    private Integer quantity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.purchased_quantity
     *
     * @mbg.generated
     */
    private Integer purchasedQuantity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.total_list_price
     *
     * @mbg.generated
     */
    private BigDecimal totalListPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.total_sale_price
     *
     * @mbg.generated
     */
    private BigDecimal totalSalePrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.discount_amount
     *
     * @mbg.generated
     */
    private BigDecimal discountAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.tax_amount
     *
     * @mbg.generated
     */
    private BigDecimal taxAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_invoice_item.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    // order新增字段，非invoice表
    private Integer staffId;
    private String type;
    private Boolean isDeleted;
    private Long lineTaxId;
    // order refund / payment 需求新增新字段
    private long petId;
    private String taxName; // taxId, taxRate  旧模型中已定义未使用， taxAmount当前使用
    private String currentCode;
    private Integer refundedQuantity;
    private BigDecimal refundedAmount;
    private BigDecimal refundedTaxAmount;
    private BigDecimal refundedDiscountAmount;
    private BigDecimal refundedConvenienceFee;
}
