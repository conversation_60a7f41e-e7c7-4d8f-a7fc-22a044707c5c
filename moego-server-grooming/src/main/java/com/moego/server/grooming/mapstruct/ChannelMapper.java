package com.moego.server.grooming.mapstruct;

import com.moego.idl.models.order.v1.RefundChannel;
import com.moego.server.payment.dto.CanRefundChannel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ChannelMapper {
    ChannelMapper INSTANCE = Mappers.getMapper(ChannelMapper.class);

    @Mapping(
            target = "canRefundAmount",
            expression = "java(java.math.BigDecimal.valueOf(channel.getCanRefundAmount()))")
    CanRefundChannel toRefundDto(RefundChannel channel);
}
