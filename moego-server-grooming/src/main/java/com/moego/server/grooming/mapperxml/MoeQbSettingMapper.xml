<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeQbSettingMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeQbSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="connect_id" jdbcType="INTEGER" property="connectId" />
    <result column="enable_sync" jdbcType="TINYINT" property="enableSync" />
    <result column="sync_begin_date" jdbcType="VARCHAR" property="syncBeginDate" />
    <result column="min_sync_date" jdbcType="VARCHAR" property="minSyncDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="tax_sync_type" jdbcType="TINYINT" property="taxSyncType" />
    <result column="user_version" jdbcType="INTEGER" property="userVersion" />
    <result column="last_disconnected_time" jdbcType="VARCHAR" property="lastDisconnectedTime" />
    <result column="sales_receipt_enable" jdbcType="TINYINT" property="salesReceiptEnable" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, connect_id, enable_sync, sync_begin_date, min_sync_date, status,
    create_time, update_time, company_id, tax_sync_type, user_version, last_disconnected_time,
    sales_receipt_enable
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbSettingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_qb_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_qb_setting
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_qb_setting
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeQbSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_setting (business_id, connect_id, enable_sync,
      sync_begin_date, min_sync_date, status,
      create_time, update_time, company_id,
      tax_sync_type, user_version, last_disconnected_time,
      sales_receipt_enable)
    values (#{businessId,jdbcType=INTEGER}, #{connectId,jdbcType=INTEGER}, #{enableSync,jdbcType=TINYINT},
      #{syncBeginDate,jdbcType=VARCHAR}, #{minSyncDate,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT},
      #{taxSyncType,jdbcType=TINYINT}, #{userVersion,jdbcType=INTEGER}, #{lastDisconnectedTime,jdbcType=VARCHAR},
      #{salesReceiptEnable,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_qb_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="connectId != null">
        connect_id,
      </if>
      <if test="enableSync != null">
        enable_sync,
      </if>
      <if test="syncBeginDate != null">
        sync_begin_date,
      </if>
      <if test="minSyncDate != null">
        min_sync_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="taxSyncType != null">
        tax_sync_type,
      </if>
      <if test="userVersion != null">
        user_version,
      </if>
      <if test="lastDisconnectedTime != null">
        last_disconnected_time,
      </if>
      <if test="salesReceiptEnable != null">
        sales_receipt_enable,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        #{connectId,jdbcType=INTEGER},
      </if>
      <if test="enableSync != null">
        #{enableSync,jdbcType=TINYINT},
      </if>
      <if test="syncBeginDate != null">
        #{syncBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="minSyncDate != null">
        #{minSyncDate,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="taxSyncType != null">
        #{taxSyncType,jdbcType=TINYINT},
      </if>
      <if test="userVersion != null">
        #{userVersion,jdbcType=INTEGER},
      </if>
      <if test="lastDisconnectedTime != null">
        #{lastDisconnectedTime,jdbcType=VARCHAR},
      </if>
      <if test="salesReceiptEnable != null">
        #{salesReceiptEnable,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeQbSettingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_qb_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.connectId != null">
        connect_id = #{record.connectId,jdbcType=INTEGER},
      </if>
      <if test="record.enableSync != null">
        enable_sync = #{record.enableSync,jdbcType=TINYINT},
      </if>
      <if test="record.syncBeginDate != null">
        sync_begin_date = #{record.syncBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="record.minSyncDate != null">
        min_sync_date = #{record.minSyncDate,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.taxSyncType != null">
        tax_sync_type = #{record.taxSyncType,jdbcType=TINYINT},
      </if>
      <if test="record.userVersion != null">
        user_version = #{record.userVersion,jdbcType=INTEGER},
      </if>
      <if test="record.lastDisconnectedTime != null">
        last_disconnected_time = #{record.lastDisconnectedTime,jdbcType=VARCHAR},
      </if>
      <if test="record.salesReceiptEnable != null">
        sales_receipt_enable = #{record.salesReceiptEnable,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_setting
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      connect_id = #{record.connectId,jdbcType=INTEGER},
      enable_sync = #{record.enableSync,jdbcType=TINYINT},
      sync_begin_date = #{record.syncBeginDate,jdbcType=VARCHAR},
      min_sync_date = #{record.minSyncDate,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      tax_sync_type = #{record.taxSyncType,jdbcType=TINYINT},
      user_version = #{record.userVersion,jdbcType=INTEGER},
      last_disconnected_time = #{record.lastDisconnectedTime,jdbcType=VARCHAR},
      sales_receipt_enable = #{record.salesReceiptEnable,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeQbSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_setting
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="connectId != null">
        connect_id = #{connectId,jdbcType=INTEGER},
      </if>
      <if test="enableSync != null">
        enable_sync = #{enableSync,jdbcType=TINYINT},
      </if>
      <if test="syncBeginDate != null">
        sync_begin_date = #{syncBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="minSyncDate != null">
        min_sync_date = #{minSyncDate,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="taxSyncType != null">
        tax_sync_type = #{taxSyncType,jdbcType=TINYINT},
      </if>
      <if test="userVersion != null">
        user_version = #{userVersion,jdbcType=INTEGER},
      </if>
      <if test="lastDisconnectedTime != null">
        last_disconnected_time = #{lastDisconnectedTime,jdbcType=VARCHAR},
      </if>
      <if test="salesReceiptEnable != null">
        sales_receipt_enable = #{salesReceiptEnable,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeQbSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_qb_setting
    set business_id = #{businessId,jdbcType=INTEGER},
      connect_id = #{connectId,jdbcType=INTEGER},
      enable_sync = #{enableSync,jdbcType=TINYINT},
      sync_begin_date = #{syncBeginDate,jdbcType=VARCHAR},
      min_sync_date = #{minSyncDate,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      tax_sync_type = #{taxSyncType,jdbcType=TINYINT},
      user_version = #{userVersion,jdbcType=INTEGER},
      last_disconnected_time = #{lastDisconnectedTime,jdbcType=VARCHAR},
      sales_receipt_enable = #{salesReceiptEnable,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByBusinessId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_qb_setting
        where business_id = #{businessId,jdbcType=INTEGER}
        <if test="status != null">
          AND status = #{status}
        </if>
        order by id desc limit 1
    </select>
  <select id="selectByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_qb_setting
    where company_id = #{companyId} and status = 1
  </select>
    <select id="selectAllStatusNormal" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_qb_setting
        where status = 1
        order by id desc
    </select>


    <update id="updateOldRecordByStatus">
    update moe_qb_setting
        set
        status = 2,
        update_time = #{updateTime}
    where
        id != #{id}
        and status = 1
        and business_id = #{businessId}
    </update>


  <select id="selectTaxSyncTypeByBusinessId" resultType="byte">
      select tax_sync_type
      from moe_qb_setting
      where business_id = #{businessId}
      order by id desc
      limit 1
    </select>
  <select id="selectByBusinessIdAndConnectId" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from moe_qb_setting
    where business_id = #{businessId}
    and connect_id = #{connectId}
    limit 1
  </select>
  <select id="listReceiptOpenBusiness"
          resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
      from moe_qb_setting
    where sales_receipt_enable = 1;
  </select>
  <select id="listByParams" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List"/>
    from moe_qb_setting
    <where>
      <if test="params.businessId != null">
        and business_id = #{params.businessId}
      </if>
      <if test="params.companyId != null">
        and company_id = #{params.companyId}
      </if>
      <if test="params.receiptStatus != null">
        and sales_receipt_enable = #{params.receiptStatus}
      </if>
      <if test="params.enableSyncStatus != null">
        and enable_sync = #{params.enableSyncStatus}
      </if>
      <if test="params.userVersion != null">
        and user_version = #{params.userVersion}
      </if>
    </where>
    order by update_time desc
    limit #{limit} offset #{offset}
  </select>
  <select id="countByParams" resultType="java.lang.Integer">
    select
      count(1)
    from moe_qb_setting
    <where>
      <if test="businessId != null">
        and business_id = #{businessId}
      </if>
      <if test="companyId != null">
        and company_id = #{companyId}
      </if>
      <if test="receiptStatus != null">
        and sales_receipt_enable = #{receiptStatus}
      </if>
      <if test="enableSyncStatus != null">
        and enable_sync = #{enableSyncStatus}
      </if>
      <if test="userVersion != null">
        and user_version = #{userVersion}
      </if>
    </where>
  </select>

  <update id="batchUpdateByPrimaryKeySelective">
    <foreach collection="records" item="record" separator=";">
      UPDATE moe_qb_setting
      <set>
        <if test="record.businessId != null">
          business_id = #{record.businessId,jdbcType=INTEGER},
        </if>
        <if test="record.connectId != null">
          connect_id = #{record.connectId,jdbcType=INTEGER},
        </if>
        <if test="record.enableSync != null">
          enable_sync = #{record.enableSync,jdbcType=TINYINT},
        </if>
        <if test="record.syncBeginDate != null">
          sync_begin_date = #{record.syncBeginDate,jdbcType=VARCHAR},
        </if>
        <if test="record.minSyncDate != null">
          min_sync_date = #{record.minSyncDate,jdbcType=VARCHAR},
        </if>
        <if test="record.status != null">
          status = #{record.status,jdbcType=TINYINT},
        </if>
        <if test="record.createTime != null">
          create_time = #{record.createTime,jdbcType=BIGINT},
        </if>
        <if test="record.updateTime != null">
          update_time = #{record.updateTime,jdbcType=BIGINT},
        </if>
        <if test="record.companyId != null">
          company_id = #{record.companyId,jdbcType=BIGINT},
        </if>
        <if test="record.taxSyncType != null">
          tax_sync_type = #{record.taxSyncType,jdbcType=TINYINT},
        </if>
        <if test="record.userVersion != null">
          user_version = #{record.userVersion,jdbcType=INTEGER},
        </if>
        <if test="record.lastDisconnectedTime != null">
          last_disconnected_time = #{record.lastDisconnectedTime,jdbcType=VARCHAR},
        </if>
      </set>
      WHERE id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>
