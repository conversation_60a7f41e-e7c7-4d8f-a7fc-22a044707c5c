// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/customer/v2/metadata_service.proto

package customerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomerRequestMultiError, or nil if none found.
func (m *CreateCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCustomer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "Customer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "Customer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "Customer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCustomerRequestMultiError(errors)
	}

	return nil
}

// CreateCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerRequestMultiError) AllErrors() []error { return m }

// CreateCustomerRequestValidationError is the validation error returned by
// CreateCustomerRequest.Validate if the designated constraints aren't met.
type CreateCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerRequestValidationError) ErrorName() string {
	return "CreateCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerRequestValidationError{}

// Validate checks the field values on GetCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomerRequestMultiError, or nil if none found.
func (m *GetCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := GetCustomerRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCustomerRequestMultiError(errors)
	}

	return nil
}

// GetCustomerRequestMultiError is an error wrapping multiple validation errors
// returned by GetCustomerRequest.ValidateAll() if the designated constraints
// aren't met.
type GetCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerRequestMultiError) AllErrors() []error { return m }

// GetCustomerRequestValidationError is the validation error returned by
// GetCustomerRequest.Validate if the designated constraints aren't met.
type GetCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerRequestValidationError) ErrorName() string {
	return "GetCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerRequestValidationError{}

// Validate checks the field values on ListCustomersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomersRequestMultiError, or nil if none found.
func (m *ListCustomersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilter() == nil {
		err := ListCustomersRequestValidationError{
			field:  "Filter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCustomersRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCustomersRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCustomersRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSorting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCustomersRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCustomersRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSorting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCustomersRequestValidationError{
				field:  "Sorting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := ListCustomersRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for ReturnTotalSize

	if len(errors) > 0 {
		return ListCustomersRequestMultiError(errors)
	}

	return nil
}

// ListCustomersRequestMultiError is an error wrapping multiple validation
// errors returned by ListCustomersRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCustomersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomersRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomersRequestMultiError) AllErrors() []error { return m }

// ListCustomersRequestValidationError is the validation error returned by
// ListCustomersRequest.Validate if the designated constraints aren't met.
type ListCustomersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomersRequestValidationError) ErrorName() string {
	return "ListCustomersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomersRequestValidationError{}

// Validate checks the field values on ListCustomersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomersResponseMultiError, or nil if none found.
func (m *ListCustomersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCustomers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomersResponseValidationError{
						field:  fmt.Sprintf("Customers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomersResponseValidationError{
						field:  fmt.Sprintf("Customers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomersResponseValidationError{
					field:  fmt.Sprintf("Customers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if m.TotalSize != nil {
		// no validation rules for TotalSize
	}

	if len(errors) > 0 {
		return ListCustomersResponseMultiError(errors)
	}

	return nil
}

// ListCustomersResponseMultiError is an error wrapping multiple validation
// errors returned by ListCustomersResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCustomersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomersResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomersResponseMultiError) AllErrors() []error { return m }

// ListCustomersResponseValidationError is the validation error returned by
// ListCustomersResponse.Validate if the designated constraints aren't met.
type ListCustomersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomersResponseValidationError) ErrorName() string {
	return "ListCustomersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomersResponseValidationError{}

// Validate checks the field values on UpdateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerRequestMultiError, or nil if none found.
func (m *UpdateCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := UpdateCustomerRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRef() == nil {
		err := UpdateCustomerRequestValidationError{
			field:  "Ref",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCustomerRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCustomerRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCustomerRequestValidationError{
				field:  "Ref",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCustomerRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerRequestValidationError is the validation error returned by
// UpdateCustomerRequest.Validate if the designated constraints aren't met.
type UpdateCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerRequestValidationError) ErrorName() string {
	return "UpdateCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerRequestValidationError{}

// Validate checks the field values on DeleteCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCustomerRequestMultiError, or nil if none found.
func (m *DeleteCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := DeleteCustomerRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Inactivate

	if len(errors) > 0 {
		return DeleteCustomerRequestMultiError(errors)
	}

	return nil
}

// DeleteCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCustomerRequestMultiError) AllErrors() []error { return m }

// DeleteCustomerRequestValidationError is the validation error returned by
// DeleteCustomerRequest.Validate if the designated constraints aren't met.
type DeleteCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCustomerRequestValidationError) ErrorName() string {
	return "DeleteCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCustomerRequestValidationError{}

// Validate checks the field values on CreateContactRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateContactRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateContactRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateContactRequestMultiError, or nil if none found.
func (m *CreateContactRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateContactRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContact()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateContactRequestValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateContactRequestValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContact()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateContactRequestValidationError{
				field:  "Contact",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateContactRequestMultiError(errors)
	}

	return nil
}

// CreateContactRequestMultiError is an error wrapping multiple validation
// errors returned by CreateContactRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateContactRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateContactRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateContactRequestMultiError) AllErrors() []error { return m }

// CreateContactRequestValidationError is the validation error returned by
// CreateContactRequest.Validate if the designated constraints aren't met.
type CreateContactRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateContactRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateContactRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateContactRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateContactRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateContactRequestValidationError) ErrorName() string {
	return "CreateContactRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateContactRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateContactRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateContactRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateContactRequestValidationError{}

// Validate checks the field values on GetContactRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetContactRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetContactRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetContactRequestMultiError, or nil if none found.
func (m *GetContactRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetContactRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := GetContactRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetContactRequestMultiError(errors)
	}

	return nil
}

// GetContactRequestMultiError is an error wrapping multiple validation errors
// returned by GetContactRequest.ValidateAll() if the designated constraints
// aren't met.
type GetContactRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetContactRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetContactRequestMultiError) AllErrors() []error { return m }

// GetContactRequestValidationError is the validation error returned by
// GetContactRequest.Validate if the designated constraints aren't met.
type GetContactRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetContactRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetContactRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetContactRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetContactRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetContactRequestValidationError) ErrorName() string {
	return "GetContactRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetContactRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetContactRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetContactRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetContactRequestValidationError{}

// Validate checks the field values on ListContactsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListContactsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListContactsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListContactsRequestMultiError, or nil if none found.
func (m *ListContactsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListContactsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilter() == nil {
		err := ListContactsRequestValidationError{
			field:  "Filter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListContactsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListContactsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListContactsRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSorting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListContactsRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListContactsRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSorting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListContactsRequestValidationError{
				field:  "Sorting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := ListContactsRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for ReturnTotalSize

	if len(errors) > 0 {
		return ListContactsRequestMultiError(errors)
	}

	return nil
}

// ListContactsRequestMultiError is an error wrapping multiple validation
// errors returned by ListContactsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListContactsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListContactsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListContactsRequestMultiError) AllErrors() []error { return m }

// ListContactsRequestValidationError is the validation error returned by
// ListContactsRequest.Validate if the designated constraints aren't met.
type ListContactsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListContactsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListContactsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListContactsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListContactsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListContactsRequestValidationError) ErrorName() string {
	return "ListContactsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListContactsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListContactsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListContactsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListContactsRequestValidationError{}

// Validate checks the field values on ListContactsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListContactsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListContactsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListContactsResponseMultiError, or nil if none found.
func (m *ListContactsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListContactsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetContacts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListContactsResponseValidationError{
						field:  fmt.Sprintf("Contacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListContactsResponseValidationError{
						field:  fmt.Sprintf("Contacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListContactsResponseValidationError{
					field:  fmt.Sprintf("Contacts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if m.TotalSize != nil {
		// no validation rules for TotalSize
	}

	if len(errors) > 0 {
		return ListContactsResponseMultiError(errors)
	}

	return nil
}

// ListContactsResponseMultiError is an error wrapping multiple validation
// errors returned by ListContactsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListContactsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListContactsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListContactsResponseMultiError) AllErrors() []error { return m }

// ListContactsResponseValidationError is the validation error returned by
// ListContactsResponse.Validate if the designated constraints aren't met.
type ListContactsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListContactsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListContactsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListContactsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListContactsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListContactsResponseValidationError) ErrorName() string {
	return "ListContactsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListContactsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListContactsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListContactsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListContactsResponseValidationError{}

// Validate checks the field values on UpdateContactRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateContactRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateContactRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateContactRequestMultiError, or nil if none found.
func (m *UpdateContactRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateContactRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := UpdateContactRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRef() == nil {
		err := UpdateContactRequestValidationError{
			field:  "Ref",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateContactRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateContactRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateContactRequestValidationError{
				field:  "Ref",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateContactRequestMultiError(errors)
	}

	return nil
}

// UpdateContactRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateContactRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateContactRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateContactRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateContactRequestMultiError) AllErrors() []error { return m }

// UpdateContactRequestValidationError is the validation error returned by
// UpdateContactRequest.Validate if the designated constraints aren't met.
type UpdateContactRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateContactRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateContactRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateContactRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateContactRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateContactRequestValidationError) ErrorName() string {
	return "UpdateContactRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateContactRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateContactRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateContactRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateContactRequestValidationError{}

// Validate checks the field values on DeleteContactRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteContactRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteContactRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteContactRequestMultiError, or nil if none found.
func (m *DeleteContactRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteContactRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Inactivate

	if len(errors) > 0 {
		return DeleteContactRequestMultiError(errors)
	}

	return nil
}

// DeleteContactRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteContactRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteContactRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteContactRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteContactRequestMultiError) AllErrors() []error { return m }

// DeleteContactRequestValidationError is the validation error returned by
// DeleteContactRequest.Validate if the designated constraints aren't met.
type DeleteContactRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteContactRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteContactRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteContactRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteContactRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteContactRequestValidationError) ErrorName() string {
	return "DeleteContactRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteContactRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteContactRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteContactRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteContactRequestValidationError{}

// Validate checks the field values on CreateCustomerRelatedDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateCustomerRelatedDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerRelatedDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateCustomerRelatedDataRequestMultiError, or nil if none found.
func (m *CreateCustomerRelatedDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerRelatedDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCustomerRelatedData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRelatedDataRequestValidationError{
					field:  "CustomerRelatedData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRelatedDataRequestValidationError{
					field:  "CustomerRelatedData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerRelatedData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRelatedDataRequestValidationError{
				field:  "CustomerRelatedData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCustomerRelatedDataRequestMultiError(errors)
	}

	return nil
}

// CreateCustomerRelatedDataRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateCustomerRelatedDataRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerRelatedDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerRelatedDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerRelatedDataRequestMultiError) AllErrors() []error { return m }

// CreateCustomerRelatedDataRequestValidationError is the validation error
// returned by CreateCustomerRelatedDataRequest.Validate if the designated
// constraints aren't met.
type CreateCustomerRelatedDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerRelatedDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerRelatedDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerRelatedDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerRelatedDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerRelatedDataRequestValidationError) ErrorName() string {
	return "CreateCustomerRelatedDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerRelatedDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerRelatedDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerRelatedDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerRelatedDataRequestValidationError{}

// Validate checks the field values on GetCustomerRelatedDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerRelatedDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerRelatedDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCustomerRelatedDataRequestMultiError, or nil if none found.
func (m *GetCustomerRelatedDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerRelatedDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := GetCustomerRelatedDataRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCustomerRelatedDataRequestMultiError(errors)
	}

	return nil
}

// GetCustomerRelatedDataRequestMultiError is an error wrapping multiple
// validation errors returned by GetCustomerRelatedDataRequest.ValidateAll()
// if the designated constraints aren't met.
type GetCustomerRelatedDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerRelatedDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerRelatedDataRequestMultiError) AllErrors() []error { return m }

// GetCustomerRelatedDataRequestValidationError is the validation error
// returned by GetCustomerRelatedDataRequest.Validate if the designated
// constraints aren't met.
type GetCustomerRelatedDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerRelatedDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerRelatedDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerRelatedDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerRelatedDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerRelatedDataRequestValidationError) ErrorName() string {
	return "GetCustomerRelatedDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerRelatedDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerRelatedDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerRelatedDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerRelatedDataRequestValidationError{}

// Validate checks the field values on ListCustomerRelatedDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomerRelatedDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerRelatedDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCustomerRelatedDataRequestMultiError, or nil if none found.
func (m *ListCustomerRelatedDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerRelatedDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilter() == nil {
		err := ListCustomerRelatedDataRequestValidationError{
			field:  "Filter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCustomerRelatedDataRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCustomerRelatedDataRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCustomerRelatedDataRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSorting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCustomerRelatedDataRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCustomerRelatedDataRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSorting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCustomerRelatedDataRequestValidationError{
				field:  "Sorting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := ListCustomerRelatedDataRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for ReturnTotalSize

	if len(errors) > 0 {
		return ListCustomerRelatedDataRequestMultiError(errors)
	}

	return nil
}

// ListCustomerRelatedDataRequestMultiError is an error wrapping multiple
// validation errors returned by ListCustomerRelatedDataRequest.ValidateAll()
// if the designated constraints aren't met.
type ListCustomerRelatedDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerRelatedDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerRelatedDataRequestMultiError) AllErrors() []error { return m }

// ListCustomerRelatedDataRequestValidationError is the validation error
// returned by ListCustomerRelatedDataRequest.Validate if the designated
// constraints aren't met.
type ListCustomerRelatedDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerRelatedDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerRelatedDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerRelatedDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerRelatedDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerRelatedDataRequestValidationError) ErrorName() string {
	return "ListCustomerRelatedDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerRelatedDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerRelatedDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerRelatedDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerRelatedDataRequestValidationError{}

// Validate checks the field values on ListCustomerRelatedDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomerRelatedDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerRelatedDataResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCustomerRelatedDataResponseMultiError, or nil if none found.
func (m *ListCustomerRelatedDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerRelatedDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCustomerRelatedData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomerRelatedDataResponseValidationError{
						field:  fmt.Sprintf("CustomerRelatedData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomerRelatedDataResponseValidationError{
						field:  fmt.Sprintf("CustomerRelatedData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomerRelatedDataResponseValidationError{
					field:  fmt.Sprintf("CustomerRelatedData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if m.TotalSize != nil {
		// no validation rules for TotalSize
	}

	if len(errors) > 0 {
		return ListCustomerRelatedDataResponseMultiError(errors)
	}

	return nil
}

// ListCustomerRelatedDataResponseMultiError is an error wrapping multiple
// validation errors returned by ListCustomerRelatedDataResponse.ValidateAll()
// if the designated constraints aren't met.
type ListCustomerRelatedDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerRelatedDataResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerRelatedDataResponseMultiError) AllErrors() []error { return m }

// ListCustomerRelatedDataResponseValidationError is the validation error
// returned by ListCustomerRelatedDataResponse.Validate if the designated
// constraints aren't met.
type ListCustomerRelatedDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerRelatedDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerRelatedDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerRelatedDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerRelatedDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerRelatedDataResponseValidationError) ErrorName() string {
	return "ListCustomerRelatedDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerRelatedDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerRelatedDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerRelatedDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerRelatedDataResponseValidationError{}

// Validate checks the field values on UpdateCustomerRelatedDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateCustomerRelatedDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerRelatedDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateCustomerRelatedDataRequestMultiError, or nil if none found.
func (m *UpdateCustomerRelatedDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerRelatedDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := UpdateCustomerRelatedDataRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRef() == nil {
		err := UpdateCustomerRelatedDataRequestValidationError{
			field:  "Ref",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCustomerRelatedDataRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCustomerRelatedDataRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCustomerRelatedDataRequestValidationError{
				field:  "Ref",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCustomerRelatedDataRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerRelatedDataRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCustomerRelatedDataRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerRelatedDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerRelatedDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerRelatedDataRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerRelatedDataRequestValidationError is the validation error
// returned by UpdateCustomerRelatedDataRequest.Validate if the designated
// constraints aren't met.
type UpdateCustomerRelatedDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerRelatedDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerRelatedDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerRelatedDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerRelatedDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerRelatedDataRequestValidationError) ErrorName() string {
	return "UpdateCustomerRelatedDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerRelatedDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerRelatedDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerRelatedDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerRelatedDataRequestValidationError{}

// Validate checks the field values on DeleteCustomerRelatedDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteCustomerRelatedDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCustomerRelatedDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteCustomerRelatedDataRequestMultiError, or nil if none found.
func (m *DeleteCustomerRelatedDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCustomerRelatedDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := DeleteCustomerRelatedDataRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Inactivate

	if len(errors) > 0 {
		return DeleteCustomerRelatedDataRequestMultiError(errors)
	}

	return nil
}

// DeleteCustomerRelatedDataRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeleteCustomerRelatedDataRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteCustomerRelatedDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCustomerRelatedDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCustomerRelatedDataRequestMultiError) AllErrors() []error { return m }

// DeleteCustomerRelatedDataRequestValidationError is the validation error
// returned by DeleteCustomerRelatedDataRequest.Validate if the designated
// constraints aren't met.
type DeleteCustomerRelatedDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCustomerRelatedDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCustomerRelatedDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCustomerRelatedDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCustomerRelatedDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCustomerRelatedDataRequestValidationError) ErrorName() string {
	return "DeleteCustomerRelatedDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCustomerRelatedDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCustomerRelatedDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCustomerRelatedDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCustomerRelatedDataRequestValidationError{}

// Validate checks the field values on CreateContactTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateContactTagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateContactTagRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateContactTagRequestMultiError, or nil if none found.
func (m *CreateContactTagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateContactTagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContactTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateContactTagRequestValidationError{
					field:  "ContactTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateContactTagRequestValidationError{
					field:  "ContactTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContactTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateContactTagRequestValidationError{
				field:  "ContactTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateContactTagRequestMultiError(errors)
	}

	return nil
}

// CreateContactTagRequestMultiError is an error wrapping multiple validation
// errors returned by CreateContactTagRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateContactTagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateContactTagRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateContactTagRequestMultiError) AllErrors() []error { return m }

// CreateContactTagRequestValidationError is the validation error returned by
// CreateContactTagRequest.Validate if the designated constraints aren't met.
type CreateContactTagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateContactTagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateContactTagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateContactTagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateContactTagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateContactTagRequestValidationError) ErrorName() string {
	return "CreateContactTagRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateContactTagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateContactTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateContactTagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateContactTagRequestValidationError{}

// Validate checks the field values on GetContactTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetContactTagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetContactTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetContactTagRequestMultiError, or nil if none found.
func (m *GetContactTagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetContactTagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := GetContactTagRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetContactTagRequestMultiError(errors)
	}

	return nil
}

// GetContactTagRequestMultiError is an error wrapping multiple validation
// errors returned by GetContactTagRequest.ValidateAll() if the designated
// constraints aren't met.
type GetContactTagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetContactTagRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetContactTagRequestMultiError) AllErrors() []error { return m }

// GetContactTagRequestValidationError is the validation error returned by
// GetContactTagRequest.Validate if the designated constraints aren't met.
type GetContactTagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetContactTagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetContactTagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetContactTagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetContactTagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetContactTagRequestValidationError) ErrorName() string {
	return "GetContactTagRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetContactTagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetContactTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetContactTagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetContactTagRequestValidationError{}

// Validate checks the field values on ListContactTagsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListContactTagsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListContactTagsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListContactTagsRequestMultiError, or nil if none found.
func (m *ListContactTagsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListContactTagsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilter() == nil {
		err := ListContactTagsRequestValidationError{
			field:  "Filter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListContactTagsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListContactTagsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListContactTagsRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSorting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListContactTagsRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListContactTagsRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSorting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListContactTagsRequestValidationError{
				field:  "Sorting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := ListContactTagsRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for ReturnTotalSize

	if len(errors) > 0 {
		return ListContactTagsRequestMultiError(errors)
	}

	return nil
}

// ListContactTagsRequestMultiError is an error wrapping multiple validation
// errors returned by ListContactTagsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListContactTagsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListContactTagsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListContactTagsRequestMultiError) AllErrors() []error { return m }

// ListContactTagsRequestValidationError is the validation error returned by
// ListContactTagsRequest.Validate if the designated constraints aren't met.
type ListContactTagsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListContactTagsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListContactTagsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListContactTagsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListContactTagsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListContactTagsRequestValidationError) ErrorName() string {
	return "ListContactTagsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListContactTagsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListContactTagsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListContactTagsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListContactTagsRequestValidationError{}

// Validate checks the field values on ListContactTagsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListContactTagsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListContactTagsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListContactTagsResponseMultiError, or nil if none found.
func (m *ListContactTagsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListContactTagsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetContactTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListContactTagsResponseValidationError{
						field:  fmt.Sprintf("ContactTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListContactTagsResponseValidationError{
						field:  fmt.Sprintf("ContactTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListContactTagsResponseValidationError{
					field:  fmt.Sprintf("ContactTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if m.TotalSize != nil {
		// no validation rules for TotalSize
	}

	if len(errors) > 0 {
		return ListContactTagsResponseMultiError(errors)
	}

	return nil
}

// ListContactTagsResponseMultiError is an error wrapping multiple validation
// errors returned by ListContactTagsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListContactTagsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListContactTagsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListContactTagsResponseMultiError) AllErrors() []error { return m }

// ListContactTagsResponseValidationError is the validation error returned by
// ListContactTagsResponse.Validate if the designated constraints aren't met.
type ListContactTagsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListContactTagsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListContactTagsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListContactTagsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListContactTagsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListContactTagsResponseValidationError) ErrorName() string {
	return "ListContactTagsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListContactTagsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListContactTagsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListContactTagsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListContactTagsResponseValidationError{}

// Validate checks the field values on UpdateContactTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateContactTagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateContactTagRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateContactTagRequestMultiError, or nil if none found.
func (m *UpdateContactTagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateContactTagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := UpdateContactTagRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRef() == nil {
		err := UpdateContactTagRequestValidationError{
			field:  "Ref",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateContactTagRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateContactTagRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateContactTagRequestValidationError{
				field:  "Ref",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateContactTagRequestMultiError(errors)
	}

	return nil
}

// UpdateContactTagRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateContactTagRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateContactTagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateContactTagRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateContactTagRequestMultiError) AllErrors() []error { return m }

// UpdateContactTagRequestValidationError is the validation error returned by
// UpdateContactTagRequest.Validate if the designated constraints aren't met.
type UpdateContactTagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateContactTagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateContactTagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateContactTagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateContactTagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateContactTagRequestValidationError) ErrorName() string {
	return "UpdateContactTagRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateContactTagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateContactTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateContactTagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateContactTagRequestValidationError{}

// Validate checks the field values on DeleteContactTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteContactTagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteContactTagRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteContactTagRequestMultiError, or nil if none found.
func (m *DeleteContactTagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteContactTagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Inactivate

	if len(errors) > 0 {
		return DeleteContactTagRequestMultiError(errors)
	}

	return nil
}

// DeleteContactTagRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteContactTagRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteContactTagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteContactTagRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteContactTagRequestMultiError) AllErrors() []error { return m }

// DeleteContactTagRequestValidationError is the validation error returned by
// DeleteContactTagRequest.Validate if the designated constraints aren't met.
type DeleteContactTagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteContactTagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteContactTagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteContactTagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteContactTagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteContactTagRequestValidationError) ErrorName() string {
	return "DeleteContactTagRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteContactTagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteContactTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteContactTagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteContactTagRequestValidationError{}

// Validate checks the field values on CreateLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLeadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLeadRequestMultiError, or nil if none found.
func (m *CreateLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLead()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "Lead",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "Lead",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLead()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "Lead",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLeadRequestMultiError(errors)
	}

	return nil
}

// CreateLeadRequestMultiError is an error wrapping multiple validation errors
// returned by CreateLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLeadRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLeadRequestMultiError) AllErrors() []error { return m }

// CreateLeadRequestValidationError is the validation error returned by
// CreateLeadRequest.Validate if the designated constraints aren't met.
type CreateLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLeadRequestValidationError) ErrorName() string {
	return "CreateLeadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLeadRequestValidationError{}

// Validate checks the field values on GetLeadRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetLeadRequestMultiError,
// or nil if none found.
func (m *GetLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := GetLeadRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetLeadRequestMultiError(errors)
	}

	return nil
}

// GetLeadRequestMultiError is an error wrapping multiple validation errors
// returned by GetLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type GetLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLeadRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLeadRequestMultiError) AllErrors() []error { return m }

// GetLeadRequestValidationError is the validation error returned by
// GetLeadRequest.Validate if the designated constraints aren't met.
type GetLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLeadRequestValidationError) ErrorName() string { return "GetLeadRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLeadRequestValidationError{}

// Validate checks the field values on ListLeadsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListLeadsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLeadsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLeadsRequestMultiError, or nil if none found.
func (m *ListLeadsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLeadsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilter() == nil {
		err := ListLeadsRequestValidationError{
			field:  "Filter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListLeadsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListLeadsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListLeadsRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSorting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListLeadsRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListLeadsRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSorting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListLeadsRequestValidationError{
				field:  "Sorting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := ListLeadsRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for ReturnTotalSize

	if len(errors) > 0 {
		return ListLeadsRequestMultiError(errors)
	}

	return nil
}

// ListLeadsRequestMultiError is an error wrapping multiple validation errors
// returned by ListLeadsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListLeadsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLeadsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLeadsRequestMultiError) AllErrors() []error { return m }

// ListLeadsRequestValidationError is the validation error returned by
// ListLeadsRequest.Validate if the designated constraints aren't met.
type ListLeadsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLeadsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLeadsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLeadsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLeadsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLeadsRequestValidationError) ErrorName() string { return "ListLeadsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListLeadsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLeadsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLeadsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLeadsRequestValidationError{}

// Validate checks the field values on ListLeadsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListLeadsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLeadsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLeadsResponseMultiError, or nil if none found.
func (m *ListLeadsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLeadsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLeads() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListLeadsResponseValidationError{
						field:  fmt.Sprintf("Leads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListLeadsResponseValidationError{
						field:  fmt.Sprintf("Leads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListLeadsResponseValidationError{
					field:  fmt.Sprintf("Leads[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if m.TotalSize != nil {
		// no validation rules for TotalSize
	}

	if len(errors) > 0 {
		return ListLeadsResponseMultiError(errors)
	}

	return nil
}

// ListLeadsResponseMultiError is an error wrapping multiple validation errors
// returned by ListLeadsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListLeadsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLeadsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLeadsResponseMultiError) AllErrors() []error { return m }

// ListLeadsResponseValidationError is the validation error returned by
// ListLeadsResponse.Validate if the designated constraints aren't met.
type ListLeadsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLeadsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLeadsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLeadsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLeadsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLeadsResponseValidationError) ErrorName() string {
	return "ListLeadsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListLeadsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLeadsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLeadsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLeadsResponseValidationError{}

// Validate checks the field values on UpdateLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLeadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLeadRequestMultiError, or nil if none found.
func (m *UpdateLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := UpdateLeadRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRef() == nil {
		err := UpdateLeadRequestValidationError{
			field:  "Ref",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadRequestValidationError{
				field:  "Ref",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateLeadRequestMultiError(errors)
	}

	return nil
}

// UpdateLeadRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLeadRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLeadRequestMultiError) AllErrors() []error { return m }

// UpdateLeadRequestValidationError is the validation error returned by
// UpdateLeadRequest.Validate if the designated constraints aren't met.
type UpdateLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLeadRequestValidationError) ErrorName() string {
	return "UpdateLeadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLeadRequestValidationError{}

// Validate checks the field values on DeleteLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteLeadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteLeadRequestMultiError, or nil if none found.
func (m *DeleteLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Inactivate

	if len(errors) > 0 {
		return DeleteLeadRequestMultiError(errors)
	}

	return nil
}

// DeleteLeadRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteLeadRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteLeadRequestMultiError) AllErrors() []error { return m }

// DeleteLeadRequestValidationError is the validation error returned by
// DeleteLeadRequest.Validate if the designated constraints aren't met.
type DeleteLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteLeadRequestValidationError) ErrorName() string {
	return "DeleteLeadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteLeadRequestValidationError{}

// Validate checks the field values on CreateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAddressRequestMultiError, or nil if none found.
func (m *CreateAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAddressRequestValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAddressRequestMultiError(errors)
	}

	return nil
}

// CreateAddressRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAddressRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAddressRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAddressRequestMultiError) AllErrors() []error { return m }

// CreateAddressRequestValidationError is the validation error returned by
// CreateAddressRequest.Validate if the designated constraints aren't met.
type CreateAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAddressRequestValidationError) ErrorName() string {
	return "CreateAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAddressRequestValidationError{}

// Validate checks the field values on GetAddressRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAddressRequestMultiError, or nil if none found.
func (m *GetAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := GetAddressRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAddressRequestMultiError(errors)
	}

	return nil
}

// GetAddressRequestMultiError is an error wrapping multiple validation errors
// returned by GetAddressRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddressRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddressRequestMultiError) AllErrors() []error { return m }

// GetAddressRequestValidationError is the validation error returned by
// GetAddressRequest.Validate if the designated constraints aren't met.
type GetAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddressRequestValidationError) ErrorName() string {
	return "GetAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddressRequestValidationError{}

// Validate checks the field values on ListAddressesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAddressesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAddressesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAddressesRequestMultiError, or nil if none found.
func (m *ListAddressesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAddressesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilter() == nil {
		err := ListAddressesRequestValidationError{
			field:  "Filter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAddressesRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAddressesRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAddressesRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSorting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAddressesRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAddressesRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSorting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAddressesRequestValidationError{
				field:  "Sorting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := ListAddressesRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for ReturnTotalSize

	if len(errors) > 0 {
		return ListAddressesRequestMultiError(errors)
	}

	return nil
}

// ListAddressesRequestMultiError is an error wrapping multiple validation
// errors returned by ListAddressesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListAddressesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAddressesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAddressesRequestMultiError) AllErrors() []error { return m }

// ListAddressesRequestValidationError is the validation error returned by
// ListAddressesRequest.Validate if the designated constraints aren't met.
type ListAddressesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAddressesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAddressesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAddressesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAddressesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAddressesRequestValidationError) ErrorName() string {
	return "ListAddressesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAddressesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAddressesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAddressesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAddressesRequestValidationError{}

// Validate checks the field values on ListAddressesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAddressesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAddressesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAddressesResponseMultiError, or nil if none found.
func (m *ListAddressesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAddressesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAddresses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAddressesResponseValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAddressesResponseValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAddressesResponseValidationError{
					field:  fmt.Sprintf("Addresses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if m.TotalSize != nil {
		// no validation rules for TotalSize
	}

	if len(errors) > 0 {
		return ListAddressesResponseMultiError(errors)
	}

	return nil
}

// ListAddressesResponseMultiError is an error wrapping multiple validation
// errors returned by ListAddressesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListAddressesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAddressesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAddressesResponseMultiError) AllErrors() []error { return m }

// ListAddressesResponseValidationError is the validation error returned by
// ListAddressesResponse.Validate if the designated constraints aren't met.
type ListAddressesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAddressesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAddressesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAddressesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAddressesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAddressesResponseValidationError) ErrorName() string {
	return "ListAddressesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAddressesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAddressesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAddressesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAddressesResponseValidationError{}

// Validate checks the field values on UpdateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAddressRequestMultiError, or nil if none found.
func (m *UpdateAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := UpdateAddressRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRef() == nil {
		err := UpdateAddressRequestValidationError{
			field:  "Ref",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAddressRequestValidationError{
				field:  "Ref",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAddressRequestMultiError(errors)
	}

	return nil
}

// UpdateAddressRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAddressRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAddressRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAddressRequestMultiError) AllErrors() []error { return m }

// UpdateAddressRequestValidationError is the validation error returned by
// UpdateAddressRequest.Validate if the designated constraints aren't met.
type UpdateAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAddressRequestValidationError) ErrorName() string {
	return "UpdateAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAddressRequestValidationError{}

// Validate checks the field values on DeleteAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAddressRequestMultiError, or nil if none found.
func (m *DeleteAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Inactivate

	if len(errors) > 0 {
		return DeleteAddressRequestMultiError(errors)
	}

	return nil
}

// DeleteAddressRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteAddressRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAddressRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAddressRequestMultiError) AllErrors() []error { return m }

// DeleteAddressRequestValidationError is the validation error returned by
// DeleteAddressRequest.Validate if the designated constraints aren't met.
type DeleteAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAddressRequestValidationError) ErrorName() string {
	return "DeleteAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAddressRequestValidationError{}

// Validate checks the field values on CreateCustomFieldRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomFieldRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomFieldRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomFieldRequestMultiError, or nil if none found.
func (m *CreateCustomFieldRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomFieldRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCustomField()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomFieldRequestValidationError{
					field:  "CustomField",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomFieldRequestValidationError{
					field:  "CustomField",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomField()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomFieldRequestValidationError{
				field:  "CustomField",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCustomFieldRequestMultiError(errors)
	}

	return nil
}

// CreateCustomFieldRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCustomFieldRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomFieldRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomFieldRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomFieldRequestMultiError) AllErrors() []error { return m }

// CreateCustomFieldRequestValidationError is the validation error returned by
// CreateCustomFieldRequest.Validate if the designated constraints aren't met.
type CreateCustomFieldRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomFieldRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomFieldRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomFieldRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomFieldRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomFieldRequestValidationError) ErrorName() string {
	return "CreateCustomFieldRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomFieldRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomFieldRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomFieldRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomFieldRequestValidationError{}

// Validate checks the field values on GetCustomFieldRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomFieldRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomFieldRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomFieldRequestMultiError, or nil if none found.
func (m *GetCustomFieldRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomFieldRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := GetCustomFieldRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCustomFieldRequestMultiError(errors)
	}

	return nil
}

// GetCustomFieldRequestMultiError is an error wrapping multiple validation
// errors returned by GetCustomFieldRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCustomFieldRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomFieldRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomFieldRequestMultiError) AllErrors() []error { return m }

// GetCustomFieldRequestValidationError is the validation error returned by
// GetCustomFieldRequest.Validate if the designated constraints aren't met.
type GetCustomFieldRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomFieldRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomFieldRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomFieldRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomFieldRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomFieldRequestValidationError) ErrorName() string {
	return "GetCustomFieldRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomFieldRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomFieldRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomFieldRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomFieldRequestValidationError{}

// Validate checks the field values on ListCustomFieldsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomFieldsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomFieldsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomFieldsRequestMultiError, or nil if none found.
func (m *ListCustomFieldsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomFieldsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilter() == nil {
		err := ListCustomFieldsRequestValidationError{
			field:  "Filter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCustomFieldsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCustomFieldsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCustomFieldsRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSorting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCustomFieldsRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCustomFieldsRequestValidationError{
					field:  "Sorting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSorting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCustomFieldsRequestValidationError{
				field:  "Sorting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := ListCustomFieldsRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for ReturnTotalSize

	if len(errors) > 0 {
		return ListCustomFieldsRequestMultiError(errors)
	}

	return nil
}

// ListCustomFieldsRequestMultiError is an error wrapping multiple validation
// errors returned by ListCustomFieldsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCustomFieldsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomFieldsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomFieldsRequestMultiError) AllErrors() []error { return m }

// ListCustomFieldsRequestValidationError is the validation error returned by
// ListCustomFieldsRequest.Validate if the designated constraints aren't met.
type ListCustomFieldsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomFieldsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomFieldsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomFieldsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomFieldsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomFieldsRequestValidationError) ErrorName() string {
	return "ListCustomFieldsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomFieldsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomFieldsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomFieldsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomFieldsRequestValidationError{}

// Validate checks the field values on ListCustomFieldsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomFieldsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomFieldsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomFieldsResponseMultiError, or nil if none found.
func (m *ListCustomFieldsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomFieldsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCustomFields() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomFieldsResponseValidationError{
						field:  fmt.Sprintf("CustomFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomFieldsResponseValidationError{
						field:  fmt.Sprintf("CustomFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomFieldsResponseValidationError{
					field:  fmt.Sprintf("CustomFields[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if m.TotalSize != nil {
		// no validation rules for TotalSize
	}

	if len(errors) > 0 {
		return ListCustomFieldsResponseMultiError(errors)
	}

	return nil
}

// ListCustomFieldsResponseMultiError is an error wrapping multiple validation
// errors returned by ListCustomFieldsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCustomFieldsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomFieldsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomFieldsResponseMultiError) AllErrors() []error { return m }

// ListCustomFieldsResponseValidationError is the validation error returned by
// ListCustomFieldsResponse.Validate if the designated constraints aren't met.
type ListCustomFieldsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomFieldsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomFieldsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomFieldsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomFieldsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomFieldsResponseValidationError) ErrorName() string {
	return "ListCustomFieldsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomFieldsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomFieldsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomFieldsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomFieldsResponseValidationError{}

// Validate checks the field values on UpdateCustomFieldRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomFieldRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomFieldRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomFieldRequestMultiError, or nil if none found.
func (m *UpdateCustomFieldRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomFieldRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := UpdateCustomFieldRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRef() == nil {
		err := UpdateCustomFieldRequestValidationError{
			field:  "Ref",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCustomFieldRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCustomFieldRequestValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCustomFieldRequestValidationError{
				field:  "Ref",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCustomFieldRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomFieldRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCustomFieldRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomFieldRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomFieldRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomFieldRequestMultiError) AllErrors() []error { return m }

// UpdateCustomFieldRequestValidationError is the validation error returned by
// UpdateCustomFieldRequest.Validate if the designated constraints aren't met.
type UpdateCustomFieldRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomFieldRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomFieldRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomFieldRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomFieldRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomFieldRequestValidationError) ErrorName() string {
	return "UpdateCustomFieldRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomFieldRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomFieldRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomFieldRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomFieldRequestValidationError{}

// Validate checks the field values on DeleteCustomFieldRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCustomFieldRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCustomFieldRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCustomFieldRequestMultiError, or nil if none found.
func (m *DeleteCustomFieldRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCustomFieldRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := DeleteCustomFieldRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteCustomFieldRequestMultiError(errors)
	}

	return nil
}

// DeleteCustomFieldRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteCustomFieldRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteCustomFieldRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCustomFieldRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCustomFieldRequestMultiError) AllErrors() []error { return m }

// DeleteCustomFieldRequestValidationError is the validation error returned by
// DeleteCustomFieldRequest.Validate if the designated constraints aren't met.
type DeleteCustomFieldRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCustomFieldRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCustomFieldRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCustomFieldRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCustomFieldRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCustomFieldRequestValidationError) ErrorName() string {
	return "DeleteCustomFieldRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCustomFieldRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCustomFieldRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCustomFieldRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCustomFieldRequestValidationError{}

// Validate checks the field values on ListCustomersRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomersRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomersRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomersRequest_FilterMultiError, or nil if none found.
func (m *ListCustomersRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomersRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOrganization() == nil {
		err := ListCustomersRequest_FilterValidationError{
			field:  "Organization",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOrganization()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCustomersRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCustomersRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrganization()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCustomersRequest_FilterValidationError{
				field:  "Organization",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCustomersRequest_FilterMultiError(errors)
	}

	return nil
}

// ListCustomersRequest_FilterMultiError is an error wrapping multiple
// validation errors returned by ListCustomersRequest_Filter.ValidateAll() if
// the designated constraints aren't met.
type ListCustomersRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomersRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomersRequest_FilterMultiError) AllErrors() []error { return m }

// ListCustomersRequest_FilterValidationError is the validation error returned
// by ListCustomersRequest_Filter.Validate if the designated constraints
// aren't met.
type ListCustomersRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomersRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomersRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomersRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomersRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomersRequest_FilterValidationError) ErrorName() string {
	return "ListCustomersRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomersRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomersRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomersRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomersRequest_FilterValidationError{}

// Validate checks the field values on ListCustomersRequest_Sorting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomersRequest_Sorting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomersRequest_Sorting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomersRequest_SortingMultiError, or nil if none found.
func (m *ListCustomersRequest_Sorting) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomersRequest_Sorting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Direction

	if len(errors) > 0 {
		return ListCustomersRequest_SortingMultiError(errors)
	}

	return nil
}

// ListCustomersRequest_SortingMultiError is an error wrapping multiple
// validation errors returned by ListCustomersRequest_Sorting.ValidateAll() if
// the designated constraints aren't met.
type ListCustomersRequest_SortingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomersRequest_SortingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomersRequest_SortingMultiError) AllErrors() []error { return m }

// ListCustomersRequest_SortingValidationError is the validation error returned
// by ListCustomersRequest_Sorting.Validate if the designated constraints
// aren't met.
type ListCustomersRequest_SortingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomersRequest_SortingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomersRequest_SortingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomersRequest_SortingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomersRequest_SortingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomersRequest_SortingValidationError) ErrorName() string {
	return "ListCustomersRequest_SortingValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomersRequest_SortingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomersRequest_Sorting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomersRequest_SortingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomersRequest_SortingValidationError{}

// Validate checks the field values on UpdateCustomerRequest_UpdateRef with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerRequest_UpdateRef) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerRequest_UpdateRef with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateCustomerRequest_UpdateRefMultiError, or nil if none found.
func (m *UpdateCustomerRequest_UpdateRef) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerRequest_UpdateRef) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GivenName != nil {
		// no validation rules for GivenName
	}

	if m.FamilyName != nil {
		// no validation rules for FamilyName
	}

	if m.CustomFields != nil {

		if all {
			switch v := interface{}(m.GetCustomFields()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateCustomerRequest_UpdateRefValidationError{
						field:  "CustomFields",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateCustomerRequest_UpdateRefValidationError{
						field:  "CustomFields",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCustomFields()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateCustomerRequest_UpdateRefValidationError{
					field:  "CustomFields",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.LifecycleId != nil {
		// no validation rules for LifecycleId
	}

	if m.OwnerStaffId != nil {
		// no validation rules for OwnerStaffId
	}

	if m.ActionStateId != nil {
		// no validation rules for ActionStateId
	}

	if m.AvatarPath != nil {

		if utf8.RuneCountInString(m.GetAvatarPath()) > 255 {
			err := UpdateCustomerRequest_UpdateRefValidationError{
				field:  "AvatarPath",
				reason: "value length must be at most 255 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.ReferralSourceId != nil {

		if m.GetReferralSourceId() <= 0 {
			err := UpdateCustomerRequest_UpdateRefValidationError{
				field:  "ReferralSourceId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return UpdateCustomerRequest_UpdateRefMultiError(errors)
	}

	return nil
}

// UpdateCustomerRequest_UpdateRefMultiError is an error wrapping multiple
// validation errors returned by UpdateCustomerRequest_UpdateRef.ValidateAll()
// if the designated constraints aren't met.
type UpdateCustomerRequest_UpdateRefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerRequest_UpdateRefMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerRequest_UpdateRefMultiError) AllErrors() []error { return m }

// UpdateCustomerRequest_UpdateRefValidationError is the validation error
// returned by UpdateCustomerRequest_UpdateRef.Validate if the designated
// constraints aren't met.
type UpdateCustomerRequest_UpdateRefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerRequest_UpdateRefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerRequest_UpdateRefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerRequest_UpdateRefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerRequest_UpdateRefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerRequest_UpdateRefValidationError) ErrorName() string {
	return "UpdateCustomerRequest_UpdateRefValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerRequest_UpdateRefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerRequest_UpdateRef.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerRequest_UpdateRefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerRequest_UpdateRefValidationError{}

// Validate checks the field values on ListContactsRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListContactsRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListContactsRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListContactsRequest_FilterMultiError, or nil if none found.
func (m *ListContactsRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListContactsRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListContactsRequest_FilterMultiError(errors)
	}

	return nil
}

// ListContactsRequest_FilterMultiError is an error wrapping multiple
// validation errors returned by ListContactsRequest_Filter.ValidateAll() if
// the designated constraints aren't met.
type ListContactsRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListContactsRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListContactsRequest_FilterMultiError) AllErrors() []error { return m }

// ListContactsRequest_FilterValidationError is the validation error returned
// by ListContactsRequest_Filter.Validate if the designated constraints aren't met.
type ListContactsRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListContactsRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListContactsRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListContactsRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListContactsRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListContactsRequest_FilterValidationError) ErrorName() string {
	return "ListContactsRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListContactsRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListContactsRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListContactsRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListContactsRequest_FilterValidationError{}

// Validate checks the field values on ListContactsRequest_Sorting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListContactsRequest_Sorting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListContactsRequest_Sorting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListContactsRequest_SortingMultiError, or nil if none found.
func (m *ListContactsRequest_Sorting) ValidateAll() error {
	return m.validate(true)
}

func (m *ListContactsRequest_Sorting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Direction

	if len(errors) > 0 {
		return ListContactsRequest_SortingMultiError(errors)
	}

	return nil
}

// ListContactsRequest_SortingMultiError is an error wrapping multiple
// validation errors returned by ListContactsRequest_Sorting.ValidateAll() if
// the designated constraints aren't met.
type ListContactsRequest_SortingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListContactsRequest_SortingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListContactsRequest_SortingMultiError) AllErrors() []error { return m }

// ListContactsRequest_SortingValidationError is the validation error returned
// by ListContactsRequest_Sorting.Validate if the designated constraints
// aren't met.
type ListContactsRequest_SortingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListContactsRequest_SortingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListContactsRequest_SortingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListContactsRequest_SortingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListContactsRequest_SortingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListContactsRequest_SortingValidationError) ErrorName() string {
	return "ListContactsRequest_SortingValidationError"
}

// Error satisfies the builtin error interface
func (e ListContactsRequest_SortingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListContactsRequest_Sorting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListContactsRequest_SortingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListContactsRequest_SortingValidationError{}

// Validate checks the field values on UpdateContactRequest_UpdateRef with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateContactRequest_UpdateRef) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateContactRequest_UpdateRef with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateContactRequest_UpdateRefMultiError, or nil if none found.
func (m *UpdateContactRequest_UpdateRef) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateContactRequest_UpdateRef) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GivenName != nil {
		// no validation rules for GivenName
	}

	if m.FamilyName != nil {
		// no validation rules for FamilyName
	}

	if m.Email != nil {
		// no validation rules for Email
	}

	if m.Phone != nil {

		if all {
			switch v := interface{}(m.GetPhone()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateContactRequest_UpdateRefValidationError{
						field:  "Phone",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateContactRequest_UpdateRefValidationError{
						field:  "Phone",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhone()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateContactRequest_UpdateRefValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Note != nil {
		// no validation rules for Note
	}

	if len(errors) > 0 {
		return UpdateContactRequest_UpdateRefMultiError(errors)
	}

	return nil
}

// UpdateContactRequest_UpdateRefMultiError is an error wrapping multiple
// validation errors returned by UpdateContactRequest_UpdateRef.ValidateAll()
// if the designated constraints aren't met.
type UpdateContactRequest_UpdateRefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateContactRequest_UpdateRefMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateContactRequest_UpdateRefMultiError) AllErrors() []error { return m }

// UpdateContactRequest_UpdateRefValidationError is the validation error
// returned by UpdateContactRequest_UpdateRef.Validate if the designated
// constraints aren't met.
type UpdateContactRequest_UpdateRefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateContactRequest_UpdateRefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateContactRequest_UpdateRefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateContactRequest_UpdateRefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateContactRequest_UpdateRefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateContactRequest_UpdateRefValidationError) ErrorName() string {
	return "UpdateContactRequest_UpdateRefValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateContactRequest_UpdateRefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateContactRequest_UpdateRef.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateContactRequest_UpdateRefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateContactRequest_UpdateRefValidationError{}

// Validate checks the field values on ListCustomerRelatedDataRequest_Filter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListCustomerRelatedDataRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerRelatedDataRequest_Filter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListCustomerRelatedDataRequest_FilterMultiError, or nil if none found.
func (m *ListCustomerRelatedDataRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerRelatedDataRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOrganization()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCustomerRelatedDataRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCustomerRelatedDataRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrganization()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCustomerRelatedDataRequest_FilterValidationError{
				field:  "Organization",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCustomerRelatedDataRequest_FilterMultiError(errors)
	}

	return nil
}

// ListCustomerRelatedDataRequest_FilterMultiError is an error wrapping
// multiple validation errors returned by
// ListCustomerRelatedDataRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type ListCustomerRelatedDataRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerRelatedDataRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerRelatedDataRequest_FilterMultiError) AllErrors() []error { return m }

// ListCustomerRelatedDataRequest_FilterValidationError is the validation error
// returned by ListCustomerRelatedDataRequest_Filter.Validate if the
// designated constraints aren't met.
type ListCustomerRelatedDataRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerRelatedDataRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerRelatedDataRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerRelatedDataRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerRelatedDataRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerRelatedDataRequest_FilterValidationError) ErrorName() string {
	return "ListCustomerRelatedDataRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerRelatedDataRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerRelatedDataRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerRelatedDataRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerRelatedDataRequest_FilterValidationError{}

// Validate checks the field values on ListCustomerRelatedDataRequest_Sorting
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListCustomerRelatedDataRequest_Sorting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListCustomerRelatedDataRequest_Sorting with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ListCustomerRelatedDataRequest_SortingMultiError, or nil if none found.
func (m *ListCustomerRelatedDataRequest_Sorting) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerRelatedDataRequest_Sorting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Direction

	if len(errors) > 0 {
		return ListCustomerRelatedDataRequest_SortingMultiError(errors)
	}

	return nil
}

// ListCustomerRelatedDataRequest_SortingMultiError is an error wrapping
// multiple validation errors returned by
// ListCustomerRelatedDataRequest_Sorting.ValidateAll() if the designated
// constraints aren't met.
type ListCustomerRelatedDataRequest_SortingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerRelatedDataRequest_SortingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerRelatedDataRequest_SortingMultiError) AllErrors() []error { return m }

// ListCustomerRelatedDataRequest_SortingValidationError is the validation
// error returned by ListCustomerRelatedDataRequest_Sorting.Validate if the
// designated constraints aren't met.
type ListCustomerRelatedDataRequest_SortingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerRelatedDataRequest_SortingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerRelatedDataRequest_SortingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerRelatedDataRequest_SortingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerRelatedDataRequest_SortingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerRelatedDataRequest_SortingValidationError) ErrorName() string {
	return "ListCustomerRelatedDataRequest_SortingValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerRelatedDataRequest_SortingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerRelatedDataRequest_Sorting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerRelatedDataRequest_SortingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerRelatedDataRequest_SortingValidationError{}

// Validate checks the field values on
// UpdateCustomerRelatedDataRequest_UpdateRef with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerRelatedDataRequest_UpdateRef) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateCustomerRelatedDataRequest_UpdateRef with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateCustomerRelatedDataRequest_UpdateRefMultiError, or nil if none found.
func (m *UpdateCustomerRelatedDataRequest_UpdateRef) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerRelatedDataRequest_UpdateRef) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.ClientColor != nil {
		// no validation rules for ClientColor
	}

	if m.IsBlockMessage != nil {
		// no validation rules for IsBlockMessage
	}

	if m.IsBlockOnlineBooking != nil {
		// no validation rules for IsBlockOnlineBooking
	}

	if m.LoginEmail != nil {
		// no validation rules for LoginEmail
	}

	if m.ReferralSourceId != nil {
		// no validation rules for ReferralSourceId
	}

	if m.ReferralSourceDesc != nil {
		// no validation rules for ReferralSourceDesc
	}

	if m.SendAutoEmail != nil {
		// no validation rules for SendAutoEmail
	}

	if m.SendAutoMessage != nil {
		// no validation rules for SendAutoMessage
	}

	if m.SendAppAutoMessage != nil {
		// no validation rules for SendAppAutoMessage
	}

	if m.PreferredGroomerId != nil {
		// no validation rules for PreferredGroomerId
	}

	if m.PreferredFrequencyDay != nil {
		// no validation rules for PreferredFrequencyDay
	}

	if m.PreferredFrequencyType != nil {
		// no validation rules for PreferredFrequencyType
	}

	if m.PreferredDay != nil {
		// no validation rules for PreferredDay
	}

	if m.PreferredTime != nil {
		// no validation rules for PreferredTime
	}

	if m.IsUnsubscribed != nil {
		// no validation rules for IsUnsubscribed
	}

	if m.Birthday != nil {

		if all {
			switch v := interface{}(m.GetBirthday()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateCustomerRelatedDataRequest_UpdateRefValidationError{
						field:  "Birthday",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateCustomerRelatedDataRequest_UpdateRefValidationError{
						field:  "Birthday",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBirthday()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateCustomerRelatedDataRequest_UpdateRefValidationError{
					field:  "Birthday",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.CustomizeLifeCycleId != nil {
		// no validation rules for CustomizeLifeCycleId
	}

	if m.CustomizeActionStateId != nil {
		// no validation rules for CustomizeActionStateId
	}

	if len(errors) > 0 {
		return UpdateCustomerRelatedDataRequest_UpdateRefMultiError(errors)
	}

	return nil
}

// UpdateCustomerRelatedDataRequest_UpdateRefMultiError is an error wrapping
// multiple validation errors returned by
// UpdateCustomerRelatedDataRequest_UpdateRef.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerRelatedDataRequest_UpdateRefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerRelatedDataRequest_UpdateRefMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerRelatedDataRequest_UpdateRefMultiError) AllErrors() []error { return m }

// UpdateCustomerRelatedDataRequest_UpdateRefValidationError is the validation
// error returned by UpdateCustomerRelatedDataRequest_UpdateRef.Validate if
// the designated constraints aren't met.
type UpdateCustomerRelatedDataRequest_UpdateRefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerRelatedDataRequest_UpdateRefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerRelatedDataRequest_UpdateRefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerRelatedDataRequest_UpdateRefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerRelatedDataRequest_UpdateRefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerRelatedDataRequest_UpdateRefValidationError) ErrorName() string {
	return "UpdateCustomerRelatedDataRequest_UpdateRefValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerRelatedDataRequest_UpdateRefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerRelatedDataRequest_UpdateRef.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerRelatedDataRequest_UpdateRefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerRelatedDataRequest_UpdateRefValidationError{}

// Validate checks the field values on ListContactTagsRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListContactTagsRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListContactTagsRequest_Filter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListContactTagsRequest_FilterMultiError, or nil if none found.
func (m *ListContactTagsRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListContactTagsRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOrganization() == nil {
		err := ListContactTagsRequest_FilterValidationError{
			field:  "Organization",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOrganization()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListContactTagsRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListContactTagsRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrganization()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListContactTagsRequest_FilterValidationError{
				field:  "Organization",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Name != nil {
		// no validation rules for Name
	}

	if len(errors) > 0 {
		return ListContactTagsRequest_FilterMultiError(errors)
	}

	return nil
}

// ListContactTagsRequest_FilterMultiError is an error wrapping multiple
// validation errors returned by ListContactTagsRequest_Filter.ValidateAll()
// if the designated constraints aren't met.
type ListContactTagsRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListContactTagsRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListContactTagsRequest_FilterMultiError) AllErrors() []error { return m }

// ListContactTagsRequest_FilterValidationError is the validation error
// returned by ListContactTagsRequest_Filter.Validate if the designated
// constraints aren't met.
type ListContactTagsRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListContactTagsRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListContactTagsRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListContactTagsRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListContactTagsRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListContactTagsRequest_FilterValidationError) ErrorName() string {
	return "ListContactTagsRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListContactTagsRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListContactTagsRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListContactTagsRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListContactTagsRequest_FilterValidationError{}

// Validate checks the field values on ListContactTagsRequest_Sorting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListContactTagsRequest_Sorting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListContactTagsRequest_Sorting with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListContactTagsRequest_SortingMultiError, or nil if none found.
func (m *ListContactTagsRequest_Sorting) ValidateAll() error {
	return m.validate(true)
}

func (m *ListContactTagsRequest_Sorting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Direction

	if len(errors) > 0 {
		return ListContactTagsRequest_SortingMultiError(errors)
	}

	return nil
}

// ListContactTagsRequest_SortingMultiError is an error wrapping multiple
// validation errors returned by ListContactTagsRequest_Sorting.ValidateAll()
// if the designated constraints aren't met.
type ListContactTagsRequest_SortingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListContactTagsRequest_SortingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListContactTagsRequest_SortingMultiError) AllErrors() []error { return m }

// ListContactTagsRequest_SortingValidationError is the validation error
// returned by ListContactTagsRequest_Sorting.Validate if the designated
// constraints aren't met.
type ListContactTagsRequest_SortingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListContactTagsRequest_SortingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListContactTagsRequest_SortingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListContactTagsRequest_SortingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListContactTagsRequest_SortingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListContactTagsRequest_SortingValidationError) ErrorName() string {
	return "ListContactTagsRequest_SortingValidationError"
}

// Error satisfies the builtin error interface
func (e ListContactTagsRequest_SortingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListContactTagsRequest_Sorting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListContactTagsRequest_SortingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListContactTagsRequest_SortingValidationError{}

// Validate checks the field values on UpdateContactTagRequest_UpdateRef with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateContactTagRequest_UpdateRef) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateContactTagRequest_UpdateRef
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateContactTagRequest_UpdateRefMultiError, or nil if none found.
func (m *UpdateContactTagRequest_UpdateRef) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateContactTagRequest_UpdateRef) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Color != nil {
		// no validation rules for Color
	}

	if m.SortOrder != nil {
		// no validation rules for SortOrder
	}

	if len(errors) > 0 {
		return UpdateContactTagRequest_UpdateRefMultiError(errors)
	}

	return nil
}

// UpdateContactTagRequest_UpdateRefMultiError is an error wrapping multiple
// validation errors returned by
// UpdateContactTagRequest_UpdateRef.ValidateAll() if the designated
// constraints aren't met.
type UpdateContactTagRequest_UpdateRefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateContactTagRequest_UpdateRefMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateContactTagRequest_UpdateRefMultiError) AllErrors() []error { return m }

// UpdateContactTagRequest_UpdateRefValidationError is the validation error
// returned by UpdateContactTagRequest_UpdateRef.Validate if the designated
// constraints aren't met.
type UpdateContactTagRequest_UpdateRefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateContactTagRequest_UpdateRefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateContactTagRequest_UpdateRefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateContactTagRequest_UpdateRefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateContactTagRequest_UpdateRefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateContactTagRequest_UpdateRefValidationError) ErrorName() string {
	return "UpdateContactTagRequest_UpdateRefValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateContactTagRequest_UpdateRefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateContactTagRequest_UpdateRef.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateContactTagRequest_UpdateRefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateContactTagRequest_UpdateRefValidationError{}

// Validate checks the field values on ListLeadsRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLeadsRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLeadsRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLeadsRequest_FilterMultiError, or nil if none found.
func (m *ListLeadsRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLeadsRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOrganization()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListLeadsRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListLeadsRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrganization()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListLeadsRequest_FilterValidationError{
				field:  "Organization",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListLeadsRequest_FilterMultiError(errors)
	}

	return nil
}

// ListLeadsRequest_FilterMultiError is an error wrapping multiple validation
// errors returned by ListLeadsRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type ListLeadsRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLeadsRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLeadsRequest_FilterMultiError) AllErrors() []error { return m }

// ListLeadsRequest_FilterValidationError is the validation error returned by
// ListLeadsRequest_Filter.Validate if the designated constraints aren't met.
type ListLeadsRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLeadsRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLeadsRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLeadsRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLeadsRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLeadsRequest_FilterValidationError) ErrorName() string {
	return "ListLeadsRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListLeadsRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLeadsRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLeadsRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLeadsRequest_FilterValidationError{}

// Validate checks the field values on ListLeadsRequest_Sorting with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLeadsRequest_Sorting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLeadsRequest_Sorting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLeadsRequest_SortingMultiError, or nil if none found.
func (m *ListLeadsRequest_Sorting) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLeadsRequest_Sorting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Direction

	if len(errors) > 0 {
		return ListLeadsRequest_SortingMultiError(errors)
	}

	return nil
}

// ListLeadsRequest_SortingMultiError is an error wrapping multiple validation
// errors returned by ListLeadsRequest_Sorting.ValidateAll() if the designated
// constraints aren't met.
type ListLeadsRequest_SortingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLeadsRequest_SortingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLeadsRequest_SortingMultiError) AllErrors() []error { return m }

// ListLeadsRequest_SortingValidationError is the validation error returned by
// ListLeadsRequest_Sorting.Validate if the designated constraints aren't met.
type ListLeadsRequest_SortingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLeadsRequest_SortingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLeadsRequest_SortingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLeadsRequest_SortingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLeadsRequest_SortingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLeadsRequest_SortingValidationError) ErrorName() string {
	return "ListLeadsRequest_SortingValidationError"
}

// Error satisfies the builtin error interface
func (e ListLeadsRequest_SortingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLeadsRequest_Sorting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLeadsRequest_SortingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLeadsRequest_SortingValidationError{}

// Validate checks the field values on UpdateLeadRequest_UpdateRef with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLeadRequest_UpdateRef) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLeadRequest_UpdateRef with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLeadRequest_UpdateRefMultiError, or nil if none found.
func (m *UpdateLeadRequest_UpdateRef) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLeadRequest_UpdateRef) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GivenName != nil {
		// no validation rules for GivenName
	}

	if m.FamilyName != nil {
		// no validation rules for FamilyName
	}

	if m.CustomFields != nil {

		if all {
			switch v := interface{}(m.GetCustomFields()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateLeadRequest_UpdateRefValidationError{
						field:  "CustomFields",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateLeadRequest_UpdateRefValidationError{
						field:  "CustomFields",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCustomFields()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateLeadRequest_UpdateRefValidationError{
					field:  "CustomFields",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.LifecycleId != nil {
		// no validation rules for LifecycleId
	}

	if m.OwnerStaffId != nil {
		// no validation rules for OwnerStaffId
	}

	if m.State != nil {
		// no validation rules for State
	}

	if len(errors) > 0 {
		return UpdateLeadRequest_UpdateRefMultiError(errors)
	}

	return nil
}

// UpdateLeadRequest_UpdateRefMultiError is an error wrapping multiple
// validation errors returned by UpdateLeadRequest_UpdateRef.ValidateAll() if
// the designated constraints aren't met.
type UpdateLeadRequest_UpdateRefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLeadRequest_UpdateRefMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLeadRequest_UpdateRefMultiError) AllErrors() []error { return m }

// UpdateLeadRequest_UpdateRefValidationError is the validation error returned
// by UpdateLeadRequest_UpdateRef.Validate if the designated constraints
// aren't met.
type UpdateLeadRequest_UpdateRefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLeadRequest_UpdateRefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLeadRequest_UpdateRefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLeadRequest_UpdateRefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLeadRequest_UpdateRefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLeadRequest_UpdateRefValidationError) ErrorName() string {
	return "UpdateLeadRequest_UpdateRefValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLeadRequest_UpdateRefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLeadRequest_UpdateRef.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLeadRequest_UpdateRefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLeadRequest_UpdateRefValidationError{}

// Validate checks the field values on ListAddressesRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAddressesRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAddressesRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAddressesRequest_FilterMultiError, or nil if none found.
func (m *ListAddressesRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAddressesRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCustomerId() <= 0 {
		err := ListAddressesRequest_FilterValidationError{
			field:  "CustomerId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListAddressesRequest_FilterMultiError(errors)
	}

	return nil
}

// ListAddressesRequest_FilterMultiError is an error wrapping multiple
// validation errors returned by ListAddressesRequest_Filter.ValidateAll() if
// the designated constraints aren't met.
type ListAddressesRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAddressesRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAddressesRequest_FilterMultiError) AllErrors() []error { return m }

// ListAddressesRequest_FilterValidationError is the validation error returned
// by ListAddressesRequest_Filter.Validate if the designated constraints
// aren't met.
type ListAddressesRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAddressesRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAddressesRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAddressesRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAddressesRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAddressesRequest_FilterValidationError) ErrorName() string {
	return "ListAddressesRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListAddressesRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAddressesRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAddressesRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAddressesRequest_FilterValidationError{}

// Validate checks the field values on ListAddressesRequest_Sorting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAddressesRequest_Sorting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAddressesRequest_Sorting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAddressesRequest_SortingMultiError, or nil if none found.
func (m *ListAddressesRequest_Sorting) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAddressesRequest_Sorting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Direction

	if len(errors) > 0 {
		return ListAddressesRequest_SortingMultiError(errors)
	}

	return nil
}

// ListAddressesRequest_SortingMultiError is an error wrapping multiple
// validation errors returned by ListAddressesRequest_Sorting.ValidateAll() if
// the designated constraints aren't met.
type ListAddressesRequest_SortingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAddressesRequest_SortingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAddressesRequest_SortingMultiError) AllErrors() []error { return m }

// ListAddressesRequest_SortingValidationError is the validation error returned
// by ListAddressesRequest_Sorting.Validate if the designated constraints
// aren't met.
type ListAddressesRequest_SortingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAddressesRequest_SortingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAddressesRequest_SortingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAddressesRequest_SortingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAddressesRequest_SortingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAddressesRequest_SortingValidationError) ErrorName() string {
	return "ListAddressesRequest_SortingValidationError"
}

// Error satisfies the builtin error interface
func (e ListAddressesRequest_SortingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAddressesRequest_Sorting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAddressesRequest_SortingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAddressesRequest_SortingValidationError{}

// Validate checks the field values on UpdateAddressRequest_UpdateRef with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAddressRequest_UpdateRef) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAddressRequest_UpdateRef with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateAddressRequest_UpdateRefMultiError, or nil if none found.
func (m *UpdateAddressRequest_UpdateRef) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAddressRequest_UpdateRef) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAddressRequest_UpdateRefValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAddressRequest_UpdateRefValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAddressRequest_UpdateRefValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Type != nil {
		// no validation rules for Type
	}

	if len(errors) > 0 {
		return UpdateAddressRequest_UpdateRefMultiError(errors)
	}

	return nil
}

// UpdateAddressRequest_UpdateRefMultiError is an error wrapping multiple
// validation errors returned by UpdateAddressRequest_UpdateRef.ValidateAll()
// if the designated constraints aren't met.
type UpdateAddressRequest_UpdateRefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAddressRequest_UpdateRefMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAddressRequest_UpdateRefMultiError) AllErrors() []error { return m }

// UpdateAddressRequest_UpdateRefValidationError is the validation error
// returned by UpdateAddressRequest_UpdateRef.Validate if the designated
// constraints aren't met.
type UpdateAddressRequest_UpdateRefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAddressRequest_UpdateRefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAddressRequest_UpdateRefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAddressRequest_UpdateRefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAddressRequest_UpdateRefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAddressRequest_UpdateRefValidationError) ErrorName() string {
	return "UpdateAddressRequest_UpdateRefValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAddressRequest_UpdateRefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAddressRequest_UpdateRef.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAddressRequest_UpdateRefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAddressRequest_UpdateRefValidationError{}

// Validate checks the field values on ListCustomFieldsRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomFieldsRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomFieldsRequest_Filter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCustomFieldsRequest_FilterMultiError, or nil if none found.
func (m *ListCustomFieldsRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomFieldsRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOrganization() == nil {
		err := ListCustomFieldsRequest_FilterValidationError{
			field:  "Organization",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOrganization()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCustomFieldsRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCustomFieldsRequest_FilterValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrganization()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCustomFieldsRequest_FilterValidationError{
				field:  "Organization",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AssociationType

	if m.IsRequired != nil {
		// no validation rules for IsRequired
	}

	if len(errors) > 0 {
		return ListCustomFieldsRequest_FilterMultiError(errors)
	}

	return nil
}

// ListCustomFieldsRequest_FilterMultiError is an error wrapping multiple
// validation errors returned by ListCustomFieldsRequest_Filter.ValidateAll()
// if the designated constraints aren't met.
type ListCustomFieldsRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomFieldsRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomFieldsRequest_FilterMultiError) AllErrors() []error { return m }

// ListCustomFieldsRequest_FilterValidationError is the validation error
// returned by ListCustomFieldsRequest_Filter.Validate if the designated
// constraints aren't met.
type ListCustomFieldsRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomFieldsRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomFieldsRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomFieldsRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomFieldsRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomFieldsRequest_FilterValidationError) ErrorName() string {
	return "ListCustomFieldsRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomFieldsRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomFieldsRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomFieldsRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomFieldsRequest_FilterValidationError{}

// Validate checks the field values on ListCustomFieldsRequest_Sorting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomFieldsRequest_Sorting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomFieldsRequest_Sorting with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCustomFieldsRequest_SortingMultiError, or nil if none found.
func (m *ListCustomFieldsRequest_Sorting) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomFieldsRequest_Sorting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Direction

	if len(errors) > 0 {
		return ListCustomFieldsRequest_SortingMultiError(errors)
	}

	return nil
}

// ListCustomFieldsRequest_SortingMultiError is an error wrapping multiple
// validation errors returned by ListCustomFieldsRequest_Sorting.ValidateAll()
// if the designated constraints aren't met.
type ListCustomFieldsRequest_SortingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomFieldsRequest_SortingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomFieldsRequest_SortingMultiError) AllErrors() []error { return m }

// ListCustomFieldsRequest_SortingValidationError is the validation error
// returned by ListCustomFieldsRequest_Sorting.Validate if the designated
// constraints aren't met.
type ListCustomFieldsRequest_SortingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomFieldsRequest_SortingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomFieldsRequest_SortingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomFieldsRequest_SortingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomFieldsRequest_SortingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomFieldsRequest_SortingValidationError) ErrorName() string {
	return "ListCustomFieldsRequest_SortingValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomFieldsRequest_SortingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomFieldsRequest_Sorting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomFieldsRequest_SortingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomFieldsRequest_SortingValidationError{}

// Validate checks the field values on UpdateCustomFieldRequest_UpdateRef with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateCustomFieldRequest_UpdateRef) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomFieldRequest_UpdateRef
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateCustomFieldRequest_UpdateRefMultiError, or nil if none found.
func (m *UpdateCustomFieldRequest_UpdateRef) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomFieldRequest_UpdateRef) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Type != nil {
		// no validation rules for Type
	}

	if m.IsRequired != nil {
		// no validation rules for IsRequired
	}

	if m.State != nil {
		// no validation rules for State
	}

	if len(errors) > 0 {
		return UpdateCustomFieldRequest_UpdateRefMultiError(errors)
	}

	return nil
}

// UpdateCustomFieldRequest_UpdateRefMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCustomFieldRequest_UpdateRef.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomFieldRequest_UpdateRefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomFieldRequest_UpdateRefMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomFieldRequest_UpdateRefMultiError) AllErrors() []error { return m }

// UpdateCustomFieldRequest_UpdateRefValidationError is the validation error
// returned by UpdateCustomFieldRequest_UpdateRef.Validate if the designated
// constraints aren't met.
type UpdateCustomFieldRequest_UpdateRefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomFieldRequest_UpdateRefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomFieldRequest_UpdateRefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomFieldRequest_UpdateRefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomFieldRequest_UpdateRefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomFieldRequest_UpdateRefValidationError) ErrorName() string {
	return "UpdateCustomFieldRequest_UpdateRefValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomFieldRequest_UpdateRefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomFieldRequest_UpdateRef.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomFieldRequest_UpdateRefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomFieldRequest_UpdateRefValidationError{}
