package salesforce

import (
	"context"
	"fmt"
	"strings"

	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
)

type Account struct {
	ID    *string `json:"Id,omitempty"`
	Email *string `json:"MoeGo_Log_in_Email__c,omitempty"`
	Name  *string `json:"Name,omitempty"`
}

func (i *impl) GetAccountByEmail(ctx context.Context, email string) (*Account, error) {
	query := fmt.Sprintf("q=SELECT+%s+FROM+Account+WHERE+MoeGo_Log_in_Email__c='%s'",
		strings.Join(salesutils.ParseStructJSONTagNames(Account{}), ","),
		email,
	)
	result := &queryResult[*Account]{}

	if err := i.Query(ctx, query, result); err != nil {
		return nil, err
	}

	accounts := result.Records
	if len(accounts) == 0 {
		return nil, nil
	}

	return accounts[0], nil
}
