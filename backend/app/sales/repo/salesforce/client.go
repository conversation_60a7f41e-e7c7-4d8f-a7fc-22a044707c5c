package salesforce

import (
	"context"
	"fmt"
	"sync"

	"github.com/MoeGolibrary/moego/backend/app/sales/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
)

var (
	pricebookID = "01sHs000008APktIAG"
	c           *impl
	once        sync.Once
)

type Client interface {
	GetProduct(ctx context.Context, name string) *Product

	GetOpportunity(ctx context.Context, id string) (*Opportunity, error)
	UpdateOpportunity(ctx context.Context, op *Opportunity) error

	ListOpportunityLineItems(ctx context.Context, opportunityID string, productIDs ...string) (
		[]*OpportunityLineItem, error)
	CreateOpportunityLineItems(ctx context.Context, items []*OpportunityLineItem) error
	DeleteOpportunityLineItems(ctx context.Context, ids []string) error

	// CreateContract creates a contract in Salesforce.
	// The contract ID will be populated in the contract object upon success.
	CreateContract(ctx context.Context, contract *Contract) error

	GetAccountByEmail(ctx context.Context, email string) (*Account, error)
}

// 这里主要是为了演示 config 的使用，以 获取 stripe 的配置信息为例
type impl struct {
	clientID, clientSecret string
	client                 http.Client
}

type accessToken struct {
	Token string `json:"access_token"`
	Type  string `json:"token_type"`
}

func New(cfg *config.Salesforce) Client {
	once.Do(func() {
		checkSalesforceConfig(cfg)
		// init client
		c = &impl{
			clientID:     cfg.ClientID,
			clientSecret: cfg.ClientSecret,
			client:       http.NewClientProxy("salesforce-api"),
		}

		err := c.initProducts(context.Background())
		if err != nil {
			panic(err)
		}
	})

	return c
}

func checkSalesforceConfig(cfg *config.Salesforce) {
	if cfg == nil {
		panic("config is nil")
	}
	if cfg.InstanceURL == "" {
		panic("instance url is empty")
	}
	if cfg.ClientID == "" {
		panic("client id is empty")
	}
	if cfg.ClientSecret == "" {
		panic("client secret is empty")
	}
}

func (i *impl) refreshAccessToken(ctx context.Context) (*accessToken, error) {

	token := &accessToken{}

	//body := url.Values{}
	//body.Set("grant_type", "client_credentials")
	//body.Set("client_id", i.clientID)
	//body.Set("client_secret", i.clientSecret)

	//path := "/services/oauth2/token"
	//opt := client.WithCurrentSerializationType(codec.SerializationTypeForm)
	//if err := i.client.Post(ctx, path, m, token, opt); err != nil {
	//	return nil, err
	//}

	path := fmt.Sprintf("/services/oauth2/token?grant_type=client_credentials&client_id=%s&client_secret=%s",
		i.clientID, i.clientSecret)
	if err := i.client.Post(ctx, path, nil, token); err != nil {
		return nil, err
	}

	return token, nil
}

func (i *impl) getHeaders(ctx context.Context) (*http.ClientReqHeader, error) {
	token, err := i.refreshAccessToken(ctx)
	if err != nil {
		return nil, err
	}
	headers := &http.ClientReqHeader{}
	headers.AddHeader("Authorization", token.Type+" "+token.Token)
	headers.AddHeader("Content-Type", "application/json")

	return headers, nil
}
