package sales

import (
	"bytes"
	"context"
	"html/template"
	"time"

	"gorm.io/gorm"
)

const (
	MoegoPayContractName = "MoeGo Pay contract"
	AnnualContractName   = "Annual contract"
)

type ContractTemplate struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Template  string     `json:"template"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty"`
}

func (c *ContractTemplate) TableName() string {
	return "moego_sales.public.contract_template"
}

func (c *ContractTemplate) Render(v any) (string, error) {
	tpl := template.Must(template.New(c.Name).Parse(c.Template))

	var buf bytes.Buffer
	if err := tpl.Execute(&buf, v); err != nil {
		return "", err
	}

	result := buf.String()

	return result, nil
}

type ContractTemplateReadWriter interface {
	Get(ctx context.Context, id string) (*ContractTemplate, error)
	List(ctx context.Context, filters ...ContractTemplateQueryOption) ([]*ContractTemplate, error)
	GetNewestTemplate(ctx context.Context, name string) (*ContractTemplate, error)
}

type contractTemplateRWImpl struct {
	db *gorm.DB
}

func NewContractTemplateRW() ContractTemplateReadWriter {
	return &contractTemplateRWImpl{
		db: NewDB(),
	}
}

func (i *contractTemplateRWImpl) List(ctx context.Context, filters ...ContractTemplateQueryOption) (
	[]*ContractTemplate, error) {
	sql := i.db.WithContext(ctx)
	for _, filter := range filters {
		sql = filter(sql)
	}
	sql.Order("id DESC")

	var templates []*ContractTemplate
	if err := sql.Find(&templates).Error; err != nil {
		return nil, err
	}

	return templates, nil
}

func (i *contractTemplateRWImpl) Get(ctx context.Context, id string) (*ContractTemplate, error) {
	t := &ContractTemplate{}
	if err := i.db.WithContext(ctx).Where("id = ?", id).First(t).Error; err != nil {
		return nil, err
	}

	return t, nil
}

func (i *contractTemplateRWImpl) GetNewestTemplate(ctx context.Context, name string) (*ContractTemplate, error) {
	t := &ContractTemplate{}
	if err := i.db.WithContext(ctx).
		Where("name = ?", name).
		Where("deleted_at is null").
		Order("version DESC").
		First(t).Error; err != nil {
		return nil, err
	}

	return t, nil
}

type ContractTemplateQueryOption func(*gorm.DB) *gorm.DB

func WithContractTemplateName(name string) ContractTemplateQueryOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("name = ?", name)
	}
}
