
create table salesforce_opportunity (
    id                    TEXT primary key,
    salesforce_account_id TEXT,
    email                 TEXT,
    tier                  TEXT,

    terminal_percentage     NUMERIC(5, 2),
    terminal_fixed          NUMERIC(12, 2),
    non_terminal_percentage NUMERIC(5, 2),
    non_terminal_fixed      NUMERIC(12, 2),
    min_volume              NUMERIC(12, 2),

    spif                    NUMERIC(12, 2),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

comment on table salesforce_opportunity is 'Salesforce Opportunity';
comment on column salesforce_opportunity.id is 'Salesforce Opportunity ID';
comment on column salesforce_opportunity.salesforce_account_id is 'Account ID related to this opportunity in Salesforce';
comment on column salesforce_opportunity.email is 'Email of the customer';
comment on column salesforce_opportunity.tier is 'Tier of the customer, e.g., T1, T2';
comment on column salesforce_opportunity.terminal_percentage is 'Percentage for terminal transactions';
comment on column salesforce_opportunity.terminal_fixed is 'Fixed amount in dollars for terminal transactions';
comment on column salesforce_opportunity.non_terminal_percentage is 'Percentage for non-terminal transactions';
comment on column salesforce_opportunity.non_terminal_fixed is 'Fixed amount in dollars for non-terminal transactions';
comment on column salesforce_opportunity.min_volume is 'Minimum transaction volume required for Moego Pay';
comment on column salesforce_opportunity.spif is 'Sales Performance Incentive Fund for MoeGo Pay custom percentage';
comment on column salesforce_opportunity.created_at is 'Timestamp of when the record was created';
comment on column salesforce_opportunity.updated_at is 'Timestamp of when the record was last updated';


create table salesforce_opportunity_line_item (
    id bigserial primary key,
    opportunity_id TEXT       not null,
    product_id TEXT           not null,
    quantity int              not null,
    unit_price NUMERIC(12, 2) not null,
    discount_percentage NUMERIC(5, 2),
    contract_link   TEXT,
    contract_type   TEXT,
    contract_signed boolean,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

create index opportunity_line_item_opportunity_product_idx on salesforce_opportunity_line_item (opportunity_id, product_id);

comment on table salesforce_opportunity_line_item is 'Salesforce Opportunity Product Line Item';
comment on column salesforce_opportunity_line_item.id is 'Internal ID for the line item, may not be used';
comment on column salesforce_opportunity_line_item.opportunity_id is 'Salesforce Opportunity ID';
comment on column salesforce_opportunity_line_item.product_id is 'Salesforce Product ID';
comment on column salesforce_opportunity_line_item.quantity is 'Quantity of the product';
comment on column salesforce_opportunity_line_item.unit_price is 'Unit price of the product (after discount)';
comment on column salesforce_opportunity_line_item.discount_percentage is 'Discount percentage';
comment on column salesforce_opportunity_line_item.contract_link is 'Link of the contract';
comment on column salesforce_opportunity_line_item.contract_type is 'Type of the contract';
comment on column salesforce_opportunity_line_item.contract_signed is 'Whether the contract is signed';
comment on column salesforce_opportunity_line_item.created_at is 'Timestamp of when the record was created';
comment on column salesforce_opportunity_line_item.updated_at is 'Timestamp of when the record was last updated';