package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Group Class Enroll Parameters
 * 团体课程报名参数
 */
@Data
@Schema(description = "团体课程报名参数")
public class GroupClassEnrollParams {

    @NotNull
    @Positive
    @Schema(description = "预约ID", required = true)
    private Integer appointmentId;

    @NotNull
    @Positive
    @Schema(description = "客户ID", required = true)
    private Integer customerId;

    @NotNull
    @Schema(description = "宠物ID列表", required = true)
    private List<Integer> petIds;

    @Schema(description = "业务ID")
    private Integer businessId;

    @Schema(description = "公司ID")
    private Long companyId;

    @Schema(description = "操作员工ID")
    private Integer staffId;

    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;

    @Schema(description = "支付方式")
    private String paymentMethod;

    @Schema(description = "备注")
    private String notes;
}
