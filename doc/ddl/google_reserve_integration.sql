create table if not exists public.google_reserve_integration
(
  id          bigserial
    constraint google_reserve_integration_pk
    primary key,
  business_id integer   default 0                 not null,
  enabled     boolean   default false             not null,
  create_time timestamp default CURRENT_TIMESTAMP not null,
  update_time timestamp default CURRENT_TIMESTAMP not null,
  status      integer   default 0                 not null
);

comment on table public.google_reserve_integration is 'Google Reserve 集成';
comment on column public.google_reserve_integration.id is 'id';
comment on column public.google_reserve_integration.business_id is 'business id';
comment on column public.google_reserve_integration.enabled is '是否开启 Google Reserve';
comment on column public.google_reserve_integration.create_time is 'create time';
comment on column public.google_reserve_integration.update_time is 'update time';
comment on column public.google_reserve_integration.status is '集成状态(GoogleReserveIntegrationStatus), 0-configured, 1-unmatched, 2-matched';

create unique index if not exists google_reserve_integration_business_id_uindex
  on public.google_reserve_integration (business_id);
