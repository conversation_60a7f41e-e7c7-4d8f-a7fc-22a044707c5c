package salesforce

import "strings"

type CreateResponse struct {
	ID      *string        `json:"id"`
	Success *bool          `json:"success"`
	Errors  []ErrorMessage `json:"errors"`
}

func (r *CreateResponse) IsSuccess() bool {
	if r == nil {
		return false
	}

	return *r.Success
}

func (r *CreateResponse) GetMergedErrorMessage() string {
	if r == nil {
		return ""
	}

	var messages []string
	for _, err := range r.Errors {
		if err.Message != nil {
			messages = append(messages, *err.Message)
		}
	}

	return strings.Join(messages, " ")
}

type ErrorMessage struct {
	Message   *string `json:"message"`
	ErrorCode *string `json:"errorCode"`
}
