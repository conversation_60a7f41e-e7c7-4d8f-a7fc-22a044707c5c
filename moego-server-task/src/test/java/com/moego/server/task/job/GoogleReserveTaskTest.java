package com.moego.server.task.job;

import com.moego.server.business.dto.MoeBusinessDto;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link GoogleReserveTask} tester.
 */
@SpringBootTest
@Disabled("local test only")
class GoogleReserveTaskTest {

    @Autowired
    GoogleReserveTask task;

    /**
     * {@link GoogleReserveTask#sendGoogleReserveIntegrationDailySummary()}
     */
    @Test
    void testSendGoogleReserveIntegrationDailySummary() {
        task.sendGoogleReserveIntegrationDailySummary();
    }

    /**
     * {@link GoogleReserveTask#sendMessageToSlack(GoogleReserveTask.SlackGoogleReserveDailySummaryRequestBody)}
     */
    @Test
    void testSendMessageToSlack() {
        GoogleReserveTask.SlackGoogleReserveDailySummaryRequestBody body =
                new GoogleReserveTask.SlackGoogleReserveDailySummaryRequestBody();
        body.setBookingRequestsFromGoogle(10);
        body.setTotalConnectedBusinessCount(20);
        body.setTotalMatchedBusinessCount(15);
        body.setTodayConnectedBusiness(mockBusinesses(5));
        body.setBusinessToCount(mockBusinesses(10).stream()
                .collect(Collectors.toMap(Function.identity(), it -> (long) (Math.random() * 10L))));
        task.sendMessageToSlack(body);
    }

    /**
     * {@link GoogleReserveTask#sendFeeds()}
     */
    @Test
    void testSendFeeds() {
        task.sendFeeds();
    }

    private static List<MoeBusinessDto> mockBusinesses(int count) {
        List<MoeBusinessDto> result = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            MoeBusinessDto dto = new MoeBusinessDto();
            dto.setId(i);
            dto.setOwnerEmail(i + "@moego.pet");
            dto.setBusinessName("business-" + i);
            result.add(dto);
        }
        return result;
    }

    /**
     * {@link GoogleReserveTask#fetchBusinessToGoogleAppointmentCount(List)}
     */
    @Test
    void testFetchBusinessToGoogleAppointmentCount() {
        var bizIds = IntStream.rangeClosed(100600, 100620).boxed().toList();
        task.fetchBusinessToGoogleAppointmentCount(bizIds);
    }
}
