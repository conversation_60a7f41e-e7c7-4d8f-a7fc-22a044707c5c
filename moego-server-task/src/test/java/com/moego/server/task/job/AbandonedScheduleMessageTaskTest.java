package com.moego.server.task.job;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link AbandonedScheduleMessageTask} tester.
 */
@SpringBootTest
@Disabled("Only for local test")
class AbandonedScheduleMessageTaskTest {

    @Autowired
    AbandonedScheduleMessageTask task;

    /**
     * {@link AbandonedScheduleMessageTask#sendAbandonedScheduleMessage()}
     */
    @Test
    void testSendAbandonedScheduleMessage() {
        task.sendAbandonedScheduleMessage();
    }
}
