package com.moego.server.task.job;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link SendRenewEndDateNotificationTask} tester.
 */
@SpringBootTest
@Disabled("For local test only")
class SendRenewEndDateNotificationTaskTest {

    @Autowired
    SendRenewEndDateNotificationTask task;

    /**
     * {@link SendRenewEndDateNotificationTask#sendRenewEndDateNotification()}
     */
    @Test
    void testSendRenewEndDateNotification() {
        task.sendRenewEndDateNotification();
    }
}
