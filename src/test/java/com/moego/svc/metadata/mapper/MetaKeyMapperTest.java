/*
 * @since 2023-04-14 14:03:53
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.metadata.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotSame;

import com.moego.svc.metadata.entity.MetaKey;
import com.moego.svc.metadata.utils.MockBeans;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("unit-test")
@SpringBootTest
@Transactional
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MetaKeyMapperTest extends MockBeans {

    private final MetaKeyMapper keyMapper;

    @Test
    public void shouldReturnDifferentObject() {
        var key = new MetaKey();
        key.setName("name");
        key.setDescription("description");
        key.setOwnerType("BUSINESS");
        key.setPermissionLevel("OWNER");
        key.setDefaultValue("");
        key.setGroup("default");
        key.setInternalOperatorId("operator");
        key.setCreatedAt(timeUtils.now());
        keyMapper.insertSelective(key);
        var key1 = keyMapper.selectByPrimaryKey(key.getId());
        var key2 = keyMapper.selectByPrimaryKey(key.getId());
        assertEquals(key1, key2);
        assertNotSame(key1, key2);
    }
}
