/*
 * @since 2023-04-13 15:35:09
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.metadata.controller;

import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_ACCOUNT;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_ACCOUNT_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_BUSINESS;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_BUSINESS_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_COMPANY;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_COMPANY_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_ENTERPRISE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_STAFF;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_STAFF_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_SYSTEM;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_UNSPECIFIED;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_BUSINESS_ANY_STAFF;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_COMPANY_ANY_BUSINESS_OWNER;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_COMPANY_ANY_STAFF;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_NOBODY;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_OWNER;
import static com.moego.svc.metadata.converter.MetadataConverter.OWNER_TYPE_;
import static com.moego.svc.metadata.converter.MetadataConverter.PERMISSION_LEVEL_;
import static com.moego.svc.metadata.utils.Messages.INVALID_PERMISSION_LEVEL;
import static com.moego.svc.metadata.utils.Messages.META_KEY_NOT_FOUND;
import static com.moego.svc.metadata.utils.Messages.META_VALUE_NOT_FOUND;
import static com.moego.svc.metadata.utils.Messages.NAME_IS_ALREADY_IN_USE;
import static com.moego.svc.metadata.utils.Messages.START_SHOULD_BEFORE_END;
import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.google.protobuf.Empty;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.metadata.v1.PermissionLevel;
import com.moego.idl.models.metadata.v1.ValueModel;
import com.moego.idl.service.metadata.v1.DeleteKeyRequest;
import com.moego.idl.service.metadata.v1.DescribeKeysRequest;
import com.moego.idl.service.metadata.v1.DescribeMetadataRequest;
import com.moego.idl.service.metadata.v1.DescribeValuesRequest;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.GetValueRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc.MetadataServiceBlockingStub;
import com.moego.idl.service.metadata.v1.UpdateValueRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.metadata.converter.MetadataConverter;
import com.moego.svc.metadata.dto.PageInfo;
import com.moego.svc.metadata.entity.MetaKey;
import com.moego.svc.metadata.mapper.MetaKeyMapper;
import com.moego.svc.metadata.mapper.MetaValueMapper;
import com.moego.svc.metadata.utils.MockBeans;
import com.moego.svc.metadata.utils.PrettyMap;
import com.moego.svc.metadata.utils.PrettySet;
import io.grpc.Status.Code;
import io.grpc.StatusRuntimeException;
import io.grpc.inprocess.InProcessServerBuilder;
import jakarta.annotation.PostConstruct;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.function.Executable;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("unit-test")
@SpringBootTest
@Transactional
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MetadataControllerTest extends MockBeans {

    private final MetaKeyMapper keyMapper;
    private final MetadataConverter metadataConverter;
    private final MetadataServiceBlockingStub metadataServiceBlockingStub;
    private final String serverName = InProcessServerBuilder.generateName();
    private final MetaValueMapper valueMapper;
    private List<MetaKey> KEYS;

    @PostConstruct
    public void init() {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));

        when(timeUtils.now()).thenAnswer(invocation -> {
            var now = Instant.now();
            return LocalDateTime.ofEpochSecond(now.getEpochSecond(), now.getNano() / 1000 * 1000, UTC);
        });

        KEYS = List.of(
                key("k01", "d01", OWNER_TYPE_SYSTEM, "v01", null, null, PERMISSION_LEVEL_OWNER, "g01", "u01"),
                key("k02", "d02", OWNER_TYPE_COMPANY, "v02", 1, null, PERMISSION_LEVEL_OWNER, "g01", "u01"),
                // prettier-ignore
                key(
                        "k03",
                        "d03",
                        OWNER_TYPE_COMPANY,
                        "v03",
                        -1,
                        null,
                        PERMISSION_LEVEL_COMPANY_ANY_BUSINESS_OWNER,
                        "g01",
                        "u01"),
                key("k04", "d04", OWNER_TYPE_COMPANY, "v04", null, 1, PERMISSION_LEVEL_COMPANY_ANY_STAFF, "g01", "u01"),
                key("k05", "d05", OWNER_TYPE_COMPANY, "v05", null, -1, PERMISSION_LEVEL_NOBODY, "g01", "u01"),
                key("k06", "d06", OWNER_TYPE_BUSINESS, "v06", -2, -1, PERMISSION_LEVEL_OWNER, "g01", "u01"),
                // prettier-ignore
                key("k07", "d07", OWNER_TYPE_BUSINESS, "v07", 1, 2, PERMISSION_LEVEL_BUSINESS_ANY_STAFF, "g01", "u01"),
                key("k08", "d08", OWNER_TYPE_BUSINESS, "v08", -1, 1, PERMISSION_LEVEL_NOBODY, "g01", "u01"),
                key("k09", "d09", OWNER_TYPE_STAFF, "v09", null, null, PERMISSION_LEVEL_OWNER, "g01", "u01"),
                key("k10", "d10", OWNER_TYPE_STAFF, "v10", null, null, PERMISSION_LEVEL_NOBODY, "g01", "u02"),
                key("k11", "d11", OWNER_TYPE_ACCOUNT, "v11", null, null, PERMISSION_LEVEL_OWNER, "g01", "u02"),
                key("k12", "d12", OWNER_TYPE_ACCOUNT, "v12", null, null, PERMISSION_LEVEL_NOBODY, "g01", "u02"),
                key("k13", "d13", OWNER_TYPE_SYSTEM, "v13", null, null, PERMISSION_LEVEL_OWNER, "g03", "u02"),
                key("k14", "d14", OWNER_TYPE_COMPANY, "v14", null, null, PERMISSION_LEVEL_OWNER, "g03", "u02"),
                key("k15", "d15", OWNER_TYPE_BUSINESS, "v15", null, null, PERMISSION_LEVEL_OWNER, "g03", "u02"),
                key("k16", "d16", OWNER_TYPE_STAFF, "v16", null, null, PERMISSION_LEVEL_OWNER, "g03", "u02"),
                key("k17", "d17", OWNER_TYPE_ACCOUNT, "v17", null, null, PERMISSION_LEVEL_OWNER, "g03", "u02"),
                key("k18", "d18", OWNER_TYPE_SYSTEM, "v18", null, null, PERMISSION_LEVEL_OWNER, "g03", "u02"),
                key("k19", "d19", OWNER_TYPE_SYSTEM, "v19", null, null, PERMISSION_LEVEL_OWNER, "ag01", "u03"),
                key("k20", "d20", OWNER_TYPE_SYSTEM, "v20", null, null, PERMISSION_LEVEL_OWNER, null, "u03"));
    }

    @Test
    public void createKey() {
        for (MetaKey key : KEYS) {
            var now = new Date();
            var input = metadataConverter.toCreateKeyRequest(key);
            var output = metadataServiceBlockingStub.createKey(input);
            assertTrue(output.getKey().getId() > 0);
            var result = keyMapper.selectByPrimaryKey(output.getKey().getId());
            assertTrue(absDiffTime(result.getCreatedAt(), now) < 5);
            key.setId(output.getKey().getId());
            key.setCreatedAt(result.getCreatedAt());
            assertEquals(key, result);
            var reverted = metadataConverter.toMetaKey(output.getKey());
            assertEquals(key, reverted);
        }
    }

    @Test
    void createDuplicatedKey() {
        var params = metadataConverter.toCreateKeyRequest(KEYS.get(0));
        metadataServiceBlockingStub.createKey(params);
        shouldThrows(NAME_IS_ALREADY_IN_USE, () -> {
            var badParams = params.toBuilder()
                    .setKeyDef(params.getKeyDef().toBuilder().setGroup("random"))
                    .build();
            metadataServiceBlockingStub.createKey(badParams);
        });
    }

    @Test
    public void describeGroups() {
        var result = metadataServiceBlockingStub.describeGroups(Empty.getDefaultInstance());
        assertEquals(List.of("default"), result.getGroupsList());
        createKey();
        result = metadataServiceBlockingStub.describeGroups(Empty.getDefaultInstance());
        assertEquals(KEYS.stream().map(MetaKey::getGroup).distinct().sorted().toList(), result.getGroupsList());
    }

    @Test
    public void getKey() {
        var params = metadataConverter.toCreateKeyRequest(KEYS.get(0));
        var key = metadataServiceBlockingStub.createKey(params).getKey();
        var key1 = metadataServiceBlockingStub
                .getKey(metadataConverter.toGetKeyRequest(key.getId()))
                .getKey();
        assertEquals(key, key1);
        var key3 = metadataServiceBlockingStub
                .getKey(metadataConverter.toGetKeyRequest(key.getName()))
                .getKey();
        assertEquals(key, key3);
        shouldThrows(
                META_KEY_NOT_FOUND, () -> metadataServiceBlockingStub.getKey(metadataConverter.toGetKeyRequest(1L)));
        shouldThrows(
                META_KEY_NOT_FOUND,
                () -> metadataServiceBlockingStub.getKey(metadataConverter.toGetKeyRequest("random")));
    }

    @Test
    public void describeKeys() {
        createKey();
        var page = PaginationRequest.newBuilder().setPageSize(3).setPageNum(2);
        var input = DescribeKeysRequest.newBuilder().setPagination(page);
        var out = metadataServiceBlockingStub.describeKeys(input.build());
        var all = KEYS.stream()
                .sorted(Comparator.comparingLong(MetaKey::getId).reversed())
                .toList();
        var pie = new PageInfo(2, 3, all.size());
        var pir = metadataConverter.toPageInfo(out.getPagination());
        assertEquals(pie, pir);
        var expList = all.subList(3, 6);
        var realList =
                out.getKeysList().stream().map(metadataConverter::toMetaKey).toList();
        assertEquals(expList, realList);
        var nameLike = KEYS.get(0).getName().substring(0, 2);
        var group = KEYS.get(0).getGroup();
        var ownerType = KEYS.get(0).getOwnerType();

        var nameLikeList =
                KEYS.stream().filter(v -> v.getName().startsWith(nameLike)).toList();
        var groupList = KEYS.stream().filter(v -> v.getGroup().equals(group)).toList();
        var ownerTypeList =
                KEYS.stream().filter(v -> v.getOwnerType().equals(ownerType)).toList();
        input.clearPagination().setNameLike(nameLike);
        out = metadataServiceBlockingStub.describeKeys(input.build());
        assertEquals(out.getPagination().getTotal(), nameLikeList.size());
        input.clearNameLike().setGroup(group);
        out = metadataServiceBlockingStub.describeKeys(input.build());
        assertEquals(out.getPagination().getTotal(), groupList.size());
        input.clearGroup().setOwnerType(metadataConverter.toOwnerType(ownerType));
        out = metadataServiceBlockingStub.describeKeys(input.build());
        assertEquals(out.getPagination().getTotal(), ownerTypeList.size());
    }

    @Test
    public void updateKey() {
        createKey();
        var key = KEYS.get(0);
        var key1 = key("ku1", "du1", OWNER_TYPE_BUSINESS, "vu1", -3, 3, PERMISSION_LEVEL_NOBODY, "gu1", "ou1");
        key1.setId(key.getId());
        key1.setCreatedAt(key.getCreatedAt());
        key1.setName(key.getName());
        metadataServiceBlockingStub.updateKey(metadataConverter.toUpdateKeyRequest(key1));
        var key2 = metadataServiceBlockingStub
                .getKey(metadataConverter.toGetKeyRequest(key1.getId()))
                .getKey();
        key1.setUpdatedAt(metadataConverter.toLocalDateTime(key2.getUpdatedAt()));
        assertTrue(absDiffTime(key1.getUpdatedAt(), new Date()) < 5);
        assertEquals(metadataConverter.toKeyModel(key1), key2);

        shouldThrows(OWNER_TYPE_UNSPECIFIED.name(), () -> {
            var key3 = new MetaKey();
            key3.setId(key.getId());
            key3.setOwnerType(metadataConverter.toString(OWNER_TYPE_UNSPECIFIED));
            metadataServiceBlockingStub.updateKey(metadataConverter.toUpdateKeyRequest(key3));
        });

        shouldThrows(INVALID_PERMISSION_LEVEL.format("COMPANY_ANY_BUSINESS_OWNER", "ACCOUNT"), () -> {
            var key3 = new MetaKey();
            key3.setId(key.getId());
            key3.setOwnerType(metadataConverter.toString(OWNER_TYPE_ACCOUNT));
            key3.setPermissionLevel(metadataConverter.toString(PERMISSION_LEVEL_COMPANY_ANY_BUSINESS_OWNER));
            key3.setInternalOperatorId("KK");
            metadataServiceBlockingStub.updateKey(metadataConverter.toUpdateKeyRequest(key3));
        });

        shouldThrows(INVALID_PERMISSION_LEVEL.format("COMPANY_ANY_BUSINESS_OWNER", "BUSINESS"), () -> {
            var key3 = new MetaKey();
            key3.setId(key.getId());
            key3.setPermissionLevel(metadataConverter.toString(PERMISSION_LEVEL_COMPANY_ANY_BUSINESS_OWNER));
            key3.setInternalOperatorId("KK");
            metadataServiceBlockingStub.updateKey(metadataConverter.toUpdateKeyRequest(key3));
        });

        shouldThrows(START_SHOULD_BEFORE_END, () -> {
            var key3 = new MetaKey();
            key3.setId(key.getId());
            key3.setStartAt(timeUtils.now().plusDays(6));
            key3.setInternalOperatorId("KK");
            metadataServiceBlockingStub.updateKey(metadataConverter.toUpdateKeyRequest(key3));
        });

        shouldThrows(START_SHOULD_BEFORE_END, () -> {
            var key3 = new MetaKey();
            key3.setId(key.getId());
            key3.setEndAt(timeUtils.now().minusDays(6));
            key3.setInternalOperatorId("KK");
            metadataServiceBlockingStub.updateKey(metadataConverter.toUpdateKeyRequest(key3));
        });

        var key4 = metadataServiceBlockingStub
                .getKey(metadataConverter.toGetKeyRequest(key1.getId()))
                .getKey();
        assertEquals(metadataConverter.toKeyModel(key1), key4);
    }

    @Test
    public void deleteKey() {
        createKey();
        var key0 = KEYS.get(0);
        var getIn = GetKeyRequest.newBuilder().setId(key0.getId()).build();
        // should find by id
        metadataServiceBlockingStub.getKey(getIn);
        // should find by name
        metadataServiceBlockingStub.getKey(
                getIn.toBuilder().setName(key0.getName()).build());
        var delIn = DeleteKeyRequest.newBuilder().setId(key0.getId()).setInternalOperatorId("KK");
        // delete
        metadataServiceBlockingStub.deleteKey(delIn.build());
        // should find by id
        var key1 = metadataServiceBlockingStub.getKey(getIn).getKey();
        // should with delete time
        assertTrue(absDiffTime(metadataConverter.toLocalDateTime(key1.getDeletedAt()), new Date()) < 5);
        // should not find by name
        shouldThrows(
                "the meta key was not found.",
                () -> metadataServiceBlockingStub.getKey(
                        getIn.toBuilder().setName(key0.getName()).build()));
        // should not throw
        metadataServiceBlockingStub.deleteKey(delIn.build());
    }

    @Test
    public void updateValue() {
        createKey();
        var key = KEYS.get(0);
        var upIn = UpdateValueRequest.newBuilder()
                .setKeyId(key.getId())
                .setValue("uv1")
                .setOwnerId(21)
                .setOperatorId(22)
                .build();
        metadataServiceBlockingStub.updateValue(upIn);
        var getIn = GetValueRequest.newBuilder()
                .setKeyId(key.getId())
                .setOwnerId(21)
                .build();
        var value = metadataServiceBlockingStub.getValue(getIn).getValue();
        assertTrue(absDiffTime(metadataConverter.toLocalDateTime(value.getCreatedAt()), new Date()) < 5);
        var expected = ValueModel.newBuilder()
                .setId(value.getId())
                .setKeyId(key.getId())
                .setOwnerId(21)
                .setValue("uv1")
                .setOperatorId(22)
                .setCreatedAt(value.getCreatedAt())
                .build();
        assertEquals(expected, value);
        var upIn2 = upIn.toBuilder().setInternalOperatorId("iu1").build();
        metadataServiceBlockingStub.updateValue(upIn2);
        var value2 = metadataServiceBlockingStub.getValue(getIn).getValue();
        assertTrue(absDiffTime(metadataConverter.toLocalDateTime(value2.getUpdatedAt()), new Date()) < 5);
        var expected2 = expected.toBuilder()
                .clearOperatorId()
                .setInternalOperatorId("iu1")
                .setUpdatedAt(value2.getUpdatedAt())
                .build();
        assertEquals(expected2, value2);
        var upIn3 = upIn.toBuilder().clearValue().build();
        metadataServiceBlockingStub.updateValue(upIn3);
        shouldThrows(META_VALUE_NOT_FOUND, () -> metadataServiceBlockingStub.getValue(getIn));
        var metaValue = valueMapper.selectByPrimaryKey(value.getId());
        assertTrue(absDiffTime(metaValue.getDeletedAt(), new Date()) < 5);
    }

    @Test
    public void getValue() {
        // almost tested by updateValue, here only test miss matched
        var getIn = GetValueRequest.newBuilder().setKeyId(1).setOwnerId(1).build();
        shouldThrows(META_VALUE_NOT_FOUND, () -> metadataServiceBlockingStub.getValue(getIn));
    }

    @Test
    public void describeValues() {
        createKey();
        for (OwnerType ownerType : OwnerType.values()) {
            if (ownerType == OwnerType.UNRECOGNIZED || ownerType == OWNER_TYPE_UNSPECIFIED) {
                continue;
            }
            var upIn = UpdateValueRequest.newBuilder()
                    .setKeyId(KEYS.get(0).getId())
                    .setValue(ownerType.name())
                    .setOwnerId(ownerType.getNumber())
                    .setOperatorId(ownerType.ordinal())
                    .build();
            metadataServiceBlockingStub.updateValue(upIn);
            var upIn2 = upIn.toBuilder().setKeyId(KEYS.get(1).getId()).build();
            if (ownerType.ordinal() % 2 == 1) metadataServiceBlockingStub.updateValue(upIn2);
        }
        var pageIn1 =
                PaginationRequest.newBuilder().setPageNum(2).setPageSize(2).build();
        var req1 = DescribeValuesRequest.newBuilder()
                .setPagination(pageIn1)
                .setKeyId(KEYS.get(0).getId())
                .build();
        var out1 = metadataServiceBlockingStub.describeValues(req1);
        assertEquals(OwnerType.values().length - 2, out1.getPagination().getTotal());
        assertEquals(KEYS.get(0).getId(), out1.getValues(0).getKeyId());
    }

    @Test
    public void extractValues() {
        createKey();
        Map<Long, Map<Long, String>> overwrites = new HashMap<>();
        var idx = 0;
        for (MetaKey key : KEYS) {
            for (OwnerType ownerType : OwnerType.values()) {
                idx += 1;
                if (ownerType == OwnerType.UNRECOGNIZED || ownerType == OWNER_TYPE_UNSPECIFIED) {
                    continue;
                }
                if (idx % 3 != 0) {
                    var upIn = UpdateValueRequest.newBuilder()
                            .setKeyId(key.getId())
                            .setValue(ownerType.name() + "." + key.getId())
                            .setOwnerId(ownerType.getNumber())
                            .setOperatorId(ownerType.ordinal())
                            .build();
                    metadataServiceBlockingStub.updateValue(upIn);
                    if (idx % 4 == 0) {
                        var upIn2 = upIn.toBuilder().clearValue().build();
                        metadataServiceBlockingStub.updateValue(upIn2);
                        continue;
                    } else if (idx % 5 == 0) {
                        upIn = upIn.toBuilder()
                                .setValue(upIn.getValue() + ".up")
                                .build();
                        metadataServiceBlockingStub.updateValue(upIn);
                    }
                    overwrites
                            .computeIfAbsent(key.getId(), k -> new HashMap<>())
                            .put(upIn.getOwnerId(), upIn.getValue());
                }
            }
        }
        var groups = metadataServiceBlockingStub
                .describeGroups(Empty.getDefaultInstance())
                .getGroupsList();
        Set<OwnerType> hintOwners = new PrettySet<>();
        for (String group : groups) {
            Map<String, String> expects = new PrettyMap<>();
            Map<OwnerType, Long> owners = new PrettyMap<>();
            for (MetaKey key : KEYS) {
                if (!key.getGroup().equals(group)) {
                    continue;
                }
                if (key.getStartAt() != null && key.getStartAt().isAfter(timeUtils.now())) {
                    continue;
                }
                if (key.getEndAt() != null && key.getEndAt().isBefore(timeUtils.now())) {
                    continue;
                }
                expects.put(key.getName(), key.getDefaultValue());
                var ownerType = metadataConverter.toOwnerType(key.getOwnerType());
                if (overwrites.containsKey(key.getId())) {
                    if (owners.containsKey(ownerType)) {
                        if (overwrites.get(key.getId()).containsKey(owners.get(ownerType))) {
                            expects.put(
                                    key.getName(), overwrites.get(key.getId()).get(owners.get(ownerType)));
                        }
                    } else {
                        var owner =
                                overwrites.get(key.getId()).keySet().iterator().next();
                        owners.put(ownerType, owner);
                        expects.put(key.getName(), overwrites.get(key.getId()).get(owner));
                    }
                }
            }
            var ownerMap = owners.entrySet().stream()
                    .collect(Collectors.toUnmodifiableMap(
                            v -> Long.valueOf(v.getKey().getNumber()), Map.Entry::getValue));
            var req = ExtractValuesRequest.newBuilder()
                    .setGroup(group)
                    .putAllOwners(ownerMap)
                    .build();
            var meta = metadataServiceBlockingStub.extractValues(req);
            assertEquals(expects, new PrettyMap<>(meta.getValuesMap()));
            System.out.println("ExtractValues, owners: " + new PrettyMap<>(ownerMap)
                    + ", values: "
                    + new PrettyMap<>(meta.getValuesMap()));
            hintOwners.addAll(owners.keySet());
        }
        var allOwners = new PrettySet<>(Arrays.stream(OwnerType.values())
                .filter(v -> v != OwnerType.UNRECOGNIZED && v != OWNER_TYPE_UNSPECIFIED && v != OWNER_TYPE_ENTERPRISE)
                .toList());
        assertEquals(allOwners, hintOwners);
    }

    @Test
    public void extractAnonymousValues() {
        createKey();
        var key = KEYS.get(0);
        var upIn = UpdateValueRequest.newBuilder()
                .setKeyId(key.getId())
                .setValue("O1")
                .setOwnerId(1)
                .setOperatorId(1)
                .build();
        metadataServiceBlockingStub.updateValue(upIn);
        var upIn2 =
                upIn.toBuilder().setValue("O2").setOwnerId(2).setOperatorId(2).build();
        metadataServiceBlockingStub.updateValue(upIn2);
        var extractValuesInput = ExtractValuesRequest.newBuilder().setKeyId(key.getId());
        var out = metadataServiceBlockingStub.extractValues(extractValuesInput.build());
        assertEquals(key.getDefaultValue(), out.getValuesMap().get(key.getName()));
    }

    @Test
    public void describeMetadata() {
        createKey();
        for (int i = 0; i < KEYS.size(); i++) {
            var key = KEYS.get(i);
            if (metadataConverter.toOwnerType(key.getOwnerType()).equals(OWNER_TYPE_SYSTEM)) {
                continue;
            }
            for (int j = 1; j < i + 1; j++) {
                var value = "V-" + key.getName() + "-" + j;
                var upIn = UpdateValueRequest.newBuilder()
                        .setKeyId(key.getId())
                        .setValue(value)
                        .setOwnerId(j)
                        .setOperatorId(1L)
                        .setInternalOperatorId("OP-" + value)
                        .build();
                metadataServiceBlockingStub.updateValue(upIn);
                if (j % 2 == 0) {
                    metadataServiceBlockingStub.updateValue(
                            upIn.toBuilder().clearValue().build());
                }
            }
        }
        var meta = metadataServiceBlockingStub.describeMetadata(
                DescribeMetadataRequest.newBuilder().setGroup("g01").build());
        assertEquals(0, meta.getValueMapCount());
        var exp1 = KEYS.stream()
                .filter(v -> v.getGroup().equals("g01")
                        && (v.getStartAt() == null || v.getStartAt().isBefore(timeUtils.now()))
                        && (v.getEndAt() == null || v.getEndAt().isAfter(timeUtils.now())))
                .collect(Collectors.toMap(MetaKey::getName, metadataConverter::toKeyModel));
        assertEquals(new PrettyMap<>(exp1), new PrettyMap<>(meta.getKeyMapMap()));
        assertTrue(exp1.size() > 0);
        var meta2 = metadataServiceBlockingStub.describeMetadata(DescribeMetadataRequest.newBuilder()
                .setGroup("g01")
                .putOwners(OWNER_TYPE_COMPANY_VALUE, 1)
                .putOwners(OWNER_TYPE_BUSINESS_VALUE, 2)
                .putOwners(OWNER_TYPE_STAFF_VALUE, 3)
                .putOwners(OWNER_TYPE_ACCOUNT_VALUE, 5)
                .build());
        assertEquals(new PrettyMap<>(exp1), new PrettyMap<>(meta2.getKeyMapMap()));
        var real2 = meta2.getValueMapMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().getValue()));
        var exp2 = new PrettyMap<String, String>();
        exp1.forEach((k, v) -> {
            switch (v.getOwnerType()) {
                case OWNER_TYPE_COMPANY -> exp2.put(k, "V-" + k + "-1");
                case OWNER_TYPE_STAFF -> exp2.put(k, "V-" + k + "-3");
                case OWNER_TYPE_ACCOUNT -> exp2.put(k, "V-" + k + "-5");
            }
        });
        assertEquals(exp2, new PrettyMap<>(real2));
    }

    private void shouldThrows(String message, Executable executable) {
        var e = assertThrowsExactly(StatusRuntimeException.class, executable);
        assertEquals(Code.INVALID_ARGUMENT, e.getStatus().getCode());
        try {
            var d = ExceptionUtil.extractCommonError(e);
            assertEquals(message, d.getMessage());
        } catch (Throwable ignored) {
            assertThat(e.getMessage()).contains(message);
        }
    }

    private MetaKey key(
            String name,
            String desc,
            OwnerType ownerType,
            String defaultValue,
            Integer startAt,
            Integer endAt,
            PermissionLevel permissionLevel,
            String group,
            String operator) {
        var expected = new MetaKey();
        var now = timeUtils.now();
        expected.setName(name);
        expected.setDescription(desc);
        expected.setOwnerType(ownerType.name().substring(OWNER_TYPE_.length()));
        expected.setDefaultValue(defaultValue);
        expected.setStartAt(startAt == null ? null : now.plusDays(startAt));
        expected.setEndAt(endAt == null ? null : now.plusDays(endAt));
        expected.setPermissionLevel(permissionLevel.name().substring(PERMISSION_LEVEL_.length()));
        expected.setGroup(group == null ? "default" : group);
        expected.setInternalOperatorId(operator);
        return expected;
    }

    private long absDiffTime(LocalDateTime key, Date now) {
        return Math.abs(key.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond()
                - now.toInstant().getEpochSecond());
    }
}
