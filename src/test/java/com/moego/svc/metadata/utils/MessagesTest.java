/*
 * @since 2023-05-26 14:18:30
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.metadata.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("unit-test")
@SpringBootTest
@Transactional
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MessagesTest extends MockBeans {

    @Test
    public void format() {
        var twoFormat = new Messages.Two("FOO %s %s").format("BAR", "BAZ");
        assertEquals("FOO BAR BAZ", twoFormat);
    }
}
