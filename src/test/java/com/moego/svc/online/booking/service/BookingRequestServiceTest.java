package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.BookingRequestDynamicSqlSupport.bookingRequest;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mybatis.dynamic.sql.SqlBuilder.and;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanOrEqualTo;

import com.moego.svc.online.booking.dto.BookingRequestFilterDTO;
import com.moego.svc.online.booking.mapper.BookingRequestDynamicSqlSupport;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;

@ExtendWith(MockitoExtension.class)
class BookingRequestServiceTest {

    @Mock
    private BookingRequestMapper bookingRequestMapper;

    @InjectMocks
    private BookingRequestService bookingRequestService;

    @Test
    void testCountBookingRequests() {
        final var input = new BookingRequestFilterDTO()
                .setCompanyId(1L)
                .setBusinessIds(List.of(11L, 12L))
                .setStartDate("2023-05-01")
                .setEndDate("2023-05-31");
        when(bookingRequestMapper.count((CountDSLCompleter) any())).thenReturn(10L);

        final var result = bookingRequestService.countBookingRequest(input);
        final var expected = 10L;
        assertEquals(expected, result);
    }

    @Test
    void buildCriteria() {

        LocalDate threeMonthsAgo = LocalDate.now().minusMonths(3);

        final var input = new BookingRequestFilterDTO()
                .setCompanyId(1L)
                .setBusinessIds(List.of(11L, 12L))
                .setCreatedBefore(new Date(1730794299L * 1000))
                .setCreatedAfter(new Date(1730707899L * 1000))
                .setIsWaitlistExpired(true)
                .setExpiredDate(threeMonthsAgo);

        final var result = BookingRequestService.buildCriteria(input);
        final var expect = List.of(
                and(BookingRequestDynamicSqlSupport.bookingRequest.companyId, isEqualTo(1L)),
                and(BookingRequestDynamicSqlSupport.bookingRequest.businessId, isIn(List.of(11L, 12L))),
                and(bookingRequest.customerId, isInWhenPresent()),
                and(bookingRequest.appointmentId, isInWhenPresent()),
                and(bookingRequest.createdAt, isLessThanOrEqualTo(new Date(1730794299L * 1000))),
                and(bookingRequest.createdAt, isGreaterThanOrEqualTo(new Date(1730707899L * 1000))),
                and(BookingRequestDynamicSqlSupport.startDate, isLessThanOrEqualTo(threeMonthsAgo.toString())));
        assertThat(result).usingRecursiveComparison().ignoringCollectionOrder().isEqualTo(expect);
    }
}
