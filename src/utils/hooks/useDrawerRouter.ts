import { useMemo, useState } from 'react';

export type RouteNameType = string | number | symbol;
export type RouteMapType<T extends RouteNameType> = {
  [K in T]?: unknown;
};

export type RouteItem<T extends RouteNameType, U extends RouteMapType<T>> = {
  name: T;
  params?: U[T];
};

export const useDrawerRouter = <T extends RouteNameType, U extends RouteMapType<T>>(fallback: RouteItem<T, U>) => {
  const [queue, setQueue] = useState<Array<RouteItem<T, U>>>([]);

  const push = <K extends T>(name: K, params?: U[K]) => {
    setQueue((pre) => {
      const newVal = { name, params };
      if (pre.some((q) => q.name === name)) {
        return pre.filter((q) => q.name !== name).concat(newVal);
      } else {
        return pre.concat(newVal);
      }
    });
  };

  // clear and push
  const go = <K extends T>(name: K, params?: U[K]) => {
    setQueue([{ name, params }]);
  };

  const reset = () => {
    setQueue([]);
  };

  const back = () => {
    setQueue((pre) => pre.slice(0, -1));
  };

  const replace = <K extends T>(name: K, params?: U[K]) => {
    setQueue((pre) => pre.slice(0, -1).concat({ name, params }));
  };

  return useMemo(() => {
    const getParams = <K extends T>(name: K): U[K] => {
      return queue.find((q) => q.name === name)?.params as U[K];
    };
    const is = (page: T) => {
      return current.name === page;
    };

    const current = queue[queue.length - 1] || fallback;

    return {
      isEmpty: queue.length === 0,
      current,
      push,
      reset,
      back,
      go,
      is,
      replace,
      getParams,
    };
  }, [queue, fallback]);
};
