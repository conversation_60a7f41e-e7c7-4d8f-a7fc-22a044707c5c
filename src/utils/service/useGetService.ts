import { useSelector } from 'amos';
import { companyServiceMapBox } from '../../store/service/companyService.boxes';
import { companyServiceMapBox as legacyServiceMapBox } from '../../store/service/service.boxes';
import { useEnableFulfillmentFlow } from '../hooks/useEnableFulfillmentFlow';

export const useGetService = (serviceId: string) => {
  const [companyServiceMap, legacyServiceMap] = useSelector(companyServiceMapBox, legacyServiceMapBox);
  const enableFulfillmentFlow = useEnableFulfillmentFlow();

  return enableFulfillmentFlow
    ? companyServiceMap.mustGetItem(serviceId)
    : legacyServiceMap.mustGetItem(Number(serviceId));
};
