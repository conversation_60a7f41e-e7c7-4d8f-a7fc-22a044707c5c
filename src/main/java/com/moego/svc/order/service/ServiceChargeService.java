package com.moego.svc.order.service;

import com.moego.svc.order.model.dto.ServiceChargeDTO;
import com.moego.svc.order.model.params.GetServiceChargeParams;
import com.moego.svc.order.model.params.SaveServiceChargeParams;
import java.util.List;

public interface ServiceChargeService {
    // 不用修改，mg 后不应该调用
    ServiceChargeDTO getServiceChargeById(Long id);

    // 已改，新逻辑兼容新旧两种数据
    List<ServiceChargeDTO> getServiceChargeByIdList(Long companyId, Long businessId, List<Long> ids);

    // 已改，新逻辑兼容新旧两种数据
    List<ServiceChargeDTO> getServiceChargeList(GetServiceChargeParams params);

    // 不用修改，mg 后不应该调用
    List<ServiceChargeDTO> getAutoApplyServiceChargeList(Long companyId, Long businessId);

    // 不用修改，mg 后不应该调用
    ServiceChargeDTO saveServiceCharge(SaveServiceChargeParams params);

    Boolean sortServiceCharge(Long companyId, Long businessId, Long operatorId, List<Long> ids);

    Integer deleteServiceCharge(Long companyId, Long businessId, Long operatorId, List<Long> deleteIdList);
}
