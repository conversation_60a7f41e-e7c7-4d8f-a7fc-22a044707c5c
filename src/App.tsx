import { datadogRum } from '@datadog/browser-rum';
import { MoeGoPayTerminalProvider } from '@moego/finance-terminal';
import { MoeGoPayProvider } from '@moego/finance-web-kit';
import { Logger, type LoggerUser } from '@moego/tools/webTools';
import { ThemeProvider, Toaster } from '@moego/ui';
import { setUser } from '@sentry/browser';
import { type User as SentryUser } from '@sentry/types';
import { useDispatch, useSelector, useStore } from 'amos';
import React, { useEffect, type FC } from 'react';
import { useHistory } from 'react-router-dom';
import { useAsync, useMount } from 'react-use';
import './App.css';
import { TempFeature } from './TempFeature';
import { VersionCheck } from './VersionCheck';
import { AlertManager } from './components/Alert/AlertApi';
import { GlobalCallCenter } from './components/GlobalCallCenter/GlobalCallCenter';
import { FloatableHostProvider } from './components/Modal/FloatableHost.provider';
import { OpenInMoeGo } from './components/OpenInMoeGo';
import { GlobalModals } from './components/globals/GlobalModals';
import { SocketBackground } from './components/globals/SocketBackground';
import { MOE_ENV } from './config/host/const';
import { LockModal } from './container/Account/components/LockModal';
import { AwesomeCalendarProvider } from './container/Calendar/latest/AwesomeCalendarProvider';
import { AllInOneProvider } from './container/Calendar/latest/CalendarLodgingOverview';
import { useCapitalBlocked } from './container/Finance/hooks/useCapitalBlocked';
import { UpgradeNewInvoiceGlobalModal } from './container/PaymentFlow/shared/UpgradeInvoiceReinvent/UpgradeInvoiceReinventGlobalModal';
import { useInitPageSpy } from './init/initPageSpy';
import {
  PATH_DEFAULT,
  PATH_GOOGLE_CALENDAR_SYNC_AUTH,
  PATH_GOOGLE_CALENDAR_SYNC_CALLBACK,
  PATH_GOOGLE_CALENDAR_SYNC_SETTING,
  PATH_MOBILE_EDITOR,
  PATH_SIGN_IN,
  PATH_SIGN_UP,
  PATH_SQUARE_POS,
  PATH_SQUARE_POS_CALLBACK,
} from './router/paths';
import { Routes } from './router/routes';
import { FinanceKit, useSetupFinanceKit } from './service/finance-kit';
import { getInvoiceReinventWhitelist } from './store/PaymentFlow/whitelist.actions';
import { getAccountInfo } from './store/account/account.actions';
import { selectCurrentAccount } from './store/account/account.selectors';
import { BusinessType } from './store/business/business.options';
import { selectCurrentBusiness } from './store/business/business.selectors';
import { selectCurrentPermissions, selectStaffRole } from './store/business/role.selectors';
import { getAlertSettings } from './store/checkInOutAlert/checkInOutAlert.actions';
import { getEnterpriseTheme } from './store/company/company.actions';
import { selectCurrentCompany } from './store/company/company.selectors';
import { getMetadataByGroup } from './store/metadata/metadata.actions';
import { META_DATA_AI_GROUP_NAME, META_DATA_KEY_LIST } from './store/metadata/metadata.config';
import { useMetaData } from './store/metadata/metadata.hooks';
import { getFinanceRedDotStatus } from './store/notification/red-dot.actions';
import { getPaymentSettingInfo } from './store/payment/actions/public/payment.actions';
import { getPetIncidentList } from './store/pet/petIncident.actions';
import { getPetVaccineList } from './store/pet/petVaccine.actions';
import { getSquareAccount } from './store/square/actions/public/square.actions';
import { getStaffList } from './store/staff/staff.actions';
import { selectCurrentStaff } from './store/staff/staff.selectors';
import { getMoeGoPaySetupStatus, getStripeAccount } from './store/stripe/actions/public/stripe.actions';
import { getPreAuthOnboardingSettings } from './store/stripe/preAuth.actions';
import { isNormal } from './store/utils/identifier';
import { getVanList } from './store/van/van.actions';
import { DrawerProvider } from './utils/Drawer';
import { useMoegoBroadcastChannelEvent } from './utils/MoegoBroadcastChannel';
import { MoegoWsProvider } from './utils/MoegoWs-react';
import { growthBook, growthBookReady } from './utils/growthBook/growthBook';
import { useLatestCallback } from './utils/hooks/useLatestCallback';
import { usePaymentVersionInfo } from './utils/hooks/usePaymentVersionInfo';
import { useSetCustomThemeColor } from './utils/hooks/useSetCustomThemeColor';
import { useUpdateIntercom } from './utils/hooks/useUpdateIntercom';
import { SWITCH_BUSINESS_PAGE_RELOAD_DISABLED } from './utils/pagesConstants';
import { ReportEvent } from './utils/reportType';
import { reportGTMOnly } from './utils/tracker';
import { waitForGTMReady } from './utils/waitForGTMReady';

const VERSIONCHECK_PAGE_DISABLE = [
  PATH_GOOGLE_CALENDAR_SYNC_AUTH,
  PATH_GOOGLE_CALENDAR_SYNC_CALLBACK,
  PATH_GOOGLE_CALENDAR_SYNC_SETTING,
  PATH_SIGN_IN,
  PATH_SIGN_UP,
  PATH_SQUARE_POS,
  PATH_SQUARE_POS_CALLBACK,
  PATH_MOBILE_EDITOR,
];

export const App: FC = () => {
  const history = useHistory();
  const dispatch = useDispatch();
  const store = useStore();
  const role = store.select(selectStaffRole);
  const [staff, account, business, company, permissions] = useSelector(
    selectCurrentStaff(),
    selectCurrentAccount,
    selectCurrentBusiness,
    selectCurrentCompany(),
    selectCurrentPermissions(),
  );
  const { value: isCapitalBlocked } = useCapitalBlocked();
  const { isPaymentV2, isLoading: isLoadingPaymentVersion } = usePaymentVersionInfo();
  const didLogin = isNormal(account.id) && isNormal(business.id);
  const pathname = history?.location.pathname;
  const disableVersionCheck = VERSIONCHECK_PAGE_DISABLE.some((v) => v.regex.test(pathname));
  const disableSwitchBusinessReload = SWITCH_BUSINESS_PAGE_RELOAD_DISABLED.some((v) => v.regex.test(pathname));
  const [engagementCenter, , loading] = useMetaData<boolean>(META_DATA_KEY_LIST.EngagementCenter);
  const showEngagementCenter = !loading && engagementCenter;
  const MGPProviderConfig = {
    env: MOE_ENV === 'production' ? 'production' : 'sandbox',
    companyId: company.id.toString(),
    businessId: business.id.toString(),
    locale: 'en',
    kit: FinanceKit,
  } as const;

  const spyModalEle = useInitPageSpy();

  useUpdateIntercom(history);

  useAsync(async () => {
    dispatch(getAccountInfo());
  }, []);

  useMount(() => {
    // enterprise level setting does not need to be fetched when company/business switched
    dispatch(getEnterpriseTheme());
  });

  useEffect(() => {
    if (isNormal(business.id)) {
      if (business.preferSquare()) {
        dispatch(getSquareAccount());
      } else {
        dispatch(getStripeAccount());
        dispatch(getPaymentSettingInfo());
      }
      if (business.isMobileGrooming()) {
        dispatch([getVanList()]);
      } else {
        dispatch(getStaffList(business.id));
      }

      dispatch(getInvoiceReinventWhitelist());
      dispatch(getMetadataByGroup(META_DATA_AI_GROUP_NAME));
      dispatch(getPetVaccineList());
      dispatch(getPetIncidentList({ isIncludeDeleted: true }));
      dispatch(getAlertSettings());
    }
  }, [business.id]);

  useEffect(() => {
    if (isNormal(business.id) && !isCapitalBlocked) {
      dispatch(getFinanceRedDotStatus());
    }
  }, [business.id, isCapitalBlocked]);

  useEffect(() => {
    if (isNormal(staff.id)) {
      dispatch(getPreAuthOnboardingSettings());
    }
  }, [staff.id]);

  // TODO（Sam）：全量 PaymentV2 后删掉这里的逻辑。
  useEffect(() => {
    if (isLoadingPaymentVersion || isPaymentV2) return;

    if (isNormal(business.id) && permissions.has('viewCardProcessing')) {
      dispatch(getMoeGoPaySetupStatus());
    }
  }, [business.id, permissions.has('viewCardProcessing'), isPaymentV2, isLoadingPaymentVersion]);

  const updateReportUser = () => {
    const user: LoggerUser & SentryUser = {
      id: account.trackId(MOE_ENV),
      username: account.trackName(MOE_ENV),
      email: account.email,
      businessId: business.id,
      businessName: business.businessName,
      staffId: staff.id,
    };
    setUser(user);
    Logger.setUser(user);
    datadogRum.setUser(user);
  };

  const initTracking = () => {
    //GA user init
    if (isNormal(account.id) && isNormal(business.id)) {
      const reportObject = {
        user_id: `${account.trackId(MOE_ENV)}`,
        user_name: `${account.trackName(MOE_ENV)}`, // Full name
        user_email: `${account.email}`,
        user_role: staff ? (staff.isOwner() ? 'Owner' : (role?.name ?? '')) : 'Account',
        user_business_type: BusinessType.mapLabels[business.appType],
        business_id: business.id,
        business_name: business.businessName,
        business_type: BusinessType.mapLabels[business.appType].legacyLabel,
        company_id: company.id,
        company_name: company.companyName,
        company_locationNum: company.locationNum,
        company_vanNum: company.vansNum,
        permission_level: (!company.isGrandfather ? 1000 : 100) + (company.premiumType || 0) + '',
        plan_version: (company.planVersion || 0) + '',
        premium_type: (company.premiumType || 0) + '',
      };
      waitForGTMReady(() => {
        reportGTMOnly(ReportEvent.SetUserInfo, reportObject);
      });
    }
  };

  useEffect(() => {
    initTracking();
  }, [business.id, business.appType, account.email, staff.isOwner()]);

  useEffect(() => {
    updateReportUser();
  }, [business, account, staff]);

  // init growthbook
  useEffect(() => {
    // Load features from the GrowthBook API
    growthBook.loadFeatures({ autoRefresh: true }).then(growthBookReady.resolve).catch(growthBookReady.reject);
  }, []);

  useEffect(() => {
    growthBook.updateAttributes({
      enterprise: String(company.enterpriseId),
    });
  }, [company.enterpriseId]);

  useMoegoBroadcastChannelEvent(
    'switchAccount',
    useLatestCallback((data) => {
      if (isNormal(account.id) && data.accountId !== account.id && !disableSwitchBusinessReload) {
        window.location.replace(PATH_DEFAULT.build());
      }
    }),
  );

  useMoegoBroadcastChannelEvent(
    'logout',
    useLatestCallback(() => {
      if (!disableSwitchBusinessReload) {
        window.location.replace(PATH_SIGN_IN.build());
      }
    }),
  );

  useMoegoBroadcastChannelEvent(
    'login',
    useLatestCallback(() => {
      if (!disableSwitchBusinessReload) {
        // just reload in case page session expired
        window.location.reload();
      }
    }),
  );

  useSetupFinanceKit();
  useSetCustomThemeColor();

  return (
    <MoegoWsProvider>
      <MoeGoPayProvider {...MGPProviderConfig}>
        <MoeGoPayTerminalProvider {...MGPProviderConfig}>
          <ThemeProvider theme="light">
            <DrawerProvider>
              <AllInOneProvider>
                <AwesomeCalendarProvider>
                  <FloatableHostProvider>
                    <Routes />
                    {showEngagementCenter && <GlobalCallCenter />}
                    <AlertManager />
                    <SocketBackground />
                    <GlobalModals didLogin={didLogin} />
                    {spyModalEle}
                    <OpenInMoeGo />
                    <LockModal />
                    <UpgradeNewInvoiceGlobalModal />
                    {!disableVersionCheck && <VersionCheck />}
                    <TempFeature />
                    <Toaster />
                  </FloatableHostProvider>
                </AwesomeCalendarProvider>
              </AllInOneProvider>
            </DrawerProvider>
          </ThemeProvider>
        </MoeGoPayTerminalProvider>
      </MoeGoPayProvider>
    </MoegoWsProvider>
  );
};
