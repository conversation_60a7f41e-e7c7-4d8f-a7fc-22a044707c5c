/*
 * @since 2020-08-19 11:09:52
 * <AUTHOR> <<EMAIL>>
 */
import {
  type AccountLoginRequest,
  type AccountRegisterRequest,
} from '@moego/api-web/moego/api/account/v1/account_access_api';
import {
  type UpdatePasswordRequest,
  type UpdateProfileRequest,
} from '@moego/api-web/moego/api/account/v1/account_info_api';
import { type GetEnterpriseStaffsByWorkingLocationIdsParams } from '@moego/api-web/moego/api/organization/v1/staff_api';
import { action } from 'amos';
import { type AxiosResponse } from 'axios';
import { http } from '../../middleware/api';
import { AccountAccessClient, AccountInfoClient, PermissionClient, StaffClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { type RequireOnlyOne } from '../../types/common';
import { moegoBroadcastChannel } from '../../utils/MoegoBroadcastChannel';
import { checkIsLimitLoginError } from '../../utils/checkIsLimitLoginError';
import { omitEmpty } from '../../utils/misc';
import { ReportEvent } from '../../utils/reportType';
import { reportGTMOnly } from '../../utils/tracker';
import { businessIdListBox, businessMapBox, currentBusinessIdBox } from '../business/business.boxes';
import { getLocationList } from '../business/location.actions';
import { transferNewRole2Old } from '../business/role.actions';
import { PermissionKinds, roleMapBox } from '../business/role.boxes';
import { AsStatus, boardingDaycareFeatureEnableBox } from '../common/common.boxes';
import { getAccountCompanyList, getCompanyQuestionRecord } from '../company/company.actions';
import { companyMapBox, currentCompanyIdBox } from '../company/company.boxes';
import { getSubscriptionPlanFeature } from '../company/subscription.actions';
import { SubscriptionPlanMaps } from '../company/subscription.boxes';
import { getOwnerRoleDetail } from '../permission/permission.actions';
import { accountStaffListBox, currentStaffIdBox, staffMapBox } from '../staff/staff.boxes';
import { ID_ANONYMOUS, ID_LOGOUT } from '../utils/identifier';
import { accountGroupItemMapBox, accountGroupListBox, accountMapBox, currentAccountIdBox } from './account.boxes';
import { selectCurrentAccount } from './account.selectors';
import { growthBook } from '../../utils/growthBook/growthBook';
import { GROWTHBOOK_CHANNEL_KEY } from '../../config/host/const';
import { getCompanyCareTypeList } from '../careType/careType.actions';

export const register = action(async (_dispatch, _select, input: AccountRegisterRequest) => {
  await AccountAccessClient.register(input);
});

export const login = action(async (_dispatch, _select, input: RequireOnlyOne<AccountLoginRequest>) => {
  await AccountAccessClient.login(input);
  moegoBroadcastChannel.postMessage('login', {});
});

export const cleanupSession = action((dispatch) => {
  dispatch([
    currentAccountIdBox.setState(ID_LOGOUT),
    currentCompanyIdBox.setState(ID_LOGOUT),
    currentBusinessIdBox.setState(ID_LOGOUT),
    currentStaffIdBox.setState(ID_LOGOUT),
  ]);
});

/**
 * 登出之后请不要手动 goto sign_in,cleanupSession会触发GuardRoute自动跳转sign_in,不然会引起页面闪缩
 */
export const logout = action(async (dispatch) => {
  reportGTMOnly(ReportEvent.Logout);
  await AccountAccessClient.logout({});
  dispatch(cleanupSession());
  moegoBroadcastChannel.postMessage('logout', {});
});

export const getAccountInfo = action(async (dispatch, _select) => {
  try {
    const { account, business, staff, preference, company, planFeature, version } =
      await http.open('GET/business/account/v2/info');
    if (!account) {
      throw Error('Auth failed');
    }
    // 先把 account id 设置好，后面的接口如果因为被限制登录时间而报错跳转，GuardRoute 需要用到这个 id
    // 这里不填 companyId 会有问题, 只能先设置一下
    dispatch([
      currentAccountIdBox.setState(account.accountId),
      currentCompanyIdBox.setState(company?.id ?? ID_ANONYMOUS),
    ]);
    // 这两个接口需要权限校验，注册用户一开始可能没有 company 或者 business
    if (company && business) {
      growthBook.setAttributes({
        id: account.accountId.toString(),
        business: business.id.toString(),
        company: company.id.toString(),
        enterprise: String(company.enterpriseId),
        channel: GROWTHBOOK_CHANNEL_KEY,
        buildVersion: window.MOE_VERSION,
      });

      dispatch(getEnterpriseMasterAccount());
      dispatch(getLocationList(account.accountId));
      if (version > AsStatus.NOT_AS) {
        dispatch([boardingDaycareFeatureEnableBox.setState(version === AsStatus.IS_BD)]);
        dispatch(getOwnerRoleDetail());
        dispatch(getCompanyCareTypeList());
        try {
          const roleDetail = await PermissionClient.getRoleDetail({ roleId: staff.roleId + '' });
          const transferList = transferNewRole2Old([roleDetail]);
          dispatch([
            roleMapBox.setIterables(
              transferList.map((item) => ({
                id: Number(item.id),
                name: item.name,
                permissions: item.rolePermissions
                  .filter(Boolean)
                  .map((item) => PermissionKinds.mapKeys[item!.permissionId] as PermissionKinds),
              })),
            ),
          ]);
        } catch (e) {
          if (!checkIsLimitLoginError(e as AxiosResponse)) {
            throw e;
          }
        }
      }
    }
    dispatch(getSubscriptionPlanFeature());
    await dispatch(getAccountCompanyList(account.accountId));
    let isFillQuestion = true;
    if (company?.id) {
      try {
        isFillQuestion = (await dispatch(getCompanyQuestionRecord({ autoToast: false, companyId: company?.id })))
          .isFillQuestion;
      } catch {
        // question don't effect get account info.
      }
    }
    dispatch([
      accountMapBox.mergeItem(account.accountId, {
        ...account,
        id: account.accountId,
        passwordUpdateTime: Number(account.passwordUpdateTime),
      }),
      businessMapBox.mergeItems(business ? [{ ...business, ...preference }] : []),
      currentBusinessIdBox.setState(business ? business.id : ID_ANONYMOUS),
      staffMapBox.mergeItems(
        staff ? [{ ...staff, id: staff.staffId, businessId: business.id, accountId: account.accountId }] : [],
      ),
      currentStaffIdBox.setState(staff ? staff.staffId : ID_ANONYMOUS),
      companyMapBox.mergeItems(
        company?.id
          ? [
              {
                ...company,
                ...preference,
                notificationSoundEnable: Boolean(preference?.notificationSoundEnable),
                createdCompanyType: company.companyType,
                planFeature,
                companyId: company.id,
                enterpriseId: String(company.enterpriseId || ID_ANONYMOUS),
                companyName: company.name,
                freeAlertBarVisible: (company?.premiumType ?? 0) === SubscriptionPlanMaps.Free,
                isFillQuestion,
              },
            ]
          : [],
      ),
      currentCompanyIdBox.setState(company?.id ?? ID_ANONYMOUS),
    ]);
    return { account, business, staff, preference, company };
  } catch (error) {
    console.error(error);
    dispatch([
      currentAccountIdBox.setState(ID_ANONYMOUS),
      currentStaffIdBox.setState(ID_ANONYMOUS),
      currentBusinessIdBox.setState(ID_ANONYMOUS),
      currentCompanyIdBox.setState(ID_ANONYMOUS),
    ]);
    return null;
  }
});

/**
 * 注意这个方法只能在 AS 迁移前的用户中使用
 */
export const getAccountDetail = action(async (dispatch, _select) => {
  const { businessInfo, account, relevantAccountDto } = await http.open('GET/business/account/v2/queryWithBusiness');
  const relevantAccounts = relevantAccountDto.map((item) => ({
    id: item.accountId,
    accountId: item.accountId,
    firstName: item.accountFirstName,
    lastName: item.accountLastName,
    email: item.accountEmail,
  }));
  dispatch([
    businessMapBox.mergeItems(businessInfo.map((d) => ({ ...d, id: d.businessId }))),
    accountMapBox.mergeItems([omitEmpty({ ...account, id: account.accountId }), ...relevantAccounts]),
    accountStaffListBox.setList(
      account.accountId,
      businessInfo.map((b) => b.staffId),
    ),
    businessIdListBox.setList(
      account.accountId,
      businessInfo.map((b) => b.businessId),
    ),
    staffMapBox.mergeItems(
      businessInfo
        .map((b) => ({
          id: b.staffId,
          businessId: b.businessId,
          accountId: account.accountId,
          employeeCategory: b.employeeCategory,
        }))
        .concat(
          relevantAccountDto.map((item) => ({
            id: item.staffId,
            employeeCategory: item.employeeCategory,
            businessId: item.businessId,
            accountId: item.accountId,
          })),
        ),
    ),
    accountGroupItemMapBox.mergeItems(
      relevantAccountDto.map((item) => ({
        ...item,
        ownKey: `${account.accountId}-${item.accountId}`,
        currentAccountId: account.accountId,
      })),
    ),
    accountGroupListBox.setList(
      account.accountId,
      relevantAccountDto.map((r) => `${account.accountId}-${r.accountId}`),
    ),
  ]);
  return { businessInfo, account };
});

export const updateAccountInfo = action(
  async (dispatch, select, input: UpdateProfileRequest, accountId: number = select(currentAccountIdBox)) => {
    await AccountInfoClient.updateProfile(input);
    dispatch(accountMapBox.mergeItem(accountId, input));
  },
);

export const switchAccount = action(async (dispatch, select, accountId: number) => {
  await http.open('POST/business/account/v2/switch', { accountId });
  moegoBroadcastChannel.postMessage('switchAccount', { accountId });
});

export const updatePassword = action(async (dispatch, select, input: UpdatePasswordRequest) => {
  await AccountInfoClient.updatePassword(input);
  location.reload();
});

export const forgetPasswordSendCode = action(async (dispatch, select, email: string) => {
  const r = await http.open('POST/business/account/resetPassword/sendEmail', { email });
  return r;
});

export const forgetPasswordCheckCode = action(
  async (dispatch, select, input: OpenApiModels['POST/business/account/resetPassword/checkCode']['Req']) => {
    await http.open('POST/business/account/resetPassword/checkCode', input);
  },
);

export const forgetPasswordResetPassword = action(
  async (dispatch, select, input: OpenApiModels['POST/business/account/resetPassword']['Req']) => {
    await http.open('POST/business/account/resetPassword', input);
  },
);

export const updateAccountBusinessOrder = action(async (_dispatch, _select, businessIdList: number[]) => {
  await http.open('PUT/business/myAccount/business/order', { businessIdList });
});

export const getWebsocketToken = action(async (_dispatch, _select) => {
  const { token } = await http.open('POST/business/account/v2/ws');
  return token;
});

export const getRelevantAccountList = action(async (dispatch, select) => {
  const { accounts } = await AccountInfoClient.getRelevantAccounts({});
  const account = select(selectCurrentAccount);

  dispatch([
    accountMapBox.mergeItems([
      { ...account, id: account.id },
      ...accounts.map((item) => ({
        id: Number(item.id),
        firstName: item.firstName,
        lastName: item.lastName,
        email: item.email,
      })),
    ]),
    accountGroupItemMapBox.mergeItems(
      accounts.map((item) => ({
        ...item,
        accountId: Number(item.id),
        accountEmail: item.email,
        accountFirstName: item.firstName,
        accountLastName: item.lastName,
        ownKey: `${account.id}-${item.id}`,
        currentAccountId: account.id,
      })),
    ),
    accountGroupListBox.setList(
      account.id,
      accounts.map((r) => `${account.id}-${r.id}`),
    ),
  ]);

  return accounts;
});

/** 获取 enterprise 的 master 账号信息 */
const getEnterpriseMasterAccount = action(async (dispatch, _select) => {
  const { locationStaffs } = await StaffClient.getEnterpriseStaffsByWorkingLocationIds(
    {} as unknown as GetEnterpriseStaffsByWorkingLocationIdsParams,
  );

  const staffs = locationStaffs
    .flatMap((item) => item.staffs)
    .map((item) => {
      return {
        ...item,
        id: +item.id,
        roleId: +item.roleId,
        hireDate: +item.hireDate,
        accountId: +item.accountId,
        businessId: +item.businessId,
      };
    });
  dispatch(staffMapBox.mergeItems(staffs));
});
