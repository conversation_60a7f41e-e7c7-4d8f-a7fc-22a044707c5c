import { type ServicePriceUnit } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  type ServiceModelSource,
  type AdditionalServiceRule,
  type AutoRolloverRule,
  type ServiceModelPetCodeFilter,
  type LocationStaffOverrideRule,
  type CustomizedBreed,
} from '@moego/api-web/moego/models/offering/v1/service_models';

export interface AllServicesAttributes {
  duration: number;
  maxDuration: number;
  lodgingFilter: boolean;
  customizedLodgings: string[];

  breedFilter: boolean;
  customizedBreed: CustomizedBreed[];

  petSizeFilter: boolean;
  customizedPetSizes: string[];
  weightFilter: boolean;
  weightRange: number[];

  petCodeFilter: ServiceModelPetCodeFilter;

  coatFilter: boolean;
  customizedCoat: string[];

  autoRolloverRule: AutoRolloverRule;
  bundleServiceIds: string[];

  isEvaluationRequired: boolean;
  isEvaluationRequiredForOb: boolean;
  evaluationId: string;
  additionalServiceRule: AdditionalServiceRule;

  numSessions: number;
  durationSessionMin: number;
  capacity: number;
  isRequirePrerequisiteClass: boolean;
  prerequisiteClassIds: string[];

  locationStaffOverrideList: LocationStaffOverrideRule[];
  availableForAllStaff: boolean;
  availableStaffIdList: string[];
}

export interface ServicePricing {
  priceUnit: ServicePriceUnit;
  price: number;
  taxId: string;
}

export interface CompanyServiceView {
  serviceId: string;
  name: string;
  categoryId: string;
  description: string;
  isActive: boolean;
  images: string[];
  colorCode: string;
  careTypeId: string;
  source: ServiceModelSource;
  isAllBusiness: boolean;
  availableBusinessIdList: string[];
  sort?: string;

  // careTypeService 特性
  attributes: Partial<AllServicesAttributes>;
  pricing: ServicePricing;
}
