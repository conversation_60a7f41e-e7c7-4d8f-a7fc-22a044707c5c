import { getFeatureIsOn } from '../../../../utils/growthBook/growthBook';
import { currentCompanyIdBox } from '../../../company/company.boxes';
import { createServiceObservableAction } from '../../../observableServices/observableServices';
import { companyServiceListBox, companyServiceMapBox } from '../../companyService.boxes';
import { type CompanyServiceView } from '../../service.types';
import { type GetCompanyServiceInfoParams } from '../private/service.actions';
import { getCompanyFullServiceInfoList } from './service.actions';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';

export const getCompanyServiceList = createServiceObservableAction(
  'getCompanyServiceList',
  async (dispatch, select, input: GetCompanyServiceInfoParams, signal?: AbortSignal) => {
    const enableFulfillmentFlow = await getFeatureIsOn(GrowthBookFeatureList.EnableFulfillmentFlow);
    if (!enableFulfillmentFlow) {
      const res = await dispatch(getCompanyFullServiceInfoList(input, signal));
      return res;
    }

    const companyId = select(currentCompanyIdBox);
    const res: CompanyServiceView[] = [];
    // TODO:(peter) fulfillment flow get company list api
    dispatch([
      companyServiceMapBox.mergeItems(res),
      companyServiceListBox.setList(
        String(companyId),
        res.map((item) => String(item.serviceId)),
      ),
    ]);
  },
);
