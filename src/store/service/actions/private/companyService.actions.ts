import { getFeatureIsOn } from '../../../../utils/growthBook/growthBook';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';
import { currentCompanyIdBox } from '../../../company/company.boxes';
import { createServiceObservableAction } from '../../../observableServices/observableServices';
import { companyServiceListBox, companyServiceMapBox } from '../../companyService.boxes';
import { type CompanyServiceView } from '../../service.types';
import { removeService, sortService } from './service.actions';
import { action } from 'amos';
import { getCompanyServiceList } from '../public/companyService.actions';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export const createCompanyService = createServiceObservableAction(
  'createCompanyService',
  async (dispatch, select, input: Omit<CompanyServiceView, 'serviceId'>) => {
    const companyId = String(select(currentCompanyIdBox));

    // TODO:(peter) fulfillment flow Add service api call
    const res = await Promise.resolve({ input, serviceId: String(Math.random() * 10000) });
    dispatch([
      companyServiceMapBox.mergeItem(res.serviceId, res),
      companyServiceListBox.pushList(companyId, res.serviceId),
    ]);
    return res;
  },
);

export const updateCompanyService = createServiceObservableAction(
  'updateCompanyService',
  async (dispatch, select, input: CompanyServiceView, applyUpcomingAppt: boolean = false) => {
    // TODO:(peter) fulfillment flow Update service api call
    await Promise.resolve(applyUpcomingAppt);
    dispatch(companyServiceMapBox.mergeItem(input.serviceId, input));
    return input;
  },
);

export const deleteCompanyService = createServiceObservableAction(
  'deleteCompanyService',
  async (dispatch, select, serviceId: string, serviceType: number, serviceItemType: number) => {
    const enableFulfillmentFlow = await getFeatureIsOn(GrowthBookFeatureList.EnableFulfillmentFlow);
    const companyId = String(select(currentCompanyIdBox));

    if (enableFulfillmentFlow) {
      // TODO:(peter) fulfillment flow Delete service api call
      dispatch([companyServiceMapBox.deleteItem(serviceId), companyServiceListBox.deleteItem(companyId, serviceId)]);
    } else {
      await dispatch(removeService(Number(serviceId), serviceType, serviceItemType));
    }
  },
);

export const sortCompanyServices = action(
  async (dispatch, select, idList: string[], categoryId: number, types: number | string) => {
    const enableFulfillmentFlow = await getFeatureIsOn(GrowthBookFeatureList.EnableFulfillmentFlow);

    if (enableFulfillmentFlow) {
      // TODO:(peter) fulfillment flow Sort service api call
      await Promise.resolve();
      dispatch(
        getCompanyServiceList({
          businessIds: [],
          staffIds: [],
          // TODO:(peter) remove this line after fulfillment flow is implemented
          serviceType: ServiceType.SERVICE,
          serviceItemType: ServiceItemType.UNSPECIFIED,
        }),
      );
    } else {
      const serviceIdList = idList.map(Number);
      await dispatch(sortService(serviceIdList, categoryId, types));
    }
  },
);
