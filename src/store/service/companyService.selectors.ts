import { selector } from 'amos';
import { currentCompanyIdBox } from '../company/company.boxes';
import { companyServiceListBox, companyServiceMapBox } from './companyService.boxes';
import { isNormal } from '../utils/identifier';
import { group } from '../utils/utils';

export interface SelectCompanyServicesParams {
  businessId?: string;
  onlyCategory?: boolean;
  onlyService?: boolean;
  careTypeIds?: string[];
}

/**
 * get all company services without any filter
 */
export const selectAllCompanyServices = selector((select) => {
  const companyId = String(select(currentCompanyIdBox));
  const serviceIdList = select(companyServiceListBox).getList(companyId);
  const serviceList = serviceIdList.map((serviceId) => select(companyServiceMapBox.mustGetItem(serviceId)));
  return serviceList;
});

/**
 * filter company services by businessId and careTypeIds, default active only
 */
export const selectFilteredCompanyServices = selector(
  (select, businessId: string = '', careTypeIds?: string[], isActive: boolean = true) => {
    const serviceList = select(selectAllCompanyServices());

    const filteredServiceList = serviceList.filter((service) => {
      const isMatchBusiness = isNormal(businessId)
        ? service.isAllBusiness || service.availableBusinessIdList.includes(businessId)
        : true;
      const isMatchCareType = careTypeIds?.length ? careTypeIds.includes(service.careTypeId) : true;

      const isMatchStatus = service.isActive === isActive;

      return isMatchBusiness && isMatchCareType && isMatchStatus;
    });

    return filteredServiceList;
  },
);

/**
 * group company services by category
 */
export const selectGroupedCompanyServices = selector((select, businessId?: string, careTypeIds?: string[]) => {
  const filteredList = select(selectFilteredCompanyServices(businessId, careTypeIds));

  return group(filteredList.toArray(), (item) => item.categoryId);
});

/**
 * get all category ids of company services after filter by businessId and careTypeIds
 */
export const selectCompanyCategoryIds = selector((select, businessId?: string, careTypeIds?: string[]) => {
  const groupedServices = select(selectGroupedCompanyServices(businessId, careTypeIds));

  return groupedServices.map(([categoryId]) => categoryId);
});

/**
 * get all service ids of company services after filter by businessId and careTypeIds
 */
export const selectCompanyServiceIds = selector((select, businessId?: string, careTypeIds?: string[]) => {
  const filteredList = select(selectFilteredCompanyServices(businessId, careTypeIds));

  return filteredList.map((service) => service.serviceId);
});
