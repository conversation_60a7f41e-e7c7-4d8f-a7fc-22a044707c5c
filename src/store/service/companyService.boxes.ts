import { Record } from 'immutable';
import { createRecordMapBox } from '../utils/RecordMap';
import { createOwnListBox, OwnList } from '../utils/OwnList';
import { type CompanyServiceView } from './service.types';
import { ServiceModelSource } from '@moego/api-web/moego/models/offering/v1/service_models';
import { ServicePriceUnit } from '@moego/api-web/moego/models/offering/v1/service_enum';

export class CompanyServiceRecord extends Record<CompanyServiceView>({
  serviceId: '',
  categoryId: '',
  name: '',
  description: '',

  colorCode: '',
  careTypeId: '',
  isActive: true,
  images: [],
  source: ServiceModelSource.MOEGO_PLATFORM,
  isAllBusiness: true,
  availableBusinessIdList: [],
  sort: '',
  attributes: {
    duration: 0,
    maxDuration: 0,
    lodgingFilter: false,
    customizedLodgings: [],
    breedFilter: false,
    customizedBreed: [],

    petSizeFilter: false,
    customizedPetSizes: [],
    weightFilter: false,
    weightRange: [],

    petCodeFilter: {
      isAllPetCode: true,
      isWhiteList: true,
      petCodeIds: [],
    },

    coatFilter: false,
    customizedCoat: [],

    bundleServiceIds: [],
    numSessions: 0,
    durationSessionMin: 0,
    capacity: 0,
    isRequirePrerequisiteClass: false,
    prerequisiteClassIds: [],
    isEvaluationRequired: false,
    isEvaluationRequiredForOb: false,
    evaluationId: '',
    additionalServiceRule: {
      enable: false,
      minStayLength: 1,
      applyRules: [],
    },
    locationStaffOverrideList: [],
    availableForAllStaff: true,
    availableStaffIdList: [],
  },
  pricing: {
    price: 0,
    priceUnit: ServicePriceUnit.PER_SESSION,
    taxId: '',
  },
}) {}

export class BusinessServiceRecord extends Record({
  ownId: '',
  serviceId: '',
  price: 0,
  duration: 0,
  taxId: '',
  staffOverrideList: [],
}) {
  static ownKey(businessId: string, serviceId: string) {
    return `${businessId}-${serviceId}`;
  }
}

export const companyServiceListBox = createOwnListBox('company/services/list', OwnList.ss());
export const companyServiceMapBox = createRecordMapBox('company/service', CompanyServiceRecord, 'serviceId');
export const businessServiceMapBox = createRecordMapBox('business/service', BusinessServiceRecord, 'ownId');
