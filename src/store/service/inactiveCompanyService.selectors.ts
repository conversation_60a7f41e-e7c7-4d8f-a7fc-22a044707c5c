import { selector } from 'amos';
import { selectFilteredCompanyServices } from './companyService.selectors';

/**
 * select inactive service filtered by businessId and careTypeIds
 */
export const selectInactiveCompanyServices = selector((select, businessId: string = '', careTypeIds?: string[]) => {
  const filteredList = select(selectFilteredCompanyServices(businessId, careTypeIds, false));

  return filteredList.filter((service) => !service.isActive);
});
