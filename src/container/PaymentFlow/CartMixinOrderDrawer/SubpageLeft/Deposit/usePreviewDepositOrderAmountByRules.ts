import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { useDispatch, useStore } from 'amos';
import { groupBy } from 'lodash';
import { BFFOrderClient } from '../../../../../middleware/bff';
import { getListPetServices } from '../../../../../store/PaymentFlow/cart.action';
import { selectCartList, selectSelectedCartDetailList } from '../../../../../store/PaymentFlow/cart.selectors';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { getTaxList } from '../../../../../store/business/tax.actions';
import { taxMapBox } from '../../../../../store/business/tax.boxes';
import { serviceChargeMapBox } from '../../../../../store/service/service.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { productItemInfoMapBox } from '../../../TakePaymentDrawer/SubpageRight/AddOrderItemSubpage/modules/AddProducts/store/orderProducts.boxes';
import {
  convertCartItemsToCreateOrderItems,
  convertFeesItemsToCreateOrderItems,
  convertProductToCreateOrderItem,
  defaultTax,
} from '../../Cart/Cart.utils';
import { productSelectedBox } from '../../SubpageRight/AddMoreItemSubpage/modules/AddProducts/store/orderProducts.boxes';
import { type z } from '@moego/bff-openapi';
import { type schemas } from '@moego/bff-openapi/clients/client.order';

export const usePreviewDepositOrderAmountByRules = () => {
  const store = useStore();
  const dispatch = useDispatch();

  const resolveItems = (appointmentId: string) => {
    const sourceItems = store.select(selectSelectedCartDetailList(appointmentId));
    const { surcharges } = store.select(selectCartList(appointmentId));
    const product = store.select(productSelectedBox);

    const services = convertCartItemsToCreateOrderItems(sourceItems);
    const servicesCharges = convertFeesItemsToCreateOrderItems(
      surcharges.map((s) => {
        const { taxId } = store.select(serviceChargeMapBox.mustGetItem(s.serviceChargeId));
        const tax = store.select(taxMapBox.mustGetItem(taxId));
        return {
          ...s,
          tax: isNormal(taxId)
            ? {
                id: String(tax.id),
                name: tax.taxName,
                rate: String(tax.taxRate),
              }
            : { ...defaultTax },
        };
      }),
    );
    const products = product.map((p) => {
      const productInfo = store.select(productItemInfoMapBox.mustGetItem(p.id));
      const tax = store.select(taxMapBox.mustGetItem(productInfo.taxId));
      const business = store.select(selectCurrentBusiness);
      return convertProductToCreateOrderItem(productInfo, {
        externalUuid: p.externalUuid,
        staffId: p.staffId || '0',
        petId: '0',
        quantity: p.quantity,
        currencyCode: business.currencyCode,
        tax: isNormal(productInfo.taxId)
          ? { id: String(tax.id), name: tax.taxName, rate: String(tax.taxRate) }
          : { ...defaultTax },
      });
    });

    const allItems = services.concat(servicesCharges).concat(products);

    return {
      sourceItems,
      allItems,
    };
  };

  const previewDepositOrderAmountByRules = useLatestCallback(async (sourceId: string) => {
    await Promise.all([dispatch(getListPetServices({ id: sourceId })), dispatch(getTaxList())]);
    const { sourceItems } = resolveItems(sourceId);
    const petServicesMap = groupBy(sourceItems, 'pet.id');
    const petIds = Object.keys(petServicesMap);

    const { priceItems, orderDetail } = await BFFOrderClient.previewDepositOrderByRules({
      sourceId: sourceId,
      sourceType: OrderSourceType.APPOINTMENT,
      petServices: petIds.map((petId) => ({
        petId,
        services: petServicesMap[petId].map((s) => ({
          serviceId: s.serviceId,
          unitPrice: s.unitPrice,
          quantity: s.quantity,
          totalPrice: s.totalPrice,
          staffId: s.staffId,
          associatedServiceId: s.associatedServiceId,
          serviceItemType: s.serviceItemType,
          serviceType: s.serviceType,
          externalUuid: s.externalId,
        })) as unknown as z.infer<typeof schemas.PreviewDepositOrderByRulesRequest>['petServices'][number]['services'],
      })),
    });

    const remainAmount = orderDetail?.order?.remainAmount ?? undefined;

    return {
      remainAmount: remainAmount ? MoeMoney.fromMoney(remainAmount).valueOf() : undefined,
      priceItems,
    };
  });

  return previewDepositOrderAmountByRules;
};
