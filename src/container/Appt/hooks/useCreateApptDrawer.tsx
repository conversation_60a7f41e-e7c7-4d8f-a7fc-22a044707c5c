import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useStore } from 'amos';
import dayjs from 'dayjs';
import React from 'react';
import { useRouteMatch } from 'react-router';
import { PATH_GROOMING_CALENDAR, PATH_MAP_VIEW } from '../../../router/paths';
import { AutoMessageType } from '../../../store/autoMessage/autoMessage.boxes';
import {
  calendarQuickAddApptFields,
  calendarQuickAddApptVisible,
  calendarSelectedDate,
  quickAddPlaceholderBox,
} from '../../../store/calendarLatest/calendar.boxes';
import { type QuickAddSource } from '../../../store/calendarLatest/calendar.types';
import { deleteApptPreviewCardList } from '../../../store/calendarLatest/card.actions';
import { emitReloadMapViewAppts } from '../../../store/mapView/actions/public/mapView.actions';
import { selectMapViewConfig } from '../../../store/mapView/mapView.selectors';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { useCloseAllDrawer } from '../../../utils/hooks/useCloseAllDrawer';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';
import { toggleIntercom } from '../../../utils/hooks/useToggleIntercom';
import { ApptEvent } from '../events/events';
import { DrawerLazyWrapper, type DrawerWrapperProps } from '../modules/QuickAddAppt/QuickAddApptDrawer.lazy';
import { clearCreateApptData, setNewApptPreset, updateQuickAddConfig } from '../store/appt.actions';
import { CreateApptId } from '../store/appt.types';
import { usePreventGo2QuickAdd } from './usePreventGo2QuickAdd';
import { useTicketAlerts } from './useTicketAlerts';

export interface OpenProps extends Pick<DrawerWrapperProps, 'onReady'> {
  /**
   * params 的数据会放在 quickAddConfig 里面，它标识一些与petService 无关的参数
   */
  params?: {
    clientId?: number;
    source?: QuickAddSource;
    disableViewClientLastAppt?: boolean;
    allPetsStartAtSameTime?: boolean;
    alertNotes?: string;
    colorCode?: string;
  };
  /**
   * preset 代表这些字段会做为 placeholder service 的值，它会放在appt service 里面
   * 它的作用是快速同步卡片和在后续的 flow 作为填充数据
   */
  preset?: {
    appointmentStart?: dayjs.Dayjs;
    appointmentEnd?: dayjs.Dayjs;
    staffId?: number;
    lodgingId?: string;
    serviceItemType?: ServiceItemType;
  };
}

export const useCreateApptDrawer = () => {
  const dispatch = useDispatch();
  const store = useStore();
  const { mountDrawer } = useFloatableHost();
  const { closeAllDrawer } = useCloseAllDrawer();

  const preventGo2QuickAdd = usePreventGo2QuickAdd();
  const { fallback } = useGetStartTime();
  const inCalendarView = useRouteMatch(PATH_GROOMING_CALENDAR);
  const openTicketAlerts = useTicketAlerts();

  const resolvePreset = (v?: OpenProps['preset']) => {
    const { appointmentStart, appointmentEnd, ...rest } = v || {};
    const start = v?.appointmentStart || fallback();

    return {
      ...rest,
      startDate: start.format(DATE_FORMAT_EXCHANGE),
      startTime: start.getMinutes(),
      endDate: appointmentEnd?.format(DATE_FORMAT_EXCHANGE),
      endTime: appointmentEnd?.getMinutes(),
    };
  };

  const resolveProps = (v?: OpenProps) => {
    return {
      ...v,
      preset: resolvePreset(v?.preset),
      params: v?.params || {},
    };
  };

  const onClose = () => {
    toggleIntercom(false);
    dispatch([
      deleteApptPreviewCardList(CreateApptId), // 清理可能存在的 preview 卡片
      calendarQuickAddApptFields.setState(calendarQuickAddApptFields.initialState), // 清理默认 config
      clearCreateApptData(), // 清理 appt pet service 数据
      calendarQuickAddApptVisible.setState(false),
    ]);
  };

  const beforeOpen = async (v: ReturnType<typeof resolveProps>) => {
    const { preset, params } = v;

    closeAllDrawer();
    toggleIntercom(true);

    await dispatch([
      // 这里也可以通过给 drawer props 来实现，但是为了方便现有的逻辑，我们先保持
      // 如果用 props 来实现，那好处是不需要 reset，但是缺点是外部可能无法感知上下文
      updateQuickAddConfig(params),

      // placeholder card，最大的作用是用来 drawer 与 calendar 互相直接同步数据
      setNewApptPreset(preset),

      // 清理 placeholder card
      quickAddPlaceholderBox.setState(quickAddPlaceholderBox.initialState),

      // 让外部感知到 drawer 已打开
      calendarQuickAddApptVisible.setState(true),
    ]);

    if (inCalendarView) {
      // 定位 calendar 时图为 新 appt 的日期
      // 这里大概率只有 book again 会用到，因为其他情况，我们默认取得就是当前 calendar date 的时间
      const nextDate = dayjs(preset.startDate);
      const calendarViewDate = store.select(calendarSelectedDate);
      !calendarViewDate.isSame(nextDate, 'day') && dispatch(calendarSelectedDate.setState(nextDate));
    }
  };

  const open = async (p?: OpenProps) => {
    if (preventGo2QuickAdd) {
      return;
    }

    const props = resolveProps(p);

    await beforeOpen(props);

    const { promise, closeFloatable: closeModal } = mountDrawer(
      <DrawerLazyWrapper
        onClose={() => {
          onClose();
          closeModal();
        }}
        onCreated={async ({ ticketId, clientId }) => {
          onClose();
          closeModal();

          openTicketAlerts({
            ticketId,
            customerId: clientId,
            mode: AutoMessageType.AppointmentBooked,
          });

          dispatch(emitReloadMapViewAppts()); // 我擦，怎么 map view 有自己的事件
          ApptEvent.created.emit({ apptId: String(ticketId) });
        }}
        onReady={props.onReady}
      />,
    );

    return promise;
  };

  return open;
};

export const useGetStartTime = () => {
  const store = useStore();
  const inMapView = useRouteMatch(PATH_MAP_VIEW.path);
  const inCalendarView = useRouteMatch(PATH_GROOMING_CALENDAR);

  const followCalendar = () => {
    let apptDateTime = dayjs();
    const calendarViewDate = store.select(calendarSelectedDate);
    if (calendarViewDate.isValid()) {
      apptDateTime = calendarViewDate.setMinutes(apptDateTime.getMinutes());
    }
    return apptDateTime;
  };

  const followMapView = () => {
    let apptDateTime = dayjs();
    const { selectedDate } = store.select(selectMapViewConfig());
    if (selectedDate.isValid()) {
      apptDateTime = selectedDate.setMinutes(apptDateTime.getMinutes());
    }
    return apptDateTime;
  };

  const fallback = () => {
    // calendar or map view 下，quick add 为当前页面选择的时间，startTime 为当前时间
    if (inMapView) {
      return followMapView();
    } else if (inCalendarView) {
      return followCalendar();
    }
    return dayjs();
  };

  return {
    followMapView,
    followCalendar,
    fallback,
  };
};
