import React, { memo } from 'react';
import { useQuickAddConfig } from '../hooks/useQuickAddConfig';
import { DrawerRouteName, useCreateApptRouterContext } from '../QuickAddApptDrawer.router';
import { Comments, type CommentsProps } from './Comments';

export const CommentsCreate = memo<Pick<CommentsProps, 'clientId'>>((props) => {
  const { clientId } = props;
  const drawerRouter = useCreateApptRouterContext();
  const { tab, petId } = drawerRouter.getParams(DrawerRouteName.Comment) || {};
  const [{ ticketComment }, setQuickAddFields] = useQuickAddConfig();

  return (
    <Comments
      clientId={clientId}
      activeTab={tab}
      petId={petId}
      comments={ticketComment}
      onCommentsChange={(value) => setQuickAddFields({ ticketComment: value })}
    />
  );
});
