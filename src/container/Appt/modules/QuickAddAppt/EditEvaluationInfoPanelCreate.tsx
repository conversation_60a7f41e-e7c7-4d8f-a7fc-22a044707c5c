import React, { memo } from 'react';
import { EditEvaluationInfoPanel } from '../../components/Evaluation/EditEvaluationInfoPanel';
import { DrawerRouteName, useCreateApptRouterContext } from './QuickAddApptDrawer.router';

export const EditEvaluationInfoPanelCreate = memo(() => {
  const drawerRouter = useCreateApptRouterContext();
  const { payload } = drawerRouter.getParams(DrawerRouteName.EditPetEvaluation);
  return <EditEvaluationInfoPanel payload={payload} />;
});
