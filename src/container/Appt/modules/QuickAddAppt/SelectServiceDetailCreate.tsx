import React, { memo } from 'react';
import { SelectServiceDetail } from '../../components/SelectServiceDetail/SelectServiceDetail';
import { DrawerRouteName, useCreateApptRouterContext } from './QuickAddApptDrawer.router';

export const SelectServiceDetailCreate = memo(() => {
  const drawerRouter = useCreateApptRouterContext();
  const payload = drawerRouter.getParams(DrawerRouteName.SelectServiceDetail);
  return <SelectServiceDetail payload={payload} />;
});
