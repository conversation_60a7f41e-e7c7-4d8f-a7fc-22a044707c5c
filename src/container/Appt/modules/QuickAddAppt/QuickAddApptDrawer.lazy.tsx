import { Spin } from '@moego/ui';
import React, { lazy, memo, Suspense } from 'react';
import { CalendarDrawer } from '../../../Calendar/latest/ApptCalendar/components/CalendarDrawer';
import { type QuickAddApptDrawerProps } from './QuickAddApptDrawer';
import { useQuickAddConfig } from './hooks/useQuickAddConfig';
import { useDraftConfirmConfig, useQuickAddLeaveConfirm } from './hooks/useQuickAddLeaveConfirm';

const LazyComponent = lazy(() =>
  import('./QuickAddApptDrawer').then(({ QuickAddApptDrawer }) => ({
    default: QuickAddApptDrawer,
  })),
);

export interface DrawerWrapperProps extends Pick<QuickAddApptDrawerProps, 'onClose' | 'onCreated' | 'onReady'> {}

export const DrawerLazyWrapper = memo((props: DrawerWrapperProps) => {
  const { onClose, onReady, onCreated } = props;
  const { asyncDoubleConfirm } = useDraftConfirmConfig();
  const [{ isDirty }] = useQuickAddConfig();

  const handleClose = (checkDirty = true) => {
    if (checkDirty && isDirty) {
      asyncDoubleConfirm({
        closable: true,
        onOk: () => Promise.resolve(),
        onCancel: onClose,
      });
      return;
    }
    onClose?.();
  };

  useQuickAddLeaveConfirm(handleClose);

  return (
    <CalendarDrawer isOpen onClose={handleClose}>
      <Suspense
        fallback={
          <div className="moe-w-full moe-h-full moe-flex moe-justify-center moe-items-center">
            <Spin isLoading />
          </div>
        }
      >
        <LazyComponent onClose={handleClose} onCreated={onCreated} onReady={onReady} />
      </Suspense>
    </CalendarDrawer>
  );
});
