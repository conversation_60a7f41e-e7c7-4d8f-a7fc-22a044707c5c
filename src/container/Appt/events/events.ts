import EventEmitter from 'eventemitter3';

const eventEmitter = new EventEmitter();

enum EVENT_MAP {
  created = 'created',
  refresh = 'refresh',
}

const wrapper = <T>(signal: EVENT_MAP) => {
  return {
    on(fn: (payload?: T) => void) {
      eventEmitter.on(signal, fn);
      return () => {
        eventEmitter.off(signal, fn);
      };
    },
    emit(payload?: T) {
      eventEmitter.emit(signal, payload);
    },
    listenerCount() {
      return eventEmitter.listenerCount(signal);
    },
  };
};

export const ApptEvent = {
  created: wrapper<{ apptId: string }>(EVENT_MAP.created),
  refresh: wrapper(EVENT_MAP.refresh),
};
