<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC
  "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
  "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
  <context id="Postgresql" targetRuntime="MyBatis3DynamicSql" defaultModelType="flat">
    <property name="beginningDelimiter" value="`"/>
    <property name="endingDelimiter" value="`"/>
    <property name="javaFileEncoding" value="UTF-8"/>

    <commentGenerator>
      <property name="suppressDate" value="true"/>
      <property name="addRemarkComments" value="true"/>
    </commentGenerator>

    <jdbcConnection
      driverClass="org.postgresql.Driver"
      connectionURL="******************************************************************"
      userId="moego_developer_240310_eff7a0dc"
      password="G0MxI7NM_jX_f7Ky73vnrwej97xg1tly"
    />

    <javaModelGenerator
      targetPackage="com.moego.svc.googlepartner.entity"
      targetProject="src/main/java"
    >
      <property name="trimStrings" value="true"/>
    </javaModelGenerator>

    <javaClientGenerator
      targetPackage="com.moego.svc.googlepartner.mapper"
      targetProject="src/main/java"
    />

    <table tableName="google_reserve_integration">
      <generatedKey column="id" sqlStatement="JDBC"/>
    </table>
  </context>
</generatorConfiguration>
