package com.moego.server.payment.service;

import static com.moego.common.enums.PaymentMethodEnum.METHOD_NAME_SQUARE;
import static com.moego.common.enums.PaymentMethodEnum.METHOD_NAME_STRIPE;
import static com.moego.common.enums.PaymentMethodEnum.MODULE_FULFILLMENT;
import static com.moego.common.enums.PaymentMethodEnum.MODULE_GROOMING;
import static com.moego.common.enums.PaymentMethodEnum.MODULE_RETAIL;
import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.common.utils.PageUtil.hasEmptyCollectionFilter;
import static com.moego.common.utils.PageUtil.selectPage;
import static java.util.stream.Collectors.groupingBy;

import com.alibaba.fastjson.JSONObject;
import com.moego.common.StripePaymentMethodEnum;
import com.moego.common.constant.ActiveMQConstant;
import com.moego.common.constant.CommonConstant;
import com.moego.common.constant.Dictionary;
import com.moego.common.constant.OrderConstant;
import com.moego.common.distributed.LockManager;
import com.moego.common.dto.PaymentSummary;
import com.moego.common.dto.clients.BusinessClientsDTO;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.dto.clients.FilterDTO;
import com.moego.common.dto.notificationDto.NotificationExtraInvoicePaidDto;
import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.MessageConst;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.enums.PropertyEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.StripeApi;
import com.moego.common.enums.payment.SplitSyncRecordTypeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.GsonUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.PaymentUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.appointment.v1.AppointmentNoShowStatus;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderPaymentStatus;
import com.moego.idl.models.organization.v1.TimeZone;
import com.moego.idl.models.payment.v1.PaymentMethodExtra;
import com.moego.idl.models.split_payment.v1.Vendor;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.CancelAppointmentRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.metadata.v1.DescribeMetadataRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v1.PayOrderRequest;
import com.moego.idl.service.order.v1.PayOrderResponse;
import com.moego.idl.service.order.v1.SetTipsRequest;
import com.moego.idl.service.order.v1.UpdateOrderPaymentRequest;
import com.moego.idl.service.order.v1.UpdateOrderPaymentResponse;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.GetLocationDetailRequest;
import com.moego.idl.service.organization.v1.QueryCompaniesByIdsRequest;
import com.moego.lib.actimvemq.autoconfigure.MoeMessageSender;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.common.util.RequestUtils;
import com.moego.lib.utils.DateTimeUtils;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.ICustomerGroomingClient;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.GroomingCustomerInfoDTO;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.customer.params.GroomingQueryCustomerParams;
import com.moego.server.grooming.api.IGroomingAppointmentService;
import com.moego.server.grooming.client.IBookOnlineDepositClient;
import com.moego.server.grooming.client.IDepositClient;
import com.moego.server.grooming.client.IGroomingInvoiceClient;
import com.moego.server.grooming.client.IQuickBooksClient;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.DepositDto;
import com.moego.server.grooming.dto.InvoiceSummaryDTO;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.server.grooming.params.DepositVo;
import com.moego.server.grooming.params.InvoiceValueType;
import com.moego.server.grooming.params.MoeBookOnlineDepositBatchQueryByCompanyVO;
import com.moego.server.grooming.params.MoeBookOnlineDepositBatchQueryVO;
import com.moego.server.grooming.params.MoeBookOnlineDepositVO;
import com.moego.server.grooming.params.SetPaymentParams;
import com.moego.server.message.client.IGroomingEmailClient;
import com.moego.server.message.client.IMessageClient;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.params.CardLinkParam;
import com.moego.server.message.params.notification.NotificationInvoicePaidParams;
import com.moego.server.payment.constant.TimeConstant;
import com.moego.server.payment.dto.AssociationAccountDTO;
import com.moego.server.payment.dto.AssociationStripeDTO;
import com.moego.server.payment.dto.ChargeByConnectedAccountRequest;
import com.moego.server.payment.dto.CreateCardRequest;
import com.moego.server.payment.dto.CreateCustomerRequest;
import com.moego.server.payment.dto.CustomerPaymentDTO;
import com.moego.server.payment.dto.DeleteCardForCustomerRequest;
import com.moego.server.payment.dto.GetSquareTokenResponse;
import com.moego.server.payment.dto.InvoicePaymentDto;
import com.moego.server.payment.dto.MoeGoPayTransactionSummaryDto;
import com.moego.server.payment.dto.PaymentAmountSumDto;
import com.moego.server.payment.dto.PaymentAntiFraudCollectDTO;
import com.moego.server.payment.dto.PaymentDTO;
import com.moego.server.payment.dto.PaymentDetailDTO;
import com.moego.server.payment.dto.PaymentIntentRequest;
import com.moego.server.payment.dto.PaymentListDto;
import com.moego.server.payment.dto.RefundDTO;
import com.moego.server.payment.dto.SyncOrderPaymentDetail;
import com.moego.server.payment.dto.square.SquarePaymentRequest;
import com.moego.server.payment.dto.square.SquareTakePaymentResponse;
import com.moego.server.payment.dto.square.SquareTerminalCheckoutResponse;
import com.moego.server.payment.dto.stripe.ProcessingFeeDTO;
import com.moego.server.payment.helper.BookingRequestHelper;
import com.moego.server.payment.helper.OBDepositHelper;
import com.moego.server.payment.mapper.MmStripeAccountMapper;
import com.moego.server.payment.mapper.MmStripeCustomerMapper;
import com.moego.server.payment.mapper.MoeCreditCardMapper;
import com.moego.server.payment.mapper.PaymentMapper;
import com.moego.server.payment.mapper.RefundMapper;
import com.moego.server.payment.mapper.po.StripeTransactionSummaryPo;
import com.moego.server.payment.mapperbean.BusinessSquare;
import com.moego.server.payment.mapperbean.MmStripeAccount;
import com.moego.server.payment.mapperbean.MmStripeCustomer;
import com.moego.server.payment.mapperbean.MoePayDetail;
import com.moego.server.payment.mapperbean.MoePreAuthRecord;
import com.moego.server.payment.mapperbean.Payment;
import com.moego.server.payment.mapperbean.Refund;
import com.moego.server.payment.mapstruct.PaymentStructMapper;
import com.moego.server.payment.params.CardRequestParams;
import com.moego.server.payment.params.CreatePaymentParams;
import com.moego.server.payment.params.DescribePaymentsParams;
import com.moego.server.payment.params.PaymentRecordParam;
import com.moego.server.payment.params.PaymentSigRequest;
import com.moego.server.payment.service.params.PaymentRecord;
import com.moego.server.payment.service.params.SquareRefundHookParam;
import com.moego.server.payment.service.params.StripeCustomerParam;
import com.moego.server.payment.service.params.StripePaymentUpdateParam;
import com.moego.server.payment.service.params.UpdatePaymentResultParams;
import com.moego.server.payment.service.util.BusinessInfoHelper;
import com.moego.server.payment.service.util.TipsUtil;
import com.moego.server.payment.util.BusinessDateFormatUtil;
import com.moego.server.payment.web.dto.BusinessHasValidAccountResponse;
import com.moego.server.payment.web.dto.PayoutViewList;
import com.moego.server.payment.web.dto.TransactionHistoryExportDTO;
import com.moego.server.payment.web.vo.TransactionHistoryReq;
import com.moego.server.retail.client.IRetailInvoiceClient;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import com.stripe.exception.StripeException;
import com.stripe.model.Account;
import com.stripe.model.Charge;
import com.stripe.model.Customer;
import com.stripe.model.PaymentIntent;
import com.stripe.model.PaymentMethod;
import com.stripe.model.PaymentMethodCollection;
import com.stripe.net.RequestOptions;
import com.stripe.param.CustomerUpdateParams;
import com.stripe.param.PaymentIntentRetrieveParams;
import com.stripe.param.PaymentIntentUpdateParams;
import jakarta.jms.Topic;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.command.ActiveMQTextMessage;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class PaymentService {

    private static final int MAX_PAYOUT_LIMIT = 100;

    @Autowired
    private PaymentMapper paymentMapper;

    @Autowired
    private MoeCreditCardMapper moeCreditCardMapper;

    @Autowired
    private RefundMapper refundMapper;

    @Autowired
    private MmStripeCustomerMapper stripeCustomerMapper;

    @Autowired
    private StripeService stripeService;

    @Autowired
    private SquareService squareService;

    @Autowired
    private IQuickBooksClient quickBooksClient;

    @Autowired
    private IGroomingInvoiceClient groomingInvoiceClient;

    @Autowired
    private IDepositClient iDepositClient;

    @Autowired
    private IRetailInvoiceClient retailInvoiceClient;

    @Autowired
    private ICustomerGroomingClient iCustomerGroomingClient;

    @Autowired
    private INotificationClient iNotificationClient;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private MmStripeAccountMapper stripeAccountMapper;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private IGroomingEmailClient iGroomingEmailClient;

    @Autowired
    private IGroomingAppointmentService groomingAppointmentService;

    @Autowired
    private PaymentSettingService paymentSettingService;

    @Autowired
    private IBookOnlineDepositClient iBookOnlineDepositClient;

    @Autowired
    private OBDepositHelper obDepositHelper;

    @Autowired
    private PreAuthService preAuthService;

    @Autowired
    private PayDetailService payDetailService;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    @Autowired
    private MoeMessageSender moeMessageSender;

    @Autowired
    private IMessageClient iMessageClient;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;

    @Autowired
    private BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerServiceBlockingStub;

    @Autowired
    private MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    @Autowired
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    @Autowired
    @Qualifier("paymentDataCollectTopic")
    private Topic paymentDataCollectTopic;

    @Value("${order.offset.retail}")
    private Long retailOffset;

    public static final String VENDOR_ALL = "all";
    public static final String REFUND_PAYMENT_METHOD = "MoeGo Pay Account Balance";
    private static final String FAILED = "failed";

    @Autowired
    private SplitPaymentService splitPaymentService;

    @Autowired
    private AppointmentServiceBlockingStub appointmentService;

    @Autowired
    private BookingRequestHelper bookingRequestHelper;

    @Autowired
    private TransactionOperations transactionOperations;

    /**
     * 更新 order id, customer id等信息， 只针对 prepay 场景
     *
     * @param paymentDTO
     * @return
     */
    public Boolean updatePaymentRecordById(PaymentDTO paymentDTO) {
        if (paymentDTO.getId() == null) {
            return false;
        }
        // 目前只更新invoiceId、customerId
        Payment updateRecord = new Payment();
        updateRecord.setId(paymentDTO.getId());
        updateRecord.setGroomingId(paymentDTO.getGroomingId());
        updateRecord.setInvoiceId(paymentDTO.getInvoiceId());
        updateRecord.setCustomerId(paymentDTO.getCustomerId());
        updateRecord.setUpdateTime(CommonUtil.get10Timestamp());
        updateRecord.setStatus(paymentDTO.getStatus());
        boolean result = paymentMapper.updateByPrimaryKeySelective(updateRecord) > 0;
        // ob prepay capture支付后才有 invoice id、customer id 等信息, 现在创建 order payment 记录, 并回填
        ThreadPool.execute(() -> {
            Payment payment = paymentMapper.selectByPrimaryKey(paymentDTO.getId());
            // 首次创建时，payment amount 包含了 convenience fee、tips，这里需要去掉 convenience fee
            BookOnlineDepositDTO depositDTO =
                    iBookOnlineDepositClient.getOBDepositByPaymentId(payment.getBusinessId(), payment.getId());
            if (depositDTO != null) {
                payment.setAmount(payment.getAmount().subtract(depositDTO.getConvenienceFee()));
            }
            // payment record 创建时无 order id，现在创建 order payment 记录
            syncCreateOrderPayment(payment, payment.getTips());
            // update order payment id to db
            Payment toUpdate = new Payment();
            toUpdate.setId(paymentDTO.getId());
            toUpdate.setOrderPaymentId(payment.getOrderPaymentId());
            paymentMapper.updateByPrimaryKeySelective(toUpdate);
        });
        return result;
    }

    public PaymentSummary.PaymentDto createPaymentAndConfirm(Integer businessId, CreatePaymentParams params)
            throws StripeException {
        MmStripeAccount stripeAccount = stripeAccountMapper.selectByBusinessId(businessId);
        if (stripeAccount == null) {
            throw new CommonException(ResponseCodeEnum.STRIPE_ACCOUNT_NOT_FOUND);
        }

        String stripeCustomerId = getStripeCustomerIdWithCreate(businessId, params.getCustomerId());
        if (!StringUtils.hasText(stripeCustomerId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Invalid customer");
        }

        params.setMethod(PaymentMethodEnum.METHOD_NAME_CREDIT_CARD);

        String cardId = null;
        if (!StringUtils.hasText(params.getStripePaymentMethodId())) {
            if (!StringUtils.hasText(params.getChargeToken())) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "chargeToken is required.");
            }

            CreateCardRequest createCardRequest = new CreateCardRequest();
            createCardRequest.setStripeAccountId(stripeAccount.getStripeAccountId());
            createCardRequest.setStripeCustomerId(stripeCustomerId);
            createCardRequest.setChargeToken(params.getChargeToken());
            createCardRequest.setCustomerId(params.getCustomerId());
            createCardRequest.setSaveCard(false);
            createCardRequest.setByPreAuth(false);
            cardId = stripeService.createCard(createCardRequest);
            params.setStripePaymentMethodId(cardId);
        }

        MoePreAuthRecord preAuthRecord = preAuthService.getPreAuthRecordByInvoiceId(businessId, params.getInvoiceId());
        if (preAuthRecord != null && GroomingAppointmentEnum.SOURCE_OB.equals(preAuthRecord.getTicketSource())) {
            BookOnlineDepositDTO deposit =
                    obDepositHelper.getOBDeposit(preAuthRecord.getBusinessId(), preAuthRecord.getTicketId());
            if (deposit != null
                    && deposit.getBookingFee() != null
                    && deposit.getBookingFee().doubleValue() > 0
                    && !Objects.equals(deposit.getStatus(), BookOnlineDepositConst.PAID)) {
                OrderModel order = orderClient.getOrder(GetOrderRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setId(preAuthRecord.getInvoiceId())
                        .build());
                if (order.getPaidAmount() <= 0) { // 限定首次支付
                    params.setGuid(deposit.getGuid()); // update payment id to ob deposit
                    params.setFromPreAuth(true);
                    // 待存量含 booking fee的 OB request执行完毕后再清理相关 booking fee 代码
                    params.setAmount(params.getAmount().add(deposit.getBookingFee()));
                    params.setBookingFeeAmount(deposit.getBookingFee());
                    return createPayment(businessId, params, Boolean.TRUE, Boolean.TRUE);
                }
            }
        }
        return createPayment(businessId, params, Boolean.TRUE, Boolean.FALSE);
    }

    /**
     * 发起支付流程并创建payment记录
     *
     * @param businessId    商家id
     * @param paymentParams 请求参数
     * @param confirm       是否confirm payment intent
     * @param isFromOB      是否是来自OB的请求
     * @return 支付信息
     */
    public PaymentSummary.PaymentDto createPayment(
            Integer businessId, CreatePaymentParams paymentParams, Boolean confirm, Boolean isFromOB) {
        return createPayment(businessId, paymentParams, confirm, isFromOB, false);
    }

    // 先组装好数据 -> insert payment -> create payment intent -> update payment  减少local和vendor之间交互的间隔,降低失败率
    // 2024/10/11 insert payment之前先创建 orderPayment， 并记录 orderPaymentId 后续流程不变
    public PaymentSummary.PaymentDto createPayment(
            Integer businessId,
            CreatePaymentParams paymentParams,
            Boolean confirm,
            Boolean isFromOB,
            boolean withRawData) {
        PaymentSummary.PaymentDto paymentDto = new PaymentSummary.PaymentDto();
        // 检查定金支付状态
        if (MODULE_GROOMING.equals(paymentParams.getModule())) {
            checkDepositConflict(businessId, paymentParams);
        } else if (MODULE_RETAIL.equals(paymentParams.getModule())) {
            log.debug("retail payment, no need to check deposit conflict");
        } else {
            log.error("Invalid module for {} invoice {}.", paymentParams.getModule(), paymentParams.getInvoiceId());
        }
        Payment payment = getPaymentByPaymentIntentIdWithCreate(businessId, paymentParams, withRawData);
        ActivityLogRecorder.record(Action.CREATE, ResourceType.PAYMENT, payment.getId(), paymentParams);
        // 更新支付记录
        Integer primaryId = payment.getId();
        Payment toUpdateStatusRecord = new Payment();
        toUpdateStatusRecord.setId(primaryId);
        // payment 记录初始化状态为 created，根据情况更新为 processing/paid，或者不更新
        toUpdateStatusRecord.setStatus(PaymentStatusEnum.CREATED);
        PaymentIntentRequest req = new PaymentIntentRequest();
        if (PaymentUtil.isCreditCard(payment.getMethod())) {
            // 使用信用卡或者第三方支付（apple/google pay）进行支付，校验商家是否开启 stripe
            MmStripeAccount stripeAccount = stripeAccountMapper.selectByBusinessId(businessId);
            if (stripeAccount == null
                    || stripeAccount.getStripeAccountId() == null
                    || stripeAccount.getStripeAccountId().isEmpty()) {
                throw new CommonException(ResponseCodeEnum.STRIPE_ACCOUNT_NOT_FOUND);
            }

            // 查询business
            InfoIdParams businessIdParams = new InfoIdParams();
            businessIdParams.setInfoId(businessId);
            MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
            // create or update payment intent
            req.setIsOnline(paymentParams.getIsOnline());
            // 如果存在 payment intent id 说明是更新
            if (StringUtils.hasText(paymentParams.getPaymentIntentId())) {
                req.setPaymentIntentId(paymentParams.getPaymentIntentId());
            }
            req.setIsPreAuth(paymentParams.getFromPreAuth() != null && paymentParams.getFromPreAuth());
            req.setPreAuthId(paymentParams.getPreAuthId());
            // 根据是否设置locationId 判断是否为reader支付
            req.setStripeTerminal(StringUtils.hasText(paymentParams.getLocationId()));
            req.setSaveCard(paymentParams.getSaveCard());
            req.setStripePaymentMethod(paymentParams.getStripePaymentMethod());
            req.setCustomerId(paymentParams.getCustomerId());
            // 添加 invoiceId 到 strip PaymentIntent 的meta属性
            req.setInvoiceId(paymentParams.getInvoiceId());
            req.setPrimaryId(primaryId);
            req.setConfirm(confirm);
            // amount: in cents
            // For example, to charge 10 USD, provide an amount value of 1000 (i.e., 1000 cents).
            req.setAmount(
                    paymentParams.getAmount().multiply(new BigDecimal(100)).intValue());
            req.setCurrency(stripeAccount.getDefaultCurrency());
            req.setBusinessName(businessDto.getBusinessName());
            req.setStripeAccountId(stripeAccount.getStripeAccountId());
            req.setTipsAmount(paymentParams.getTipsAmount());
            req.setBookingFeeAmount(paymentParams.getBookingFeeAmount());
            req.setIsFromOB(isFromOB);
            req.setCompanyId(Long.valueOf(businessDto.getCompanyId()));
            req.setIsCancellationFee(paymentParams.getIsCancellationFee());
            if (Boolean.TRUE.equals(paymentParams.getAddProcessingFee())
                    && CommonConstant.ENABLE.equals(
                            paymentSettingService.getPaymentSetting(businessId).getAutoCancelFeeByClient())
                    && stripeService.isDebitCard(paymentParams.getStripePaymentMethodId())) {
                paymentParams.setAddProcessingFee(false);
            }
            // 接口参数传addProcessingFee = true时，检查是否可以添加，计算并保存convenienceFee
            if (Boolean.TRUE.equals(paymentParams.getAddProcessingFee())
                    && paymentSettingService.isProcessingFeeAvailable(businessDto, false)) {
                // 如果在支付过程中添加了tips，计算Convenience Fee时应该去掉这一部分，否则用户实际支付的金额会比invoice上展示的多
                Integer convenienceFee = stripeService.getConvenienceFeeWithoutTips(businessId, req);
                req.setAddProcessingFee(paymentParams.getAddProcessingFee());
                req.setConvenienceFee(convenienceFee);
            }

            try {
                if (!PrimitiveTypeUtil.isNullOrZero(paymentParams.getCustomerId())) {
                    String stripeCustomerId = getStripeCustomerIdWithCreate(businessId, paymentParams.getCustomerId());
                    if (StringUtils.hasText(stripeCustomerId)) {
                        req.setStripeCustomerId(stripeCustomerId);
                        paymentDto.setStripeCustomerId(stripeCustomerId);
                    }
                } else {
                    // 如果需要save card，并且customerId为空时，需要检查customerData，并创建一个stripe customer
                    if (Boolean.TRUE.equals(paymentParams.getSaveCard())
                            && !stripeService.isStripeCustomerExisting(paymentParams.getPaymentIntentId())) {
                        String stripeCustomerId = createStripeCustomerBeforePayment(stripeAccount, paymentParams);
                        req.setStripeCustomerId(stripeCustomerId);
                        paymentDto.setStripeCustomerId(stripeCustomerId);
                    }
                }
                if (StringUtils.hasText(paymentParams.getStripePaymentMethodId())) {
                    req.setStripePaymentMethodId(paymentParams.getStripePaymentMethodId());
                }
                paymentDto.setStripeAccountId(stripeAccount.getStripeAccountId());
                // set stripePaymentMethod for different fee
                PaymentIntent paymentIntent = stripeService.createOrUpdatePaymentIntent(businessId, req);
                // create/update payment intent时，重新更新一下amount值
                BigDecimal actualPaymentAmount = BigDecimal.valueOf(paymentIntent.getAmount())
                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.CEILING);
                BigDecimal bookingFee = req.getBookingFeeAmount() == null ? BigDecimal.ZERO : req.getBookingFeeAmount();
                // 更新到数据库的金额需要减去booking fee
                toUpdateStatusRecord.setAmount(actualPaymentAmount.subtract(bookingFee));
                toUpdateStatusRecord.setTips(req.getTipsAmount());
                toUpdateStatusRecord.setStripeIntentId(paymentIntent.getId());
                toUpdateStatusRecord.setStripeClientSecret(paymentIntent.getClientSecret());
                toUpdateStatusRecord.setVendor(METHOD_NAME_STRIPE);
                // return pi for stripe reader
                payment.setAmount(actualPaymentAmount);
                payment.setStripeIntentId(toUpdateStatusRecord.getStripeIntentId());
                payment.setStripeClientSecret(toUpdateStatusRecord.getStripeClientSecret());
                // stripe create payment intent接口返回的status字段，用于前端判断是否需要3ds
                paymentDto.setStripeStatus(paymentIntent.getStatus());
                paymentDto.setStripeCustomerId(paymentIntent.getCustomer());
            } catch (StripeException e) {
                throw new CommonException(ResponseCodeEnum.STRIPE_EXCEPTION, e.getMessage(), e);
            }
            // stripe 信用卡 通过回调 invoice、增加通知
            // pay online 每次支付时会创建一个 payment intent，支付失败再次支付会重新创建一个 payment intent，pay online 同 reader 不更新 processing
            // 状态。 #ERP-1469
            // 针对3DS的卡, payment intent一直处于incomplete状态, 然后支付状态会一直是processing
            if (!req.isStripeTerminal() && !Boolean.TRUE.equals(paymentParams.getIsOnline())) { // reader支付 不更新状态
                toUpdateStatusRecord.setStatus(PaymentStatusEnum.PROCESSING);
            }
            // 调用第三方成功， 更新 order Payment 状态未 transaction created
            syncUpdateOrderPayment(
                    payment, OrderPaymentStatus.ORDER_PAYMENT_STATUS_TRANSACTION_CREATED, null, null, null);
        } else {
            // 非信用卡支付都是已支付状态
            toUpdateStatusRecord.setStatus(PaymentStatusEnum.PAID);
        }
        payment.setStatus(toUpdateStatusRecord.getStatus());
        try {
            // 更新状态到数据库  (如果发起了第三方支付，更新 vendor 相关信息)
            paymentMapper.updateByPrimaryKeySelective(toUpdateStatusRecord);
            // 如果是OB，新增OB定金记录
            if (Boolean.TRUE.equals(isFromOB)) {
                MoeBookOnlineDepositVO obDepositVO = new MoeBookOnlineDepositVO();
                obDepositVO.setBusinessId(payment.getBusinessId());
                obDepositVO.setAmount(payment.getAmount());
                obDepositVO.setStatus(BookOnlineDepositConst.PROCESSING);
                if (paymentParams.getBookingFeeAmount() != null) {
                    log.warn("booking fee should not be set now,  amount: {}", paymentParams.getBookingFeeAmount());
                    BigDecimal actualAmount = obDepositVO.getAmount().subtract(paymentParams.getBookingFeeAmount());
                    obDepositVO.setAmount(actualAmount);
                    obDepositVO.setBookingFee(paymentParams.getBookingFeeAmount());
                }
                if (paymentParams.getTipsAmount() != null) {
                    obDepositVO.setTipsAmount(paymentParams.getTipsAmount());
                }
                obDepositVO.setPaymentId(payment.getId());
                obDepositVO.setGuid(paymentParams.getGuid());
                Integer convenienceFeeInCents = req.getConvenienceFee();
                if (Boolean.TRUE.equals(req.getAddProcessingFee()) && convenienceFeeInCents != null) {
                    BigDecimal convenienceFee = BigDecimal.valueOf(convenienceFeeInCents)
                            .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                    obDepositVO.setConvenienceFee(convenienceFee);
                }
                iBookOnlineDepositClient.createOrUpdateOBDeposit(obDepositVO);
            } else { // 非OB定金保存到invoice deposit中
                if (BooleanEnum.VALUE_TRUE.equals(paymentParams.getIsDeposit())) {
                    DepositVo depositVo = new DepositVo();
                    BeanUtils.copyProperties(payment, depositVo);
                    // 未支付之前 deposit 仅保存期望支付的押金，tips 待拿到支付结果之后再行更新到 deposit record 中
                    if (!PaymentStatusEnum.PAID.equals(toUpdateStatusRecord.getStatus())) {
                        depositVo.setAmount(paymentParams.getAmountWithoutTips());
                    }
                    iDepositClient.createOrUpdateDeposit(depositVo);
                }
            }
        } catch (DataIntegrityViolationException ex) {
            if (ex.getMessage().contains("Duplicate")) {
                if (ex.getMessage().contains("UIX_payment_check_number")) {
                    throw new CommonException(
                            ResponseCodeEnum.INVALID_CHECK_NUMBER,
                            "'" + paymentParams.getCheckNumber() + "' is duplicated.");
                }
            }
            throw ex;
        }

        if (toUpdateStatusRecord.getStatus().equals(PaymentStatusEnum.PAID)) {
            // only cash payment flow run here
            syncUpdateOrderPaymentFromCash(primaryId);
            if (!isFromOB
                    && sendPaymentResultToModule(
                            paymentParams.getModule(),
                            paymentParams.getInvoiceId(),
                            paymentParams.getIsOnline(),
                            BooleanEnum.VALUE_TRUE.equals(paymentParams.getIsDeposit()))) {
                Payment toUpdateComplete = new Payment();
                toUpdateComplete.setStatus(PaymentStatusEnum.COMPLETED);
                toUpdateComplete.setUpdateTime(CommonUtil.get10Timestamp());
                toUpdateComplete.setId(primaryId);
                paymentMapper.updateByPrimaryKeySelective(toUpdateComplete);
            }
        }

        BeanUtils.copyProperties(payment, paymentDto);
        paymentDto.setStatus(payment.getStatusString());
        onPostPaymentCreate(payment, true);
        return paymentDto;
    }

    private void onPostPaymentCreate(Payment paymentDto, boolean paySuccess) {
        try {
            PaymentAntiFraudCollectDTO build = PaymentAntiFraudCollectDTO.builder()
                    .ip(RequestUtils.getIP())
                    .paymentId(paymentDto.getId())
                    .paySuccess(paySuccess)
                    .build();
            ActiveMQTextMessage message = new ActiveMQTextMessage();
            message.setStringProperty(
                    ActiveMQConstant.PAYMENT_DATA_COLLECT_TAG, ActiveMQConstant.PAYMENT_DATA_COLLECT_TAG_CREATE);
            message.setText(JsonUtil.toJson(build));
            moeMessageSender.send(paymentDataCollectTopic, message);
        } catch (Exception e) {
            log.error("send payment data collect message error", e);
        }
    }

    public Payment getPaymentByPaymentIntentId(String paymentIntentId) {
        return paymentMapper.selectByStripeIntentId(paymentIntentId);
    }

    private Payment getPaymentByPaymentIntentIdWithCreate(
            Integer businessId, CreatePaymentParams paymentParams, boolean withRawData) {
        // 同步创建 order payment，并记录 order payment id 到 payment
        if (ObjectUtils.isEmpty(paymentParams.getPaymentIntentId())) {
            return createPaymentRecord(businessId, paymentParams, withRawData);
        }

        // payment intent id 不为空，说明是 pay online
        Payment payment = paymentMapper.selectByStripeIntentId(paymentParams.getPaymentIntentId());
        if (payment == null) {
            payment = createPaymentRecord(businessId, paymentParams, withRawData);
        }

        // 如果 payment 处于 create or failed 状态，可以使用当前 payment intent 进行支付。 #ERP-1584
        // 如果 payment 处于 paid or complete 状态，说明当前 payment intent 已经支付过，并且成功了，不能再次支付
        // pay online 时，不存在 processing 状态
        if (PaymentStatusEnum.PAID.equals(payment.getStatus())
                || PaymentStatusEnum.COMPLETED.equals(payment.getStatus())) {
            throw new CommonException(ResponseCodeEnum.PAYMENT_STATUS_EXCEPTION);
        }

        return payment;
    }

    private Payment createPaymentRecord(Integer businessId, CreatePaymentParams paymentParams, boolean withRawData) {
        Payment payment = PaymentStructMapper.INSTANCE.toEntity(paymentParams);
        payment.setBusinessId(businessId);
        payment.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
        // payment 记录初始化状态为 created，根据情况更新为 processing/paid，或者不更新
        payment.setStatus(PaymentStatusEnum.CREATED);
        if (StringUtils.hasText(paymentParams.getPaymentIntentId())) {
            payment.setStripeIntentId(paymentParams.getPaymentIntentId());
            payment.setTips(paymentParams.getTipsAmount());
        }
        if (isNormal(paymentParams.getStripePaymentMethod())) {
            payment.setVendor(PaymentMethodEnum.METHOD_NAME_STRIPE);
        } else {
            payment.setVendor("");
        }
        payment.assertPaymentData();
        CommonUtil.conditionalReset(
                withRawData, payment::setCreateTime, paymentParams::getRawCreateTime, CommonUtil::get10Timestamp);
        CommonUtil.conditionalReset(
                withRawData, payment::setUpdateTime, paymentParams::getRawUpdateTime, CommonUtil::get10Timestamp);
        // 创建 order payment，并记录其主键
        syncCreateOrderPayment(payment, paymentParams.getTipsAmount());
        paymentMapper.insertSelective(payment);
        return payment;
    }

    public Integer createPaymentRecord(PaymentRecordParam params) {
        Payment toAddRecord = PaymentStructMapper.INSTANCE.toEntity(params);
        PaymentIntent paymentIntent;
        try {
            PaymentIntentRetrieveParams retrieveParams = PaymentIntentRetrieveParams.builder()
                    .addExpand(StripeApi.PAYMENT_METHOD)
                    .addExpand(StripeApi.EXPAND_LATEST_CHARGE)
                    .build();
            paymentIntent = PaymentIntent.retrieve(
                    params.getStripeIntentId(),
                    retrieveParams,
                    RequestOptions.builder().build());

            // set processing fee
            toAddRecord.setProcessingFee(PaymentUtil.getAmountFromUnit(paymentIntent.getApplicationFeeAmount()));
            // default stripe payment method type
            toAddRecord.setStripePaymentMethod(
                    StripePaymentMethodEnum.COF.getCode().byteValue());
            toAddRecord.setVendor(PaymentMethodEnum.METHOD_NAME_STRIPE);
            toAddRecord.setCreateTime(DateUtil.get10Timestamp());
            toAddRecord.setUpdateTime(DateUtil.get10Timestamp());
            // get card info from charges
            Charge charge = paymentIntent.getLatestChargeObject();
            PaymentMethod paymentMethod = paymentIntent.getPaymentMethodObject();

            if (charge != null) {
                if (charge.getPaymentMethodDetails().getCard() != null) {
                    populateCardInfo(
                            toAddRecord, charge.getPaymentMethodDetails().getCard());
                } else if (charge.getPaymentMethodDetails().getUsBankAccount() != null) {
                    toAddRecord.setStripePaymentMethod(
                            StripePaymentMethodEnum.ACH.getCode().byteValue());
                    toAddRecord.setCardNumber(
                            charge.getPaymentMethodDetails().getUsBankAccount().getLast4());
                }
            } else if (paymentMethod != null) {
                if (paymentMethod.getCard() != null) {
                    populateCardInfo(toAddRecord, paymentMethod.getCard());
                } else if (paymentMethod.getUsBankAccount() != null) {
                    toAddRecord.setStripePaymentMethod(
                            StripePaymentMethodEnum.ACH.getCode().byteValue());
                    toAddRecord.setCardNumber(paymentMethod.getUsBankAccount().getLast4());
                }
            }
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, e, "Invalid payment intent id: " + params.getStripeIntentId());
        }
        toAddRecord.assertPaymentData();

        transactionOperations.executeWithoutResult(status -> {
            Payment existingPayment = paymentMapper.selectByStripeIntentIdForUpdate(params.getStripeIntentId());
            if (existingPayment != null) {
                // 根据 payment intent id 查询 payment 记录，如果存在则更新，不存在则创建
                toAddRecord.setId(existingPayment.getId());
                paymentMapper.updateByPrimaryKeySelective(toAddRecord);
            } else {
                // 创建 order payment 并记录其主键
                syncCreateOrderPayment(toAddRecord, toAddRecord.getTips());
                paymentMapper.insertSelective(toAddRecord);
            }
            // 成功的才需要写入
            if (Objects.equals(PaymentStatusEnum.COMPLETED, toAddRecord.getStatus())) {
                BigDecimal cvf = BigDecimal.ZERO;
                if (MapUtils.isNotEmpty(paymentIntent.getMetadata())) {
                    cvf = paymentIntent.getMetadata().containsKey(PaymentStripeStatus.CONVENIENCE_FEE_KEY)
                            ? new BigDecimal(paymentIntent.getMetadata().get(PaymentStripeStatus.CONVENIENCE_FEE_KEY))
                            : BigDecimal.ZERO;
                }

                MoePayDetail moePayDetail = payDetailService.selectByIntentIdForUpdate(toAddRecord.getStripeIntentId());
                if (moePayDetail != null) {
                    payDetailService.deleteById(moePayDetail.getId());
                }

                // 同时写入 pay detail
                payDetailService.generateDetail(PaymentRecord.builder()
                        .id(toAddRecord.getId())
                        .businessId(toAddRecord.getBusinessId())
                        .companyId(toAddRecord.getCompanyId())
                        .orderId(toAddRecord.getInvoiceId())
                        .amount(toAddRecord.getAmount())
                        .bookingFee(BigDecimal.ZERO) // 当前已经没有booking fee 了
                        .stripeIntentId(toAddRecord.getStripeIntentId())
                        .tips(toAddRecord.getTips())
                        .convenienceFee(cvf)
                        .isDeposit(BooleanEnum.VALUE_TRUE.equals(toAddRecord.getIsDeposit()))
                        .build());
            }
        });
        return toAddRecord.getId();
    }

    private static void populateCardInfo(Payment toAddRecord, PaymentMethod.Card paymentMethod) {
        toAddRecord.setCardFunding(paymentMethod.getFunding());
        toAddRecord.setCardType(paymentMethod.getBrand());
        toAddRecord.setCardNumber(paymentMethod.getLast4());
        toAddRecord.setExpMonth(String.valueOf(paymentMethod.getExpMonth()));
        toAddRecord.setExpYear(String.valueOf(paymentMethod.getExpYear()));
    }

    private static void populateCardInfo(Payment toAddRecord, Charge.PaymentMethodDetails.Card paymentMethod) {
        toAddRecord.setCardFunding(paymentMethod.getFunding());
        toAddRecord.setCardType(paymentMethod.getBrand());
        toAddRecord.setCardNumber(paymentMethod.getLast4());
        toAddRecord.setExpMonth(String.valueOf(paymentMethod.getExpMonth()));
        toAddRecord.setExpYear(String.valueOf(paymentMethod.getExpYear()));
    }

    public void syncCreateOrderPayment(Payment toAddRecord, BigDecimal tipsBeforeCreate) {
        // 记录 order payment:
        // check order version  and record
        OrderModel order = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(toAddRecord.getInvoiceId()).build());
        // 如果是 ob prepay， 支付时没有 order id,在创建 order 后 再创建order payment
        if (!isNormal(order.getId()) || order.getOrderVersion() < OrderConstant.ORDER_VERSION_REFUND) {
            return;
        }
        PaymentMethodExtra.Legacy.Builder extraInfoBuilder = PaymentMethodExtra.Legacy.newBuilder();
        Optional.ofNullable(toAddRecord.getStripePaymentMethod()).ifPresent(extraInfoBuilder::setStripePaymentMethod);
        Optional.ofNullable(toAddRecord.getCheckNumber()).ifPresent(extraInfoBuilder::setCheckNumber);
        Optional.ofNullable(toAddRecord.getSignature()).ifPresent(extraInfoBuilder::setSignature);
        PayOrderRequest.Builder orderPaymentBuilder = PayOrderRequest.newBuilder()
                .setOrderId(toAddRecord.getInvoiceId())
                .setCompanyId(toAddRecord.getCompanyId())
                .setBusinessId(toAddRecord.getBusinessId())
                .setCustomerId(toAddRecord.getCustomerId())
                .setPaymentMethodId(toAddRecord.getMethodId())
                .setPaymentMethodVendor(toAddRecord.getVendor())
                .setIsOnline(Boolean.TRUE.equals(toAddRecord.getIsOnline()))
                .setIsDeposit(BooleanEnum.VALUE_TRUE.equals(toAddRecord.getIsDeposit()))
                .setPaidBy(Optional.ofNullable(toAddRecord.getPaidBy()).orElse(""))
                .setPaymentMethodExtra(PaymentMethodExtra.newBuilder()
                        .setLegacy(extraInfoBuilder.build())
                        .build())
                .setPaymentStatus(
                        StringUtils.hasText(toAddRecord.getStripeIntentId())
                                        || !PaymentMethodEnum.METHOD_NAME_CREDIT_CARD.equalsIgnoreCase(
                                                toAddRecord.getMethod())
                                ? OrderPaymentStatus.ORDER_PAYMENT_STATUS_TRANSACTION_CREATED
                                : OrderPaymentStatus.ORDER_PAYMENT_STATUS_CREATED)
                // 发起支付前：totalAmount = amount + BeforeTips，如果回调中还有 tips，需要更新 totalAmount
                .setTotalAmount(MoneyUtils.toGoogleMoney(toAddRecord.getAmount()))
                // amount 不包含 本次支付带入的tips, 如有 tips会再次更新该字段
                .setAmount(MoneyUtils.toGoogleMoney(toAddRecord.getAmount()));
        Optional.ofNullable(toAddRecord.getId()).ifPresent(orderPaymentBuilder::setPaymentId);
        Optional.ofNullable(toAddRecord.getMethod()).ifPresent(orderPaymentBuilder::setPaymentMethod);
        Optional.ofNullable(toAddRecord.getStaffId()).ifPresent(orderPaymentBuilder::setStaffId);
        Optional.ofNullable(tipsBeforeCreate).ifPresent(tips -> orderPaymentBuilder
                .setPaymentTipsBeforeCreate(MoneyUtils.toGoogleMoney(tips))
                .setAmount(MoneyUtils.toGoogleMoney(toAddRecord.getAmount().subtract(tips))));
        PayOrderResponse orderPaymentResponse = orderClient.payOrder(orderPaymentBuilder.build());
        log.info("Create order payment response: {}, request is {}", orderPaymentResponse, orderPaymentBuilder);
        // save order payment id
        toAddRecord.setOrderPaymentId(orderPaymentResponse.getOrderPayment().getId());
    }

    public void syncUpdateOrderPaymentFromCash(Integer paymentId) {
        Payment existingPayment = paymentMapper.selectByPrimaryKey(paymentId);

        syncUpdateOrderPayment(
                existingPayment,
                com.moego.server.payment.service.util.PaymentUtil.convertToOrderPaymentStatus(
                        existingPayment.getStatus()),
                BigDecimal.ZERO,
                null,
                null);
    }

    public void syncUpdateOrderPaymentFromWebhook(StripePaymentUpdateParam updateParam) {
        Payment existingPayment = paymentMapper.selectByPrimaryKey(updateParam.getMoegoPaymentId());
        syncUpdateOrderPayment(
                existingPayment,
                updateParam.getOrderPaymentStatus(),
                updateParam.getConvenienceFee(),
                updateParam.getTerminalTips(),
                // 支付回调，最终结果，把准确的tips before create 更新到order
                updateParam.getTipsBeforeCreate());
    }

    /**
     * 根据 order payment id 更新 order payment
     * 如果 order payment id 不存在，不做任何操作
     *
     * @param toUpdateRecord
     */
    public void syncUpdateOrderPayment(
            Payment toUpdateRecord,
            OrderPaymentStatus orderPaymentStatus,
            BigDecimal convenienceFee,
            BigDecimal terminalTips,
            BigDecimal tipsBeforeCreate) {
        if (!isNormal(toUpdateRecord.getOrderPaymentId())) {
            // nothing to do for no order payment id case
            return;
        }
        // 全量更新 payment extra 信息
        PaymentMethodExtra.Legacy.Builder extraInfoBuilder = buildPaymentMethodExtra(toUpdateRecord);

        // 更新 order payment 状态
        UpdateOrderPaymentRequest.Builder orderPaymentBuilder = UpdateOrderPaymentRequest.newBuilder()
                .setOrderPaymentId(toUpdateRecord.getOrderPaymentId())
                .setPaymentStatus(orderPaymentStatus)
                .setPaymentMethodVendor(toUpdateRecord.getVendor())
                .setPaymentId(toUpdateRecord.getId())
                .setPaymentMethodExtra(PaymentMethodExtra.newBuilder()
                        .setLegacy(extraInfoBuilder.build())
                        .build())
                // 发起支付前：totalAmount = amount，如果回调中还有 tips，需要更新 totalAmount
                .setTotalAmount(MoneyUtils.toGoogleMoney(toUpdateRecord.getAmount()));
        // 这里只有回调过来更新的时候没有设置为null
        Optional.ofNullable(tipsBeforeCreate)
                .ifPresent(tips -> orderPaymentBuilder.setPaymentTipsBeforeCreate(MoneyUtils.toGoogleMoney(tips)));
        Optional.ofNullable(convenienceFee)
                .ifPresent(fee -> orderPaymentBuilder.setConvenienceFee(MoneyUtils.toGoogleMoney(fee)));
        // 如果有 terminal tips， 则更新 paymentTipsAfterCreate
        Optional.ofNullable(terminalTips)
                .ifPresent(tips -> orderPaymentBuilder.setPaymentTipsAfterCreate(MoneyUtils.toGoogleMoney(tips)));
        if (isNormal(toUpdateRecord.getProcessingFee())) {
            orderPaymentBuilder.setProcessingFee(MoneyUtils.toGoogleMoney(toUpdateRecord.getProcessingFee()));
        }
        if (isNormal(toUpdateRecord.getInvoiceId())) {
            orderPaymentBuilder.setOrderId(toUpdateRecord.getInvoiceId());
        }
        if (isNormal(toUpdateRecord.getCustomerId())) {
            orderPaymentBuilder.setCustomerId(toUpdateRecord.getCustomerId());
        }

        // update payment status reason
        if (StringUtils.hasText(toUpdateRecord.getCancelReason())) {
            orderPaymentBuilder.setPaymentStatusReason(toUpdateRecord.getCancelReason());
        }
        UpdateOrderPaymentResponse orderPaymentResponse = orderClient.updateOrderPayment(orderPaymentBuilder.build());
        log.info("Update order payment response: {}, request is {}", orderPaymentResponse, orderPaymentBuilder);
    }

    @NotNull // square 相关字段暂不涉及
    private static PaymentMethodExtra.Legacy.Builder buildPaymentMethodExtra(Payment toUpdateRecord) {
        PaymentMethodExtra.Legacy.Builder extraInfoBuilder = PaymentMethodExtra.Legacy.newBuilder();
        Optional.ofNullable(toUpdateRecord.getExpYear()).ifPresent(expYearStr -> {
            try {
                extraInfoBuilder.setExpYear(Integer.parseInt(expYearStr));
            } catch (NumberFormatException e) {
                log.error("Fail to parse exp year: {}", expYearStr, e);
            }
        });
        Optional.ofNullable(toUpdateRecord.getExpMonth()).ifPresent(expMonthStr -> {
            try {
                extraInfoBuilder.setExpMonth(Integer.parseInt(expMonthStr));
            } catch (NumberFormatException e) {
                log.error("Fail to parse exp month: {}", expMonthStr, e);
            }
        });
        Optional.ofNullable(toUpdateRecord.getCardType()).ifPresent(extraInfoBuilder::setCardType);
        Optional.ofNullable(toUpdateRecord.getCardFunding()).ifPresent(extraInfoBuilder::setCardFunding);
        Optional.ofNullable(toUpdateRecord.getCardNumber()).ifPresent(extraInfoBuilder::setCardNumber);
        Optional.ofNullable(toUpdateRecord.getStripePaymentMethod())
                .ifPresent(extraInfoBuilder::setStripePaymentMethod);
        Optional.ofNullable(toUpdateRecord.getCheckNumber()).ifPresent(extraInfoBuilder::setCheckNumber);
        Optional.ofNullable(toUpdateRecord.getSignature()).ifPresent(extraInfoBuilder::setSignature);
        Optional.ofNullable(toUpdateRecord.getStripeClientSecret()).ifPresent(extraInfoBuilder::setStripeClientSecret);
        Optional.ofNullable(toUpdateRecord.getStripeIntentId()).ifPresent(extraInfoBuilder::setStripeIntentId);
        return extraInfoBuilder;
    }

    public void setStripePaymentResult(StripePaymentUpdateParam updateParam) {
        Integer moegoPaymentId = updateParam.getMoegoPaymentId();
        Payment payment = paymentMapper.selectByPrimaryKey(moegoPaymentId);
        if (payment == null) {
            throw new CommonException(ResponseCodeEnum.STRIPE_INTENT_NOT_FOUND, "primary id: " + moegoPaymentId);
        }
        OrderModel orderModel = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(payment.getInvoiceId()).build());
        updateParam.setOrderVersion(orderModel.getOrderVersion());

        //  card pay 需要去重或移除操作
        if (PaymentStripeStatus.CARD_PAY.equals(payment.getStripePaymentMethod())) {
            processingStripePaymentMethod(payment, null);
        } else if (PaymentStripeStatus.isStripeTerminal(
                payment.getStripePaymentMethod())) { // terminal（smart/bluetooth reader）
            processingStripePaymentMethod(payment, true); // 去重成功后，把更新结果刷新到paymentIntent的MetaData中
        }

        Payment toUpdate = new Payment();
        toUpdate.setId(payment.getId());
        if (StringUtils.hasText(updateParam.getCardFunding())) {
            // update card info
            toUpdate.setCardFunding(updateParam.getCardFunding());
            toUpdate.setCardType(updateParam.getCardType());
            toUpdate.setCardNumber(updateParam.getCardNumber());
            toUpdate.setExpYear(updateParam.getExpYear());
            toUpdate.setExpMonth(updateParam.getExpMonth());
        }
        if (updateParam.getSuccess()) {
            updateParam.setOrderPaymentStatus(OrderPaymentStatus.ORDER_PAYMENT_STATUS_PAID);
            BigDecimal bookingFee = updateParam.getBookingFee();
            BigDecimal updatePaidAmount = updateParam.getTotalPaidAmount().subtract(bookingFee);
            payment.setAmount(updatePaidAmount); // ERP-3681更新后 方便pay online通知新金额
            // 支付成功之后更新更新实际支付的押金金额
            if (PaymentStripeStatus.SOURCE_FROM_OB.equals(updateParam.getSource())) {
                MoeBookOnlineDepositVO obDepositVO = new MoeBookOnlineDepositVO();
                obDepositVO.setBusinessId(payment.getBusinessId());
                obDepositVO.setPaymentId(payment.getId());
                obDepositVO.setStatus(BookOnlineDepositConst.PAID);
                obDepositVO.setAmount(updatePaidAmount);
                obDepositVO.setTipsAmount(updateParam.getTipsAmount());
                obDepositVO.setBookingFee(bookingFee);
                iBookOnlineDepositClient.updateOBDepositByPaymentId(obDepositVO);

                // 更新 booking request payment_status 为 SUCCESS
                bookingRequestHelper.updateBookingRequestPaymentStatusByPaymentId(
                        payment.getId(), BookingRequestModel.PaymentStatus.SUCCESS);
            }
            // prepay capture之后再创建deposit记录
            if (BooleanEnum.VALUE_TRUE.equals(payment.getIsDeposit())) {
                DepositVo depositVo = new DepositVo();
                depositVo.setStaffId(payment.getStaffId());
                depositVo.setBusinessId(payment.getBusinessId());
                depositVo.setInvoiceId(payment.getInvoiceId());
                depositVo.setDescription(payment.getDescription());
                // 用于B端Take payment时, 展示deposit paid金额
                depositVo.setAmount(updatePaidAmount);
                iDepositClient.createOrUpdateDeposit(depositVo);
            }

            // booking fee是放在application fee里，存放进Payment表的processing fee时需要减去booking fee
            BigDecimal processingFeeForBiz =
                    stripeService.getProcessingFeeForBiz(updateParam.getStripeFee(), updateParam.getBookingFee());
            toUpdate.setProcessingFee(processingFeeForBiz);
            // reader支付时，可能添加了tips，需要更新本笔支付总额
            toUpdate.setAmount(updatePaidAmount);
            toUpdate.setStatus(PaymentStatusEnum.PAID);
            toUpdate.setUpdateTime(DateUtil.get10Timestamp());
            if (orderModel.getOrderVersion() < OrderConstant.ORDER_VERSION_REFUND
                    && TipsUtil.shouldSyncTips(payment.getModule(), updateParam.getTipsAmount())) {
                // 更新 order tips 需要合并到更新 order payment流程中
                lockAndUpdateTips(updateParam, toUpdate);
            } else {
                paymentMapper.updateByPrimaryKeySelective(toUpdate);
            }
            // payment result更新到invoice
            // order version >= 2 时，更新 order payment(调用新接口，使用增量方式更新 tips 和 paid amount)
            // 只在order version <=1 时调用 updateOrderIncr 接口
            UpdatePaymentResultParams paymentResultParams = new UpdatePaymentResultParams()
                    .setModule(payment.getModule())
                    .setInvoiceId(payment.getInvoiceId())
                    .setIsOnline(payment.getIsOnline())
                    .setIsFromPreAuth(updateParam.getIsPreAuth())
                    .setIsDeposit(BooleanEnum.VALUE_TRUE.equals(payment.getIsDeposit()))
                    .setConvenienceFee(updateParam.getConvenienceFee())
                    .setIsFromOB(PaymentStripeStatus.SOURCE_FROM_OB.equals(updateParam.getSource()));
            // cancel appointment after cancellation fee paid
            if (Boolean.TRUE.equals(updateParam.getIsCancellationFee())) {
                var cancelAppointmentRequest = CancelAppointmentRequest.newBuilder()
                        .setAppointmentId(orderModel.getSourceId())
                        .setCancelByType(AppointmentUpdatedBy.BY_PET_PARENT_APP)
                        .setCancelBy(orderModel.getCustomerId())
                        .setCancelReason(GroomingAppointmentEnum.PPA_CANCEL_REASON)
                        .setNoShow(AppointmentNoShowStatus.NO_SHOW)
                        .build();
                appointmentService.cancelAppointment(cancelAppointmentRequest);
            }
            // 更新 order payment
            if (orderModel.getOrderVersion() >= OrderConstant.ORDER_VERSION_REFUND) {
                syncUpdateOrderPaymentFromWebhook(updateParam);
            }

            if (sendPaymentResultToModule(paymentResultParams)) {
                toUpdate.setStatus(PaymentStatusEnum.COMPLETED);
                notifyAfterPayOnlineSuccess(payment);
            }
        } else {
            // set order payment status
            if (PaymentStripeStatus.PAYMENT_CANCEL.equals(updateParam.getEventType())) {
                updateParam.setOrderPaymentStatus(OrderPaymentStatus.ORDER_PAYMENT_STATUS_CANCELED);
            } else {
                updateParam.setOrderPaymentStatus(OrderPaymentStatus.ORDER_PAYMENT_STATUS_FAILED);
            }
            // webhook失败回调时，更新ob deposit状态为Failed
            if (PaymentStripeStatus.SOURCE_FROM_OB.equals(updateParam.getSource())) {
                MoeBookOnlineDepositVO obDepositVO = new MoeBookOnlineDepositVO();
                obDepositVO.setStatus(
                        PaymentStripeStatus.PAYMENT_CANCEL.equals(updateParam.getEventType())
                                ? BookOnlineDepositConst.CANCEL
                                : BookOnlineDepositConst.FAILED);
                obDepositVO.setPaymentId(moegoPaymentId);
                obDepositVO.setBusinessId(payment.getBusinessId());
                iBookOnlineDepositClient.updateOBDepositByPaymentId(obDepositVO);

                // 更新 booking request payment_status 为 FAILED
                bookingRequestHelper.updateBookingRequestPaymentStatusByPaymentId(
                        payment.getId(), BookingRequestModel.PaymentStatus.FAILED);
            }
            toUpdate.setStatus(PaymentStatusEnum.FAILED);
            toUpdate.setStripeIntentId(updateParam.getPaymentIntentId());
            toUpdate.setCancelReason(updateParam.getFailedCode());
        }
        toUpdate.setUpdateTime(DateUtil.get10Timestamp());
        paymentMapper.updateByPrimaryKeySelective(toUpdate);

        // 更新 order payment for failed or cancel event only
        if (PaymentStatusEnum.FAILED.equals(toUpdate.getStatus())
                && orderModel.getOrderVersion() >= OrderConstant.ORDER_VERSION_REFUND) {
            syncUpdateOrderPaymentFromWebhook(updateParam);
        }

        // 支付完成后，异步记录支付详情，根据当前invoice/order的subtotal、tax比例记录到对应的moe_pay_detail中
        if (updateParam.getSuccess()) {
            ThreadPool.submit(() -> {
                payDetailService.generateDetail(PaymentRecord.builder()
                        .id(updateParam.getMoegoPaymentId())
                        .businessId(payment.getBusinessId())
                        .companyId(payment.getCompanyId())
                        .orderId(payment.getInvoiceId())
                        .amount(toUpdate.getAmount())
                        .bookingFee(updateParam.getBookingFee())
                        .stripeIntentId(payment.getStripeIntentId())
                        .tips(updateParam.getTipsAmount())
                        .convenienceFee(updateParam.getConvenienceFee())
                        .isDeposit(BooleanEnum.VALUE_TRUE.equals(payment.getIsDeposit()))
                        .build());
                // 支付成功检查是否有需要释放的preauth
                preAuthService.releasePreAuth(payment.getBusinessId(), payment.getInvoiceId());

                // payment intent success, invoke split payment, 如果这里失败了有定时任务兜底
                ThreadPool.execute(() ->
                        splitPaymentService.split(Vendor.STRIPE, payment.getId(), SplitSyncRecordTypeEnum.PAYMENT));
            });
        }
        afterPaymentFinish(toUpdate.getId());
    }

    public void afterPaymentFinish(Integer paymentId) {
        if (paymentId == null) {
            return;
        }
        try {
            ActiveMQTextMessage message = new ActiveMQTextMessage();
            message.setText(String.valueOf(paymentId));
            moeMessageSender.send(paymentDataCollectTopic, message);
        } catch (Exception e) {
            log.error("drop payment collect data error:{}", paymentId, e);
        }
    }

    private void lockAndUpdateTips(StripePaymentUpdateParam updateParam, Payment toUpdate) {
        String resourceCacheKey = lockManager.getResourceKey(LockManager.PAYMENT_WEBHOOK_SET_TIPS, toUpdate.getId());
        String randomValue = CommonUtil.getUuid();
        try {
            if (lockManager.lockWithRetry(resourceCacheKey, randomValue)) {
                Payment payment = paymentMapper.selectByPrimaryKey(toUpdate.getId());
                if (!Objects.equals(payment.getStatus(), PaymentStatusEnum.PAID)
                        && !Objects.equals(payment.getStatus(), PaymentStatusEnum.COMPLETED)) {
                    // paid 或者 complete说明已经set
                    SetTipsRequest tipsParam = SetTipsRequest.newBuilder()
                            .setBusinessId(payment.getBusinessId())
                            .setInvoiceId(payment.getInvoiceId())
                            .setOmitResult(true)
                            .setValue(updateParam.getTipsAmount().doubleValue())
                            .setValueType(InvoiceValueType.AMOUNT.value())
                            .build();
                    orderClient.setTips(tipsParam);
                }
                paymentMapper.updateByPrimaryKeySelective(toUpdate);
            }
        } finally {
            lockManager.unlock(resourceCacheKey, randomValue);
        }
    }

    /**
     * 处理支付方式
     * 1. 如果需要 save card，进行去重
     * 2. 如果不需要保存，移除本次支付用到的支付方式
     *
     * @param payment
     * @param saveCard
     * @return 是否去重成功
     */
    public Boolean processingStripePaymentMethod(Payment payment, Boolean saveCard) {
        if (payment == null || !StringUtils.hasText(payment.getStripeIntentId())) {
            return false;
        }
        Boolean deleteDuplicatedCard = false;
        try {
            PaymentIntent paymentIntent = PaymentIntent.retrieve(payment.getStripeIntentId());
            if (Objects.isNull(paymentIntent)) {
                return false;
            }

            // 如果 saveCard 为空，从 payment intent 的 metadata 中解析出 saveCard
            if (saveCard == null) {
                Map<String, String> metadata = paymentIntent.getMetadata();
                String saveCardString = metadata.get(PaymentStripeStatus.SAVE_CARD);
                saveCard = Boolean.parseBoolean(saveCardString);
            }

            // 如果需要保存支付方式, 则进行去重
            MmStripeCustomer mmStripeCustomer = stripeCustomerMapper.selectByCustomerId(payment.getCustomerId());
            if (mmStripeCustomer == null) {
                return false;
            }
            if (saveCard) {
                log.info(
                        "processing stripe payment method[deduplicate]: stripe payment method {}, payment intent id {}",
                        payment.getStripePaymentMethod(),
                        paymentIntent.getId());
                deleteDuplicatedCard = stripeService.deduplicatePaymentsMethod(mmStripeCustomer.getStripeCustomerId());
                boolean isTerminal = PaymentStripeStatus.isStripeTerminal(payment.getStripePaymentMethod());
                if (deleteDuplicatedCard && isTerminal) {
                    PaymentIntentUpdateParams paymentIntentUpdateParams = PaymentIntentUpdateParams.builder()
                            .putMetadata(PaymentStripeStatus.SAVE_CARD, Boolean.FALSE.toString())
                            .build();
                    paymentIntent.update(paymentIntentUpdateParams);
                }
            } else {
                // 解析出 payment method，根据 payment method id 进行移除
                String paymentMethodId = stripeService.parsePaymentMethod(paymentIntent);
                if (!StringUtils.hasText(paymentMethodId)) {
                    log.warn("payment method id is empty, delete payment method failed.");
                    return deleteDuplicatedCard;
                }
                log.info(
                        "processing stripe payment method[delete]: stripe payment method {}, payment method id {}",
                        payment.getStripePaymentMethod(),
                        paymentMethodId);
                // 不需要保存卡，移除本次支付用到的支付方式
                MmStripeAccount stripeAccount = stripeAccountMapper.selectByBusinessId(payment.getBusinessId());
                DeleteCardForCustomerRequest deleteCardForCustomerRequest = new DeleteCardForCustomerRequest();
                deleteCardForCustomerRequest.setCustomerId(payment.getCustomerId());
                deleteCardForCustomerRequest.setCardId(paymentMethodId);
                deleteCardForCustomerRequest.setStripeCustomerId(mmStripeCustomer.getStripeCustomerId());
                // 新增的card都是platform下的
                final boolean isStripeCustomerInPlatform = mmStripeCustomer
                        .getObject()
                        .equalsIgnoreCase(PaymentStripeStatus.STRIPE_CUSTOMER_TYPE_PLATFORM);
                if (!isStripeCustomerInPlatform) {
                    deleteCardForCustomerRequest.setStripeAccountId(stripeAccount.getStripeAccountId());
                }
                stripeService.deleteCardForCustomer(deleteCardForCustomerRequest);
            }
        } catch (Exception ex) {
            log.error("a exception occurred while received webhook event processing card info.", ex);
        }
        return deleteDuplicatedCard;
    }

    private void notifyAfterPayOnlineSuccess(Payment payment) {
        // pay online invoice
        if (payment.getIsOnline()) {
            ThreadPool.execute(() -> {
                OrderModel order = orderClient.getOrder(GetOrderRequest.newBuilder()
                        .setBusinessId(payment.getBusinessId())
                        .setId(payment.getInvoiceId())
                        .build());
                if (order == null) {
                    log.error(
                            "order is null, businessId: {}, orderId: {}",
                            payment.getBusinessId(),
                            payment.getInvoiceId());
                    return;
                }
                List<Integer> staffIds =
                        groomingAppointmentService.getAppointmentRelatedStaffIds((int) order.getSourceId());
                // 调用通知发送
                NotificationInvoicePaidParams invoicePaidParams = new NotificationInvoicePaidParams();
                invoicePaidParams.setBusinessId(payment.getBusinessId());
                invoicePaidParams.setTokenStaffId(payment.getStaffId());
                invoicePaidParams.setStaffIdList(new HashSet<>(staffIds)); // 只通知 appointment 相关的 staff
                // 给前端的数据体
                NotificationExtraInvoicePaidDto invoicePaidDto = new NotificationExtraInvoicePaidDto();
                invoicePaidDto.setCustomerId(payment.getCustomerId());
                invoicePaidDto.setInvoiceId(payment.getInvoiceId());
                invoicePaidDto.setAmount(payment.getAmount());
                invoicePaidDto.setInvoiceType(payment.getModule());
                // customer firstName 和 lastName 在notification模块组装
                invoicePaidParams.setWebPushDto(invoicePaidDto);
                iNotificationClient.sendNotificationInvoicePaid(invoicePaidParams);
            });
            // 判断是否是 grooming module, 才需要发送invoice confirm email, total paid的判断在以下方法内
            if (MODULE_GROOMING.equals(payment.getModule())) {
                ThreadPool.execute(() -> iGroomingEmailClient.sendInvoiceConfirmEmail(payment.getInvoiceId()));
            }
        }
    }

    /**
     * 根据payment结果更新invoice信息
     * 2024-11: 后续更新 order 走 order payment 更新流程
     *
     * @param module
     * @param invoiceId
     * @param isOnline
     * @param isDeposit
     * @return
     */
    @Deprecated
    private Boolean sendPaymentResultToModule(String module, Integer invoiceId, Boolean isOnline, Boolean isDeposit) {
        UpdatePaymentResultParams paymentResultParams = new UpdatePaymentResultParams()
                .setModule(module)
                .setInvoiceId(invoiceId)
                .setIsOnline(isOnline)
                .setIsDeposit(isDeposit)
                .setConvenienceFee(null)
                .setIsFromOB(false);
        return sendPaymentResultToModule(paymentResultParams);
    }

    /**
     * 根据payment结果更新invoice信息
     * <p>
     * convenienceFee 支付产生的processingFee，如需要保存为invoice的convenienceFee 则传这个参数，不需要则传null或传0
     *
     * @return
     */
    private Boolean sendPaymentResultToModule(UpdatePaymentResultParams paymentResultParams) {
        String module = paymentResultParams.getModule();
        Integer invoiceId = paymentResultParams.getInvoiceId();
        Boolean isDeposit = paymentResultParams.getIsDeposit();

        PaymentSummary paymentSummary = getPayments(module, invoiceId, true);
        if (MODULE_GROOMING.equals(module)) {
            com.moego.server.grooming.params.SetPaymentParams params =
                    new com.moego.server.grooming.params.SetPaymentParams();
            BeanUtils.copyProperties(paymentSummary, params);
            params.setIsOnline(paymentResultParams.getIsOnline());
            params.setIsPreAuth(paymentResultParams.getIsFromPreAuth());
            params.setConvenienceFee(paymentResultParams.getConvenienceFee());
            if (paymentSummary.getRefunds() != null) {
                params.setRefunds(paymentSummary.getRefunds().stream()
                        .map(r -> {
                            SetPaymentParams.RefundDTO dto = new SetPaymentParams.RefundDTO();
                            BeanUtils.copyProperties(r, dto);
                            return dto;
                        })
                        .toList());
            }
            // 检查是否有定金支付成功
            params.setIsDeposit(isDeposit);
            if (Boolean.TRUE.equals(isDeposit)) {
                ThreadPool.execute(() -> {
                    log.info("update deposit status for invoice {}", invoiceId);
                    iDepositClient.updateDepositPaid(invoiceId);
                });
            }
            // 增加qb同步 信息
            ThreadPool.execute(() -> quickBooksClient.invoiceCheckQbSyncStatus(null, invoiceId));
            // OB prepay来源
            params.setIsFromOB(paymentResultParams.getIsFromOB());
            // 被调用方根据orderVersion自行判断是否更新 order (其他表的更新逻辑不变）
            return groomingInvoiceClient.setPaymentResult(params);
        } else if (MODULE_RETAIL.equals(module)) {
            com.moego.server.retail.param.SetPaymentParams params =
                    new com.moego.server.retail.param.SetPaymentParams();
            BeanUtils.copyProperties(paymentSummary, params);
            params.setConvenienceFee(paymentResultParams.getConvenienceFee());
            // 同步至 QuickBook
            ThreadPool.execute(() -> {
                quickBooksClient.invoiceCheckQbSyncStatus(null, invoiceId);
            });
            // 被调用方根据orderVersion自行判断是否更新 order
            return retailInvoiceClient.setPaymentResult(params);
        } else if (MODULE_FULFILLMENT.equals(module)) {
            com.moego.server.grooming.params.SetPaymentParams params =
                    new com.moego.server.grooming.params.SetPaymentParams();
            BeanUtils.copyProperties(paymentSummary, params);
            params.setIsOnline(paymentResultParams.getIsOnline());
            params.setIsPreAuth(paymentResultParams.getIsFromPreAuth());
            params.setConvenienceFee(paymentResultParams.getConvenienceFee());
            if (paymentSummary.getRefunds() != null) {
                params.setRefunds(paymentSummary.getRefunds().stream()
                        .map(r -> {
                            SetPaymentParams.RefundDTO dto = new SetPaymentParams.RefundDTO();
                            BeanUtils.copyProperties(r, dto);
                            return dto;
                        })
                        .toList());
            }

            return groomingInvoiceClient.setPaymentResult(params);
        }
        return false;
    }

    public List<InvoicePaymentDto> getGroomingInvoicePaymentDto(Integer businessId, List<Integer> invoiceIds) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return Collections.emptyList();
        }
        List<InvoicePaymentDto> paymentDtoList = paymentMapper.selectByGroomingInvoiceIds(businessId, invoiceIds);
        // 将credit card支付方式拆分为多种
        for (InvoicePaymentDto invoicePaymentDto : paymentDtoList) {
            invoicePaymentDto.setMethod(PaymentUtil.getCreditCardReportMethod(
                    invoicePaymentDto.getMethod(), invoicePaymentDto.getSquarePaymentMethod()));
        }
        return paymentDtoList;
    }

    public List<PaymentSummary> getPaymentList(String module, List<Integer> invoiceIds) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return Collections.emptyList();
        }
        List<Payment> payments = paymentMapper.selectByModuleAndInvoiceIds(module, invoiceIds);

        if (payments == null || payments.isEmpty()) {
            return new ArrayList<>();
        }

        List<Refund> refunds = refundMapper.selectByModuleAndInvoiceIds(module, invoiceIds);

        return payments.stream().collect(groupingBy(Payment::getInvoiceId)).entrySet().stream()
                .map(entry -> {
                    PaymentSummary paymentSummary = new PaymentSummary();
                    paymentSummary.setModule(module);
                    paymentSummary.setInvoiceId(entry.getKey());
                    paymentSummary.setPayments(entry.getValue().stream()
                            .map(p -> {
                                PaymentSummary.PaymentDto dto = new PaymentSummary.PaymentDto();
                                BeanUtils.copyProperties(p, dto);
                                dto.setStatus(p.getStatusString());
                                return dto;
                            })
                            .toList());
                    // 过滤掉未支付的payment, 然后统计支付总额和processing fee
                    paymentSummary.setPaidAmount(entry.getValue().stream()
                            .filter(p -> PaymentStatusEnum.PAID.compareTo(p.getStatus()) <= 0)
                            .map(Payment::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    paymentSummary.setProcessingFee(entry.getValue().stream()
                            .filter(p -> PaymentStatusEnum.PAID.compareTo(p.getStatus()) <= 0)
                            .map(Payment::getProcessingFee)
                            .filter(processingFee -> processingFee.compareTo(BigDecimal.ZERO) > 0)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));

                    List<PaymentSummary.PaymentRefundDto> refundDtoList = refunds.stream()
                            .filter(r -> entry.getKey().equals(r.getInvoiceId()))
                            .map(r -> {
                                PaymentSummary.PaymentRefundDto dto = new PaymentSummary.PaymentRefundDto();
                                BeanUtils.copyProperties(r, dto);
                                dto.setStatus(r.getStatus().equals(PaymentStatusEnum.FAILED) ? FAILED : "created");
                                return dto;
                            })
                            .toList();

                    if (!refundDtoList.isEmpty()) {
                        paymentSummary.setRefunds(refundDtoList);
                        paymentSummary.setRefundedAmount(refundDtoList.stream()
                                .filter(r -> !r.getStatus().equals(FAILED))
                                .map(PaymentSummary.PaymentRefundDto::getAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        paymentSummary.setRefundedAmount(new BigDecimal(0));
                    }

                    return paymentSummary;
                })
                .toList();
    }

    public PaymentSummary getPayments(String module, Integer invoiceId, boolean onlySuccess) {
        List<Payment> payments =
                paymentMapper.selectByModuleAndInvoiceIds(module, Collections.singletonList(invoiceId));

        if (payments == null || payments.isEmpty()) {
            return null;
        }

        PaymentSummary paymentSummary = new PaymentSummary();
        paymentSummary.setModule(module);
        paymentSummary.setInvoiceId(invoiceId);
        paymentSummary.setCustomerId(payments.get(0).getCustomerId());
        paymentSummary.setPayments(payments.stream()
                .filter(p -> !onlySuccess || PaymentStatusEnum.PAID.compareTo(p.getStatus()) <= 0)
                .map(p -> {
                    PaymentSummary.PaymentDto dto = new PaymentSummary.PaymentDto();
                    BeanUtils.copyProperties(p, dto);
                    dto.setStatus(p.getStatusString());
                    return dto;
                })
                .toList());
        paymentSummary.setPaidAmount(payments.stream()
                .filter(p -> PaymentStatusEnum.PAID.compareTo(p.getStatus()) <= 0)
                .map(Payment::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        paymentSummary.setProcessingFee(payments.stream()
                .filter(p -> PaymentStatusEnum.PAID.compareTo(p.getStatus()) <= 0)
                .map(Payment::getProcessingFee)
                .filter(processingFee -> processingFee.compareTo(BigDecimal.ZERO) > 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        List<Refund> refunds = refundMapper.selectByModuleAndInvoiceIds(module, Collections.singletonList(invoiceId));

        if (!CollectionUtils.isEmpty(refunds)) {
            paymentSummary.setRefunds(refunds.stream()
                    .map(r -> {
                        PaymentSummary.PaymentRefundDto dto = new PaymentSummary.PaymentRefundDto();
                        BeanUtils.copyProperties(r, dto);
                        dto.setStatus(r.getStatus().equals(PaymentStatusEnum.FAILED) ? "failed" : "created");
                        return dto;
                    })
                    .toList());
            paymentSummary.setRefundedAmount(refunds.stream()
                    .filter(r -> !r.getStatus().equals(PaymentStatusEnum.FAILED))
                    .map(Refund::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        return paymentSummary;
    }

    public PaymentAmountSumDto getAmountSumByCustomerId(Integer businessId, Integer customerId) {
        return paymentMapper.getAmountSumByCustomerId(businessId, customerId);
    }

    public Payment selectById(Integer paymentId) {
        return paymentMapper.selectByPrimaryKey(paymentId);
    }

    public Integer saveSignature(Integer businessId, PaymentSigRequest param) {
        Payment current = paymentMapper.selectByPrimaryKey(param.getPrimaryId());
        if (current == null || !current.getBusinessId().equals(businessId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "invalid id");
        }
        Payment toUpdate = new Payment();
        toUpdate.setId(param.getPrimaryId());
        toUpdate.setSignature(param.getSignature());
        toUpdate.setUpdateTime(DateUtil.get10Timestamp());
        return paymentMapper.updateByPrimaryKeySelective(toUpdate);
    }

    public PaymentListDto selectPageList(Integer businessId, TransactionHistoryReq req) {
        if (VENDOR_ALL.equalsIgnoreCase(req.getVendor())) {
            req.setVendor(null);
        }
        String merchant = null;
        if (METHOD_NAME_SQUARE.equalsIgnoreCase(req.getVendor())) {
            merchant = squareService.getBusinessSquareWithException(businessId).getMerchantId();
        }
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(businessId).build());
        if (businessInfo == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        Long startTimeStamp = null, endTimestamp = null;
        try {
            if (StringUtils.hasText(req.getStartDate())) {
                startTimeStamp = DateTimeUtils.toInstant(
                                LocalDate.parse(
                                                req.getStartDate(),
                                                DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT))
                                        .atStartOfDay(),
                                StringUtils.hasText(businessInfo.getTimezoneName())
                                        ? businessInfo.getTimezoneName()
                                        : TimeConstant.DEFAULT_TIME_ZONE)
                        .getEpochSecond();
            }
            if (StringUtils.hasText(req.getEndDate())) {
                endTimestamp = DateTimeUtils.toInstant(
                                LocalDate.parse(
                                                req.getEndDate(),
                                                DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT))
                                        .plusDays(1L)
                                        .atStartOfDay(),
                                StringUtils.hasText(businessInfo.getTimezoneName())
                                        ? businessInfo.getTimezoneName()
                                        : TimeConstant.DEFAULT_TIME_ZONE)
                        .getEpochSecond();
            }
        } catch (Exception e) {
            log.error("parse timestamp error:{}", req, e);
        }
        List<Payment> paymentList = paymentMapper.selectList(
                businessId,
                req.getModule(),
                req.getCustomerId(),
                req.getMethod(),
                req.getVendor(),
                merchant,
                req.getInvoiceIds(),
                req.getPageNum() == null ? null : CommonUtil.getLimitOffset(req.getPageNum(), req.getPageSize()),
                req.getPageSize(),
                req.getOrder(),
                startTimeStamp,
                endTimestamp);
        Integer paymentCountMap = paymentMapper.getListCount(
                businessId,
                req.getModule(),
                req.getCustomerId(),
                req.getMethod(),
                req.getVendor(),
                merchant,
                req.getInvoiceIds(),
                startTimeStamp,
                endTimestamp);

        PaymentListDto returnMap = new PaymentListDto();
        returnMap.setPaymentCount(paymentCountMap);
        if (paymentList == null || paymentList.size() == 0) {
            return returnMap;
        }
        List<Refund> refundList = getRefunds(req.getModule(), paymentList);
        List<GroomingCustomerInfoDTO> customerInfoList = getCustomers(businessId, paymentList);
        List<InvoiceSummaryDTO> groomingInvoiceList = getInvoiceSummaries(paymentList);

        // 查询OB定金记录
        Set<Integer> paymentIds = paymentList.stream().map(Payment::getId).collect(Collectors.toSet());
        MoeBookOnlineDepositBatchQueryVO vo = new MoeBookOnlineDepositBatchQueryVO();
        vo.setBusinessId(businessId);
        vo.setPaymentIds(paymentIds);
        List<BookOnlineDepositDTO> prepayDTOs = iBookOnlineDepositClient.getOBDepositByPaymentIds(vo);
        buildPaymentList(returnMap, paymentList, groomingInvoiceList, customerInfoList, refundList, prepayDTOs);
        return returnMap;
    }

    public PaymentListDto selectPageListV2(Long companyId, TransactionHistoryReq req) {
        if (VENDOR_ALL.equalsIgnoreCase(req.getVendor())) {
            req.setVendor(null);
        }
        List<String> merchants = new ArrayList<>();
        if (METHOD_NAME_SQUARE.equalsIgnoreCase(req.getVendor())) {
            var businessIds = iBusinessBusinessClient.getBusinessByCompanyId(companyId.intValue()).keySet().stream()
                    .toList();
            merchants = squareService.getBusinessSquareListWithBusinessIds(businessIds).stream()
                    .map(BusinessSquare::getMerchantId)
                    .toList();
        }
        TimeZone companyTimeZone = companyServiceBlockingStub
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getTimeZone();
        if (companyTimeZone.getName().isEmpty()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        Long startTimeStamp = null, endTimestamp = null;
        try {
            if (StringUtils.hasText(req.getStartDate())) {
                startTimeStamp = DateTimeUtils.toInstant(
                                LocalDate.parse(
                                                req.getStartDate(),
                                                DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT))
                                        .atStartOfDay(),
                                StringUtils.hasText(companyTimeZone.getName())
                                        ? companyTimeZone.getName()
                                        : TimeConstant.DEFAULT_TIME_ZONE)
                        .getEpochSecond();
            }
            if (StringUtils.hasText(req.getEndDate())) {
                endTimestamp = DateTimeUtils.toInstant(
                                LocalDate.parse(
                                                req.getEndDate(),
                                                DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT))
                                        .plusDays(1L)
                                        .atStartOfDay(),
                                StringUtils.hasText(companyTimeZone.getName())
                                        ? companyTimeZone.getName()
                                        : TimeConstant.DEFAULT_TIME_ZONE)
                        .getEpochSecond();
            }
        } catch (Exception e) {
            log.error("parse timestamp error:{}", req, e);
        }
        List<Payment> paymentList = paymentMapper.selectListV2(
                companyId,
                req.getModule(),
                req.getCustomerId(),
                req.getMethod(),
                req.getVendor(),
                merchants,
                req.getInvoiceIds(),
                req.getPageNum() == null ? null : CommonUtil.getLimitOffset(req.getPageNum(), req.getPageSize()),
                req.getPageSize(),
                req.getOrder(),
                startTimeStamp,
                endTimestamp);
        Integer paymentCountMap = paymentMapper.getListCountV2(
                companyId,
                req.getModule(),
                req.getCustomerId(),
                req.getMethod(),
                req.getVendor(),
                merchants,
                req.getInvoiceIds(),
                startTimeStamp,
                endTimestamp);

        PaymentListDto returnMap = new PaymentListDto();
        returnMap.setPaymentCount(paymentCountMap);
        if (paymentList == null || paymentList.isEmpty()) {
            return returnMap;
        }
        List<Refund> refundList = getRefunds(req.getModule(), paymentList);

        // 查询OB定金记录
        Set<Integer> paymentIds = paymentList.stream().map(Payment::getId).collect(Collectors.toSet());
        MoeBookOnlineDepositBatchQueryByCompanyVO vo = new MoeBookOnlineDepositBatchQueryByCompanyVO();
        vo.setCompanyId(companyId);
        vo.setPaymentIds(paymentIds);
        List<BookOnlineDepositDTO> prepayDTOs = iBookOnlineDepositClient.getOBDepositByPaymentIdsV2(vo);
        // 构造GroomingCustomerInfoDTO 来复用buildPaymentList，目前只需要last name和first name
        var customerIds = paymentList.stream()
                .map(Payment::getCustomerId)
                .map(Long::valueOf)
                .distinct()
                .toList();
        var customerNameViewMap = businessCustomerServiceBlockingStub
                .batchGetCustomer(BatchGetCustomerRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllIds(customerIds)
                        .build())
                .getCustomersList();
        List<GroomingCustomerInfoDTO> customerInfoList = customerNameViewMap.stream()
                .map(customerNameView -> {
                    GroomingCustomerInfoDTO groomingCustomerInfoDTO = new GroomingCustomerInfoDTO();
                    groomingCustomerInfoDTO.setCustomerId((int) customerNameView.getId());
                    groomingCustomerInfoDTO.setFirstName(customerNameView.getFirstName());
                    groomingCustomerInfoDTO.setLastName(customerNameView.getLastName());
                    return groomingCustomerInfoDTO;
                })
                .toList();
        List<InvoiceSummaryDTO> groomingInvoiceList = getInvoiceSummaries(paymentList);
        buildPaymentList(returnMap, paymentList, groomingInvoiceList, customerInfoList, refundList, prepayDTOs);
        return returnMap;
    }

    private void buildPaymentList(
            PaymentListDto returnMap,
            List<Payment> paymentList,
            List<InvoiceSummaryDTO> groomingInvoiceList,
            List<GroomingCustomerInfoDTO> customerInfoList,
            List<Refund> refundList,
            List<BookOnlineDepositDTO> prepayDTOs) {
        Map<Integer, BookOnlineDepositDTO> paymentIdToOBDeposit =
                prepayDTOs.stream().collect(Collectors.toMap(BookOnlineDepositDTO::getPaymentId, Function.identity()));
        returnMap.setPaymentList(paymentList.stream()
                .filter(payment -> !shouldHidePayment(payment, paymentIdToOBDeposit))
                .map(p -> {
                    PaymentDTO dto = PaymentStructMapper.INSTANCE.toDto(p);

                    if (MODULE_GROOMING.equals(p.getModule())) {
                        Optional<InvoiceSummaryDTO> optionalInvoiceDTO = groomingInvoiceList.stream()
                                .filter(i -> i.getId().equals(p.getInvoiceId()))
                                .findAny();
                        optionalInvoiceDTO.ifPresent(
                                invoiceSummaryDTO -> dto.setGroomingId(invoiceSummaryDTO.getGroomingId()));
                    } else if (MODULE_RETAIL.equals(p.getModule())) {
                        if (dto.getInvoiceId() < retailOffset) {
                            // 迁移数据后，这里的invoice需要转化成迁移后的id
                            dto.setInvoiceId(p.getInvoiceId() + retailOffset.intValue());
                        }
                    }

                    customerInfoList.stream()
                            .filter(c -> c.getCustomerId().equals(p.getCustomerId()))
                            .findFirst()
                            .ifPresent(custInfo -> dto.setCustomerName(
                                    CommonUtil.nameFormat(custInfo.getFirstName(), custInfo.getLastName())));
                    dto.setRefunds(refundList.stream()
                            .filter(r -> r.getOriginPaymentId().equals(p.getId()))
                            .map(r -> {
                                RefundDTO refundDTO = new RefundDTO();
                                BeanUtils.copyProperties(r, refundDTO);
                                return refundDTO;
                            })
                            .toList());
                    // 是否为prepay
                    dto.setIsPrepay(paymentIdToOBDeposit.containsKey(p.getId())
                            && Objects.equals(
                                    DepositPaymentTypeEnum.PrePay,
                                    paymentIdToOBDeposit.get(p.getId()).getDepositType()));
                    dto.setRefundAndCollected();
                    dto.setPaymentMethod();
                    return dto;
                })
                .toList());
        returnMap.setPaymentCount(returnMap.getPaymentList().size());
    }

    private boolean shouldHidePayment(Payment payment, Map<Integer, BookOnlineDepositDTO> paymentIdToOBDeposit) {
        // 过滤掉 OB prepay 且状态为 PROCESSING 的 payment 记录
        // 会出现这种情况的原因：OBC submit 之后，调用了 /payment/v2/bookOnline/pay 创建 payment 记录（processing），
        // 在 confirm 之前报错或者直接关闭了浏览器，造成 payment intent 没有触发 confirm，回调里不能将 booking request 的 payment_status 扭转为 FAILED or
        // PROCESSING
        // 导致商家看不到 OB request，但是却可以在 customer payment history 看到一笔 processing 的 payment 记录，从而造成疑惑
        var obDeposit = paymentIdToOBDeposit.get(payment.getId());
        return obDeposit != null
                && Objects.equals(payment.getStatus(), PaymentStatusEnum.PROCESSING)
                && isInvisibleBookingRequest(obDeposit);
    }

    private boolean isInvisibleBookingRequest(BookOnlineDepositDTO obDeposit) {
        var invisiblePaymentStatus =
                Set.of(BookingRequestModel.PaymentStatus.WAITING, BookingRequestModel.PaymentStatus.FAILED);

        if (isNormal(obDeposit.getBookingRequestId())) {
            var bookingRequest =
                    bookingRequestHelper.getBookingRequestByBookingRequestId(obDeposit.getBookingRequestId());
            return bookingRequest != null && invisiblePaymentStatus.contains(bookingRequest.getPaymentStatus());
        }

        if (isNormal(obDeposit.getGroomingId())) {
            var bookingRequest = bookingRequestHelper.getBookingRequestByAppointmentId(
                    obDeposit.getBusinessId(), obDeposit.getGroomingId());
            return bookingRequest != null && invisiblePaymentStatus.contains(bookingRequest.getPaymentStatus());
        }

        // should not happen
        return false;
    }

    private List<Refund> getRefunds(String module, List<Payment> paymentList) {
        return refundMapper.selectByModuleAndInvoiceIds(
                module,
                paymentList.stream().map(Payment::getInvoiceId).distinct().toList());
    }

    private List<GroomingCustomerInfoDTO> getCustomers(Integer businessId, List<Payment> paymentList) {
        GroomingQueryCustomerParams params = new GroomingQueryCustomerParams();
        params.setBusinessId(businessId);
        params.setCustomerIds(
                paymentList.stream().map(Payment::getCustomerId).distinct().toList());
        return iCustomerGroomingClient.getCustomerGroomingInfo(params);
    }

    private List<InvoiceSummaryDTO> getInvoiceSummaries(List<Payment> paymentList) {
        List<Integer> groomingInvoiceIds = paymentList.stream()
                .filter(p -> MODULE_GROOMING.equals(p.getModule()))
                .map(Payment::getInvoiceId)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(groomingInvoiceIds)) {
            return new ArrayList<>();
        }

        CommonIdsParams commonIdsParams = new CommonIdsParams();
        commonIdsParams.setIds(groomingInvoiceIds);
        return groomingInvoiceClient.selectInvoicesByIds(commonIdsParams);
    }

    /**
     * 创建stripe customer，用于OB take deposit，此时还没有创建customer，但需要支付和save card，所以提前创建stripe customer
     *
     * @param stripeAccount
     * @param paymentParams
     * @return
     * @throws StripeException
     */
    private String createStripeCustomerBeforePayment(MmStripeAccount stripeAccount, CreatePaymentParams paymentParams)
            throws StripeException {
        if (paymentParams.getCustomerData() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "save card need customer data");
        } else {
            BookOnlineCustomerParams customerData = paymentParams.getCustomerData();
            StripeCustomerParam stripeCustomerParam = StripeCustomerParam.builder()
                    .name(customerData.getFirstName() + " " + customerData.getLastName())
                    .stripeAccountId(stripeAccount.getStripeAccountId())
                    .phone(customerData.getPhoneNumber())
                    .email(customerData.getEmail())
                    .build();
            Customer customer = stripeService.createCustomer(stripeCustomerParam);
            return customer.getId();
        }
    }

    public String setStripeCustomerCofTimeStamp(Integer businessId, Integer customerId, boolean send) {
        // cof link信息更新到customer metadata后， 异步记录到DB
        try {
            String stripeCustomerId = getStripeCustomerIdWithCreate(businessId, customerId);
            if (stripeCustomerId == null) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "get stripe customer failed.");
            }
            Map<String, String> metadata = new HashMap<>();
            Long now = DateUtil.get10Timestamp();
            metadata.put(PaymentStripeStatus.COF_TIME_STAMP_KEY, now.toString());
            metadata.put(PaymentStripeStatus.COF_SUBMITTED_KEY, Boolean.FALSE.toString());
            metadata.put(PaymentStripeStatus.COF_EXPIRE_TIME_STAMP_KEY, getCOFExpireTimeStamp(businessId, now));
            if (send) {
                metadata.put(PaymentStripeStatus.COF_SEND_TIME_STAMP_KEY, now.toString());
                // 发送 message 后，异步创建 card link reminder task
                ThreadPool.execute(() -> {
                    CardLinkParam moeCardLink = new CardLinkParam();
                    moeCardLink.setBusinessId(businessId);
                    moeCardLink.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
                    moeCardLink.setCustomerId(customerId);
                    moeCardLink.setCofTimestamp(now);
                    moeCardLink.setStatus(MessageConst.LINK_INIT);
                    Integer count = iMessageClient.createOrUpdateCardLink(moeCardLink);
                    log.info("create card link count:{} for {}", count, stripeCustomerId);
                });
            }
            Customer stripeCustomer = Customer.retrieve(stripeCustomerId);
            stripeCustomer.update(
                    CustomerUpdateParams.builder().setMetadata(metadata).build());
            return stripeCustomerId;
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "set stripe customer meta data failed for " + customerId);
        }
    }

    private static final String CUSTOMIZED_COF_EXPIRED_TIME_METADATA_KEY = "customized_cof_expire_seconds";

    private String getCOFExpireTimeStamp(Integer businessId, Long now) {
        try {
            var business = businessServiceBlockingStub.getLocationDetail(
                    GetLocationDetailRequest.newBuilder().setId(businessId).build());
            var companies = companyServiceBlockingStub.queryCompaniesByIds(QueryCompaniesByIdsRequest.newBuilder()
                    .addAllCompanyIds(List.of(business.getLocation().getCompanyId()))
                    .build());
            var enterpriseId = companies
                    .getCompanyIdToCompanyMap()
                    .get(business.getLocation().getCompanyId())
                    .getEnterpriseId();
            if (enterpriseId == 0) {
                return String.valueOf(now + PaymentStripeStatus.DEFAULT_EXPIRE_SECONDS);
            }
            var metadata = metadataServiceBlockingStub.describeMetadata(DescribeMetadataRequest.newBuilder()
                    .setKeyName(CUSTOMIZED_COF_EXPIRED_TIME_METADATA_KEY)
                    .putOwners(OwnerType.OWNER_TYPE_ENTERPRISE_VALUE, enterpriseId)
                    .build());
            log.info("get customized cof config:{} ", metadata.getValueMapMap());
            var valueModel = metadata.getValueMapMap().get(CUSTOMIZED_COF_EXPIRED_TIME_METADATA_KEY);
            if (valueModel != null
                    && StringUtils.hasText(valueModel.getValue())
                    && Long.parseLong(valueModel.getValue()) != 0) {
                var expiredSeconds = Long.parseLong(valueModel.getValue());
                return String.valueOf(now + expiredSeconds);
            }
        } catch (Exception e) {
            // 获取特定配置失败则直接返回默认值
            log.error("get customized cof expire seconds failed for businessId:{} ", businessId, e);
        }
        return String.valueOf(now + PaymentStripeStatus.DEFAULT_EXPIRE_SECONDS);
    }

    private void markAsSubmitted(String stripeCustomerId) {
        try {
            Map<String, String> metadata = new HashMap<>();
            metadata.put(PaymentStripeStatus.COF_SUBMITTED_KEY, Boolean.TRUE.toString());
            Customer stripeCustomer = Customer.retrieve(stripeCustomerId);
            CustomerUpdateParams updateParams =
                    CustomerUpdateParams.builder().setMetadata(metadata).build();
            stripeCustomer.update(updateParams);
        } catch (StripeException e) {
            throw new CommonException(
                    ResponseCodeEnum.STRIPE_EXCEPTION, "set stripe customer meta data failed for " + stripeCustomerId);
        }
    }

    public Integer checkingCofTimeStamp(Integer customerId) {
        MmStripeCustomer mmStripeCustomer = stripeCustomerMapper.selectByCustomerId(customerId);
        if (mmStripeCustomer == null || StringUtils.isEmpty(mmStripeCustomer.getStripeCustomerId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "no stripe customer found.");
        }

        try {
            Customer stripeCustomer = Customer.retrieve(mmStripeCustomer.getStripeCustomerId());
            String timestamp = stripeCustomer.getMetadata().get(PaymentStripeStatus.COF_TIME_STAMP_KEY);
            if (timestamp == null) {
                return PaymentStripeStatus.SUBMIT_EXPIRED;
            }
            String expiredTimestamp = stripeCustomer.getMetadata().get(PaymentStripeStatus.COF_EXPIRE_TIME_STAMP_KEY);
            if (expiredTimestamp != null && DateUtil.get10Timestamp() > Long.parseLong(expiredTimestamp)) {
                return PaymentStripeStatus.SUBMIT_EXPIRED;
            }
            if (expiredTimestamp == null
                    && DateUtil.get10Timestamp() - Long.parseLong(timestamp)
                            > PaymentStripeStatus.DEFAULT_EXPIRE_SECONDS) {
                return PaymentStripeStatus.SUBMIT_EXPIRED;
            }
            if (Boolean.TRUE
                    .toString()
                    .equals(stripeCustomer.getMetadata().get(PaymentStripeStatus.COF_SUBMITTED_KEY))) {
                return PaymentStripeStatus.SUBMIT_SUBMITTED;
            }
            return PaymentStripeStatus.SUBMIT_VALID;
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "invalid stripe customer id " + mmStripeCustomer.getStripeCustomerId());
        }
    }

    public String createPaymentMethod(Integer customerId, CardRequestParams cardRequestParams) {
        MmStripeCustomer mmStripeCustomer = stripeCustomerMapper.selectByCustomerId(customerId);
        if (!TipsUtil.isStripeCustomerValid(mmStripeCustomer)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "no stripe customer found.");
        }
        cardRequestParams.setBusinessId(mmStripeCustomer.getBusinessId());
        String pmId = stripeService.createPaymentMethod(mmStripeCustomer.getStripeCustomerId(), cardRequestParams);
        markAsSubmitted(mmStripeCustomer.getStripeCustomerId());
        ThreadPool.execute(() -> {
            stripeService.deduplicatePaymentsMethod(mmStripeCustomer.getStripeCustomerId());
            stripeService.markStripeCustomerHasCOF(mmStripeCustomer.getId());
            // 标记 link as submitted in db
            iMessageClient.markAsSubmitted(customerId);
        });
        return pmId;
    }

    private String getStripeCustomerIdWithCreate(Integer businessId, Integer customerId) throws StripeException {
        try {
            MmStripeCustomer mmStripeCustomer = stripeCustomerMapper.selectByCustomerId(customerId);
            if (mmStripeCustomer == null) {
                MmStripeAccount stripeAccount = stripeAccountMapper.selectByBusinessId(businessId);
                if (stripeAccount == null
                        || stripeAccount.getStripeAccountId() == null
                        || stripeAccount.getStripeAccountId().isEmpty()) {
                    throw ExceptionUtil.bizException(Code.CODE_STRIPE_ACCOUNT_NOT_FOUND);
                }

                CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
                customerInfoIdParams.setBusinessId(businessId);
                customerInfoIdParams.setCustomerId(customerId);

                CustomerInfoDto customerInfo = iCustomerCustomerClient.getCustomerWithDeleted(customerInfoIdParams);
                CreateCustomerRequest createCustomerRequest = new CreateCustomerRequest();
                createCustomerRequest.setStripeAccountId(stripeAccount.getStripeAccountId());
                createCustomerRequest.setName(customerInfo.getLastName() + " " + customerInfo.getFirstName());
                Customer customer = stripeService.createCustomer(createCustomerRequest);

                stripeService.saveStripeCustomer(customer.getId(), businessId, customerId);
                return customer.getId();
            } else {
                return mmStripeCustomer.getStripeCustomerId();
            }
        } catch (Exception e) {
            log.error("Fail to create stripe customer: ", e);
            return null;
        }
    }

    public BusinessHasValidAccountResponse businessHasVerifiedAccount(Integer businessId) {
        BusinessHasValidAccountResponse result = new BusinessHasValidAccountResponse();
        result.setBusinessId(businessId);
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(businessId).build());
        if (PaymentMethodEnum.CARD_PROCESSOR_TYPE_SQUARE.equals(businessDto.getPrimaryPayType())) {
            // for square
            try {
                GetSquareTokenResponse squareTokenResponse = squareService.getToken(businessId);
                result.setHasValidAccount(squareTokenResponse.getSquareConnected());
            } catch (CommonException commonException) {
                log.error("get square token info failed {}", commonException.getMessage());
                result.setHasValidAccount(false);
            }
            return result;
        }
        // for stripe
        MmStripeAccount bizStripeAccount = stripeAccountMapper.selectByBusinessId(businessId);
        result.setHasValidAccount(bizStripeAccount != null
                && stripeService.bizHasVerifyBankAccount(bizStripeAccount.getStripeAccountId()));
        return result;
    }

    /**
     * 支付并更新 invoice 状态
     *
     * @param businessId
     * @param request
     * @return
     * @see <a href="https://developer.squareup.com/docs/payments-api/webhooks#ach-payment-related-webhook-behavior">
     * webhook for payment doc
     * </a>
     */
    public SquareTakePaymentResponse takePaymentAndUpdateInvoice(Integer businessId, SquarePaymentRequest request) {
        // 检查定金支付状态
        if (MODULE_GROOMING.equals(request.getModule())) {
            checkDepositStatus(request.getIsDeposit(), request.getInvoiceId());
        }

        SquareTakePaymentResponse response = squareService.takePayment(businessId, request);
        // 更新invoice成功后，标记支付记录完成
        if (sendPaymentResultToModule(
                request.getModule(),
                request.getInvoiceId(),
                request.getIsOnline(),
                BooleanEnum.VALUE_TRUE.equals(request.getIsDeposit()))) {
            squareService.updatePaymentDBRecordCompleted(response.getPrimaryId());
            // notify for pay online
            Payment paymentInfo = new Payment();
            BeanUtils.copyProperties(request, paymentInfo);
            afterPaymentFinish(paymentInfo.getId());
            notifyAfterPayOnlineSuccess(paymentInfo);
        } else {
            log.warn("update invoice failed for {}", request.getInvoiceId());
        }
        return response;
    }

    public void updateInvoiceForReader(Integer paymentPrimaryId, SquarePaymentRequest request) {
        // 更新invoice成功后，标记支付记录完成
        if (sendPaymentResultToModule(
                request.getModule(),
                request.getInvoiceId(),
                request.getIsOnline(),
                BooleanEnum.VALUE_TRUE.equals(request.getIsDeposit()))) {
            squareService.updatePaymentDBRecordCompleted(paymentPrimaryId);
        } else {
            log.warn("update invoice failed for reader {}", request.getInvoiceId());
        }
    }

    public void recordRefundSuccessForReader(SquareRefundHookParam hookParam) {
        final String squarePaymentId = hookParam.getSquarePaymentId();
        final String squareRefundId = hookParam.getRefundId();
        Payment payment = paymentMapper.selectByStripeChargeId(squarePaymentId);
        if (payment == null) {
            log.error("not payment history found for this refund - {}", squarePaymentId);
            return;
        }
        List<com.moego.server.payment.mapperbean.Refund> moegoRefunds = refundMapper.selectByPaymentId(payment.getId());
        Optional<com.moego.server.payment.mapperbean.Refund> foundMoegoRefund = moegoRefunds.stream()
                .filter(r -> squareRefundId.equals(r.getStripeRefundId()))
                .findFirst();
        if (foundMoegoRefund.isPresent()) {
            // record already
            log.info("refund {} for {} completed7", squareRefundId, payment.getId());
        } else {
            com.moego.server.payment.mapperbean.Refund refund = new com.moego.server.payment.mapperbean.Refund();
            refund.setModule(payment.getModule());
            refund.setInvoiceId(payment.getInvoiceId());
            refund.setBusinessId(payment.getBusinessId());
            refund.setCompanyId(payment.getCompanyId());
            refund.setCustomerId(payment.getCustomerId());
            refund.setAmount(hookParam.getRefundAmount());
            refund.setOriginPaymentId(payment.getId());
            refund.setReason(hookParam.getReason());
            refund.setStatus(PaymentStatusEnum.CREATED);
            refund.setCreateTime(CommonUtil.get10Timestamp());

            refund.setStripeRefundId(squareRefundId);
            refund.setUpdateTime(refund.getCreateTime());
            refundMapper.insertSelective(refund);

            // 获取invoice支付历史， 并更新到invoice
            sendRefundResultToGroomingModule(payment);
        }
    }

    /**
     * 同步支付结果到grooming invoice
     *
     * @param payment
     */
    public void sendRefundResultToGroomingModule(Payment payment) {
        if (payment.getInvoiceId() == null || payment.getInvoiceId() <= 0) {
            return;
        }
        // 获取invoice支付历史， 并更新到invoice
        PaymentSummary paymentSummary = getPayments(payment.getModule(), payment.getInvoiceId(), true);
        SetPaymentParams setPaymentParams = new SetPaymentParams();
        BeanUtils.copyProperties(paymentSummary, setPaymentParams);
        setPaymentParams.setIsRefund(true);
        if (paymentSummary.getRefunds() != null) {
            setPaymentParams.setRefunds(paymentSummary.getRefunds().stream()
                    .map(r -> {
                        SetPaymentParams.RefundDTO refundDTO = new SetPaymentParams.RefundDTO();
                        BeanUtils.copyProperties(r, refundDTO);
                        return refundDTO;
                    })
                    .collect(Collectors.toList()));
        }
        groomingInvoiceClient.setPaymentResult(setPaymentParams);
    }

    public SquareTerminalCheckoutResponse webhookUpdatePaymentAndInvoice(JSONObject jsonObject) {
        SquareTerminalCheckoutResponse response = squareService.terminalChargeUpdated(jsonObject);
        if (!response.getSuccess()) {
            log.warn("deal with terminal callback failed for {}", response.getReason());
            return response;
        }
        // 更新invoice成功后，标记支付记录完成
        if (sendPaymentResultToModule(
                response.getModule(),
                response.getInvoiceId(),
                response.getIsOnline(),
                Boolean.TRUE.equals(response.getIsDeposit()))) {
            response.getMoegoPaymentIds().forEach(primaryPaymentId -> {
                squareService.updatePaymentDBRecordCompleted(primaryPaymentId);
            });
        }
        return response;
    }

    public void setMmStripeCustomerHasCard() {
        // get all stripe customer with <hasCard = 0>
        List<MmStripeCustomer> stripeCustomerList = stripeCustomerMapper.selectAllCustomers();
        Map<Integer, MmStripeAccount> bizAccountMap = stripeCustomerMapper.selectAllBizMap();
        stripeCustomerList.forEach(stripeCust -> {
            if (StringUtils.isEmpty(stripeCust.getStripeCustomerId())) {
                Integer count = stripeCustomerMapper.deleteByPrimaryKey(stripeCust.getId());
                log.info("delete customer {} has no valid info {}", count, GsonUtil.toJson(stripeCust, true));
            } else {
                PaymentMethodCollection cards = null;
                try {
                    cards = stripeService.getPaymentMethodList(
                            stripeCust.getStripeCustomerId(),
                            bizAccountMap.get(stripeCust.getBusinessId()).getStripeAccountId());
                } catch (StripeException e) {
                    log.error("get card failed", e);
                    return;
                }
                if (cards != null && !CollectionUtils.isEmpty(cards.getData())) {
                    MmStripeCustomer toUpdate = new MmStripeCustomer();
                    toUpdate.setId(stripeCust.getId());
                    toUpdate.setHasCard(Boolean.TRUE);
                    stripeCustomerMapper.updateByPrimaryKeySelective(toUpdate);
                    log.info(
                            "moego customer{} - stripe {} has card",
                            stripeCust.getCustomerId(),
                            stripeCust.getStripeCustomerId());
                } else {
                    log.info("stripe customer{} no card", stripeCust.getStripeCustomerId());
                }
            }
        });
    }

    public void syncAllStripeAccountStatement() {
        List<MmStripeAccount> allConnectedAccount = stripeAccountMapper.selectAllAccount();
        for (int i = 0; i < allConnectedAccount.size(); i++) {
            MmStripeAccount currentAccount = allConnectedAccount.get(i);
            try {
                if (ObjectUtils.isEmpty(currentAccount.getStripeAccountId())) {
                    continue;
                }
                stripeService.updateStripeAccountStatement(
                        currentAccount.getStripeAccountId(), currentAccount.getBusinessId());
            } catch (Exception e) {
                log.error(
                        "sync business name to the stripe account {} failed for {}",
                        currentAccount.getStripeAccountId(),
                        e.getMessage());
            }
        }
    }

    public void tureOnConnectedAccountDebitNegative() {
        List<MmStripeAccount> allConnectedAccount = stripeAccountMapper.selectAllAccount();
        for (int i = 0; i < allConnectedAccount.size(); i++) {
            MmStripeAccount currentAccount = allConnectedAccount.get(i);
            try {
                if (StringUtils.isEmpty(currentAccount.getStripeAccountId())) {
                    continue;
                }
                stripeService.turnOnConnectedAccountDebitNegative(currentAccount.getStripeAccountId());
            } catch (StripeException e) {
                log.error(
                        "ture on {} debit negative flag failed for {}",
                        currentAccount.getStripeAccountId(),
                        e.getMessage());
            }
        }
    }

    public void rollBackAllUsConnectedAccountToPlus2() {
        List<MmStripeAccount> allConnectedAccount = stripeAccountMapper.selectAllAccount();
        for (MmStripeAccount currentAccount : allConnectedAccount) {
            try {
                if (!StringUtils.hasText(currentAccount.getStripeAccountId())
                        || currentAccount.getBusinessId() <= 0
                        || !BusinessConst.COUNTRY_US.equalsIgnoreCase(currentAccount.getCountry())) {
                    log.warn(
                            "omit this account {} , business_id = {}, country = {}",
                            currentAccount.getStripeAccountId(),
                            currentAccount.getBusinessId(),
                            currentAccount.getCountry());
                    continue;
                }
                Account account = stripeService.rollBackPayoutSchedule(currentAccount.getStripeAccountId());
                log.info(
                        "current account {} roll back to T + 2 success: {}",
                        account.getId(),
                        account.getSettings().getPayouts().getSchedule().getDelayDays());
            } catch (StripeException e) {
                log.error(
                        "current account roll back to T + 2 failed on {}  {}",
                        currentAccount.getStripeAccountId(),
                        e.getMessage());
            }
        }
    }

    /**
     * 0. 支持幂等操作
     * 1. 查询2023/01月份所有商家的processing fee差额
     * 2. 针对每个商家，一次性扣除差额， 包括所有online/reader
     * 3. 如果本笔交易有bookingFee，payment表记录的amount和processing fee已减去，对商家透明
     */
    public void chargeConnectedAccountFeeForJan2023(Boolean chargeNow) {
        List<ProcessingFeeDTO> allBizFeeSummary = paymentMapper.selectFeeGroupByBusinessId();
        for (int i = 0; i < allBizFeeSummary.size(); i++) {
            ProcessingFeeDTO fee = allBizFeeSummary.get(i);
            log.info(i + ": business {} need to be charged {}$.", fee.getBusinessId(), fee.getSum0());
            if (Boolean.TRUE.equals(chargeNow)
                    && Dictionary.COUNTRY_UNITED_STATES.equals(fee.getCountry())
                    && fee.getSum0().compareTo(BigDecimal.ONE) > 0) {
                MmStripeAccount stripeAccount = stripeAccountMapper.selectByBusinessId(fee.getBusinessId());
                ChargeByConnectedAccountRequest chargeRequest = new ChargeByConnectedAccountRequest();
                chargeRequest.setDefaultCurrency(Dictionary.CURRENCY_CODE_USD);
                chargeRequest.setStripeAccountId(stripeAccount.getStripeAccountId());
                chargeRequest.setDescription("Adjustment to Jan processing fee");
                chargeRequest.setAmountInCent(
                        fee.getSum0().multiply(BigDecimal.valueOf(100)).intValue());
                try {
                    Charge charge = stripeService.chargeByConnectedAccount(chargeRequest);
                    if (charge == null) {
                        log.info(
                                "already charge connected account {}, businessId {}",
                                stripeAccount.getStripeAccountId(),
                                fee.getBusinessId());
                    } else {
                        log.info(
                                "charge {} {} fee amount {} success.",
                                chargeRequest.getStripeAccountId(),
                                fee.getCountry(),
                                fee.getSum0());
                    }
                } catch (StripeException e) {
                    log.error(
                            "charge {} failed, business_id = {}",
                            chargeRequest.getStripeAccountId(),
                            fee.getBusinessId());
                }
            }
        }
    }

    /**
     * 如果是deposit支付，判断是否已有支付了的deposit记录，如果有则不再支付
     *
     * @param isDeposit deposit标志
     * @param invoiceId 支付的invoice id
     */
    public void checkDepositStatus(Byte isDeposit, Integer invoiceId) {
        if (BooleanEnum.VALUE_TRUE.equals(isDeposit) && invoiceId != null && invoiceId > 0) {
            DepositDto depositDto = iDepositClient.getDepositByInvoiceId(invoiceId);
            // 已有支付的定金时，抛异常，不再继续支付
            if (depositDto != null && Objects.equals(depositDto.getStatus(), PaymentStatusEnum.PAID)) {
                throw new CommonException(ResponseCodeEnum.DEPOSIT_HAS_PAID_EXCEPTION);
            }
        }
    }

    private void checkDepositConflict(Integer businessId, CreatePaymentParams paymentParams) {
        // preauth和deposit不能共存
        if (Boolean.TRUE.equals(paymentParams.getFromPreAuth())) {
            DepositDto depositDto = iDepositClient.getDepositByInvoiceId(paymentParams.getInvoiceId());
            if (depositDto != null && Objects.equals(depositDto.getStatus(), PaymentStatusEnum.PAID)) {
                throw new CommonException(ResponseCodeEnum.DEPOSIT_HAS_PAID_EXCEPTION);
            }
        } else if (BooleanEnum.VALUE_TRUE.equals(paymentParams.getIsDeposit())
                && paymentParams.getInvoiceId() != null
                && paymentParams.getInvoiceId() > 0) {
            DepositDto depositDto = iDepositClient.getDepositByInvoiceId(paymentParams.getInvoiceId());
            // 已有支付的定金时，抛异常，不再继续支付
            if (depositDto != null && Objects.equals(depositDto.getStatus(), PaymentStatusEnum.PAID)) {
                throw new CommonException(ResponseCodeEnum.DEPOSIT_HAS_PAID_EXCEPTION);
            }
            boolean closed = preAuthService.getByInvoiceIdClosed(businessId, paymentParams.getInvoiceId());
            if (!closed) {
                throw new CommonException(ResponseCodeEnum.DEPOSIT_HAS_PAID_EXCEPTION, "PreAuth Has Been Opened");
            }
        }
    }

    public MoeGoPayTransactionSummaryDto getMoeGoPayTransactionSummary(int businessId, long startTime, long endTime) {
        StripeTransactionSummaryPo po = paymentMapper.getStripTransactionSummary(businessId, startTime, endTime);
        if (po == null) {
            return null;
        }
        List<InvoicePaymentDto> invoiceList = paymentMapper.queryPaymentDtoList(
                businessId,
                List.of(PaymentStatusEnum.PAID, PaymentStatusEnum.COMPLETED),
                startTime,
                endTime,
                PaymentMethodEnum.METHOD_NAME_STRIPE,
                PaymentMethodEnum.METHOD_NAME_CREDIT_CARD);
        List<Long> orderIdList =
                invoiceList.stream().map(k -> k.getInvoiceId().longValue()).toList();
        // 根据 order id 批量查回 order 信息
        GetOrderListRequest params = GetOrderListRequest.newBuilder()
                .addAllOrderIds(orderIdList)
                .setQueryDetail(false)
                .build();
        List<OrderDetailModel> orderList = orderClient.getOrderList(params).getOrderListList();
        Double totalTips = orderList.stream()
                .filter(k -> k.getOrder().getTipsAmount() > 0)
                .mapToDouble(k -> k.getOrder().getTipsAmount())
                .sum();
        Double totalFee = orderList.stream()
                .filter(k -> k.getOrder().getExtraFeeAmount() > 0)
                .mapToDouble(k -> k.getOrder().getExtraFeeAmount())
                .sum();
        MoeGoPayTransactionSummaryDto dto = new MoeGoPayTransactionSummaryDto();
        dto.setTotalAmount(po.getAmount());
        dto.setTotalCount(po.getCount());
        dto.setTotalTips(totalTips);
        dto.setTotalFee(totalFee);
        return dto;
    }

    public List<TransactionHistoryExportDTO> buildExportDto(MoeBusinessDto businessInfo, List<PaymentDTO> paymentList) {
        SimpleDateFormat format = BusinessDateFormatUtil.getUTCFormatter(
                businessInfo.getDateFormat(), businessInfo.getTimeFormatType(), businessInfo.getTimezoneName());
        List<TransactionHistoryExportDTO> data = new ArrayList<>(paymentList.size());
        for (PaymentDTO payment : paymentList) {
            TransactionHistoryExportDTO export = new TransactionHistoryExportDTO();
            BeanUtils.copyProperties(payment, export);
            if (payment.getAmount() == null || payment.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                export.setAmount("");
            } else {
                export.setAmount(businessInfo.getCurrencySymbol() + payment.getAmount());
            }
            if (payment.getProcessingFee() == null || payment.getProcessingFee().compareTo(BigDecimal.ZERO) < 0) {
                export.setProcessingFee("");
            } else {
                export.setProcessingFee(businessInfo.getCurrencySymbol() + payment.getProcessingFee());
            }
            export.setCreateTime(format.format(payment.getCreateTime() * 1000));
            data.add(export);
            if (!CollectionUtils.isEmpty(payment.getRefunds())) {
                List<TransactionHistoryExportDTO> refunds = buildRefundExport(
                        payment.getRefunds(),
                        format,
                        export.getCustomerName(),
                        businessInfo.getCurrencySymbol(),
                        export.getGroomingId());
                data.addAll(refunds);
            }
        }
        return data;
    }

    private List<TransactionHistoryExportDTO> buildRefundExport(
            List<RefundDTO> refunds, SimpleDateFormat format, String clientName, String symbol, Integer bookingId) {
        List<TransactionHistoryExportDTO> data = new ArrayList<>(refunds.size());
        for (RefundDTO refund : refunds) {
            TransactionHistoryExportDTO export = new TransactionHistoryExportDTO();
            export.setCreateTime(format.format(refund.getCreateTime() * 1000));
            export.setCustomerName(clientName);
            if (refund.getAmount() == null || refund.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                export.setAmount("");
            } else {
                export.setAmount("-" + symbol + refund.getAmount());
            }
            export.setPaidBy("-");
            export.setProcessingFee(BigDecimal.ZERO.toPlainString());
            export.setPaymentMethod(REFUND_PAYMENT_METHOD);
            export.setStatus(refund.getStatus());
            export.setGroomingId(bookingId);
            export.setNotes(refund.getReason());
            data.add(export);
        }
        return data;
    }

    public Set<Integer> listCustomerIdByCreditCardFilter(ClientsFilterDTO clientsFilterDTO) {
        if (Objects.isNull(clientsFilterDTO.companyId())
                || CollectionUtils.isEmpty(clientsFilterDTO.filters())
                || Objects.isNull(clientsFilterDTO.type())) {
            return Collections.emptySet();
        }
        // Needs to reverse the result
        boolean needReversed = clientsFilterDTO.filters().stream().anyMatch(FilterDTO::isReversed);
        boolean isCofMonFilter = clientsFilterDTO.filters().stream()
                .anyMatch(filterDTO -> Objects.equals(filterDTO.getProperty(), PropertyEnum.cof_status));

        ClientsFilterDTO.ClientsFilterDTOBuilder filterBuilder = ClientsFilterDTO.builder()
                .preferredBusinessIds(clientsFilterDTO.preferredBusinessIds())
                .companyId(clientsFilterDTO.companyId())
                .customerIds(clientsFilterDTO.customerIds())
                .businessDateTime(clientsFilterDTO.businessDateTime())
                .type(clientsFilterDTO.type())
                .connector(clientsFilterDTO.connector());

        if (!needReversed) {
            ClientsFilterDTO replaceClientsFilter;
            if (isCofMonFilter) {
                replaceClientsFilter = filterBuilder
                        .filters(clientsFilterDTO.filters().stream()
                                .filter(filterDTO -> Objects.equals(filterDTO.getProperty(), PropertyEnum.cof_status))
                                .toList())
                        .build();
                return moeCreditCardMapper.listCustomerIdByExpiredMon(replaceClientsFilter);
            } else {
                replaceClientsFilter =
                        filterBuilder.filters(clientsFilterDTO.filters()).build();
                return moeCreditCardMapper.listCustomerIdByFilter(replaceClientsFilter);
            }
        } else if (isCofMonFilter) {
            // has no cof & expired mon取交集为空
            return Collections.emptySet();
        }
        List<Set<Integer>> filterSetList = new ArrayList<>();
        List<CompletableFuture<Set<Integer>>> futureList = new ArrayList<>();

        // Forward filtering
        List<FilterDTO> forwardFilterDTOList = clientsFilterDTO.filters().stream()
                .filter(filterDTO -> !filterDTO.isReversed())
                .toList();
        if (!CollectionUtils.isEmpty(forwardFilterDTOList)) {
            futureList.add(CompletableFuture.supplyAsync(
                    () -> {
                        ClientsFilterDTO forwardFilter =
                                filterBuilder.filters(forwardFilterDTOList).build();
                        return moeCreditCardMapper.listCustomerIdByFilter(forwardFilter);
                    },
                    ThreadPool.getSubmitExecutor()));
        }
        // Reverse filter
        clientsFilterDTO.filters().stream()
                .filter(FilterDTO::isReversed)
                .forEach(filterDTO -> futureList.add(CompletableFuture.supplyAsync(
                        () -> {
                            ClientsFilterDTO reverseFilter = filterBuilder
                                    .filters(Collections.singletonList(filterDTO))
                                    .build();
                            Set<Integer> filterCustomerIds = moeCreditCardMapper.listCustomerIdByFilter(reverseFilter);
                            Set<Integer> allCustomerIds = new HashSet<>(clientsFilterDTO.customerIds());
                            allCustomerIds.removeAll(filterCustomerIds);
                            return allCustomerIds;
                        },
                        ThreadPool.getSubmitExecutor())));
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        futureList.forEach(future -> {
            try {
                filterSetList.add(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.error("listCustomerIdByCountFilter error", e);
                /* Clean up whatever needs to be handled before interrupting  */
                Thread.currentThread().interrupt();
            }
        });
        // Intersect or union
        return switch (clientsFilterDTO.type()) {
            case TYPE_AND -> filterSetList.stream()
                    .reduce((a, b) -> {
                        a.retainAll(b);
                        return a;
                    })
                    .orElse(Collections.emptySet());
            case TYPE_OR -> filterSetList.stream().flatMap(Set::stream).collect(Collectors.toSet());
        };
    }

    public Set<Integer> listCustomerIdByFilter(ClientsFilterDTO clientsFilterDTO) {
        if (Objects.isNull(clientsFilterDTO.companyId())
                || CollectionUtils.isEmpty(clientsFilterDTO.filters())
                || Objects.isNull(clientsFilterDTO.type())) {
            return Collections.emptySet();
        }
        // Needs to reverse the result
        boolean needReversed = clientsFilterDTO.filters().stream().anyMatch(FilterDTO::isReversed);
        if (!needReversed) {
            return paymentMapper.listCustomerIdByFilter(clientsFilterDTO);
        }
        List<Set<Integer>> filterSetList = new ArrayList<>();
        List<CompletableFuture<Set<Integer>>> futureList = new ArrayList<>();
        ClientsFilterDTO.ClientsFilterDTOBuilder filterBuilder = ClientsFilterDTO.builder()
                .preferredBusinessIds(clientsFilterDTO.preferredBusinessIds())
                .companyId(clientsFilterDTO.companyId())
                .customerIds(clientsFilterDTO.customerIds())
                .businessDateTime(clientsFilterDTO.businessDateTime())
                .type(clientsFilterDTO.type())
                .connector(clientsFilterDTO.connector());
        // Forward filtering
        List<FilterDTO> forwardFilterDTOList = clientsFilterDTO.filters().stream()
                .filter(filterDTO -> !filterDTO.isReversed())
                .toList();
        if (!CollectionUtils.isEmpty(forwardFilterDTOList)) {
            futureList.add(CompletableFuture.supplyAsync(
                    () -> {
                        ClientsFilterDTO forwardFilter =
                                filterBuilder.filters(forwardFilterDTOList).build();
                        return paymentMapper.listCustomerIdByFilter(forwardFilter);
                    },
                    ThreadPool.getExecutor()));
        }
        // Reverse filter
        clientsFilterDTO.filters().stream()
                .filter(FilterDTO::isReversed)
                .forEach(filterDTO -> futureList.add(CompletableFuture.supplyAsync(
                        () -> {
                            ClientsFilterDTO reverseFilter = filterBuilder
                                    .filters(Collections.singletonList(filterDTO))
                                    .build();
                            Set<Integer> filterCustomerIds = paymentMapper.listCustomerIdByFilter(reverseFilter);
                            Set<Integer> allCustomerIds = new HashSet<>(clientsFilterDTO.customerIds());
                            allCustomerIds.removeAll(filterCustomerIds);
                            return allCustomerIds;
                        },
                        ThreadPool.getExecutor())));
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        futureList.forEach(future -> {
            try {
                filterSetList.add(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.error("listCustomerIdByCountFilter error", e);
            }
        });
        // Intersect or union
        return switch (clientsFilterDTO.type()) {
            case TYPE_AND -> filterSetList.stream()
                    .reduce((a, b) -> {
                        a.retainAll(b);
                        return a;
                    })
                    .orElse(Collections.emptySet());
            case TYPE_OR -> filterSetList.stream().flatMap(Set::stream).collect(Collectors.toSet());
        };
    }

    public Map<Integer, CustomerPaymentDTO> listCustomerPaymentInfo(BusinessClientsDTO businessClientsDTO) {
        var companyId = businessClientsDTO.companyId();
        if (companyId == null && businessClientsDTO.businessId() != null) {
            companyId = businessInfoHelper.getCompanyIdByBusinessId(businessClientsDTO.businessId());
        }

        if (Objects.isNull(companyId) || CollectionUtils.isEmpty(businessClientsDTO.customerIds())) {
            return Collections.emptyMap();
        }
        List<CustomerPaymentDTO> paymentDTOList =
                paymentMapper.listCustomerPaymentInfo(companyId, businessClientsDTO.customerIds());
        if (CollectionUtils.isEmpty(paymentDTOList)) {
            return Collections.emptyMap();
        }
        return paymentDTOList.stream()
                .collect(Collectors.toMap(CustomerPaymentDTO::getCustomerId, Function.identity()));
    }

    public Integer syncPaymentForPayout(
            Integer businessId, String payoutId, String startCreatedDate, String endCreatedDate) {
        AtomicInteger result = new AtomicInteger(0);
        MmStripeAccount stripeAccount = stripeAccountMapper.selectByBusinessId(businessId);
        if (stripeAccount == null) {
            throw new BizException(Code.CODE_PARAMS_ERROR_VALUE, "invalid business id");
        }
        if (StringUtils.hasText(payoutId)) {
            log.info("sync payout {} for business {}", payoutId, businessId);
            // sync for one payout
            stripeService.syncForSpecifiedPayout(stripeAccount.getStripeAccountId(), payoutId);
            result.incrementAndGet();
        }
        Long startCreated = null, endCreated = null;
        if (StringUtils.hasText(startCreatedDate)) {
            if (StringUtils.hasText(endCreatedDate)) {
                // 若开始日期和结束日期都指定了，则开始日期必须小于等于结束日期。
                if (0 < startCreatedDate.compareTo(endCreatedDate)) {
                    throw new BizException(
                            Code.CODE_PARAMS_ERROR_VALUE, "start date must be less than or equal to end date");
                }
            }
            startCreated = LocalDate.parse(startCreatedDate, DateTimeFormatter.ISO_LOCAL_DATE)
                            .atStartOfDay()
                            .toEpochSecond(ZoneOffset.UTC)
                    * 1000;
        }
        if (StringUtils.hasText(endCreatedDate)) {
            endCreated = LocalDate.parse(endCreatedDate, DateTimeFormatter.ISO_LOCAL_DATE)
                            .atStartOfDay()
                            .toEpochSecond(ZoneOffset.UTC)
                    * 1000;
        }

        if (startCreated == null && endCreated == null) {
            log.info("no limit date range for payout, nop");
            return result.get();
        }
        PayoutViewList payoutViewList = stripeService.getPayoutList(
                stripeAccount.getStripeAccountId(), startCreated, endCreated, null, MAX_PAYOUT_LIMIT);
        payoutViewList.getPayouts().forEach(payout -> {
            // sync for each payout
            log.info("sync payout {} for business {}", payout.getId(), businessId);
            stripeService.syncForSpecifiedPayout(stripeAccount.getStripeAccountId(), payout.getId());
            result.incrementAndGet();
        });
        return result.get();
    }

    public List<PaymentDTO> getPaymentsByInvoiceId(Integer invoiceId) {
        List<Payment> payments = paymentMapper.selectPaymentsByInvoiceId(invoiceId);
        if (CollectionUtils.isEmpty(payments)) {
            return List.of();
        }
        return PaymentStructMapper.INSTANCE.toDtos(payments);
    }

    public Pair<List<PaymentDetailDTO>, Pagination> describePayments(DescribePaymentsParams params) {
        if (hasEmptyCollectionFilter(params.ids(), params.businessIds(), params.invoiceIds())) {
            return Pair.of(
                    Collections.emptyList(),
                    new Pagination(
                            params.pagination().pageNum(), params.pagination().pageSize(), 0));
        }
        var res = selectPage(params.pagination(), () -> paymentMapper.describePayments(params));
        var payments = res.getFirst();
        var paymentIds = payments.stream().map(Payment::getId).toList();
        var idToDetails = payDetailService.getByPaymentIds(paymentIds).stream()
                .distinct()
                .collect(Collectors.toMap(MoePayDetail::getPaymentId, Function.identity()));
        return Pair.of(
                payments.stream()
                        .map(p -> PaymentStructMapper.INSTANCE.toDetail(p, idToDetails.get(p.getId())))
                        .toList(),
                res.getSecond());
    }

    public AssociationAccountDTO getAssociationAccountByBusinessId(Integer businessId) {
        var result = AssociationAccountDTO.builder();
        Optional.ofNullable(stripeAccountMapper.selectByBusinessId(businessId)).ifPresent(stripe -> {
            var account = stripeService.getAccount(stripe.getStripeAccountId());
            result.stripeAccount(AssociationStripeDTO.builder()
                    .stripeAccountId(stripe.getStripeAccountId())
                    .metadata(account.getMetadata())
                    .build());
        });
        Optional.ofNullable(squareService.getToken(businessId)).ifPresent(result::squareTokenResponse);
        return result.build();
    }

    public SyncOrderPaymentDetail getPaymentDetail(Long paymentId) {
        SyncOrderPaymentDetail result = new SyncOrderPaymentDetail();
        var existingPayment = paymentMapper.selectByPrimaryKey(paymentId.intValue());
        if (existingPayment == null) {
            log.warn("no payment found for payment {}", paymentId);
            return result;
        }
        result.setServerPaymentStatus(existingPayment.getStatus());
        result.setPaymentMethodVendor(existingPayment.getVendor());
        result.setTotalAmount(existingPayment.getAmount());
        // can be destination charge or split payment charge
        result.setProcessingFee(existingPayment.getProcessingFee());
        // for failed payment intent
        result.setPaymentStatusReason(existingPayment.getCancelReason());
        result.setCardFunding(existingPayment.getCardFunding());
        result.setCardType(existingPayment.getCardType());
        result.setCardNumber(existingPayment.getCardNumber());
        result.setExpMonth(existingPayment.getExpMonth());
        result.setExpYear(existingPayment.getExpYear());
        result.setStripePaymentMethod(existingPayment.getStripePaymentMethod());
        if (existingPayment.getVendor().equals(METHOD_NAME_STRIPE)
                && StringUtils.hasText(existingPayment.getStripeIntentId())) {
            try {
                PaymentIntent paymentIntent = PaymentIntent.retrieve(existingPayment.getStripeIntentId());
                result.setConvenienceFee(getConvenienceFeeFromMeta(paymentIntent));
                result.setPaymentTipsAfterCreate(getPaymentTipsAmountFromReader(paymentIntent));
                result.setStripeIntentId(paymentIntent.getId());
            } catch (StripeException e) {
                log.warn("get payment intent failed for payment {}", existingPayment.getId(), e);
            }
        }
        return result;
    }

    public BigDecimal getConvenienceFeeFromMeta(PaymentIntent paymentIntent) {
        Map<String, String> meta = paymentIntent.getMetadata();
        if (meta != null && meta.containsKey(PaymentStripeStatus.CONVENIENCE_FEE_KEY)) {
            return new BigDecimal(meta.get(PaymentStripeStatus.CONVENIENCE_FEE_KEY));
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal getPaymentTipsAmountFromReader(PaymentIntent paymentIntent) {
        Long tipsAmount = paymentIntent.getAmountDetails().getTip().getAmount();
        if (PrimitiveTypeUtil.isNullOrZero(tipsAmount)) {
            // null 表示没有tips， 不等价于 BigDecimal.ZERO
            return null;
        } else {
            return BigDecimal.valueOf(tipsAmount).divide(BigDecimal.valueOf(100L), 2, BigDecimal.ROUND_UP);
        }
    }

    public Integer syncPayDetail(List<Integer> paymentIdList) {
        if (CollectionUtils.isEmpty(paymentIdList)) {
            return 0;
        }
        List<Payment> paymentList = paymentMapper.selectByPaymentIds(paymentIdList);
        paymentList.stream()
                .filter(payment -> payment.getVendor().equals(METHOD_NAME_STRIPE)
                        && StringUtils.hasText(payment.getStripeIntentId()))
                .forEach(payment -> stripeService.generatePayDetailByIntentId(payment.getStripeIntentId(), payment));
        return paymentList.size();
    }
}
