package com.moego.server.payment.helper;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.TriggerBookingRequestAutoAcceptedRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.grooming.api.IOnlineBookingService;
import com.moego.server.grooming.api.IOrderDecouplingFlowMarkerService;
import com.moego.server.grooming.client.IBookOnlineDepositClient;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.payment.mapper.PaymentMapper;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/1/21
 */
@Component
@RequiredArgsConstructor
public class BookingRequestHelper {
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestStub;
    private final OrderHelper orderHelper;
    private final IOrderDecouplingFlowMarkerService orderDecouplingFlowMarkerApi;
    private final IBookOnlineDepositClient iBookOnlineDepositClient;
    private final IOnlineBookingService onlineBookingApi;
    private final PaymentMapper paymentMapper;

    /**
     * Trigger booking request auto accepted.
     *
     * <p> 只有在 order decoupling 流程下才会触发。
     *
     * @param paymentId payment id
     */
    public void triggerBookingRequestAutoAccepted(int paymentId) {
        var payment = paymentMapper.selectByPrimaryKey(paymentId);
        if (payment == null) {
            return;
        }

        var obDeposit = iBookOnlineDepositClient.getOBDepositByPaymentId(payment.getBusinessId(), payment.getId());
        if (obDeposit == null) {
            return;
        }

        // 在 order decoupling 流程下才会触发以下逻辑
        if (!isOrderDecouplingFlow(obDeposit)) {
            return;
        }

        if (isNormal(obDeposit.getBookingRequestId())) {
            ThreadPool.execute(() -> {
                bookingRequestStub.triggerBookingRequestAutoAccepted(
                        TriggerBookingRequestAutoAcceptedRequest.newBuilder()
                                .setId(obDeposit.getBookingRequestId())
                                .build());
            });
        } else if (isNormal(obDeposit.getGroomingId())) {
            ThreadPool.execute(() -> {
                onlineBookingApi.triggerBookingRequestAutoAccepted(obDeposit.getGroomingId());
            });
        }
    }

    /**
     * Update BookingRequest payment_status by payment id.
     *
     * @param paymentId     payment id
     * @param paymentStatus payment status
     */
    public void updateBookingRequestPaymentStatusByPaymentId(
            int paymentId, BookingRequestModel.PaymentStatus paymentStatus) {
        var bookingRequest = getBookingRequestByPaymentId(paymentId);
        if (bookingRequest == null) {
            return;
        }
        bookingRequestStub.updateBookingRequest(UpdateBookingRequestRequest.newBuilder()
                .setId(bookingRequest.getId())
                .setPaymentStatus(paymentStatus)
                .build());
    }

    @Nullable
    private BookingRequestModel getBookingRequestByPaymentId(int paymentId) {
        var payment = paymentMapper.selectByPrimaryKey(paymentId);
        if (payment == null) {
            return null;
        }

        var obDeposit = iBookOnlineDepositClient.getOBDepositByPaymentId(payment.getBusinessId(), payment.getId());
        if (obDeposit == null) {
            return null;
        }

        if (isNormal(obDeposit.getBookingRequestId())) {
            return getBookingRequestByBookingRequestId(obDeposit.getBookingRequestId());
        } else if (isNormal(obDeposit.getGroomingId())) {
            return getBookingRequestByAppointmentId(payment.getBusinessId(), obDeposit.getGroomingId());
        } else {
            return null;
        }
    }

    /**
     * Get booking request by appointment id.
     *
     * @param businessId    business id
     * @param appointmentId appointment id
     * @return booking request
     */
    @Nullable
    public BookingRequestModel getBookingRequestByAppointmentId(int businessId, int appointmentId) {
        return bookingRequestStub
                .listBookingRequests(ListBookingRequestsRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addAppointmentIds(appointmentId)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageSize(1)
                                .setPageNum(1)
                                .build())
                        .build())
                .getBookingRequestsList()
                .stream()
                .findFirst()
                .orElse(null);
    }

    /**
     * Get booking request by booking request id.
     *
     * @param bookingRequestId booking request id
     * @return booking request
     */
    @Nullable
    public BookingRequestModel getBookingRequestByBookingRequestId(long bookingRequestId) {
        var resp = bookingRequestStub.getBookingRequest(
                GetBookingRequestRequest.newBuilder().setId(bookingRequestId).build());
        return resp.hasBookingRequest() ? resp.getBookingRequest() : null;
    }

    private boolean isOrderDecouplingFlow(BookOnlineDepositDTO obDeposit) {
        if (isNormal(obDeposit.getBookingRequestId())) {
            var order = orderHelper.getBySource(OrderSourceType.BOOKING_REQUEST, obDeposit.getBookingRequestId());
            return order != null
                    && orderDecouplingFlowMarkerApi.getOrderDecouplingFlowMarker(
                                    order.getOrder().getId())
                            != null;
        } else if (isNormal(obDeposit.getGroomingId())) {
            var order = orderHelper.getBySource(OrderSourceType.APPOINTMENT, obDeposit.getGroomingId());
            return order != null
                    && orderDecouplingFlowMarkerApi.getOrderDecouplingFlowMarker(
                                    order.getOrder().getId())
                            != null;
        }
        return false;
    }
}
